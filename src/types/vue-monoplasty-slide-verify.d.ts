declare module 'vue-monoplasty-slide-verify' {
  import { VueConstructor } from 'vue'
  
  interface SlideVerifyProps {
    l?: number
    r?: number
    w?: number
    h?: number
    sliderText?: string
    onSuccess?: () => void
    onFail?: () => void
    onClose?: () => void
  }

  interface SlideVerifyInstance {
    reset: () => void
  }

  const SlideVerify: VueConstructor<SlideVerifyInstance>
  export default SlideVerify
} 