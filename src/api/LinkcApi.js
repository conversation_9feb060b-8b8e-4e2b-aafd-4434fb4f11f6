import http from '@src/util/http';
import { objectDateToTimestamp } from 'pub-bbx-utils';
let headeUrl = ''

export function getRules(params) {
  return http.get(`${headeUrl}/linkc/getRules`, params)
}

export function saveRules(params) {
  return http.post(`${headeUrl}/linkc/saveRules`, params)
}

export function getInfos(params) {
  return http.get(`${headeUrl}/linkc/getInfos`, params)
}

export function saveInfos(params) {
  return http.post(`${headeUrl}/linkc/saveInfos`, params)
}

export function weChat(params) {
  return http.post(`${headeUrl}/linkc/weChat`, params)
}

export function orderList(params) {
  objectDateToTimestamp(params, ['startTime', 'endTime', 'completeTimeStart', 'completeTimeEnd'])
  return http.post(`${headeUrl}/linkc/order/list`, params)
}

export function orderNum() {
  return http.get(`${headeUrl}/linkc/order/num`)
}

export function orderDetail(params) {
  return http.get(`${headeUrl}/linkc/order/view`, params)
}

export function orderDeliver(params) {
  return http.post(`${headeUrl}/linkc/order/deliver`, params)
}

export function getEventList(params) {
  return http.get(`${headeUrl}/linkc/getEventList`, params)
}

export function getWikiList(params) {
  return http.post('/api/linkc/outside/linkc/v3b/wiki/list', params)
}

export function repertoryOut(params,query) {
  return http.post(`${headeUrl}/linkc/order/repertoryOut${query||''}`, params)
}

/**
* 获取退换货地址
*/
export function getPaasBackGray(params){
  return http.get('/api/paas/outside/pc/formField/getPaasBackGray', params);
}

/**
 * @description 获取租户是否有公众号设置
 */
export function getWechatSetting (params) {
  return http.get('/api/linkc/outside/linkc/v3b/tenant/getWechatSetting', params)
}

/**
 * @description 商城设置-获取商城相关配置
 */
 export function getOrderConfig (params) {
  return http.get('/api/linkc/outside/linkc/v3b/shop/config/getOrderConfig', params)
}

/**
 * @description 商城设置-编辑商城相关配置
 */
 export function updateOrderConfig (params) {
  return http.post('/api/linkc/outside/linkc/v3b/shop/config/updateOrderConfig', params)
}

/**
 * @description 商城设置-查询快递公司列表
 */
 export function getExpressConfig (params) {
  return http.get('/api/linkc/outside/linkc/v3b/shop/config/getExpressConfig', params)
}

/**
 * @description 商城设置-批量保存快递公司
 */
 export function saveExpressConfig (params) {
  return http.post('/api/linkc/outside/linkc/v3b/shop/config/saveExpressConfig', params)
}

/**
 * @description 商城设置-物流公司更新排序
 */
 export function updateExpressConfig (params) {
  return http.post('/api/linkc/outside/linkc/v3b/shop/config/updateExpressConfig', params)
}

/**
 * @description 商城设置-删除快递公司
 */
 export function deleteExpressConfig (params) {
  return http.post('/api/linkc/outside/linkc/v3b/shop/config/deleteExpressConfig', params)
}

/**
 * @description 获取仓库列表
 * keyword
 * pageNum
 * pageSize
 */
 export function getSparepartOrMaterialWarehouse (params) {
  return http.post('/api/linkc/outside/linkc/v3b/shop/config/getSparepartOrMaterialWarehouse', params)
}

/**
 * @description 3.0获取物料仓库类型
 */
 export function searchWarehouseType (params) {
  return http.get('/api/warehouse/outside/setting/warehouse/type/search', params)
}

/**
 * @description 保存仓库
 */
 export function saveWareHouse (params) {
  return http.post('/api/linkc/outside/linkc/v3b/shop/config/saveWareHouse', params)
}

/**
 * @description 查询仓库列表
 */
 export function queryWarehouseList (params) {
  return http.get('/api/linkc/outside/linkc/v3b/shop/config/queryWarehouseList', params)
}

/**
 * @description 删除仓库
 */
 export function deleteWareHouse (params) {
  return http.post('/api/linkc/outside/linkc/v3b/shop/config/deleteWareHouse', params)
}

/**
 * @description 更新仓库排序
 */
 export function updateSortWareHouse (params) {
  return http.post('/api/linkc/outside/linkc/v3b/shop/config/updateSort', params)
}

/**
 * @description 更新支付方式
 */
 export function updatePaymentConfig (params) {
  return http.post('/api/linkc/outside/linkc/v3b/shop/config/updatePaymentConfig', params)
}

/**
 * @description 查询规格接口
 */
 export function querySpecsList () {
  return http.get('/api/linkc/outside/linkc/v3b/shop/specs/query')
}

/**
 * @description 保存/修改规格
 */
export function saveSpecs (params) {
  return http.post('/api/linkc/outside/linkc/v3b/shop/specs/insert', params)
}

/**
 * @description 删除规格
 */
export function deleteSpecs (params) {
  return http.post('/api/linkc/outside/linkc/v3b/shop/specs/delete', params)
}

/**
 * @description 查询商城订单结算列表
 */
 export function getShopOrderDataList (params) {
  return http.post('/api/customer/outside/customer/settlement/getShopOrderDataList', params)
}

/**
 * @description 查询商城订单结算列表
 */
export function checkEdit (params) {
  return http.get('/api/linkc/outside/linkc/v3b/shop/specs/checkEdit', params)
}
/**
 * @description 新增商品分组
 */
export function insertGrouping(params) {
  return http.post('/api/linkc/outside/linkc/v3b/shop/group/insert', params)
}
/**
 * @description 查询商品分组
 */
export function queryGrouping(params) {
  return http.get('/api/linkc/outside/linkc/v3b/shop/group/query', params)
}
/**
 * @description 删除商品分组
 */
export function deleteGrouping(params) {
  return http.post(`/api/linkc/outside/linkc/v3b/shop/group/delete?id=${params.id}`)
}
/**
 * @description 编辑商品分组
 */
export function updateGrouping(params) {
  return http.post('/api/linkc/outside/linkc/v3b/shop/group/update', params)
}
/**
 * @description 删除分组校验
 */
export function groupCheckEdit(params) {
  return http.post(`/api/linkc/outside/linkc/v3b/shop/group/checkEdit?id=${params.id}`)
}

/**
 * @description 修改支付设置
 */
export function updatePaymentChannels(params) {
  return http.post(`/api/linkc/outside/linkc/v3b/shop/config/updatePaymentChannels`,params)
}
/**
 * @description 获取租户商城支付渠道相关配置
 */
export function getPaymentChannels(params) {
  return http.get(`/api/linkc/outside/linkc/v3b/shop/config/getPaymentChannels`,params)
}
/**
 * @description 修改商城售后管理
 */
export function updateAfterSale (params) {
  return http.post('/api/linkc/outside/linkc/v3b/shop/config/updateReturnOrderConfig', params)
}
/**
 * @description 获取仓库列表
 * keyword
 * pageNum
 * pageSize
 */

/**
 * @description 根据仓库获取仓位
 */
export function getreturnInboundOrderTypeList (params) {
  return http.get('/api/warehouse/outside/warehousePosition/search', params)
}
/**
 * @description 初始化云仓
 */
export function adjustSetting(params) {
  return http.post('/api/warehouse/outside/setting/adjustSetting', params)
}
/**
 * @description 获取动态日志
 */
export function getRecordList(params) {
  return http.post('/api/linkc/outside/linkc/v3b/afterSales/getRecordList', params)
}
/**
 * @description 添加动态日志
 */
export function saveRemarks(params) {
  return http.post('/api/linkc/outside/linkc/v3b/afterSales/saveRemarks', params)
}
/**
 * @description 售后单详情
 */
export function afterSalegetDetails(params) {
  return http.get('/api/linkc/outside/linkc/v3b/afterSales/getDetails', params)
}
/**
 * @description 批量审批售后单
 */
export function batchApprovalAfterSales (params) {
  return http.post('/api/linkc/outside/linkc/v3b/afterSales/batchApproval', params)
}
/**
 * @description 审批售后单
 */
export function approvalAfterSales (params) {
  return http.get('/api/linkc/outside/linkc/v3b/afterSales/approval', params)
}
/**
 * @description 查询售后单列表
 */
export function afterSalegetList (params) {
  return http.post('/api/linkc/outside/linkc/v3b/afterSales/getList', params)
}
/**
 * @description 获取订单出入库记录
 */
export function basedOnBusinessAcquisition (params) {
  return http.get('/api/warehouse/outside/discrepancy/record/basedOnBusinessAcquisition', params)
}

