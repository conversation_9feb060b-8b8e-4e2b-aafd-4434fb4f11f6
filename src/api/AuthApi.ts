
import { AddDingtalkAuthScopeModel, DingtalkAuthCheckScopeModel } from '@model/param/in/Dingtalk'
import { DingtalkAuthCheckScopeResult } from '@model/param/out/Dingtalk'
import Result from '@model/Result'
import http from '@src/util/http' 

/**
 * @description 校验钉钉用户授权功能点
 */
function checkDingScope(params: DingtalkAuthCheckScopeModel): Promise<Result<DingtalkAuthCheckScopeResult>> {
  return http.post('/api/user/outside/user/checkDingScope', params) as Promise<Result<DingtalkAuthCheckScopeResult>>
}

/**
 * @description 使用钉钉authCode刷新钉钉authToken
 */
function refreshDingToken(authCode: string): Promise<Result<boolean>> {
  return http.get('/api/user/outside/user/refreshDingToken/' + authCode)
}

/**
 * @description 新添加的钉钉用户授权功能点
 */
function addDingScope(params: AddDingtalkAuthScopeModel): Promise<Result<boolean>> {
  return http.post('/api/user/outside/user/addDingScope', params)
}

export {
  checkDingScope,
  refreshDingToken,
  addDingScope
}
