
import http from '@src/util/http'
import { StorageHttpParamsSet, StorageHttpParamsBase } from '@src/component/common/BaseTabBar/types'
/**
 * @des 获取富文本内容
 * @param {Array<stirng>} ids
 * @returns 
 */
export function getRichTextContent(params: {} | undefined) {
	return http.post('/api/task/outside/richText/getRichTextById', params);
}
/**
 * @des 获取im服务备注富文本内容
 * @param {Array<stirng>} ids
 * @returns 
 */
export function getImRichTextContent(params: {} | undefined) {
	return http.post('/api/im/outside/richText/getRichTextById', params);
}

/**
 * @des 获取物料富文本内容
 * @param {Array<stirng>} ids
 * @returns 
 */
export function getWarehouseRichTextContent(params: {} | undefined) {
	return http.post('/api/warehouse/outside/richText/getRichTextById', params);
}

/**
 * @des 获取商品富文本内容
 * @param id
 * @returns 
 */
export function getGoodsRichTextContent(params: {} | undefined) {
	return http.get('/api/linkc/outside/linkc/v3c/notLogin/richText/getRichTextById', params);
}


/**
 * @des 详情页面右侧tabbar用户行为缓存存储
 */
 export function setStorageForDetailTabbar(params: {} | StorageHttpParamsSet = {}) {
	return http.post('/api/workbench/outside/cardSelect/saveCardSelect', params);
}

/**
 * @des 详情页面右侧tabbar用户行为缓存获取
 */
 export function getStorageForDetailTabbar(params: StorageHttpParamsBase) {
	return http.post('/api/workbench/outside/cardSelect/getCardSelect', params);
}


/**
 * @des 根据id获取 用户详情
 * 
 */
 export function getUserInfoByIds(params: [] | undefined) {
	return http.post('/api/user/outside/select/getUserByIdList', params);
}


/**
 * @des 事件、客户、工单选择协同人 事件、客户选择负责人 获取部门列表数据
 */
 export function getExUserTagTree(params: [] | undefined) {
	return http.post('/pc/biz/select/user/tag/tree', params);
}

/**
 * @des 事件、客户选择负责人 搜索人员列表方法
 */
 export function getExUserUserSearch(params: [] | undefined) {
	return http.post('/pc/biz/select/user/search', params);
}

/**
 * @des 事件、客户选择负责人 搜索更多人员列表方法
 */
 export function getExUserUserMore(params: [] | undefined) {
	return http.post('/pc/biz/select/user/more', params);
}

/**
 * @des 获取系统内的语言列表
 */
 export function getSystemLanguageList(params: {} | undefined) {
	return http.get('/api/user/outside/language/support/list', params);
}

/**
 * @des 切换系统内的语言列表
 */
 export function setSystemLanguage(params: {} | undefined) {
	return http.get('/api/user/outside/language/selectLanguage', params);
}

/**
 * @des 获取当前用户的语言环境
 */
export async function getCurrentLanguage(params: { deviceType: 1 | 2 } = { deviceType: 1 }) {
	const res = await http.get('/api/user/outside/language/getCurrentLanguage', params);
	return res.data
}

/**
 * @des 获取服务器时区偏移量
 */
export async function fetchServerTimezoneOffset() {
	const res = await http.get('/api/workbench/outside/i18n/getSystemTimeZongRawOffSet');
	return 0 - (res.data || 28800000)
}

/**
 * @des 获取列表货币
 */
export function fetchCurrencyList (params: {} | undefined) {
	return http.post('/api/workbench/outside/currency/list', params, false);
}

/**
 * @des 获取按钮绑定的触发器
 */
export function getButtonConcatTriggerList (params: {} | undefined) {
	return http.post('/api/application/outside/trigger/getTriggerByBizTypeInfo', params);
}

/**
 * @des 保存自定义的页面按钮
 * @see https://yapi.shb.ltd/project/5788/interface/api/59867
 */
export function setDataForButtonSet (params: {} | undefined) {
	return http.post('/api/voice/outside/container/button/updateButton', params);
}

/**
 * @des 获取对应应用的自定义按钮
 * @see https://yapi.shb.ltd/project/5788/interface/api/59885
 */
export function getDataForButtonSet (params: {} | undefined) {
	return http.post('/api/voice/outside/container/button/getButtonByModule', params);
}

