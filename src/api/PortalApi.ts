/* util */
import http from '@src/util/http'
/* model */
import {
  GetDoorTypeFieldListModel,
  GetDoorTypeFieldModel,
  GetDoorTypeListModel,
  GetDoorTypeModel,
  UpdateDoorTypeFieldListModel,
  UpdateDoorTypeModel,
  GetShareInfo,
  UpdateShareInfo,
  getDoorPath,
  DoorName,
  GetGoodsListModel,
  UpdateGoodsInfoModel,
  GetServiceListModel,
} from '@model/param/in/Portal'
import {
  GetDoorTypeListResult,
  GetDoorTypeInfoResult,
  GetDoorTypeResult,
  UpdateDoorTypeResult,
  GetShareInfoResult,
  GetDoorAuthResult
} from '@model/param/out/Portal'
import Result from '@model/Result'
import { FormatTemplate, formatDate } from 'pub-bbx-utils';

const fixedPrefixTaskPath = '/api/linkc'

/**
 * @description 获取字段详情
 */
export function getDoorTypeField(params: GetDoorTypeFieldModel): Promise<any> {
  return http.post(`${fixedPrefixTaskPath}/outside/linkc/v3b/doorTypeField/get`, params)
}

/**
 * @description 获取租户下的模板类型列表
 */
export function getDoorTypeInfo(params: { id: string }): Promise<GetDoorTypeInfoResult> {
  return http.get(`${fixedPrefixTaskPath}/outside/linkc/v3b/doorType/getOne`, params)
}

/**
 * @description 获取租户下的模板类型列表
 */
export function getDoorTypeList(params: GetDoorTypeListModel): Promise<GetDoorTypeListResult> {
  return http.get(`${fixedPrefixTaskPath}/outside/linkc/v3b/doorType/getDoorTypeList`, params)
}

/**
 * @description 获取租户下的模板类型列表
 */
export function getDoorTypeListToC(params: GetDoorTypeListModel): Promise<GetDoorTypeListResult> {
  return http.get(`${fixedPrefixTaskPath}/outside/linkc/v3c/doorType/getDoorTypeList`, params)
}

/**
 * @description 获取租户下的模板类型单个
 */
export function getDoorType(params: GetDoorTypeModel): Promise<GetDoorTypeResult> {
  return http.get(`${fixedPrefixTaskPath}/outside/linkc/v3b/doorType/getOne`, params)
}

/**
 * @description 编辑模板类型
 */
export function updateDoorType(params: UpdateDoorTypeModel): Promise<UpdateDoorTypeResult> {
  return http.post(`${fixedPrefixTaskPath}/outside/linkc/v3b/doorType/update`, params)
}

/**
 * @description 根据类型和模块获取字段list和字段配置
 */
export function getDoorTypeFieldList(params: GetDoorTypeFieldListModel): Promise<Result<null>> {
  return http.get(`${fixedPrefixTaskPath}/outside/linkc/v3b/doorTypeField/getListByTypeId`, params)
}

/**
 * @description 保存字段组件配置
 */
export function updateDoorTypeFieldList(params: UpdateDoorTypeFieldListModel): Promise<Result<null>> {
  return http.post(`${fixedPrefixTaskPath}/outside/linkc/v3b/doorTypeField/save`, params)
}

/**
 * @description 保存字段组件配置
 */
export function updateDoorTypeFieldListDraft(params: UpdateDoorTypeFieldListModel): Promise<Result<null>> {
  return http.post(`${fixedPrefixTaskPath}/outside/linkc/v3b/doorTypeField/saveDraft`, params)
}

/**
 * @description 保存门户主题色
 */
export function updateDoorThemeColour(params: { themeColour: string, typeId: string }): Promise<Result<null>> {
  return http.get(`${fixedPrefixTaskPath}/outside/linkc/v3b/doorType/updateThemeColour`, params)
}

/**
 * @description 获取分享设置
 */
export function getShareInfo(params: GetShareInfo): Promise<GetShareInfoResult> {
  return http.get(`${fixedPrefixTaskPath}/outside/linkc/v3b/doorType/getShareInfo`, params)
}

/**
 * @description 获取分享设置
 */
export function getShareInfoV2(params: GetShareInfo): Promise<GetShareInfoResult> {
  return http.get(`${fixedPrefixTaskPath}/outside/linkc/v3b/doorType/getShareInfoV2`, params)
}

/**
 * @description 获取分享设置
 */
export function saveShareInfo(params: UpdateShareInfo): Promise<Result<null>> {
  return http.post(`${fixedPrefixTaskPath}/outside/linkc/v3b/doorType/updateShare`, params)
}

/**
 * @description 获取分享设置
 */
export function saveShareInfoV2(params: UpdateShareInfo): Promise<Result<null>> {
  return http.post(`${fixedPrefixTaskPath}/outside/linkc/v3b/doorType/updateShareV2`, params)
}


/**
 * @description 获取门户权限
 */
export function getDoorAuth(): Promise<GetDoorAuthResult> {
  return http.get(`/portal/getDoorAuth`)
}

/**
 * @description 获取租户填写的微信资料
 */
export function getDoorWeChatInfo() {
  return http.get(`${fixedPrefixTaskPath}/outside/linkc/v3b/doorType/getDoorWeChatInfo`)
}

/**
 * @description 下载自主门户海报
 */
export function downloadDoorPoster(params: getDoorPath): Promise<Result<null>> {
  return http.get(`${fixedPrefixTaskPath}/outside/linkc/v3b/doorType/downloadPoster`, params)
}

export function downloadDoorPosterV2(params: getDoorPath): Promise<Result<null>> {
  return http.get(`${fixedPrefixTaskPath}/outside/linkc/v3b/doorType/downloadPosterV2`, params)
}

export function getShortLink(params: getDoorPath): Promise<Result<null>> {
  return http.get(`${fixedPrefixTaskPath}/outside/linkc/v3b/convert/long2Short`, params)
}

/**
 * @description 获取公司信息
 */
export function getCompanyInfo(): Promise<Result<null>> {
  return http.get('/setting/getCompanyInfo')
}

/**
 * @description 修改门户名称
 */
export function updateDoorName(params: DoorName): Promise<Result<null>> {
  return http.get(`${fixedPrefixTaskPath}/outside/linkc/v3b/tenant/updateDoorName`, params)
}

/**
 * 查询备件列表
 */
export function queryPartList(): Promise<any> {
  return http.post(`${fixedPrefixTaskPath}/outside/linkc/v3b/goods/partList`);
}

/**
 * @description 新建模板类型
 */
export function createDoorType(params: DoorName): Promise<Result<null>> {
  return http.post(`${fixedPrefixTaskPath}/outside/linkc/v3b/doorType/create`, params)
}

/**
 * @description 删除模板
 */
export function deleteDoorType(params: DoorName): Promise<Result<null>> {
  return http.get(`${fixedPrefixTaskPath}/outside/linkc/v3b/doorType/delete`, params)
}

/**
 * @description 复制模板
 */
export function copyDoorType(params: DoorName): Promise<Result<null>> {
  return http.get(`${fixedPrefixTaskPath}/outside/linkc/v3b/doorType/copyDoorType`, params)
}

/**
 * @description 判断组件下的知识库文章是否已经被删除或者改为内部
 */
export function gjudgeWikiStatus(params: DoorName): Promise<Result<null>> {
  return http.post(`${fixedPrefixTaskPath}/outside/linkc/v3b/wiki/gjudgeWikiStatus`, params)
}

/**
 * @description 查询商品列表
 */
export function queryGoodsList(params: GetGoodsListModel): Promise<Result<null>> {
  return http.post(`${fixedPrefixTaskPath}/outside/linkc/v3b/shop/commodity/getList`, params)
}

/**
 * @description 商品列表发布商品
 */
export function releaseGoods(params: { commodityIdList: string[], originType: number }): Promise<Result<null>> {
  return http.post(`${fixedPrefixTaskPath}/outside/linkc/v3b/shop/commodity/saveList`, params)
}

/**
 * @description 商品列表发布商品
 */
export function releaseGoodsV2(params: { commodityList: string[] }): Promise<Result<null>> {
  return http.post(`${fixedPrefixTaskPath}/outside/linkc/v3b/shop/commodity/saveListV2`, params)
}

/**
 * @description 获取商品初始信息
 */
export function initSaveListInfo(params: { commodityIdList: string[], originType: number }): Promise<Result<null>> {
  return http.post(`${fixedPrefixTaskPath}/outside/linkc/v3b/shop/commodity/initSaveListInfo`, params)
}

/**
 * @description 删除商品
 */
export function deleteGoods(params: { ids: string[] }): Promise<Result<null>> {
  return http.post(`${fixedPrefixTaskPath}/outside/linkc/v3b/shop/commodity/deleteCommodity`, params)
}

/**
 * @description 查询商品详情
 */
export function queryGoodsDetail(params: { id: string }): Promise<Result<null>> {
  return http.post(`${fixedPrefixTaskPath}/outside/linkc/v3b/shop/commodity/getDetail`, params)
}

/**
 * @description 编辑商品信息
 */
export function editGoodsInfo(params: UpdateGoodsInfoModel): Promise<Result<null>> {
  return http.post(`${fixedPrefixTaskPath}/outside/linkc/v3b/shop/commodity/saveInfo`, params)
}

/**
 * @description 获取商品类别
 */
export function getCommodityTypeList(params: { originType: number }): Promise<Result<null>> {
  return http.get(`${fixedPrefixTaskPath}/outside/linkc/v3b/shop/commodity/getCommodityTypeList`, params)
}

/**
 * @description 商品上下架
 */
export function updatePutawayStatus(params: { ids: string[], putawayStatus: number }): Promise<Result<null>> {
  return http.post(`${fixedPrefixTaskPath}/outside/linkc/v3b/shop/commodity/updatePutawayStatus`, params)
}

/**
 * @description 获取服务项目列表
 */
export function getServiceList(params: GetServiceListModel): Promise<Result<null>> {
  return http.post(`${fixedPrefixTaskPath}/outside/linkc/v3b/shop/commodity/servicePrice/page`, params)
}

/**
 * @description 检验商品价格
 */
export function checkPrice(params: { ids: string[] }): Promise<Result<null>> {
  return http.post(`${fixedPrefixTaskPath}/outside/linkc/v3b/shop/commodity/checkPrice`, params)
}

/**
 * @description 批量修改商品
 */
export function batchUpdateGoods(params: any): Promise<Result<null>> {
  return http.post(`${fixedPrefixTaskPath}/outside/linkc/v3b/shop/commodity/batchUpdate`, params)
}

/**
 * @description 订单详情
 */
export function orderDetails(params: any): Promise<Result<null>> {
  return http.get(`${fixedPrefixTaskPath}/outside/linkc/v3b/order/orderDetails`, params)
}

/**
 * @description 订单详情:动态信息
 */
export function orderDynamic(params: any): Promise<Result<null>> {
  return http.get(`${fixedPrefixTaskPath}/outside/linkc/v3b/orderDynamic/list`, params)
}

/**
 * @description 订单详情:物流信息
 */
export function logisticsDetails(params: any): Promise<Result<null>> {
  return http.get(`${fixedPrefixTaskPath}/outside/linkc/v3b/logistics/logisticsDetails`, params)
}

/**
 * @description 订单详情:关联服务
 */
export function myService(params: any): Promise<Result<null>> {
  return http.get(`${fixedPrefixTaskPath}/outside/linkc/v3b/orderTask/myService`, params)
}

/**
 * @description 订单详情:商品信息
 */
export function orderWithCommodity(params: any): Promise<Result<null>> {
  return http.get(`${fixedPrefixTaskPath}/outside/linkc/v3b/order/orderWithCommodity`, params)
}

/**
 * @description 查询订单列表
 */
export function queryOrderList(params: any): Promise<Result<null>> {
  return http.post(`${fixedPrefixTaskPath}/outside/linkc/v3b/order/list`, params)
}

/**
 * @description 取消订单
 */
export function cancelOrder(params: { orderId: string }): Promise<Result<null>> {
  return http.get(`${fixedPrefixTaskPath}/outside/linkc/v3b/order/cancelOrder`, params)
}

/**
 * @description 修改订单查询订单详情
 */
export function queryOrderInfo(params: { id: string }): Promise<Result<null>> {
  return http.get(`${fixedPrefixTaskPath}/outside/linkc/v3b/order/querySimpleOrderInfo`, params)
}

/**
 * @description 修改订单
 */
export function editOrderInfo(params: any): Promise<Result<null>> {
  return http.post(`${fixedPrefixTaskPath}/outside/linkc/v3b/order/editOrder`, params)
}

/**
 * @description 获取商品动态信息
 */
export function queryCommodityDynamic(params: { id: string }): Promise<Result<null>> {
  return http.get(`${fixedPrefixTaskPath}/outside/linkc/v3b/shop/commodity/queryCommodityDynamic`, params)
}

/**
 * @description 商品动态记录-添加备注
 */
export function addCommodityRemark(params: { commodityId: string, remark: string }): Promise<Result<null>> {
  return http.post(`${fixedPrefixTaskPath}/outside/linkc/v3b/shop/commodity/addCommodityRemark`, params)
}

/**
 * @description 订单关联工单
 */
export function saveTask(params: { id: string }): Promise<Result<null>> {
  return http.get(`${fixedPrefixTaskPath}/outside/linkc/v3b/orderTask/saveTask`, params)
}

/**
 * @description 快递发货
 */
export function deliverGoods(params: any): Promise<Result<null>> {
  return http.post(`${fixedPrefixTaskPath}/outside/linkc/v3b/order/deliverGoods`, params)
}

/**
 * @description 查询订单关联物流
 */
export function getSimpleLogistics(params: { id: string }): Promise<Result<null>> {
  return http.get(`${fixedPrefixTaskPath}/outside/linkc/v3b/logistics/simpleLogistics`, params)
}

/**
 * @description 查询订单关联联系人
 */
export function getLinkManId(params: { phone: string }): Promise<Result<null>> {
  return http.get(`${fixedPrefixTaskPath}/outside/linkc/v3b/order/getLinkManId`, params)
}

/**
 * @description 获取商品预览链接
 */
export function getCommodityPreviewUrl(params: { phone: string }): Promise<Result<null>> {
  return http.get(`${fixedPrefixTaskPath}/outside/linkc/v3b/shop/commodity/getCommodityPreviewUrl`, params)
}

/**
 * @description 获取商品预览链接
 */
 export function exportBathUpdateTemplate(params: { phone: string }): Promise<Result<null>> {
  return http.post(`${fixedPrefixTaskPath}/outside/linkc/v3b/shop/commodity/exportBathUpdateTemplate`, params)
}

/**
 * @description 获取商品预览链接
 */
 export function checkAbilityToPay(params: {}): Promise<Result<null>> {
  return http.get(`${fixedPrefixTaskPath}/outside/linkc/v3b/shop/commodity/checkAbilityToPay`, params)
}

/**
 * @description 导入批量更新
 */
export function newUpdate(params={}){
  return http.post('/excels/serviceMall/update',params);
}

/**
 * @description 订单详情动态信息新增备注
 */
 export function addDynamic(params: { id: string }): Promise<Result<null>> {
  return http.post(`${fixedPrefixTaskPath}/outside/linkc/v3b/orderDynamic/addDynamic`, params)
}

/**
 * @description 查询物流信息
 */
 export async function queryLogisticsDetails(params: any): Promise<Result<null>> {
  const res = await http.get(`${fixedPrefixTaskPath}/outside/linkc/v3b/logistics/queryLogisticsDetails`, params)
  res?.data?.list?.forEach((item: { time: number }) => {
    // @ts-ignore
    item.time = formatDate(item.time, FormatTemplate.full)
  })
  return res
}

/**
 * @description 查询新版商城列表数据
 */
 export function commodityList(params: any): Promise<Result<null>> {
  return http.post(`${fixedPrefixTaskPath}/outside/linkc/v3c/shopCommodity/commodityList`, params);
}

/**
 * @description 获取Pass功能列表
 */
 export function getPaasGrayFunctionList(params: DoorName): Promise<Result<null>> {
  return http.get(`/api/paas/outside/pc/app/template/access`, params)
}

/**
 * @description 获取公司信息
 */
 export function getCompany(params:any){
  return http.get(`/setting/company`, params)
}

/**
 * @description 门户数据看板-门户数据
 */
export function queryDoorDateTop(params: any): Promise<Result<null>> {
  return http.get(`${fixedPrefixTaskPath}/outside/linkc/v3b/doorData/queryDoorDateTop`, params);
}

/**
 * @description 门户数据看板-门户趋势统计
 */
export function doorTrendStatistics(params: any): Promise<Result<null>> {
  return http.get(`${fixedPrefixTaskPath}/outside/linkc/v3b/doorData/doorTrendStatistics`, params);
}

/**
 * @description 门户数据看板-用户信息top-地区统计
 */
export function userAreaStatistics(params: any): Promise<Result<null>> {
  return http.get(`${fixedPrefixTaskPath}/outside/linkc/v3b/doorData/userAreaStatistics`, params);
}

/**
 * @description 门户数据看板-用户信息top-人员统计
 */
export function userInfoStatistics(params: any): Promise<Result<null>> {
  return http.get(`${fixedPrefixTaskPath}/outside/linkc/v3b/doorData/userInfoStatistics`, params);
}

/**
 * @description 门户数据看板-来源渠道统计
 */
export function sourceChannelStatistics(params: any): Promise<Result<null>> {
  return http.get(`${fixedPrefixTaskPath}/outside/linkc/v3b/doorData/sourceChannelStatistics`, params);
}

/**
 * @description 门户数据看板-页面浏览数据统计
 */
export function pageViews(params: any): Promise<Result<null>> {
  return http.get(`${fixedPrefixTaskPath}/outside/linkc/v3b/doorData/pageViews`, params);
}

/**
 * @description 门户数据看板-获取用户标签
 */
export function getUserTag(params: any): Promise<Result<null>> {
  return http.get(`${fixedPrefixTaskPath}/outside/linkc/v3b/doorData/getUserTag`, params);
}

/**
 * @description 门户数据看板-分页查询登录记录
 */
export function loginRecordPage(params: any): Promise<Result<null>> {
  return http.post(`${fixedPrefixTaskPath}/outside/linkc/v3b/doorData/loginRecordPage`, params);
}

/**
 * @description 门户数据看板-保存联系人
 */
export function unknownUserRelationLinkman(params: any): Promise<Result<null>> {
  return http.post(`${fixedPrefixTaskPath}/outside/linkc/v3b/doorData/unknownUserRelationLinkman`, params);
}
/** 
* @description 退款审批
*/
export function refundApprove(params:any){
  return http.post(`/api/linkc/outside/linkc/v3b/order/refundApprove`, params)
}

/** 
* @description B端查询退款流程详情
*/
export function getRefundInfo(params:any){
  return http.get(`/api/linkc/outside/linkc/v3b/order/getRefundInfo`, params)
}

/**
 * @description 获取批量审批权限
 */
export function getRefundApproveAuth () {
  return http.get('/api/linkc/outside/linkc/v3b/order/getRefundApproveAuth')
}

/**
 * @description 获取门户小程序码 - 通用版
 */
export function queryDoorAppletCode (params:any) {
  return http.get('/api/im/outside/pc/doorApplet/getDoorAppletCode', params)
}

/**
 * @description 下载门户小程序码海报
 */
export function downloadDoorAppletCodePoster (params:any) {
  return http.get('/api/linkc/outside/linkc/v3b/doorType/downloadPosterWithApplet', params)
}

/**
 * @description 默认访问门户小程序配置 
 * */ 
export function defaultAccessPortalAppletConfiguration(params: any) {
  return http.post('/api/im/outside/pc/doorApplet/doorAppletSetting', params)
}

/**
 * @description 获取订单状态对应总数
 */
export function getOrderCountByState(params: any) {
  return http.post('/api/linkc/outside/linkc/v3b/order/getOrderCountByState', params)
}