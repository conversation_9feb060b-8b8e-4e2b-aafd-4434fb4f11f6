import http from '@src/util/http';

/**
 * 保存增值线索。
 * @param params 包含要保存的增值线索类型的对象。
 * @param params.type 增值线索类型。 1-在线客服 2-呼叫中心 3-视频客服
 * type: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | 13 | 14 | 15 | 16 | 17 | 18 | 19 | 20 | 21 | 22 | 23 | 24 | 25
 */
export async function saveValueAddedClue(params: {}){
  const res = await http.post('/api/app/outside/valueAddedClue/save', params)
  return !!res?.success
}