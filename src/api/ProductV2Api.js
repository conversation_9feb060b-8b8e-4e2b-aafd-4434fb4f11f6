import http from '@src/util/http';
import { getTimestamp } from 'pub-bbx-utils';
// import {isLocal} from '@src/util/function';
// const headUrl = isLocal()?'':"/api/customer";
const headUrl = '/api/customer';
const ruleHeader = '/api/linkc';

let headUrl2 = '/api/elasticsearch/outside/es'

/** ------------ start 规则 ----------------------- */
/**
 * 获取开关状态
 * @param params
 * @returns {*}
 */
function productConfig(){
  return http.get('/setting/product/productConfig');
}

/**
 * 产品类型选项start
 * @param params
 * @returns {*}
 */
function saveProductMenu(params){
  return http.post(`${headUrl}/outside/pc/catalog/saveProductType`,params);
}

function moveProductType(params){
  return http.get(`${headUrl}/outside/pc/catalog/moveProductType`,params);
}

function dateCalculator(params){
  return http.post(`${headUrl}/outside/formula/date/calculator`,params);
}

// 产品二维码配置向导
function productTypeData(){
  return http.get('/setting/productTypeData');
}

function saveField4Qrcode(params){
  return http.post('/setting/product/saveField4Qrcode',params,false)
}
function saveEventType(params){
  return http.post('/setting/product/saveEventType',params,false)
}
/**
 * 产品类型选项end
 * @param params
 * @returns {*}
 */
 function getProductMenu(){
  return http.get(`${headUrl}/outside/pc/catalog/productTypeList`);
}

// 复制产品类型备件、知识库
function clonePartOrWiki(params){
  return http.get(`${headUrl}/outside/pc/catalog/clonePartOrWiki`, params);
}

/**
 * 获取产品类型
 * @param params
 * @returns {*}
 */
function queryCatalogsByPage(params){
  return http.get(`${ruleHeader}/outside/superCode/product/queryCatalogsByPage`, params);
}

/**
 * 获取规则信息
 * @param params
 * @returns {*}
 */
function queryRuleInfo(params){
  return http.get(`${ruleHeader}/outside/superCode/rule/queryRuleInfo`, params);
}

/**
 * 获取字段
 * @param params
 * @returns {*}
 */
function queryApplyOptions(params){
  return http.get(`${ruleHeader}/outside/superCode/rule/queryApplyOptions`, params);
}

/**
 * 新建规则
 * @param params
 * @returns {*}
 */
function addDistributeRule(params){
  return http.post(`${ruleHeader}/outside/superCode/rule/addDistributeRule`, params);
}

/**
 * 获制定人员
 * @param params
 * @returns {*}
 */
function queryCSByCondition(params){
  return http.get(`${ruleHeader}/outside/superCode/rule/queryCSByCondition`, params);
}

/**
 * 获取规则列表
 * @param params
 * @returns {*}
 */
function queryAllRules(params){
  return http.get(`${ruleHeader}/outside/superCode/rule/queryAllRules`, params);
}

/**
 * 删除规则
 * @param params
 * @returns {*}
 */
function removeDistributeRule(params){
  return http.post(`${ruleHeader}/outside/superCode/rule/removeDistributeRule`, params);
}
/**
 * 移动规则
 * @param params
 * @returns {*}
 */
function moveRule(params){
  return http.post(`${ruleHeader}/outside/superCode/rule/saveSort`, params);
}

/**
 * 编辑规则
 * @param params
 * @returns {*}
 */
function modifyDistributeRule(params){
  return http.post(`${ruleHeader}/outside/superCode/rule/modifyDistributeRule`, params);
}

/**
 * 启用/禁用规则
 * @param params
 * @returns {*}
 */
function modifyDistributeRuleState(params){
  return http.post(`${ruleHeader}/outside/superCode/rule/modifyDistributeRuleState`, params);
}

/**
 * 启用/禁用功能设置
 * @param params
 * @returns {*}
 */
function saveFunc(params){
  return http.post('/setting/product/config/save', params, false);
}

/**
 * 修改二维码配置
 * @param params
 * @returns {*}
 */
function qrCodeSetting(params){
  return http.post('/api/customer/outside/code/qrCodeSetting', params);
}

/**
 * 获取系统配置信息
 * @param params
 * @returns {*}
 */
export function getTenantConfig(params){
  return http.post(`${headUrl}/outside/tenant/getTenantConfig`, params);
}

/**
 * 修改系统相关配置
 * @param params
 * @returns {*}
 */
 export function editTenantConfig(params){
  return http.post(`${headUrl}/outside/tenant/tenantConfig/edit`, params);
}

/**
 * 修改系统单个配置
 * @param params
 * @returns {*}
 */
export function editTenantSingleConfig(params){
  return http.post(`${headUrl}/outside/tenant/tenantConfig/editSingleConfig`, params);
}

/** ------------ start 产品 ----------------------- */
/**
 * 获取产品类型表单
 * @param params
 * @returns {*}
 */
function getProductMenuField(params) {
  return http.get(`${headUrl}/outside/pc/catalog/setting/select`, params)
}

/**
 * 保存产品类型表单
 * @param params
 * @returns {*}
 */
function setProductMenuField(params) {
  return http.post(`${headUrl}/outside/pc/catalog/setting/save`, params);
}

/**
 * 目录树设置详情
 * @param params
 * @returns {*}
 */
function setInfoTreeList(params) {
  return http.post(`${headUrl}/outside/pc/catalog/saveInfo`, params);
}

/**
 * 目录列表获取
 * @param params
 * @returns {*}
 */
function getPageList(params) {
  return http.post(`${headUrl}/outside/pc/catalog/pageList`, params);
}

/**
 * 目录列表详情获取
 * @param params
 * @returns {*}
 */
function getPageInfo(params) {
  return http.get(`${headUrl}/outside/pc/catalog/info`, params)
}

// 新建产品类型
function catalogCreate(params){
  return http.post(`${headUrl}/outside/pc/catalog/create`, params);
}

/**
 * 目录详情关联产品列表查询
 * @param params
 *   id : 目录id
 * @returns {*}
 */
function getPageProduct(params) {
  return http.get(`${headUrl}/outside/pc/catalog/product`, params);
}

/**
 * 目录详情关联产品
 * @param params
 *   id : 目录id
 * @returns {*}
 */
function setPageRelationProduct(params) {
  return http.post(`${headUrl}/outside/pc/catalog/relationProduct`, params);
}

/**
 * 目录详情产品备件列表查询
 * @param params
 *   id : 目录id
 * @returns {*}
 */
function getPageLinkPart(params) {
  return http.get(`${headUrl}/outside/pc/catalog/part`, params);
}

/**
 * 目录详情关联知识库列表查询
 * @param params
 *   id : 目录id
 * @returns {*}
 */
function getPageLinkWiki(params) {
  return http.get(`${headUrl}/outside/pc/catalog/wiki`, params);
}

/**
 * 目录详情产品备件或知识库
 * @param params
 *   catalogId : 目录id
 *   relationIds: 关联id
 *   type ： 关联类型 part wiki
 * @returns {*}
 */
function setPagerelationPartOrWiki(params) {
  return http.post(`${headUrl}/outside/pc/catalog/relationPartOrWiki`, params);
}

/**
 * 目录详情知识库列表查询
 * @param params
 *   id : 目录id
 * @returns {*}
 */
function getPageWiki(params) {
  return http.get(`${headUrl}/outside/pc/catalog/getWiki`, params);
}

/**
 * 目录详情关联知识库列表查询
 * @param params
 *   id : 目录id
 * @returns {*}
 */
function setPageInfo(params) {
  if(params){
    params.createTime = getTimestamp(params?.createTime)
    params.updateTime = getTimestamp(params?.updateTime)
  }
  return http.post(`${headUrl}/outside/pc/catalog/edit`, params);
}

/**
 * 获取可以克隆的目录列表
 * @param params
 *   id : 目录id
 * @returns {*}
 */
function getPageCloneData(params) {
  return http.get(`${headUrl}/outside/pc/catalog/cloneData`, params);
}

/**
 * 获取关联的备件列表
 * @param params
 *   id : 目录id
 * @returns {*}
 */
function getPagePart(params) {
  return http.get(`${headUrl}/outside/pc/catalog/getPart`, params);
}

/**
 * 获取关联的知识库列表
 * @param params
 *   id : 目录id
 * @returns {*}
 */
function getPagelinkWiki(params) {
  return http.get(`${headUrl}/outside/pc/catalog/getWiki`, params);
}

/**
 * 目录重命名
 * @param params
 *   id : 目录id
 * @returns {*}
 */
function renameTree(params) {
  return http.post(`${headUrl}/outside/pc/catalog/updateTree`, params);
}

/**
 * 目录产品备件知识库单独删除
 * @param params
 *   id : 目录id
 * @returns {*}
 */
function removePartOrWiki(params) {
  return http.post(`${headUrl}/outside/pc/catalog/removePartOrWiki`, params);
}

/**
 * 目录清空内容
 * @param params
 *   id : 目录id
 * @returns {*}
 */
function clearCatalogData(params) {
  return http.get(`${headUrl}/outside/pc/catalog/clearCatalogData`, params);
}

/**
 * 目录动态信息
 * @param params
 *   id : 目录id
 * @returns {*}
 */
function getMenuRecord(params) {
  return http.get(`${headUrl}/outside/pc/catalog/record`, params);
}

/**
 * 查询目录树某个节点
 * @param params
 *   id : 目录id
 * @returns {*}
 */
function getTreeListNode(params) {
  return http.get(`${headUrl}/outside/pc/catalog/getTreeListNode`, params);
}

/**
 * 克隆某个目录
 * @param params
 *   id : 目录id
 * @returns {*}
 */
function cloneMenu(params) {
  return http.get(`${headUrl}/outside/pc/catalog/clone`, params);
}

/**
 * 目录详情tab的数量
 * @param params
 *   id : 目录id
 * @returns {*}
 */
function productMenuStatistics(params) {
  return http.get(`${headUrl}/outside/pc/catalog/relation/statistics`, params);
}

/**
 * 目录详情tab的数量
 * @param params
 * @returns {*}
 */
function getPageLinkProduct(params) {
  return http.get(`${headUrl}/outside/pc/catalog/product`, params);
}

/**
 * 搜索产品列表轻量
 * @param params
 *   id : 产品id
 * @returns {*}
 */
function searchProduct(params) {
  return http.get(`${headUrl}/outside/pc/product/getCloneProductData`, params);
}

/**
 * 搜索未绑定的二维码
 * @param params
 *   id : 产品id
 * @returns {*}
 */
function searchQrcode(params) {
  return http.get(`${headUrl}/outside/pc/product/qrcode`, params);
}

/**
 * 搜索所有的产品类型
 * @param params
 * @returns {*}
 */
function searchAllcatalog(params) {
  return http.get(`${headUrl}/outside/pc/catalog/catalogList`, params);
}

/**
 * 获取产品列表所需表单
 * @param params
 *   id : 产品id
 * @returns {*}
 */
function getListProductFields(params) {
  return http.get(
    `${headUrl}/outside/pc/product/getProductAndCatalogTemplate`,
    params
  );
}


/**
 * 获取产品列表有无目录的数量
 * @param params
 *   id : 产品id
 * @returns {*}
 */
function getProductLinkCatalogCount(params) {
  return http.get(
    `${headUrl2}/product/catalogCount`,
    params
  );
}

/**
 * 获取克隆产品类型树接口
 * @returns {*}
*/
function cloneData(params){
  return http.get(`${headUrl}/outside/pc/catalog/cloneData`, params);
}

/**
 * 产品类型关联产品
 * @param params
 *    catalogId: 产品类型id
 *    productIds: 产品id们
 * @returns {*}
 */
function relationProduct(params){
  return http.post(`${headUrl}/outside/pc/catalog/relationProduct`, params);
}

/**
 * 产品类型关联产品，获取产品下拉列表
 * @param params
 *    keyWord
 *    pageNum
 *    pageSize
 * @returns {*}
 */
function productList(params){
  return http.get(`${headUrl}/outside/pc/catalog/productList`, params);
}

/**
 * 产品取消关联产品类型
 * @param params
 *    productId
 * @returns {*}
 */
function unRelationCatalog(params){
  return http.get(`${headUrl}/outside/pc/product/unRelationCatalog`, params);
}
/**
 * 获取产品详情页右侧组件筛选存储
 * @param params
 * @returns {*}
 */
function getCardCache() {
  return http.get(`${headUrl}/outside/pc/product/getCardCache`, {})
}
/**
 * 设置产品详情页右侧组件筛选存储
 * @param params
 *  Array
 * @returns {*}
 */
function setCardCache(params) {
  return http.post(`${headUrl}/outside/pc/product/setCardCache`, params)
}

/**
 * 目录树获取
 * @param params
 * @returns {*}
 */
function getTreeList(params) {
  return http.get(`${headUrl}/outside/pc/catalog/treeList`, params);
}

/**
 * 目录树新增
 * @param params
 * @returns {*}
 */
function setTreeList(params) {
  return http.post(`${headUrl}/outside/pc/catalog/saveTree`, params);
}

/**
 * 产品目录树编辑
*/
function updateTree(params){
  return http.post(`${headUrl}/outside/pc/catalog/updateTree`, params)
}

/**
 * 目录树删除
 * @param params
 * @returns {*}
 */
function delTreeList(params) {
  return http.post(`${headUrl}/outside/pc/catalog/batchDelete`, params);
}

/**
 * 目录树排序
 * @param params
 * @returns {*}
 */
function sortTreeList(params) {
  return http.post(`${headUrl}/outside/pc/catalog/treeSort`, params);
}

/**
 * 质保功能开关启用/禁用功能设置
 * @param params
 * @returns {*}
 */
function qualitySave(params){
  return http.post(`${headUrl}/outside/quality/config/save`, params, false);
}

/**
 * @description 获取质保功能设置开关是否启用
 */
function getQualityConfig(params){
  return http.post(`${headUrl}/outside/quality/config`, params);
}

/**
 * @description 获取质保功能设置开关是否显示
 */
function getQualityConfigGray(params){
  return http.get(`${headUrl}/outside/quality/grayFunction`, params);
}

/**
 * @description 获取关联修改产品列表
 * @param params
 * @returns {*}
 */
function getRelationModifyProductList(params) {
  return http.post(`${headUrl}/outside/pc/catalog/catalog/related`, params);
}

/**
 * @description 产品类型关联产品数据同步修改
 */
function productTypeRelationProductModify(params) {
  return http.post(`${headUrl}/outside/pc/catalog`, params);
}

/**
 * @description 产品类型关联产品数据不修改
 */
function productTypeRelationProductNotModify(params) {
  return http.post(`${headUrl}/outside/pc/catalog`, params);
}

/**
 * @description 获取产品类型详情
 */
function getProductCatalogInfo(params) {
  return http.get(`${headUrl}/outside/pc/catalog/catalog`, params);
}

/**
 * 获取产品质保变更日志列表
 * @param {Object} params - 参数
 * @param {String} params.primaryId - 产品id
 * @param {Number} params.pageNum - 页码
 * @param {Number} params.pageSize - 页面大小
 */
function getProductQualityInfoRecordList(params) {
  return http.post(`${headUrl}/outside/pc/customer/product/quality/list`, params)
}

/**
 * 批量编辑产品类型
 */
function batchEditProductType(params) {
  return http.post(`${headUrl}/outside/pc/catalog/batchUpdate`, params)
}

/**
 * 关联客户
 * @param {*} params 
 */
function bindCustomer(params){
  return http.post(`${headUrl}/outside/pc/product/bindCustomer`,params)
}

/*
 * 第一次开启解绑
 */
function getFirstSettingUnBindCustomer() {
  return http.get(`${headUrl}/outside/pc/userCache/getFirstSettingUnBindCustomer`);
}

/*
 路程 2021-10-18添加 产品注册信息
*/
/**
 * 得到配置
 * @param {*} params 
 */
 function getConfig(params){
  return http.post(`${headUrl}/outside/tenant/register/config`,params)
}
/**
 * 修改配置
 * @param {*} params 
 */
 function setConfig(params){
  return http.post(`${headUrl}/outside/tenant/register/config/edit`,params)
}
/**
 * 是否开启配置
 * @param {*} params 
 */
 function isDisplay(){
  return http.get(`${headUrl}/outside/register/display`)
}
/**
 * 得到产品或者客户与产品注册的关联关系
 * @param {*} params 
 */
function getMapping(params){
  return http.post(`${headUrl}/outside/register/product/mapping`, params)
}
/**
 * 根据产品注册id得到产品相关的映射值
 * @param {*} params 
 */
 function getProductValue(){
  return http.get(`${headUrl}/outside/register/product/value`)
}
/**
 * 根据类型与目标表得到对应的字段
 * @param {*} params 
 */
 function getType(params){
  return http.post(`${headUrl}/outside/register/field/type`, params)
}
function getCardFieldAll(params){
  return http.post(`${headUrl}/outside/card/cardFieldAll`, params)
}
function getCardInfoRecordList(params){
  return http.post(`${headUrl}/outside/card/cardInfoRecordList`, params)
}
function getIsRelationPart(params){
  return http.get(`${headUrl}/outside/card/product/isRelationPart`, params)
}

function productCheckAuth(params){
  return http.get(`${headUrl}/outside/pc/product/checkAuth`, params)
}
// 产品类型下的物料清单
function getProductTypeMaterielBill(params) {
  return http.post(`${headUrl}/outside/pc/catalog/materielList`, params)
}
// 产品物料清单优先级
function sortMaterial(params) {
  return http.post(`${headUrl}/outside/pc/catalog/relation/material/primary`, params)
}
// 产品类型关联物料
function relationMateriel(params) {
  return http.post(`${headUrl}/outside/pc/catalog/relationMateriel`, params)
}
// 产品类型删除物料
function deleteMateriel(params) {
  return http.post(`${headUrl}/outside/pc/catalog/deleteMateriel`, params)
}
// 产品类型附加组件数据统计
function amountCount(params) {
  return http.get(`${headUrl}/outside/pc/catalog/count`, params)
}
// 获取物料清单
function getMaterielPage(params) {
  return http.post(`${headUrl}/outside/pc/catalog/materielPage`, params)
}

// 获取物料Bom
function getMaterialForBomList(params) {
  return http.post(`/api/warehouse/outside/bom/pic/page`, params)
}

// 根据物料id查询详情
function editGet(params = {}) {
  return http.get(`/api/warehouse/outside/material/get`, params);
}

// 获取产品类型有物料清单的产品
function filterProducts(params) {
  return http.post(`${headUrl}/outside/pc/product/filterProducts`, params)
}

// 产品类型远程搜索
function queryCatalogTree(params = {}) {
  return http.get(`${headUrl}/outside/pc/catalog/queryCatalogTree`, params)
}


// 产品质保管理系统设置
// 质保服务项目列表
function productQualityManageList(params = {}) {
  return http.post(`/api/app/outside/product/qaProject/page`, params)
}
// 质保服务项目新建
function productQualityManageListSave(params = {}) {
  return http.post(`/api/app/outside/product/qaProject/save`, params)
}
// 质保服务项目编辑
function productQualityManageListUpdate(params = {}) { 
  return http.post(`/api/app/outside/product/qaProject/update`, params)
}
// 质保服务项目删除
function productQualityManageListRemove(params = {}) { 
  return http.post(`/api/app/outside/product/qaProject/remove`, params)
}
// 质保服务项目开关
function productQualityManageListState(params = {}) {
  return http.post(`/api/app/outside/product/qaProject/update/state`, params)
}

// 产品质保信息附加组件
function productQualityInfoList(params = {}) {
  return http.post(`/api/app/outside/product/qaProjectRel/page`, params)
}
function productQualityInfoSave(params = {}) {
  return http.post(`/api/app/outside/product/qaProjectRel/save`, params)
}
function productQualityInfoUpdate(params = {}) { 
  return http.post(`/api/app/outside/product/qaProjectRel/update`, params)
}
function productQualityInfoRemove(params = {}) { 
  return http.post(`/api/app/outside/product/qaProjectRel/remove`, params)
}

// 故障库
function productFaultRecord(params = {}) {
  return http.post(`/api/library/outside/task/fault/card/getProductFaultRecord`, params)
}

// 产品类型故障
function productTypeFaultRecord(params = {}) {
  return http.post(`/api/library/outside/fault/library/productSelectFaultTree`, params)
}

/**
 * @des 搜索产品通过树形分页查询
 * @see 
 */
export function searchProductByTreeModeListPage(params){
  return http.post('/api/customer/outside/pc/catalog/queryCatalogCascade', params);
}

/** 校验产品类型关联的服务项目是否将被删掉*/
export function checkRelatedServiceItem(params){
  return http.post('/api/customer/outside/pc/catalog/checkRelatedServiceItem', params);
}

export function getCatalogList(params = {}) {
  return http.post('/api/customer/outside/pc/catalog/simple/list', params);
}


export function getWarrantyCardInfo(params = {}) {
  return http.get('/api/linkc/outside/linkc/v3b/extended/warranty/card/getByProduct', params);
}

export {
  getIsRelationPart,
  getCardFieldAll,
  getCardInfoRecordList,
  getType,
  getMapping,
  getProductValue,
  getConfig,
  setConfig,
  isDisplay,
  getFirstSettingUnBindCustomer,
  bindCustomer,
  getProductMenuField,
  setProductMenuField,
  getTreeList,
  setTreeList,
  setInfoTreeList,
  sortTreeList,
  delTreeList,
  getPageList,
  getPageInfo,
  catalogCreate,
  setPageInfo,
  getPageProduct,
  getPageLinkPart,
  getPageLinkWiki,
  getPageLinkProduct,
  setPageRelationProduct,
  setPagerelationPartOrWiki,
  getPageWiki,
  getPageCloneData,
  getPagePart,
  getPagelinkWiki,
  renameTree,
  removePartOrWiki,
  clearCatalogData,
  getMenuRecord,
  getTreeListNode,
  cloneMenu,
  productMenuStatistics,
  searchProduct,
  searchQrcode,
  searchAllcatalog,
  getListProductFields,
  getProductLinkCatalogCount,
  queryAllRules,
  removeDistributeRule,
  moveRule,
  modifyDistributeRule,
  saveFunc,
  qrCodeSetting,
  queryCSByCondition,
  addDistributeRule,
  queryApplyOptions,
  queryRuleInfo,
  productConfig,
  queryCatalogsByPage,
  modifyDistributeRuleState,
  cloneData,
  productList,
  relationProduct,
  unRelationCatalog,
  clonePartOrWiki,
  getCardCache,
  setCardCache,
  updateTree,
  qualitySave,
  getProductMenu,
  saveProductMenu,
  moveProductType,
  productTypeData,
  saveField4Qrcode,
  saveEventType,
  getRelationModifyProductList,
  productTypeRelationProductModify,
  productTypeRelationProductNotModify,
  getProductCatalogInfo,
  getQualityConfig,
  getProductQualityInfoRecordList,
  batchEditProductType,
  getQualityConfigGray,
  productCheckAuth,
  getProductTypeMaterielBill,
  relationMateriel,
  deleteMateriel,
  sortMaterial,
  amountCount,
  getMaterielPage,
  getMaterialForBomList,
  editGet,
  filterProducts,
  queryCatalogTree,
  productQualityManageList,
  productQualityManageListSave,
  productQualityManageListUpdate,
  productQualityManageListRemove,
  productQualityManageListState,
  productQualityInfoList,
  productQualityInfoSave,
  productQualityInfoUpdate,
  productQualityInfoRemove,
  productFaultRecord,
  productTypeFaultRecord,
  dateCalculator
}
