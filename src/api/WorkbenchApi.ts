import http from '@src/util/http';
import Result from '@model/Result';
const prefix = '/api/workbench/outside';

export function checkResult(res: any) {
  if (res.succ || res.success || res.code === 0 || res.status === 0) {
    return res.data;
  } 
  return Promise.reject(res);
  
}

// 获取初始信息(企业版本， 灰度信息)
export async function fetchInitialData() {
  const res = await http.get(`${prefix}/get/initial/data`);
  return checkResult(res);
}

/**
 * 获取快捷入口配置
 * @param {*} params
 */
export async function getQuickEntrance() {
  const res = await http.get(`${prefix}/wb/quick/entrance`);
  return checkResult(res);
}

/**
 * 保存快捷入口配置
 * @param {*} params
 */
export async function saveEntrance(params: {} | undefined) {
  const res = await http.post(`${prefix}/wb/quick/entrance/save`, params);
  return checkResult(res);
}

/**
 * @description 获取工作台首页个人今日日程
 */
export function getWorkbenchTodayCalendar() {
  return http.get('/api/workbench/outside/person/getTodayCalendar')
}

/**
 * @description 根据身份证照片识别
 */
export function getWorkbenchIdCardOcr(params: {}) {
  return http.post('/api/workbench/outside/ocr/identityCardRecognition', params)
}

/**
 * @description 根据身份证照片判断余量（是否可用）
 */
export function getWorkbenchIdCardOrcCheck(params: {}) {
  return http.get('/api/workbench/outside/ocr/queryIdentityCardMargin', params)
}

/**
 * @description 消息提醒
 */

export function fetchWorkbenchRemind(params: {}) {
  return http.post('/api/workbench/outside/ocr/identityCardRemind', params)
}

/**
 * @description 消息提醒类型
 */

export function fetchWorkbenchRemindTypeList(params: {}) {
  return http.get('/api/workbench/outside/wb/remindList', params)
}

// 时区数据类型
export interface Timezone {
  id: string
  introduce: string
  originalOffset: number
  zoneId: string
}

// 获取时区列表
export async function fetchTimezoneList(){

  const res: Result<Timezone[]> = await http.get('/api/workbench/outside/time/zone/getTimeZoneList');
  if(res.succ || res.status === 0){
    return res.data
  }
  
  return Promise.reject(res)
}


export const fetchGetSettingMenusViewDetail = (id: number)=> {
  return http.get(`/api/workbench/outside/setting/getEditViewSetting/${id}`)
}

export const fetchGetSettingViewList = ()=> {
  return http.get(`/api/workbench/outside/setting/getViewList`)
}


export const fetchSaveSettingViewList = (params = {})=> {
  return http.post(`/api/workbench/outside/setting/saveViewSetting`, params)
}

export const fetchSaveSettingViewAuthority= (params = {})=> {
  return http.post(`/api/workbench/outside/setting/saveViewAuthority`, params)
}

export const fetchRemoveSettingViewAuthority= (id: number)=> {
  return http.post(`/api/workbench/outside/setting/delViewAuthority/${id}`)
}

export const fetchRemoveSettingView= (id: number)=> {
  return http.get(`/api/workbench/outside/setting/deleteViewSetting/${id}`)
}


export const workbenchMessageWebsocketUrl = '/api/workbench/message/last/unread';
