/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2021-03-11 09:49:05
 * @LastEditTime: 2025-05-20 11:46:52
 * @LastEditors: 铃兰 <EMAIL>
 * @Description: 服务台
 * You build it, You run it.
 */
import http from '@src/util/http'
import { objectDateToTimestamp } from 'pub-bbx-utils'

/**
 * eventSearchModel 类型时间处理
 * @param {*} params 
 * @returns 
 */
export function disposeEventSearchModelDate (params){
  objectDateToTimestamp(params, [
    'createTimeStart',
    'createTimeEnd',
    'planStartTime',
    'planStartTimeStart',
    'planStartTimeEnd',
    'planEndTime',
    'planEndTimeStart',
    'planEndTimeEnd',
    'allotTimeStr',
    'allotTimeStart',
    'allotTimeEnd',
    'startTimeStr',
    'startTimeStart',
    'startTimeEnd',
    'completeTimeStart',
    'completeTimeEnd',
    'evaluateTimeStart',
    'evaluateTimeEnd',
  ])
  return params
}

/** 
 * @description 服务事件列表-获取页面所需的initData信息
 * @param {Object} params 参数对象
 */
export function queryEventPageInfo(query = {}, params = {}) {
  const isOrderList = query.isOrderList ? query.isOrderList : false
  const isAllTemplate = query.isAllTemplate ? query.isAllTemplate : false
  disposeEventSearchModelDate(params)
  return http.post(`/event/list/page/info?isOrderList=${isOrderList}&isAllTemplate=${isAllTemplate}`, params)
}

/** 
 * @description 服务事件列表-获取分页数据
 * @param {Object} params 参数对象
 */
export function queryEventPageList(query = {}, params = {}) {
  const isOrderList = query.isOrderList ? query.isOrderList : false
  disposeEventSearchModelDate(params)
  return http.post(`/event/list/page?isOrderList=${isOrderList}`, params)
}

/**
 * 
 * @param {*} params 
 * @returns 
 */
export function fetchNoFinishedEvents(params = {}){
  disposeEventSearchModelDate(params)
  return http.post('/event/sum/noFinishedEvents', params)
}

/**
 * 
 * @param {*} params 
 * @returns 
 */
export function fetchNoFinishedEventsList(params = {}){
  disposeEventSearchModelDate(params)
  return http.post('/event/sum/noFinishedEvents/list', params)
}

/**
 * 服务记录--历史服务事件
 * @param {*} params 
 */
export function getEventHistoryList(params = {}) {
  disposeEventSearchModelDate(params)
  return http.post('/event/list4CallCenterHistory', params)
}

/** 
 * @description 获取事件类型列表
 */
export function getEventTypeAllList(params = {}) {
  return http.get('/event/eventType/allList', params)
}

/** 
 * @description 异步获取字段信息
 */
export function getEventTemplateFields(params = {}) {
  return http.get('/setting/getEventTemplateFieldList', params)
}

/** 
 * @description 列表页/详情页-删除
 */
export function eventDelete(params = {}) {
  return http.post('/event/delete', params)
}

/** 
 * @description 列表页/批量编辑
 */
export function eventBatchEdit(params = {}) {
  return http.post('/event/editBatch', params, false)
}

/** 
 * @description 列表页/批量转派
 */
export function eventRedeployBatch(params = {}) {
  return http.post('/event/redeployBatch', params)
}

/** 
 * @description 事件订阅
 */
export function eventSubscribe(params = {}) {
  return http.get('/event/subscribe', params)
}

/** 
 * @description 事件退订
 */
export function eventUnSubscribe(params = {}) {
  return http.get('/event/unSubscribe', params)
}

/** 
 * @description 异步获取客户列表
 */
export function queryEventCustomerList(params = {}) {
  return http.get('/event/customer/list', params)
}

/**
 * @description 获取客户/产品 关联的工单数量数据
 */
export function getCountForCreate(params) {
  return http.get('/event/getCountForCreate', params, false);
}

/**
 * @description 获取客户联系人和地址数据
 * @param {Object} params -- 参数对象
 * @param {String} params.customerId -- 客户id
 * @param {String} params.productId -- 产品id
 * @param {String} params.notNull -- 分页数
 */
export function getEventDefaultInfo(params) {
  return http.get('/event/defaultInfo', params);
}

/**
 * @description 获取客户产品数据
 * @param {Object} params -- 参数对象
 * @param {String} params.pageSize -- 页码大小
 * @param {String} params.pageNum -- 分页数
 * @param {String} params.keyword -- 关键字
 * @param {String} params.customerId -- 客户id
 */
export function getEventCustomerProduct(params) {
  return http.post('/api/customer/outside/pc/product/list', params);
}

/**
 * @description 查询客户产品关联字段
 * @param {Object} params -- 参数对象
 * @param {String} params.module -- 模块 customer/product
 * @param {String} params.id -- 客户id
 * @param {String} params.fieldName -- 字段名称
 * @param {String} params.formType -- 字段类型
 */
export function getEventRelatedInfo(params) {
  return http.get('/event/getRelatedInfo', params);
}


/**
 * @description 新的接口 查询客户产品关联字段
 * @param {Object} params -- 参数对象
 * @param {String} params.module -- 模块 customer/product
 * @param {String} params.id -- 客户id
 * @param {String} params.fieldName -- 字段名称
 * @param {String} params.formType -- 字段类型
 */
export function getEventNewRelatedInfo(params) {
  return http.post('/api/customer/outside/pc/customer/getRelatedInfos', params);
}


/**
 * @description 内部创建事件
 */
export function eventCreateInner(params) {
  return http.post('/event/createInner', params);
}

/**
 * @description 获取事件信息
 */
export function getEventById(params) {
  return http.get('/event/getEventById', params);
}

/**
 * @description 查询由事件转成的关联工单
 */
export function getEventRelatedTask(params) {
  return http.get('/event/eventRelatedTask', params);
}

/**
 * @description 事件日志列表
 */
export function getEventRecord(params) {
  return http.get('/event/view/eventRecord', params);
}

/**
 * @description 修改协同人
 */
export function eventUpdateSynergy(params) {
  return http.post('/event/updateSynergy', params);
}

/**
 * @description 修改负责人
 */
export function eventUpdateExecutor(params) {
  return http.post('/event/updateExecutor', params);
}

/**
 * @description 事件取消
 */
export function eventOff(params) {
  return http.post('/event/off', params);
}

/**
 * @description 事件继续
 */
export function eventUnPause(params) {
  return http.get('/event/unpause', params);
}

/**
 * @description 事件暂停
 */
export function eventPause(params) {
  return http.get('/event/pause', params);
}

/**
 * @description 检查附件不为空
 */
export function eventCheckNotNullForCard(params) {
  return http.get('/event/checkNotNullForCard', params);
}

/**
 * @description 事件开始
 */
export function eventStart(params) {
  return http.get('/event/start', params);
}

/**
 * @description 事件结束
 */
export function eventFinish(params) {
  return http.post('/event/finish', params);
}

/**
 * @description 事件转工单验证审批
 */
export function eventCheck4ConvertApprove(params) {
  return http.get('/event/check4ConvertApprove', params);
}

/**
 * @description 获取转变客户信息
 */
export function eventGetInfo4CusChange(params) {
  return http.get('/event/getInfo4CusChange', params);
}

/**
 * @description 自动分配
 */
export function eventAutoDispatch(params) {
  return http.get('/event/autodispatch', params);
}

/**
 * @description 获取审批页面信息
 */
export function approveGet(params) {
  return http.get('/approve/get', params);
}

/**
 * @description 撤回审批
 */
export function approveOffApprove(params) {
  return http.get('/approve/offApprove', params);
}

/**
 * @description 事件回退
 */
export function eventRollBack(params) {
  return http.post('/event/rollBack', params);
}

/**
 * @description 事件生成日志
 */
export function eventCreateRecord(params, query) {
  return http.post(`/event/eventRecord/create${query || ''}`, params);
}

/**
 * @description 获取事件编辑/新建 initdata
 */
export function queryEventEditInitData(params = {}) {
  return http.get('/event/edit/info', params);
}

/**
 * @description 从呼叫中心跳到事件-获取页面所需的initData信息
 */
export function queryEventEdit4CallCenterInit(params = {}) {
  return http.get('/event/edit4CallCenter/init', params);
}

/**
 * @description 服务事件复制-获取页面所需的initData信息
 */
export function queryEventEditInitData4Copy(params = {}) {
  return http.get('/event/edit/info/copy', params);
}

/**
 * @description 服务事件从客户新建-获取页面所需的initData信息
 */
export function queryEventEditInitData4Customer(params = {}) {
  return http.get('/event/edit/info/customer', params);
}

/**
 * @description 服务事件从产品新建-获取页面所需的initData信息
 */
export function queryEventEditInitData4Product(params = {}) {
  return http.get('/event/edit/info/product', params);
}

/**
 * @description 获取事件详情 initdata
 */
export function queryEventViewInitData(params = {}) {
  return http.get('/event/view/info', params)
}

/**
 * @description 获取客户列表
 */
export function eventGetList4Convert(params = {}) {
  return http.get('/event/customer/getList4Convert', params);
}

/**
 * @description 事件转工单提醒
 */
export function eventUpdate4CusInfo(params = {}) {
  return http.post('/event/update4CusInfo', params, false);
}

/**
 * @description 校验是否有可用工单类型
 */
export function existValidTaskTypeForLoginUser(params = {}) {
  return http.get('/task/existValidTaskTypeForLoginUser', params, false);
}

/**
 * @description 回执暂存
 */
export function eventSaveReceiptDraft(params = {}) {
  return http.post('/event/saveReceiptDraft', params, true);
}

/**
 * @description 修改评价
 */
export function eventEvaluateUpdate(params = {}) {
  return http.post('/event/evaluateUpdate', params, true);
}

/**
 * @description 删除卡片内容
 */
export function eventDeleteCardInfo(params = {}) {
  return http.post('/event/deleteCardInfo', params, false);
}

/**
 * @description 编辑卡片内容
 */
export function eventEditCardInfo(params = {}) {
  return http.post('/event/editCardInfo', params, false);
}

/**
 * @description 保存卡片内容
 */
export function eventSaveCardInfo(params = {}) {
  return http.post('/event/saveCardInfo', params, false);
}

/**
 * @description 删除备注
 */
export function eventDeleteRecord(params = {}) {
  return http.post('/event/deleteEventRecord', params, false);
}

/**
 * 执行审批操作
 * @param {String | Id} params.id - 审批条目id
 * @param {String} params.result - 审批结果 'success' or 'fail'
 * @param {String} params.approveRemark - 审批备注
 */
export function applyApprove ({params, query}) {
  return http.post(`/approve/saveResult${query || ''}`, params, false)
}

/**
 * @description 获取事件最近更新记录
 * @param {Object} params - 参数
 * @param {String} params.evevntId - 事件id
 * @return {MsgModal<String>} 最近更新记录
 */
export function getEventUpdateRecord(params) {
  return http.get('/event/getLatestRecord', params, false)
  // return http.get('/api/event/service_case/outside/eventRecord/getLatestRecord', params, false)
}

/**
 * @description 保存联系人
 */
export function createLinkman(params) {
  return http.post('/event/linkman/create', params)
}

/**
 * 下载附件
 * @param {*} params 
 */
export function exportAttachment(params) {
  return http.post('/excels/eventAttachmentDownloadBatch', params);
}

/**
 * 校验事件
 * @param {*} params 
 */
export function calendarEventShow(params) {
  return http.post('/event/check/calendar/event', params);
}

/**
 * 开通事件
 * @param {*} params 
 */
export function openCalendarEvent(params) {
  return http.post('/event/open/calendar/event', params);
}
/**
 * 事件的邮件分享
 * @param {*} params 
 */
export function emailShare(params) {
  return http.post('/api/event/service_case/outside/event/share/emailShare', params);
}

/**
 * 获取公司邮箱
 * @param {*} params 
 */
export function getTenantEmail(params) {
  return http.get('/event/getTenantEmail', params);
}

/**
 * 判断问券方案是否变更
 * @param {*} params 
 */
export function judgeSatisfactionUidIsChange(params) {
  return http.get('/event/judgeSatisfactionUidIsChange', params);
}

/**
 * 获取事件客户满意度表单字段
 * @param {*} params 
 */
export function satisfactionFields(params) {
  return http.get('/event/satisfaction/fields', params);
}
/**
 * 校验事件自动分配是否可以匹配到负责人
 * @param {*} params 
 */
export function checkAutoDispatch(params) {
  return http.post('/event/checkAutoDispatch', params);
}
/**
 * 将链接转成短链
 * @param {*} params 
 */
export function getShortenUrl(params){
  return http.get('/api/event/service_case/outside/event/share/shortenUrl', params);
}

/**
 * 获取DomainId
 * @param {*} params 
 */
export function getDomainId(params){
  return http.get('/api/event/service_case/outside/event/share/getDomainId', params);
}

/**
 * 获取邮箱配置信息
 * @param {*} params 
 */
export function getEmailConfig(params){
  return http.get('/api/app/outside/email/emailConfig', params);
}

/**
 * 事件转工单子表单数据转换
 * @param {*} params 
 */
export function eventConversionTask(params){
  return http.get('/event/eventConversionTask', params);
}

/**
 * 事件转工单子表单数据转换
 * @param {*} params 
 */
export function getServiceEventTypeList(params){
  return http.get('/event/type', params);
}
/*
 * 获取亚码芬冰雪一键维修事件模板表格数据
 * @param {*} params 
 */
export function getAmerPrintBX(params){
  return http.get('/api/middleware/outside/amersports/print/as', params);
}

/**
 * 获取亚码芬始祖鸟一键维修，REBIRD创建事件，始祖鸟预约到店事件模板表格数据
 * @param {*} params 
 */
export function getAmerPrintOther(params){
  return http.get('/api/middleware/outside/amersports/print/Arc', params);
}

export function getEnabledEventCardList(params){
  return http.get('/setting/cardManage/getEnabledEventCardList', params);
}
// 事件高级审批初始化
export function createVipApproveFlowForEvent(params){
  return http.post('/api/event/service_case/outside/event/vipApprove/createVipApproveFlow', params);
}
// 判断事件是否需要高级审批
export function checkEventNeedVipApprove(params){
  return http.post('/api/event/service_case/outside/event/vipApprove/checkNeedVipApprove', params);
}
// 事件创建高级审批
export function createEventApproval(query, params){
  return http.post(`/api/event/service_case/outside/event/vipApprove/create${query}`, params);
}
// 事件高级审批查询按钮信息
export function getApproveLoadForEvent(params){
  return http.post('/api/event/service_case/outside/event/vipApprove/getApproveLoad', params);
}
// 事件高级审批-提交
export function toSendVipApproveForEvent(params, query){
  return http.post(`/api/event/service_case/outside/event/vipApprove/submit${query}`, params);
}
// 事件-客户
export function getEventOfCustomer(params){
  return http.post('/customer/event/list', params);
}
// 关闭事件
export function closeEvent(params){
  return http.post('/api/event/service_case/outside/event/closeEvent', params);
}