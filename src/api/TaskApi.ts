import http from '@src/util/http'
import { objectDateToTimestamp, getTimestamp } from 'pub-bbx-utils'
import {
	TaskCreateAndEditModel,
	PlanTaskCreateAndEditModel,
	TaskSearchListModel,
	TaskGetCardDetailListModel,
	TaskAllotUserListByTagModel,
	TaskAutoDispatchResultListModel,
	TaskUserCardSearchModel,
	TaskAllotUserSearchModel,
	TaskAllotApproveGetModel,
	TaskAllotModel,
	TaskTagListSearchModel,
	TaskTagUserListSearchModel,
	TaskAllotTaskPoolModel,
	TaskPoolAuthUsersGetModel,
	TaskPoolSubscriptionUsersGetModel,
	CustomerTahTaskPoolCountGetModel,
	TaskPoolSearchModel,
	TaskReAllotTaskPoolModel,
	TaskPlanTaskSearchModel,
	TaskSubscriceModel,
	TaskBatchBalanceModel,
	TaskCheckNotNullForCardModel,
	TaskReviewSendSmsModel,
} from '@model/param/in/Task'

import {
	getUserViewsResult,
	getTaskCountByStateResult,
	getTaskSearchListResult,
	getTaskFilterWithPartResult,
	getLatestRecordResult,
	getUserListByTagResult,
	getTaskConfigResult,
	getAutoDispatchResultListResult,
	getTaskUserCardInfoResult,
	getTaskAllotUserInfoResult,
	getTaskAllotApproveResult,
	getTaskAllotResult,
	getTaskTagListResult,
	getTaskAllotTaskPoolResult,
	getTaskPoolAuthUsersResult,
	getTaskPoolSubscriptionUsersResult,
	getCustomerTagTaskPoolCountResult,
	getTaskAllotTaskPollApproveResult,
	getTaskTypeResult,
	getTaskTypesResult,
	getTaskPlanTaskResult,
	getTaskPlanTaskDetailResult,
	getTaskPlanTaskInitDataResult,
	getTaskBalanceInitDataResult,
	getTaskSubscribeResult,
	getTaskUnSubscribeResult,
	getTaskBalanceCountResult,
	getTaskBalanceFieldInfoResult,
	getTaskBalancePriceResult,
	getBatchTaskBalanceResult,
	getCardDetailResult,
	getTurnOnTaskExceptionNodeInfoResult,
	getTaskCloseInitDataResult,
	getTaskCheckNotNullForCardResult,
	getCloseTaskResult,
	getTaskPoolInfoResult,
	getTaskPoolInitDataResult,
	getTaskReviewSendSmsDataResult,
	getTaskReviewInitDataResult,
	getTaskAllotInitDataResult,
	getBatchAllotResult,
	getBatchCreatePlanTaskResult
} from '@model/param/out/Task'

import GrayUtil from '@src/util/gray'
import { getSystemNormalSearchInputForLength } from '@model/utils/getSystemConfig'
import { disposeProductSearchModelDate } from './ProductApi'

const fixedPrefixTaskPath = '/api/task';
const ElASTICSEARCH = '/api/elasticsearch';
const fixedPrefixPaymentPath = '/api/payment';
const CUSTOMER = '/api/customer'
const APPS = '/api/app'
const prefixPartPath = '/api/part'
const REPORT = '/api/report'

/* ------------- start 旧工单api ---------------- */

export function disposeTaskSearchModelDate(params: any = {}){
  if(!params) return
  const stringKeys = [
    'timeStart',
    'timeEnd',
    'planTimeStr',
    'planTimeStart',
    'planTimeEnd',
    'planStartTimeStr',
    'planStartTimeStart',
    'planStartTimeEnd',
    'planEndTimeStr',
    'planEndTimeStart',
    'planEndTimeEnd',
    'completeTimeStart',
    'completeTimeEnd',
    'reviewTimeStr',
    'reviewTimeStart',
    'reviewTimeEnd',
    'allotTimeStr',
    'allotTimeStart',
    'allotTimeEnd',
    'reallotTimeStr',
    'reallotTimeStart',
    'reallotTimeEnd',
    'acceptTimeStr',
    'acceptTimeStart',
    'acceptTimeEnd',
    'startTimeStr',
    'startTimeStart',
    'startTimeEnd',
    'balanceTimeStr',
    'balanceTimeStart',
    'balanceTimeEnd',
    'closeTimeStr',
    'closeTimeStart',
    'closeTimeEnd',
    'updateTimeStart',
    'updateTimeEnd',
  ]


  objectDateToTimestamp(params, stringKeys)
  params.planStart = getTimestamp(params.planStart)
  params.planEnd = getTimestamp(params.planEnd)

  return
}

/**
 * 删除计划任务
 * @param {Object} params - 参数
 * @param {Array} params.ids - 计划id
 */
export function deletePlanTask(params: {} | undefined) {
	return http.post('/api/task/outside/pc/task/planTask/deletePlanTask', params);
}

/**
 * 工单表单 关联字段
 * module为 customer/product
 * id为已选择的客户或产品的id，选择的产品数量=1的时候才去查值赋值
 * fieldName和formType填这种字段setting里存的值
 * @param {*} params = {String module,String id,String fieldName,String formType}
 */
export function relatedFieldValue(params: {} | undefined) {
	return http.get('/dd/task/relatedFieldValue', params);
}

/**
 * @description 获取工单表单数据
 * @param {Object} params-- params
 * @param {String} params.tableName -- 表名
 * @param {String} params.templateId -- 类型模板id
 */
export function getTaskTemplateFields(params: {} | undefined) {
	return http.get('/task/getTaskTemplateFields', params);
}
/**
 * @description 获取工单满意度表单数据
 * @param {String} params.templateId -- 类型模板id
 */
 export function getSatisfactionFields(params: {} | undefined) {
	// return http.get('/task/getTaskTemplateFields', params);
	return http.get(`${fixedPrefixTaskPath}/outside/satisfaction/fields`,params);
}

/**
 * @description 获取工单表单数据(老版本，可以获取被隐藏的系统组件)
 * @param {Object} params-- params
 * @param {String} params.tableName -- 表名
 * @param {String} params.typeId -- 类型模板id
 */
export function getFields(params: {} | undefined) {
	return http.get('/setting/taskType/getFields', params);
}

/**
 * @description 获取工单表单数据
 * @param {Object} params-- params
 * @param {String} params.typeId -- 工单类型id
 * @param {String} params.tableName -- task:工单表单字段 task_receipt:工单回执表单字段
 * @param {String} params.isFromSetting -- 用于设置页全部显示，不用于设置页，则通过可见性和隐藏性来过滤字段
 */
export function getAllFields(params: {} | undefined) {
	return http.get('/setting/taskType/getAllFields', params);
}

/**
 * @description 是否是审批状态
 * @param {Object} params-- params
 * @param {String} params.taskId - 工单id
 */
interface Params {
	taskId?: string
}
export function checkQualityApprove(params: Params) {
	return http.get(`/api/app/outside/approve/checkQualityApprove/${params.taskId}`, );
}
/**
 * @description 质保信息取值
 * @param {Object} params-- params
 * @param {String} params.taskId - 工单id
 */
export function getQualityByTaskId(params: Params) {
	return http.get(`${fixedPrefixTaskPath}/outside/pc/task/getQualityByTaskId/${params.taskId}`);
}

/**
 * @description 质保信息取值
 * @param {Object} params-- params
 * @param {String} params.productIdList - 产品列表
 * @param {String} params.tenantId - tenantId
 */
 export function productQuality(params: Params) {
	return http.post(`/api/customer/outside/pc/task/quality`, params);
}
/**
 * @description 获取工单公共表单数据
 * @param {Object} params-- params
 * @param {String} params.typeId -- 工单类型id
 * @param {String} params.tableName -- task:工单表单字段 task:工单
 */
 export function getCommonFields(params: {} | undefined) {
	return http.get('/setting/taskType/getCommon', params);
}

/**
 * @description 获取工单高级搜索公共字段
 * @param {Object} params-- params
 * @param {String} params.tableName -- 表名
 * @param {String} params.typeId -- 类型模板id
 */
export function getCommon(params: {} | undefined) {
	return http.get('/setting/taskType/getCommon', params);
}

/**
 * @description 获取工单表单公共字段列表
 * @param {Object} params-- params
 * @param {String} params.tableName -- task:工单表单字段 task_receipt:工单回执表单字段
 */
export function getCommonFieldList(params: {} | undefined) {
	return http.get(`${fixedPrefixTaskPath}/outside/pc/task/getCommonFieldList`, params);
}


/**
 * @description 获取工单额外的字段
 * @param {Object} params-- params
 */
export function getTaskExtendFieldList(params: {} | undefined) {
	return http.post(`${APPS}/outside/msg/config/getCommonFieldList`, params);
}

/**
 * @description 确认开启工单设置灰度
 * @param {Object} params-- params
 * @param {String} params.isConfirm -- true为确认开启
 */
export function checkConfirmSettingGrayFunction(params: {} | undefined) {
	return http.get('/setting/checkConfirmSettingGrayFunction', params);
}

/**
 * @description 查询客户产品关联字段
 * @param {Object} params -- 参数对象
 * @param {String} params.module -- 模块 customer/product
 * @param {String} params.id -- 客户id
 * @param {String} params.fieldName -- 字段名称
 * @param {String} params.formType -- 字段类型
 */
export function getTaskRelatedInfo(params: {} | undefined) {
	return http.get('/task/getRelatedInfo', params);
}

/**
 * @description 获取客户数据
 * @param {Object} params -- 参数对象
 * @param {String} params.pageSize -- 页码大小
 * @param {String} params.pageNum -- 分页数
 * @param {String} params.keyword -- 关键字
 */
export function getTaskCustomerList(params: any = {}) {
	return http.post('/task/customer/list', params);
}

/**
 * @description 获取客户数据
 * @param {Object} params -- 参数对象
 * @param {String} params.pageSize -- 页码大小
 * @param {String} params.pageNum -- 分页数
 * @param {String} params.keyword -- 关键字
 */
export function getTaskCustomerListV2(params: {} | undefined) {
	return http.post('/api/customer/outside/pc/customer/list/V2', params, true, {cancelable: true});
}

/**
 * @description 获取客户产品数据
 * @param {Object} params -- 参数对象
 * @param {String} params.pageSize -- 页码大小
 * @param {String} params.pageNum -- 分页数
 * @param {String} params.keyword -- 关键字
 * @param {String} params.customerId -- 客户id
 */
export function getTaskCustomerProduct(params: {} | undefined) {
	// 所有涉及产品的es搜索 关键词超过三十个字自动截取前三十个字
	if((params as any)?.keyword){
		(params as any).keyword = getSystemNormalSearchInputForLength((params as any).keyword)
	}
  disposeProductSearchModelDate(params)
	return http.post('/api/customer/outside/pc/product/list', params);
}

/**
 * @description 获取客户联系人数据
 * @param {Object} params -- 参数对象
 * @param {String} params.pageSize -- 页码大小
 * @param {String} params.pageNum -- 分页数
 * @param {String} params.keyword -- 关键字
 * @param {String} params.customerId -- 客户id
 */
export function getTaskCustomerLinkman(params: {} | undefined) {
	return http.get('/api/elasticsearch/outside/es/linkman/list', params);
}

/**
 * @description 获取客户地址数据
 * @param {Object} params -- 参数对象
 * @param {String} params.pageSize -- 页码大小
 * @param {String} params.pageNum -- 分页数
 * @param {String} params.keyword -- 关键字
 * @param {String} params.customerId -- 客户id
 */
export function getTaskCustomerAddress(params: {} | undefined) {
	return http.get('/task/customer/address', params);
}


/**
 * @description 获取工单相关负责人
 * @param {Object} params -- 参数对象
 * @param {String} params.taskId -- 工单id
 */
 export function getTaskUser(params: {} | undefined) {
	return http.get('/api/task/outside/pc/task/getTaskUserInfo', params);
}

/**
 * @description 获取客户联系人和地址数据
 * @param {Object} params -- 参数对象
 * @param {String} params.customerId -- 客户id
 * @param {String} params.productId -- 产品id
 * @param {String} params.notNull -- 分页数
 */
export function getTaskDefaultInfo(params: {} | undefined) {
	return http.get('/task/defaultInfo', params);
}

/**
 * @description 通过产品id获取客户数据
 * @param {Object} params -- 参数对象
 * @param {String} params.id -- 产品id
 */
export function getCustomerByProduct(params: {} | undefined) {
	let productPreFixedPath = GrayUtil.getCustomerApiPath();
	return http.get(`${productPreFixedPath}/customer/product/detail/data`, params);
}

/**
 * @description 通过联系人id获取地址数据
 * @param {Object} params -- 参数对象
 * @param {String} params.lmId -- 产品id
 */
export function getLmBindAddress(params: {} | undefined) {
	return http.get('/task/getLmBindAddress', params);
}

/**
 * @description 获取新建客户相关数据
 */
export function getCreateCustomerData(params: {} | undefined) {
	return http.get('/task/getCustomerEditInitData', params);
}

/**
 * @description 获取新建产品相关数据
 */
export function getCreateProductData(params: {} | undefined) {
	return http.get('/task/getProducEditInitData', params);
}

/**
 * @description 获取备件配置信息
 */
export function getSparepartConfig(params: {} | undefined) {
	return http.post('/partV2/repertory/sparepartConfig', params);
}

/**
 * @description 获取备件仓库列表数据
 */
export function getRepertoryList(params: {} | undefined) {
	return http.get('/task/repertory', params);
}

/**
 * @description 检查附加组件是否必填
 */
export function checkNotNullForCard(params: TaskCheckNotNullForCardModel): Promise<getTaskCheckNotNullForCardResult> {
	return http.post('/task/checkNotNullForCard', params, false);
}

/**
 * @description 打印工单
 */
export function printTask(params: {} | undefined) {
	return http.get('/task/getPrintToken', params);
}

/**
 * @description 审批详情
 */
export function getApprove(params: {} | undefined) {
	return http.get('/approve/get', params);
}

/**
 * @description 审批
 */
export function saveApprove(params: {} | undefined,query: '') {
	return http.post(`/approve/saveResult${query||''}`, params, false);
}

/**
 * @description 撤回审批
 */
export function offApprove(params: {} | undefined) {
	return http.get('/approve/offApprove', params);
}

/**
 * @description 发起审批
 */
export function applyApprove(params: {} | undefined,query:'') {
	return http.post(`/approve/apply${query||''}`, params);
}

// paas审批流程保存
export function reDeployment(params: {} | undefined) {
  return http.post(`/api/paas/outside/pc/processor/reDeployment`, params)
}

/**
 * 更新paas 流程
 * @param params 
 */
export function updateProcess(params: {} | undefined) {
	return http.post(`/api/paas/outside/pc/processor/version/edit`, params);
}
  

// 获取办理审批paas流程数据
export function getPaasAttribute(params: {} | undefined) {
  return http.get(`/api/paas/outside/pc/processor/getAttribute`, params)
}

// 获取工单详情审批按钮展示 审批id
export function getApproveLoad(params: {} | undefined) {
  return http.post(`/api/app/outside/taskVipApprove/getApproveLoad`, params)
}



// 办理高级审批提交接口
export function saveVipApprove(params: {} | undefined, query: string) {
  return http.post(`/api/app/outside/taskVipApprove/submit${query}`, params)
}


// 获取服务报告外显名称字段 templateId
export function getTaskServiceReportNameField(params: {} | undefined) {
  return http.get(`/api/task/outside/task/template/getTaskServiceReportNameField`, params)
}

// 获取服务报告结算导出字段 taskTypeId
export function getExtendField(params: {} | undefined) {
  return http.post(`/api/task/outside/task/template/except/getExtendField`, params)
}


// 高级审批初始化流程配置接口
export function createVipApproveFlow(params: {} | undefined) {
  return http.post(`/api/app/outside/taskVipApprove/createVipApproveFlow`, params)
}

// 发起高级审批保存接口
export function createVipApprove(params: {} | undefined, query: string) {
  return http.post(`/api/app/outside/taskVipApprove/create${query}`, params)
}

/**
 * @description 查询是否需要高级审批接口
 * @param {Object} params -- 参数对象
 * @param {String} params.taskId -- 工单id
 * @param {String} params.action -- 工单节点
 */

export function checkNeedVipApprove(params: {} | undefined) {
  return http.post(`/api/app/outside/taskVipApprove/checkNeedVipApprove`, params)
}

/**
 * @description 新建工单时校验是否需要审批
 */
export function createApproveCheck(params: {} | undefined) {
	return http.post('/api/task/outside/pc/task/approve/checkCreateNeedApprove', params, true);
}

/**
 * @description 开始工单时校验是否需要审批
 */
export function startApproveCheck(id: String, params: {} | undefined) {
	return http.post(`/task/approve/start?id=${id}`, params);
}

/**
 * @description 取消工单时校验是否需要审批
 */
export function offApproveCheck(params: {} | undefined) {
	return http.post('/task/approve/off', params, true);
}

/**
 * @description 暂停工单时校验是否需要审批
 */
export function pauseApproveCheck(params: {} | undefined) {
	return http.get('/task/approve/pause', params);
}

/**
 * @description 审核结算时校验是否需要审批
 */
export function balanceApproveCheck(params: {} | undefined) {
	return http.post('/balance/approve/confirm', params, false, { headers: { indices: true } });
}

/**
 * @description 回访时校验是否需要审批
 */
export function reviewApproveCheck(params: {} | undefined) {
	return http.post('/task/approve/degreeForView', params);
}

/**
 * @description 完成时校验是否需要审批
 */
export function finishApproveCheck(params: {} | undefined) {
	return http.post('/task/approve/finish', params);
}

/**
 * @description 关闭工单时校验是否需要审批
 */
export function closeApproveCheck(params: { id: string }): Promise<getCloseTaskResult> {
	return http.post('/task/closeTask', params);
}

/**
 * @description 获取工单最近更新记录
 * @param {Object} params - 参数
 * @param {String} params.taskId - 工单id
 * @return {MsgModal<String>} 最近更新记录
 */
export function getTaskUpdateRecord(params: { taskId: string }): Promise<getLatestRecordResult> {
	return http.get('/task/getLatestRecord', params, false)
	// return http.get('/api/task/outside/pc/task/getLatestRecord', params, false)
}

/**
 * @description 获取产品列表
 * @param {Object} params - 参数
 */
export function getTaskCustonerProductList(params: {} | undefined) {
	 // 所有涉及产品的es搜索 关键词超过三十个字自动截取前三十个字
	if((params as any)?.keyword){
		(params as any).keyword = getSystemNormalSearchInputForLength((params as any).keyword)
	}
  disposeProductSearchModelDate(params)
	return http.post('/api/customer/outside/pc/product/list', params);
}

/**
 * @description 获取产品列表
 * @param {Object} params - 参数
 * @param {String} params.module - 模块名字
 * @param {String} params.id - id
 */
export function getCountForCreate(params: {} | undefined) {
	
	return http.get('/task/getCountForCreate', params, false);
}

/**
 * @description 根据手机号搜索客户
 * @param {Object} params - 参数
 * @param {String} params.phone - 手机号
 */
export function getCustomerByPhone(params: {} | undefined) {
	return http.get('/task/getCustomerByPhone', params, false);
}

/**
 * @description 获取工单配置
 */
export function getTaskConfig(): Promise<getTaskConfigResult> {
	return http.get('/task/getTaskConfig', {}, false);
}

/**
 * @description 获取默认派单设置
 */
export function getDefaultExecutor(params: {}): Promise<getTaskConfigResult> {
	return http.get(`${fixedPrefixTaskPath}/outside/pc/task/defaultExecutor`, params, false);
}

/**
 * @description 创建工单备注
 * @param {Object} params - 参数
 * @param {String} params.taskId - 工单Id
 * @param {String} params.taskNo - 工单编号
 * @param {Object} params.content - 内容信息
 * @param {Number} params.showInOwn - 是否仅自己可见
 * @param {Number} params.toCustomer - 客户可见
 * @param {Number} params.cusNotice - 向客户发送短信
 */
// export function taskRecordCreate(params: any) {
//   return http.post("/task/taskRecord/create", params, false);
// }

/* -------------  end  旧工单api ---------------- */

/* ------------- start 新工单api ---------------- */

/**
 * 获取工单类型
 */
export function getTaskTypes() {
	return http.get(`${fixedPrefixTaskPath}/outside/dd/task/types`);
}

/**
 * 获取工单列表
 * @param {Object} params - 参数对象
 * @param {String} params.tenantId - 租户id
 * @param {String} params.customerId - 客户id
 * @param {String} params.keyword - 关键字
 * @param {String} params.productId - 产品id
 * @param {String} params.typeId - 工单类型id
 */
export function taskList(params: {} | undefined) {
	return http.post('/task/findList', params);
}

/**
 * 开始工单
 * @param {Object} params - 参数对象
 * @param {String} params.planTime - 计划时间时间戳
 * @param {String} params.taskId - 工单id
 * @param {String} params.tick - 是否勾选 1勾选 0不勾选
 */
export function startTask(params: {} | undefined) {
	return http.post(`${fixedPrefixTaskPath}/outside/pc/task/start`, params);
}

/**
 * 接受工单
 * @param {Object} params - 参数对象
 * @param {String} params.newPlanTime - 计划时间
 * @param {String} params.planStartTime - 计划开始时间
 * @param {String} params.planEndTime - 计划结束时间
 * @param {String} params.taskId - 工单id
 * @param {String} params.tick - 是否勾选 1勾选 0不勾选
 */
export function accept(params: {} | undefined) {
	return http.post(`${fixedPrefixTaskPath}/outside/pc/task/accept`, params);
}

/**
 * 从工单池接单
 * @param {Object} params - 参数对象
 * @param {String} params.newPlanTime - 计划时间
 * @param {String} params.planStartTime - 计划开始时间
 * @param {String} params.planEndTime - 计划结束时间
 * @param {String} params.taskId - 工单id
 * @param {String} params.tick - 是否勾选 1勾选 0不勾选
 */
export function acceptFromPool(params: {} | undefined) {
	objectDateToTimestamp(params, [
		'newPlanTime',
		'planStartTime',
		'planEndTime',
	])
	return http.post(
		`${fixedPrefixTaskPath}/outside/pc/task/acceptFromPool`,
		params
	);
}

/**
 * 修改计划时间
 * @param {Object} params - 参数对象
 * @param {String} params.planTime - 计划时间
 * @param {String} params.taskId - 工单id
 * @param {String} params.sendSMS - 是否发送短信
 */
export function modifyPlanTime(params: {} | undefined) {
	return http.post(
		`${fixedPrefixTaskPath}/outside/pc/task/updatePlanTime`,
		params
	);
}

/**
 * 拒绝工单
 * @param {Object} params - 参数对象
 * @param {String} params.reason - 拒绝原因
 * @param {String} params.taskId - 工单id
 */
export function refuseTask(params: {} | undefined) {
	return http.post(`${fixedPrefixTaskPath}/outside/pc/task/refuse`, params);
}

/**
 * 取消工单
 * @param {Object} params - 参数对象
 * @param {String} params.reason - 取消原因
 * @param {String} params.taskId - 工单id
 * @param {String} params.isGoBack - 是否回退备件 1是0否
 */
export function cancelTask(params: {} | undefined) {
	return http.post(`${fixedPrefixTaskPath}/outside/pc/task/off`, params);
}
/**
 * 复制并取消工单
 * @param {String} params.reason - 取消原因
 * @param {String} params.taskId - 工单id
 */
export function cancelTaskV2(params: {} | undefined) {
	return http.post(`${fixedPrefixTaskPath}/outside/pc/task/offV2`, params);
}

/**
 * 判断曾回退、且当前状态未完成的工单，在最后一次完成时是否使用了备件
 * @param {Object} params - 参数对象
 * @param {String} params.taskId - 工单id
 */
export function finishedWithPart(params: {} | undefined) {
	return http.get(
		`${fixedPrefixTaskPath}/outside/pc/task/finishedWithPart`,
		params
	);
}

/**
 * 暂停工单
 * @param {Object} params - 参数对象
 * @param {String} params.reason - 暂停原因
 * @param {String} params.taskId - 工单id
 */
export function pauseTask(params: {} | undefined,query:'') {
	return http.post(`${fixedPrefixTaskPath}/outside/pc/task/pause${query||''}`, params);
}

/**
 * 继续
 * @param {Object} params - 参数对象
 * @param {String} params.taskId - 工单id
 */
export function unpauseTask(params: {} | undefined) {
	return http.post(`${fixedPrefixTaskPath}/outside/pc/task/unpause`, params);
}

/**
 * 回退工单
 * @param {Object} params - 参数对象
 * @param {String} params.taskId - 工单id
 * @param {String} params.reason - 回退原因
 */
export function rollBackTask(params: {} | undefined,query:'') {
	return http.post(`${fixedPrefixTaskPath}/outside/pc/task/rollBack${query||''}`, params);
}

/**
 * 删除工单
 * @param {Object} params - 参数对象
 * @param {Array} params.taskIds - 需要删除的工单id数组
 */
export function deleteTask(params: {} | undefined) {
	return http.post(`${fixedPrefixTaskPath}/outside/pc/task/delete`, params);
}

/**
 * 结算工单
 * @param {Object} params - 参数对象
 * @param {Array} params.balanceAttachments - 附件
 * @param {Object} params.balanceAttributes - 自定义结算字段值集合
 * @param {String} params.taskId - 工单id
 */
export function balanceTask(params: {} | undefined) {
	return http.post(`${fixedPrefixTaskPath}/outside/pc/task/balance`, params);
}

/**
 * 编辑结算
 * @param {Object} params - 参数对象
 * @param {Array} params.balanceAttachments - 附件
 * @param {Object} params.balanceAttributes - 自定义结算字段值集合
 * @param {String} params.taskId - 工单id
 */
export function editBalance(params: {} | undefined) {
	return http.post(
		`${fixedPrefixTaskPath}/outside/pc/task/editBalance`,
		params
	);
}

/**
 * 回退结算
 * @param {Object} params - 参数对象
 * @param {String} params.reason - 回退原因
 * @param {String} params.taskId - 工单id
 */
export function rollBackBalance(params: {} | undefined,query:'') {
	return http.post(
		`${fixedPrefixTaskPath}/outside/pc/task/rollBackBalance${query||''}`,
		params
	);
}

/**
 * 新版基于方案Uid查询问卷问题
 * @param {Object} params - 参数对象
 * @param {String} params.taskId - 工单id
 */
 export function getQuestionsBySatisfactionUId(params: {} | undefined) {
	return http.get(`${fixedPrefixTaskPath}/outside/pc/task/getQuestionsBySatisfactionUId`, params);
}
/**
 * 暂存回访工单
 * @param {Object} params - 参数对象
 * @param {String} params.taskId - 工单id
 * @param {String} params.suggestion - 回访备注
 * @param {Object} params.evaluate - 自定义回访信息
 * @param {String} params.degree - 满意度
 * @param {Boolean} params.autoClosed - 回访并关闭true，光回访传false
 */
export function temporaryStorageReview(params: {} | undefined,query:'') {
	return http.post(`${fixedPrefixTaskPath}/outside/pc/task/temporaryStorageReview${query||''}`, params);
}
/**
 * 读取暂存回访工单
 * @param {Object} params - 参数对象
 * @param {String} params.taskId - 工单id
 */
export function getReviewTempInfoByTaskId(params: {} | undefined) {
	return http.get(`${fixedPrefixTaskPath}/outside/pc/task/getReviewTempInfoByTaskId`, params);
}
/**
 * 查询暂存客户评价动态信息
 * @param {Object} params - 参数对象
 * @param {String} params.taskId - 工单id
 * @param {String} params.action - 日志类型
 */
export function queryCustomerReviewInfo(params: {} | undefined) {
	return http.post(`${fixedPrefixTaskPath}/outside/pc/task/queryCustomerReviewInfo`, params);
}
/**
 * 回访工单
 * @param {Object} params - 参数对象
 * @param {String} params.taskId - 工单id
 * @param {String} params.suggestion - 回访备注
 * @param {Object} params.evaluate - 自定义回访信息
 * @param {String} params.degree - 满意度
 * @param {Boolean} params.autoClosed - 回访并关闭true，光回访传false
 */
export function reviewTask(params: {} | undefined,query:'') {
	return http.post(`${fixedPrefixTaskPath}/outside/pc/task/review${query||''}`, params);
}
/**
 * 查看工单是否修改方案
 */
export function judgeSatisfactionUidIsChange(params: Object = {}) {
	return http.get(`${fixedPrefixTaskPath}/outside/pc/task/judgeSatisfactionUidIsChange`, params);
}

/**
 * 回访工单-博立
 * @param {Object} params - 参数对象
 * @param {String} params.taskId - 工单id
 * @param {String} params.suggestion - 回访备注
 * @param {Object} params.evaluate - 自定义回访信息
 * @param {Boolean} params.autoClosed - 回访并关闭true，光回访传false
 */
export function reviewTaskBoli(params: {} | undefined) {
	return http.post(`${fixedPrefixTaskPath}/outside/pc/task/boLi/review`, params);
}

/**
 * @description 查询附加组件列表
 * @param {Object} params - 参数
 * @param {String} params.taskId - 工单Id
 */
 export function getTaskCardInfoByTaskId(params: any) {
	return http.get(
		`${fixedPrefixTaskPath}/outside/task/cardInfo/getTaskCardInfoByTaskId`,
		params,
		false
	);
 }
 /**
 * @description 查询附加组件列表带排序
 * @param {Object} params - 参数
 * @param {String} params.taskId - 工单Id
 * @param {String} params.cardId - 附加组件Id
 */
export function getTaskCardInfoByOrderTaskId(params: {} | undefined) {
	return http.post(`${fixedPrefixTaskPath}/outside/task/cardInfo/getSortingAdditionalComponents`, params);
}

/**
 * @description 保存附加组件
 * @param {Object} params 参数对象
 * @param {String} params.cardId 附加组件id
 * @param {String} params.inputType 附加组件填写类型
 * @param {Object} params.attribute 附加组件自定义字段
 * @param {String} params.taskId 工单id
 */
export function taskCreateCard(params: {} | undefined) {
	return http.post(`${fixedPrefixTaskPath}/outside/pc/task/card/save`, params);
}

/**
 * @description 编辑附加组件
 * @param {Object} params 参数对象
 * @param {String} params.cardId 附加组件id
 * @param {String} params.inputType 附加组件填写类型
 * @param {Object} params.attribute 附加组件自定义字段
 * @param {String} params.taskId 工单id
 */
export function taskEditCard(params: {} | undefined) {
	return http.post(`${fixedPrefixTaskPath}/outside/pc/task/card/edit`, params);
}

/**
 * @description 删除附加组件
 * @param {Object} params 参数对象
 * @param {String} params.cardId 附加组件id
 * @param {String} params.infoId 附加组件实例id
 * @param {String} params.taskId 工单id
 */
export function taskDeleteCard(params: {} | undefined) {
	return http.post(
		`${fixedPrefixTaskPath}/outside/pc/task/card/delete`,
		params
	);
}

/**
 * @description 保存多次附加组件
 * @param {Object} params 参数对象
 * @param {String} params.cardId 附加组件id
 * @param {String} params.inputType 附加组件填写类型
 * @param {Object} params.attribute 附加组件自定义字段
 * @param {String} params.taskId 工单id
 */
export function taskBatchSaveOrEdit(params: any | undefined) {
  return http.post(`${fixedPrefixTaskPath}/outside/pc/task/card/batchSaveOrEdit`, params);
}

/**
 * @description 工时记录列表
 * @param {Object} params 参数对象
 * @param {String} params.cardId 附加组件id
 * @param {String} params.orderId 工单id
 */
export function getHoursRecordList(params: {} | undefined) {
	return http.post(
		`${fixedPrefixTaskPath}/outside/tasktime/getMainList`,
		params
	);
}

/**
 * @description 编辑工时记录
 * @param {String} params.id 工时记录附加组件实例id
 */
export function updateHoursRecord(params: {} | undefined) {
	return http.post(
		`${fixedPrefixTaskPath}/outside/tasktime/updateWorkTimeRecord`,
		params
	);
}

/**
 * @description 新增工时记录
 * @param {String} params.cardId 工时记录id
 * @param {String} params.orderId 工单id
 * @param {String} params.recordFromType 默认1
 */
export function createHoursRecord(params: {} | undefined) {
	return http.post(
		`${fixedPrefixTaskPath}/outside/tasktime/createWorkTimeRecordForweb`,
		params
	);
}

/**
 * @description 删除工时记录
 * @param {String} params.mainId 工时记录附加组件实例id
 */
export function deleteHoursRecord(params: {} | undefined) {
	return http.post(
		`${fixedPrefixTaskPath}/outside/tasktime/deleteWorkTimeRecord`,
		params,
		false
	);
}

/**
 * @description 工时记录位置详情
 * @param {String} params.mainId 工时记录附加组件实例id
 */
export function hoursRecordLocation(params: {} | undefined) {
	return http.post(
		`${fixedPrefixTaskPath}/outside/tasktime/getDetailList`,
		params,
		false
	);
}

/**
 * @description 物料质保状态查询
 * @param {Array} params.materialIds 物料id
 * @param {String} params.taskId 工单id
 */
 export function getMaterialsWarranty(params: {} | undefined) {
	return http.post(`${fixedPrefixTaskPath}/outside/pc/warehouse/materials/warranty`, params);
}

/**
 * @description 完成回执
 */
export function finishTask(params: {} | undefined) {
	return http.post(`${fixedPrefixTaskPath}/outside/pc/task/finish`, params);
}

/**
 * @description 编辑回执
 */
export function editReceipt(params: {} | undefined) {
	return http.post(
		`${fixedPrefixTaskPath}/outside/pc/task/editReceipt`,
		params
	);
}

/**
 * @description 暂存回执
 */
export function receiptDraft(params: {} | undefined) {
	return http.post(`${fixedPrefixTaskPath}/outside/pc/task/receiptDraft/save`, params);
}

/**
 * @description 修改协同人
 */
export function updateSynergies(params: {} | undefined) {
	return http.post(`${fixedPrefixTaskPath}/outside/pc/task/updateSynergies`, params);
}

/**
 * @description 获取工单对应的订单的支付状态
 * @param {Object} params 参数
 * @param {String} params.taskId 工单id
 */
export function getPaymentOrder(params: {} | undefined) {
	return http.get(
		`${fixedPrefixPaymentPath}/outside/payment/order/query`,
		params
	);
}

/**
 * @description 获取工单对应的订单的支付状态
 * @param {Object} params 参数
 * @param {String} params.taskId 工单id
 */
export function getPaymentDetail(params: {} | undefined) {
	return http.get(
		`${fixedPrefixPaymentPath}/outside/paymentBill/online/getByTaskId`,
		params
	);
}

/**
 * 保存工单设置信息
 * @param {Object} params - 参数对象
 * @param {String} params.fields - 设置form对象
 */
export function taskSettingSave(params: {} | undefined, query:'') {
	return http.post(`/setting/taskType/field/save${query||''}`, params);
}

/**
 * 保存工单结算规则配置
 */
export function saveSettlementRule(params: {} | undefined) {
	return http.post('/api/app/outside/taskType/settlementRule/saveSettlementRule', params)
}

/**
 * 获取结算规则
 */
export function getSettlementRuleModule(params: {} | undefined) {
	return http.post(`/api/app/outside/taskType/settlementRule/getSettlementRuleModule`, params)
}
/**
 * 将私有字段升级为公共字段/将公共字段降级为私有字段
 * @param {Object} params - 参数对象
 * @param {String} params.templateId - 工单类型id
 * @param {Array} params.commonFieldFormList - 需升降的公共字段数组
 */
export function setCommonFields(params: {} | undefined) {
	return http.post(`${fixedPrefixTaskPath}/outside/pc/task/setCommonFields`, params);
}

/**
 * 更新公共字段设置
 * @param {Object} params - 字段对象
 */
export function updateCommonField(params: {} | undefined) {
	return http.post(`${fixedPrefixTaskPath}/outside/pc/task/updateCommonField`, params);
}

/**
 * 校验人员是否是审批人
 * @param {Object} params - 参数对象
 * @param {String} params.id - 人员id
 */
export function checkUser(params: {} | undefined) {
	return http.post('/setting/fieldInfo/check', params, false);
}

/**
 * 取消人员在流程中的审批人身份
 * @param {Object} params - 参数对象
 * @param {String} params.id - 人员id
 */
export function cancelUserApproval(params: {} | undefined) {
	return http.post('/setting/fieldInfo/confirm', params, false);
}

/**
 * 工单设置，删除组件
 * @param {Object} params - 参数对象
 * @param {String} params.id - 人员id
 */
export function deleteComponent(params: {} | undefined) {
	return http.post('/setting/fieldInfo/delete2', params, false);
}

/**
 * 工单设置，删除组件
 * @param {Object} params - 参数对象
 * @param {String} params.id - 字段id
 */
export function deleteField(params: {} | undefined) {
	return http.post('/setting/fieldInfo/delete3', params, false);
}

/**
 * 获取工单设置的除组件外的其他信息
 * @param {Object} params - 参数对象
 * @param {String} params.id - 工单id
 */
export function getTaskType(params: { id: string } | undefined): Promise<getTaskTypeResult> {
	return http.get('/setting/taskType/getOne', params);
}

/**
 * 工单设置，回执其他设置模块的配置修改
 * @param {Object} params - 参数对象
 * @param {String} params.id - 配置id
 * @param {String} params.name - 配置名
 * @param {String} params.state - 配置状态
 */
export function modifyOption(params: {} | undefined) {
	return http.post('/setting/taskType/saveOption', params, false);
}

/**
 * 工单设置，回执合规设置模块的配置修改
 * @param {Object} params - 参数对象
 * @param {String} params.id - 配置id
 * @param {String} params.name - 配置名
 * @param {String} params.state - 配置状态
 */
export function modifyConfig(params: {} | undefined) {
	return http.post('/setting/taskType/saveConfig', params, false);
}

/**
 * 工单设置，回执合规设置模块的配置修改
 * @param {Object} params - 参数对象
 * @param {String} params.typeId - 配置id
 */
export function getTaskCardDetailList(params: {} | undefined) {
	return http.get('/setting/getTaskCardDetailList', params);
}

/**
 * 顶部筛选, 状态数据展示
 */
export function getTaskCountByState(params: {} | undefined): Promise<getTaskCountByStateResult> {
	objectDateToTimestamp(params, [ 'timeStart', 'timeEnd' ])
	return http.post(`${ElASTICSEARCH}/outside/es/task/getTaskCountByState`, params)
}

/**
 * 存为视图
 * @param {Object} params -对象参数
 * @param {String} params.viewName -视图名称
 * @param {String} params.viewRegion -是否全员可见
 * @param {String} params.editViewId -视图id
 * @param {Array} params.selectedCols -选择列选中的参数
 * @param {String} params.tsmStr
 */
export function createView(params: {} | undefined) {
	return http.post(
		`${fixedPrefixTaskPath}/outside/pc/view/createTaskView`,
		params
	);
}

/**
 * 编辑视图
 * @param {Object} params -对象参数
 * @param {String} params.viewName -视图名称
 * @param {String} params.viewRegion -是否全员可见
 * @param {String} params.editViewId -视图id
 * @param {Array} params.selectedCols -选择列选中的参数
 * @param {String} params.tsmStr
 */
export function editView(params: {} | undefined) {
	return http.post(
		`${fixedPrefixTaskPath}/outside/pc/view/editTaskView`,
		params
	);
}

/**
 * 删除视图
 * @param {Object} params -对象参数
 * @param {String} params.editViewId -视图id
 */
export function deleteView(params: {} | undefined) {
	return http.get(
		`${fixedPrefixTaskPath}/outside/pc/view/deleteOneView/${params}`
	);
}

/**
 * 工单设置，回执合规设置模块 下载此工单类型的标识对应表
 * @param {Object} params - 参数对象
 * @param {String} params.typeId - 配置id
 */
export function getTaskTemplate(params: {} | undefined) {
	return http.get('/setting/taskType/getTemplateDic', params);
}

/**
 * 获取工单列表
 * @param {Object} params - 参数对象
 * @param {String} params.allotUser - 派单人userId
 * @param {Object} params.conditions - 自定义字段的搜索条件
 * @param {String} params.createUser - 创建人userId
 * @param {String} params.cusAddress - 详细地址搜索
 * @param {String} params.cusCity - 市搜索
 * @param {String} params.cusDist - 区搜索
 * @param {String} params.cusProvince - 省搜索
 * @param {String} params.cusTagIds - 团队ID，用于查询符合团队的客户
 * @param {String} params.customerId - 客户id
 * @param {String} params.customerLinkman - 客户联系人
 * @param {number} params.dataLevel - 数据权限
 * @param {String} params.executor - 负责人userId
 * @param {String} params.executorIdList - 负责人userIdList，配合数据权限使用
 * @param {String} params.ids - 工单idList
 * @param {boolean} params.ignoreTaskPoolAuth - 忽略工单池权限
 * @param {String} params.keyword - 关键字搜索
 * @param {boolean} params.mobileState - 是否是移动端的工单状态，search方法特殊处理
 * @param {String} params.myId - 搜索人id，配合数据权限使用
 * @param {boolean} params.noGeo - 获取是否有定位的工单
 * @param {boolean} params.orderByDistance - 是否按距离排序
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页显示数量
 * @param {String} params.productId - 产品id
 * @param {String} params.serviceContent - 服务内容
 * @param {String} params.serviceType - 服务类型
 * @param {String} params.state - 工单状态
 * @param {String} params.stateList - 工单状态list
 * @param {String} params.synergyId - 协同人userId
 * @param {String} params.tagId - 工单按团队搜索
 * @param {String} params.templateId - 工单类型Id
 * @param {String} params.tenantId	 - 租户Id
 * @param {integer} params.onceException	 - 异常标记：曾超时暂停拒绝整合 1超时 2拒绝 3暂停 4回退 5位置异常
 * @param {integer} params.onceOverTime	 - 曾超时
 * @param {integer} params.onceRefused	 - 曾拒绝
 * @param {integer} params.oncePaused	 - 曾暂停
 * @param {integer} params.onceReallot	 - 曾转派
 * @param {integer} params.oncePrinted	 - 曾打印
 * @param {integer} params.allotType	 - 派单方式
 * @param {String} params.updateTimeStart	 - 更新时间start
 * @param {String} params.updateTimeEnd	 - 更新时间end
 * @param {integer} params.inApprove	 - 是否审批中
 * @param {integer} params.isException	 - 是否异常
 * @param {String} params.oneTagExecutorIdList	 - 服务团队负责人userIdList，配合tagId一起使用
 * @param {String} params.tagIds	 - 多个团队
 * @param {String} params.tagExecutorIdList	 - 团队负责人userIdList，配合tagIds一起使用
 * @param {String} params.customerUserIdList	 - 我所有客户
 * @param {String} params.customerUserId	 - 我客户的：工单的客户的创建人和负责人id
 * @param {String} params.timeStart	 - 开始时间
 * @param {String} params.timeEnd	 - 结束时间
 * @param {String} params.createTimeStart	 - 创建时间start
 * @param {String} params.createTimeEnd	 - 创建时间end
 * @param {String} params.planTimeStart	 - 计划时间start
 * @param {String} params.planTimeEnd	 - 计划时间end
 * @param {String} params.allotTimeStart	 - 派单时间start
 * @param {String} params.allotTimeEnd	 - 派单时间end
 * @param {String} params.completeTimeStart	 - 完成时间start
 * @param {String} params.completeTimeEnd	 - 完成时间end
 * @param {String} params.reviewTimeStart	 - 回访时间start
 * @param {String} params.reviewTimeEnd	 - 回访时间end
 * @param {String} params.balanceTimeStart	 - 结算时间start
 * @param {String} params.balanceTimeEnd	 - 结算时间end
 * @param {String} params.closeTimeStart	 - 关闭时间start
 * @param {String} params.closeTimeEnd	 - 关闭时间end
 * @param {String} params.acceptTimeStart	 - 接收时间start
 * @param {String} params.acceptTimeEnd	 - 接收时间end
 * @param {String} params.startTimeStart	 - 开始时间start
 * @param {String} params.startTimeEnd	 - 开始时间end
 * @param {String} params.level	 - 优先级
 * @param {String} params.overTime	 - 超时时间
 * @param {String} params.inTaskPool	 - 工单池标记
 * @param {String} params.isReview	 - 已回访
 * @param {integer} params.exceptionType	 - 异常类型 0全部 1暂停 2超时
 * @param {String} params.customerIdList	 - 客户IdList，配合数据权限使用
 * @param {String} params.sorts	 - 排序，默认按照创建时间倒序
 * @param {String} params.taskTypeIdList	 - 工单类型IdList，用来工单池顶部统计查询时筛选工单类型
 * @param {integer} params.onceRollback	 - 是否回退过 1回退过 2未回退过
 * @param {boolean} params.validAddress	 - 地址是否有效
 * @param {integer} params.startDistance	 - 起始距离
 * @param {String} params.endDistance	 - 终止距离
 * @param {number} params.myLongitude	 - 当前位置经度
 * @param {number} params.myLatitude	 - 当前位置纬度
 * @param {string} params.taskIds	 - 已经筛选过的工单ID
 * @param {integer} params.isSettled	 - 已结算
 * @param {string} params.reviewType	 - 回访列表选 1人工回访 2客户评价
 * @param {string} params.degree	 - 满意度: 不满意, 满意, 一般
 * @param {string} params.searchCondition	 - 关键词搜索类型
 * @param {string} params.view	 - 视图
 */
export function search(params: TaskSearchListModel): Promise<getTaskSearchListResult> {
	if(params){
		// @ts-ignore
		params.timeStart = getTimestamp(params.timeStart)
		// @ts-ignore
		params.timeEnd = getTimestamp(params.timeEnd)
	}
	return http.post(`${ElASTICSEARCH}/outside/es/task/search`, params)
}

/*
 * 工单设置，回执合规设置模块 保存自定义打印模板
 * @param {Object} params - 参数对象
 * @param {String} params.typeId - 配置id
 */
export function savePrintTemplate(params: {} | undefined) {
	return http.post('/setting/taskType/savePrintTemplates', params, false, { headers: { indices: true } });
}

/**
 * 工单设置，回执合规设置模块 保存自定义报告模板
 * @param {Object} params - 参数对象
 * @param {String} params.typeId - 配置id
 */
export function saveReportTemplate(params: {} | undefined) {
	return http.post('/setting/taskType/saveReportTemplates', params, false, { headers: { indices: true } });
}

/**
 * 工单设置，回执合规设置模块 保存系统 报告模板勾选项
 * @param {Object} params - 参数对象
 * @param {String} params.id - 配置id
 * @param {String} params.reportSetting - 勾选整个对象包裹
 */
export function saveSystemReport(params: {} | undefined) {
	return http.post('/setting/taskType/saveReport', params);
}

/**
 * 工单设置，回执合规设置模块 保存系统 打印模板勾选项
 * @param {Object} params - 参数对象
 * @param {String} params.id - 配置id
 * @param {String} params.printSetting - 勾选整个对象包裹
 */
export function saveSystemPrint(params: {} | undefined) {
	return http.post('/setting/taskType/savePrint', params);
}

/**
 * 修改工单类型颜色和名称
 *
 * @param {string} params.name 工单类型名称
 * @param {string} parmas.color 工单类型颜色
 */
export function updateTaskTypeNameAndColor(params: {} | undefined) {
	return http.post(`${fixedPrefixTaskPath}/outside/pc/task/taskType/updateTaskType`, params);
}

/**
 * @description 暂存工单
 */
export function temporarySaveTask(params: TaskCreateAndEditModel) {
	return http.post(`${fixedPrefixTaskPath}/outside/pc/task/createHalf`, params);
}

/**
 * @description 新建工单
 */
export function createTask(params: TaskCreateAndEditModel) {
	return http.post(`${fixedPrefixTaskPath}/outside/pc/task/create`, params);
}

/**
 * @description 新建工单-呼叫中心
 */
export function createTask4CallCenter(params: TaskCreateAndEditModel) {
  return http.post(`/task/create4CallCenter`, params);
}

/**
 * @description 编辑工单
 */
export function editTask(params: TaskCreateAndEditModel) {
	return http.post(`${fixedPrefixTaskPath}/outside/pc/task/edit`, params);
}

/**
 * @description 查询关联显示项数据
 * @param {Object} params 参数对象
 * @param {String} params.customerId 客户id
 * @param {String} params.productId 产品id
 */
export function getRelatedInfo(params: {} | undefined) {
	return http.get(
		`${fixedPrefixTaskPath}/outside/pc/task/getRelatedInfo`,
		params
	);
}

/**
 * @description 查询关联显示项数据
 * @param {Object} params 参数对象
 * @param {String} params.customerId 客户id
 * @param {Array} params.productIds 产品id数组
 */
export function getRelatedInfos(params: {} | undefined) {
	return http.post(
		`${fixedPrefixTaskPath}/outside/pc/task/getRelatedInfos`,
		params
	);
}

/**
 * @description 筛选已经关联过备件的工单
 * @param {String} taskIds 查询的工单ids example: taskIds=1&taskIds=2
 */
// export function withPart(taskIds: string): Promise<getTaskFilterWithPartResult> {
// 	return http.get(`/task/filter/withPart?${taskIds}`)
// }
export function withPart(params: Array<string>): Promise<getTaskFilterWithPartResult> {
	return http.post(`/task/filter/bindPart`, params)
}

/**
 * @description 批量更新
 */
export function editBatchTask(params: Object = {}) {
	return http.post(
		`${fixedPrefixTaskPath}/outside/pc/task/editBatchTask`,
		params
	);
}

/**
 * @description 工单转派
 */
export function redeployBatch(params: Object = {}) {
	return http.post('/task/redeployBatch', params);
}

/**
 * @description 新建计划任务
 */
export function createPlanTask(params: PlanTaskCreateAndEditModel) {
	return http.post(
		`${fixedPrefixTaskPath}/outside/pc/task/planTask/create`,
		params
	);
}

/**
 * @description 编辑计划任务
 */
export function editPlanTask(params: PlanTaskCreateAndEditModel) {
	return http.post(
		`${fixedPrefixTaskPath}/outside/pc/task/planTask/update`,
		params
	);
}

/**
 * @description 插入工单日志
 * @param {Object} params - 参数
 * @param {String} params.taskId - 工单Id
 * @param {String} params.taskNo - 工单编号
 * @param {String} params.content - 内容信息
 * @param {Number} params.showInOwn - 是否仅自己可见
 * @param {Number} params.toCustomer - 客户可见
 * @param {Number} params.cusNotice - 向客户发送短信
 */
export function taskRecordCreate(url:any,params: any) {
	return http.post(
		`${fixedPrefixTaskPath}/outside/pc/taskrecord/create?`+url,
		params
	);
}

/**
 * @description 查询工单日志列表
 * @param {Object} params - 参数
 * @param {String} params.taskId - 工单Id
 */
export function taskRecordList(params: any) {
	return http.get(
		`${fixedPrefixTaskPath}/outside/pc/taskrecord/list`,
		params,
		false
	);
}

/**
 * @description 查询归档工单日志列表
 * @param {Object} params - 参数
 * @param {String} params.taskId - 工单Id
 */
 export function taskArchiveRecordList(params: any) {
	return http.get(
		`${fixedPrefixTaskPath}/outside/pc/taskrecord/archiveTaskRecordList`,
		params,
		false
	);
}

/**
 * @description 删除工单日志
 * @param {Object} params - 参数
 * @param {String} params.id -  工单备注Id
 */
export function taskRecordDelete(params: any) {
	return http.post(
		`${fixedPrefixTaskPath}/outside/pc/taskrecord/logicDelete`,
		params,
		false
	);
}

/**
 * @description 拨打电话
 */
export function dialout(params: Record<string, any>) {
	return http.post('/api/callcenter/outside/callcenter/api/dialout', params, false);
}

/**
 * @description 获取视图
 * @return {getUserViewsResult} 视图列表
 */
export function getUserViews(): Promise<getUserViewsResult> {
	return http.get(`${fixedPrefixTaskPath}/outside/pc/view/getUserViews`)
}

/**
 * @description 根据工单类型id获取其附加组件信息
 */
export function getCardDetailList(params: TaskGetCardDetailListModel): Promise<getCardDetailResult> {
	return http.get('/task/getCardDetailList', params)
}

/**
 * @description 用户是否有编辑权限
 */
export function getTaskAuthUpdate(params: any) {
	return http.get('/task/auth/update', params)
}

/**
 * @description 是否展示日历派单功能
 */
export function getTaskCalendarDispatch(params: any) {
	return http.get('/task/check/calendar/dispatch', params)
}

/**
 * @description 查询一个视图
 */
export function getOneView(params: string) {
	return http.get(`${fixedPrefixTaskPath}/outside/pc/view/getOneView/${params}`)
}

/**
 * @description 查询指派人员列表
 */
export function getUserListByTag(params: TaskAllotUserListByTagModel): Promise<getUserListByTagResult> {
	return http.post('/task/allotMap/userListByTag', params)
}

/**
 * @description 旧版切换原因
 */
export function revertReason(params: object) {
	return http.post('/task/revertReason', params)
}
/**
 * 获取负责人 协同人 派单人 创建人
 */
export function getSimpleUserListByIds(params: object = []) {
	return http.post(`${APPS}/outside/user/getSimpleUserListByIds`, params)
}
/**
 * 获取联系人
 */

export function getLinkmanListByIds(params: object = []) {
	return http.post(`${CUSTOMER}/outside/pc/customer/getLinkmanListByIds`, params)
}

/**
* 获取产品
*/
export function getSimpleProductList(params: object = []) {
	return http.post(`${CUSTOMER}/outside/pc/getSimpleProductList`, params)
}

/**
 * 获取克隆产品类型树接口
 * @returns {*}
*/
export function getProductType(params: object) {
  return http.get(`${CUSTOMER}/outside/pc/catalog/cloneData`);
}

/**
 * 获取当前选中产品的产品类型
 * @param params
 * @returns {*}
 */
 export function getCurrentProductType(params: object) {
  return http.get(`${CUSTOMER}/outside/pc/catalog/path`, params)
}
/**
 * 获取客户人
 */
export function getSimpleCustomerList(params: object = []) {
	return http.post(`${CUSTOMER}/outside/pc/customer/getSimpleCustomerList`, params)
}
/**
* 获取团队服务
*/
export function getSimpleTagListByIds(params: object = []) {
	return http.post(`${APPS}/outside/tag/getSimpleTagListByIds`, params)
}

/** 
 * @description 查询指派工单池人员列表
*/
export function getTaskAllotPoolUserList(params: TaskAllotUserListByTagModel): Promise<getUserListByTagResult> {
	return http.post('/task/allotMap/userListByTag', params)
}

/**
 * @description 查询工单池工单列表
 */
export function getTaskAllotPoolList(params: any): Promise<any> {
	return http.post(`${ElASTICSEARCH}/outside/dd/es/task/taskPool`, params)
}

/**
 * @description 查询指派工单团队人员列表
 */
export function getTaskAllotDispatchTeamUserList(params: TaskTagUserListSearchModel): Promise<any> {
	return http.post('/task/customerTag/dispatch/list', params)
}

/**
 * @description 查询转派工单团队人员列表
 */
export function getTaskAllotRedeployTeamUserList(params: TaskTagUserListSearchModel): Promise<any> {
	return http.post('/task/customerTag/redeploy/list', params)
}

/**
 * @description 自动派单-获取预估列表结果
 */
export function getAutoDispatchResultList(params: TaskAutoDispatchResultListModel): Promise<getAutoDispatchResultListResult> {
	return http.post(`${fixedPrefixTaskPath}/outside/pc/task/autoDispatchResultList`, params)
}

/**
 * @description 自动派单-审批结果
 */
export function getTaskAutoDispatchApprove(params: any): Promise<any> {
	return http.post('/task/approve/autoDispatch', params)
}

/**
 * @description 自动派单-提交开始派单
 */
export function taskAutoDispatch(params: any): Promise<any> {
	return http.post(`${fixedPrefixTaskPath}/outside/pc/task/autoDispatch`, params)
}

/**
 * @description 工单指派-获取人员卡片信息
 */
export function getTaskUserCardInfo(params: TaskUserCardSearchModel): Promise<getTaskUserCardInfoResult> {
	return http.post(`${ElASTICSEARCH}/outside/es/task/getUserCardInfo`, params)
}

/**
 * @description 工单指派-获取人员列表信息
 */
export function getTaskAllotUserInfo(params: TaskAllotUserSearchModel): Promise<getTaskAllotUserInfoResult> {
	return http.post(`${ElASTICSEARCH}/outside/es/task/getAllotUserInfo`, params, true, {
		cancelable: true,
	})
}

/**
 * @description 工单指派-获取服务商列表信息
 */
export function getServiceProviderApi(params = {}) {
  return http.post(`${ElASTICSEARCH}/outside/es/task/serviceProvider/user`, params)
}

/**
 * @description 设置审批--回执金额规则
 */

export function getSettlementRule(params = {}) {
  return http.post('/api/app/outside/taskType/settlementRule/getSettlementRuleDistinctObjName', params)
}


/**
 * @description 工单指派-指派前验证是否审批接口
 */
export function getTaskAllotApprove(params: TaskAllotApproveGetModel): Promise<getTaskAllotApproveResult> {
	objectDateToTimestamp(params, ['planStartTime', 'planEndTime'])
	return http.post('/task/confirmAllotTo', params)
}

/**
 * @description 工单指派-转派前验证是否审批接口
 */
export function getTransferTaskAllotApprove(params: TaskAllotApproveGetModel): Promise<getTaskAllotApproveResult> {
	objectDateToTimestamp(params, ['planStartTime', 'planEndTime'])
	return http.post('/task/checkTransferTask', params)
}

/**
 * @description 工单指派-验证指派到工单池是否需要审批
 */
export function getTaskAllotTaskPoolApprove(params: TaskAllotTaskPoolModel): Promise<getTaskAllotTaskPollApproveResult> {
	return http.post('/task/approve/allotTaskToPool', params)
}

/**
 * @description 工单指派-指派工单-到负责人
 */
export function taskAllotExcutor(params: TaskAllotModel): Promise<getTaskAllotResult> {
	return http.post(`${fixedPrefixTaskPath}/outside/pc/task/allot`, params)
}

/**
 * @description 工单转派
 */
export function taskReAllot(params: TaskAllotModel,query:string): Promise<getTaskAllotResult> {
	return http.post(`${fixedPrefixTaskPath}/outside/pc/task/reallot${query||''}`, params)
}

/**
 * @description 工单转派到工单池
 */
export function taskReAllotTaskPool(params: TaskReAllotTaskPoolModel): Promise<getTaskAllotResult> {
	return http.post(`${fixedPrefixTaskPath}/outside/dd/task/allotToPool`, params)
}

/**
 * @description 工单指派-指派工单池
 */
export function taskAllotTaskPoll(params: TaskAllotTaskPoolModel): Promise<getTaskAllotTaskPoolResult> {
	return http.post(`${fixedPrefixTaskPath}/outside/pc/task/allotToPool`, params)
}

/**
 * @description 工单指派-团队列表
 */
export function getTaskDispatchTagList(params: TaskTagListSearchModel): Promise<getTaskTagListResult> {
	return http.get('/task/tag/dispatch/customerTagList', params)
}

/**
 * @description 工单指派-团队列表
 */
export function getTaskRedeployTagList(params: TaskTagListSearchModel): Promise<getTaskTagListResult> {
	return http.get('/task/tag/redeploy/customerTagList', params)
}

/**
 * @description 工单指派-获取工单池可以接单用户列表
 */
export function getTaskPoolAuthUserList(params: TaskPoolAuthUsersGetModel): Promise<getTaskPoolAuthUsersResult> {
	return http.get('/task/taskPool/user/list', params)
}

/**
 * @description 工单指派-获取订阅工单池用户列表
 */
export function getTaskPoolSubscriptionUserList(): Promise<getTaskPoolSubscriptionUsersResult> {
	return http.get(`${fixedPrefixTaskPath}/outside/dd/task/list/taskPoolSubscriptionUserAndCount`)
}

/**
 * @description 工单指派-根据客户团队统计工单池数量
 */
export function getCustomerTagTaskPoolCount(params: CustomerTahTaskPoolCountGetModel): Promise<getCustomerTagTaskPoolCountResult> {
	return http.post(`${ElASTICSEARCH}/outside/es/task/getCusTagsTaskPoolCount`, params)
}

/**
 * @description 工单PC工单池查询
 */
export function getTaskPoolList(params: TaskPoolSearchModel): Promise<getTaskSearchListResult> {
	return http.post(`${ElASTICSEARCH}/outside/es/task/taskPool`, params)
}

/**
 * @description 获取用户开启的配置节点 以及工单搜索范围 和 异常原因字段值
 */
export function getTurnOnTaskExceptionNodeInfo(params = {}): Promise<getTurnOnTaskExceptionNodeInfoResult> {
	return http.get(`${fixedPrefixTaskPath}/outside/pc/setting/getTurnOnTaskExceptionNodeInfo`, params)
}

/**
 * @description 获取工单异常原因
 */

export function obtainReasonByTaskStatus(params: number) {
	return http.get(`${fixedPrefixTaskPath}/outside/pc/setting/obtainReasonByTaskStatus/${params}`)
}

/**
 * 工单异常饼图
 */
export function chart(params = {}) {
	return http.post(`${REPORT}/outside/task/exception/chart`, params)
}

/**
 * 工单异常饼图列表
 */
export function chartList(params = {}) {
	return http.post(`${REPORT}/outside/task/exception/chart/list`, params)
}

/**
 * 工单异常柱状图
 */
export function histogram(params = {}) {
	return http.post(`${REPORT}/outside/task/exception/histogram`, params)
}

/**
 * 工单异常柱状图列表
 */
export function histogramList(params = {}) {
	return http.post(`${REPORT}/outside/task/exception/histogram/list`, params)
}

/**
 * 工单异常节点导出
 */
export function actionExport(params = {}) {
	return http.post('/excels/task/exception/action/export', params)
}

/**
 * 工单异常原因导出
 */
export function reasonExport(params = {}) {
	return http.post('/excels/task/exception/reason/export', params)
}

/**
 * 异常节点获取全部数量
 */
export function chartTotal(params = {}) {
	return http.post(`${REPORT}/outside/task/exception/chart/all/total`, params)
}

/**
 * 异常原因获取全部数量
 */
export function histogramTotal(params = {}) {
	return http.post(`${REPORT}/outside/task/exception/histogram/all/total`, params)
}

/**
 * @description 查询所有工单类型
 */
export function getTaskTypesMap(): Promise<getTaskTypesResult> {
	return http.get(
		`${fixedPrefixTaskPath}/outside/task/list/taskTypeMap`, 
		{},
		true, 
		{ 
			notEscape: true,
			useSessionCache: true
		}
	)
}

/**
 * 批量关闭工单
 * @param {Object} params - 参数
 * @param {Array} params.ids - 工单id
 */
export function closeBatch(params: { ids: string }) {
	return http.post('/task/closeBatch', params, false);
}

/**
 * 发送回访短信
 * @param {Object} params - 参数
 * @param {Array} params.ids - 工单id
 */
export function sendReviewMessage(params: TaskReviewSendSmsModel): Promise<getTaskReviewSendSmsDataResult> {
	return http.post('/task/sendReviewMessage', params, true);
}

/**
 * 查看回访短信
 */
 export function previewTaskReviewMessage(params = {}) {
	return http.get(`/task/preview/review/message?${new URLSearchParams(params)}`)
}

/**
 * @description 新建工单时判断是否有清单可以添加
 * @param {Object} params 参数对象
 * @param {String} params.templateId 模版id
 */
export function getConfigByTaskTypeId(params: {} | undefined) {
  return http.post(`${fixedPrefixTaskPath}/outside/pc/inventory/getConfigByTaskTypeId`, params, false);
}

/**
 * 备件品类列表
 * @param {*} params = {
 * keyWord  模糊搜索关键字
 * sparepartTypes 备件类型
 * }
 */
export function getSparePartList(params: {} | undefined) {
  return http.post(`${prefixPartPath}/outside/dd/part/sparepart/getSparePartList`, params);
}

/**
 * 根据添加备件品类的范围设置查询备件列表
 * @param {*}
 */
 export function getSparePartByUserTagList(params: {} | undefined) {
	return http.post(`${fixedPrefixTaskPath}/outside/task/cardInfo/getSparePartByUserTagList`, params);
  }

/**
 * 备件配置信息
 */
export function sparepartConfig(params: {} | undefined) {
  return http.post(`${prefixPartPath}/outside/dd/part/sparepart/sparepartConfig`, params);
}

/**
 * 根据附加组件id 查询备件清单和服务清单信息 + 物料申领
 * @param {Object} params 参数对象
 * @param {String} params.cardId 附加组件id
 * @param {String} params.taskId 工单id
 */
export function getInventoryByCardId(params: {} | undefined) {
  return http.post(`${fixedPrefixTaskPath}/outside/pc/inventory/getInventory`, params);
}

/**
 * 修改备件、服务清单的预计用量
 * @param {Object} params 参数对象
 * @param {String} params.id 清单数据Id
 * @param {String} params.preNum 预计用量
 * @param {String} params.type 1 表示备件清单 2 表示服务清单
 */
export function putTaskInventoryValue(params: {} | undefined) {
  return http.post(`${fixedPrefixTaskPath}/outside/pc/inventory/putTaskInventoryValue`, params);
}

/**
 * 删除备件清单内添加的备件+物料申领
 * @param {Object} params 参数对象
 * @param {String} params.taskInventoryId 清单数据Id
 */
export function deleteByInventory(params: {} | undefined) {
  return http.post(`${fixedPrefixTaskPath}/outside/pc/inventory/deleteByInventory`, params);
}

/**
 * 添加备件到 备件、服务清单
 * @param {Object} params 参数对象
 * @param {String} params.baseTaskTypeId 工单类型Id
 * @param {String} params.baseTaskInventories 备件、服务项目数据
 */
export function addTaskInventory(params: {} | undefined) {
  return http.post(`${fixedPrefixTaskPath}/outside/pc/inventory/addTaskInventory`, params);
}

/**
 * 根据备件id，获取所有具有该备件的仓库，以及个人库用量
 * @param {Array} params 备件品类Ids
 */
export function getRepertoryBySparePartIds(params: {} | undefined) {
  return http.post(`${prefixPartPath}/outside/dd/part/sparepart/getRepertoryBySparePartIds`, params);
}

/**
 * 添加备件并申请出库 + 物料清单新增并申领
 * @param {Object} params 参数对象
 * @param {Array} params.spareparts 申领信息
 * @param {String} params.remark 备注
 * @param {String} params.approveType 申请类型
 * @param {String} params.taskCardId 附加组件id
 * @param {String} params.taskId 工单id
 * @param {String} params.businessInfo 工单信息
 */
export function addInventoryAndApply(params: {} | undefined) {
  return http.post(`${fixedPrefixTaskPath}/outside/pc/inventory/addInventoryAndApply`, params);
}

/**
 * 完成回执查询核销物料的关联产品类型
 * @param {Object} params 参数对象
 * @param {Array} params.materielIdList 物料id
 */
export function getCatalogByMat(params: {} | undefined) {
  return http.post('/api/customer/outside/pc/catalog/catalogByMat', params);
}

/**
 * 物料返还查询物料绑定产品类型对应的产品
 * @param {Object} params 参数对象
 * @param {Array} params.materielIdList 物料idd
 * @param {Array} params.productIds 产品id
 */
export function getProductByMat(params: {} | undefined) {
  return http.post('/api/customer/outside/pc/catalog/product/byMat', params);
}

/**
 * 同步报价单
 * @param {Object} params 参数对象
 * @param {Array} params.taskId
 */
export function getTaskSyncQuote(params: {} | undefined) {
  return http.post('/api/middleware/outside/bgi/quote/get/taskSyncQuote', params);
}

/**
 * 申请出库
 * @param {Object} params 参数对象
 * @param {Array} params.spareparts 申领信息
 * @param {String} params.remark 备注
 * @param {String} params.approveType 申请类型
 * @param {String} params.taskCardId 附加组件id
 * @param {String} params.taskId 工单id
 * @param {String} params.businessInfo 工单信息
 */
export function addInventoryApply(params: {} | undefined) {
  return http.post(`${fixedPrefixTaskPath}/outside/pc/inventory/addInventoryApply`, params);
}

/**
 * 
 * @description 获取工单安装产品和安装位置字段 目前用于博立定制 后续可能通用
 */
export function getExpensePartField(params: {} | undefined) {
	return http.get(`${fixedPrefixTaskPath}/outside/pc/task/getExpensePartField`, params);
}

/**
 * @description 查询计划任务列表
 */
export function getPlanTaskList(params: TaskPlanTaskSearchModel): Promise<getTaskPlanTaskResult> {
	return http.post(`${fixedPrefixTaskPath}/outside/task/list/plantask/list`, params)
}

/**
 * @description  查询计划任务列表 初始化数据
 * @param {String} params.planId 传planId 获取详情
 */
export function getPlanTaskListInitData(): Promise<getTaskPlanTaskDetailResult> {
	return http.get(`${fixedPrefixTaskPath}/outside/task/list/plantask/init`)
}

/**
 * @description  查询计划任务详情 动态信息
 * @param {String} params.planId
 */
export function getPlanTaskRecord(params: TaskSearchListModel): Promise<getTaskPlanTaskInitDataResult> {
	return http.post('/api/task/outside/pc/task/planTask/action/record', params)
}

/**
 * @description  查询计划任务详情 计划工单列表
 * @param {String} params.planId
 */
export function getPlanTaskTaskList(params: TaskSearchListModel): Promise<getTaskSearchListResult> {
	return http.post('/api/task/outside/pc/task/planTask/task/list', params)
}

/**
 * @description  查询计划任务详情 计划工单列表 删除工单
 * @param {String} params.planId
 * @param {String} params.taskIds 
 */
export function deletePlanTaskTask(params: TaskSearchListModel): Promise<getTaskSearchListResult> {
	return http.post('/api/task/outside/pc/task/planTask/task/delete', params)
}

/**
 * @description  查询计划任务详情 计划工单列表 立即创建
 * @param {String} params.planId
 * @param {String} params.taskIds 
 */
export function createTaskNow(params: TaskSearchListModel): Promise<getTaskSearchListResult> {
	return http.post('/api/task/outside/pc/task/planTask/task/createTaskNow', params)
}

/**
 * @description  查询计划任务详情 计划工单列表 调整时间
 * @param {String} params.planId 计划任务id
 * @param {String} params.taskId 工单id
 * @param {String} params.expectCreateTime 预计创建时间
 */
export function updatePlanTaskTaskTime(params: TaskSearchListModel): Promise<getTaskSearchListResult> {
	return http.post('/api/task/outside/pc/task/planTask/task/updateTime', params)
}

/**
 * @description  查询计划任务详情 取消
 * @param {String} params.planId
 */
export function cancelPlanTask(params: TaskSearchListModel): Promise<getTaskSearchListResult> {
	return http.get('/api/task/outside/pc/task/planTask/cancel', params)
}

/**
 * @description 查询审核结算列表
 */
export function getTaskBalanceList(params: TaskSearchListModel): Promise<getTaskSearchListResult> {
	return http.post(`${ElASTICSEARCH}/outside/es/task/pc/list/balance`, params)
}

/**
 * @description  查询工单结算列表 初始化数据
 */
export function getTaskBalanceListInitData(): Promise<getTaskBalanceInitDataResult> {
	return http.get(`${fixedPrefixTaskPath}/outside/task/list/balance/init`)
}

/**
 * 订阅工单
 * @param params
 * @param params.module 模块名称
 */
export function taskSubscribe(params: TaskSubscriceModel): Promise<getTaskSubscribeResult> {
	return http.get('/task/subscribe', params)
}

/**
 * 取消订阅工单
 * @param params
 * @param params.module 模块名称
 */
export function taskUnSubscribe(params: TaskSubscriceModel): Promise<getTaskUnSubscribeResult> {
	return http.get('/task/unSubscribe', params)
}

/**
 * @description: 工单PC端结算列表顶部统计
 */
export function getTaskBalanceCount(): Promise<getTaskBalanceCountResult> {
	return http.get(`${ElASTICSEARCH}/outside/es/task/pc/list/balance/top/count`)
}

/**
 * @description: 工单结算自定义字段查询
 */
export function getTaskBalanceFieldInfo(): Promise<getTaskBalanceFieldInfoResult> {
	return http.get(`${fixedPrefixTaskPath}/outside/task/list/balance/fieldinfo`)
}

/**
 * @description: 工单结算金额查询
 */
export function getTaskBalancePrice(params: TaskSearchListModel): Promise<getTaskBalancePriceResult> {
	return http.post(`${ElASTICSEARCH}/outside/es/task/pc/list/balance/price`, params)
}

/**
 * @description: 工单批量结算
 */
export function batchBalanceTask(params: TaskBatchBalanceModel): Promise<getBatchTaskBalanceResult> {
	return http.post('/balance/balanceBatch', params, false)
}

/**
 * 备件清单核销列表
 * @param {Object} params 参数对象
 * @param {String} params.cardId 附加组件id
 * @param {String} params.taskId 工单id
 */
export function getInventoryRecordGroup(params: {} | undefined) {
  return http.get(`${fixedPrefixTaskPath}/outside/pc/inventory/getInventoryRecordGroup`, params);
}

/**
 * 备件清单核销列表
 * @param {Object} params 参数对象
 * @param {String} params.taskId 工单id
 */
export function getReceiptSparePartProperty(params: {} | undefined) {
  return http.get(`${fixedPrefixTaskPath}/outside/pc/task/getReceiptSparePartProperty`, params);
}


/* -------------  end  新工单api ---------------- */

/**
 * 批量创建计划任务
 */
export function createBatchPlanTask(params: {} | undefined): Promise<getBatchCreatePlanTaskResult> {
	return http.post('/task/planTask/createBatch', params);
}

/**
 * 批量指派: 工单池
 */
export function batchAllotPool(params: {} | undefined): Promise<getBatchAllotResult> {
	return http.post('/task/allotTasksToPool', params, false);
}

/**
 * 批量指派: 工单负责人
 */
export function batchAllotTask(params: {} | undefined): Promise<getBatchAllotResult> {
	return http.post('/task/allotTasks', params, false);
}
/**
 * 批量指派: 自动分配
 */
export function batchAllotAuto(params: {} | undefined): Promise<getBatchAllotResult> {
	return http.post('/task/autoDispatchBatch', params, false);
}
/**
 * 指派列表批量指派: 自动分配
 */
export function batchAutoDispatch(params: {} | undefined): Promise<getBatchAllotResult> {
	return http.post('/api/task/outside/pc/task/batchAutoDispatch', params);
}

/**
 * 地图指派列表
 */
export function getAllotListAsyn(params: {} | undefined) {
  disposeTaskSearchModelDate(params);
	return http.post('/task/getAllotListAsyn', params, false);
}

/**
 * 指派列表
 */
export function getTaskAllotList(params: TaskSearchListModel): Promise<getTaskSearchListResult> {
	return http.post(`${ElASTICSEARCH}/outside/es/task/pc/list/allot`, params)
}

/**
 * 指派列表initData
 */
export function getAllotListInit(params: {} | undefined) {
	return http.get(`${fixedPrefixTaskPath}/outside/task/list/allot/init`, params)
}

/**
 * 工单池列表
 */
export function getPoolList(params: {} | undefined) {
	return http.post(`${ElASTICSEARCH}/outside/es/task/pc/list/pool`, params)
}

/**
 * 工单池列表initData
 */
export function getTaskPoolListInitData(): Promise<getTaskPoolInitDataResult> {
	return http.get(`${fixedPrefixTaskPath}/outside/task/list/taskpool/init`)
}

/**
 * 工单指派列表initData
 */
export function getTaskAllotListInitData(): Promise<getTaskAllotInitDataResult> {
	return http.get(`${fixedPrefixTaskPath}/outside/task/list/allot/init`)
}

/**
 * 工单回访列表初始化数据
 */
export function getTaskReviewListInitData(): Promise<getTaskReviewInitDataResult> {
	return http.get(`${fixedPrefixTaskPath}/outside/task/list/review/init`)
}

/**
 * @description  查询工单关闭列表 初始化数据
 */
export function getTaskCloseListInitData(): Promise<getTaskCloseInitDataResult> {
	return http.get(`${fixedPrefixTaskPath}/outside/task/list/close/init`)
}

/**
 *@description 工单关闭列表
 */
export function getTaskCloseList(params: TaskSearchListModel): Promise<getTaskSearchListResult> {
	return http.post(`${ElASTICSEARCH}/outside/es/task/pc/list/close`, params)
}

/** 
 * 工单池列表待接受和超时工单数
 */
export function getTaskPoolHeadInfos(): Promise<getTaskPoolInfoResult> {
	return http.get('/task/getTaskPoolHeadInfos')
}

/**
 * @description 工单回访列表
 * 和PC端/outside/es/task/search返回结构一致，请求体参数根据具体页面上需要支持的再定，内部的搜索支持基本是通用的
 * 入参上
 * 多了reviewType、isReview、isEvaluate、balanceUser、reviewUser、degree
 * 返回值上
 * 多了评价数据esTaskEvaluateEntity
*/
export function getTaskReviewList(params: TaskSearchListModel): Promise<getTaskSearchListResult> {
	return http.post(`${ElASTICSEARCH}/outside/es/task/pc/list/review`, params)
}

/** 
 * 获取工单类型
 */
export function getTaskTypeList(params: any) {
	return http.get('/task/getTaskTypeList')
}

/** 
 * 获取工单类型配置的知识库字段
 */
/**
 * @description 获取工单类型配置的知识库字段
 * @param {Object} params - 参数
 * @param {String} params.typeId - 工单类型Id
 */
 export function getConfig(params: any) {
	return http.get('/dd/taskType/getConfig', params)
}

/**
 * 下载附件
 * @param {*} params 
 */
 export function exportAttachment(params: any){
  return http.post('/excels/taskAttachmentDownloadBatch', params, true);
}

/**
 * @description 工单详情中判断当前工单是否显示服务报告打印按钮
 * @param {Object} params - 参数
 */
 export function serviceReportCheckPrintAuthority(params: any) {
	return http.get(`${fixedPrefixTaskPath}/outside/pc/task/serviceReport/checkPrintAuthority`, params)
}

/**
 * @description 点击发送给客户确认，判断是否已经给客户发送过服务报告
 * @param {Object} params - 参数
 */
 export function serviceReportGetConfirmStatus(params: any) {
	return http.get(`${fixedPrefixTaskPath}/outside/pc/transformation/getConfirmStatus`, params)
}

/**
 * @description 发送服务报告，并且发送短信通知客户
 * @param {Object} params - 参数
 */
 export function serviceReportSendServiceReport(params: any) {
	return http.post(`${fixedPrefixTaskPath}/outside/pc/transformation/sendServiceReport`, params)
}

/**
 * @description 工单详情生成服务报告（需要签字）
 * @param {Object} params - 参数
 */
 export function createServiceReportPreview(params: any) {
	return http.get('/task/createServiceReportPreview', params)
}

/**
 * @description 工单详情生成服务报告（不需要签字）
 * @param {Object} params - 参数
 */
 export function createServiceReportDownload(params: any) {
	return http.get('/task/createServiceReportDownload', params)
}


/**
 * @description 获取备件配置信息
 */
 export function getSparepartConfigValid() {
	return http.get('/partV2/repertory/apply/valid');
}
/**
 * @description 获取产品附件组件数据
 * @param {Object} params - 参数
 */
 export function getRelationPartCard(params: any) {
	return http.get('/api/customer/outside/card/product/relationPartCard', params)
}
/**
 * @description 获取产品附件组件所有数据
 * @param {Object} params - 参数 productId，cardId
 */
 export function getCardInfo(params: any) {
	return http.get('/api/customer/outside/card/product/cardInfo', params)
}
/**
 * @description 工单费用灰度控制接口
 * @param {Object} params
 */
 export function getTaskCost(params: any) {
	return http.get('/setting/getTaskCost', params)
}
/**
 * @description 筛选工单产品
 * @param {Object} params
 */
 export function getQueryTaskProduct(params: Params) {
	return http.get(`${fixedPrefixTaskPath}/outside/pc/task/queryTaskProduct`,params);
}

/**
 * @description 工单的结算单
 * @param {Object} params - 参数
 */
export function getTaskSettleInfo(params: any) {
  return http.get('/api/app/outside/settle/getSettleByTaskId', params)
}

/**
 * 分页查询结算单
 * @param {String} keyword 查询关键字
 * @param {Number} pageNum 查询页码
 */
export function settleList(params: any = {}) {
  return http.post('/api/app/outside/settle/list', params);
}

/**
 * 服务商结算 将勾选的对象加入结算单中
 * @param {String} keyword 查询关键字
 * @param {Number} pageNum 查询页码
 */
export function addToSettle(params: any = {}) {
  return http.post('/api/app/outside/settle/addToSettle', params);
}

/**
 * 查询服务商结算单权限
 * @param {Object} params - 参数
 */
export function searchProvideSettleAuth(params: any) {
  return http.get('/api/app/outside/settle/taskTabInit', params);
}

/**
 * 服务商结算 校验选中的工单进行结算单创建
 * @param {String} keyword 查询关键字
 * @param {Number} pageNum 查询页码
 */
export function checkSettle(params: any = {}) {
  return http.post('/api/app/outside/settle/checkSettle', params);
}

/**
 * 服务商结算 基于工单id，服务商id创建结算单
 * @param {String} keyword 查询关键字
 * @param {Number} pageNum 查询页码
 */
export function createSettle(params: any = {}) {
  return http.post('/api/app/outside/settle/createSettle', params);
}

/**
 * 获取故障库 - 故障现象列表
 * @param {String} keyword 查询关键字
 * @param {Array} productTypeList 产品类型
 */
export function getWikiList(params: any = {}) {
  return http.post('/api/search/outside/es/wiki/list', params);
}

/**
 * 获取工单结算项目
 * @param {String} keyword 查询关键字
 * @param {Number} pageNum 查询页码
 */
export function TaskGetTaskItem(params: any = {}) {
  return http.get('/api/app/outside/settle/task/getTaskItem', params);
}

/**
 * 保存工单结算项目
 * @param {String} keyword 查询关键字
 * @param {Number} pageNum 查询页码
 */
export function TaskSaveTaskItem(params: any = {}) {
  return http.post('/api/app/outside/settle/task/saveTaskItem', params);
}
/* 表单设计器中、获取服务商资质和工程师资质的下拉列表数据
 * @param {Object} 暂无参数
 */
export function getDataSourceApi(params:{}) {
  return http.get(`${fixedPrefixTaskPath}/outside/task/template/qualification/dataSource`, params);
}
// 工单关闭前检测未结束工时记录人员名单接口（PC端）
export function checkWorkTimeBeforeFinishApi(params: any = {}) {
  return http.post('/api/task/outside/tasktime/checkWorkTimeBeforeFinish', params, false );
}
// 获取合同相关的客户和产品数据
export function getContractCustomerApi(params: any) {
  return http.get('/api/customer/outside/contract/getContractCustomer', params);
}

// 根据合同配置获取合同生效失效时间
export function getContractTime(params: any) {
	return http.get(`/api/customer/outside/contract/jump/planTask`, params)
}

/**
 * @des pc端工单选人组件-部门树
 * @param params
 * @see http://yapi.shb.ltd/project/307/interface/api/35640
 * @returns
 */
export function getTaskSelectUserTagTree(params: any) {
  return http.post('/pc/task/select/user/tag/tree', params);
}

/**
 * @des pc端工单选人组件-点击部门
 * @param params
 * @see http://yapi.shb.ltd/project/307/interface/api/35646
 * @returns
 */
export function getTaskSelectUserTagClick(params: any) {
  return http.post('/pc/task/select/user/tag/click', params);
}

/**
 * @des pc端工单选人组件-搜索人员
 * @param params
 * @see http://yapi.shb.ltd/project/307/interface/api/35652
 * @returns
 */
export function getTaskSelectUserTagSearch(params: any) {
  return http.post('/pc/task/select/user/search', params);
}

/**
 * @des pc端工单选人组件-搜索人员,点击更多
 * @param params
 * @see http://yapi.shb.ltd/project/307/interface/api/35658
 * @returns
 */
export function getTaskSelectUserTagMore(params: any) {
  return http.post('/pc/task/select/user/more', params);
}
// 获取工单列表质保信息
export function getQualityFieldApi(params: any = {}) {
  return http.get('/setting/taskType/getQualityField', params);
}

// 获取产品类型名称换产品目录
export function getTopCatalogListByName(params: any = {}) {
	return http.get(`${CUSTOMER}/outside/pc/catalog/topCatalogListByName`, params);
}

// 工单结算金额审批，走paas审批
export function taskProviderSettleApproveApply(params: any = {}) {
	return http.post(`${APPS}/outside/settle/task/taskProviderSettleApproveApply`, params);
}

// 工单详情 -- 工单结算金额tab页 详情查询
export function getOneTaskExtra(params: any = {}) {
	return http.get(`${APPS}/outside/settle/task/getOneTaskExtra`, params);
}

// 工单详情 -- 获取工单详情页面 工单结算金额tab页的字段
export function buildFields(params: any = {}) {
	return http.get(`${APPS}/outside/settle/task/buildFields`, params);
}

// 工单详情 -- 获取工单详情页面 工单结算金额tab页的字段 包括结算明细
export function newbuildFields(params: any = {}) {
	return http.get(`/api/app/outside/settle/task/view/buildFields`, params);
}

// 工单详情 -- 获取服务商列表(东风农机)
export function getSpUserForSpAllot(params: any = {}) {
	return http.post(`/api/user/outside/select/getSpUserForSpAllot`, params);
}

// 工单详情 -- 获取轨迹里程
export function getTrack(params: any = {}) {
	return http.get(`/api/task/outside/pc/task/get/track`, params);
}

// 工单 -- 工单类型列表
export function getTaskTypeListNew(params: any = {}) {
	return http.get(`/task/taskType/role`, params);
}

// 工单 -- 工单获取类型可创建可以查看列表
export function getCurrentAllTaskTypeList(params: any = {}) {
	return http.get(`/task/type`, params);
}
// 工单回执 -- 获取单个汇率
export function queryRate(params: any = {}) {
	return http.post(`/api/workbench/outside/exchange/rate/query`, params);
}

// 工单回执 -- 获取汇率列表
export function queryRateList(params: any = {}) {
	return http.post(`/api/workbench/outside/exchange/rate/queryBatch`, params);
}

// 工单回执 校验申领物料数量校验
export function getValiteReturnUnusedCheck(params: any = {}){
	return http.post(`/api/task/outside/pc/warehouse/outbound/data/copy`, params);
}

// 工单回访 -- 批量指派
export function batchAllocateTask(params: any = {}) {
	return http.post('/api/task/outside/task/user/batchAllocateTask', params);
}

// 附加组件-备件退回
export function taskSparePartReturn(params: any = {}) {
	return http.post('/api/task/outside/pc/inventory/taskSparePartReturn', params);
}

// 根据工单类型获取对应附加组件信息
export function getTaskCardByIds(params: any = {}) {
	return http.post('/api/task/outside/pc/task/card/getCardByIds', params);
}
// 工单自定义节点-点击自定义按钮查询表单字段
export function getTaskCustomNodeFields(params: any = {}) {
	return http.get('/api/task/outside/pc/task/getCurrentNodeFieldList', params);
}

// 工单自定义节点-完成节点（保存节点信息）
export function saveCustomNode(params: any = {}) {
	return http.post('/api/task/outside/pc/task/submitCustomerNode', params);
}

// 工单自定义节点-获取工单当前节点的负责人和之前节点的办理人
export function getCurrentNodeExecutorAndNodeHandlingPerson(params: any = {}) {
	return http.get('/api/task/outside/pc/task/getCurrentNodeExecutorAndNodeHandlingPerson', params);
}

// 工单自定义节点-更新当前节点的负责人
export function updateCurrentNodeExecutor(params: any = {}) {
	return http.post('/api/task/outside/pc/task/updateCurrentNodeExecutor', params);
}

// 工单自定义节点-获取进度条
export function getSelectNodeCompleteTime(params: any = {}) {
	return http.get('/api/task/outside/pc/task/selectNodeCompleteTime', params);
}

// 工单详情-获取表单（包括自定义）
export function getTheFieldOfNodesThatHaveBeenPassed(params: any = {}) {
	return http.get('/api/task/outside/pc/task/getTheFieldOfNodesThatHaveBeenPassed', params);
}

/**
 * 根据工单类型id获取自定义节点和对应的表单字段
 * @param {String} taskTypeId 工单类型id
 */
export function getTaskNodeFieldListByTypeIds(params: any = {}) {
	return http.get(`${fixedPrefixTaskPath}/outside/pc/task/selectNodeFieldListByTypeId`,params);
}

/**
 * 暂存工单流程节点信息
 */
export function draftSaveCustomerNode(params: any = {}) {
	return http.post(`${fixedPrefixTaskPath}/outside/pc/task/nodeTemporaryStorageSave`,params);
}

/**
 * 查询暂存工单流程节点信息
 */
export function getCurrentDraftCustomerNode(params: any = {}) {
	return http.get(`${fixedPrefixTaskPath}/outside/pc/task/getCurrentNodeTemporaryStorage`,params);
}
/**
 * 智能化派单批量自动指派
 */
export function batchAutoDispatchByAgent(params: any = {}) {
	return http.post(`${fixedPrefixTaskPath}/outside/pc/task/batchAutoDispatchByAgent`,params);
}

export function aiTaskSearch(params: {
	input: string,
	page: number,
	pageSize: number,
}) {
	return http.post(`/api/elasticsearch/outside/ai/task/search`,params);
}

export function aiTaskSearchV2(params: {
	page: number,
	pageSize: number,
	input: string,
	searchParam: Record<string, any>
}) {
	return http.post(`/api/elasticsearch/outside/ai/task/search_v2/next`,params);
}


export function getTaskInfoByServiceShopOrder(params: any = {}) {
	return http.get(`/api/middleware/outside/kaili/cleanTask/init`, params);
}