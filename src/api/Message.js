
import http from '@src/util/http';
let prefix = '/api/message/outside/msg/config';
  
/** 
 * @description 通过模块名获取消息设置列表
 */
export function getListByModule(params = {}) {
  return http.get(`${prefix}/getListByModule`, params)
}

/** 
 * @description 开启关闭消息通知
 */
export function msgConfigSwitch(params = {}) {
  return http.post(`${prefix}/switch`, params)
}

/** 
 * @description 获取消息设置详情
 */
export function configDetail(params = {}) {
  return http.get(`${prefix}/detail`, params)
}

/** 
 * @description 获取消息设置详情
 */
export function getConfigDetail(params = {}) {
  return http.get(`${prefix}/detail/config`, params)
}

/** 
 * @description 获取系统设置详情
 */
export function getSysDetail(params = {}) {
  return http.get(`${prefix}/detail/sys`, params)
}

/** 
 * @description 获取短信设置详情
 */
export function getSmsDetail(params = {}) {
  return http.get(`${prefix}/detail/sms`, params)
}

/** 
 * @description 获取短信设置详情
 */
 export function getEmailDetail(params = {}) {
  return http.get(`${prefix}/detail/email`, params)
}

/** 
 * @description 获取微信设置详情
 */
 export function getWxDetail(params = {}) {
  return http.get(`${prefix}/detail/weixin`, params)
}

/** 
 * @description 获取电话设置详情
 */
 export function getPhoneDetail(params = {}) {
  return http.get(`${prefix}/detail/calling`, params)
}

/** 
 * @description 更新系统消息类型设置
 */
export function updateSysMsg(params = {}) {
  return http.post(`${prefix}/updateSysMsg`, params)
}


/** 
 * @description 查询消息设置的渠道是否全部关闭
 */
export function checkChannelAllClose(params = {}) {
  return http.get(`${prefix}/checkChannelAllClose`, params)
}

/** 
 * @description 查询应用列表
 */
export function getMessageApplyRange(params = {}) {
  return http.get('/setting/getMessageApplyRange', params)
}


/** 
 * @description 检验有没有填写计划时间
 */
export function checkHasTaskPlanTime(params = {}) {
  return http.get(`${prefix}/checkHasTaskPlanTime`, params)
}


// 设置发送时间新接口-消息设置页面
export function setReportSendTime(params = {}) {
  return http.post(`${prefix}/setReportSendTime`, params, false);
}

/**
 * @description 获取触发器消息设置的触发规则
 * @see http://yapi.shb.ltd/project/157/interface/api/37926
 * @param {Object} params 参数
 * @param {String} params.tenantId 租户id
 * @param {String} params.messageTypeName 消息类型名称 ( 如果是初始化 则参数是 default )
 */
export function getTriggerRule(params = {}) {
  // 临时先用 inside 的接口
  return http.get('/api/message/inside/msg/config/trigger/getTriggerRule', params);
  // return http.get('/api/message/v1/outside/msg/config/trigger/getTriggerRule', params);
}

/**
 * @description 触发器获取消息节点默认的配置
 * @see http://yapi.shb.ltd/project/157/interface/api/37946
 * @param {Object} params 参数
 */
export function getMessageConfig(params = {}) {
  return http.get('/api/application/outside/trigger/message/getMessageConfig', params);
}

export function getMessageConfigDemo(params = {}, demoTriggerId) {
  return http.get(`/api/application/outside/trigger/template/message/getMessageConfig`, params);
}

/**
 * @description 触发器发送消息内部选人获取标签
 * @see http://yapi.shb.ltd/project/157/interface/api/37930
 * @param {Object} params 参数
 */
export function getTriggerMarkList(params = {}) {
  return http.get('/api/application/outside/trigger/message/getMarkList', params);
}

/**
 * @description 触发器发送消息内部选人点击动态获取
 * @see http://yapi.shb.ltd/project/157/interface/api/37934
 * @param {Object} params 参数
 */
export function getDynamicMessageField(params = {}) {
  return http.post('/api/application/outside/trigger/message/getDynamicMessageField', params);
}

/**
 * @description 触发器获取表单字段用于消息替换
 * @see http://yapi.shb.ltd/project/3088/interface/api/37938
 * @param {Object} params 参数
 * @param {String} params.bizType 业务类型
 * @param {String} params.bizTypeId 业务类型id
 */
export function getTriggerTemplateField(params = {}) {
  return http.post('/api/application/outside/trigger/message/getTemplateField', params);
}

/**
 * @description 获取订阅消息模板字段
 * @param {*} params 
 */
export function getLabelSubscribeGetTemplateField(params = {}) {
  return http.post('/api/voice/outside/label/subscribe/getTemplateField', params);
}

/**
 * @des 来自标签订阅接口内部选人动态获取
 * @param {*} params 
 */
export function getLabelSubscribeMarkList(params = {}) {
  return http.get('/api/voice/outside/label/subscribe/getMarkList', params);
}

export function getTriggerTemplateFieldDemo(params = {}, demoTriggerId) {
  const _demoTriggerId = demoTriggerId || params.demoTriggerId
  return http.post(`/api/application/outside/trigger/template/message/getTemplateField?demoTriggerId=${_demoTriggerId}`, params);
}

/**
 * @description 触发器定时字段
 */
export function getTemplateField(apiId = []) {
  return http.post(`/api/application/outside/trigger/getDateField`, apiId);
}

/**
 * @description 触发器定时字段
 */
export function getTemplateFieldV2(apiId = []) {
  return http.post(`/api/application/outside/trigger/message/getDateField`, apiId);
}

export function getTemplateFieldDemo(params = {}, demoTriggerId) {
  const _demoTriggerId = demoTriggerId || params.demoTriggerId
  return http.post(`/api/application/outside/trigger/template/message/getDateField?demoTriggerId=${_demoTriggerId}`, params);
}

/**
 * @description 获取工单自定义节点的消息
 */
export function getTaskCustomNodeMessageRules() {
    return http.get('/api/task/outside/task/message/rule/getMessageRuleList')
}

/**
 * @description 消息工单自定义节点开关
 */
export function createTaskCustomNodeMessageRuleList(params = {}){
    return http.post('/api/task/outside/task/message/rule/createMessageRuleList', params)
}

/**
 * @description 获取订阅配置
 */
export function getSubscribeConfig(params) {
  if (!params.configId) {
    return http.get(`/api/voice/outside/label/subscribe/getSubscribeConfig?labelId=${params.labelId}&side=${params.side}`)
  }
  return http.get(`/api/voice/outside/label/subscribe/getSubscribeConfig?labelId=${params.labelId}&side=${params.side}&configId=${params.configId}`)
}

/**
 * @description 保存订阅配置
 */
export function updateSubscribe(params) {
  return http.post('/api/voice/outside/label/subscribe/updateSubscribe', params)
}

/**
 * @description 获取模板字段配置
 */
export function getDateFieldLabel(params) {
  return http.post('/api/voice/outside/label/subscribe/getDateField', params)
}

/**
 * @description 课程开关通知更新
 */
export function trainingSettingUpdateByCode(params = {}) {
  return http.post(`/api/app/outside/trainingSetting/updateByCode`, params);
}
