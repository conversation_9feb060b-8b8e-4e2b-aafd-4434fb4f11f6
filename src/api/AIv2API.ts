/* util */
import http from '@src/util/http'
/* types */
import { 
  AIAgentAppAbilityBizAppItem, 
  AIAgentAppAbilityItem, 
  AIAgentAppAuthAddParamType, 
  AIAgentAppAuthItem, 
  AIAgentAppAuthRemoveParamType, 
  AIAgentAppType, 
  AiAgentChatData, 
  AIAgentDataSummaryType, 
  AiAgentLog, 
  AIAgentType, 
  DingtalkAssistantInstallInfo
} from '@src/modules/ai/types'
/* models */
import MsgModel from '@model/MsgModel'
import { 
  AddDingtalkAgentParamType,
  AIAgentAppAbilityAddParamType,
  AIAgentAppAbilityListParamType,
  AIAgentAppAbilityRemoveParamType,
  AIAgentAppAddTemplateParamType,
  AIAgentAppAuthListParamType,
  AIAgentAppDeleteParamType,
  AIAgentAppDetailParamType,
  AIAgentAppDetailUpdateParamType,
  AIAgentAppListParamType,
  AIAgentAppResetPromptParamType,
  AIAgentAppTemplateListParamType, 
  AiAgentChartParamType, 
  AIAgentCreateParamType, 
  AIAgentDeleteParamType, 
  AIAgentDetailParamType, 
  AIAgentEditModelParamType, 
  AIAgentEditNameParamType, 
  AIAgentListParamType, 
  AIAgentLogParamType, 
  AIAgentStatusParamType, 
  AIAgentWorkflowShowParamType, 
  AiChatAgentConversationParamType, 
  AIImAgentDetailSummaryParamType, 
  AIImQaAgentParamType, 
  AiGetTicketParameterExtractionParamType, 
  AIVocAgentDetailSummaryParamType, 
  AIVocAgentSummaryAbstractParamType, 
  AIVocAgentSummaryParamType,
  DeleteNotOpenWikiParamType,
  AIAgentAppAbilityRelearnParamType,
  SendShareAccessMessageParamType,
  GetAbstractTemplateListParamType,
  GetAbstractFieldsParamType
} from '@src/modules/ai/model/param'
import { 
  AiChatAgentConversationResultType,
  AIImAgentDetailSummaryResultType,
  AIImQaAgentResultType,
  AIVocAgentDetailSummaryResultType, 
  AIVocAgentSummaryAbstractResultType, 
  AIVocAgentSummaryResultType 
} from '@src/modules/ai/model/result'
import { AIAgentTemplateEnum, AiKnowledgeTypeEnum } from '@src/modules/ai/model/enum'
import Page from '@model/Page'
import {
  DeleteChatConversationContentModel,
  DeleteChatConversationContentShareModel,
  GetChatConversationModel, 
  GetChatConversationShareModel, 
  GetChatHistoryMessageModel, 
  GetChatHistoryMessageShareModel, 
  SendChatConversationContentModel, 
  SendChatConversationContentShareModel 
} from '@model/param/in/AI'
import { 
  DeleteChatConversationContentResult,
  GetChatConversationResult, 
  GetChatHistoryMessageResult, 
  SendChatConversationContentResult 
} from '@model/param/out/AI'
import Result from '@model/Result'
import { AiSummaryFieldsType, AiSummaryTemplateType } from '@src/modules/ai/types/summary'
import { isFalsy } from 'pub-bbx-utils'

function getAIAgentDataStatistics(): Promise<MsgModel<AIAgentDataSummaryType>> {
  return http.get('/api/voice/outside/xiaobao/agent/logAnalysis/overviewOfAgentUsage')
}

function getAIAgentList(params: AIAgentListParamType): Promise<MsgModel<AIAgentType[]>> {
  return http.post('/api/voice/outside/xiaobao/agent/list', params)
}

function updateAgentStatus(params: AIAgentStatusParamType): Promise<MsgModel<void>> {
  return http.post('/api/voice/outside/xiaobao/agent/status', params)
}

function deleteAIAgent(params: AIAgentDeleteParamType): Promise<MsgModel<void>> {
  return http.post('/api/voice/outside/xiaobao/agent/delete', params)
}

function getDingtalkAgentList(): Promise<MsgModel<DingtalkAssistantInstallInfo[]>> {
  return http.post('/api/voice/outside/xiaobao/agent/dingtalk/list')
}

function addDingtalkAgent(params: AddDingtalkAgentParamType): Promise<MsgModel<void>> {
  return http.post('/api/voice/outside/xiaobao/agent/dingtalk/add', params)
}

function getAIAgentDetail(params: AIAgentDetailParamType): Promise<MsgModel<AIAgentType>> {
  return http.post('/api/voice/outside/xiaobao/agent/detail', params)
}

function getAIAgentDetailShare(params: AIAgentDetailParamType): Promise<MsgModel<AIAgentType>> {
  return http.post('/api/voice/outside/xiaobao/agent/share/detail', params)
}

function getAIAgentAppList(params: AIAgentAppListParamType): Promise<MsgModel<AIAgentAppType[]>> {
  return http.post('/api/voice/outside/xiaobao/agent/app/list', params)
}

function getAIAgentAppListWithAuth(params: AIAgentAppListParamType): Promise<MsgModel<AIAgentAppType[]>> {
  return http.post('/api/voice/outside/xiaobao/agent/app/listWithAuth', params)
}

function getAIAgentAppListShare(params: AIAgentAppListParamType): Promise<MsgModel<AIAgentAppType[]>> {
  return http.post('/api/voice/outside/xiaobao/agent/app/share/list', params)
}

function editAIAgentName(params: AIAgentEditNameParamType): Promise<MsgModel<void>> {
  return http.post('/api/voice/outside/xiaobao/agent/updateName', params)
}

function editAIAgentModel(params: AIAgentEditModelParamType): Promise<MsgModel<void>> {
  return http.post('/api/voice/outside/xiaobao/agent/updateModel', params)
}

function getAIAgentAppTemplateList(params: AIAgentAppTemplateListParamType): Promise<MsgModel<AIAgentAppType[]>> {
  return http.post('/api/voice/outside/xiaobao/agent/app/template/list', params)
}

function addAIAgentAppTemplate(params: AIAgentAppAddTemplateParamType): Promise<MsgModel<void>> {
  return http.post('/api/voice/outside/xiaobao/agent/app/add', params)
}

function deleteAIAgentApp(params: AIAgentAppDeleteParamType): Promise<MsgModel<void>> {
  return http.post('/api/voice/outside/xiaobao/agent/app/delete', params)
}

function addAIAgent(params: AIAgentCreateParamType): Promise<MsgModel<void>> {
  return http.post('/api/voice/outside/xiaobao/agent/add', params)
}

function getAIAgentAppDetail(params: AIAgentAppDetailParamType): Promise<MsgModel<AIAgentAppType>> {
  return http.get('/api/voice/outside/xiaobao/agent/app/detail', params)
}

function updateAIAgentAppDetail(params: AIAgentAppDetailUpdateParamType): Promise<MsgModel<void>> {
  return http.post('/api/voice/outside/xiaobao/agent/app/update', params)
}

function resetAIAgentAppPrompt(params: AIAgentAppResetPromptParamType): Promise<MsgModel<void>> {
  return http.post('/api/voice/outside/xiaobao/agent/app/resetPrompt', params)
}

function getAIAgentAppAbilityList(params: AIAgentAppAbilityListParamType): Promise<MsgModel<AIAgentAppAbilityItem[]>> {
  return http.post('/api/voice/outside/xiaobao/agent/app/knowledge/list', params).then((res: MsgModel<AIAgentAppAbilityItem[]>) => {
    try {
      const isSuccess = MsgModel.isSuccess(res)
      if (isSuccess) {
        
        let data = MsgModel.getData<AIAgentAppAbilityItem[]>(res, [])
        
        data = data.filter(item => {
          
          const isLocalUpload = item.bizType == AiKnowledgeTypeEnum.LOCAL_UPLOAD
          if (isFalsy(isLocalUpload)) {
            return true
          }
          
          const fileName = item.name
          const suffix = fileName.split('.').pop() || ''
  
          const allowFileSuffix = ["PDF", "DOC", "DOCX", "XLSX", "PPT", "TXT", "PPTX"]
          const isAllowFile = allowFileSuffix.includes(suffix.toUpperCase())
          return isAllowFile
          
        })
        
        return MsgModel.succ('success', data) as MsgModel<AIAgentAppAbilityItem[]>
      }
      return res
    } catch (error) {
      return res
    }
  })
}

function addAIAgentAppAbility(params: AIAgentAppAbilityAddParamType): Promise<MsgModel<void>> {
  return http.post('/api/voice/outside/xiaobao/agent/app/knowledge/application/add', params)
}

function aiAgentAppAbilityRelearn(params: AIAgentAppAbilityRelearnParamType): Promise<MsgModel<void>> {
  return http.post('/api/voice/outside/xiaobao/agent/app/knowledge/refresh', params)
}

function removeAIAgentAppAbility(params: AIAgentAppAbilityRemoveParamType): Promise<MsgModel<void>> {
  return http.post('/api/voice/outside/xiaobao/agent/app/knowledge/delete', params)
}

function getAIAgentAppAbilityBizAppList(): Promise<MsgModel<AIAgentAppAbilityBizAppItem[]>> {
  return new Promise((resolve, reject) => {
    resolve(
      {
        "status": 0,
        "success": true,
        "message": "success",
        data: [
          {
            id: '1',
            bizId: null,
            bizType: AiKnowledgeTypeEnum.TASK,
            name: '工单中心'
          },
          {
            id: '2',
            bizId: null,
            bizType: AiKnowledgeTypeEnum.CALL_CENTER,
            name: '呼叫中心'
          },
          {
            id: '3',
            bizId: null,
            bizType: AiKnowledgeTypeEnum.IM,
            name: '在线客服'
          }
        ]
      }
    )
  })
}

function getAIAgentAppAuthList(params: AIAgentAppAuthListParamType): Promise<MsgModel<AIAgentAppAuthItem[]>> {
  return http.get('/api/voice/outside/xiaobao/agent/app/ranges', params)
}

function removeAIAgentAppAuth(params: AIAgentAppAuthRemoveParamType): Promise<MsgModel<void>> {
  return new Promise((resolve, reject) => {
    resolve(
      {
        "status": 0,
        "success": true,
        "message": "success",
      }
    )
  })
}

function vocAgentSummary(params: AIVocAgentSummaryParamType): Promise<MsgModel<AIVocAgentSummaryResultType>> {
  return http.post('/api/voice/outside/xiaobao/workflow/handle/voc/list', params)
}

function agentSummaryAbstract(params: AIVocAgentSummaryAbstractParamType): Promise<MsgModel<AIVocAgentSummaryAbstractResultType>> {
  return http.post('/api/voice/outside/xiaobao/workflow/handle/abstract/List', params)
}

function vocAgentDetailSummary(params: AIVocAgentDetailSummaryParamType): Promise<MsgModel<AIVocAgentDetailSummaryResultType>> {
  return http.post('/api/voice/outside/xiaobao/workflow/handle/voc/detail', params)
}

function imAgentDetailSummary(params: AIImAgentDetailSummaryParamType): Promise<MsgModel<AIImAgentDetailSummaryResultType>> {
  return http.get('/api/voice/outside/xiaobao/workflow/handle/IM/detail', params)
}

function getAIAgentWorkflowShow(params: AIAgentWorkflowShowParamType): Promise<MsgModel<boolean>> {
  return http.post('/api/voice/outside/xiaobao/agent/workflow/show', params)
}

function getAIAgentLog(params: AIAgentLogParamType): Promise<MsgModel<Page<AiAgentLog>>> {
  return http.post('/api/voice/outside/xiaobao/agent/logAnalysis/getLogPageList', params)
}

/**
 * @description 获取系统 Agent
 */
function getSystemAgent(): Promise<MsgModel<AIAgentType>> {
  return http.get('/api/voice/outside/xiaobao/agent/querySystemAgent') as Promise<MsgModel<AIAgentType>>
}

/**
 * @description 获取系统 Agent
 */
function aiChatAgentConversation(params: AiChatAgentConversationParamType): Promise<MsgModel<AiChatAgentConversationResultType>> {
  return http.post('/api/voice/outside/xiaobao/chat/handle/handleChat', params, true, {
    cancelable: true
  }) as Promise<MsgModel<AiChatAgentConversationResultType>>
}

/**
 * @description 初始化会话
 */
function getChatConversation(params: GetChatConversationModel): Promise<Result<GetChatConversationResult>> {
  return http.post('/api/voice/outside/xiaobao/ai/conversation/init', params) as Promise<Result<GetChatConversationResult>>
}

/**
 * @description 初始化会话
 */
function getChatConversationShare(params: GetChatConversationShareModel): Promise<Result<GetChatConversationResult>> {
  return http.post('/api/voice/outside/xiaobao/ai/conversation/share/init', params) as Promise<Result<GetChatConversationResult>>
}

/**
 * @description 发送会话消息
 */
function sendChatConversationContent(params: SendChatConversationContentModel): Promise<Result<SendChatConversationContentResult>> {
  return http.post('/api/voice/outside/xiaobao/ai/conversation/insert/content', params) as Promise<Result<SendChatConversationContentResult>>
}

/**
 * @description 发送会话消息
 */
function sendChatConversationContentShare(params: SendChatConversationContentShareModel): Promise<Result<SendChatConversationContentResult>> {
  return http.post('/api/voice/outside/xiaobao/ai/conversation/share/insert/content', params) as Promise<Result<SendChatConversationContentResult>>
}

/**
 * @description 删除会话消息
 */
function deleteChatConversationContent(params: DeleteChatConversationContentModel): Promise<Result<DeleteChatConversationContentResult>> {
  return http.post('/api/voice/outside/xiaobao/ai/conversation/delete/content', params) as Promise<Result<DeleteChatConversationContentResult>>
}

/**
 * @description 删除会话消息
 */
function deleteChatConversationContentShare(params: DeleteChatConversationContentShareModel): Promise<Result<DeleteChatConversationContentResult>> {
  return http.post('/api/voice/outside/xiaobao/ai/conversation/share/delete/content', params) as Promise<Result<DeleteChatConversationContentResult>>
}

/**
 * @description 获取聊天消息记录
 */
function getHistoryMessage(params: GetChatHistoryMessageModel): Promise<Result<GetChatHistoryMessageResult>> {
  return http.post('/api/voice/outside/xiaobao/ai/conversation/query/content', params) as Promise<Result<GetChatHistoryMessageResult>>
}

/**
 * @description 获取聊天消息记录
 */
function getHistoryMessageShare(params: GetChatHistoryMessageShareModel): Promise<Result<GetChatHistoryMessageResult>> {
  return http.post('/api/voice/outside/xiaobao/ai/conversation/share/query/content', params) as Promise<Result<GetChatHistoryMessageResult>>
}

/**
 * @description 获取图表数据
 */
function getAiAgentChartData(params: AiAgentChartParamType): Promise<MsgModel<AiAgentChatData>> {
  return http.post('/api/voice/outside/xiaobao/agent/logAnalysis/useAnalyticsVOResult', params)
}

function imQaAgent(params: AIImQaAgentParamType): Promise<MsgModel<AIImQaAgentResultType>> {
  return http.post('/api/voice/outside/xiaobao/workflow/handle/im/qa', params)
}

/**
 * @description 获取工单参数提取
 */
function getTicketParameterExtraction(params: AiGetTicketParameterExtractionParamType): Promise<MsgModel<Record<string, any>>> {
  return http.post('/api/voice/outside/xiaobao/chat/handle/ticketParameterExtraction', params)
}

/**
 * @description 获取工单摘要模板列表
 */
function getAbstractTemplateList(params: GetAbstractTemplateListParamType): Promise<MsgModel<AiSummaryTemplateType[]>> {
  return http.get('/api/voice/outside/xiaobao/abstract/template/getTemplateList', params)
}

/*
 * @description 获取首页 Agent 列表
 */
function getHomeAIAgentList(): Promise<MsgModel<AIAgentType[]>> {
  return http.post('/api/voice/outside/xiaobao/agent/home/<USER>').then((res: any) => {
    
    const isFail = MsgModel.isFail(res)
    if (isFail) {
      return res
    }

    function isAllowAgent(item: AIAgentType | undefined): boolean {
      return Boolean(item?.enable && !item?.needIm)
    }
    
    const data = MsgModel.getData<AIAgentType[]>(res, [])
    const systemAgent = data.find(item => item.template == AIAgentTemplateEnum.System)
    const isAllowSystemAgent = isAllowAgent(systemAgent)
    
    if (systemAgent && isAllowSystemAgent) {
      const msgModel = MsgModel.succ('success', [systemAgent]) as MsgModel<AIAgentType[]>
      return msgModel
    }
    
    const agentList = data.filter(isAllowAgent)
    const msgModel = MsgModel.succ('success', agentList) as MsgModel<AIAgentType[]>
    return msgModel
    
  })
}

/**
 * @description 发送验证码
 */
function sendLoginVerifyCode(params: { phone: string }): Promise<MsgModel<void>> {
  return http.post('/api/voice/outside/xiaobao/login/sendCode', params)
}

/**
 * @description 登录
 */
function login(params: { phone: string, code: string }): Promise<MsgModel<void>> {
  return http.post('/api/voice/outside/xiaobao/login', params)
}

function deleteNotOpenWiki(params: DeleteNotOpenWikiParamType): Promise<MsgModel<void>> {
  return http.get('/api/voice/outside/xiaobao/agent/app/knowledge/deleteNotOpenWiki', params)
}

function sendShareAccessMessage(params: SendShareAccessMessageParamType): Promise<MsgModel<void>> {
  return http.post('/api/voice/outside/xiaobao/agent/sendShareAccessMessage', params)
}

/**
 * @description 获取摘要字段
 */
function getAbstractFields(params: GetAbstractFieldsParamType): Promise<MsgModel<AiSummaryFieldsType[]>> {
  return http.get('/api/voice/outside/xiaobao/abstract/fields', params)
}

export {
  getAIAgentDataStatistics,
  getAIAgentList,
  getAIAgentDetail,
  editAIAgentName,
  getAIAgentAppTemplateList,
  addAIAgentAppTemplate,
  addAIAgent,
  getAIAgentAppDetail,
  getAIAgentAppAbilityList,
  addAIAgentAppAbility,
  removeAIAgentAppAbility,
  getAIAgentAppAbilityBizAppList,
  getAIAgentAppAuthList,
  removeAIAgentAppAuth,
  vocAgentSummary,
  vocAgentDetailSummary,
  updateAgentStatus,
  getDingtalkAgentList,
  addDingtalkAgent,
  getAIAgentAppList,
  deleteAIAgentApp,
  updateAIAgentAppDetail,
  editAIAgentModel,
  deleteAIAgent,
  resetAIAgentAppPrompt,
  getAIAgentWorkflowShow,
  getAIAgentLog,
  getSystemAgent,
  aiChatAgentConversation,
  getAIAgentDetailShare,
  getAIAgentAppListShare,
  getChatConversation,
  getChatConversationShare,
  sendChatConversationContent,
  sendChatConversationContentShare,
  getHistoryMessage,
  getHistoryMessageShare,
  deleteChatConversationContent,
  deleteChatConversationContentShare,
  getAiAgentChartData,
  getAIAgentAppListWithAuth,
  agentSummaryAbstract,
  imAgentDetailSummary,
  imQaAgent,
  getTicketParameterExtraction,
  getHomeAIAgentList,
  sendLoginVerifyCode,
  login,
  deleteNotOpenWiki,
  aiAgentAppAbilityRelearn,
  sendShareAccessMessage,
  getAbstractTemplateList,
  getAbstractFields
}