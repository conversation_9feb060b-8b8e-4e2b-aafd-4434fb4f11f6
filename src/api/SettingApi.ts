import CustomerTag from '@model/entity/CustomerTag';
import Tenant from '@model/entity/Tenant';
import WorkMsgTag from '@model/entity/WorkMsgTag';
import MsgModel from '@model/MsgModel';
import { GetOfflineSettingsModel, SetOfflineSettingsModel } from '@model/param/in/Setting';
import { GetOfflineSettingsResult } from '@model/param/out/Setting';
import Result from '@model/Result';
import http from '@src/util/http';
let fixedPrefixTaskPath = '/api/task';

/** 
 * 获取版本号  
 * 1为老版本(vip版) 2基础版本 3企业版本
*/
export function getSettingEdition(params = {}) {
  return http.get('/setting/edition', params)
}

/** 
 * 获取灰度权限
*/
export function getGrayAuth(params = {}) {
  return http.get('/setting/getGrayFunctionInfo', params)
}

// 获取自定义报表灰度接口
export function getGrayCustomReport(params = {}){
	return http.get('/api/dataware/outside/user/getGrayCustomReport', params)
}

/** 
 * 获取多语言列表
*/
export function getLanguages(params = {}) {
  return http.get('/api/workbench/outside/form/getLanguages', params)
}

/** 
 * 获取基础版权限
*/
export function getBasicEditionAuth(params = {}) {
  return http.get('/setting/find/control', params)
}

/** 
 * 获取企业列表（切换企业）
*/
export function getSwitchingCompanies(params = {}) {
  return http.get('/api/user/outside/multi/is', params)
}

/** 
 * 切换企业
*/
export function switchingCompanies(params = {}) {
  return http.get('/api/user/outside/multi/switch', params, false)
}

/** 
 * 获取一键拉群按钮权限
*/
export function getUserCardConfig(params = {}) {
  return http.get('/api/user/outside/tenant/getUserCardConfig', params)
}

/******************** S 商品列表 ***************/

/**
 * 商品备件列表
 */
export const getShopSparepartRepertory =  (params = {}) => {
  return http.get('/api/part/outside/part/service/api/getShopSparepartRepertory', params)
}
/**
 * 
*/
export const setSpartPartExternalUrl = (params = {}) => {
  return http.post('/api/part/outside/part/service/api/setSpartPartExternalUrl',params);
}

/**
 * 判断工单编号的编号规则是否重复
 * 
*/
export const isTaskNoRepeat = (params = {}) => {
  return http.post('/setting/taskType/rule/check',params);
}

/**
 * 是否启用橱窗
 */
export const updateIsShowOrIsShopWindows = (params = {}) => {
  return http.post('/api/part/outside/part/service/api/updateIsShowOrIsShopWindows', params, false)
}

/**
 * 商品服务列表
 */
export const serviceList = (params = {}) => {
  return http.get('/setting/market/service/list', params)
}

/**
 * 是否发布
 */
export const marketItem = (params = {}) => {
  return http.post('/setting/marketItem/save', params, false)
} 

/**
 * 获取备件设置
 */
export const sparepartConfig = () => {
  return http.post('/partV2/repertory/sparepartConfig')
}

/**
 * 获取仓库列表
 */
export const allRepertory = () => {
  return http.get('/partV2/repertory/allRepertory')
} 

/******************** E 商品列表 ***************/

/** 
 * 获取用户状态
*/
export function getStateColorMap() {
  return http.get('/setting/getStateColorMap')
}

/** 
 * 获取用户列表
*/
export function getSettingUserList(params: {} | undefined) {
  return http.get('/setting/user/list', params)
}

/** 
 * 获取角色列表
*/
export function getSettingRoleList(params: {} | undefined) {
  return http.get('/setting/role/list', params)
}

/** 
 * 获取工单类型列表
*/
export function getSettingTaskTypeList(params: {} | undefined) {
  return http.get('/setting/taskType/list', params)
}

/** 
 * 获取工单类型字段 不含被删除的系统字段
*/
export function getSettingTaskTypeEnabledFields(params: {} | undefined) {
  return http.get('/setting/taskType/getEnabledFields', params)
}

/** 
 * 获取租户下当前工单类型的所有表单显示类型
*/
export function getFieldType(params: {} | undefined) {
  return http.get('/setting/getFieldType', params)
}

/** 
 * 新建分配规则
*/
export function saveSettingDispatchRule(params: {} | undefined) {
  return http.post('/setting/dispatchRule/task/save', params)
}
/** 
 * 
*/
export function getSettingUserSeeAllOrg(params: {} | undefined) {
  return http.post('/setting/user/getSeeAllOrg', params)
}

/**
 * 班次列表
 */
export function getWorkNameList() {
	return http.post(`/api/calendar/outside/calendar/scheduling/work/name/getList`);
}

/**
 * 工作状态
 */
 export function getWorkStatusList() {
	return http.get(`/setting/workTime`);
}

/** 
 * 部门列表
*/
export function getSecurityTagTree(params: {} | undefined) {
  return http.post('/security/tag/tree', params)
}

/** 
 * @description 检测版本某些功能数量是否超过限制
*/
export function checkNumExceedLimit(params: {} | undefined) {
  return http.post('/setting/checkDataCount', params)
}

/** 
 * 全局设置信息
*/
export function getTenantConfigData(params: {} | undefined) {
  return http.get('/setting/getTenantConfig', params)
}
/******************** S 工单设置 ***************/
/**
 * 获取异常原因 & 工单范围设置
 */
export function getTaskExceptionConfig(params: {} | undefined) {
  return http.post('/setting/task/taskSet/getTaskExceptionConfig', params)
}
/**
 * 保存异常原因 & 工单范围设置
 */
export function saveExceptionTaskReason(params: {} | undefined) {
  return http.post('/setting/task/taskSet/saveExceptionTaskReason', params)
}
/**
 * 异常节点启用/禁用
 */
export function saveExceptionTaskSwitch(params: {} | undefined) {
  return http.post('/setting/task/taskSet/saveExceptionTaskSwitch', params)
}
/**
 * 工单设置启用/禁用
 */
export function saveTaskAct(params: {} | undefined) {
  return http.post('/setting/task/taskAct/save', params, false)
}
/**
 * 保存工单池相关设置 启用/禁用
 */
export function saveTaskPoolSetting(params: {} | undefined) {
  return http.post('/setting/taskPool/saveSetting', params, false)
}
/**
 * 保存单日最大接单次数设置
 */
export function saveSingleDayMaxOrderNum(params: {} | undefined) {
  return http.post('/setting/task/singleDayMaxOrderNum', params, false)
}
/**
 * 工单池接单前隐藏部分字段功能 启用/禁用
 */
export function savetaskPoolEncrption(params: {} | undefined) {
  return http.post('/setting/taskPool/encrption/save', params)
}
/**
 * 获取工单池接单前隐藏部分字段列表
 */
export function getTaskPoolEncrptionFind(params: {} | undefined) {
  return http.post('/setting/taskPool/encrption/find', params)
}
/**
 * 工单池接单前隐藏部分字段-获取当前工单类型所有字段信息
 */
export function getTaskPoolEncrptionList(params: {} | undefined) {
  return http.post('/setting/taskPool/encrption/list', params)
}
/**
 * 工单池接单前隐藏部分字段-保存隐藏部分字段设置
 */
export function saveTaskPoolEncrptionFile(params: {} | undefined) {
  return http.post('/setting/taskPool/encrption/save/file', params)
}
/**
 * 派单设置-保存指派工单负责人时不显示的用户状态
 */
export function saveTaskUserState(params: {} | undefined) {
  return http.post('/setting/task/taskSet/saveState', params, false)
}
/**
 * 派单设置-保存隐藏人员位置信息设置
 */
export function saveAllotExclude(params: {} | undefined) {
  return http.post('/setting/task/taskSet/saveAllotExclude', params, false)
}
/**
 * 派单设置-删除工单分配规则
 */
 export function delDispatchRule(params: {} | undefined) {
  return http.post('/setting/dispatchRule/task/delete', params,false)
}
/**
 * 派单设置-修改工单分配规则顺序
 */
 export function sortDispatchRule(params: {} | undefined) {
  return http.post('/setting/dispatchRule/task/saveSort', params)
}
/**
 * 派单设置-当前工单分配规则启用/禁用
 */
 export function enableDispatchRule(params: {} | undefined) {
  return http.post('/setting/dispatchRule/task/enable', params, false)
}
/**
 * 派单设置-获取当前工单分配规则信息
 */
 export function getOneDispatchRule(params: {} | undefined) {
  return http.get('/setting/dispatchRule/task/getOne', params)
}
/**
 * 派单设置-修改获取当前工单分配规则信息
 */
 export function updateSettingDispatchRule(params: {} | undefined) {
  return http.post('/setting/dispatchRule/task/update', params)
}

/**
 * 工单编辑设置-设置以下工单状态下工单不可编辑
 */
 export function saveTaskUpdate(params: {} | undefined) {
  return http.post('/setting/task/taskSet/saveTaskUpdate', params)
}
/**
 * 工单编辑设置-设置以下工单状态下工单不可编辑
 */
 export function saveTaskReceipt(params: {} | undefined) {
  return http.post('/setting/task/taskReceipt/save', params,false)
}
/**
 * 保存工单结算相关设置 启用/禁用
 */
 export function saveTaskBalanceConfig(params: {} | undefined) {
  return http.post('/setting/task/taskBalance/configSave', params,false)
}
/**
 * 保存工单结算相关设置 启用/禁用
 */
 export function saveTaskBalance(params: {} | undefined) {
  return http.post('/setting/task/taskBalance/save', params,false)
}
/**
 * 保存工单备注管理相关设置 启用/禁用
 */
 export function saveTaskDdmessage(params: {} | undefined) {
  return http.post('/setting/message/ddmessage/save', params,false)
}
/**
 * 编辑工单备注模板
 */
 export function updateRemarkContent(params: {} | undefined) {
  return http.post('/setting/remark/content/update', params)
}
/**
 * 新建工单备注模板
 */
 export function createRemarkContent(params: {} | undefined) {
  return http.post('/setting/remark/create', params)
}

/**
 * 工单备注模板交换位置
 */
 export function remarkPositionExchange(params: {} | undefined) {
  return http.post('/setting/remark/position/exchange', params,false)
}
/**
 * 工单备注模板交换位置
 */
 export function deleteTaskRemark(params: {} | undefined) {
  return http.post('/setting/remark/delete', params,false)
}

/**
 * 工单和事件备注模板交换位置
 */
 export function taskEventremarkExchange(params: {} | undefined) {
  return http.post('/setting/remark/resort', params)
}
/**
 * 通过计划任务创建周期性工单 启用计划任务 启用/禁用
 */
 export function savePlanTask(params: {} | undefined) {
  return http.post('/setting/planTask/saveConfig', params)
}

/**
 * 人员列表
 */
 export function getAllotUserList() {
  return http.get('/setting/task/getAllotUserList')
}

/**
 * 异常提醒设置消息提醒以下人员
 */
 export function getErrorAllotUserList(params: {} | undefined) {
  return http.get('/setting/task/getAllotUserList/page',params)
}
/**
 * 分配规则列表
 */
 export function getTaskDispatchRules() {
  return http.get('/setting/task/getDispatchRules')
}
/**
 * 工单结算自定义字段列表
 */
 export function getTaskBalanceFields(paams: any) {
  return http.get('/setting/task/getBalanceFields')
}
/**
 * 工单结算自定义字段列表
 */
 export function getTaskRemarkList(paams: any) {
  return http.get('/setting/task/getRemarkList')
}
/**
 * 工单分享设置
 */
export function saveTaskShare(params: {}) {
  return http.post('/api/task/outside/pc/setting/share/config/save', params)
}
/**
 * 查询工单分享设置
 */
export function getTaskShare() {
  return http.get('/api/task/outside/pc/setting/share/config/get')
}
/******************** E 工单设置 ***************/

/******************** S 工单类型设置 ***************/

/**
 * 查询行业模板库记录
 */
export function getSysTaskTypeList() {
  return http.post('/setting/taskType/getSysList');
}

/**
 * 工单类型排序
 * 
 * @param {object} params 工单类型排序
 */
export function taskTypeOrder(params: {} | undefined) {
  return http.post('/api/task/outside/task/type/update/order', params);
}


/**
 * 启用/关闭工单类型
 * 
 * @param {string} params.id 工单类型id
 * @param {number} params.enabled 工单类型开启/关闭
 */
export function taskTypeEnable(params: {} | undefined) {
  return http.post('/setting/taskType/enable', params, false);
}

/**
 * 删除工单类型
 * 
 * @param {string} params.typeId 工单类型id  
 */
export function delTaskType(params: {} | undefined) {
  return http.post('/setting/taskType/delete', params, false);
}

/**
 * 更新工单类型可用团队
 * 
 * @param {string} params.id 工单类型id  
 * @param {string} params.tagIds 可用团队id (英文逗号分割)
 */
export function changeTags(params: {} | undefined) {
  return http.post('/setting/taskType/changeTags', params, false);
}

/**
 * 查询满意度方案下拉列表
 * 
 * @param {string} params.id 工单类型id  
 * @param {string} params.tagIds 可用团队id (英文逗号分割)
 */
 export function getUsedConfigList(params: {} | undefined) {
  return http.get('/api/customer/outside/satisfactionConfig/getUsedConfigList', params);
}
/**
 * 获取当前满意度方案的题目信息
 * 
 * @param {string} params.satisfactionId 满意度方案id  
 */
export function getSatisfactisonQuestions(params: {} | undefined) {
  return http.get('/api/customer/outside/satisfactionConfig/getSatisfactisonQuestions', params);
}
/**
 * 创建工单类型
 * 
 * @param {string} params.typeName 工单类型名称
 * @param {string} params.templateId 模板id  
 * @param {string} params.color 颜色
 */
export function createTaskType(params: {} | undefined) {
  return http.post('/setting/taskType/create', params, false);
}

/**
 * 创建工单类型 (新版)
 * 
 * @param {string} params.typeName 工单类型名称
 * @param {string} params.templateId 模板id  
 * @param {string} params.color 颜色
 * @param {string} params.nameLanguageMap 国际化
 */
export function createInitTaskType(params: {} | undefined) {
  return http.post('/setting/taskType/init/create/new', params);
}

/**
 * 根据行业模板创建工单类型
 * 
 * @param {string} params.taskTypeId 行业模板id
 * @param {string} params.taskTypeName 工单类型名称  
 * @param {string} params.color 颜色
 */
export function importTaskType(params: {} | undefined) {
  return http.post('/setting/taskType/import', params, false);
}

/**
 * 启用或禁用工单流程
 * 
 * @param {string} params.id 工单类型id  
 * @param {boolean} params.status 状态 
 * @param {string} params.flowName 流程 
 */
export function flowSwitchTaskType(params: {} | undefined) {
  return http.post('/setting/taskType/flowSwitch', params, false);
}

/**
 * 流程设置保存
 * 
 * @param {string} params （yapi: http://30.40.61.216:3000/project/59/interface/api/14327）
 */
export function saveProcess(params: {} | undefined) {
  return http.post('/setting/saveProcess', params);
}

/**
 * 高级设置页保存
 * 
 * @param {string} params.templateId 工单类型id
 * @param {string} params.saveOptionFormList 设置服务报告和打印功能	  
 * @param {string} params.typeConfigForms 水印设置和位置异常提示设置	
 */
export function advancedSetting(params: {} | undefined) {
  return http.post('/setting/taskType/advancedSetting', params);
}

/**
 * 获取工单类型设置列表 （包含最大可设置的工单数、所有团队列表）
 */
export function getTaskTypeManage(params: {} | undefined) {
  return http.get('/setting/taskType/getTaskTypeManage', params);
}

/**
 * 获取工单表单、回执表单中必填的人员字段
 * 
 * @param {String} id 工单类型id 
 */
export function getFromUser(id: any) {
  return http.get(`/setting/getFromUser/${id}`);
}

/******************** E 工单类型设置 ***************/
/**
 * 确认计划时间
 * 
 * @param {string} params （yapi: http://30.40.61.216:3000/project/59/interface/api/18387）
 * @param {string} params.planTimeTypeCode 计划时间类型 1计划开始时间 2计划完成时间  
 */
export function postSettingPlanTime(params: {} | undefined) {
  return http.post(`${fixedPrefixTaskPath}/outside/pc/setting/plan/time/change`,params);
  //return http.post('/outside/pc/setting/plan/time/change', params);
}
/**
 * 判断是否在日历
 * 
 * @param {string} params （yapi: http://30.40.61.216:3000/project/59/interface/api/18384）
 */
 export function getSettingCalendar() {
  return http.get('/setting/check/calendar');
  // return {
  //   data:{
  //     status:2
  //   }
  // }
}

/**
 * 判断首页是否需要弹出弹窗
 * @param {string} params （setting/check/calendar/user）
 */
 export function getCalendarUser() {
  return http.get('/setting/check/calendar/user');
}

/** ****************** S 服务台设置 ***************/

/**
 * 获得服务台设置开关状态
 */
export function getServiceStationConfig(params: {} | undefined) {
  return http.get('/setting/getServiceStationConfig', params);
}


/**
 * 获得服务台事件分配规则列表数据
 */
export function getEventDispatchRules(params: {} | undefined) {
  return http.get('/setting/event/getDispatchRules', params);
}

/**
 * 获得服务台待分配订阅管理数据
 */
export function getAllotSubList(params: {} | undefined) {
  return http.get('/setting/getAllotSubList', params);
}

/**
 * 获得服务台事件备注模板开关状态
 */
export function getMessageConfig(params: {} | undefined) {
  return http.get('/setting/getMessageConfig', params);
}

/**
 * 获得服务台事件备注模板列表数据
 */
export function getEventRemarkList(params: {} | undefined) {
  return http.get('/setting/event/getRemarkList', params);
}

/**
 * 服务台设置开关状态存储
 * @param {String} flow 
 * @param {Boolean} state 开关状态
 */
export function saveSwitchState(params: {} | undefined) {
  return http.post('/setting/serviceStation/save', params, false);
}

/**
 * 查询事件类型
 * @param {String} keyword 查询关键字
 * @param {Number} pageNum 查询页码
 */
export function getEventTypeList(params: {} | undefined) {
  return http.get('/setting/eventType/list', params);
}

/**
 * 查询应用条件字段
 * @param {String} templateId 事件类型id
 * @param {Number} tableName 
 */
export function getEventTemplateFields(params: {} | undefined) {
  return http.get('/setting/getEventTemplateFields', params);
}

/**
 * 查询服务台待分配管理人员列表
 * @param {String} keyword 查询关键字
 * @param {Number} pageNum 查询页码
 */
export function getAllotUsersList(params: {} | undefined) {
  return http.get('/event/user/list', params);
}

/**
 * 查询服务台分配规则部门标签
 */
export function getTagList(params: {} | undefined) {
  return http.post('/security/tag/tree', params);
}

/**
 * 查询服务台待分配管理人员列表
 * @param {String} keyword 查询关键字
 * @param {Number} pageNum 查询页码
 */
export function getRoleList(params: {} | undefined) {
  return http.get('/setting/role/list', params);
}

/**
 * 新建分配规则
 */
export function saveRlue(params: {} | undefined) {
  return http.post('/setting/dispatchRule/event/save', params);
}

/**
 * 编辑分配规则
 */
export function editRlue(params: {} | undefined) {
  return http.post('/setting/dispatchRule/event/update', params);
}

/**
 * 删除分配规则
 * @param {String} id
 */
export function deleteRlue(params: {} | undefined) {
  return http.post('/setting/dispatchRule/event/delete', params, false);
}

/**
 * 更改分配规则状态
 * @param {String} id
 * @param {Number} enabled
 */
export function saveRuleState(params: {} | undefined) {
  return http.post('/setting/dispatchRule/event/enable', params, false);
}

/**
 *获得要编辑的分配规则数据
 * @param {String} id
 */
export function getRlueOne(params: {} | undefined) {
  return http.get('/setting/dispatchRule/event/getOne', params);
}

/**
 * 交换分配规则表格位置
 */
export function exchangeRule(params: {} | undefined) {
  return http.post('/setting/dispatchRule/event/saveSort', params);
}

/**
 * 服务台待分配订阅管理 保存
 * @param {String} ids 用户id
 * @param {String} model 
 */
export function saveAllotUsers(params: {} | undefined) {
  return http.post('/setting/message/subManage/save', params, false);
}

/**
 * 服务台事件备注模板开关状态存储
 * @param {String} message 
 * @param {Boolean} state 开关状态
 */
export function saveEventRemarkState(params: {} | undefined) {
  return http.post('/setting/message/ddmessage/save', params, false);
}

/**
 * 新建事件备注模板
 * @param {String} content 内容 
 * @param {String} module
 */
export function createRemark(params: {} | undefined) {
  return http.post('/setting/remark/create', params);
}

/**
 * 删除事件备注模板
 * @param {String} id 
 */
export function deleteEventRemark(params: {} | undefined) {
  return http.post('/setting/remark/delete', params, false);
}

/**
 * 编辑事件备注模板
 * @param {String} id 
 * @param {String} content 内容 
 * @param {String} module
 */
export function updateRemark(params: {} | undefined) {
  return http.post('/setting/remark/content/update', params);
}

/**
 * 获得编辑事件备注模板数据
 * @param {String} id 
 */
export function getRemarkData(params: {} | undefined) {
  return http.post('/setting/remark/detail', params, false);
}

/**
 * 交换事件备注模板表格位置
 * @param {String} id1
 * @param {String} id2 
 */
export function exchangeRemark(params: {} | undefined) {
  return http.post('/setting/remark/position/exchange', params, false);
}

/** ****************** E 服务台设置 ***************/


/** ****************** S 服务项目设置 ***************/

/**
 * 查询服务项目单位设置
 */
export function getServiceUnits() {
  return http.get('/setting/getServiceUnits');
}

/**
 * 查询服务项目类别设置
 */
export function getServiceTypes() {
  return http.get('/setting/getServiceTypes');
}

/**
 * 查询服务项目编号设置开关状态
 */
export function getNumberDuplicate() {
  return http.get('/setting/getNumberDuplicate');
}

/**
 * 新增和删除服务项目单位
 * @param {Array} data
 */
export function saveServiceUnit(params: {} | undefined) {
  return http.post('/setting/cost/serviceUnit/save', params, false, {
    headers: {
      indices: false
    }
  });
}

/**
 * 编辑服务项目单位
 * @param {Array} data
 * @param {String} oldVal
 * @param {String} newVal
 */
export function editServiceUnit(params: {} | undefined) {
  return http.post('/setting/cost/serviceUnit/update', 
    params, false, { 
      headers: { 
        indices: false 
      }
    }
  );
}

/**
 * 新增和删除服务项目类别
 * @param {Array} data
 */
export function saveServiceItemsType(params: {} | undefined) {
  return http.post('/setting/cost/serviceItemsType/save', params, false, {
    headers: {
      indices: false
    }
  });
}

/**
 * 编辑服务项目类别
 * @param {Array} data
 * @param {String} oldVal
 * @param {String} newVal
 */
export function editServiceItemsType(params: {} | undefined) {
  return http.post('/setting/cost/serviceItemsType/update',
    params, false, {
      headers: {
        indices: false
      }
    }
  );
}

/**
 * 更改服务项目编号设置开关状态
 * @param {String} name 
 * @param {Boolean} state 开关状态
 */
export function saveNumberDuplicateState(params: {} | undefined) {
  return http.post('/setting/serviceItem/saveSetting', params, false);
}

/** ****************** E 服务项目设置 ***************/

/******************** S 备件库管理设置 ***************/
/**
 * 保存备件库设置
 */
 export function saveSettingSparepart2(params: {} | undefined) {
  return http.post('/setting/sparepart2/save', params,false);
}

/**
 * 备件设置列表
 */
 export function getRepertoryAllRepertory(params: {} | undefined) {
  return http.post('/api/part/outside/dd/part/repertory/getAllRepertory', params);
}
/**
 * 删除备件设置
 */
 export function delRepertoryRepertory(id: string) {
  return http.get('/setting/sparepart2/repertory/delete/'+id);
}
/**
 * 新增备件设置
 */
 export function saveSparepartRepertory(params: {} | undefined) {
  return http.post('/setting/sparepart2/repertory/save', params);
}
/**
 * 修改备件设置
 */
 export function updateSparepartRepertory(params: {} | undefined) {
  return http.post('/setting/sparepart2/repertory/update', params);
}

/**
 * 仓库管理员可选人员列表
 */
 export function getCandidateManagers(params: {} | undefined) {
  return http.get('/partV2/category/candidate/managers', params);
}
/**
 * 仓库管理员可选人员列表
 */
 export function getRepertoryData(id: string) {
  return http.get('/setting/sparepart2/repertory/get/'+id);
}
/******************** E 备件库管理设置 ***************/

/******************** S 备件选项设置 ***************/
/**
 * 保存入库类型
 */
export function saveSparepartInstock(params: {} | undefined) {
  return http.post('/setting/sparepart/instock/save', params,false);
}
/**
 * 保存出库类型
 */
export function saveSparepartOutstock(params: {} | undefined) {
  return http.post('/setting/sparepart/outstock/save', params,false);
}
/**
 * 保存备件单位
 */
export function saveSparepartUnit(params: {} | undefined) {
  return http.post('/setting/sparepart/unit/save', params,false);
}
/**
 * 保存备件类别选项
 */
export function saveSparepartType(params: {} | undefined) {
  return http.post('/setting/sparepart/type/save', params,false);
}

/**
 * 编辑入库类型 oldVal,newVal,data
 */
 export function updateSparepartInstock(params: {} | undefined) {
  return http.post('/setting/sparepart/instock/updateInStock', params,false);
}
/**
 * 编辑出库类型
 */
export function updateSparepartOutstock(params: {} | undefined) {
  return http.post('/setting/sparepart/outstock/updateOutStock', params,false);
}
/**
 * 编辑备件单位
 */
export function updateSparepartUnit(params: {} | undefined) {
  return http.post('/setting/sparepart/unit/updateUnit', params,false);
}
/**
 * 编辑备件类别选项
 */
export function updateSparepartType(params: {} | undefined) {
  return http.post('/setting/sparepart/type/update', params,false);
}
/**
 * 备件出入库单位设置
 */
export function saveSparepartPrecision(params: {} | undefined) {
  return http.post('/setting/sparepart/precision/save', params,false);
}

/******************** E 备件选项设置 ***************/
/**
 * 获取日历配置时间
 *
 */
 export function getAgendaConfig() {
  return http.post('/api/calendar/agendaConfig/get' );
}


/**
 * 日历时间配置更新
 * @param {string} params.minute 设置时间  
 */
 export function updateAgendaConfig(params: {} | undefined) {
  return http.post('/api/calendar/agendaConfig/update', params, false);
}

/******************** start 客户设置 ***************/
export function getCustomerTagList(): Promise<Result<CustomerTag[]>> {
  return http.post('/api/customer/outside/tenant/tag/list');
}

export function setCustomerTagList(params: { tags: CustomerTag[] }): Promise<Result<string[]>> {
  return http.post('/api/customer/outside/tenant/tag/update', params);
}
/******************** end 客户设置 ***************/

/******************** start 租户 ***************/
export function getTenant(): Promise<Result<Tenant>> {
  return http.get('/setting/getTenant')
}
/******************** end 租户 ***************/

/******************** start 工作信息维护标签 ***************/
export function getBaseLabelList() {
  return http.get('/setting/getBaseLabelList');
}

export function editBaseLabel(params: { tags: WorkMsgTag[] }) {
  return http.post('/setting/editBaseLabel', params);
}
/******************** end 工作信息维护标签 ***************/

/** ****************** S 知识库设置 ***************/

/**
 * 获得知识库设置
 */
export function getWikiConfig() {
  return http.get('/setting/getWikiConfig');
}

/**
 * 获得所有站外搜索配置数据
 * @param {String} id 
 */
export function getAllWikiOutsideSearch(params: {} | undefined) {
  return http.get('/api/search/outside/wiki/outsideSearch/getAllWikiOutsideSearch', params, false);
}

/**
 * 新建站外搜索配置数据
 * @param {String} name 名称
 * @param {String} link 链接
 */
export function createWikiOutsideSearch(params: {} | undefined) {
  return http.post('/api/search/outside/wiki/outsideSearch/createWikiOutsideSearch', params);
}

/**
 * 编辑站外搜索配置数据
 * @param {String} id 
 * @param {String} name 名称
 * @param {String} link 链接
 */
export function updateWikiOutsideSearch(params: {} | undefined) {
  return http.post('/api/search/outside/wiki/outsideSearch/updateWikiOutsideSearch', params);
}

/**
 * 删除站外搜索配置数据
 * @param {String} id 
 * @param {String} name 名称
 * @param {String} link 链接
 */
export function deleteWikiOutsideSearch(params: {} | undefined) {
  return http.get('/api/search/outside/wiki/outsideSearch/deleteWikiOutsideSearch', params);
}

/**
 * 移动站外搜索配置数据
 * @param {String} id
 * @param {String} operation 移动方向
 */
export function moveWikiOutsideSearch(params: {} | undefined) {
  return http.get('/api/search/outside/wiki/outsideSearch/moveWikiOutsideSearch', params);
}

/**
 * 知识库审批人保存
 * @param {String} userIds
 */
export function wikiApproversSave(params: {} | undefined) {
  return http.post('/setting/wiki/approvers/save', params, false);
}

/**
 * 查询知识库审批人员可选列表
 * @param {String} keyword 查询关键字
 * @param {Number} pageNum 查询页码
 */
export function getApproverList(params: {} | undefined) {
  return http.get('/wiki/approver/list', params);
}

/**
 * 知识库开关状态存储
 * @param {String} message 
 * @param {Boolean} state 开关状态
 */
export function saveWikiSetting(params: {} | undefined) {
  return http.post('/setting/wiki/save', params, false);
}

/**
 * 知识库站外搜索开关状态
 * @param {String} message 
 * @param {Boolean} state 开关状态
 */
export function switchWikiOutsideSearch(params: {} | undefined) {
  return http.get('/api/search/outside/wiki/outsideSearch/switchWikiOutsideSearch', params, false);
}

/** ****************** E 知识库设置 ***************/

/** 
 * 企业版功能试用发送验证码
*/
export function settingTrailSendSmsCode(params: Record<string, any>): Promise<MsgModel<any>> {
  return http.post('/setting/trial/sendSmsCode', params)
}

/** 
 * 企业版功能开启试用
*/
export function settingTrailApply(params: Record<string, any>): Promise<MsgModel<any>> {
  return http.post('/setting/trial/apply', params)
}

// 获取 im initData
export async function getImConfig() {
	const res = await http.get(`/setting/im/getConfig`);
	if (!res.succ || res.status !== 0) return Promise.reject(res);
	return res.data;
}

/** 
 * 在线支付列表-确认支付
*/
export function confirmPay(params: Record<string, any>): Promise<MsgModel<any>> {
  return http.post('/api/paycenter/outside/pay/confirmPay', params)
}

// 获取 坐班灰度
export async function getSchedulingConfig() {
  const res = await http.get('/calendar/check/work/fixed');
  if (!res.succ || res.status !== 0) return Promise.reject(res);
  return res.data;
}
/**
 * 是否开启备件替换产品附加组件灰度
 *
 */
export function getReplaceProductCard(params: {} | undefined) {
  return http.get('/setting/getReplaceProductCard', params);
}


// 获取结算管理灰度
export async function getSettleConfig() {
  const res = await http.get('/api/app/outside/settle/config/checkGray');
  if (!res.success || res.code !== 0) return Promise.reject(res);
  return res.result;
}

// 获取版本和相关模块是否隐藏
export function findControl(params = {}) {
  return http.get('/setting/find/control', params)
}

// 订阅管理信息
export function getSubManageInfo(params: {} | undefined) {
  return http.get('/setting/message/getSubManage', params);
}

// 订阅管理保存
export function saveSubManage(params: {} | undefined) {
  return http.post('/setting/message/subManage/save', params, false);
}

// 设置发送时间
export function setReportSendTime(params: {} | undefined) {
  return http.post('/setting/setReportSendTime', params, false);
}

// 短信消息设置
export function getSmsMessageInfo(params: {} | undefined) {
  return http.get('/setting/message/getSmsMessage', params);
}

// 短信消息切换应用范围
export function changeTypes(params: {} | undefined) {
  return http.post('/setting/smsmessage/changeTypes', params, false);
}

// 短信消息设置保存
export function saveSmsMessage(params: {} | undefined) {
  return http.post('/setting/message/smsmessage/save', params, false);
}

// 短信消息设置保存计划提醒时间
export function saveSetting(params: {} | undefined) {
  return http.post('/setting/taskPlanTime/saveSetting', params, false);
}

// 短信消息设置保存客服电话
export function saveTel(params: {} | undefined) {
  return http.post('/setting/message/smsmessage/savetel', params, false);
}

// 获取语音外呼用量
export function callingUsage(params: {} | undefined) {
  return http.get('/api/message/outside/calling/usage', params, false);
}

// 渠道内获取套餐领取状态
export function messageQuery(params: {} | undefined) {
  return http.get('/api/workbench/outside/activity/message/query', params, false);
}
// 领取套餐
export function messageTake(params: {} | undefined) {
  return http.post('/api/workbench/outside/activity/message/take', params);
}

// 获取充值电话套餐
export function getCommodityPackage(params: {} | undefined) {
  return http.get('/api/paycenter/outside/pay/commodity/getCommodityPackage', params, false);
}
// 订阅消息设置保存
export function saveDailyReportSetting(params: {} | undefined) {
  return http.post('/setting/message/subManage/saveDailyReportSetting', params, true);
}

// 获取消息设置配置
export function getDDMessageConfig(params = {}) {
  return http.get('/setting/message/ddmessage', params)
}

// 更新启用备件出入库审批
export function updateSparepartMessageConfig(params = {}) {
  return http.get('/setting/updateSparepartMessageConfig', params)
}

// 飞书审批开关保存
export function saveLarkApproveState(params: {} | undefined) {
  return http.post('/api/user/outside/tenant/addApprove', params, false);
}

// 里程计算基数选择
export function SettingMileageSave(params: [] | undefined) {
  return http.post('/setting/task/mileage/save', params, true);
}

// 配置新增
export function addTenantConfig(params: [] | undefined) {
  return http.post('/api/task/outside/config/addTenantConfig', params, true);
}

// 修改新增
export function updateTenantConfigById(params: [] | undefined) {
  return http.post('/api/task/outside/config/updateTenantConfigById', params, true);
}

// 查询配置
export function getTenantConfigByCodeList(params: [] | undefined) {
  return http.post('/api/task/outside/config/getTenantConfigByCodeList', params, true);
}

// 获取工单类型Item的detail
export function getTaskTypeItemDetail(params: {} | undefined) {
  return http.get('/task/taskType/tagRole', params, true);
}

// 工单时间类型的可用部门
export function changeEventOrTaskTags(params: {} | undefined) {
  return http.post('/setting/taskType/changeTags', params, true);
}

// 获取事件类型Item的detail
export function getServiceEventTypeItemDetail(params: {} | undefined){
  return http.get('/event/eventType/role', params, true);
}

// 充值接口
export function dingPurchases(params = {} ) {
    return http.get('/api/paycenter/outside/page/commodity/dingPurchases', params);
}

// 钉钉继续支付
export function continuePaying(params = {} ) {
    return http.get(`/api/paycenter/outside/page/dingPurchases/continuePaying`, params);
}

/**
 * @description 获取离线设置
 */
function getOfflineSettings(params?: GetOfflineSettingsModel): Promise<Result<GetOfflineSettingsResult>> {
  return http.get('/api/user/outside/tenant/getOfflineSettings', params) as Promise<Result<GetOfflineSettingsResult>>
}

/**
 * @description 获取离线设置
 */
function setOfflineSettings(params: SetOfflineSettingsModel): Promise<Result<null>> {
  return http.post('/api/user/outside/tenant/saveOfflineSettings', params) as Promise<Result<null>>
}

export function getSettingRemindFieldList(params = {}) {
  return http.get('/setting/remind/fieldInfo/list', params);
}

/**
 * @see https://yapi.shb.ltd/project/7989/interface/api/62906
 */
export function getSettlementPoolByModuleCheck (params: {} | undefined) {
	return http.get('/api/voice/outside/settlement/pool/getSettlementPoolByModuleCheck', params);
}

export {
  getOfflineSettings,
  setOfflineSettings
}