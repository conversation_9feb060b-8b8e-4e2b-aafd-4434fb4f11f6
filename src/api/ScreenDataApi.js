/**
 * 数据大屏相关接口
 */

import http from '@src/util/http';

let urlAppPrefix = '/api/app';
let dsPrefix = '/api/dataware/outside'

/**
 * 更新设置信息
 * @param {*} params 
 */
function saveSettingConfig(params) {
  return http.post('/setting/screen/save', params);
}

/**
 * 获取配置信息接口
 * 目前后台逻辑是需要全部配置文件，
 * 改动会造成A先改了配置，然后B在修改，B修改配置后会覆盖A的配置
 * 所以在请求保存/更新配置前 从这个接口拿一下当前配置信息 去做merge后提交
 */
function getSettingConfig() {
  return http.get('/stats/screenData/screenDataConfig')
}

/**
 * 新接口
 * @param {*} params 
 */
function getSettingConfigV2(params) {
  if (!params) {
    return http.get(`${dsPrefix}/setting/getScreenSetting`)
  }
  return http.get(`${dsPrefix}/setting/getScreenSetting/${params}`)
}

/**
 * 心跳检测请求接口 
 */
function refreshCacheTime() {
  return http.get(`${urlAppPrefix}/outside/screen/refreshCacheTime`);
}

/**
 * 获取数据大屏数据除地图面板之外的数据
 * 这个接口不需要参数，
 * @param {*} params 
 */
function getScreenGroupData(params, dataScrrenId) {
  // return http.get(`${urlAppPrefix}/outside/screen/getScreenData`);

  // 新建
  if (!dataScrrenId) {
    return http.get(`${urlAppPrefix}/outside/screen/getScreenData/${params}`);
  }
  // 联调特殊部署，todo clear
  return http.get(`${urlAppPrefix}/outside/screen/getScreenData/${params}/${dataScrrenId}`);
}

/**
 * 获取地图面板中 工单和人员 信息
 */
function getMapPanelTaskInfo(params) {
  return http.post(`${urlAppPrefix}/outside/screen/map/task`, params);
}

/**
 * 获取地图面板中 客户，团队，备件库 信息
 */
function getMapPanelCustomerInfo(params) {
  return http.post(`${urlAppPrefix}/outside/screen/map/customer`, params);
}

/**
 * 获取免登码，用于在钉钉外浏览器中显示 
 */
function getOpenWebCode() {
  return http.get('/getOpenWebCode');
}

function getTestMapData() {
  return http.get('stats/customer/list?tagId=');
}

function  getOldAll() {
  return http.get(`${dsPrefix}/setting/getOldAll`);
}
/**
 *  保存数据大屏配置
 */
function setOldConfig(params) {
  return http.post(`${dsPrefix}/setting/setOldConfig`, params);
}

/** 
 * @description  设置模版model（涉及新老版本切换）
 */
function setModel(params = {}) {
  return http.post(`${dsPrefix}/setting/setModel/${params}`)
}

/** 
* @description  获取模版model
*/
function getModel() {
  return http.get(`${dsPrefix}/setting/getModel`)
}
  
/**
 *  获取数据大屏配置
 */
 function getScreenInitData(params) {
  return http.get('/stats/screenData/screenDataView/init', params);
}

// 获取租户是否在灰度内
function getGrayFunction() {
  return http.get(`${dsPrefix}/user/getGrayAndEdition`);
}

/**
 * @description 获取数据大屏配置
 */
export function getScreenSetting(id) {
  return http.get(`${dsPrefix}/setting/getScreenSetting/${id}`)
}

/**
 * @description 保存数据大屏配置
 */
export function saveScreen(params = {}) {
  return http.post(`${dsPrefix}/setting/saveScreen`, params)
}

/**
 * @description 保存数据大屏缩略图
 */
export function saveScreenThumbnail(params = {}) {
  return http.post(`${dsPrefix}/setting/saveScreenThumbnail`, params)
}

export {
  getScreenInitData,
  saveSettingConfig,
  getSettingConfig,
  getSettingConfigV2,
  refreshCacheTime,
  getScreenGroupData,

  getMapPanelTaskInfo,
  getMapPanelCustomerInfo,

  getOpenWebCode,
  getTestMapData,
  getOldAll,
  setOldConfig,
  setModel,
  getModel,
  getGrayFunction
}

