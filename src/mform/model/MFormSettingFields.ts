/* model */
import Field from "@model/entity/Field";
/* util */
import { randomString } from 'pub-bbx-utils';

const getFormSettingDescriptionField = (isValidate: boolean): Field => {
  return {
    defaultValue: null,
    displayName: "描述",
    fieldName: "title",
    formType: "text",
    guideProfessions: [],
    id: randomString(),
    isDelete: 0,
    isGuideData: false,
    isNull: isValidate ? 0 : 1,
    isSearch: 0,
    isSystem: 0,
    orderId: 0,
    maxlength: 20,
    placeHolder: "最多20个字",
    setting: {},
  }
}

const getFormSettingTitleField = (maxlength: number, isValidate: boolean,): Field => {
  return {
    defaultValue: null,
    displayName: "标题",
    fieldName: "title",
    formType: "text",
    guideProfessions: [],
    id: randomString(),
    isDelete: 0,
    isGuideData: false,
    isNull: isValidate ? 0 : 1,
    isSearch: 0,
    isSystem: 0,
    orderId: 0,
    maxlength: maxlength,
    placeHolder: `最多${maxlength}个字`,
    setting: {},
  }
}

const getFormSettingImageTitleField = (displayName: string, isValidate: boolean, titleMaxlength: number): Field => {
  return {
    defaultValue: null,
    displayName,
    fieldName: "title",
    formType: "textarea",
    guideProfessions: [],
    id: randomString(),
    isDelete: 0,
    isGuideData: false,
    isNull: isValidate ? 0 : 1,
    isSearch: 0,
    isSystem: 0,
    orderId: 0,
    maxlength: titleMaxlength,
    placeHolder: `最多${titleMaxlength}个字，${isValidate ? '不' : ''}可为空`,
    setting: {},
  }
}

const getFormSettingImageLinkField = (displayName: string, isValidate: boolean): Field => {
  return {
    defaultValue: null,
    displayName,
    fieldName: "link",
    formType: "text",
    guideProfessions: [],
    id: randomString(),
    isDelete: 0,
    isGuideData: false,
    isNull: isValidate ? 0 : 1,
    isSearch: 0,
    isSystem: 0,
    orderId: 0,
    placeHolder: "",
    setting: {},
  }
}

const getFormSettingImageCustomLinkField = (isValidate: boolean): Field => {
  return {
    defaultValue: null,
    displayName: "自定义链接",
    fieldName: "customLink",
    formType: "text",
    guideProfessions: [],
    id: randomString(),
    isDelete: 0,
    isGuideData: false,
    isNull: isValidate ? 0 : 1,
    isSearch: 0,
    isSystem: 0,
    orderId: 0,
    placeHolder: "请输入以http/https开头的链接",
    setting: {},
  }
}

const getFormSettingCustomerServiceField = (isValidate: boolean): Field => {
  return {
    defaultValue: null,
    displayName: "客服联系通道",
    fieldName: "customerServiceName",
    formType: "text",
    guideProfessions: [],
    id: randomString(),
    isDelete: 0,
    isGuideData: false,
    isNull: isValidate ? 0 : 1,
    isSearch: 0,
    isSystem: 0,
    orderId: 0,
    placeHolder: "请选择客服联系通道",
    setting: {},
  }
}

const getFormSettingWechatServiceField = (isValidate: boolean): Field => {
  return {
    defaultValue: null,
    displayName: "微信客服",
    fieldName: "wechatServiceUrl",
    formType: "text",
    guideProfessions: [],
    id: randomString(),
    isDelete: 0,
    isGuideData: false,
    isNull: isValidate ? 0 : 1,
    isSearch: 0,
    isSystem: 0,
    orderId: 0,
    placeHolder: "请选择微信客服",
    setting: {},
  }
}

export {
  getFormSettingTitleField,
  getFormSettingImageTitleField,
  getFormSettingImageLinkField,
  getFormSettingImageCustomLinkField,
  getFormSettingDescriptionField,
  getFormSettingCustomerServiceField,
  getFormSettingWechatServiceField,
}

export default {
  getFormSettingTitleField,
  getFormSettingImageTitleField,
  getFormSettingImageLinkField,
  getFormSettingImageCustomLinkField,
  getFormSettingDescriptionField,
  getFormSettingCustomerServiceField,
  getFormSettingWechatServiceField,
}
