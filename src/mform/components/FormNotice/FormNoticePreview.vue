<template>
  <div class="form-preview-group">
    <i class="iconfont icon-notification-fill"></i>
    <van-notice-bar ref="NoticeBarComponent" :scrollable="content.length > 19">
      {{ content }}
    </van-notice-bar>
  </div>
</template>

<script>
/* props */
import { previewProps } from '@src/mform/components/props';
import { transformI18n } from '@src/locales/index.ts';
import { useLocaleInject } from '@hooks/useLocale'
import { useFormMultiLanguage } from '@hooks/useFormMultiLanguage'
import { defaultCNKey } from '@src/util/languages.js';
export default {
  name: 'mform-text-preview',
  props: previewProps,
  setup(){
    const { locale: formPreviewLocale } = useLocaleInject('formPreviewLocale');
    const { isOpenMultiLanguage } = useFormMultiLanguage();

    return {
      formPreviewLocale,
      isOpenMultiLanguage,
    };
  },
  data() {
    return {
      text: '请填写通知内容，内容过长时将会滚动显示',
    };
  },
  computed: {
    content() {
      // let str = this.field.setting?.content;
      // return str?.length > 19 ? str.substring(0, 19) : str || this.text;
      // if (this.formPreviewLocale === defaultCNKey) {
      //   return (
      //     this.field.setting?.contentLanguage?.[defaultCNKey]
      //     || this.field.setting?.content
      //     || this.text
      //   );
      // }

      // return (
        // transformI18n(this.field.setting?.contentLanguage, this.$i18n.locale)
      // );
      return (
        // transformI18n(this.field.setting?.contentLanguage, this.$i18n.locale)
        this.field.setting?.contentLanguage?.[this.formPreviewLocale]
          || this.field.setting?.contentLanguage?.[defaultCNKey]
          || this.field.setting?.content
          || this.text
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.form-preview-group {
  width: 100%;
  height: 56px;
  padding: 12px 9px 12px 11px;
  position: relative;
  .icon-notification-fill{
      position: absolute;
      left: 18px;
      top: 50%;
      transform: translateY(-50%);
      z-index: 999;
      color: #FF7A00;
    }
  .van-notice-bar {
    width: 100%;
    height: 100%;
    background: #FFEED4;
    background-size: 100% 100%;
    padding: 8px 30px 8px 30px;
    
    ::v-deep .van-notice-bar__content {
      font-size: 13px;
      color: #595959;
      left: 5px;
    }
  }
}
</style>
