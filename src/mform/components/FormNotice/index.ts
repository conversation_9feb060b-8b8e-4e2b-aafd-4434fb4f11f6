// @ts-nocheck
import FormTextSetting from './FormNoticeSetting.vue';
import FormTextPreview from './FormNoticePreview.vue';
/* util */
import { getSetting } from '@src/mform/util'
/* vue */
import Vue from 'vue';
/* config */
import * as config from '@src/mform/config';

let FormTextField = {
  formType: 'notice', // 字段类型
  name: '公告',
  isSystem: 0,
  number: 10,
  icon: 'icon-notification-fill',
  forceDelete: true,
  component: {
    setting: FormTextSetting,
    preview: FormTextPreview,
  },
  packFunction: pack,
  unPackFunction: unpack,
  validateFunction: validate,
};

export function unpack(field)  {
  let setting = getSetting(field.setting)
  
  let settingValue = {
    content: setting.noticeContent || setting.content,
    contentLanguage: setting.noticeContentLanguage || {}
  }
  
  Vue.set(field, 'setting', Object.assign(setting, settingValue))
}

export function pack(field) {
  let originSetting = getSetting(field.setting)
  
  const setting = {
    noticeContent: originSetting.content,
    noticeContentLanguage: originSetting.contentLanguage
  }
  
  Vue.set(field, 'setting', setting)
}

export function validate(field) {
  let message = []
  let setting = getSetting(field?.setting)
  let { content = '' } = setting
  
  if (!content) {
    message.push('公告内容不能为空')
  } else if (content.length > config.NOTICE_LENGTH) {
    message.push(`公告内容不能超过${config.NOTICE_LENGTH}字`)
  }
  
  return message
}

export default FormTextField;
