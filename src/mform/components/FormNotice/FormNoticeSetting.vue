<template>
  <div class="form-setting-panel form-notice-setting">
    <mform-setting-title title="公告">
    </mform-setting-title>
    
    <!-- 暂时不需要样式设置 -->
    <mform-item :label="contentField.displayName" class="form-setting-main">
      <form-textarea :field="contentField" :value="value.content" @input="updateContent"/>
      <!-- <div class="language-config" @click="openDialog" v-if="isOpenMultiLanguage">
        <i class="iconfont icon-earth"></i>
      </div> -->
      <div v-if="isOpenMultiLanguage">
         <base-select-language 
            :field="contentField"
            :defaultOption="{
              formType:'textarea',
            }"
            defaultFormType="textarea"
            :defaultValue="value.content"
            :defaultValueLanguage="value.contentLanguage"
            :isFill="false"
            @save="updateFields"
          >
          </base-select-language>
      </div>
    </mform-item>
  </div>
</template>

<script>
/* model */
import { FormNoticeSettingContentField, FormNoticeSettingTitleField, FormNoticeSettingDescriptionField } from '@src/mform/components/FormNotice/FormNoticeModel.ts'
/* mixin */
import SettingMixin from '@src/mform/mixin/setting'
/* props */
import { settingProps } from '@src/mform/components/props'
/* util */
import * as FormUtil from '@src/component/form/util'
import { findComponentsDownward } from '@src/util/assist'
import MultilingualDialog from '@src/component/compomentV2/MultilingualDialog/index.vue';

export default {
  name: 'mform-text-setting',
  mixins: [SettingMixin],
  props: settingProps,
  components: {
    MultilingualDialog,
  },
  data() {
    return {
      activeTab: 'content',
    }
  },
  computed: {
    contentField() {
      return FormNoticeSettingContentField
    },
    descriptionField() {
      return FormNoticeSettingDescriptionField
    },
    titleField() {
      return FormNoticeSettingTitleField
    },
    settingFields() {
      return [this.contentField, this.descriptionField, this.titleField]
    },
    value() {
      return {
        content: this.fieldSetting.content || '',
        contentLanguage: this.fieldSetting.contentLanguage || {},
      }
    }
  },
  methods: {
    switchTab(tab) {
      this.activeTab = tab
    },
    updateForDom(event) {
      let el = event.target;
      let prop = el.dataset.prop;
      let value = el.value;
      
      this.update(value, prop);
    },
    updateContent(value) {
      this.update(value, 'content');
      if (this.value.contentLanguage?.['zh'] !== this.value.content) {
        const _value = Object.assign({}, this.value.contentLanguage)
        _value['zh'] = this.value.content
        this.update(_value, 'contentLanguage')
      } 
    },
    validate() {
      const formItemComponents = findComponentsDownward(this, 'mform-item')
      formItemComponents.forEach(formItemComponent => {
        formItemComponent.validate && formItemComponent.validate()
      })
    },
    openDialog() {
      if (this.value.contentLanguage?.['zh'] !== this.value.content) {
        const _value = Object.assign({}, this.value.contentLanguage)
        _value['zh'] = this.value.content
        this.$refs.multilingualDialogRef.openDialog(_value);
      } else {
        this.$refs.multilingualDialogRef.openDialog(this.value.contentLanguage);
      }
    },
    updateFields(value) {
      this.updateContent(value['zh'])
      this.update(value, 'contentLanguage')
    }
  }
};
</script>
<style lang="scss">
.form-setting-main {
  .form-item-control-content {
    display: flex;
  }
}
</style>

<style lang="scss" scoped>
.language-config {
  width: 32px;
  height: 28px;
  text-align: center;
  line-height: 28px;
  background: #F5F8FA;
  border-radius: 4px;
  border: 1px solid #CBD6E2;
  cursor: pointer;
  margin-left: 10px;
}
</style>
