<template>
  <div class="form-preview-group" :class="previewMode == 'pc' ? 'pcMode' : ''">
    <MFormPreviewHeader
      :content="commonSettingValue"
      :remove-margin-bottom="true"
    />

    <template v-if="pictureInfo.length === 0">
      <div :class="`form-preview-${displayStyle}-content`">
        <div
          class="form-preview-card-default form-preview-card-box"
          v-for="item in 2"
          :key="item"
        >
          <div class="no-image">
            <i class="iconfont icon-image-fill"></i>
            <span>图片</span>
          </div>
          <div>这里显示标题</div>
        </div>
      </div>
    </template>
    <div v-else :class="[`form-preview-${displayStyle}-content`, pictureInfo.length > 1 && 'pcMode-flex']">
      <div
        class="form-preview-card-box"
        v-for="(item, index) in pictureInfo"
        :key="index"
      >
        <img v-if="item.imageUrl" :src="item.imageUrl" alt="" />
        <div v-else class="no-image">
          <i class="iconfont icon-image-fill"></i>
          <span>图片</span>
        </div>
        <div v-if="item.title">
          <span class="card-content">{{ item.title }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/* mixin */
import PreviewMixin from '@src/mform/mixin/preview';
/* props */
import { previewProps } from '@src/mform/components/props';

export default {
  name: 'mform-card-preview',
  mixins: [PreviewMixin],
  props: previewProps,
  computed: {},
};
</script>

<style lang="scss" scoped>
@mixin no-image {
  width: 100%;
  height: 193px;
  background: #f5f5f5;
  border-radius: 4px 4px 0px 0px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  i {
    font-size: 28px;
    color: #bfbfbf;
    margin-bottom: 14px;
  }

  span {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #595959;
    line-height: 22px;
  }
}

.form-preview-group {
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;

  &.pcMode {
    .form-preview-image-content, .form-preview-article-content {
      &.pcMode-flex {
        display: flex;
        flex-wrap: wrap;
        .form-preview-card-box {
          width: calc(50% - 8px);
          margin-bottom: 12px;
          &:last-child {
           padding-bottom: 12px;
          }
        }
      }
      .form-preview-card-box {
        &:nth-child(odd) {
          margin-right: 16px;
        }
      }
      .form-preview-card-box + .form-preview-card-box {
        margin-top: 0;
      }
    }

    .form-preview-image-content {
      &.pcMode-flex {
        .form-preview-card-box {
          img, .no-image {
            height: 139px;
          }
        }
      }
      .form-preview-card-box {
        div {
          .card-content {
            color: #595959;
          }
        }
      }
    }

    .form-preview-article-content {
      .form-preview-card-box {
        padding: 0 0 12px 0;
        &:last-child {
          border-bottom: 1px solid #e8e8e8;
        }
        img, .no-image {
          width: 60px;
          height: 60px;
        }
        div {
          .card-content {
            max-height: 72px;
            @include text-ellipsis-3();
          }
        }
      }
    }
  }

  .form-preview-image-content,
  .form-preview-fall-content,
  .form-preview-article-content {
    padding: 0 16px;
    margin: 12px 0;
  }

  .form-preview-image-content {
    width: 100%;
    height: auto;

    .form-preview-card-default {
      div:first-child {
        @include no-image;
      }

      div:last-child {
        width: 100%;
        padding: 10px 8px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #262626;
        background: #f7f7f7;
      }
    }

    .form-preview-card-default + .form-preview-card-default {
      margin-top: 16px;
    }

    .form-preview-card-box {
      width: 100%;
      height: auto;
      display: flex;
      flex-direction: column;

      img {
        width: 100%;
        height: 193px;
        border-radius: 8px;
      }

      .no-image {
        @include no-image;
      }

      div {
        width: 100%;
        padding: 10px 0px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #262626;

        .card-content {
          display: block;
          max-height: 36px;
          @include text-ellipsis-2();
        }
      }
    }

    .form-preview-card-box + .form-preview-card-box {
      margin-top: 16px;
    }
  }

  .form-preview-fall-content {
    width: 100%;
    height: auto;
    display: flex;
    flex-wrap: wrap;

    .form-preview-card-box {
      width: calc((100% - 8px) / 2);
      height: auto;
      margin-bottom: 8px;

      img {
        width: 100%;
        height: auto;
      }

      .no-image {
        @include no-image;
      }

      div {
        width: 100%;
        padding: 10px 8px;
        font-size: 13px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #262626;
        background: #f5f5f5;
        border-radius: 0px 0px 4px 4px;

        .card-content {
          display: block;
          max-height: 36px;
          overflow: hidden;
        }
      }
    }

    .form-preview-card-box:nth-child(odd) {
      margin-right: 8px;
    }
  }

  .form-preview-article-content {
    width: 100%;
    height: auto;

    .form-preview-card-box {
      display: flex;
      flex-direction: row-reverse;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #e8e8e8;
      padding: 12px 0;

      img {
        width: 64px;
        height: 64px;
        border-radius: 4px;
        object-fit: cover;
      }

      .no-image {
        @include no-image;
        width: 64px;
        height: 64px;

        i {
          margin: 0;
        }
      }

      div {
        width: 70%;
        height: auto;
        font-size: 16px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #262626;
        line-height: 24px;

        .card-content {
          display: block;
          max-height: 48px;
          @include text-ellipsis-2();
        }
      }
    }

    .form-preview-card-box:first-child {
      padding-top: 0;
    }

    .form-preview-card-box:last-child {
      border: none;
      padding-bottom: 0;
    }
  }
}
</style>
