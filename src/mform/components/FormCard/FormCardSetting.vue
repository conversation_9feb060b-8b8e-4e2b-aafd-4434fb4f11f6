<template>
  <div class="form-setting-panel form-card-setting">
    <mform-setting-title title="内容卡片">
    </mform-setting-title>
    
    <mform-setting-title-name 
      ref="MFormSettingTitle" 
      :value="value" 
      @input="updateTitle"
      :language-list="languageList"
      :is-open-multi-language="isOpenMultiLanguage"
    >
    </mform-setting-title-name>
    
    <mform-setting-description-name 
      ref="MFormSettingDescription" 
      :value="value" 
      @input="updateDescription"
      :language-list="languageList"
      :is-open-multi-language="isOpenMultiLanguage"
    >
    </mform-setting-description-name>
    
    <mform-setting-divider />
    
    <mform-setting-list-style-tab mode="card" :is-graphic="false" :value="value.displayStyle" @input="updateDisplayStyle">
    </mform-setting-list-style-tab>
    
    <mform-setting-photo-article-list 
      title="添加文章"
      :sub-title="`最多添加 10 个导航，图片建议尺寸${isDisplayStyleImage ? '比例16:9' : '64*64像素'}，可拖动排序`"
      :event-list="eventList"
      :wiki-list="wikiList"
      :title-maxlength="40"
      :value="value.pictureInfo" 
      @input="updatePictureInfo"
      :form-type="field.formType"
      :language-list="languageList"
      :is-open-multi-language="isOpenMultiLanguage"
    >
    </mform-setting-photo-article-list>
    
  </div>
</template>

<script>
/* mixin */
import SettingMixin from '@src/mform/mixin/setting'
/* props */
import { settingProps } from '@src/mform/components/props'
/* util */
import * as FormUtil from '@src/component/form/util'
import { findComponentsDownward } from '@src/util/assist'

export default {
  name: 'mform-card-setting',
  mixins: [SettingMixin],
  props: settingProps,
  data() {
    return {
      
    }
  },
  computed: {
    value() {      
      return {
        ...this.commonSettingValue,
        displayStyle: this.displayStyle,
        pictureInfo: this.pictureInfo
      }
    }
  },
  methods: {
    validate(immediate = false) {
      const formItemComponents = findComponentsDownward(this, 'mform-item')
      formItemComponents.forEach(formItemComponent => {
        this.$nextTick(() => {
          formItemComponent.validate && formItemComponent.validate(true, immediate)
        })
      })
    }
  }
};
</script>

<style lang="scss">
.form-card-setting {
  .el-radio-group .el-radio-button {
    // width: 33.33%;
  }
}
</style>
