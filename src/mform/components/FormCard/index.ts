// @ts-nocheck
import FormCardSetting from './FormCardSetting.vue';
import FormCardPreview from './FormCardPreview.vue';

import { getSetting } from '@src/mform/util'
import Vue from 'vue';
import _ from 'lodash'

let FormCardField = {
  formType: 'contentCard', // 字段类型
  name: '内容卡片',
  isSystem: 0,
  number: 10,
  icon: 'icon-read-fill',
  forceDelete: true,
  component: {
    preview: FormCardPreview,
    setting: FormCardSetting
  },
  packFunction: pack,
  unPackFunction: unpack,
  validateFunction: validate,
}

export function pack(field) {
  let originSetting = getSetting(field.setting)
  
  let pictureInfo = originSetting.pictureInfo.map((info, index) => {
    let infos = {}
    if (info.urlType === 'wiki') {
      if (info.wikiIds.length) {
        let wikiTypeIds = info.wikiIds.filter(wiki=> typeof wiki === 'number')
        let wikiDoIds = info.wikiIds.filter(wiki=> typeof wiki === 'string')
        infos.wikiTypeIds = wikiTypeIds
        infos.wikiDoIds = wikiDoIds
      } else {
        infos.wikiId = info.eventId
      }
    }
    infos.title = info.title
    infos.pictureTitle = info.title
    infos.pictureTitleLanguage = info.titleLanguage
    infos.pictureUrl = info.urlAddress
    infos.imageUrl = info.imageUrl
    infos.urlType = info.urlType
    infos.isShowWikiType = info.isShowWikiType
    infos.id = (index + 1).toString()
    return infos
  })
  
  const setting = {
    specialName: originSetting.title,
    specialNameLanguage: originSetting.titleLanguage,
    specialNameSwitch: originSetting.notShowTitle,
    describe: originSetting.description,
    describeLanguage: originSetting.descriptionLanguage,
    describeSwitch: originSetting.notShowDescription,
    listStyle: originSetting.displayStyle,
    pictureInfo: pictureInfo
  }
  
  Vue.set(field, 'setting', setting)
}

export function unpack(field)  {
  let setting = getSetting(field.setting)
  let fieldName = field.fieldName || ''
  let pictureInfo = setting.pictureInfo || []
  
  pictureInfo = pictureInfo.map(info => {
    let urlAddress = info.pictureUrl || info.urlAddress;
    const isWiki = info.urlType == 'wiki'
    if (!isWiki) {
      if ((info.pictureUrl && info.pictureUrl != '无链接') || info.urlAddress) {
        info.urlType = 'custom'
      } else {
        info.urlType = 'none'
      }
    }

    return {
      title: info.pictureTitle,
      titleLanguage: info.pictureTitleLanguage || {},
      imageUrl: info.imageUrl,
      eventId: info.wikiId,
      urlAddress: urlAddress,
      urlType: info.urlType,
      wikiIds: [...(info.wikiTypeIds || []), ...(info.wikiDoIds || [])],
      isShowWikiType: info.isShowWikiType === false ? false : true,
      wikiDoIds: info.wikiDoIds || [],
      id: info.id,
    }
  })
  
  let settingValue = {
    title: setting.specialName,
    titleLanguage: setting.specialNameLanguage || {},
    notShowTitle: setting.specialNameSwitch === false ? false : true,
    description: setting.describe,
    descriptionLanguage: setting.describeLanguage || {},
    notShowDescription: setting.describeSwitch === false ? false : true,
    displayStyle: setting.listStyle
  }
  
  Vue.set(setting, 'pictureInfo', pictureInfo)
  Vue.set(field, 'setting', Object.assign(setting, settingValue))
}

export function validate(field) {
  let message = []
  let setting = getSetting(field?.setting)
  let { title, notShowTitle, description, notShowDescription, pictureInfo = [] } = setting
  
  if (!notShowTitle && !title) {
    message.push('请填写标题名称')
  } 
  if (!notShowDescription && !description) {
    message.push('请填写描述信息')
  }
  
  pictureInfo.forEach((info, index) => {
    if (info.urlType == 'custom') {
      
      if (!info.urlAddress || info.urlAddress == '无链接') {
        message.push(`内容卡片 文章列表 第${index + 1}项 存在空白链接，请检查`)
      }
    }
    
    if (info.urlType == 'event') {
      if (!info.eventId) {
        message.push(`内容卡片 文章列表 第${index + 1}项 存在空白事件链接，请检查`)
      }
    }

    if (info.urlType == 'wiki') {
      if (!info.eventId && !info.wikiIds.length) {
        message.push(`内容卡片 文章列表 第${index + 1}项 存在空白知识库文章链接，请检查`)
      }
    }
    
  })
  
  return message
}
export default FormCardField
