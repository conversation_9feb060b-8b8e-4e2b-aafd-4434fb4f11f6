<template>
  <div class="form-preview-group">
    <div class="form-preview-companyInfo" :class="previewMode == 'pc' ? 'pcMode' : ''">
      <template v-if="companyInfo.isShowAvatar">
        <img class="mar-b-8" :src="companyInfo.avatar" alt="" />
      </template>
      <div class="form-preview-companyInfo-main">
        <div class="company-name-address">
          <div class="company-name mar-b-8 flex-x jus-center" v-if="companyInfo.isShowName">
            {{ companyInfo.name }}
          </div>
          <div class="company-address mar-b-8 font-13 flex-x jus-center" v-if="companyInfo.isShowAddress && companyInfo.address">
            {{ getTransformData('portal.address') }}：{{ companyInfo.address | interceptString }}
          </div>
        </div>
        <div class="company-info-wrap">
          <div class="info-item" v-if="companyInfo.isShowAddress && companyInfo.address">
            <div class="info-icon">
              <i class="iconfont icon-arrow" :style="{ color: color }"></i>
            </div>
            <div class="info-text">{{ $t('common.base.map') }}</div>
          </div>
          <div class="info-item mar-l-24" v-if="companyInfo.isShowPhone && companyInfo.phone">
            <div class="info-icon">
              <i class="iconfont icon-dianhua2" :style="{ color: color }"></i>
            </div>
            <div class="info-text">{{ $t('common.base.phone') }}</div>
          </div>
          <div class="info-item mar-l-24" v-if="companyInfo.isShowEmail && companyInfo.email">
            <div class="info-icon">
              <i class="iconfont icon-email" :style="{ color: color }"></i>
            </div>
            <div class="info-text">{{ $t('common.base.email') }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/* props */
import { previewProps } from '@src/mform/components/props';
import { findComponentUpward } from '@src/util/assist';
import { transformI18n } from '@src/locales/index.ts';
import { useLocaleInject } from '@hooks/useLocale'
import { useFormMultiLanguage } from '@hooks/useFormMultiLanguage'
import { defaultCNKey } from '@src/util/languages.js';
import { getRootWindow } from '@src/util/dom';

export default {
  name: 'mform-company-info-preview',
  props: previewProps,
  data() {
    return {};
  },
  setup(){
    const { locale: formPreviewLocale } = useLocaleInject('formPreviewLocale');
    const { isOpenMultiLanguage } = useFormMultiLanguage();

    return {
      formPreviewLocale,
      isOpenMultiLanguage,
    };
  },
  methods: {
    getValue(key) {
      // if (this.formPreviewLocale === defaultCNKey) {
      //   return (
      //     this.field.setting[`${key}Language`]?.[defaultCNKey] ||
      //     this.field.setting[key]
      //   );
      // }
      // return transformI18n(
      //   this.field.setting?.[`${key}Language`],
      //   this.$i18n?.locale
      // );
     return this.field.setting?.[`${key}Language`]?.[this.formPreviewLocale] || 
            this.field.setting[`${key}Language`]?.[defaultCNKey] ||
            this.field.setting[key]
    },
    getTransformData(message) {
      return transformI18n(message, this.formPreviewLocale)
    },
  },
  computed: {
    formDesignComponent() {
      return findComponentUpward(this, 'mform-design');
    },
    formPreviewComponent() {
      return findComponentUpward(this, 'mform-preview');
    },
    background() {
      const themeColour =
        this.formDesignComponent?.themeColour ||
        this.formPreviewComponent?.themeColour ||
        '';

      if (!themeColour)
        return 'linear-gradient(180deg, #FFFFFF 0%, #FFE9D8 100%)';

      const color = themeColour.split(',')[0];
      if (color === '#FF7100') {
        return 'linear-gradient(180deg, #FFFFFF 0%, #FFE9D8 100%)';
      } else if (color === '#096DD9') {
        return 'linear-gradient(180deg, #FFFFFF 0%, #E1F0FF 100%)';
      } else if (color === this.getThemeColor) {
        return 'linear-gradient(180deg, #FFFFFF 0%, #E4FFFF 100%)';
      }
      return 'linear-gradient(180deg, #FFFFFF 0%, #F3F3F3 100%)';
    },
    color() {
      return (
        this.formDesignComponent?.themeColour.split(',')[0] ||
        this.formPreviewComponent?.themeColour.split(',')[0] ||
        ''
      );
    },
    initCompanyInfo() {
      return (
        this.formDesignComponent?.companyInfo ||
        this.formPreviewComponent?.companyInfo ||
        {}
      );
    },
    name() {
      // if (this.formPreviewLocale === defaultCNKey) {
      //   return (
      //     this.field.setting?.nameLanguage?.[defaultCNKey] || this.field.setting?.name
      //   );
      // }
      // return this.field.setting?.nameLanguage[this.formPreviewLocale]
      // return transformI18n(this.field.setting?.nameLanguage, this.$i18n?.locale);
      return this.field.setting?.nameLanguage?.[this.formPreviewLocale] || 
            this.field.setting?.nameLanguage?.[defaultCNKey] || 
            this.field.setting?.name
    },
    companyInfo() {
      // if (!this.name) return this.initCompanyInfo;

      return {
        avatar: this.field.setting?.avatar || this.initCompanyInfo.logo || 'https://file.shb.ltd/shb-resource/DefaultCompany.png',
        isShowAvatar: this.field.setting?.isShowAvatar,
        name:
          this.formPreviewLocale === defaultCNKey
            ? this.initCompanyInfo.name
            : this.getValue('name') || '',
        isShowName: this.field.setting?.isShowName,
        phone:
          this.formPreviewLocale === defaultCNKey
            ? this.initCompanyInfo.phone
            : this.getValue('phone') || '',
        isShowPhone: this.field.setting?.isShowPhone,
        email:
          this.formPreviewLocale === defaultCNKey
            ? this.initCompanyInfo.email
            : this.getValue('email') || '',
        isShowEmail: this.field.setting?.isShowEmail,
        address:
          this.formPreviewLocale === defaultCNKey
            ? this.initCompanyInfo.address
            : this.getValue('address') || '',
        isShowAddress: this.field.setting?.isShowAddress,
      };
    },
    serviceHotLine() {
      const RootWindow = getRootWindow(window);
      return RootWindow?.grayAuth?.SERVICE_HOT_LINE ?? false;
    },
  },
  filters: {
    interceptString(value) {
      if (!value) return;

      return value.length > 36 ? `${value.substring(0, 36)}...` : value;
    },
  },
};
</script>

<style lang="scss" scoped>
.form-preview-group {
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;

  .form-preview-companyInfo {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 16px;

    &.pcMode {
      flex-direction: row;
      justify-content: center;
      img {
        max-width: 120px;
        margin-bottom: 0;
      }
      .form-preview-companyInfo-main {
        display: flex;
        width: auto;
        .company-name-address {
          max-width: 200px;
          margin: 0 12px;
          .company-name {
            margin-bottom: 4px;
          }
          .company-address {
            margin-bottom: 0;
          }
        }
        .company-info-wrap {
          padding-top: 2px;
        }
      }
    }

    img {
      height: 40px;
      max-width: 100%;
    }

    &-main {
      width: 100%;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: normal;
      color: #595959;
      line-height: 22px;

      .company-name {
        font-size: 15px;
      }
      .company-address {
        line-height: 20px;
      }
      .company-info-wrap {
        display: flex;
        justify-content: center;
        .info-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          .info-icon {
            width: 24px;
            height: 24px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 20px;
            background: rgba(0, 0, 0, 0.04);
            margin-bottom: 4px;
            cursor: pointer;
            .iconfont {
              font-size: 14px;
            }
          }
          .info-text {
            line-height: 14px;
            font-size: 10px;
            color: #8C8C8C;
          }
        }
      }
    }
  }
}
</style>
