<template>
  <div class="form-setting-panel">
    <mform-setting-title title="公司信息"> </mform-setting-title>
    <!-- 头像 -->
    <div class="form-setting-panel-item">
      <el-checkbox
        class="notshow-title"
        :value="value.isShowAvatar"
        @input="updateStatus('Avatar', $event)"
      >
      </el-checkbox>
      <mform-item class="form-item" :label="avatarField.displayName">
        <img :src="value.avatar" class="avatar" />
        <!-- <el-upload
          class="form-item-upload"
          action="string"
          :before-upload="onBeforeUploadImage"
          :http-request="uploadImagePic"
          :limit="10000"
          accept=".jpg,.jpeg,.png"
          :show-file-list="false"
        >
          <mform-setting-mark text="更换图片" v-if="value.avatar">
            <img :src="value.avatar" class="avatar" />
          </mform-setting-mark>
          <i class="el-icon-plus" v-else></i>
        </el-upload> -->
      </mform-item>
    </div>

    <!-- 名称 -->
    <div class="form-setting-panel-item">
      <el-checkbox
        class="notshow-title"
        :value="value.isShowName"
        @input="updateStatus('Name', $event)"
      >
      </el-checkbox>
      <mform-item class="form-item company-item" :label="nameField.displayName">
        <form-text :field="nameField" :value="value.name" @input="updateName" />
        <!-- <div
          class="language-config"
          @click="openDialog('name')"
          v-if="isOpenMultiLanguage"
        >
          <i class="iconfont icon-earth"></i>
        </div> -->
        <div v-if="isOpenMultiLanguage">
          <base-select-language
            selectKey="name" 
            :field="nameField"
            :defaultOption="{
              formType:'text',
            }"
            defaultFormType="text"
            :defaultValue="value.name"
            :defaultValueLanguage="value.nameLanguage"
            @save="updateFields"
          >
          </base-select-language>
        </div>
      </mform-item>
    </div>

    <!-- 电话 -->
    <div class="form-setting-panel-item">
      <el-checkbox
        class="notshow-title"
        :value="value.isShowPhone"
        @input="updateStatus('Phone', $event)"
      >
      </el-checkbox>
      <mform-item
        class="form-item company-item"
        :label="phoneField.displayName"
      >
        <form-text
          :field="phoneField"
          :value="value.phone"
          @input="updatePhone"
        />
        <!-- <div
          class="language-config"
          @click="openDialog('phone')"
          v-if="isOpenMultiLanguage"
        >
          <i class="iconfont icon-earth"></i>
        </div> -->
        <div v-if="isOpenMultiLanguage">
          <base-select-language 
            selectKey="phone" 
            :field="phoneField"
            :defaultOption="{
              formType:'text',
            }"
            defaultFormType="text"
            :defaultValue="value.phone"
            :defaultValueLanguage="value.phoneLanguage"
            @save="updateFields"
          >
          </base-select-language>
        </div>
      </mform-item>
    </div>

    <!-- 邮箱 -->
    <div class="form-setting-panel-item">
      <el-checkbox
        class="notshow-title"
        :value="value.isShowEmail"
        @input="updateStatus('Email', $event)"
      >
      </el-checkbox>
      <mform-item
        class="form-item company-item"
        :label="emailField.displayName"
      >
        <form-text
          :field="emailField"
          :value="value.email"
          @input="updateEmail"
        />
        <!-- <div
          class="language-config"
          @click="openDialog('email')"
          v-if="isOpenMultiLanguage"
        >
          <i class="iconfont icon-earth"></i>
        </div> -->
        <div v-if="isOpenMultiLanguage">
          <base-select-language 
            selectKey="email" 
            :field="emailField"
            :defaultOption="{
              formType:'text',
            }"
            defaultFormType="text"
            :defaultValue="value.email"
            :defaultValueLanguage="value.emailLanguage"
            @save="updateFields"
          >
          </base-select-language>
        </div>
      </mform-item>
    </div>

    <!-- 地址 -->
    <div class="form-setting-panel-item">
      <el-checkbox
        class="notshow-title"
        :value="value.isShowAddress"
        @input="updateStatus('Address', $event)"
      >
      </el-checkbox>
      <mform-item
        class="form-item company-item"
        :label="addressField.displayName"
      >
        <form-text
          :field="addressField"
          :value="value.address"
          @input="updateAddress"
        />
        <!-- <div
          class="language-config"
          @click="openDialog('address')"
          v-if="isOpenMultiLanguage"
        >
          <i class="iconfont icon-earth"></i>
        </div> -->
        <div v-if="isOpenMultiLanguage">
          <base-select-language 
            selectKey="address" 
            :field="addressField"
            :defaultOption="{
              formType:'text',
            }"
            defaultFormType="text"
            :defaultValue="value.address"
            :defaultValueLanguage="value.addressLanguage"
            @save="updateFields"
          >
          </base-select-language>
        </div>
      </mform-item>
    </div>

    <div class="form-setting-tip" @click="jumpSetting">
      点击前往公司信息设置修改公司信息
    </div>

    <MultilingualDialog
      ref="multilingualDialogRef"
      title="多语配置"
      :is-need-validation="false"
      :language-list="languageList"
      @update="updateFields"
    />
  </div>
</template>

<script>
/* mixin */
import SettingMixin from '@src/mform/mixin/setting';
/* props */
import { settingProps } from '@src/mform/components/props';
import {
  formCompanyInfoSettingAvatarField,
  formCompanyInfoSettingNameField,
  formCompanyInfoSettingPhoneField,
  formCompanyInfoSettingEmailField,
  formCompanyInfoSettingAddressField,
} from '@src/mform/components/FormCompanyInfo/FormCompanyInfoMode.ts';
import Uploader from 'packages/BaseUpload/uploader';
import MultilingualDialog from '@src/component/compomentV2/MultilingualDialog/index.vue';
import _ from 'lodash';
import { PageRoutesTypeEnum } from 'pub-bbx-global/pageType/dist/enum/PageRoutesEnum'
import { openAccurateTab } from '@src/util/platform'
export default {
  name: 'mform-company-info-setting',
  mixins: [SettingMixin],
  props: settingProps,
  components: {
    MultilingualDialog,
  },
  data() {
    return {
      isNotNull: false,
      key: '',
    };
  },
  mounted() {
    this.init();
  },
  computed: {
    companyInfo() {
      return this.formDesignComponent?.companyInfo || {};
    },
    value() {
      // if (!this.fieldSetting.name) return this.companyInfo;
      console.log('this.fieldSetting', this.fieldSetting);

      return {
        avatar: this.fieldSetting.avatar,
        isShowAvatar: this.fieldSetting.isShowAvatar,
        name: this.fieldSetting.name,
        nameLanguage: this.fieldSetting.nameLanguage,
        isShowName: this.fieldSetting.isShowName,
        phone: this.fieldSetting.phone,
        phoneLanguage: this.fieldSetting.phoneLanguage,
        isShowPhone: this.fieldSetting.isShowPhone,
        email: this.fieldSetting.email,
        emailLanguage: this.fieldSetting.emailLanguage,
        isShowEmail: this.fieldSetting.isShowEmail,
        address: this.fieldSetting.address,
        addressLanguage: this.fieldSetting.addressLanguage,
        isShowAddress: this.fieldSetting.isShowAddress,
      };
    },
    avatarField() {
      return formCompanyInfoSettingAvatarField(this.isNotNull);
    },
    nameField() {
      return formCompanyInfoSettingNameField(!this.isNotNull);
    },
    phoneField() {
      return formCompanyInfoSettingPhoneField(this.isNotNull);
    },
    emailField() {
      return formCompanyInfoSettingEmailField(this.isNotNull);
    },
    addressField() {
      return formCompanyInfoSettingAddressField(this.isNotNull);
    },
  },
  methods: {
    init() {
      // if (!this.fieldSetting.avatar && this.companyInfo.logo)
      this.updateAvatar(this.companyInfo.logo);
      // if (!this.fieldSetting.name && this.companyInfo.name)
      this.updateName(this.companyInfo.name || '');
      // if (!this.fieldSetting.phone && this.companyInfo.phone)
      this.updatePhone(this.companyInfo.phone || '');
      // if (!this.fieldSetting.email && this.companyInfo.email)
      this.updateEmail(this.companyInfo.email || '');
      // if (!this.fieldSetting.address && this.companyInfo.address)
      this.updateAddress(this.companyInfo.address || '');
    },
    updateAvatar(value) {
      this.update(value, 'avatar');
    },
    updateLanguageObj(value, key) {
      const _language = _.cloneDeep(this.value[`${key}Language`]);
      this.$set(_language, 'zh', value);
      this.update(_language, `${key}Language`);
    },
    updateName(value) {
      this.update(value, 'name');
      this.updateLanguageObj(value, 'name');
    },
    updatePhone(value) {
      this.update(value, 'phone');
      this.updateLanguageObj(value, 'phone');
    },
    updateEmail(value) {
      this.update(value, 'email');
      this.updateLanguageObj(value, 'email');
    },
    updateAddress(value) {
      this.update(value, 'address');
      this.updateLanguageObj(value, 'address');
    },
    updateStatus(key, value) {
      this.update(value, `isShow${key}`);
    },
    jumpSetting() {
      // this.$platform.openTab({
      //   id: 'ACCOUNT_COMPANY',
      //   title: '公司信息设置',
      //   close: true,
      //   url: '/foundation/account/company',
      // });
      openAccurateTab({
        type: PageRoutesTypeEnum.PageFeAccountCompany,
      })
    },
    onBeforeUploadImage(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG) {
        this.$message.error('上传图片只能是 jpg/png 格式!');
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2mb!');
      }

      return isJPG && isLt2M;
    },
    uploadImagePic(param) {
      Uploader.upload(param.file, '/files/upload')
        .then(result => {
          if (result.status != 0) {
            this.$message({
              message: `${result.message}`,
              duration: 1500,
              type: 'error',
            });
            return;
          }

          let file = result.data;
          let item = {
            uid: param.file.uid,
            id: file.id,
            filename: file.fileName,
            // 如果后端返回url,必须使用。如果后端不返回，需要拼接
            url: file.ossUrl || file.url || `/files/get?fileId=${file.id}`,
            fileSize: file.fileSizeStr,
          };

          this.updateAvatar(item.url);
        })
        .catch(err => {
          console.warn(err);
        })
        .finally(() => {
          //
        });
    },
    openDialog(key) {
      this.key = key;
      this.$refs.multilingualDialogRef.disabled = true;
      // console.log('this.key', this.key, this.fieldSetting, this.value[`${key}Language`]);
      if (this.value[`${key}Language`]?.['zh'] !== this.value[key]) {
        const _value = Object.assign({}, this.value[`${key}Language`]);
        _value['zh'] = this.value[key];
        if (this.key === 'name') {
          this.$refs.multilingualDialogRef.openDialog(_value, true);
        } else {
          this.$refs.multilingualDialogRef.openDialog(_value);
        }
      } else {
        if (this.key === 'name') {
          this.$refs.multilingualDialogRef.openDialog(
            this.value[`${key}Language`],
            true
          );
        } else {
          this.$refs.multilingualDialogRef.openDialog(
            this.value[`${key}Language`]
          );
        }
      }
    },
    updateFields(value, key) {
      this.key = key;
      this.update(value['zh'], this.key);
      this.update(value, `${this.key}Language`);
    },
  },
};
</script>

<style lang="scss" scoped>
.form-setting-panel {
  &-item {
    display: flex;
    align-items: center;

    .el-checkbox {
      margin-left: 16px;
    }
  }

  .form-item {
    display: flex;
    align-items: baseline;
    margin-top: 12px;
    width: 100%;

    &-upload {
      .el-upload-list {
        display: none;
      }

      width: 48px;
      height: 48px;
      background-color: #fbfdff;
      border: 1px dashed #c0ccda;
      border-radius: 3px;
      display: flex;
      height: 50px;
      width: 50px;
      line-height: 50px;
      img {
        width: 48px;
        height: 48px;
        position: relative;
        top: -2px;
      }
      i {
        width: 48px;
        line-height: 48px;
        font-size: 24px;
      }
    }

    img {
      height: 40px;
      max-width: 100%;
      position: relative;
      top: -2px;
    }

    ::v-deep label {
      width: 80px;
    }

    ::v-deep .form-item-control {
      max-width: 100%;
    }

    & > .form-item-control {
      // max-width: calc(100% - 46px);
      display: flex;
      flex-flow: column;
      & > label {
        width: 60px;
        margin-left: 10px;
        padding-top: 0;
        padding-left: 0;
      }
    }
  }

  .language-config {
    width: 32px;
    height: 28px;
    text-align: center;
    line-height: 28px;
    background: #f5f8fa;
    border-radius: 4px;
    border: 1px solid #cbd6e2;
    cursor: pointer;
    margin-left: 10px;
  }
}

.form-setting-tip {
  margin: 16px;
  color: $color-primary-light-6;
  cursor: pointer;
}
</style>

<style lang="scss">
.company-item {
  .form-item-control-content {
    display: flex;
  }
}
</style>
