// @ts-nocheck
import FormCompanyInfoPreview from './FormCompanyInfoPreview.vue';
import FormCompanyInfoSetting from './FormCompanyInfoSetting.vue';

import { getSetting } from '@src/mform/util'
import Vue from 'vue'

let FormCompanyInfoField = {
  formType: 'companyInfo', // 字段类型
  name: '公司信息',
  icon: 'icon-qiye-tianchong',
  isSystem: 0,
  forceDelete: true,
  number: 1,
  component: {
    setting: FormCompanyInfoSetting,
    preview: FormCompanyInfoPreview,
  },
  packFunction: pack,
  unPackFunction: unpack,
  validateFunction: validate,
}

export function pack(field) {  
  let originSetting = getSetting(field.setting)

  let setting = {
    avatar: originSetting?.avatar || '',
    isShowAvatar:  originSetting.isShowAvatar ?? true,
    name: originSetting?.name || '',
    nameLanguage: originSetting?.nameLanguage || {},
    isShowName:  originSetting.isShowName ?? true,
    phone: originSetting?.phone || '',
    phoneLanguage: originSetting?.phoneLanguage || {},
    isShowPhone:  originSetting.isShowPhone ?? true,
    email: originSetting?.email || '',
    emailLanguage: originSetting?.emailLanguage || {},
    isShowEmail:  originSetting.isShowEmail ?? true,
    address: originSetting?.address || '',
    addressLanguage: originSetting?.addressLanguage || {},
    isShowAddress:  originSetting.isShowAddress ?? true,
  }

  Vue.set(field, 'setting', setting)
}

export function unpack(field) {
  let setting = getSetting(field.setting)
  
  const settingValue = {
    avatar: setting.avatar || '',
    isShowAvatar:  setting.isShowAvatar ?? true,
    name: setting.name || '',
    nameLanguage: setting.nameLanguage || {},
    isShowName:  setting.isShowName ?? true,
    phone: setting.phone || '',
    phoneLanguage: setting.phoneLanguage || {},
    isShowPhone:  setting.isShowPhone ?? true,
    email: setting.email || '',
    emailLanguage: setting.emailLanguage || {},
    isShowEmail:  setting.isShowEmail ?? true,
    address: setting.address || '',
    addressLanguage: setting.addressLanguage || {},
    isShowAddress:  setting.isShowAddress ?? true,
  }
  
  Vue.set(field, 'setting', Object.assign(setting, settingValue))
  
}

export function validate(field) {
  let message = []
  let setting = getSetting(field?.setting)
  let { name } = setting

  if (!name) {
    message.push('公司名称不能为空')
  } 

  return message
  
}

export default FormCompanyInfoField;
