<template>
  <div class="form-preview-group">
    <!-- <div :style="styles" v-html="content"></div> -->
    <div class="ql-editor">
      <div class="content" :style="styles" v-html="content"></div>
    </div>
  </div>
</template>

<script>
/* props */
import { previewProps } from '@src/mform/components/props';

export default {
  name: 'mform-rich-text-preview',
  props: previewProps,
  computed: {
    backgroundColorStyle() {
      return this.field.setting?.backgroundColorStyle || '#FFFFFF';
    },
    content() {
      return this.field.setting?.content || '';
    },
    styles() {
      return {
        backgroundColor: this.backgroundColorStyle,
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.form-preview-group {
  width: 100%;
  height: auto;
  min-height: 65px;
  padding: 12px 16px 27px;

  .ql-editor {
    padding: 0;
    width: 100%;
    height: auto;

    .content {
      font-size: 14px;
      font-weight: 400;
      color: #262626;
      line-height: 26px;

      p {
        font-size: 14px;
        font-weight: 400;
        color: #262626;
        line-height: 26px;
        white-space: pre-wrap;
      }

      ::v-deep img {
        width: 100% !important;
        height: auto;
      }
    }
  }
}
</style>
