<template>
  <div class="form-setting-panel">
    <mform-setting-title title="富文本"></mform-setting-title>

    <mform-item :label="backgroundColorField.displayName">
      <form-style
        :is-show-font="false"
        :field="backgroundColorField"
        :value="value.backgroundColorStyle"
        @input="updateBackgroundColorStyle"
      />
    </mform-item>

    <base-editor
      class="base-editor"
      v-model="value.content"
      :toolbar-options="option"
      @input="updateContent"
      ref="editor"
    >
    </base-editor>
  </div>
</template>

<script>
/* model */
import { FormRichTitleSettingBackgroundColorField } from '@src/mform/components/FormRichText/FormRichTextModel.ts';
/* mixin */
import SettingMixin from '@src/mform/mixin/setting';
/* props */
import { settingProps } from '@src/mform/components/props';

export default {
  name: 'mform-rich-text',
  mixins: [SettingMixin],
  props: settingProps,
  data() {
    return {
      option: [
        'bold',
        'italic',
        'underline',
        'strike',
        { list: 'ordered' },
        { list: 'bullet' },
        { indent: '-1' },
        { indent: '+1' },
        { color: [] },
        'link',
        'image',
      ],
    };
  },
  computed: {
    backgroundColorField() {
      return FormRichTitleSettingBackgroundColorField;
    },
    backgroundColorStyle() {
      return this.fieldSetting.backgroundColorStyle || '#ffffff';
    },
    content() {
      return this.fieldSetting.content || '';
    },
    value() {
      return {
        backgroundColorStyle: {
          color: this.backgroundColorStyle,
        },
        content: this.content,
      };
    },
  },
  methods: {
    updateBackgroundColorStyle(value) {
      const color = value?.color;
      this.update(color, 'backgroundColorStyle');
    },
    updateContent(value) {
      this.update(value, 'content');
    }
  },
};
</script>

<style lang="scss" scoped>
.base-editor {
  margin: 0 16px;
}
</style>
