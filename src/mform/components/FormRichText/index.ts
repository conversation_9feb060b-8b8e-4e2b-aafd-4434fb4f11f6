// @ts-nocheck
import FormRichTextPreview from './FormRichTextPreview.vue';
import FormRichTextSetting from './FormRichTextSetting.vue';
/* util */
import { getSetting } from '@src/mform/util'
/* vue */
import Vue from 'vue';

let FormRichTextField = {
  formType: 'richText', // 字段类型
  name: '富文本',
  isSystem: 0,
  number: 10,
  icon: 'icon-fuwenben',
  forceDelete: true,
  component: {
    setting: FormRichTextSetting,
    preview: FormRichTextPreview,
  },
  packFunction: pack,
  unPackFunction: unpack,
  validateFunction: validate,
};

export function unpack(field)  {
  let setting = getSetting(field.setting)

  let settingValue = {
    backgroundColorStyle: setting.backgroundColorStyle || '#FFFFFF',
    content: setting.content || ''
  }

  Vue.set(field, 'setting', Object.assign(setting, settingValue))
}

export function pack(field) {
  let originSetting = getSetting(field.setting)
  
  const setting = {
    backgroundColorStyle: originSetting?.backgroundColorStyle || '#FFFFFF',
    content: originSetting?.content || '',
  }
  
  Vue.set(field, 'setting', setting)
}

export function validate(field) {
  let message = []
  let setting = getSetting(field?.setting)
  let { content } = setting

  if (!content) {
    message.push('请填写富文本内容')
  } 

  return message
}

export default FormRichTextField;
