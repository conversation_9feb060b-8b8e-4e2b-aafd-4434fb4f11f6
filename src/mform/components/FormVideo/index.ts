// @ts-nocheck
import FormVideoSetting from './FormVideoSetting.vue';
import FormVideoPreview from './FormVideoPreview.vue';

import { getSetting } from '@src/mform/util'
import { isEmpty } from '@src/util/type'
import Vue from 'vue';

let FormVideoField = {
  formType: 'video', // 字段类型
  name: '视频',
  isSystem: 0,
  number: 10,
  icon: 'icon-video-fill',
  forceDelete: true,
  component: {
    setting: FormVideoSetting,
    preview: FormVideoPreview,
  },
  packFunction: pack,
  unPackFunction: unpack,
  validateFunction: validate,
}

export function pack(field) {
  let originSetting = getSetting(field.setting)
  
  let videoList = originSetting.videoList || []
  let videoInfo = videoList.map(video => {
    return {
      source: video.type,
      videoUrl: video.videoUrl,
      url: video.videoUrl,
      videoPicture: video.videoPicture
    }
  })
  
  const setting = {
    specialName: originSetting.title,
    specialNameLanguage: originSetting.titleLanguage,
    specialNameSwitch: originSetting.notShowTitle,
    describe: originSetting.description,
    describeLanguage: originSetting.descriptionLanguage,
    describeSwitch: originSetting.notShowDescription,
    videoInfo
  }
  
  Vue.set(field, 'setting', setting)
}

export function unpack(field) {
  let setting = getSetting(field.setting)
  let videoInfo = setting.videoInfo || []
  let videoList = videoInfo.map(video => {
    
    return {
      type: video.source,
      videoUrl: video.videoUrl,
      url: video.videoUrl,
      videoPicture: video.videoPicture
    }
  })
  
  let settingValue = {
    title: setting.specialName,
    titleLanguage: setting.specialNameLanguage || {},
    notShowTitle: setting.specialNameSwitch === false ? false : true,
    description: setting.describe,
    descriptionLanguage: setting.describeLanguage || {},
    notShowDescription: setting.describeSwitch === false ? false : true,
    displayStyle: setting.listStyle,
    videoList
  }
  
  Vue.set(field, 'setting', Object.assign(setting, settingValue))
}

export function validate(field) {
  let message = []
  let setting = getSetting(field?.setting)
  let { videoList, notShowTitle, notShowDescription, title = '', description = '' } = setting
  
  if (!title && !notShowTitle) {
    message.push('标题名称不能为空')
  } 
  
  if (!description && !notShowDescription) {
    message.push(`描述内容不能为空`)
  }
  
  if (!videoList || isEmpty(videoList)) {
    message.push('上传视频')
  }
  
  return message
}

export default FormVideoField;
