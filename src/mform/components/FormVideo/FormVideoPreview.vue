<template>
  <div class="form-preview-group" :class="previewMode == 'pc' ? 'pcMode' : ''">
    <MFormPreviewHeader :content="commonSettingValue" />

    <div class="default-img" v-if="videoList.length === 0">
      <img :src="previewVideoBgImage" alt="" />
    </div>
    <div class="single-img" v-else-if="videoList.length === 1">
      <img :src="videoList[0].videoPicture" alt="" />
      <div></div>
      <img :src="videoPlayImage" alt="" />
    </div>
    <div v-else>
      <ul :key="previewMode" :loop="false" :show-indicators="false">
        <li v-for="item in videoList" :key="item.id">
          <img :src="item.videoPicture" alt="" />
          <div></div>
          <img :src="videoPlayImage" alt="" />
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
/* mixin */
import PreviewMixin from '@src/mform/mixin/preview';
/* props */
import { previewProps } from '@src/mform/components/props';
import { getOssUrl } from '@src/util/assets'
const previewVideoBgImage = getOssUrl('/previewVideoBg.png')
const videoPlayImage = getOssUrl('/<EMAIL>')

export default {
  name: 'mform-video-preview',
  mixins: [PreviewMixin],
  props: previewProps,
  data(){
    return {
      videoPlayImage,
      previewVideoBgImage
    }
  },
  // computed: {
  //   width() {
  //     if (this.previewMode == 'mobile') return 212
  //     return 227
  //   },
  // },
};
</script>

<style lang="scss" scoped>
.form-preview-group {
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;

  &.pcMode {
    padding: 0 16px;
  }
  ul {
    margin: 0 8px;
    display: grid;
    grid-template-columns: repeat(2, 50%);
    gap: 8px;
    li {
      position: relative;
      padding-top: 56.25%; 
      overflow: hidden;
      img {
        &:first-of-type {
          width: 100%;
          height: 100%;
          object-fit: cover; 
          position: absolute;
          top: 0;
          left: 0;
        }
        &:last-of-type {
          height: 40px;
          width: 40px;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }
    }
  }

  .default-img {
    width: 100%;
    height: 210px;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .single-img {
    width: 100%;
    height: 210px;
    position: relative;

    img:first-child {
      width: 100%;
      height: 100%;
    }

    div {
      width: 100%;
      height: 50%;
      position: absolute;
      bottom: 0;
      background: linear-gradient(
        180deg,
        rgba(0, 0, 0, 0) 0%,
        rgba(0, 0, 0, 0.67) 100%
      );
    }

    img:last-child {
      width: 48px;
      height: 48px;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
}
</style>
