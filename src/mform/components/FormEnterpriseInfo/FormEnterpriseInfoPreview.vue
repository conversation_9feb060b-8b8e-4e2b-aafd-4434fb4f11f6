<template>
  <div class="form-preview-group">
    <!-- 个人信息 -->
    <div class="form-preview-personal-info">
      <div class="form-preview-info-top">
        <div class="form-preview-info-left">
          <img :src="personalCenterDefaultAvatar" alt="" />
          <h3>{{ $t('portal.clickLogin') }}</h3>
        </div>
        <div class="form-preview-info-right">
          <van-icon name="arrow" />
        </div>
      </div>
      <div class="form-preview-info-bottom" v-if="isChinese">
        <div>
          <span>{{name}}</span>
          <van-icon name="arrow" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/* props */
import { previewProps } from '@src/mform/components/props';
import { findComponentUpward } from '@src/util/assist';
import { getCompany } from 'src/api/PortalApi.ts'
import { getOssUrl } from '@src/util/assets'
const personalCenterDefaultAvatar = getOssUrl('/personalCenterDefaultAvatar.png')
export default {
  name: 'mform-enterprise-info-preview',
  props: previewProps,
  data() {
    return {
      name:'',
      personalCenterDefaultAvatar
    }
  },
  computed: {
    formPreviewComponent() {
      return findComponentUpward(this, 'mform-preview');
    },
    isChinese() {
      return this?.$i18n.locale === 'zh';
    },
  },
  mounted(){
    this.getCompany()
  },
  methods:{
    async getCompany(){
      const {status,data} = await getCompany()
      if(status==0){
        let tenConfig = data?.tenConfig ?? {}
        this.name = tenConfig.showNickName ? tenConfig.nickName : tenConfig.tenantName
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.form-preview-group {
  width: 100%;
  height: auto;
  min-height: 65px;
  padding: 0;

  .form-preview-personal-info {
    width: 100%;
    height: auto;
    // background: linear-gradient(180deg, #ffffff 0%, #f0f0f0 100%);

    .form-preview-info-top {
      height: 103px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .form-preview-info-left {
        display: flex;
        align-items: center;

        img {
          width: 56px;
          height: 56px;
          margin: 0 23px 0 25px;
        }

        h3 {
          font-size: 18px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #262626;
          line-height: 18px;
          margin: 0;
        }
      }

      .form-preview-info-right {
        display: flex;
        align-items: center;
        color: #8c8c8c;
        margin-right: 26px;

        span {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          line-height: 20px;
          margin: 0 14px 0 8px;
        }
      }
    }

    .form-preview-info-bottom {
      height: 46px;
      display: flex;
      justify-content: center;

      div {
        height: 100%;
        width: 323px;
        background: url('@src/assets/img/personalCenterHeader.png')
          no-repeat;
        background-size: 100% 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 20px 0 16px;
        color: #f0cb91;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
      }
    }
  }
}
</style>
