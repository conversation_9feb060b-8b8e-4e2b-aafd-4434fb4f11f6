// @ts-nocheck
import preview from './FormEnterpriseInfoPreview.vue';
import setting from './FormEnterpriseInfoSetting.vue';
/* util */
import { getSetting } from '@src/mform/util'
/* vue */
import Vue from 'vue';

let FormEnterpriseInfoField = {
  formType: 'enterpriseInfo', // 字段类型
  name: '公司名称',
  isSystem: 1,
  number: 1,
  icon: 'icon-qiye-tianchong',
  forceDelete: false,
  component: {
    setting,
    preview,
  },
  packFunction: pack,
  unPackFunction: unpack,
  validateFunction: validate,
};

export function unpack(field)  {
  let setting = getSetting(field.setting)

  let settingValue = {
   
  }

  Vue.set(field, 'setting', Object.assign(setting, settingValue))
}

export function pack(field) {
  let originSetting = getSetting(field.setting)
  
  const setting = {
    
  }
  
  Vue.set(field, 'setting', setting)
}

export function validate(){

}

export default FormEnterpriseInfoField;
