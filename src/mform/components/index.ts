// @ts-nocheck
import FormNotice from '@src/mform/components/FormNotice';
import FormCard from '@src/mform/components/FormCard';
import FormGraphic from '@src/mform/components/FormGraphic';
import FormImageAD from '@src/mform/components/FormImageAD';
import FormTitle from '@src/mform/components/FormTitle';
import FormVideo from '@src/mform/components/FormVideo';
// import FormUserCenter from '@src/mform/components/FormUserCenter';   FormUserCenter拆分成多个组件
import FormShop from '@src/mform/components/FormShop';
import FormTabbar from '@src/mform/components/FormTabbar';
import FormCommodity from '@src/mform/components/FormCommodity';
import FormIM from '@src/mform/components/FormIM';
import FormCompanyInfo from '@src/mform/components/FormCompanyInfo';
import FormRichText from '@src/mform/components/FormRichText';
import FormCustomerService from '@src/mform/components/FormCustomerService';
import FormProductRegister from '@src/mform/components/FormProductRegister';
import { isEnterpriseEdition } from '@shb-lib/version'
import FormEnterpriseInfo from '@src/mform/components/FormEnterpriseInfo';
import FormEventList from '@src/mform/components/FormEventList';
import FormFunctionList from '@src/mform/components/FormFunctionList';
import FormScan from "@src/mform/components/FormScan";

// 所有字段
const ALL_FORM_FIELDS = [
  FormImageAD,
  FormGraphic,
  FormCard,
  FormVideo,
  FormTitle,
  FormRichText,
  FormNotice,
  FormIM,
  // FormUserCenter,
  FormShop,
  FormTabbar,
  FormCommodity,
  FormCompanyInfo,
  FormCustomerService,
  FormProductRegister,
  FormEnterpriseInfo,
  FormEventList,
  FormFunctionList,
  FormScan,
].reduce(
  (acc, val) =>
    (Array.isArray(val) ? (acc = acc.concat(val)) : acc.push(val)) && acc,
  []
);

const FormFieldMap = {};
const PreviewComponents = {};
const SettingComponents = {};
const BuildComponents = {};
const ViewComponents = {};

for (let i = 0; i < ALL_FORM_FIELDS.length; i++) {
  let formField = ALL_FORM_FIELDS[i];
  let field = {
    name: formField.name, // 组件显示名称
    formType: formField.formType, // 组件类型
    fieldName: formField.fieldName, // 字段名，部分系统字段会提供
    isSystem: formField.isSystem || 0, // 是否为为系统组件
    alias: formField.alias,
    forceDelete: formField.forceDelete === true,
    number: formField.number,
    icon: formField.icon,
    unPackFunction: formField.unPackFunction,
    packFunction: formField.packFunction,
    validateFunction: formField.validateFunction,
    isNotShowPreviewList: formField.isNotShowPreviewList
  };
  
  if (!formField.alias) {
    // 预览组件
    let previewComp = formField.component?.preview || {};
    PreviewComponents[previewComp.name] = previewComp;
    field.preview = previewComp.name; // 预览组件名
    
    // 设置组件
    let settingComp = formField.component?.setting;
    if (null != settingComp) {
      SettingComponents[settingComp.name] = settingComp;
      field.setting = settingComp.name; // 设置组件名
    }
    
    // 表单组件
    let buildComp = formField.component?.build;
    if (null != buildComp) {
      BuildComponents[buildComp.name] = buildComp;
      field.build = buildComp.name; // 表单组件名
    }
    
    // 显示组件
    let viewComp = formField.component?.view;
    if (null != viewComp) {
      ViewComponents[viewComp.name] = viewComp;
      field.view = viewComp.name;
    }
    
    // 扩展组件
    let extendComp = formField.component?.extend;
    if (null != extendComp && Object.keys(extendComp).length > 0) {
      let extend = {};
      
      for (let name in extendComp) {
        let comp = extendComp[name];
        SettingComponents[comp.name] = comp;
        extend[name] = comp.name;
      }
      
      field.extend = extend;
    }
  }

  FormFieldMap[formField.formType] = field;
}

const COMMON_FIELDS = [
  'pictureAD',
  'imageTextNavigation',
  'contentCard',
  'video',
  'titleText',
  'richText',
  'notice',
  'productRegister',
  'mall',
  'companyInfo',
  'enterpriseInfo',
  'eventList',
  'functionList',
  'scan',
];

const MODE_MANAGER = {
  base: {
    // include: !isEnterpriseEdition() ? [...COMMON_FIELDS, 'im'] : [...COMMON_FIELDS]
    include: [...COMMON_FIELDS, 'im']
  },
  door: {
    include: [...COMMON_FIELDS, 'im']
  },
  findMode(mode) {
    return MODE_MANAGER[mode] || MODE_MANAGER.base;
  }
};

/** 获取字段 */
FormFieldMap.get = function(formType) {
  let field = FormFieldMap[formType];
  
  if (field && field.alias) {
    let aliasField = FormFieldMap[field.alias];
    field.preview = aliasField.preview;
    field.setting = aliasField.setting;
    field.build = aliasField.build;
    field.extend = aliasField.extend || {};
  }

  return field;
};

const FieldManager = {
  /** 根据mode获取字段 */
  findModeFields(mode = 'base') {
    let fields = ALL_FORM_FIELDS;
    let modeConfig = MODE_MANAGER.findMode(mode);
    let include = modeConfig.include || [];
    
    // 排除字段
    if (include.length > 0) {
      fields = fields.filter(f => include.indexOf(f.formType) >= 0);
    }
    return fields.map(f => f.formType);
  },
  /** 根据字段类型获取单个字段 */
  findField(formType) {
    let field = FormFieldMap.get(formType);
    if (field && field.alias) {
      let aliasField = FormFieldMap[field.alias];
      
      field.preview = aliasField.preview;
      field.setting = aliasField.setting;
      field.build = aliasField.build;
      field.extend = aliasField.extend || {};
      
      return {
        ...aliasField,
        ...field
      };
    }
    
    return field;
  }
};

export {
  FieldManager,
  PreviewComponents,
  SettingComponents,
  BuildComponents,
  ViewComponents,
  FormFieldMap
};
