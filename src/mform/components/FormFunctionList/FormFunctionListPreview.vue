<template>
  <div class="form-preview-group">
    <!-- 必备工具 -->
    <div class="form-preview-essential-tool">
      <template v-for="(item, index) in toolList">
        <div class="form-preview-tool-item" :key="index" v-show="show(item)">
          <div>
            <i :class="`iconfont ${item.icon}`"></i>
            <span>{{ title(item) }}</span>
          </div>
          <van-icon name="arrow" />
        </div>
      </template>
    </div>
  </div>
</template>

<script>
/* props */
import { previewProps } from '@src/mform/components/props';
import { getRootWindow } from '@src/util/dom';
import { useLocaleInject } from '@hooks/useLocale'
import { defaultCNKey } from '@src/util/languages.js';
/* mixin */
import { VersionControlLinkCMixin } from '@src/mixins/versionControlMixin';
import { isNotUndefined } from '@src/util/type';

export default {
  name: 'mform-functionList-list-preview',
  mixins: [VersionControlLinkCMixin],
  props: previewProps,
  data() {
    return {};
  },
  setup(){
    const { locale: formPreviewLocale } = useLocaleInject('formPreviewLocale');

    return {
      formPreviewLocale,
    };
  },
  computed: {
    toolList() {
      const arr = this.field?.setting?.functionList ?? []
      
      const toolByVersionShowMap = {
        orderList: this._isShowLinkCOrderList,
        multilingual: this._isShowLinkCMultiLanguageList,
        addressList: this._isShowLinkCAddressList
      }
      
      return arr.filter(item => {
        
        const needVersionJudgeList = ['orderList', 'multilingual', 'addressList']
        if (needVersionJudgeList.includes(item.type)) {
          
          const isShow = toolByVersionShowMap[item.type]
          return isNotUndefined(isShow) ? isShow : false
          
        }
        
        return true
      })
    },
    title() {
      return function (item) {
        if (this.$i18n) {
          if (this.formPreviewLocale === defaultCNKey) return item.specialName;
          return item.specialNameLanguage[this.formPreviewLocale];
        } else {
          return item.specialName;
        }
      };
    },
    show() {
      return function (item) {
        if (item.type == 'addressList' && !this.isOpenServiceMall) return false;

        let list = [
          'orderList',
          'productList',
          'collectionList',
          'addressList',
        ];
        if (this?.$i18n.locale != 'zh' && list.includes(item.type))
          return false;
        return true;
      };
    },
    isOpenServiceMall() {
      const RootWindow = getRootWindow(window);
      return RootWindow?.grayAuth?.SERVICE_MALL ?? false;
    },
  },
};
</script>

<style lang="scss" scoped>
.form-preview-group {
  width: 100%;
  height: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #fafafa;
  overflow: auto;

  .form-preview-essential-tool {
    width: 343px;
    height: auto;
    background: #ffffff;
    box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.1),
      0px 1px 4px 0px rgba(0, 0, 0, 0.02);
    border-radius: 4px;

    .form-preview-tool-item {
      height: 56px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #8c8c8c;
      padding: 0 13px 0 19px;
      border-top: 1px solid #e8e8e8;

      div {
        display: flex;
        align-items: center;
        color: #262626;

        i {
          margin-right: 12px;
        }

        span {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          line-height: 20px;
        }
      }
    }

    .form-preview-tool-item:first-child {
      border: none;
    }
  }
}
</style>
