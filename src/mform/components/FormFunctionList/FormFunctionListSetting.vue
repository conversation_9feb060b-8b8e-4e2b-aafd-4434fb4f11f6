<template>
  <div class="form-setting-panel">
    <mform-setting-title title="功能列表"></mform-setting-title>
    <mform-setting-function-list
      :value="functionList"
      @input="updateFunctionList"
      :language-list="languageList"
      :is-open-multi-language="isOpenMultiLanguage"  
    >
    </mform-setting-function-list>
  </div>
</template>

<script>
/* mixin */
import SettingMixin from '@src/mform/mixin/setting'
/* props */
import { settingProps } from '@src/mform/components/props';

/* vue */
import MFormSettingFunctionList from 'src/mform/common/setting/MFormSettingFunctionList.vue'
/* util */
import { cloneDeep } from 'lodash'

export default {
  name: 'mform-functionList-list-setting',
  mixins: [SettingMixin],
  props: settingProps,
  components:{
    [MFormSettingFunctionList.name]:MFormSettingFunctionList
  },
  data() {
   return{
    
   }
  },
  computed:{
    functionList(){
      return this.field?.setting?.functionList ??[]
    }
  },
  methods:{
    updateFunctionList(value) {
      this.update(value, 'functionList')
    },
    update(value, prop, isSetting = true) {
      const newValue = cloneDeep(value)
      
      this.$emit('input', { value: newValue, prop, isSetting });
    },
  }
};
</script>

<style lang="scss" scoped>

</style>
