// @ts-nocheck
import preview from './FormFunctionListPreview.vue';
import setting from './FormFunctionListSetting.vue';
/* util */
import { getSetting } from '@src/mform/util'
/* vue */
import Vue from 'vue';

let FormFunctionListField = {
  formType: 'functionList', // 字段类型
  name: '功能列表',
  isSystem: 1,
  number: 1,
  icon: 'icon-xiangxiliebiao1',
  forceDelete: false,
  component: {
    setting,
    preview,
  },
  packFunction: pack,
  unPackFunction: unpack,
  validateFunction: validate,
};

export function pack(field) {
  let setting = getSetting(field.setting)
  Vue.set(field, 'setting', setting)
}

export function unpack(field) {
  let setting = getSetting(field.setting)
  Vue.set(field, 'setting', setting)
}

export function validate(field) {
  let message = []
  let functionList = field?.setting?.functionList ?? []

  functionList.map((v,i)=>{
    if(!v.specialName) message.push(`功能${i+1} - 标题不能为空`)
    if(!v.type) message.push(`功能${i+1} - 关联项不能为空`)
    if(v.type=='PAAS'){
      if(!v.url) message.push(`功能${i+1} - PASS类型不能为空`)
    }
    if(v.type=='customLink'){
      if(!v.url) message.push(`功能${i+1} - 自定义链接不能为空`)
    }
  })
  return message
}

export default FormFunctionListField;
