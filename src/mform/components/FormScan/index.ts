// @ts-nocheck
import FormScanSetting from './FormScanSetting';
import FormScanPreview from './FormScanPreview';

import { getSetting } from '@src/mform/util'
import Vue from 'vue';
import { t } from '@src/locales'

let FormVideoField = {
  formType: 'scan', // 字段类型
  name: t('portal.scan.scanName'),
  isSystem: 0,
  number: 1,
  icon: 'icon-fdn-code',
  forceDelete: true,
  component: {
    setting: FormScanSetting,
    preview: FormScanPreview,
  },
  packFunction: pack,
  unPackFunction: unpack,
  validateFunction: validate,
}

export function pack(field) {
  let originSetting = getSetting(field.setting)

  const setting = {
    specialName: originSetting.title,
  }
  
  Vue.set(field, 'setting', setting)
}

export function unpack(field) {
  let setting = getSetting(field.setting)
  
  let settingValue = {
    title: setting.specialName,
  }
  
  Vue.set(field, 'setting', Object.assign(setting, settingValue))
}

export function validate(field) {
  let message = []
  let setting = getSetting(field?.setting)
  let { title = '' } = setting
  
  if (!title) {
    message.push(t('portal.scan.validTips'))
  }
  
  return message
}

export default FormVideoField;
