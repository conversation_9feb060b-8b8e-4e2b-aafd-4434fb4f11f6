<template>
  <div class="form-setting-panel form-product-register-setting-panel">
    <mform-setting-title :title="$t('portal.scan.scanName')">
      <span slot="tips">
        <el-tooltip :content="$t('portal.scan.tip')" placement="top">
          <i class="iconfont icon-question-circle mar-l-4"></i>
        </el-tooltip>
      </span>
    </mform-setting-title>
    
    <mform-setting-title-name ref="MFormSettingTitle" :value="value" @input="updateTitle" :needTitleShowControl="false" :is-required="true">
    </mform-setting-title-name>

  </div>
</template>

<script>
/* mixin */
import SettingMixin from '@src/mform/mixin/setting'
/* props */
import { settingProps } from '@src/mform/components/props'

export default {
  name: 'mform-scan-setting',
  mixins: [SettingMixin],
  props: settingProps,
  data() {
    return {
    }
  },
  computed: {
    value() {
      return {
        ...this.commonSettingValue
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.form-product-register-setting-panel {
  ::v-deep .form-item {
    padding: 0 16px;
    & > label {
      width: 50px;
      padding-left: 0;
    }
  }
}

.register-guide-show {
  margin: 0 0 0 17px;
}

</style>