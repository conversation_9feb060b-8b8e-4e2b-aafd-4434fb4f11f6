<template>
  <div class="form-preview-group">
    <!-- 服务记录 -->
    <div class="form-preview-service-record">
      <van-grid :border="false" :column-num="4">
        <van-grid-item v-for="(item, index) in serviceList" :key="index">
          <van-image height="56" :src="item.photo" />
          <span>{{ $t(`portal.${item.text}`) }}</span>
        </van-grid-item>
      </van-grid>
    </div>
  </div>
</template>

<script>
/* props */
import { previewProps } from '@src/mform/components/props';
import { getOssUrl } from '@src/util/assets'
const photo1 = getOssUrl('/personalCenter1.png')
const photo2 = getOssUrl('/personalCenter2.png')
const photo3 = getOssUrl('/personalCenter3.png')
const photo4 = getOssUrl('/personalCenter4.png')

export default {
  name: 'mform-event-list-preview',
  props: previewProps,
  data() {
    return {
      serviceList: [
        {
          text: 'processing',
          photo: photo1,
        },
        {
          text: 'completed',
          photo: photo2,
        },
        {
          text: 'review',
          photo: photo4,
        },
        {
          text: 'all',
          photo: photo3,
        },
      ],
    };
  },
  computed: {
    
  },

};
</script>

<style lang="scss" scoped>
.form-preview-group {
  width: 100%;
  height: auto;
  min-height: 65px;
  padding: 0;

  .form-preview-service-record {
    width: 100%;
    height: 120px;
    // background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
    background: #ffffff;
    box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.1),
      0px 1px 4px 0px rgba(0, 0, 0, 0.02);
    border-radius: 4px;
    margin-bottom: 12px;
    font-size: 13px;

    span {
      height: 40px;
    }
  }
}
</style>
