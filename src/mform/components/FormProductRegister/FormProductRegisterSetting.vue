<template>
  <div class="form-setting-panel form-product-register-setting-panel">
    <mform-setting-title title="产品注册">
    </mform-setting-title>
    
    <mform-setting-title-name ref="MFormSettingTitle" :value="value" @input="updateTitle" :needTitleShowControl="false" :is-required="true">
    </mform-setting-title-name>
    
    <!-- <el-checkbox class="register-guide-show" :value="fieldSetting.ifRegisterGuide" @change="updateGuide">
      扫码后引导用户进行产品注册
    </el-checkbox> -->
    <!-- <mform-setting-divider /> -->
  </div>
</template>

<script>
/* mixin */
import SettingMixin from '@src/mform/mixin/setting'
/* props */
import { settingProps } from '@src/mform/components/props'

export default {
  name: 'mform-product-register-setting',
  mixins: [SettingMixin],
  props: settingProps,
  data() {
    return {
    }
  },
  computed: {
    value() {
      return {
        ...this.commonSettingValue
      }
    },
  },
  methods: {
    // updateGuide(ifRegisterGuide) {
    //   this.update(ifRegisterGuide, 'ifRegisterGuide')
      // this.setNotValidateAll()
      // this.validateTitle()
      // this.updateErrorStatus(false)
    // }
  }
};
</script>

<style lang="scss" scoped>
.form-product-register-setting-panel {
  ::v-deep .form-item {
    padding: 0 16px;
    & > label {
      width: 50px;
      padding-left: 0;
    }
  }
}

.register-guide-show {
  margin: 0 0 0 17px;
}

</style>