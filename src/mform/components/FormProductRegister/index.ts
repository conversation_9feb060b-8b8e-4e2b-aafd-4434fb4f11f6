// @ts-nocheck
import FormProductRegisterSetting from './FormProductRegisterSetting.vue';
import FormProductRegisterPreview from './FormProductRegisterPreview.vue';

import { getSetting } from '@src/mform/util'
import Vue from 'vue';

let FormVideoField = {
  formType: 'productRegister', // 字段类型
  name: '产品注册',
  isSystem: 0,
  number: 1,
  icon: 'icon-caidan-chanpin1',
  forceDelete: true,
  component: {
    setting: FormProductRegisterSetting,
    preview: FormProductRegisterPreview,
  },
  packFunction: pack,
  unPackFunction: unpack,
  validateFunction: validate,
}

export function pack(field) {
  let originSetting = getSetting(field.setting)

  const setting = {
    specialName: originSetting.title,
    // ifRegisterGuide: originSetting.ifRegisterGuide
  }
  
  Vue.set(field, 'setting', setting)
}

export function unpack(field) {
  let setting = getSetting(field.setting)
  
  let settingValue = {
    title: setting.specialName,
    // ifRegisterGuide: !!setting.ifRegisterGuide
  }
  
  Vue.set(field, 'setting', Object.assign(setting, settingValue))
}

export function validate(field) {
  let message = []
  let setting = getSetting(field?.setting)
  let { title = '' } = setting
  
  if (!title) {
    message.push('标题名称不能为空')
  }
  
  return message
}

export default FormVideoField;
