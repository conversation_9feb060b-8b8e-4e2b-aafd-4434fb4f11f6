<template>
  <div class="form-preview-group prod-reg">
    <img class="prod-reg-img" :src="prodregscanImage" alt=""/>
    <span class="prod-reg-title">{{content.title}}</span>
    <div class="prod-reg-add-btn"><i class="iconfont icon-right1"></i></div>
  </div>
</template>

<script>
import { previewProps } from '@src/mform/components/props';
import { getOssUrl } from '@src/util/assets'
const prodregscanImage = getOssUrl('/prodregscan.png')
export default {
  name: 'mform-product-register-preview',
  props: previewProps,
  data(){
    return{
      prodregscanImage
    }
  },
  computed: {
    content() {
      return {
        title: this.field.setting?.title || '',
        description: this.field.setting?.description || ''
      };
    },
  }
};
</script>

<style lang="scss" scoped>
.prod-reg{
  height: 68px;
  color: #262626;
  background-color: #FFF9F0;
  font-family: PingFangSC-Regular, PingFang SC;
  margin: 16px;
  padding: 0 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  &-img {
    width: 40px;
    height: 40px;
  }
  &-title {
    font-size: 16px;
    overflow: hidden;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    white-space: nowrap;
    margin: 0 12px;
    flex: 1;
  }
  &-add-btn {
    .iconfont {
      font-size: 14px;
      color: #8c8c8c;
    }
  }
}
</style>
