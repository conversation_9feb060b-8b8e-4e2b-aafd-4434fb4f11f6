<template>
  <div class="form-setting-panel form-graphic-setting">
    <mform-setting-title title="图文导航">
    </mform-setting-title>
    
    <mform-setting-title-name 
      :is-required="false" 
      ref="MFormSettingTitle" 
      :value="value" 
      :language-list="languageList"
      :is-open-multi-language="isOpenMultiLanguage"
      @input="updateTitle"
    >
    </mform-setting-title-name>
    
    <mform-setting-description-name 
      :is-required="false" 
      ref="MFormSettingDescription" 
      :value="value" 
      :language-list="languageList"
      :is-open-multi-language="isOpenMultiLanguage"
      @input="updateDescription"
    >
    </mform-setting-description-name>
    
    <mform-setting-divider />
    
    <mform-setting-list-style-tab mode="graphic" :value="value.displayStyle" @input="updateDisplayStyle">
    </mform-setting-list-style-tab>
    
    <template v-if="isDisplayStyleImage">
      <mform-setting-photo-article-list 
        title="添加导航"
        sub-title="最多添加 8 个导航，图片建议尺寸56*56像素，可拖动排序"
        :event-list="eventList"
        :title-maxlength="10"
        :max="8"
        is-show-event-select
        :is-open-register="isOpenRegister"
        :language-list="languageList"
        is-graphic
        :value="value.pictureInfo"
        :is-validate-title="false"
        @input="updatePictureInfo"
        :form-type="field.formType"
        :is-open-multi-language="isOpenMultiLanguage"
      >
      </mform-setting-photo-article-list>
    </template>
    
    <template v-else>
      <el-tabs :value="activeTab" @input="switchTab">
        
        <el-tab-pane label="内容设置" name="content">
          
          <mform-setting-photo-article-list 
            title="添加导航"
            sub-title="最多添加 8 个导航，可拖动排序"
            :event-list="eventList"
            :title-maxlength="10"
            :max="8"
            is-show-event-select
            :is-validate-title="false"
            :is-open-register="isOpenRegister"
            :language-list="languageList"
            is-graphic
            :is-show-image="false"
            :value="value.pictureInfo" 
            @input="updatePictureInfo"
            :form-type="field.formType"
            :is-open-multi-language="isOpenMultiLanguage"
          >
          </mform-setting-photo-article-list>
        </el-tab-pane>
        
        <el-tab-pane label="样式设置" name="style"> 
          <mform-item :label="backgroundColorStyleField.displayName">
            <form-style :is-show-font="false" :field="backgroundColorStyleField" :value="value.backgroundColorStyle" @input="updateBackgroundColorStyle"/>
          </mform-item>
          <mform-item :label="textStyleField.displayName">
            <form-style :is-show-font="false" :field="textStyleField" :value="value.textStyle" @input="updateTextStyle"/>
          </mform-item>
        </el-tab-pane>
        
      </el-tabs> 
    </template>
    
  </div>
</template>

<script>
/* mixin */
import SettingMixin from '@src/mform/mixin/setting'
/* model */
import { FormGraphicSettingTextStyleField, FormGraphicSettingBackgroundColorStyleField } from '@src/mform/components/FormGraphic/FormGraohicModel.ts'
import FormStyle from '@model/form/FormStyle.ts'
/* props */
import { settingProps } from '@src/mform/components/props'
/* util */
import * as FormUtil from '@src/component/form/util'
import { findComponentsDownward } from '@src/util/assist'

export default {
  name: 'mform-graphic-setting',
  mixins: [SettingMixin],
  props: settingProps,
  data() {
    return {
      activeTab: 'content',
    }
  },
  computed: {
    textStyleField() {
      return FormGraphicSettingTextStyleField
    },
    backgroundColorStyleField() {
      return FormGraphicSettingBackgroundColorStyleField
    },
    value() {
      return {
        textStyle: this.fieldSetting.textStyle,
        backgroundColorStyle: this.fieldSetting.backgroundColorStyle,
        ...this.commonSettingValue,
        displayStyle: this.displayStyle,
        pictureInfo: this.pictureInfo
      }
    },
    isOpenRegister() {
      return this.formDesignComponent?.isOpenRegister;
    },
  },
  methods: {
    switchTab(tab) {
      this.activeTab = tab
    },
    updateBackgroundColorStyle(value) {
      this.update(value, 'backgroundColorStyle')
    },
    updateTextStyle(value) {
      this.update(value, 'textStyle')
    },
    validate(immediate = false) {
      const formItemComponents = findComponentsDownward(this, 'mform-item')
      formItemComponents.forEach(formItemComponent => {
        this.$nextTick(() => {
          formItemComponent.validate && formItemComponent.validate(true, immediate)
        })
      })
    }
  }
};
</script>

<style lang="scss">
.form-graphic-setting {
  overflow-y: scroll;
  padding-bottom: 16px;
}
</style>
