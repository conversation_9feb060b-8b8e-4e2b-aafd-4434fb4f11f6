// @ts-nocheck
import FormGraphicSetting from './FormGraphicSetting.vue';
import FormGraphicPreview from './FormGraphicPreview.vue';

import { getSetting } from '@src/mform/util'
import Vue from 'vue'
import { toRefs, reactive } from 'vue'
import { t } from '@src/locales'

let FormGraphicField = {
  formType: 'imageTextNavigation', // 字段类型
  name: '图文导航',
  isSystem: 0,
  number: 20,
  icon: 'icon-tuwendaohang',
  forceDelete: true,
  component: {
    preview: FormGraphicPreview,
    setting: FormGraphicSetting
  },
  packFunction: pack,
  unPackFunction: unpack,
  validateFunction: validate,
}

export function pack(field) {
  let originSetting = getSetting(field.setting)
  let pictureInfo = originSetting.pictureInfo.map((info, index) => {
    let infos = {}
    if (info.urlType === 'wiki') {
      let wikiTypeIds = info.wikiIds.filter(wiki=> typeof wiki === 'number')
      let wikiDoIds = info.wikiIds.filter(wiki=> typeof wiki === 'string')
      infos.wikiTypeIds = wikiTypeIds
      infos.wikiDoIds = wikiDoIds
    }
    //故障树
    if (info.urlType === 'faultLibrary') {
      infos.faultLibraryIds = info?.faultLibraryIds ?? [];
    }
    infos.title = info.title
    infos.pictureTitle = info.title
    infos.pictureTitleLanguage = info.titleLanguage
    infos.pictureUrl = info.urlAddress
    infos.urlAddress = info.eventId
    infos.imageUrl = info.imageUrl
    infos.urlType = info.urlType
    infos.isShowWikiType = info.isShowWikiType
    infos.id = (index + 1).toString()
    return infos
  })
  
  const setting = {
    specialName: originSetting.title,
    specialNameLanguage: originSetting.titleLanguage,
    specialNameSwitch: originSetting.notShowTitle,
    describe: originSetting.description,
    describeLanguage: originSetting.descriptionLanguage,
    describeSwitch: originSetting.notShowDescription,
    listStyle: originSetting.displayStyle,
    pictureInfo: pictureInfo,
    textColor: originSetting.textStyle.color,
    backgroundColor: originSetting.backgroundColorStyle.color,
  }
  
  Vue.set(field, 'setting', setting)
}

export function unpack(field)  {
  let setting = getSetting(field.setting)
  let pictureInfo = setting.pictureInfo || []
  
  setting.textStyle = {
    color: setting.textColor || setting.textColour || '#262626',
    isBold: false,
    fontSize: ''
  }
  
  setting.backgroundColorStyle = {
    color: setting.backgroundColor || setting.backgroundColour || '#FFFFFF',
    isBold: false,
    fontSize: ''
  }
  
  pictureInfo = pictureInfo.map(info => {

    let urlAddress = info.pictureUrl || info.urlAddress;
    // const isEvent = info.urlType == 'event'
    // const isQuery = info.urlType == 'query'
    if (info.urlType == 'event' || info.urlType == 'query' || info.urlType == 'register' || info.urlType == 'wiki' || info.urlType == 'faultLibrary') {
      urlAddress = ''
    }
    else if ((info.pictureUrl && info.pictureUrl != '无链接') || info.urlAddress) {
      info.urlType = 'custom'
    }
    else {
      info.urlType = 'none'
    }
    
    return {
      title: info.pictureTitle || info.title,
      titleLanguage: info.pictureTitleLanguage || {},
      imageUrl: info.imageUrl,
      urlAddress: urlAddress,
      urlType: info.urlType,
      eventId: info.urlAddress,
      wikiIds: [...(info.wikiTypeIds || []), ...(info.wikiDoIds || [])],
      isShowWikiType: info.isShowWikiType === false ? false : true,
      wikiDoIds: info.wikiDoIds || [],
      faultLibraryIds: info.faultLibraryIds || [],
      id: info.id,
    }
  })
  
  let settingValue = {
    title: setting.specialName,
    titleLanguage: setting.specialNameLanguage || {},
    notShowTitle: setting.specialNameSwitch === false ? false : true,
    description: setting.describe,
    descriptionLanguage: setting.describeLanguage || {},
    notShowDescription: setting.describeSwitch === false ? false : true,
    displayStyle: setting.listStyle
  }
  
  Vue.set(setting, 'pictureInfo', pictureInfo)
  Vue.set(field, 'setting', Object.assign(setting, settingValue))
}

export function validate(field) {
  let message = []
  let setting = getSetting(field?.setting)
  let { title, notShowTitle, description, notShowDescription, pictureInfo = [] } = setting
  
  if (!notShowTitle && !title) {
    message.push('请填写标题名称')
  } 
  if (!notShowDescription && !description) {
    message.push('请填写描述信息')
  }

  pictureInfo.forEach((info, index) => {
    console.log('info', info)
    // if (!info.title) {
    //   message.push(`图片导航 导航列表 第${index + 1}项 存在空白标题，请检查`)
    // }
    
    if (info.urlType == 'custom') {
      
      if (!info.urlAddress || info.urlAddress == '无链接') {
        message.push(`图片导航 导航列表 第${index + 1}项 存在空白链接，请检查`)
      }
    }
    
    if (info.urlType == 'event') {
      if (!info.eventId) {
        message.push(`图片导航 导航列表 第${index + 1}项 存在空白事件类型，请检查`)
      }
    }

    if (info.urlType == 'wiki') {
      if (!info.wikiIds.length) {
        message.push(`图片导航 文章列表 第${index + 1}项 存在空白知识库文章链接，请检查`)
      }
    }
    if (info.urlType == 'faultLibrary') {
      if (!info.faultLibraryIds.length) {
        message.push(t('component.faultLibraryDialog.submitMsg', { data: index + 1}))
      }
    }
    
  })
  
  return message
}

export default FormGraphicField;
