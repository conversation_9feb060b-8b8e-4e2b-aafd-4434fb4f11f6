<template>
  <div class="form-preview-group" :class="previewMode == 'pc' ? 'pcMode' : ''">
    <MFormPreviewHeader
      :content="commonSettingValue"
      :remove-margin-bottom="true"
    />

    <div v-if="displayStyle === 'image'" class="form-preview-image-content">
      <div
        v-for="(item, index) in pictureInfo.length === 0
          ? initList
          : pictureInfo"
        :key="`${item.title}${index}`"
      >
        <img :src="item.imageUrl" alt="" />
        <span>{{ item.title }}</span>
      </div>
    </div>
    <div
      v-if="displayStyle === 'article'"
      class="form-preview-article-content"
      :style="styles"
    >
      <div
        v-for="(item, index) in pictureInfo.length === 0
          ? initList
          : pictureInfo"
        :key="index"
      >
        <span>{{ item.title }}</span>
      </div>
    </div>
  </div>
</template>

<script>
/* mixin */
import PreviewMixin from '@src/mform/mixin/preview';
/* props */
import { previewProps } from '@src/mform/components/props';
import { getOssUrl } from '@src/util/assets'
const img1 = getOssUrl('/previewGraphic1.png')
const img2 = getOssUrl('/previewGraphic2.png')
const img3 = getOssUrl('/previewGraphic3.png')
const img4 = getOssUrl('/previewGraphic4.png')

export default {
  name: 'mform-graphic-preview',
  mixins: [PreviewMixin],
  props: previewProps,
  data() {
    return {
      initList: [
        {
          title: '导航一',
          imageUrl: img1,
        },
        {
          title: '导航二',
          imageUrl: img2,
        },
        {
          title: '导航三',
          imageUrl: img3,
        },
        {
          title: '导航四',
          imageUrl: img4,
        },
      ],
    };
  },
  computed: {
    styles() {
      return {
        backgroundColor:
          this.field.setting?.backgroundColorStyle?.color || '#EBEBEB',
        color: this.field.setting?.textStyle?.color || '#262626',
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.form-preview-group {
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;

  &.pcMode {
    .form-preview-image-content {
      div {
        flex-direction: row;
        img {
          width: 42px;
          height: 42px;
          margin-bottom: 0;
        }
        span {
          height: 42px;
          line-height: 42px;
          font-size: 14px;
          color: #595959;
          margin: 0 0 0 5px;
        }
      }
    }
  }

  .form-preview-image-content {
    width: 100%;
    height: auto;
    display: flex;
    flex-wrap: wrap;

    div {
      width: 25%;
      margin-top: 16px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      img {
        width: 56px;
        height: 56px;
        margin-bottom: 5px;
      }

      span {
        font-size: 15px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #262626;
        line-height: 17px;
        height: 36px;
        text-align: center;
        margin: 0 6px 6px;
        overflow: hidden;
        max-width: 90px;
      }
    }
  }

  .form-preview-article-content {
    width: 100%;
    height: 48px;
    display: flex;
    align-items: center;
    background: #ebebeb;
    box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05),
      0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);
    opacity: 0.8;
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #262626;
    line-height: 17px;
    margin: 8px 0;

    div {
      width: 25%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }

    span {
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #262626;
      // line-height: 40px;
      height: 40px;
      text-align: center;
      margin: 6px;
      overflow: hidden;
      max-width: 90px;
    }
  }
}
</style>
