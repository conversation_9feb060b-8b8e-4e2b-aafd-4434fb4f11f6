<template>
  <div class="form-preview-group" :style="styles">
    <h3 :style="titleStyle">{{content.title}}</h3>
    <p :style="descriptionStyle">{{content.description}}</p>
  </div>
</template>

<script>
/* mixin */
import PreviewMixin from '@src/mform/mixin/preview';
/* props */
import { previewProps } from '@src/mform/components/props';

export default {
  name: 'mform-title-preview',
  mixins: [PreviewMixin],
  props: previewProps,
  computed: {
    content() {
      return {
        title: this.title || '请输入标题文本',
        description: this.description || '',
      };
    },
    styles() {
      let alignItems;
      if (this.field.setting?.layout === 'left') {
        alignItems = 'flex-start';
      } else if (this.field.setting?.layout === 'center') {
        alignItems = 'center';
      } else {
        alignItems = 'flex-end';
      }

      return {
        backgroundColor: this.field.setting?.backgroundColorStyle?.color || '#FFFFFF',
        alignItems
      };
    },
    titleStyle() {
      return {
        color: this.field.setting?.titleStyle?.color || '#262626',
        fontSize: `${this.field.setting?.titleStyle?.fontSize}px` || '16px',
        fontWeight: this.field.setting?.titleStyle?.isBold ? '700' : '400' || '400'
      }
    },
    descriptionStyle() {
      return {
        color: this.field.setting?.descriptionStyle?.color || '#8C8C8C',
        fontSize: `${this.field.setting?.descriptionStyle?.fontSize}px` || '14px',
        fontWeight: this.field.setting?.descriptionStyle?.isBold ? '700' : '400' || '400',
        lineHeight: `${this.field.setting?.descriptionStyle?.fontSize * 1.5}px` || '21px',
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.form-preview-group {
  display: flex;
  flex-direction: column;
  padding: 16px;

  h3 {
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #262626;
    line-height: 24px;
    margin-bottom: 12px;
    word-break: break-all;
  }

  p {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #8c8c8c;
    line-height: 18px;
    margin: 0;
    word-break: break-all;
    white-space: pre-wrap;
  }
}
</style>
