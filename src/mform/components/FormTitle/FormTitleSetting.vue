<template>
  <div class="form-setting-panel form-title-setting-panel">
    <mform-setting-title title="标题文本">
    </mform-setting-title>

    <mform-item :label="titleNameField.displayName" class="title-item">
      <form-text
        :field="titleNameField"
        :value="value.title"
        @input="updateTitle"
      />
      <!-- <div
        class="language-config"
        @click="openDialog('title')"
        v-if="isOpenMultiLanguage"
      >
        <i class="iconfont icon-earth"></i>
      </div> -->
      <div v-if="isOpenMultiLanguage">
        <base-select-language
          key="title"
          selectKey="title"  
          :field="titleNameField"
          :defaultOption="{
            formType:'text',
          }"
          defaultFormType="text"
          :defaultValue="value.title"
          :defaultValueLanguage="value.titleLanguage"
          :isFill="false"
          @save="updateFields"
        >
        </base-select-language>
      </div>
    </mform-item>
    <mform-item 
      :label="descriptionField.displayName" 
      style="margin-bottom: 24px"
      class="title-item"
    >
      <form-textarea
        :field="descriptionField"
        :value="value.description"
        @input="updateDescription"
      />
      <!-- <div
        class="language-config"
        @click="openDialog('description')"
        v-if="isOpenMultiLanguage"
      >
        <i class="iconfont icon-earth"></i>
      </div> -->
      <div v-if="isOpenMultiLanguage">
        <base-select-language 
          key="description"
          selectKey="description"
          :field="description"
          :defaultOption="{
            formType:'textarea',
          }"
          defaultFormType="textarea"
          :defaultValue="value.description"
          :defaultValueLanguage="value.descriptionLanguage"
          :isFill="false"
          @save="updateFields"
        >
        </base-select-language>
      </div>
    </mform-item>
    <mform-item :label="layoutField.displayName" style="margin-bottom: 24px">
      <el-radio-group size="medium" :value="value.layout" @input="updateLayout">
        <el-radio-button label="left">
          <i class="iconfont icon-align-left"></i>
        </el-radio-button>
        <el-radio-button label="center">
          <i class="iconfont icon-align-center1"></i>
        </el-radio-button>
        <el-radio-button label="right">
          <i class="iconfont icon-align-right"></i>
        </el-radio-button>
      </el-radio-group>
    </mform-item>
    <mform-item :label="titleStyleField.displayName">
      <form-style
        :field="titleStyleField"
        :value="value.titleStyle"
        @input="update"
      />
    </mform-item>
    <mform-item :label="descriptionStyleField.displayName">
      <form-style
        :field="descriptionStyleField"
        :value="value.descriptionStyle"
        @input="update"
      />
    </mform-item>
    <mform-item :label="backgroundColorField.displayName">
      <form-style
        :is-show-font="false"
        :field="backgroundColorField"
        :value="value.backgroundColorStyle"
        @input="update"
      />
    </mform-item>   

    <MultilingualDialog
      ref="multilingualDialogRef"
      title="多语配置"
      :is-need-validation="false"
      :language-list="languageList"
      @update="updateFields"
    />
  </div>
</template>

<script>
/* model */
import { 
  FormTitleSettingTitleNameField,
  FormTitleSettingDescriptionField,
  FormTitleSettingTitleStyleField,
  FormTitleSettingDescriptionStyleField,
  FormTitleSettingBackgroundColorField,
  FormTitleSettingLayoutField
} from '@src/mform/components/FormTitle/FormTitleModel.ts'
/* mixin */
import SettingMixin from '@src/mform/mixin/setting'
/* props */
import { settingProps } from '@src/mform/components/props'
/* util */
import * as FormUtil from '@src/component/form/util'
import _ from 'lodash'
import MultilingualDialog from '@src/component/compomentV2/MultilingualDialog/index.vue';

export default {
  name: 'mform-title-setting',
  mixins: [SettingMixin],
  props: settingProps,
  data() {
    return {
      // activeTab: 'content'
      key: '',
    }
  },
  components: {
    MultilingualDialog,
  },
  computed: {
    titleNameField() {
      return FormTitleSettingTitleNameField
    },
    descriptionField() {
      return FormTitleSettingDescriptionField
    },
    titleStyleField() {
      return FormTitleSettingTitleStyleField
    },
    descriptionStyleField() {
      return FormTitleSettingDescriptionStyleField
    },
    backgroundColorField() {
      return FormTitleSettingBackgroundColorField
    },
    layoutField() {
      return FormTitleSettingLayoutField
    },
    value() {
      return this.fieldSetting || {}
    }
  },
  methods: {
    // switchTab(tab) {
    //   this.activeTab = tab
    // },
    updateForDom(event) {
      let el = event.target;
      let prop = el.dataset.prop;
      let value = el.value;
      
      this.update(value, prop);
    },
    updateLayout(layout) {
      this.update(layout, 'layout')
    },
    updateTitle(value) {
      this.update(value, 'title')

      const _value = Object.assign({}, this.value.titleLanguage)
      _value['zh'] = value
      this.update(_value, 'titleLanguage')
    },
    updateDescription(value) {
      this.update(value, 'description')

      const _value = Object.assign({}, this.value.descriptionLanguage)
      _value['zh'] = value
      this.update(_value, 'descriptionLanguage')
    },
    updateTitleStyle(value) {
      this.update(value, 'titleStyle')
    },
    updateDescriptionStyle(value) {
      this.update(value, 'descriptionStyle')
    },
    updateBackgroundColorStyle(value) {
      this.update(value, 'backgroundColorStyle')
    },
    // update(value, prop, isSetting = true) {
    //   this.$emit('input', { value, prop, isSetting });
    // },
    openDialog(key) {
      this.key = key;
      if (this.value[`${key}Language`]?.['zh'] !== this.value[key]) {
        const _value = _.cloneDeep(this.value[`${key}Language`])
        _value['zh'] = this.value[key];
        if (this.key === 'title') {
          this.$refs.multilingualDialogRef.openDialog(_value, true);
        } else {
          this.$refs.multilingualDialogRef.openDialog(_value);
        }
      } else {
        if (this.key === 'title') {
          this.$refs.multilingualDialogRef.openDialog(
            this.value[`${key}Language`], true
          );
        } else {
          this.$refs.multilingualDialogRef.openDialog(
            this.value[`${key}Language`]
          );
        }
      }
    },
    updateFields(value,key) {
      this.key = key;
      this.update(value['zh'], this.key);
      this.update(value, `${this.key}Language`);
    },
  }
};
</script>

<style lang="scss">
.form-title-setting-panel {
  .form-item {
    padding: 0 16px;
    & > label {
      width: 80px;
      padding-left: 0;
    }
  }
  .form-item-control {
    max-width: calc(100% - 80px);
  }
  .el-radio-group {
    display: flex;
    width: 100%;
    height: 32px;
    label {
      width: 33.33%;
      padding: 0;
      .el-radio-button__inner {
        width: 100%;
        height: 32px;
        padding-top: 7px;
        padding-bottom: 7px;
      }
    }
  }

  .title-item {
    .form-item-control-content {
      display: flex;
    }
  }
}
</style>

<style lang="scss" scoped>
.language-config {
  width: 32px;
  height: 28px;
  text-align: center;
  line-height: 28px;
  background: #F5F8FA;
  border-radius: 4px;
  border: 1px solid #CBD6E2;
  cursor: pointer;
  margin-left: 10px;
}
</style>
