// @ts-nocheck
import FormTitleSetting from './FormTitleSetting.vue';
import FormTitlePreview from './FormTitlePreview.vue';

import { getSetting } from '@src/mform/util'
import Vue from 'vue';
import { toRefs, reactive } from 'vue'

let FormTitleField = {
  formType: 'titleText', // 字段类型
  name: '标题文本',
  isSystem: 0,
  number: 10,
  icon: 'icon-font-size1',
  forceDelete: true,
  component: {
    setting: FormTitleSetting,
    preview: FormTitlePreview,
  },
  packFunction: pack,
  unPackFunction: unpack,
  validateFunction: validate,
}

export function unpack(field) {
  let setting = getSetting(field.setting)
  
  const titleInfo = {
    title: setting.specialName || setting.title || '',
    titleLanguage: setting.specialNameLanguage || {},
    description: setting.describe || '',
    descriptionLanguage: setting.describeLanguage || {},
    layout: setting.displayLocation || 'left',
    titleStyle: {
      fontSize: setting.titleSize || '16',
      isBold: setting.titleWidth || true,
      color: setting.titleColour || '#262626'
    },
    descriptionStyle: {
      fontSize: setting.describeSize || '14',
      isBold: setting.describeWidth || false,
      color: setting.describeColour || '#8C8C8C'
    },
    backgroundColorStyle: {
      color: setting.backgroundColour || '#FFFFFF',
      isBold: false,
      fontSize: ''
    }
  }
  
  Vue.set(field, 'setting', Object.assign(setting, titleInfo))
}

export function pack(field)  {
  let originSetting = getSetting(field.setting)
  
  const setting = {
    specialName: originSetting.title,
    specialNameLanguage: originSetting.titleLanguage,
    describe: originSetting.description,
    displayLocation: originSetting.layout,
    describeLanguage: originSetting.descriptionLanguage,
    titleSize: originSetting.titleStyle.fontSize,
    titleWidth: originSetting.titleStyle.isBold,
    titleColour: originSetting.titleStyle.color,
    
    describeSize: originSetting.descriptionStyle.fontSize,
    describeWidth: originSetting.descriptionStyle.isBold,
    describeColour: originSetting.descriptionStyle.color,
    
    backgroundColour: originSetting.backgroundColorStyle.color,
  }
  
  Vue.set(field, 'setting', setting)
}

export function validate(field) {
  let message = []
  let setting = getSetting(field?.setting)
  let { title = '', description } = setting
  
  if (!title) {
    message.push('标题名称不能为空')
  }
  
  return message
}

export default FormTitleField;
