<template>
  <div class="form-setting-panel">
    <mform-setting-title title="图片广告">
    </mform-setting-title>
    
    <mform-setting-title-name 
      ref="MFormSettingTitle" 
      :value="value" 
      @input="updateTitle"
      :language-list="languageList"
      :is-open-multi-language="isOpenMultiLanguage"
    >
    </mform-setting-title-name>
    
    <mform-setting-description-name 
      ref="MFormSettingDescription" 
      :value="value" 
      @input="updateDescription"
      :language-list="languageList"
      :is-open-multi-language="isOpenMultiLanguage"
    >
    </mform-setting-description-name>
    
    <mform-setting-divider />
    
    <mform-setting-list-style-tab mode="imageAD" title="展示样式" is-image :value="value.displayStyle" @input="updateDisplayStyle">
    </mform-setting-list-style-tab>
    
    <mform-setting-photo-article-list 
      title="添加图片"
      sub-title="最多添加 10张图片，建议尺寸比例16:9，可拖动排序"
      :event-list="eventList"
      :value="value.pictureInfo" 
      @input="updatePictureInfo"
      :form-type="field.formType"
      :language-list="languageList"
      :is-open-multi-language="isOpenMultiLanguage"
    >
    </mform-setting-photo-article-list>
    
  </div>
</template>

<script>
/* mixin */
import SettingMixin from '@src/mform/mixin/setting'
/* model */
import FormStyle from '@model/form/FormStyle.ts'
/* props */
import { settingProps } from '@src/mform/components/props'
/* util */
import * as FormUtil from '@src/component/form/util'
import { findComponentsDownward } from '@src/util/assist'

export default {
  name: 'mform-image-ad-setting',
  mixins: [SettingMixin],
  props: settingProps,
  data() {
    return {
      // 
    }
  },
  computed: {
    value() {
      return {
        ...this.commonSettingValue,
        displayStyle: this.displayStyle,
        pictureInfo: this.pictureInfo
      }
    }
  },
  methods: {
    validate(immediate = false) {
      if (!this.isValidateAll) return
      
      const formItemComponents = findComponentsDownward(this, 'mform-item')
      formItemComponents.forEach(formItemComponent => {
        this.$nextTick(() => {
          formItemComponent.validate && formItemComponent.validate(true, immediate)
        })
      })
    }
  }
};
</script>
