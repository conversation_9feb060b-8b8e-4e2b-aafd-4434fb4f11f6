// @ts-nocheck
import FormImageADSetting from './FormImageADSetting.vue';
import FormImageADPreview from './FormImageADPreview.vue';

import { getSetting } from '@src/mform/util'
import Vue from 'vue';

let FormImageADField = {
  formType: 'pictureAD', // 字段类型
  name: '图片广告',
  isSystem: 0,
  number: 10,
  icon: 'icon-image-fill2',
  forceDelete: true,
  component: {
    preview: FormImageADPreview,
    setting: FormImageADSetting
  },
  packFunction: pack,
  unPackFunction: unpack,
  validateFunction: validate,
};

export function unpack(field)  {
  let setting = getSetting(field.setting)
  let fieldName = field.fieldName || ''
  let pictureInfo = setting.pictureInfo || []
  
  pictureInfo = pictureInfo.map(info => {
    if ((info.pictureUrl && info.pictureUrl != '无链接') || info.urlAddress) {
      info.urlType = 'custom'
    } else {
      info.urlType = 'none'
    }
    
    return {
      title: info.pictureTitle,
      titleLanguage: info.pictureTitleLanguage || {},
      imageUrl: info.imageUrl,
      urlAddress: info.pictureUrl || info.urlAddress,
      urlType: info.urlType,
    }
    
  })
  
  let settingValue = {
    title: setting.specialName,
    titleLanguage: setting.specialNameLanguage || {},
    notShowTitle: setting.specialNameSwitch === false ? false : true,
    description: setting.describe,
    descriptionLanguage: setting.describeLanguage || {},
    notShowDescription: setting.describeSwitch === false ? false : true,
    displayStyle: setting.listStyle
  }
  
  Vue.set(setting, 'pictureInfo', pictureInfo)
  Vue.set(field, 'setting', Object.assign(setting, settingValue))
}

export function pack(field) {
  let originSetting = getSetting(field.setting)
  
  let pictureInfo = originSetting.pictureInfo.map(info => {    
    return {
      pictureTitle: info.title,
      pictureTitleLanguage: info.titleLanguage,
      pictureUrl: info.urlAddress,
      imageUrl: info.imageUrl,
      urlType: info.urlType,
    }
  })
  
  const setting = {
    specialName: originSetting.title,
    specialNameLanguage: originSetting.titleLanguage,
    specialNameSwitch: originSetting.notShowTitle,
    describe: originSetting.description,
    describeLanguage: originSetting.descriptionLanguage,
    describeSwitch: originSetting.notShowDescription,
    listStyle: originSetting.displayStyle,
    pictureInfo: pictureInfo
  }
  
  Vue.set(field, 'setting', setting)
}

export function validate(field) {
  let message = []
  let setting = getSetting(field?.setting)
  let { title, notShowTitle, description, notShowDescription, pictureInfo = [] } = setting
  
  if (!notShowTitle && !title) {
    message.push('请填写标题名称')
  } 
  if (!notShowDescription && !description) {
    message.push('请填写描述信息')
  }
  
  pictureInfo.forEach((info, index) => {
    if (info.urlType == 'custom') {
      
      if (!info.urlAddress || info.urlAddress == '无链接') {
        message.push(`图文广告 图片列表 第${index + 1}项 存在空白链接，请检查`)
      }
    }
    
    if (info.urlType == 'event') {
      if (!info.eventId) {
        message.push(`图文广告 图片列表 第${index + 1}项 存在空白事件链接，请检查`)
      }
    }
    
  })
  
  return message
}

export default FormImageADField;
