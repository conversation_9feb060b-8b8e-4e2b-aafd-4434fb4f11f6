<template>
  <div class="form-preview-group">
    <MFormPreviewHeader :content="commonSettingValue" />

    <template v-if="pictureInfo.length === 0">
      <van-swipe class="my-swipe" :show-indicators="false">
        <van-swipe-item v-for="item in 2" :key="item">
          <div>
            <i class="iconfont icon-image-fill"></i>
            <span>图片</span>
          </div>
        </van-swipe-item>
        <div
          class="custom-indicator"
          slot="indicator"
          v-if="displayStyle === 'image'"
        >
          <i class="iconfont icon-image-fill"></i>
          <span>1/2</span>
        </div>
      </van-swipe>
    </template>
    <template v-else>
      <van-swipe
        v-show="displayStyle === 'image'"
        class="my-swipe"
        :class="previewMode == 'pc' ? 'pcMode' : ''"
        :autoplay="3000"
        @change="onChange"
        :key="previewMode"
        :width="width"
      >
        <van-swipe-item v-for="(item, index) in pictureInfo" :key="index">
          <img
            v-if="item.imageUrl"
            class="image-ad"
            :src="item.imageUrl"
            alt=""
          />
          <div v-else class="no-image">
            <i class="iconfont icon-image-fill"></i>
            <span>图片</span>
          </div>
          <p class="image-title" v-if="item.title">{{ item.title }}</p>
        </van-swipe-item>
        <div
          v-if="pictureInfo.length > 1"
          class="custom-indicator"
          slot="indicator"
        >
          <i class="iconfont icon-image-fill"></i>
          <span>{{ current + 1 }}/{{ pictureInfo.length }}</span>
        </div>
      </van-swipe>
      <template v-if="displayStyle === 'article'">
        <div
          v-for="(item, index) in pictureInfo"
          :key="index"
          style="position: relative"
        >
          <img
            v-if="item.imageUrl"
            class="image-ad"
            :src="item.imageUrl"
            alt=""
          />
          <div v-else class="no-image">
            <i class="iconfont icon-image-fill"></i>
            <span>图片</span>
          </div>
          <p class="image-title" v-if="item.title">{{ item.title }}</p>
        </div>
      </template>
    </template>
  </div>
</template>

<script>
/* mixin */
import PreviewMixin from '@src/mform/mixin/preview';
/* props */
import { previewProps } from '@src/mform/components/props';

export default {
  name: 'mform-image-ad-preview',
  mixins: [PreviewMixin],
  props: previewProps,
  data() {
    return {
      current: 0,
    };
  },
  computed: {
    width() {
      if (this.previewMode == 'mobile') return 370
      return 548
    }
  },
  watch: {
    field: {
      immediate: true,
      deep: true,
      handler(newValue, _) {
        if (newValue) {
          this.current = 0;
        }
      },
    },
  },
  methods: {
    onChange(index) {
      this.current = index;
    },
  },
};
</script>

<style lang="scss" scoped>
.form-preview-group {
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;

  .my-swipe .van-swipe-item {
    div {
      width: 100%;
      height: 210px;
      background: #f5f5f5;
      border-radius: 4px 4px 0px 0px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      i {
        font-size: 28px;
        color: #bfbfbf;
        margin-bottom: 8px;
      }

      span {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #595959;
        line-height: 22px;
      }
    }
  }

  .custom-indicator {
    width: 54px;
    height: 23px;
    position: absolute;
    right: 13px;
    bottom: 22px;
    font-size: 12px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 11px;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;

    i {
      margin-right: 5px;
    }
  }

  .pcMode {
    .image-ad {
      height: 308px;
    }
  }

  .image-ad {
    width: 100%;
    height: 210px;
  }

  .image-title {
    width: 100%;
    height: 24px;
    line-height: 24px;
    position: absolute;
    bottom: 0;
    background: linear-gradient(
      180deg,
      rgba(0, 0, 0, 0) 0%,
      rgba(0, 0, 0, 0.9) 100%
    );
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ffffff;
    margin: 0;
    padding-left: 8px;
    display: flex;
    align-items: flex-start;
  }

  .no-image {
    height: 210px;
    background: #f5f5f5;
    border-radius: 4px 4px 0px 0px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    i {
      font-size: 28px;
      color: #bfbfbf;
      margin-bottom: 14px;
    }

    span {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #595959;
      line-height: 22px;
    }
  }
}
</style>
