<template>
  <div class="shop-setting" v-if="isGrouping">
    <div>
      <div class="title mb-8">商品筛选</div>
    </div>
    <div class="text-tip mb-32"> 默认显示全部已上架的商品，添加条件可过滤商品</div>
    <div class="mb-8">商品菜单</div>
      <div  class="grouping mb-16">
        <el-radio v-model="isOpen" @input="handleSwitchChange" :label="false">单级分组</el-radio>
        <el-radio v-model="isOpen" @input="handleSwitchChange" :label="true">多级分组</el-radio>
      </div>
      <div v-if="isOpen">
        <div class="mb-8">商品分组</div>
      <div  class="grouping mb-16">
        <el-cascader
          v-model="groupingValue"
          class="groupCascader"
          :options="groupOptions"
          :props="groupProps"
   
          @change="changeGroup"
          clearable></el-cascader>
      </div>

      <div class="mb-8">商品类型</div>

      <div  class="type mb-16">
          <el-select
          class="groupCascader"
            v-model="typeValue"
            multiple
   
            @change="changeType"
            placeholder="请选择">
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </div>
      <!-- <div class="mb-8">
          <el-button @click="console" class="groupCascader">点击刷新</el-button>
      </div>
      <div class="text-tip center "> 
          需要手动点击按钮刷新预览商品
      </div> -->
      </div>
  </div>
</template>

<script>
/* mixin */
import SettingMixin from '@src/mform/mixin/setting'
/* props */
import { settingProps } from '@src/mform/components/props'
import { getRootWindow } from '@src/util/dom'

export default {
  name: 'mform-shop-setting',
  mixins: [SettingMixin],
  props: settingProps,
  data() {
    return {
      groupingValue:[],
      groupProps:{
        value: 'id',
        label: 'groupName',
        children: 'children',
        multiple:true
      },
      typeValue:[],
      isOpen:false,
    }
  },
  computed: {
    value() {
      return {
        ...this.commonSettingValue
      }
    },
    groupOptions(){
      return this.formDesignComponent.groupOptions || []
    },
    isGrouping(){
            const RootWindow = getRootWindow(window)
            return RootWindow.grayAuth?.DOOR_SHOP_GROUP || false
        },
    EXTENDED_WARRANTY_CARD(){
      const RootWindow = getRootWindow(window)
      return RootWindow.grayAuth?.EXTENDED_WARRANTY_CARD || false
    },
    typeOptions(){
      let arr =  [
        {
          value: '3',
          label: '物料'
        }, {
          value: '2',
          label: '服务项目'
        }, 
        {
          value: '1',
          label: '备件'
        }, 
      ]
      if(this.EXTENDED_WARRANTY_CARD){
        arr.push({
          value: '4',
          label: '延保卡'
        })
      }
      return arr
    }
  },
  methods:{
    console(){
      this.update(this.typeValue, 'productType')
      this.update(this.groupingValue, 'grouping')
    },
    changeType(){
      this.update(this.typeValue, 'productType')
    },
    changeGroup(){
      this.update(this.groupingValue, 'grouping')
      console.log(this.groupOptions)
    },
    handleSwitchChange(){
      this.update(this.groupOptions, 'groupOptions')
      this.update(this.isOpen, 'isOpenGroup')
    }
  },
  mounted(){
    this.groupingValue = this.value.grouping || []
    this.typeValue = this.value.productType || []
    this.isOpen = this.value.isOpenGroup || false
    this.update(this.groupOptions, 'groupOptions')
  }
};
</script>

<style lang="scss" scoped>
.shop-setting{
  padding: 32px;
  .title{
    font-size: 18px;
    font-weight: 600;
  }
  .text-tip{
  font-size: 12px ;
  color: #595959;
}
.mb-32{
  margin-bottom: 32px;
}
.mb-8{
  margin-bottom: 8px;
}
.mb-16{
  margin-bottom: 16px;
}
.center{
  text-align: center;
}
}
.groupCascader{
  width: 100% !important;
}
</style>