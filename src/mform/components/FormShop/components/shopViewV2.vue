<template>
  <div class="shop-container">
    <van-search
      v-model="keyWord"
      disabled
      shape="round"
      placeholder="请输入商品名称"
    />
    <van-tabs class="mall-tab" v-model="active" :color="color" animated>
      <van-tab title="备件">
        <van-list
          class="list_cart"
          v-if="partList.length"
          v-model="mallLoading"
          :finished="mallFinished"
        >
          <div class="products_box">
            <div
              class="products_list"
              :class="{ sell_out: !item.inventoryNumber }"
              v-for="item in partList"
              :key="item.id"
            >
              <div class="img_div">
                <van-image
                  class="swipe_img"
                  v-if="item.image"
                  :src="item.image"
                  fit="contain"
                ></van-image>
                <van-image v-else :src="defProduct" fit="contain"></van-image>
                <van-image
                  class="sell_out_img"
                  v-if="!item.inventoryNumber"
                  :src="sellOutImg"
                  fit="contain"
                ></van-image>
              </div>
              <div class="product_info_div">
                <h4
                  class="product_tit"
                  :class="fromShopCart ? 'overHideCon-2' : 'overHideCon-1'"
                >
                  {{ item.name }}
                </h4>
                <p class="spec_div overHideCon-1" v-if="fromShopCart">
                  <span>编号：{{ item.serialNumber }}</span>
                </p>
                <p class="spec_div overHideCon-1">
                  <span v-if="item.type">{{ item.type }}</span
                  ><span v-if="item.standard">{{ item.standard }}</span>
                </p>
                <div class="other_info">
                  <div class="price_div">
                    <p class="price">
                      ￥<span>{{ item.salePrice | fixed_2 }}</span>
                    </p>
                    <p class="sales" v-if="item.saleShow">
                      已售 {{ item.saleNumber }}
                    </p>
                  </div>
                  <div v-if="!item.inventoryNumber" class="sell_out_p">
                    商品已失效
                  </div>
                  <button
                    type="button"
                    class="van-stepper__plus"
                    :style="{ background: color }"
                  ></button>
                </div>
              </div>
            </div>
          </div>
        </van-list>
        <div v-else>
          <no-data-view></no-data-view>
        </div>
      </van-tab>
      <van-tab title="服务项目" disabled></van-tab>
    </van-tabs>
    <!-- <i class="iconfont icon-shaixuan1 category"></i> -->
  </div>
</template>

<script>
import { getLocalesOssUrl, getOssUrl } from '@src/util/assets'
const defProduct = getOssUrl('/defProduct.png')
const sellOutImg = getLocalesOssUrl('/sell_out.png')// img-checked

export default {
  data() {
    return {
      keyWord: '',
      active: 0,
      mallLoading: false,
      mallFinished: true,
      fromShopCart: true,
      defProduct,
      sellOutImg,
    };
  },
  props: {
    color: {
      type: String,
      default: '',
    },
    partList: {
      type: Array,
      default: () => [],
    },
  },
  filters: {
    fixed_2: function (value) {
      if (typeof value != 'number' || isNaN(value) || !isFinite(value))
        return value;

      let integer = value >> 0; //取整
      return integer === value ? value + '.00' : value.toFixed(2);
    },
  },
};
</script>

<style lang="scss" scoped>
.shop-container {
  width: 100%;
  background-color: #ffffff;

  .list_cart {
    padding: 0 16px;
    background: #fff;
    overflow: scroll;
    height: 480px;
  }

  .products_box {
    border-top: 1px solid #e8e8e8;
    .products_list {
      display: flex;
      padding: 8px 0 0;
      .img_div {
        width: 70px;
        height: 70px;
        border-radius: 8px;
        overflow: hidden;
        margin-right: 12px;
        position: relative;

        .swipe_img {
          width: 70px;
          height: 70px;
          background: #f8f8f8;
        }
        .sell_out_img {
          width: 36px;
          height: 36px;
          position: absolute;
          top: 50%;
          left: 50%;
          margin-left: -18px;
          margin-top: -18px;
        }
      }
      .product_info_div {
        flex: 1;
        .product_tit {
          font-size: 15px;
          line-height: 22px;
          margin: 0 0 6px;
        }
        .spec_div {
          font-size: 13px;
          font-weight: 400;
          color: #595959;
          line-height: 18px;
          margin-bottom: 4px;
          span {
            margin-right: 6px;
            padding-left: 6px;
            position: relative;
            &::before {
              content: '';
              position: absolute;
              left: 0;
              top: 0;
              width: 1px;
              height: 16px;
              background: #e8e8e8;
            }
            &:first-child {
              padding-left: 0px;
              &::before {
                display: none;
              }
            }
          }
        }
        .other_info {
          display: flex;
          justify-content: space-between;
          margin: 12px 0 16px;
        }
        .price_div {
          display: flex;
          p {
            margin: 0;
          }
          .price {
            line-height: 24px;
            font-size: 13px;
            font-weight: 400;
            color: #ff4d4f;
            span {
              font-size: 15px;
              font-weight: 500;
            }
          }
          .sales {
            line-height: 24px;
            font-size: 12px;
            font-weight: 400;
            color: #8c8c8c;
            margin-left: 4px;
          }
        }
      }
    }
    .sell_out {
      opacity: 0.68;
      .sell_out_p {
        color: #8c8c8c;
      }
    }
  }

  .van-stepper__plus {
    border-radius: 100%;
    width: 24px;
    height: 24px;
    color: #fff;
  }

  .mall-tab ::v-deep .van-tabs__wrap {
    margin-right: 33%;
  }

  .category {
    position: absolute;
    top: 54px;
    right: 0;
    z-index: 20;
    font-size: 16px;
    display: inline-block;
  }
}
</style>
