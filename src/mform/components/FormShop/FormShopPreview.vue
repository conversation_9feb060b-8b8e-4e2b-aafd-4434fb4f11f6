<template>
  <div class="form-preview-group" :style="{ height: styleHeight }">
    <ShopView v-if="!isOpenServiceMall" :part-list="partList" />
    <ShopViewV3 v-else-if="commonSettingValue.isOpenGroup" :part-list="partList" :commonSettingValue="commonSettingValue" :groupOptions="groupOptions" :color="color"></ShopViewV3>
    <ShopViewV2 v-else :color="color" :part-list="partList" />
   
  </div>
</template>

<script>
import { previewProps } from '@src/mform/components/props';
import { findComponentUpward } from '@src/util/assist';
import previewMixin from '@src/mform/mixin/preview'

import ShopView from '@src/mform/components/FormShop/components/shopView.vue';
import ShopViewV2 from '@src/mform/components/FormShop/components/shopViewV2.vue';
import ShopViewV3 from '@src/mform/components/FormShop/components/shopViewV3.vue';
export default {
  name: 'mform-shop-preview',
  props: previewProps,
  mixins: [previewMixin],
  data() {
    return {};
  },
  components: {
    ShopView,
    ShopViewV2, // 新版本商城
    ShopViewV3,//商城分组版本
  },
  computed: {
    formDesignComponent() {
      return findComponentUpward(this, 'mform-design');
    },
    formPreviewComponent() {
      return findComponentUpward(this, 'mform-preview');
    },
    styleHeight() {
      return `${this.formPreviewComponent.$el.offsetHeight}px` || '658px';
    },
    partList() {
      return (
        this.formDesignComponent?.partList ||
        this.formPreviewComponent?.partList ||
        []
      );
    },
    isOpenServiceMall() {
      return (
        this.formDesignComponent?.isOpenServiceMall ??
        this.formPreviewComponent?.isOpenServiceMall
      );
    },
    groupOptions(){
      return this.formDesignComponent?.groupOptions || 
       this.formPreviewComponent?.groupOptions || []
    },
    color() {
      return (
        this.formDesignComponent?.themeColour.split(',')[0] ||
        this.formPreviewComponent?.themeColour.split(',')[0] ||
        ''
      );
    },
  },
  mouted(){
  }
};
</script>

<style lang="scss" scoped>
.form-preview-group {
  padding: 0;
  width: 100%;
  background-color: #ffffff;
}
</style>
