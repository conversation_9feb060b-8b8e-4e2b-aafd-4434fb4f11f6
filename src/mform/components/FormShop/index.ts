// @ts-nocheck
import FormShopPreview from './FormShopPreview.vue';
import FormShopSetting from './FormShopSetting.vue'
import { getSetting } from '@src/mform/util'
import Vue from 'vue';

let FormShopField = {
  formType: 'shop', // 字段类型
  name: '商城',
  isSystem: 0,
  forceDelete: false,
  component: {
    preview: FormShopPreview,
    setting:FormShopSetting
  },
  packFunction: pack,
  unPackFunction: unpack,
  validateFunction: validate,
}

export function pack(field) {
  let originSetting = getSetting(field.setting)
  Vue.set(field, 'setting', originSetting)
}

export function unpack(field) {
  let setting = getSetting(field.setting)
  
  Vue.set(field, 'setting', setting)
}

export function validate(field) {
  
}

export default FormShopField;
