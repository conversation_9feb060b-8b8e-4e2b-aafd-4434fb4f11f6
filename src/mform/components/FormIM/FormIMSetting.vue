<template>
  <div>
    <mform-setting-title title="在线客服"> </mform-setting-title>
    <!-- <div class="im-tip" v-if="isOpenMultiLanguage">
      <i class="el-icon-warning"></i>
      <span>该控件仅支持中文语言下展示</span>
    </div> -->

    <mform-item :label="customerServiceField.displayName">
      <form-select
        :field="customerServiceField"
        :value="value.customerServiceName"
        :source="customerServiceSource"
        @input="value => updateCustomerServiceName(value)"
        placeholder="请选择客服联系通道"
      />
    </mform-item>
    <template v-if="customerServiceName === 'wx'">
      <div class="error-content" v-if="!hasWxAuth">
        未开通微信客服，暂无法使用。<span @click="jumpSetting">前往开通</span>
      </div>
      <mform-item v-else>
        <form-select
          :field="wechatServiceField"
          :value="value.wechatServiceUrl"
          :source="wechatListSource"
          @input="value => updateWechatServiceUrl(value)"
          placeholder="请选择微信客服"
        />
      </mform-item>
    </template>
  </div>
</template>

<script>
/* mixin */
import SettingMixin from '@src/mform/mixin/setting';
/* props */
import { settingProps } from '@src/mform/components/props';
import {
  getFormSettingCustomerServiceField,
  getFormSettingWechatServiceField,
} from '@src/mform/model/MFormSettingFields.ts';
import { PageRoutesTypeEnum } from 'pub-bbx-global/pageType/dist/enum/PageRoutesEnum'
import { openAccurateTab } from '@src/util/platform'
export default {
  name: 'form-im-setting',
  mixins: [SettingMixin],
  props: {
    ...settingProps,
    wechatList: {
      type: Array,
      default: () => [],
    },
    hasWxAuth: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      // customerServiceName: 'shb',
    };
  },
  computed: {
    customerServiceField() {
      return getFormSettingCustomerServiceField(false);
    },
    wechatServiceField() {
      return getFormSettingWechatServiceField(false);
    },
    value() {
      return {
        customerServiceName: this.customerServiceName,
        wechatServiceUrl: this.wechatServiceUrl
      };
    },
    openWeChatService() {
      return !!localStorage.getItem('openWeChatService') || false;
    },
    customerServiceSource() {
      if (this.openWeChatService) {
        return [
          {
            text: '售后宝客服',
            value: 'shb',
          },
          {
            text: '微信客服',
            value: 'wx',
          },
        ];
      }

      return [
        {
          text: '售后宝客服',
          value: 'shb',
        },
      ];
    },
    wechatListSource() {
      return this.wechatList.map(item => {
        return {
          text: item.wechatServiceName,
          value: item.wechatServiceUrl,
        };
      });
    },
  },
  methods: {
    updateCustomerServiceName(value) {
      if (value === 'shb') this.updateWechatServiceUrl('');
      
      this.update(value, 'customerServiceName');
      this.$nextTick(() => {
        this.updateErrorStatus(false);
      });
    },
    updateWechatServiceUrl(value) {
      this.update(value, 'wechatServiceUrl');
      this.$nextTick(() => {
        this.updateErrorStatus(false);
      });
    },
    jumpSetting() {
      // this.$platform.openTab({
      //   close: true,
      //   url: '/pcModule/setting/imSetting',
      //   id: 'ONLINE_CUSTOMER_SERVICE_SETTINGS',
      //   title: '在线客服设置'
      // });
      openAccurateTab({
        type: PageRoutesTypeEnum.PageSettingImSetting,
      })
    },
  },
};
</script>

<style lang="scss" scoped>
.im-tip {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #595959;
  margin-bottom: 21px;

  i {
    font-size: 16px;
    color: #FAAD14;
    margin: 0 8px 0 17px;
  }
}
.error-content {
  margin-left: 136px;
  color: #f56c6c;

  span {
    color: #4553fa;
    cursor: pointer;
  }
}
</style>
