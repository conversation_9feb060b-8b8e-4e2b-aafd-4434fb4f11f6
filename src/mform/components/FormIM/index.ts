// @ts-nocheck
import FormIMPreview from './FormIMPreview.vue';
import FormIMSetting from './FormIMSetting.vue';
import { getSetting } from '@src/mform/util'
import Vue from 'vue';

let FormIMField = {
  formType: 'im', // 字段类型
  name: '在线客服',
  icon: 'icon-zaixiankefu',
  isSystem: 0,
  // isNotShowPreviewList: true,
  forceDelete: false,
  number: 1,
  component: {
    setting: FormIMSetting,
    preview: FormIMPreview,
  },
  packFunction: pack,
  unPackFunction: unpack,
  validateFunction: validate,
}

export function pack(field) {
  let setting = getSetting(field.setting)

  let settingValue = {
    customerServiceName: setting?.customerServiceName || 'shb',
    wechatServiceUrl: setting?.wechatServiceUrl || '',
  }

  Vue.set(field, 'setting', Object.assign(setting, settingValue))
}

export function unpack(field) {
  let originSetting = getSetting(field.setting)
  
  const setting = {
    customerServiceName: originSetting?.customerServiceName || 'shb',
    wechatServiceUrl: originSetting?.wechatServiceUrl || '',
  }
  
  Vue.set(field, 'setting', setting)
}

export function validate(field) {
  let message = []
  let setting = getSetting(field?.setting)
  let { customerServiceName, wechatServiceUrl } = setting

  if (customerServiceName === 'wx') {
    const hasWxAuth = sessionStorage.getItem('hasWxAuth');
    if (!hasWxAuth) {
      message.push('未开通微信客服。')
      return message;
    }

    if (!wechatServiceUrl) {
      message.push('请选择微信客服。')
      return message;
    }
  }
}

export default FormIMField;
