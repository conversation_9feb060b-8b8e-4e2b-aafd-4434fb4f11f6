<template>
  <div class="form-preview-group" :class="previewMode == 'pc' ? 'pcMode' : ''">
    <template
      v-if="
        commonSettingValue.title ||
          commonSettingValue.description ||
          pictureInfo.length
      "
    >
      <MFormPreviewHeader
        :content="commonSettingValue"
        :remove-margin-bottom="true"
        :is-show-more="isShowMore"
      />

      <div class="form-preview-commodity-content">
        <div
          class="form-preview-content-box"
          v-for="item in pictureInfo"
          :key="item.id"
        >
          <img :src="item.imageUrl || goodsDefault" alt="" />
          <div class="form-preview-content-main">
            <h3>{{ item.title }}</h3>
            <span>
              <i>¥</i>
              {{ item.salePrice }}
            </span>
          </div>
        </div>
      </div>
    </template>

    <img v-else :src="initialPic" class="default-img" />
  </div>
</template>

<script>
/* mixin */
import PreviewMixin from '@src/mform/mixin/preview';
/* props */
import { previewProps } from '@src/mform/components/props';

/** assets */
import { getOssUrl } from '@src/util/assets'
const goodsDefault = getOssUrl('/goodsDefault.png') 
export default {
  name: 'mform-commodity-preview',
  mixins: [PreviewMixin],
  props: previewProps,
  data() {
    return {
      initialPic:
        'https://file.shb.ltd/shb-resource/%E5%95%86%E5%93%81%E9%BB%98%E8%AE%A4.png',
      goodsDefault,
    };
  },
  computed: {
    isShowMore() {
      return this.pictureInfo.length > 0;
    },
  },
  filters: {
    interceptString(value, len = 22, hasEllipsis = false) {
      if (!value) return;

      // 正则表达式匹配中文
      const regexp = /[^\x00-\xff]/g;
      // 当字符串字节长度小于指定的字节长度时
      if (value.replace(regexp, 'aa').length <= len) {
        return value;
      }

      // 假设指定长度内都是中文
      const m = Math.floor(len / 2);
      for (let i = m, j = value.length; i < j; i++) {
        // 当截取字符串字节长度满足指定的字节长度
        if (value.substring(0, i).replace(regexp, 'aa').length >= len) {
          return hasEllipsis
            ? `${value.substring(0, i)}...`
            : value.substring(0, i);
        }
      }
      return hasEllipsis ? `${value}...` : value;
    },
  },
};
</script>

<style lang="scss" scoped>
.form-preview-group {
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;
  &.pcMode {
    .form-preview-commodity-content {
      .form-preview-content-box {
        width: calc(25% - 6px);
        margin-right: 8px;
        &:last-child {
          margin-right: 0;
        }
        img {
          height: 110px;
          border-radius: 5px;
        }
        .form-preview-content-main {
          background: #fff;
          padding: 8px 0;
          h3 {
            color: #595959;
          }
        }
      }
    }
  }

  .form-preview-commodity-content {
    display: flex;
    flex-flow: wrap;
    padding: 16px;

    .form-preview-content-box {
      $width: calc((100% - 8px) / 2);
      width: $width;
      display: flex;
      flex-direction: column;
      margin-bottom: 8px;

      img {
        width: 100%;
        height: 167px;
      }

      .form-preview-content-main {
        background: #f7f7f7;
        padding: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        line-height: 18px;

        h3 {
          font-size: 12px;
          color: #262626;
          margin-bottom: 8px;
          @include text-ellipsis-2;
        }

        span {
          font-size: 18px;
          color: #F56C6C;

          i {
            font-size: 13px;
            font-style: normal;
          }
        }
      }
    }

    .form-preview-content-box:nth-child(odd) {
      margin-right: 8px;
    }
  }

  .default-img {
    width: 100%;
  }
}
</style>
