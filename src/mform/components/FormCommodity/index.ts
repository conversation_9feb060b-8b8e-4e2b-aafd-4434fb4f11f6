// @ts-nocheck
import FormCommodityPreview from './FormCommodityPreview';
import FormCommoditySetting from './FormCommoditySetting';
/* util */
import { getSetting } from '@src/mform/util'
/* vue */
import Vue from 'vue';
/* config */
import * as config from '@src/mform/config';

let FormCommodityField = {
  // ! 之前取名不规范，和商城英文重名，但是要改涉及到要刷数据，所以先不改了
  formType: 'mall', // 字段类型
  // formType: 'commodity', // ? 想改成这个
  name: '商品',
  isSystem: 0,
  number: 10,
  icon: 'icon-caidan-chanpin',
  forceDelete: true,
  component: {
    setting: FormCommoditySetting,
    preview: FormCommodityPreview,
  },
  packFunction: pack,
  unPackFunction: unpack,
  validateFunction: validate,
};

export function unpack(field)  {
  let setting = getSetting(field.setting)

  let settingValue = {
    title: setting.specialName,
    notShowTitle: setting.specialNameSwitch === false ? false : true,
    description: setting.describe,
    notShowDescription: setting.describeSwitch === false ? false : true,
    pictureInfo: setting.pictureInfo || []
  }

  Vue.set(field, 'setting', Object.assign(setting, settingValue))
}

export function pack(field) {
    let originSetting = getSetting(field.setting)

    let pictureInfo = originSetting.pictureInfo.map(info => {    
      return {
        id: info.id,
        imageUrl: info.imageUrl,
        repertoryCount: info.repertoryCount,
        salePrice: info.salePrice,
        serialNumber: info.serialNumber,
        title: info.title,
      }
    })
    
    const setting = {
      specialName: originSetting.title,
      specialNameSwitch: originSetting.notShowTitle,
      describe: originSetting.description,
      describeSwitch: originSetting.notShowDescription,
      
      pictureInfo: pictureInfo,
      
    }
    
    Vue.set(field, 'setting', setting)
  }

export function validate(field) {
  let message = []
  let setting = getSetting(field?.setting)
  let { title, notShowTitle, description, notShowDescription, pictureInfo = [] } = setting

  if (!notShowTitle && !title) {
    message.push('请填写标题名称')
  } 
  if (!notShowDescription && !description) {
    message.push('请填写描述信息')
  }
  
  if (!pictureInfo.length) {
    message.push('商品数量不能为0')
  }
  
  return message
}

export default FormCommodityField;
