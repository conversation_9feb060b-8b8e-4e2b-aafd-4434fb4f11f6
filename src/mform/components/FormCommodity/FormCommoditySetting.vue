<template>
  <div class="form-setting-panel form-commodity-setting-panel">
    <mform-setting-title title="商品"> </mform-setting-title>

    <mform-setting-title-name
      ref="MFormSettingTitle"
      :value="value"
      @input="updateTitle"
    >
    </mform-setting-title-name>

    <mform-setting-description-name
      ref="MFormSettingDescription"
      :value="value"
      @input="updateDescription"
    >
    </mform-setting-description-name>

    <mform-setting-divider />

    <mform-item
      ref="UploadFieldItem"
      :prop-field="uploadField"
      :label="uploadField.displayName"
    >
      <div
        class="form-video-setting-panel-tip form-video-setting-panel-tip-first"
      >
        {{ uploadField.placeHolder }}
      </div>
    </mform-item>

    <div class="form-commodity-setting-panel-upload">
      <!-- <draggable
        animation="180"
        class="nested-draggable"
        tag="div"
        :list="value.videoList"
        filter=".undraggable"
      >
      </draggable> -->
      <div
        class="parts-item"
        v-for="(item, index) in value.pictureInfo"
        :key="item.url"
      >
        <img :src="item.imageUrl || imgDefault" alt="" />
        <div class="parts-item-delete" @click="handelDeletePart(index)">
          <i class="iconfont icon-delete"></i>
        </div>
      </div>
      <div class="form-commodity-setting-add-btn" @click="openDialog">
        <i class="iconfont icon-add"></i>
      </div>
    </div>

    <mform-setting-add-parts-dialog
      ref="MFormSettingAddPartsDialog"
      :value="pictureInfo"
      :is-open-service-mall="isOpenServiceMall"
      @input="updatePictureInfo"
    ></mform-setting-add-parts-dialog>
  </div>
</template>

<script>
/* component */
import draggable from 'vuedraggable';
import { FormCommoditySettingUploadField } from '@src/mform/components/FormCommodity/FormCommodityModel.ts';
/* mixin */
import SettingMixin from '@src/mform/mixin/setting';
/* props */
import { settingProps } from '@src/mform/components/props';
import _ from 'lodash';
import { findComponentUpward } from '@src/util/assist'
/* assets */
import { getOssUrl } from '@src/util/assets'
const imgDefault = getOssUrl('/goodsDefault.png') 

export default {
  name: 'mform-commodity-setting',
  mixins: [SettingMixin],
  props: settingProps,
  data() {
    return {
      imgDefault
    };
  },
  computed: {
    value() {
      return {
        ...this.commonSettingValue,
        pictureInfo: this.pictureInfo,
      };
    },
    uploadField() {
      return FormCommoditySettingUploadField;
    },
    formDesignComponent() {
      return findComponentUpward(this, 'mform-design');
    },
    isOpenServiceMall() {
      return this.formDesignComponent?.isOpenServiceMall;
    },
  },
  methods: {
    updatePictureInfo(value) {
      this.update(value, 'pictureInfo');
      this.$nextTick(() => {
        this.updateErrorStatus(false);
      });
    },
    // draggableEndHandler() {
    //   this.$nextTick(() => {
    //     this.updatecommodityList(_.cloneDeep(this.value.pictureInfo));
    //   });
    // },
    openDialog() {
      this.$refs.MFormSettingAddPartsDialog.openDialog();
    },
    handelDeletePart(index) {
      let value = this.pictureInfo;
      if (value.length === 0) return;
      value.splice(index, 1);

      this.update(value, 'pictureInfo');
    },
  },
  components: {
    draggable,
  },
};
</script>

<style lang="scss">
.form-commodity-setting-panel {
  .form-video-setting-panel-tip {
    line-height: 20px;
    color: #8c8c8c;
    font-size: 12px;
  }
  .form-video-setting-panel-tip-first {
    padding-top: 7px;
  }

  .form-commodity-setting-panel-upload {
    display: flex;
    padding: 0 16px;
    margin-top: 8px;

    .nested-draggable {
      display: flex;
    }

    .parts-item {
      width: 64px;
      height: 64px;
      border-radius: 4px;
      margin-right: 12px;
      position: relative;

      img {
        width: 100%;
        height: 100%;
      }

      .parts-item-delete {
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        top: 0;
        left: 0;
        opacity: 0;

        i {
          color: #fafafa;
          font-size: 18px;
        }
      }
    }

    .parts-item:hover {
      cursor: pointer;

      .parts-item-delete {
        opacity: 1;
      }
    }

    .form-commodity-setting-add-btn {
      width: 64px;
      height: 64px;
      text-align: center;
      line-height: 64px;
      background: #f5f5f5;
      border: 1px dashed #d9d9d9;
      border-radius: 4px;
      cursor: pointer;

      i {
        font-size: 18px;
        color: #0e0e0e;
      }
    }
  }
}
</style>
