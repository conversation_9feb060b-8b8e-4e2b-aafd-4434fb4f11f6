// @ts-nocheck
import { Component } from 'vue';
import MFormPreviewHeader from '@src/mform/common/preview/MFormPreviewHeader.vue';
import { findComponentUpward } from '@src/util/assist'
import { transformI18n } from '@src/locales/index.ts';
import { cloneDeep } from 'lodash';
import { useLocaleInject } from '@hooks/useLocale'
import { useFormMultiLanguage } from '@hooks/useFormMultiLanguage'
import { defaultCNKey } from '@src/util/languages.js';
const PreviewMixin: Component = {
  setup(){
    const { locale: formPreviewLocale } = useLocaleInject('formPreviewLocale');
    const { isOpenMultiLanguage } = useFormMultiLanguage();

    return {
      formPreviewLocale,
      isOpenMultiLanguage,
    };
  },
  computed: {
    formDesignComponent() {
      return findComponentUpward(this, 'mform-design');
    },
    fieldSetting() {
      try {
        return JSON.parse(this.field?.setting) || {}
      } catch (error) {
        return this.field?.setting || {}
      }
    },
    title() {
      // if (this.formPreviewLocale === defaultCNKey) {
      //   return (
      //     this.fieldSetting.titleLanguage?.[defaultCNKey] ||
      //     this.fieldSetting.title
      //   );
      // }
      // return transformI18n(this.fieldSetting.titleLanguage, this.$i18n?.locale);
      return (
        this.fieldSetting.titleLanguage?.[this.formPreviewLocale] ||
        this.fieldSetting.titleLanguage?.[defaultCNKey] ||
        this.fieldSetting.title
      )
    },
    titleLanguage() {
      return this.fieldSetting.titleLanguage || {}
    },
    notShowTitle() {
      return this.fieldSetting.notShowTitle || false
    },
    description() {
      // if (this.formPreviewLocale === defaultCNKey) {
      //   return (
      //     this.fieldSetting.descriptionLanguage?.[defaultCNKey] ||
      //     this.fieldSetting.description
      //   );
      // }
      // return this.fieldSetting.descriptionLanguage[this.formPreviewLocale]
      return (
        this.fieldSetting.descriptionLanguage?.[this.formPreviewLocale] ||
        this.fieldSetting.descriptionLanguage?.[defaultCNKey] ||
        this.fieldSetting.description
      )
    },
    descriptionLanguage() {
      return this.fieldSetting.descriptionLanguage || {}
    },
    notShowDescription() {
      return this.fieldSetting.notShowDescription || false
    },
    grouping() {
      return this.fieldSetting.grouping || []
    },
    productType() {
      return this.fieldSetting.productType || []
    },
    isOpenGroup(){
      return this.fieldSetting.isOpenGroup || false
    },
    commonSettingValue() {
      return {
        title: this.title,
        titleLanguage: this.titleLanguage,
        notShowTitle: this.notShowTitle,
        description: this.description,
        descriptionLanguage: this.descriptionLanguage,
        notShowDescription: this.notShowDescription,
        grouping:this.grouping,
        productType:this.productType,
        isOpenGroup: this.isOpenGroup,
      }
    },
    displayStyle() {
      return this.fieldSetting.displayStyle || 'image'
    },
    pictureInfo() {
      const _pictureInfo = cloneDeep(this.fieldSetting.pictureInfo) || [];
      // const _pictureInfo = Object.assign([], this.fieldSetting.pictureInfo) || [];
      _pictureInfo?.forEach(item => {
        // if (this.formPreviewLocale === defaultCNKey) {
        //   item.title = item.titleLanguage?.[defaultCNKey] || item?.title
        // } else {
        //   // item.title = transformI18n(item.titleLanguage, this.$i18n?.locale);
        //   item.title = item.titleLanguage[this.formPreviewLocale]
        // }
        item.title = item.titleLanguage?.[this.formPreviewLocale] || item.titleLanguage?.[defaultCNKey] || item?.title

      })

      return _pictureInfo
    },
    videoList() {
      return this.fieldSetting.videoList || []
    },
    isDisplayStyleImage() {
      return this.displayStyle === 'image'
    },
  },
  components: {
    MFormPreviewHeader,
  }
}

export default PreviewMixin;
