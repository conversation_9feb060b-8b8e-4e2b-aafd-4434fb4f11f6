// @ts-nocheck
/* config */
import * as config from '@src/mform/config';
/* component */
import MFormSettingTitle from '@src/mform/common/setting/MFormSettingTitle.vue'
import MFormSettingTitleName from '@src/mform/common/setting/MFormSettingTitleName.vue'
import MFormSettingListStyleTab from '@src/mform/common/setting/MFormSettingListStyleTab.vue'
import MFormSettingPhotoArticleList from '@src/mform/common/setting/MFormSettingPhotoArticleList.vue'
import MFormSettingDescriptionName from '@src/mform/common/setting/MFormSettingDescriptionName.vue'
import MFormSettingDivider from '@src/mform/common/setting/MFormSettingDivider.vue'
import MFormSettingMark from '@src/mform/common/setting/MFormSettingMark.vue'
import MFormSettingAddPartsDialog from '@src/mform/common/setting/MFormSettingAddPartsDialog.vue'
/* enum */
import TableNameEnum from '@model/enum/TableNameEnum.ts';
import Vue, { Component } from 'vue';
/* form */
import { FormFieldMap } from '@src/mform/components'
/* util */
import { findComponentUpward } from '@src/util/assist'
import { findComponentsDownward } from '@src/util/assist'
import { isFunction, isEmpty } from '@src/util/type'
import { cloneDeep } from 'lodash';

const SettingMixin: Component = {
  data() {
    return {
      isValidateAll: true
    }
  },
  computed: {
    formDesignComponent() {
      return findComponentUpward(this, 'mform-design');
    },
    // 工单：移动端显示的 字段列表
    mobileShowField() {
      return this.fields.filter(field => field.isAppShow === 1);
    },
    // 名字最大长度
    nameMaxLength() {
      return config.FIELD_NAME_LENGTH_MAX;
    },
    // 占位符最大长度
    placeholderMaxLength() {
      return config.FIELD_PLACEHOLER_LENGTH_MAX;
    },
    // 工单移动端展示最大数量
    taskMobileShowMaxLengthMax() {
      return config.TASK_MOBILE_SHOW_MAX_LENGTH_MAX;
    },
    /**
     * @description 允许设为公用字段
     * 1.工单表单或回执表单
     * 2.且不是公共字段
     */
    allowPublicSet() {
      let modeArr = [TableNameEnum.Task, TableNameEnum.TaskReceipt];
      return modeArr.indexOf(this.mode) > -1 && !this.field.isCommon;
    },
    /**
     * @description: 获取当前表单设计器的所有字段
     */
    formFields() {
      let parent = findComponentUpward(this, 'mform-design');
      let form = parent?.value || {};
      return form;
    },
    /**
     * @description: 显示可见性配置
     */
    showVisibleSetting() {
      let parent = findComponentUpward(this, 'mform-design');
      return !parent.isFlowForm;
    },
    fieldSetting() {
      try {
        return JSON.parse(this.field?.setting) || {}
      } catch (error) {
        return this.field?.setting || {}
      }
    },
    title() {
      return this.fieldSetting.title || ''
    },
    titleLanguage() {
      return this.fieldSetting.titleLanguage || {}
    },
    notShowTitle() {
      return this.fieldSetting.notShowTitle || false
    },
    description() {
      return this.fieldSetting.description || ''
    },
    descriptionLanguage() {
      return this.fieldSetting.descriptionLanguage || ''
    },
    notShowDescription() {
      return this.fieldSetting.notShowDescription || false
    },
    grouping() {
      return this.fieldSetting.grouping || []
    },
    productType() {
      return this.fieldSetting.productType || []
    },
    isOpenGroup(){
      return this.fieldSetting.isOpenGroup || false
    },
    commonSettingValue() {
      return {
        title: this.title,
        titleLanguage: this.titleLanguage,
        notShowTitle: this.notShowTitle,
        description: this.description,
        descriptionLanguage: this.descriptionLanguage,
        notShowDescription: this.notShowDescription,
        grouping:this.grouping,
        productType:this.productType,
        isOpenGroup: this.isOpenGroup,
      }
    },
    displayStyle() {
      return this.fieldSetting.displayStyle || 'image'
    },
    pictureInfo() {
      return this.fieldSetting.pictureInfo || []
    },
    isDisplayStyleImage() {
      return this.displayStyle === 'image'
    },
    customerServiceName() {
      return this.fieldSetting.customerServiceName || 'shb'
    },
    wechatServiceUrl() {
      return this.fieldSetting.wechatServiceUrl || ''
    },
    isOpenMultiLanguage() {
      return this.formDesignComponent?.isOpenMultiLanguage;
    },
    languageList() {
      return this.formDesignComponent?.languageList || [];
    },
  },
  watch: {
    'field.isError': {
      handler(newValue, oldValue) {
        this.validate && this.validate(true)
      }
    }
  },
  mounted() {
    if (this.field.isError) {
      this.$nextTick(() => {
        this.validate && this.validate(true)
      })
    }
  },
  methods: {
    updateTitle({ value, prop }) {
      this.update(value, prop)
      this.setNotValidateAll()
      this.validateTitle()
      this.updateErrorStatus(false)
    },
    updateTitleLanguage(value) {
      this.update(value, 'titleLanguage')
    },
    updateDescription({ value, prop }) {
      this.update(value, prop)
      this.setNotValidateAll()
      this.validateDescription()
      this.updateErrorStatus(false)
    },
    updateDescriptionLanguage(value) {
      this.update(value, 'descriptionLanguage')
    },
    updateDisplayStyle(value) {
      this.update(value, 'displayStyle')
    },
    updatePictureInfo(value) {
      this.update(value, 'pictureInfo')
    },
    update(value, prop, isSetting = true) {
      
      const newValue = cloneDeep(value)
      
      this.$emit('input', { value: newValue, prop, isSetting });
    },
    onlyUpdate(value, prop) {
      this.$emit('input', { value, prop, isSetting: true });
    },
    updateErrorStatus(isUpdateError = true) {
      const formType = this.field?.formType
      const fieldConfig = FormFieldMap.get(formType)
      
      if (!fieldConfig || !isFunction(fieldConfig.validateFunction)) return
      
      const message = fieldConfig.validateFunction(this.field) || []
      const isError = !isEmpty(message)
      
      if (isError == this.field.isError) return
      if (!this.field.isError && !isUpdateError) return
      
      Vue.set(this.field, 'isError', isError)
      this.onlyUpdate(isError, 'isError')
      
    },
    validateTitle(immediate = true) {
      let titleComponent = this.$refs.MFormSettingTitle
      const formItemComponents = findComponentsDownward(titleComponent, 'mform-item')
      formItemComponents.forEach(formItemComponent => {
        this.$nextTick(() => {
          formItemComponent.validate && formItemComponent.validate(true, immediate)
        })
      })
    },
    validateDescription(immediate = true) {
      let descriptionComponent = this.$refs.MFormSettingDescription
      const formItemComponents = findComponentsDownward(descriptionComponent, 'mform-item')
      formItemComponents.forEach(formItemComponent => {
        this.$nextTick(() => {
          formItemComponent.validate && formItemComponent.validate(true, immediate)
        })
      })
    },
    validateVideo(immediate = true) {
      const formItemComponent = this.$refs.UploadFieldItem || {}
      formItemComponent.validate && formItemComponent.validate(true, immediate)
    },
    validate(immediate = false) {
      if (!this.isValidateAll) return
      
      const formItemComponents = findComponentsDownward(this, 'mform-item')
      formItemComponents.forEach(formItemComponent => {
        this.$nextTick(() => {
          formItemComponent.validate && formItemComponent.validate(true, immediate)
        })
      })
    },
    setNotValidateAll() {
      this.isValidateAll = false
    },
    setValidateAll() {
      this.isValidateAll = true
    }
  },
  components: {
    [MFormSettingTitle.name]: MFormSettingTitle,
    [MFormSettingTitleName.name]: MFormSettingTitleName,
    [MFormSettingListStyleTab.name]: MFormSettingListStyleTab,
    [MFormSettingPhotoArticleList.name]: MFormSettingPhotoArticleList,
    [MFormSettingDescriptionName.name]: MFormSettingDescriptionName,
    [MFormSettingDivider.name]: MFormSettingDivider,
    [MFormSettingMark.name]: MFormSettingMark,
    [MFormSettingAddPartsDialog.name]: MFormSettingAddPartsDialog,
  }
};

export default SettingMixin;
