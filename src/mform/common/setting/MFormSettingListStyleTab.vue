<template>
  <div class="mform-setting-list-style-tab">
    <div class="mform-setting-list-style-tab-title">
      {{ title }}
      <!-- <span>
        {{ styleDisplayName }}
      </span> -->
    </div>
    
    <div class="mform-setting-list-style-tab-group" v-if="isImage">
      <el-radio-group :value="_value" @input="checkboxChangeHandler">
        <el-radio-button
          v-for="item in styleDisplay"
          :label="item.label"
          :key="item.label"
        >
          <i :class="`iconfont ${item.icon}`"></i> {{ item.text }}
        </el-radio-button>
      </el-radio-group>
    </div>
    
    <div class="mform-setting-list-style-tab-group" v-else-if="isGraphic">
      <el-radio-group :value="_value" @input="checkboxChangeHandler">
        <el-radio-button
          v-for="item in styleDisplay"
          :label="item.label"
          :key="item.label"
        >
          <i :class="`iconfont ${item.icon}`"></i> {{ item.text }}
        </el-radio-button>
      </el-radio-group>
    </div>
    
    <div class="mform-setting-list-style-tab-group" v-else>
      <el-radio-group :value="_value" @input="checkboxChangeHandler">
        <el-radio-button
          v-for="item in styleDisplay"
          :label="item.label"
          :key="item.label"
        >
          <i :class="`iconfont ${item.icon}`"></i> {{ item.text }}
        </el-radio-button>
      </el-radio-group>
    </div>
    
  </div> 
</template>

<script>

const TabData = {
  imageAD: [
    { label: 'image', icon: 'icon-image-fill', text: '轮播海报' },
    { label: 'article', icon: 'icon-pingputupian', text: '一行一个' },
  ],
  graphic: [
    { label: 'image', icon: 'icon-image-fill', text: '图文' },
    { label: 'article', icon: 'icon-font-size', text: '文字' },
  ],
  card: [
    { label: 'image', icon: 'icon-image-fill', text: '大图模式' },
    // { label: 'fall', icon: 'icon-pubuliu1', text: '瀑布流' },
    { label: 'article', icon: 'icon-xiangxiliebiao1', text: '详细列表' },
  ],
}

export default {
  name: 'mform-setting-list-style-tab',
  props: {
    mode: {
      type: String,
      default: ''
    },
    isGraphic: {
      type: Boolean,
      default: true,
    },
    isImage: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '列表样式'
    },
    value: {
      type: String,
      default: 'image'
    }
  },
  computed: {
    _value() {
      return this.value || 'image'
    },
    styleDisplayName() {
      return TabData[this.mode]?.[this._value]?.text || ''
    },
    styleDisplay() {
      return TabData[this.mode] || {}
    }
  },
  methods: {
    checkboxChangeHandler(value) {
      this.update(value)
    },
    update(value) {
      this.$emit('input', value)
    }
  }
};
</script>

<style lang="scss">
.mform-setting-list-style-tab {
  padding: 12px 16px 0;
}
.mform-setting-list-style-tab-title {
  line-height: 22px;
  margin-bottom: 8px;
}
.mform-setting-list-style-tab-group {
  margin-bottom: 24px;
  .el-radio-group {
    height: 32px;
    width: 100%;
    .el-radio-button {
      width: 50%;

      i {
        vertical-align: text-bottom;
      }
    }
    .el-radio-button__inner {
      background-color: #f5f5f5;
      width: 100%;
      height: 32px;
      padding-top: 7px;
      padding-bottom: 7px;
    }
  }
}

.mform-design-setting {
  .el-tabs__nav {
    .el-tabs__item {
      padding: 0 50px !important;
    }
  }
  .el-tabs {
    .el-tabs__nav-wrap,
    .el-tabs__nav,
    .el-tabs__header {
      height: 46px;
    }
    .el-tabs__header {
      margin-bottom: 24px;
    }
  }
}

.mform-setting-list-style-tab-title {
  color: #595959;
  span {
    color: #262626;
    margin-left: 16px;
  }
}
</style>
