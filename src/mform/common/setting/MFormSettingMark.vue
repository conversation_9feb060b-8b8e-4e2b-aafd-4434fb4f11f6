<template>
  <div class="mform-setting-mark">
    <div class="mform-setting-mark-text">
      {{ text }}
    </div>
    <slot></slot>
    <div class="mform-setting-mark-bg"></div>
  </div>
</template>

<script>
export default {
  name: 'mform-setting-mark',
  props: {
    text: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      // 
    }
  }
}
</script>

<style lang="scss">
.mform-setting-mark {
  position: relative;
  height: 100%;
  width: 100%;
  
  &:hover {
    .mform-setting-mark-bg,
    .mform-setting-mark-text {
      display: block;
    }
  }
  
  &-text {
    display: none;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 2px;
    z-index: 1;
    transform: scale(0.8);
    width: 100%;
    font-size: 12px;
    height: 12px;
    line-height: 12px;
    color: #fff;
  }
}
.mform-setting-mark-bg {
  display: none;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: rgba(0,0,0, 0.4);
}
</style>