<template>
  <div class="mform-setting-description-name">
    <mform-item ref="MFormItem" :need-validate="isNeedValidation" :label="descriptionField.displayName" :validation="validation">
      <form-text :field="descriptionField" :value="value.description" @input="updateDescription"/>
      <!-- <el-checkbox class="notshow-title" :value="value.notShowDescription" @input="updateNotShowDescription">
        不可见
      </el-checkbox> -->
      <!-- <div class="language-config" @click="openDialog" v-if="isOpenMultiLanguage">
        <i class="iconfont icon-earth"></i>
      </div> -->
      <div v-if="isOpenMultiLanguage">
        <base-select-language 
          :field="descriptionField"
          :defaultOption="{
            formType:'text',
          }"
          defaultFormType="text"
          :defaultValue="value.description"
          :defaultValueLanguage="value.descriptionLanguage"
          :isFill="false"
          @save="updateFields"
        >
        </base-select-language>
      </div>
    </mform-item>
  </div>
</template>

<script>
/* model */
import { getFormSettingDescriptionField } from '@src/mform/model/MFormSettingFields.ts'
/* util */
import _ from 'lodash'
import { findComponentsDownward } from '@src/util/assist'
import MultilingualDialog from '@src/component/compomentV2/MultilingualDialog/index.vue';

export default {
  name: 'mform-setting-description-name',
  components: {
    MultilingualDialog,
  },
  props: {
    isRequired: {
      type: Boolean,
      default: false,
    },
    value: {
      type: Object,
      default: () => ({})
    },
    languageList: {
      type: Array,
      default: () => [],
    },
    isOpenMultiLanguage: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isNotNull: false,
    }
  },
  computed: {
    isNeedValidation() {
      if (this.value.notShowDescription) return false;
      
      return this.isRequired
    },
    descriptionField() {
      return getFormSettingDescriptionField(this.isNotNull)
    },
    settingFields() {
      return [this.descriptionField]
    }
  },
  watch: {
    isRequired: {
      handler(newValue) {
        this.isNotNull = newValue && !this.value.notShowDescription
      },
      immediate: true
    }
  },
  methods: {
    updateDescription(value) {
      this.update(value, 'description')

      const _value = Object.assign({}, this.value.descriptionLanguage)
      _value['zh'] = value
      this.update(_value, 'descriptionLanguage')
    },
    updateNotShowDescription(value) {      
      this.isNotNull = !value
      this.update(value, 'notShowDescription')
      
      this.$nextTick(() => {
        this.validate()
        
        setTimeout(() => {
          if (value) {
            this.$refs.MFormItem.resetValidationStatus()
          }
        })
        
      })
      
    },
    update(value, prop) {
      this.$emit('input', { prop, value })
    },
    validate() {
      if (!this.isRequired) return
      
      const formItemComponents = findComponentsDownward(this, 'mform-item')
      formItemComponents.forEach(formItemComponent => {
        formItemComponent.validate && formItemComponent.validate()
      })
    },
    validation() {
      if (!this.isRequired) return Promise.resolve(null)
      
      return new Promise((resolve, reject) => {
        
        if(!this.value.notShowDescription && !this.value.description) {
          return resolve('请填写描述信息');
        }
        
        resolve(null);
      })
    },
    openDialog() {
      if (this.value.descriptionLanguage['zh'] !== this.value.description) {
        const _value = Object.assign({}, this.value.descriptionLanguage)
        _value['zh'] = this.value.description
        this.$refs.multilingualDialogRef.openDialog(_value);
      } else {
        this.$refs.multilingualDialogRef.openDialog(this.value.descriptionLanguage);
      }
    },
    updateFields(value) {
      this.update(value['zh'], 'description')
      this.update(value, 'descriptionLanguage')
    }
  }
};
</script>

<style lang="scss">
.mform-setting-title-name {
  margin-top: 12px;
  .form-text {
    height: 28px;
    input {
      height: 28px;
    }
    input::-webkit-input-placeholder{
      color: #BFBFBF;
    }
  }
  .el-checkbox {
    padding-left: 0;
    padding-top: 8px;
  }
}

.mform-setting-description-name {
  margin-bottom: 12px;
  & > .form-item {
    align-items: baseline;

    & > label {
      width: 50px;
    }
    & > .form-item-control {
      max-width: calc(100% - 46px);
      display: flex;
      flex-flow: column;
      & > label {
        width: 60px;
        margin-left: 10px;
        padding-top: 0;
        padding-left: 0;
      }
    }
  }
}

.mform-setting-description-name {
  .form-item-control-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    & > label {
      padding-top: 0;
      padding-left: 0;
      margin-left: 10px;
      width: 80px;
    }
  }
}
</style>

<style lang="scss" scoped>
.language-config {
  width: 30px;
  height: 28px;
  text-align: center;
  line-height: 28px;
  background: #F5F8FA;
  border-radius: 4px;
  border: 1px solid #CBD6E2;
  cursor: pointer;
  margin-left: 5px;
}
</style>
