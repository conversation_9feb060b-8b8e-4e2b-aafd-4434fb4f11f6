<template>
  <el-dialog
    class="replace-image-dialog"
    title="更换图片"
    :visible.sync="dialogVisible"
    width="400px"
  >
    <div class="replace-image-dialog-container">
      <div class="system-image">
        <div class="system-image-title">
          <h3>系统图标</h3>
        </div>
        <div class="system-image-list">
          <div
            class="system-image-list-item"
            :class="{
              active: index === currentImageIndex,
            }"
            v-for="(item, index) in graphicInitImage"
            :key="item"
            @click="handleChangeImage(index)"
          >
            <img :src="item" />
            <i class="iconfont icon-morenyixuan"></i>
          </div>
        </div>
      </div>

      <div class="custom-image">
        <el-upload
          action="string"
          :before-upload="onBeforeUploadImage"
          :http-request="uploadImagePic"
          :limit="10000"
          accept=".jpg,.jpeg,.png"
          :show-file-list="false"
        >
          <div>
            <i class="iconfont icon-upload1"></i>
            <span>自定义图标</span>
          </div>
        </el-upload>
        <span>建议上传100*100尺寸图片</span>
      </div>
    </div>
    <div slot="footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="handleSave">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import Uploader from 'packages/BaseUpload/uploader';

export default {
  name: 'mform-setting-replace-image-dialog',
  inject: ['addNeedServerDeleFiles'],
  data() {
    return {
      dialogVisible: false,
      currentImageUrl: '',
    };
  },
  emits: ['updateReplaceImageUrl'],
  props: {
    graphicInitImage: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    currentImageIndex() {
      if (this.graphicInitImage.length === 0) return -1;

      return this.graphicInitImage.indexOf(this.currentImageUrl);
    },
  },
  methods: {
    openDialog(imageUrl) {
      this.dialogVisible = true;

      this.currentImageUrl = imageUrl;
    },
    handleChangeImage(index) {
      this.addNeedServerDeleFiles([this.currentImageUrl])
      this.currentImageUrl = this.graphicInitImage[index];
    },
    onBeforeUploadImage(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG) {
        this.$message.error('上传图片只能是 jpg/png 格式!');
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2mb!');
      }

      return isJPG && isLt2M;
    },
    uploadImagePic(param) {
      Uploader.upload(param.file, '/files/upload')
        .then(result => {
          if (result.status != 0) {
            this.$message({
              message: `${result.message}`,
              duration: 1500,
              type: 'error',
            });
            return;
          }

          let file = result.data;
          let item = {
            uid: param.file.uid,
            id: file.id,
            filename: file.fileName,
            // 如果后端返回url,必须使用。如果后端不返回，需要拼接
            url: file.ossUrl || file.url || `/files/get?fileId=${file.id}`,
            fileSize: file.fileSizeStr,
          };
          this.addNeedServerDeleFiles([this.currentImageUrl])
          this.currentImageUrl = item.url;
          this.handleSave();
        })
        .catch(err => {
          console.warn(err);
        })
        .finally(() => {
          //
        });
    },
    handleSave() {
      this.$emit('updateReplaceImageUrl', this.currentImageUrl);

      this.dialogVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.system-image {
  &-title {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
  }

  &-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-top: 9px;

    &-item {
      width: 64px;
      height: 64px;
      background: #ffffff;
      border-radius: 2px;
      border: 1px solid #e8e8e8;
      margin: 0 12px 12px 0;
      position: relative;

      img {
        width: 100%;
        height: 100%;
      }

      i {
        display: none;
        position: absolute;
        right: 0;
        top: 0;
        font-size: 12px;
        color: $color-primary-light-6;
      }
    }

    &-item:nth-child(4n) {
      margin-right: 0;
    }

    .active {
      border: 1px solid $color-primary-light-6;

      i {
        display: block;
      }
    }

    &-check {
      position: absolute;
      top: 0;
      right:0;
      width: 10px;
      height: 10px;
    }
  }
}

.custom-image {
  display: flex;
  align-items: center;
  margin-top: 9px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);

  .el-upload {
    margin-right: 12px;

    div {
      width: 120px;
      height: 32px;
      border-radius: 3px;
      background: #ffffff;
      border: 1px solid rgba(0, 0, 0, 0.15);
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);

      span {
        margin-left: 10px;
      }
    }
  }
}

::v-deep .el-dialog__body {
  padding: 20px 32px;
}
::v-deep .el-input-group__append,
.el-input-group__prepend {
  border-radius: 0 4px 4px 0;
}
</style>
