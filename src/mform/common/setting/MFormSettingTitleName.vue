<template>
  <div class="mform-setting-title-name">
    <mform-item ref="MFormItem" :label="titleField.displayName" :validation="validation">
      <form-text :field="titleField" :value="value.title" @input="updateTitle"/>
      <!-- <el-checkbox v-if="needTitleShowControl" class="notshow-title" :value="value.notShowTitle" @input="updateNotShowTitle">
        不可见
      </el-checkbox> -->
      <!-- <div class="language-config" @click="openDialog" v-if="isOpenMultiLanguage">
        <i class="iconfont icon-earth"></i>
      </div> -->
      <div v-if="isOpenMultiLanguage">
        <base-select-language 
          :field="titleField"
          :defaultOption="{
            formType:'text',
          }"
          defaultFormType="text"
          :defaultValue="value.title"
          :defaultValueLanguage="value.titleLanguage"
          :isFill="false"
          @save="updateFields"
        >
        </base-select-language>
      </div>
    </mform-item>
    
    <!-- <MultilingualDialog 
      ref="multilingualDialogRef"
      title="多语配置"
      :language-list="languageList"
      @update="updateFields"
    /> -->
  </div>
</template>

<script>
/* model */
import { getFormSettingTitleField } from '@src/mform/model/MFormSettingFields.ts'
/* util */
import * as FormUtil from '@src/component/form/util'
import _ from 'lodash'
import { findComponentsDownward } from '@src/util/assist'
import MultilingualDialog from '@src/component/compomentV2/MultilingualDialog/index.vue';

export default {
  name: 'mform-setting-title-name',
  components: {
    MultilingualDialog,
  },
  props: {
    isRequired: {
      type: Boolean,
      default: false,
    },
    maxLength: {
      type: Number,
      default: 20
    },
    value: {
      type: Object,
      default: () => ({})
    },
    needTitleShowControl: {
      type: Boolean,
      default: true
    },
    languageList: {
      type: Array,
      default: () => [],
    },
    isOpenMultiLanguage: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isNotNull: false,
    }
  },
  computed: {
    isNeedValidation() {
      if (this.value.notShowTitle) return false;
      
      return this.isRequired
    },
    titleField() {
      return getFormSettingTitleField(this.maxLength, this.isNotNull)
    },
    settingFields() {
      return [this.titleField]
    }
  },
  watch: {
    isRequired: {
      handler(newValue) {
        this.isNotNull = newValue && !this.value.notShowTitle
      },
      immediate: true
    }
  },
  methods: {
    updateTitle(value) {
      this.update(value, 'title')

      const _value = Object.assign({}, this.value.titleLanguage)
      _value['zh'] = value
      this.update(_value, 'titleLanguage')
    },
    updateNotShowTitle(value) {
      this.isNotNull = !value
      this.update(value, 'notShowTitle')
      
      this.$nextTick(() => {
        this.validate()
        
        setTimeout(() => {
          if (value) {
            this.$refs.MFormItem.resetValidationStatus()
          }
        })
        
      })
    },
    update(value, prop) {
      this.$emit('input', { value, prop })
    },
    validate() {
      if (!this.isRequired) return
      
      const formItemComponents = findComponentsDownward(this, 'mform-item')
      formItemComponents.forEach(formItemComponent => {
        formItemComponent.validate && formItemComponent.validate()
      })
    },
    validation(){
      if (!this.isRequired) return Promise.resolve(null)
      
      return new Promise((resolve, reject) => {
        
        if (this.value.notShowTitle) {
          return resolve(null);
        }
        
        if(!this.value.notShowTitle && !this.value.title) {
          return resolve('请填写标题名称');
        } 
        resolve(null);
      })
    },
    openDialog() {
      if (this.value.titleLanguage['zh'] !== this.value.title) {
        const _value = _.cloneDeep(this.value.titleLanguage)
        _value['zh'] = this.value.title
        this.$refs.multilingualDialogRef.openDialog(_value);
      } else {
        this.$refs.multilingualDialogRef.openDialog(this.value.titleLanguage);
      }
    },
    updateFields(value) {
      this.update(value['zh'], 'title');
      this.update(value, 'titleLanguage')
    }
  }
};
</script>

<style lang="scss">
.mform-setting-title-name {
  margin-top: 12px;
  margin-bottom: 12px;
  .form-text {
    height: 32px;
    input {
      height: 32px;
    }
    input::-webkit-input-placeholder{
      color: #BFBFBF;
    }
  }
  .el-checkbox {
    padding-left: 0;
    padding-top: 8px;
  }
  
  & > .form-item {
    align-items: baseline;

    & > label {
      width: 50px;
    }
    & > .form-item-control {
      max-width: calc(100% - 46px);
      display: flex;
      flex-flow: column;
      & > label {
        width: 60px;
        margin-left: 10px;
        padding-top: 0;
        padding-left: 0;
      }
    }
  }
}

.mform-setting-title-name {
  .form-item-control-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    & > label {
      padding-top: 0;
      padding-left: 0;
      margin-left: 10px;
      width: 80px;
    }
  }
}
</style>

<style lang="scss" scoped>
.language-config {
  width: 30px;
  height: 28px;
  text-align: center;
  line-height: 28px;
  background: #F5F8FA;
  border-radius: 4px;
  border: 1px solid #CBD6E2;
  cursor: pointer;
  margin-left: 5px;
}
</style>
