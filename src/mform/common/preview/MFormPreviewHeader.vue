<!--  -->
<template>
  <div class="form-preview-container">
    <div class="form-preview-group-header">
      <template v-if="content.title">
        <img
          class="preview-titleBg-img"
          :src="cardTitleBgImage"
          alt=""
        />
        <h2
          :style="{
            marginBottom: !content.description ? '14px' : '8px',
          }"
        >
          <span>{{ content.title }}</span>
        </h2>
      </template>
      <p
        v-if="content.description"
        :style="{
          marginTop: !content.title ? '14px' : 0,
          marginBottom: removeMarginBottom ? 0 : '14px',
        }"
      >
        {{ content.description }}
      </p>
    </div>

    <span class="form-preview-header-btn" v-if="isShowMore">
      更多 <i class="iconfont icon-right1"></i>
    </span>
  </div>
</template>

<script>
import { getOssUrl } from '@src/util/assets'
const cardTitleBgImage = getOssUrl('/cardTitleBgImage.png')
export default {
  name: 'mform-preview-header',
  data(){
    return {
      cardTitleBgImage
    }
  },
  props: {
    content: {
      type: Object,
      default: () => ({}),
    },
    removeMarginBottom: {
      type: Boolean,
      default: false,
    },
    isShowMore: {
      type: Boolean,
      default: false,
    },
  },
};
</script>

<style lang="scss" scoped>
.form-preview-container {
  display: flex;
  justify-content: space-between;
  // align-items: flex-end;

  .form-preview-group-header {
    display: flex;
    flex-direction: column;
    padding: 0 16px;

    h2 {
      font-size: 18px;
      color: #262626;
      font-family: PingFangSC-Regular, PingFang SC;
      margin: 14px 0 10px;
      position: relative;
    }

    .preview-titleBg-img {
      width: 40px;
      height: auto;
      position: absolute;
      top: 20px;
      left: 0;
    }

    p {
      font-size: 12px;
      color: #8c8c8c;
      font-family: PingFangSC-Regular, PingFang SC;
    }
  }

  .form-preview-header-btn {
    font-size: 13px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #8c8c8c;
    line-height: 20px;
    margin: 16px 18px 0 0;

    i {
      font-size: 12px;
    }
  }
}
</style>
