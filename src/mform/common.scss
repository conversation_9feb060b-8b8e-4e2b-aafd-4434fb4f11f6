.mform-design {
  width: 100%;
  height: calc(100% - 50px);
  
  display: flex;
  flex-flow: row nowrap;
  font-size: 14px;
}

.form-design-panel {
  width: 220px;
  min-width: 220px;
  height: 100%;
  user-select: none;
  background-color: #FAFAFA;
  border-radius: 4px;
  .form-design-left{
    background-color: #fff;
    padding: 0 14px;
    overflow: auto;
    height: 100%;
    // margin-bottom: 20px;
    .form-design-widget{
      margin-bottom: 24px;
   
    }
  }
}

.form-design-tabs {
  display: flex;
  flex-flow: row nowrap;
}

.form-design-withSys .form-design-tab{
  text-align: center;
}

.form-design-tab {
  padding: 12px 0 6px 0;
  flex: 1;
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  font-family: PingFangSC-Medium;
}

.form-design-tab-active{
  color: $color-primary;
  border-bottom-color: $color-primary;
}

.form-design-tabs-content {
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
}

.form-design-field-wrap {
  margin: 10px 0 0 0;
  cursor: move;
  position: relative;

  &.disabled {
    opacity: .4;
    cursor: not-allowed;

    .form-design-field:hover {
      background: none !important;
    }

    i.iconfont {
      color: #bfbfbf;
    }
  }
  .tips{
    font-size: 18px;
    position: absolute;
    top: 0;
    right: 0;
    color: #262626 !important;
  }

  .checked {
    background-color: #FFFFFF;
    box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.1);
  }
}

.form-design-field {
  width: 90px;
  height: 80px;
  line-height: auto;
  padding: 0;
  text-align: center;
  // border: 1px solid #D8D8D8;
  border: 0;
  font-size: 12px;
  transition: background-color ease .15s;
  border-radius: 3px;
  display: flex;
  flex-direction: column;
  justify-content: center;

  .anticon{
    margin-right: 0px;
  }

  .field-name {
    flex: 1;
    @include text-ellipsis();
    word-break: break-all;
  }

  .field-title {
    color: #8C8C8C;
    line-height: 0;
    margin: 10px 0 4px;
  }

  .field-number {
    font-size: 12px;
    color: #BFBFBF;
  }

  &:hover {
    background-color: #FFFFFF;
  }

  i.iconfont {
    font-size: 24px;
    margin: 0;
    width: auto;
    height: 24px;
    color: $color-primary-light-6;
  }
}

.form-design-main {
  // width: calc(100% - 700px);
  // min-width: 500px;
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100%;
  margin: 0px 12px;
  border-top: none;
  border-bottom: none;
  user-select: none;
  // background: #fff;
  background: none;
  border-radius: 4px;
  // overflow: auto;
  .form-design-config {
    width: 100%;
    text-align: center;
    position: relative;
    margin: 12px 0;

    span {
      font-size: 14px;
      color: #595959;
    }

    .form-design-language {
      position: absolute;
      top: -10px;
      right: calc(50% - 375px / 2);
      font-size: 12px;
      width: 100px;
      cursor: pointer;

      i {
        font-size: 18px;
        vertical-align: text-bottom;
        margin-left: 6px;
      }
    }
  }
  .form-design-box{
    height: calc(100% - 44px);
    padding: 0;
    display: flex;
    // justify-content: center;
    // align-items: center;
    padding: 0 0 16px 0;
  }
  // 手机边框
  .form-design-center {
    background-color: #F5F5F5;
    background-size: 100% 100%;
    background: none;

    border-radius: 12px;
    box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.1);
    position: relative;
    max-height: calc(100% - 122px);
    width: 375px;
    margin: 0 auto;
    height: 100%;

    .form-design-header {
      height: 62px;
      width: 100%;
      background: url(../assets/img/portalViewHeader.png) no-repeat;
      background-size: 100%;
      display: flex;
      justify-content: center;
      align-items: flex-end;

      h3 {
        width: 250px;
        text-align: center;
        @include text-ellipsis();
      }
    }

    .form-design-footer {
      height: 60px;
      width: 100%;
    }
  
    .form-design-phone {
      position: static;
      // top: 12px;
      // left: 12px;
      // right: 12px;
      // bottom: 12px;
      height: 100%;
      width: 100%;
      border: 1px solid #edf0f4;
      border-top: none;
      background-color: #fafafa;
      box-shadow: none;
      padding-bottom: 8px;
  
      &::-webkit-scrollbar-thumb {
        border-radius: 3px;
      }
  
    }
  }
  .form-design-hidden{
    font-size: 12px;
    color: $color-primary-light-6;
    height: 30px;
    text-align: right;
    margin: 12px 12px 0 0;
    cursor: pointer;
    font-family: PingFangSC-Medium; 
    display: flex;
    justify-content: flex-end;
    .iconfont{
      font-size: 12px;
      margin-right: 5px;
    }
  }

}
// @media screen and (max-width:1440px ) {
//   .form-design-main .form-design-center{
//     height: 86%;
//   }
// }
// @media screen and (max-width:1280px ) {
//   .form-design-main .form-design-center{
//     height: 80%;
//   }
// }

.form-design-setting {
  min-width: 250px;
  // width: 345px;
  width: 430px;
  height: auto;
  background-color: #fff;
  overflow: auto;
  border-radius: 4px;

  .ql-snow .ql-tooltip {
    left: 0 !important;
  }
}

// 公用字段设置组件
.form-design-setting-disabled {
  cursor: not-allowed;

  .common-field-setting {
    padding: 23px 12px 0;
    cursor: auto;

    &-btn {
      button {
        width: 128px;
        padding: 9px;
        margin: 0;

        &:first-child {
          margin-right: 8px;
        }
      }
    }
  }

  .form-setting-panel {
    opacity: .4;
    pointer-events: none;
  }
}

// 修改控件配置弹窗
.field-setting-modal {
  @include mask();
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 99;

  .base-panel {
    display: flex;
    flex-direction: column;

    &-title {
      min-height: 44px;
      padding: 6px 12px;
    }

    &-content {
      flex: 1;
      overflow: auto;
  
      .form-design-warning {
        margin: 12px 12px 0;
      }
    }
  
    &-footer {
      min-height: 52px;
      padding: 10px 20px;
      text-align: right;
      border-top: 1px solid #f2f8f7;
    }
  }
}

.form-setting-panel-title{
  line-height: 27px;
  font-size: 14px;
  font-weight: bold
}

.form-design-notification-content{
  padding-top: 10px;
  max-height: 320px;

  p{
    line-height: 24px;
  }
}

.form-design-field-empty{
  flex: 1;
  text-align: center;
  color: #9a9a9a;
  padding-top: 25px;
}

// ------------ setting ------------
.form-setting-panel {
  padding: 12px;
  height: 100%;
  & > h3 {
    line-height: 24px;
    padding: 3px 0;
    font-size: 14px;
    margin: 0 0 8px 0;
  }

  h4{
    margin-bottom: 8px;
  }

  .icon-question {
    font-size: $font-size-small;
    color: $text-color-secondary;
    font-weight: normal;
    margin-left: 3px;
  }
  .form-date-type{
    margin-top: 8px;
  }

  .form-setting-select {
    width: 100%;
    margin-top: 8px;
  }
  .el-tabs__nav {
    display: flex;
    width: 100%;
    .el-tabs__item {
      width: 50%;
      display: flex;
      justify-content: center;
    }
  }
}

.form-setting-group {
  margin-bottom: 24px;

  &-small {
    margin-bottom: 12px;
  }

  input[type='text'],
  textarea {
    width: 100%;
  }

  label {
    margin: 8px 5px 0 0;
    vertical-align: middle;
    cursor: pointer;
  }

  input[type="checkbox"] {
    margin-right: 3px;
  }

  textarea {
    display: block;
  }

  &.form-setting-item {
    .form-item-title {
      font-family: PingFangSC-Medium;
      margin-bottom: 0;
    }
  
    label.el-checkbox {
      margin-top: 8px;
    }
  }
}
 
.form-common-setting {
  &-panel {
    display: flex;
    justify-content: space-between;
  }

  &-title {
    &-name {
      margin-right: 4px;
      font-family: PingFangSC-Medium;
    }

    &-remind,
    .iconfont {
      font-size: $font-size-small;
      color: $text-color-secondary;
      font-weight: normal;
    }

    .iconfont {
      margin-right: 4px;
    }
  } 
}

// ------------ preview ------------
.form-preview-group {
  display: flex;
  flex-flow: row nowrap;
  overflow: hidden;
  padding: 14px 10px;
  background-color: #fff;

  label {
    display: block;
    margin: 0;
    width: 110px;
    line-height: 20px;

    @include text-ellipsis();
  }
}

.form-preview-mock {
  flex: 1;
  overflow: hidden;
  pointer-events: none;
  text-align: right;
}

.form-preview-control {
  width: 100%;
  border-radius: 4px;
  // padding: 0 5px;
  line-height: 20px;
  height: 20px;
  color: #9a9a9a;
  text-align: right;
  margin: 0;
  @include text-ellipsis();
}

.form-preview-withIcon {
  padding-right: 24px;
  position: relative;

  i.iconfont {
    position: absolute;
    right: 0;
    top: 0;
    width: 20px;
    height: 20px;
    line-height: 20px;
    font-size: 18px;
  }
}

.form-preview-notNull {
  color: red;
}

// ------------ design ------------
.form-design-phone {
  position: relative;
  box-shadow: 0 0 8px rgba(0, 0, 0, .15);
  width: 100%;
  max-width: 414px;
  min-width: 240px;
  height: 100%;
  min-height: 50%;
  margin: 0 auto;
  border-radius: 1px;
  background-color: #F2F2F2;;
  overflow: visible;
  overflow-x: visible;
  overflow-y: auto;
}

.form-design-tip {
  background: url('../assets/img/form-design-tip.png') no-repeat 50%;
  background-size: 239px 96px;
  position: relative;
  min-height: 300px;

  p {
    text-align: center;
    position: absolute;
    bottom: 70px;
    left: 0;
    right: 0;
    color: #999;
    pointer-events: none;
    margin: 0;
  }
}

.form-design-silence {
  .form-design-preview:hover {
    border-color: transparent !important;
  }

  .form-design-operation {
    display: none !important;
  }
}

.form-design-ghost {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9;
  cursor: move !important;
  background-color: rgba(255, 255, 255, .95);
  box-shadow: 0 0 8px rgba(0, 0, 0, .125);

  .form-design-field {
    background-color: #fff;
  }
}

.form-design-preview {
  cursor: move;
  position: relative;
  border: 1px dashed transparent;
  margin-top: 0;

  &.form-design-selected {
    border: 2px solid $color-primary !important;
    box-sizing: border-box;
  }
  &.form-design-error {
    border: 2px solid $color-danger !important;
    box-sizing: border-box;
  }

  &.disabled {
    cursor: default;
    border: none;
  }
  
  &:hover {
    border-color: $color-primary;

    & > .form-design-operation {
      visibility: initial;
    }
  }
}

.form-design-dragging {
  border-color: $color-primary !important;
  border-style: solid !important;
  opacity: .65;
}
.form-design-operation{
  display: flex;
  justify-content: flex-start;
  position: absolute;
  padding: 0px 8px;
  top: 0;
  right: -1px;
  height: 22px;
  line-height: 22px;
  background: #E5E5E5;
  border-radius: 9px;
  visibility: hidden;
}
.form-design-divider-separator{
  width: 1px;
  height: .9em;
  margin: 5px 6px 0 6px;
  vertical-align: middle;
  background-color: #D9D9D9;
}

.form-design-preview-btn {
  text-align: center;
  border: none;
  color: #999999;
  outline: none;
  height: 22px;
  padding: 0;
  margin: 0;
  z-index: 9;
  cursor: pointer;
  &:hover {
   color: $color-danger;
  }

  i {
    font-size: 14px;
  }
}
.form-design-preview-delete{
  &:hover {
    color: $color-danger;
  }
}
.form-design-preview-hidden{
  &:hover {
    color: $color-primary;
  }
}


.form-design-cover {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 8;
  cursor: move;
}

.form-preview {
  // height: 100%;
  // overflow-y: scroll;
}

.form-preview-empty {
  background: url(./../assets/img/form/form-design-empty.png) no-repeat 50% 40%;
  background-size: 296px;
  height: 100%;
}

// ------------ form-view ------------
.form-view{
  background-color: #fff;
  padding: 5px 0;
}

.form-view-row {
  display: flex;
  justify-content: flex-start;
  padding: 8px 20px;
  color: $text-color-primary;

  label {
    line-height: 20px;
    width: 94px;
    flex-shrink: 0;
    margin: 0 12px 0 0;
    color: $text-color-regular;
    word-break: break-all;
  }

  .form-view-row-content{
    line-height: 20px;
    flex: 1;
    overflow-x: hidden;
    color: $text-color-primary;
    word-break: break-word;
    // white-space: pre-wrap;


    .iconfont {
      color: $color-primary;
      &:hover {
        cursor: pointer;
      }
    }
  }
  .link{
    color: $color-primary-light-6;
    cursor: pointer;
    i{
      font-style: inherit;
    }
  }
}

.form-view-textarea-preview{
  white-space: pre-line;
}

.form-view-textarea-content {
  white-space: pre-wrap;
}

.section-title {
  padding: 0 32px 0 20px;
  font-size: 14px;
  color: $color-primary;
  background: $color-regular;
  font-weight: normal;
  position: relative;
  height: 48px;
  background: #FAFAFA;
  line-height: 48px;
  font-size: 16px;
  font-weight: bold;
  color: #262626;
  margin-bottom: 0;


  .iconfont {
    position: absolute;
    right: 10px;
    height: 48px;
    line-height: 48px;
    font-size: 14px;
    color: #262626;
    
    &:hover {
      color: $color-primary;
      cursor: pointer;
    }
  }

  .reversal {
    transform: rotateZ(180deg);
  }
}

.form-view-group{
  padding: 10px;
  border-radius: 2px;
  background-color: #fff;
}

.form-view-location-not {
  color: #9a9a9a !important;
}

.link-text {
  color: $color-primary !important;
  cursor: pointer;
  margin-right: 10px;
}

.form-view-info-content {
  background-color: #FAFAFA;
  padding: 3px 5px;
}

// 电子签名、客户签名
.form-view-autograph-content {
  max-width: 300px;
  height: 100px;
  border: 1px dashed #aaa;

  img {
    width: 100%;
    height: 100%;

    &[src=""], &:not([src]) {
      opacity: 0;
    }
  }
}

// ------------ form-builder ------------
.form-builder{
  padding: 10px;
  background-color: #fff;
}

.form-builder-group{
  border-radius: 2px;
  margin-bottom: 10px;
}

.form-design-warning{
  padding: 12px;
  color: $text-color-regular;
  font-size: $font-size-small;

  background: #FFFBE6;
  border: 1px solid #FFE58F;
  border-radius: $border-radius-base;
  display: flex;

  .iconfont {
    color: #FAAD14;
    margin-right: 8px;
    font-size: $font-size-base;
  }
}

.form-item__text{
  padding: 3px 0;
  line-height: 24px;
  color: #666;
}

// 手机壳样式调整
.form-design {
  // background-color: #fff;
  // min-height: 600px;
  min-width: 800px;
}
.base-hidden-modal{
  .base-modal-body {
    margin: 23px auto;
    width: 350px;
  
  }
}


// ---- vuedragger ----
.ghost {
  background: #fff;
  border: 1px dashed $color-primary;

  &::after {
    background: #fff;
  }
}

div.ghost {
  position: relative;
  overflow: hidden;
  
  &::after {
    // content: '放在这里';
    content: '\653e\5728\8fd9\91cc';
    display: block;
    background: $color-primary-light-9;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    text-align: center;
    font-size: 16px;
    color: #999;
    z-index: 10;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
  }
}

.sortable-fallback {
  border: 1px dashed transparent;
  border-color: $color-primary-light-6;
  .form-preview-group{
    background-color: #fff;
    opacity: 0.9;
  }
}
.form-wrap{
  .hide-form-item-group {
    display: none;
  }
  
  .form-item-group {
    padding: 8px 16px;
    .base-form-group:nth-last-of-type(1) {
      border: none;
      &::after {
        content: '';
        display: none;
      }
    }

  }
}
.form-type-text {
  height: 20px;
  line-height: 20px;
  padding: 0 6px;

  font-size: $font-size-small;
  color: $text-color-regular;
  background-color: $bg-color-l3;
  border-radius: 10px;
}

.mform-design-setting {
  background-color: #fff;
  .form-item {
    padding: 0 16px;
    & > label {
      padding-left: 0;
      color: #595959;
    }
  }
}

.flow-preview-header {
  width: 100%;
  height: 40px;
  background: #EDF0F5;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  border-bottom: 1px solid #e8e8e8;
  position: relative;
      
  &-translate{
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 12px;
  }
}