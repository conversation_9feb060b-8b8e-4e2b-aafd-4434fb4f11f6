/* eslint-disable vue/html-indent */
export default [
  {
    path: '/setting/task/taskFormSet',
    component: () => import(/* webpackChunkName: "SettingTaskTaskFormSetView" */ '@src/modules/setting/task/taskTypeSetting/flow/index.js')
  },
  {
    path: '/setting/task/taskSet',
    component: () => import(/* webpackChunkName: "SettingTaskTaskSetView" */ '@src/modules/setting/task/taskSetting/index.js')
  },
  {
    path: '/setting/cost/serviceItem',
    component: () => import(/* webpackChunkName: "SettingCostServiceItemView" */ '@src/modules/setting/serviceItem/index.js')
  },
  {
    path: '/setting/customer/fields',
    component: () => import(/* webpackChunkName: "SettingCustomerFieldsView" */ '@src/modules/setting/customer/fields.js')
  },
  {
    path: '/setting/product/fields',
    component: () => import(/* webpackChunkName: "SettingProductFieldsView" */ '@src/modules/setting/product/index.js')
  },
  {
    path: '/setting/product/register/fields',
    component: () => import(/* webpackChunkName: "SettingProductRegisterFieldsView" */ '@src/modules/setting/productRegister/index.js')
  },
  {
    path: '/setting/product/qrcode',
    component: () => import(/* webpackChunkName: "SettingProductQrCodeView" */ '@src/modules/setting/product/index.js')
  },
  {
    path: '/setting/sparepart/instock',
    component: () => import(/* webpackChunkName: "SettingSparePartInStockView" */ '@src/modules/setting/sparepart/instock/index.js')
  },
  {
    path: '/setting/info/wiki',
    component: () => import(/* webpackChunkName: "SettingInfoWikiView" */ '@src/modules/setting/document/documentSetting/index.js')
  },
  {
    path: '/setting/smart/settlement',
    component: () => import(/* webpackChunkName: "SettingSmartSettlementView" */ '@src/modules/setting/smartSettlement/index.js')
  },
  {
    path: '/setting/message',
    component: () => import(/* webpackChunkName: "SettingMessageView" */ '@src/modules/doMyself/setting/message/index.js')
  },
  {
    path: '/setting/message/smsmessage',
    component: () => import(/* webpackChunkName: "SettingMessageSmsMessageView" */ '@src/modules/doMyself/setting/smsMessage/index.js')
  },
  {
    path: '/setting/message/subManage',
    component: () => import(/* webpackChunkName: "SettingMessageSubManageView" */ '@src/modules/doMyself/setting/subManage/index.js')
  },
  {
    path: '/setting/message/channel',
    component: () => import(/* webpackChunkName: "SettingMessageChannelView" */ '@src/modules/setting/messageChannel/index.js')
  },
  {
    path: '/setting/serviceStation/customerPortal',
    component: () => import(/* webpackChunkName: "SettingDoMyselfSetView" */ '@src/modules/doMyself/home/<USER>')
  },
  {
    path: '/setting/doMyself/doMyselfSet',
    component: () => import(/* webpackChunkName: "SettingDoMyselfSetView" */ '@src/modules/doMyself/home/<USER>')
  },
  {
    path: '/setting/product/productType',
    component: () => import(/* webpackChunkName: "SettingProductV2CatalogView" */ '@src/modules/setting/productV2/index.js')
  },
  {
    path: '/setting/productV2/catalog/setting',
    component: () => import(/* webpackChunkName: "SettingProductV2CatalogView" */ '@src/modules/setting/productV2/index.js')
  },
  {
    path: '/setting/productV2/catalog/settingField',
    component: () => import(/* webpackChunkName: "SettingProductV2CatalogSettingFieldView" */ '@src/modules/setting/productV2/productMenuField/index.js')
  },
  {
    path: '/setting/productV2/catalog/setting/qrcodeSet',
    component: () => import(/* webpackChunkName: "SettingProductV2CatalogSettingQrCodeSetView" */ '@src/modules/superQrcode/index.js')
  },
  {
    path: '/setting/productV2/productQualitySearch',
    component: () => import(/* webpackChunkName: "SettingProductV2QualitySearchFieldView" */ '@src/modules/setting/productV2/productQualitySearch/index.js')
  },
  {
    path: '/setting/productV2/productQualityManagement',
    component: () => import(/* webpackChunkName: "SettingProductQualityManagementView" */ '@src/modules/setting/productV2/productQualityManagement/index.js')
  },
  {
    path: '/setting/serviceStation/partShop',
    component: () => import(/* webpackChunkName: "SettingServiceStationPartShopView" */ '@src/modules/system/mall/index.js')
  },
  {
    path: '/setting/taskType/manage',
    component: () => import(/* webpackChunkName: "SettingTaskTypeManageView" */ '@src/modules/setting/task/taskTypeSetting/manage/index.js')
  },
  {
    path: '/setting/task/cardManage',
    component: () => import(/* webpackChunkName: "SettingTaskCardManageView" */ '@src/modules/setting/task/taskAdditionalSetting/manage/index.js')
  },
  {
    path: '/setting/task/cardFormfields',
    component: () => import(/* webpackChunkName: "SettingTaskCardFormFieldsView" */ '@src/modules/setting/task/taskAdditionalSetting/taskAddcardFormView/index.js')
  },
  {
    path: '/setting/task/cardHoursRecord',
    component: () => import(/* webpackChunkName: "SettingTaskCardHoursRecordView" */ '@src/modules/setting/task/taskAdditionalSetting/taskHoursRecordView/index.js')
  },
  {
    path: '/setting/sparepart2/repertory',
    component: () => import(/* webpackChunkName: "SettingSparePart2RepertoryView" */ '@src/modules/setting/sparepart/repertory/index.js')
  },
  {
    path: '/setting/performance/v2/rule',
    component: () => import(/* webpackChunkName: "SettingPerformanceV2RuleView" */ '@src/modules/setting/performance/index.js')
  },
  {
    path: '/setting/performance/v2/report',
    component: () => import(/* webpackChunkName: "SettingPerformanceV2ReportView" */ '@src/modules/setting/performance/report/index.js')
  },
  {
    path: '/setting/doMyself/wxSet',
    component: () => import(/* webpackChunkName: "SettingDoMyselfWxSetView" */ '@src/modules/doMyself/home/<USER>')
  },
  {
    path: '/setting/doMyself/miniProgramSet',
    component: () => import(/* webpackChunkName: "SettingDoMyselfMiniProgramSetView" */ '@src/modules/doMyself/home/<USER>')
  },
  {
    path: '/setting/doMyself/toastList',
    component: () => import(/* webpackChunkName: "SettingDoMyselfToastListView" */ '@src/modules/doMyself/home/<USER>')
  },
  {
    path: '/setting/callcenter/stage',
    component: () => import(/* webpackChunkName: "SettingCallCenterStageView" */ '@src/modules/callcenter/stage.js')
  },
  {
    path: '/setting/callcenter/view',
    component: () => import(/* webpackChunkName: "SettingCallCenterDetailView" */ '@src/modules/callcenter/view.js')
  },
  {
    path: '/setting/callcenter/workbench',
    component: () => import(/* webpackChunkName: "SettingCallCenterWorkbenchView" */ '@src/modules/callcenter/workbench.js')
  },
  {
    path: '/setting/task/cardSpareService',
    component: () => import(/* webpackChunkName: "SettingTaskCardSpareServiceView" */ '@src/modules/setting/task/taskAdditionalSetting/taskSpareServiceView/index.js')
  },
  {
    path: '/setting/task/cardMaterialApplyService',
    component: () => import(/* webpackChunkName: "SettingTaskCardMaterialApplyServiceView" */ '@src/modules/setting/task/taskAdditionalSetting/taskMaterialApplyView/index.js')
  },
  {
    path: '/setting/task/cardFaultListSettings',
    component: () => import(/* webpackChunkName: "SettingTaskCardMaterialApplyServiceView" */ '@src/modules/setting/task/taskAdditionalSetting/taskFaultListSettings/index.js')
  },
  {
    path: '/setting/superQrcode',
    component: () => import(/* webpackChunkName: "SettingSuperQrCodeView" */ '@src/modules/superQrcode/index.js')
  },
  {
    path: '/setting/customer/additional',
    component: () => import(/* webpackChunkName: "SettingCustomerAdditionalView" */ '@src/modules/setting/customer/customerAdditionalSetting/manage/index.js')
  },
  {
    path: '/setting/product/additional',
    component: () => import(/* webpackChunkName: "SettingProductAdditionalView" */ '@src/modules/setting/productV2/productAdditionalSetting/manage/index.js')
  },
  {
    path: '/setting/customer/cardFormfields',
    component: () => import(/* webpackChunkName: "SettingCustomerCardFormFieldsView" */ '@src/modules/setting/customer/customerAdditionalSetting/customerAddcardFormView/index.js')
  },
  {
    path: '/setting/serviceRemark',
    component: () => import(/* webpackChunkName: "SettingServiceRemarkView" */ '@src/modules/setting/serviceDesk/serviceRemark/index')
  },
  {
    path: '/setting/serviceStation/eventType',
    component: () => import(/* webpackChunkName: "SettingServiceStationEventTypeView" */ '@src/modules/setting/serviceEvent/serviceEventTypeSetting/manage/index.js')
  },
  {
    path: '/setting/serviceStation/cardManage',
    component: () => import(/* webpackChunkName: "SettingServiceStationCardManageView" */ '@src/modules/setting/serviceEvent/additional/index.js')
  },
  {
    path: '/setting/serviceStation/card/view',
    component: () => import(/* webpackChunkName: "SettingServiceStationCardView" */ '@src/modules/setting/serviceEvent/additional/additionalFormView/index.js')
  },
  {
    path: '/setting/serviceStation/eventForm',
    component: () => import(/* webpackChunkName: "SettingServiceStationEventFormView" */ '@src/modules/setting/serviceEvent/serviceEventTypeSetting/flow/index.js')
  },
  {
    path: '/setting/contract/manage',
    component: () => import(/* webpackChunkName: "SettingContractManageView" */ '@src/modules/setting/contract/contractSetting/manage/index.js')
  },
  {
    path: '/setting/contract/flow',
    component: () => import(/* webpackChunkName: "SettingContractFlowView" */ '@src/modules/setting/contract/contractSetting/flow/index.js')
  },
  {
    path: '/setting/common/usercard',
    component: () => import(/* webpackChunkName: "SettingCommonUserCardView" */ '@src/modules/setting/common/userCard/index.js')
  },
  {
    path: '/setting/common/enterprise',
    component: () => import('@src/modules/setting/common/enterprise/index.js')
  },
  {
    path: '/setting/service/provider/fields',
    component: () => import(/* webpackChunkName: "SettingProviderView" */ '@src/modules/setting/serviceProviderManage/index.js')
  },
  {
    path: '/setting/linkcMall/manage',
    component: () => import(/* webpackChunkName: "SettingProviderView" */ '@src/modules/setting/linkcMall/index.js')
  },
  {
    path: '/setting/projectManage/type/set',
    component: () => import(/* webpackChunkName: "SettingProjectManageView" */ '@src/modules/setting/projectManage/projectManageTypeSetting/index.js')
  },
  {
    path: '/setting/projectManage/type/cardList',
    component: () => import(/* webpackChunkName: "SettingProjectManageTypeListView" */ '@src/modules/setting/projectManage/projectManageTypeList/index.js')
  },
  {
    path: '/setting/projectManage/task/set',
    component: () => import(/* webpackChunkName: "SettingProjectManageTaskView" */ '@src/modules/setting/projectManage/projectManageTaskSetting/index.js')
  },
  {
    path: '/setting/projectManage/task/cardList',
    component: () => import(/* webpackChunkName: "SettingProjectManageTaskListView" */ '@src/modules/setting/projectManage/projectManageTaskList/index.js')
  },
  // 故障库设置页面
  {
    path: '/setting/faultLibrary',
    component: () => import(/* webpackChunkName: "SettingFaultLibraryFieldsView" */ '@src/modules/setting/failureLibrarySetting/index.js')
  },
  // 故障库解决方案表单设计器页面
  {
    path: '/setting/faultLibrary/solutionFields',
    component: () => import(/* webpackChunkName: "SettingFaultLibrarySolutionFields" */ '@src/modules/setting/failureLibrarySetting/solutionFields.js')
  },
  {
    path: '/setting/gpt',
    component: () => import(/* webpackChunkName: "SettingGPTView" */ '@src/modules/setting/gpt/views/index/entry')
  },
  {
    path: '/setting/gpt/edit',
    component: () => import(/* webpackChunkName: "SettingGPTEditView" */ '@src/modules/setting/gpt/views/edit/entry')
  },
  {
    path: '/setting/gpt/chat',
    component: () => import(/* webpackChunkName: "SettingGPTChatView" */ '@src/modules/setting/gpt/views/chat/entry')
  },
  {
    path: '/setting/robot/chat',
    component: () => import(/* webpackChunkName: "SettingGPTChatView" */ '@src/modules/setting/gpt/views/chat/entry')
  },
  {
    path: '/setting/gpt/question',
    component: () => import(/* webpackChunkName: "SettingGPTQuestionView" */ '@src/modules/setting/gpt/views/manage/entry')
  },
  {
    path: '/setting/gpt/test',
    component: () => import(/* webpackChunkName: "SettingGPTTestView" */ '@src/modules/setting/gpt/views/test')
  },
  {
    path: '/setting/gpt/home',
    component: () => import(/* webpackChunkName: "SettingGPTHomeView" */ '@src/modules/setting/gpt/views/home')
  },
  {
    path: '/setting/gpt/intelligentQA',
    component: () => import(/* webpackChunkName: "SettingGPTIntelligentQAView" */ '@src/modules/setting/gpt/views/intelligent-qa')
  },
  {
    path: '/setting/gpt/bi',
    component: () => import(/* webpackChunkName: "SettingGPTtBIView" */ '@src/modules/setting/gpt/views/biview/entry')
  },
  {
    path: '/setting/gpt/translate',
    component: () => import(/* webpackChunkName: "SettingGPTTranslateView" */ '@src/modules/setting/gpt/views/translate/entry')
  },
  {
    path: '/setting/faceRecognition',
    component: () => import(/* webpackChunkName: "SettingGPTQuestionView" */ '@src/modules/setting/faceRecognition/setting')
  },
  {
    path: '/setting/userFacePromotion',
    component: () => import(/* webpackChunkName: "SettingGPTQuestionView" */ '@src/modules/setting/faceRecognition/promotion')
  },
  {
    path: '/setting/projectManage/time',
    component: () => import(/* webpackChunkName: "SettingGPTQuestionView" */ '@src/modules/setting/projectManage/projectManageTime/index')
  },
  {
    path: '/setting/offline',
    component: () => import(/* webpackChunkName: "SettingOfflineView" */ '@src/modules/setting/offline/views/index/entry')
  },
  {
    path: '/setting/extendedWarrantyCardType',
    component: () => import(/* webpackChunkName: "SettingExtendedWarrantyCardType" */ '@src/modules/setting/extendedWarrantyCardType/index')
  },
  {
    path: '/setting/purchaseOrderAdditionalSetting',
    component: () => import(/* webpackChunkName: "SettingExtendedWarrantyCardType" */ '@src/modules/setting/b2bMall/purchaseOrderAdditionalSetting/index')
  },
  {
    path: '/setting/authorizationView',
    component: () => import(/* webpackChunkName: "SettingAuthorizationView" */ '@src/modules/setting/authorizationView')
  },
]
