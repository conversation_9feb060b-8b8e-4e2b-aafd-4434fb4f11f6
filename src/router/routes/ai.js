/* eslint-disable vue/html-indent */
export default [
  {
    path: '/ai/agent',
    component: () => import( /* webpackChunkName: "AIAgentView" */ '@src/modules/ai/views/home/<USER>')
  },
  {
    path: '/ai/agent/chat',
    component: () => import( /* webpackChunkName: "AIAgentChatView" */ '@src/modules/ai/views/chat/entry')
  },
  {
    path: '/ai/agent/login/pc',
    component: () => import( /* webpackChunkName: "AIAgentLoginPCView" */ '@src/modules/ai/views/login/entry')
  },
  {
    path: '/ai/agent/:id',
    component: () => import( /* webpackChunkName: "AIAgentDetailView" */ '@src/modules/ai/views/detail/entry')
  },
  {
    path: '/ai/agent/app/:id',
    component: () => import( /* webpackChunkName: "AIAgentAppSettingView" */ '@src/modules/ai/views/app/entry')
  },
  {
    path: '/ai/agentEdit',
    component: () => import( /* webpackChunkName: "AIAgentAppSettingView" */ '@src/modules/ai/views/edit/index.vue')
  },
  
]
