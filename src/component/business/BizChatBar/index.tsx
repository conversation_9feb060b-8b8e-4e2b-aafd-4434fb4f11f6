/* component */
import BizChatBarHeader from "@src/component/business/BizChatBar/header"
import BizChatBarContent from "@src/component/business/BizChatBar/content"
/* const */
import { STREAM_BUSINESS_SOURCE_DOCUMENTS } from "@src/component/business/BizChatPanel/chat/model/const"
/* enum */
import { DEFAULT_IM_CHAT_SYSTEM_MESSAGE } from "@src/component/business/BizChatBar/model"
import { ChatSourceDocumentTypeEnum, OpenAIMessageRoleEnum } from "@src/component/business/BizChatPanel/chat/model/enum"
/* vue */
import { defineComponent } from "vue"
/* types */
import { OpenAIMessageItemType } from "@src/component/business/BizChatPanel/chat/model/type"
/* model */
import { ChatBaseParams, ChatConversationParams } from "@model/param/in/AI"
/* utils */
import { isFalsy } from "pub-bbx-utils"
import { isJSONObject, parse_with_default_value } from "@src/util/lang/object"
/* hooks */
import { useTenantId, useRootUser } from '@hooks/useRootWindow'
import { useEnv } from "@hooks/useEnv"
/* scss */
import "@src/component/business/BizChatBar/index.scss"
import { AIImQaAgentParamType } from "@src/modules/ai/model/param"
import { imQaAgent } from "@src/api/AIv2API"
import MsgModel from "@model/MsgModel"
import { AIImQaAgentResultType } from "@src/modules/ai/model/result"
import { userCenterGetTokenInfo } from "@src/util/userCenter"

type BizChatBarContentComponent = InstanceType<typeof BizChatBarContent>
type BizChatBarHeaderComponent = InstanceType<typeof BizChatBarHeader>

enum BizChatBarEventEnum {
  RETRY = 'retry',
  STOP = 'stop',
  CLOSE = 'close',
  USE = 'use',
  SEND = 'send',
  LOADING = 'loading',
  INPUT = 'input'
}

export default defineComponent({
  name: "biz-chat-bar",
  components: {
    BizChatBarHeader
  },
  props: {
    isV2: {
      type: Boolean,
      default: false,
    },
    onDrop: {
      type: Function,
    },
    onRetry: {
      type: Function,
    },
    onStop: {
      type: Function,
    },
    onClose: {
      type: Function,
    },
    onLoading: {
      type: Function,
    }
  },
  setup() {
    
    const { isDev } = useEnv()
    const tenantId = useTenantId()
    const loginUser = useRootUser()
    
    return {
      tenantId,
      loginUser,
      isDev
    }

  },
  data() {
    return {
      robotId: null as string | number | null,
      robotAppId: null as string | number | null,
      messages: [] as OpenAIMessageItemType[],
      loading: false as boolean,
      conversationId: null as string | number | null,
      imMessageId: null as string | number | null,
      content: '' as string,
      controller: null as AbortController | null,
      resizeObserver: null as ResizeObserver | null,
      scrollIntervalIds: [] as number[],
      isThinking: false as boolean
    }
  },
  computed: {
    userId(): string {
      return this.loginUser.userId as string
    },
    bizChatBarComponent(): BizChatBarContentComponent {
      return this.$refs.BizChatBarContent as BizChatBarContentComponent
    },
    bizChatBarHeaderComponent(): BizChatBarHeaderComponent {
      return this.$refs.BizChatBarHeader as BizChatBarHeaderComponent
    }
  },
  beforeDestroy() {
    this.clearScrollInterval()
  },
  methods: {
    renderLeft() {

    },
    renderLogo() {

    },
    show(
      robotId: string | number, 
      robotAppId: string | number, 
      messages: OpenAIMessageItemType[], 
      conversationId: string | number,
      imMessageId: string
    ) {

      if (this.isV2) {
        this.showV2(
          robotId,
          robotAppId,
          messages,
          conversationId,
          imMessageId
        )
        return
      }
      
      this.robotId = robotId
      this.robotAppId = robotAppId
      this.messages = messages
      this.conversationId = conversationId
      this.imMessageId = imMessageId
      this.content = ''
      this.scrollIntervalIds = []
      
      this.onAIAnswerHandler()
      this.emitLoadingHandler()
    },
    showV2(
      robotId: string | number, 
      robotAppId: string | number, 
      messages: OpenAIMessageItemType[], 
      conversationId: string | number,
      imMessageId: string
    ) {

      this.messages = messages
      this.content = ''
      this.conversationId = conversationId
      this.imMessageId = imMessageId

      this.onAIAnswerHandlerV2()
      this.emitLoadingHandler()
    },
    /**
     * @description 获取聊天基础参数
     */
    getChatBaseParams(): ChatBaseParams {
      let params = {
        tenantId: this.tenantId,
        userId: this.userId,
        conversationId: this.conversationId as string,
        businessId: this.conversationId as string,
        imMessageId: this.imMessageId as string,
        type: ChatSourceDocumentTypeEnum.IM,
        robotId: this.robotId as string,
        appId: this.robotAppId as string,
        isExternal: true,
        stream: true
      }  
      return params
    },
    getMessages() {
      
      let newContextMessages = this.messages.map(message => {
        return {
          content: message.content,
          type: message.role
        }
      })
      
      newContextMessages.unshift({
        content: DEFAULT_IM_CHAT_SYSTEM_MESSAGE,
        type: OpenAIMessageRoleEnum.System
      })
      
      return newContextMessages
    },
    /** 
     * @description AI 回答处理
    */
    onAIAnswerHandler() {
      
      if (this.isV2) {
        this.onAIAnswerHandlerV2()
        return
      }
      
      this.content = ''
      this.loading = true
      
      const baseParams: ChatBaseParams = this.getChatBaseParams()
      const params: ChatConversationParams = {
        ...baseParams,
        messages: this.getMessages()
      }
      
      try {
        this.sendMessageWithStream(params)
      } catch (error) {
        console.error(error)
      }
      
    },
    async onAIAnswerHandlerV2() {
      try {

        console.log('onAIAnswerHandlerV2')

        const question = this.messages[this.messages.length - 1]?.content || ''
  
        const params: AIImQaAgentParamType = {
          question,
          tenantId: "",
          userId: "",
          conversationId: this.conversationId as string,
        }

        try {
          return await this.sendMessageWithStreamV2(params)
        } catch (error) {
          console.error(error)
        }

        this.content = '正在思考...'
  
        const result = await imQaAgent(params)
        const isFail = MsgModel.isFail(result)
        if (isFail) {
          this.$message.error(result.message)
          return
        }
  
        const data = MsgModel.getData<AIImQaAgentResultType>(result, {})
        const text = data?.text || ''
  
        this.content = text
        this.$emit(BizChatBarEventEnum.INPUT, this.content)
        
      } catch (error) {
        console.error(error)
        this.content = '网络异常，请重试'
      } finally {
        this.loading = false
      }
    },
    async sendMessageWithStream(params: ChatConversationParams) {
      const controller = new AbortController();
      
      this.controller = controller
      
      const url = '/language/chat/conversation'
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: controller.signal,
        body: JSON.stringify(params),
      });
      
      if (!response.ok) {
        return;
      }
      
      const data = response.body;
      
      if (!data) {
        return;
      }
      
      const reader = data.getReader();
      const decoder = new TextDecoder();

      try {
        const { done, value } = await reader.read()
        const data = decoder.decode(value, { stream: true })
        const isErrorJSON = isJSONObject(data)
        if (isErrorJSON) {
          const error = parse_with_default_value(data, {} as Record<string, any>)
          const isFail = MsgModel.isFail(error)
          if (isFail) {
            this.$message.error(error?.message || '网络异常，请稍后再试')
            return
          }
        }
      } catch (error) {
        console.warn(error)
      }

      let done = false;
      let text = '';
      
      while (!done) {
        
        const { value, done: doneReading } = await reader.read();
        
        done = doneReading;
        
        let chunkValue = decoder.decode(value);
        
        const dataPrefix = 'data:';
        chunkValue = chunkValue.startsWith(dataPrefix) ? chunkValue.slice(dataPrefix.length) : chunkValue;
        chunkValue = chunkValue.trim();
        
        try {
          chunkValue = chunkValue.replaceAll(dataPrefix, '');
        } catch (error) {
          chunkValue = chunkValue.replace(new RegExp(dataPrefix, 'g'), '');
        }
        
        // 如果是业务数据来源，则单独处理
        if (
          chunkValue.startsWith(STREAM_BUSINESS_SOURCE_DOCUMENTS)
        ) {
          break;
        }   
        // 如果是业务数据来源，单独处理
        else if (chunkValue.includes(STREAM_BUSINESS_SOURCE_DOCUMENTS)) {
          try {
            const index = chunkValue.indexOf(STREAM_BUSINESS_SOURCE_DOCUMENTS)
            text += chunkValue.slice(0, index)
          } catch (error) {
            console.error(error)
          }
        } 
        else {
          text += chunkValue;
        }
        
        this.content = text;
        
        this.$emit(BizChatBarEventEnum.INPUT, this.content)
        
      }
      
      try {
        if (isJSONObject(text)) {
          const result = JSON.parse(text)
          const content = result?.data?.content || ''
          this.content = content
        }
      } catch (error) {
        console.error(error)
      }
      
      this.$emit(BizChatBarEventEnum.INPUT, this.content)
      
      this.loading = false
      
      setTimeout(() => {
        this.clearScrollInterval()
      }, 500)
      
      if (isFalsy(text)) {
        this.$message.error('网络异常，请重试')
        return
      }
      
    },
    /** 
     * @description 发送消息会话问题 (流式)
    */
    async sendMessageWithStreamV2(params: Record<string, any>) {  
      this.content = '正在思考...'
      this.loading = true
      const controller = new AbortController();
      this.controller = controller;

      let eventCount = 0;
      let buffer = ''; // 添加缓冲区存储不完整的数据
      let text = ''
      
      try {
        const tokenInfo = userCenterGetTokenInfo() as Record<string, any>
        const response = await fetch('/api/voice/outside/xiaobao/workflow/handle/im/qaWithStream', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'token': tokenInfo?.token || '',
          },
          signal: controller.signal,
          body: JSON.stringify(params),
        });
      
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const reader = response.body?.getReader();
        if (!reader) {
          throw new Error('Response body is null');
        }
      
        const decoder = new TextDecoder();
        const endThinkTagText = '</think>'
        let isFirst = true
        
        while (true) {
          const { value, done } = await reader.read();
          if (done) {
            console.log(`流式响应结束，共收到 ${eventCount} 条消息`);
            break;
          }
      
          // 解码当前数据块并添加到buffer
          buffer += decoder.decode(value, { stream: true });

          try {
            if (isFirst) {
              const data = decoder.decode(value, { stream: true })
              const isErrorJSON = isJSONObject(data)
              if (isErrorJSON) {
                const error = parse_with_default_value(data, {} as Record<string, any>)
                const isFail = MsgModel.isFail(error)
                if (isFail) {
                  this.$message.error(error?.message || '网络异常，请稍后再试')
                  this.content = ''
                  return
                }
              }
            }
          } catch (error) {
            console.warn(error)
          } finally {
            isFirst = false
          }
          
          // 按行分割处理数据
          const lines = buffer.split('\n');
          
          // 保留最后一行（可能不完整）
          buffer = lines.pop() || '';
        
          // 处理完整的行
          for (const line of lines) {
            if (line.trim() === '') continue; // 跳过空行
            if (line.startsWith('data:')) {
              eventCount++;
              const message = line.replace(/^data:\s*/, '').trim();
              
              console.log(`第 ${eventCount} 条消息:`, message);
              
              if (message) {

                // 更新助理消息
                let textValue = JSON.parse(message).data.text || ''

                // 如果  textValue 包含 </think>, 则在 </think> 前面加一个空的 div
                if (textValue.includes(endThinkTagText)) {
                  const endThinkTagIndex = textValue.indexOf(endThinkTagText)
                  const before = textValue.slice(0, endThinkTagIndex)
                  const after = textValue.slice(endThinkTagIndex)
                  textValue = before + `<div></div>` + after
                }
                

                text += textValue

                this.content = text;
        
                this.$emit(BizChatBarEventEnum.INPUT, this.content)

              }
            }
          }

        }
      
      } catch (error) {
        console.error('Stream error:', error);
      }
      
           
      try {
        if (isJSONObject(text)) {
          const result = JSON.parse(text)
          const content = result?.data?.content || ''
          this.content = content
        }
      } catch (error) {
        console.error(error)
      }
      
      this.$emit(BizChatBarEventEnum.INPUT, this.content)
      
      this.loading = false
      
      setTimeout(() => {
        this.clearScrollInterval()
      }, 500)
      
      if (isFalsy(text)) {
        this.$message.error('网络异常，请重试')
        return
      }

    },
    scrollViewOnceHandler() {
      this.$nextTick(() => {
        this.scrollViewHandlerImpl()
      })
    },
    scrollViewHandler() {
      
      this.scrollViewOnceHandler()
      
      const scrollIntervalId = setInterval(() => {
        this.scrollViewContentHandler()
      }, 200)
      
      console.log('setInterval scrollViewHandler ', scrollIntervalId)
      
      this.scrollIntervalIds = [
        ...this.scrollIntervalIds,
        scrollIntervalId
      ]
      
      console.log('scrollIntervalIds ', this.scrollIntervalIds)
      
    },
    scrollViewContentHandler() {
      this.bizChatBarComponent?.$el.scrollIntoView()
      console.log('setInterval scrollViewContentHandler')
    },
    scrollViewHandlerImpl() {
      // this.bizChatBarHeaderComponent?.$el.scrollIntoView()
    },
    emitLoadingHandler() {
      setTimeout(() => {
        this.$emit(BizChatBarEventEnum.LOADING)
      }, 100)
    },
    clearScrollInterval() {
      try {
        this.scrollIntervalIds.forEach(id => {
          clearInterval(id as number)
        })
      } catch (error) {
        console.error(error)
      }
    },
    outsideClearScrollInterval() {
      this.clearScrollInterval()
    },
    onRetryHandler() {
      this.onAIAnswerHandler()
    },
    onStopHandler() {
      this.onCloseHandler()
      if (this.controller) {
        this.controller.abort()
      }
    },
    onCloseHandler() {
      this.$emit(BizChatBarEventEnum.CLOSE)
      this.clearScrollInterval()
    },
    onUseHandler() {
      this.$emit(BizChatBarEventEnum.USE, this.content)
      this.onCloseHandler()
    },
    onSendHandler() {
      this.$emit(BizChatBarEventEnum.SEND, this.content)
      this.onCloseHandler()
    },
    outsideOnStopHandler() {
      this.onStopHandler()
    }
  },
  render() {
    return (
      <div class="biz-chat-bar">
        <BizChatBarHeader
          ref="BizChatBarHeader"
          isAnswering={this.loading}
          onDrop={this.onCloseHandler}
          onRetry={this.onRetryHandler}
          onStop={this.onStopHandler}
          onUse={this.onUseHandler}
          onSend={this.onSendHandler}
        >
        </BizChatBarHeader>
        <BizChatBarContent
          ref="BizChatBarContent"
          value={this.content}
        >
        </BizChatBarContent>
      </div>
    )
  }
})