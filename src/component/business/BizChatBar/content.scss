
.biz-chat-bar-content {
  padding: 12px;
  max-height: 320px;
  overflow-y: auto;
  background: linear-gradient(110deg, rgba(238, 255, 254, 1.6) 0%, rgba(241, 241, 254, 1.6) 100%, rgba(245, 225, 255, 1.6) 100%);
  border: 1px solid #F0F2F5;
  border-radius: 8px;
}

.biz-chat-bar-content__content {
  think {
    color: #8c8c8c;
    border-left: 4px solid #ddd;
    padding-left: 10px;
    height: 100%;
    display: block;
    margin-bottom: 12px;
    p {
      margin-top: 12px;
    }
  }
  img {
    width: 100%;
  }
  ol {
    padding-left: 26px;
    list-style-type: decimal !important;
  }
  ul {
    padding-left: 16px;
    list-style-type: decimal !important;
  }
  li::marker {
    color: #262626;
    font-weight: bold;
    content: "· "; /* 自定义标记内容 */
  }
  li li {
    &::marker {
      font-weight: bold;
      content: "· "; /* 自定义标记内容 */
    }
  }
  ol > li::marker {
    content: counter(list-item) ". ";
  }
}