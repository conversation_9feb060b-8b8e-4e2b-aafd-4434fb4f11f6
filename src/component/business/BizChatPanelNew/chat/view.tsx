/* api */
import { conversationChat, initChat } from '@src/api/LLMApi'
import { textToReports } from "@src/api/AIApi";
/* components */
import { 
  ChatAIInputBar, 
  ChatAIMessageList, 
  ChatAIHistoryMessageTip,
  ChatAIAppTag,
  ChatAIPromptTag
} from '@src/component/business/BizChatPanelNew/chat/components'
import { Loading } from 'element-ui'
import ChatAIMessageTest from '@src/component/business/BizChatPanelNew/chat/components/message-test'
// @ts-ignore
import VueMarkdown from 'vue-markdown'
/* enum */
import { ChatSourceDocumentTypeEnum, ComponentNameEnum } from '@src/component/business/BizChatPanelNew/chat/model/enum'
/* hooks */
import { useLoading } from '@hooks/useLoading'
import {
  useChatFetchContentSecurityText,
  useChatFetchConversation,
  useChatFetchDeleteMessage,
  useChatFetchHistoryMessage,
  useChatFetchQuestionInStandardLib,
  useChatFetchSendNewMessage,
  useStateForAiChat,
} from '@src/component/business/BizChatPanelNew/chat/hooks'
/* model */
import { 
  ChatIMMessageItemType,
  DEFAULT_CHAT_SYSTEM_MESSAGE, 
  OpenAIMessageItemType, 
  OpenAIMessageRoleEnum, 
  STREAM_BUSINESS_SOURCE_DOCUMENTS
} from '@src/component/business/BizChatPanelNew/chat/model'
import { 
  ChatBaseParams, 
  ChatConversationParams, 
  ChatInitParams, 
  GetChatConversationModel, 
  GetChatConversationShareModel, 
  GetChatHistoryMessageModel, 
  SendChatConversationContentModel, 
  SendChatConversationContentShareModel
} from '@model/param/in/AI'
/* vue */
import { computed, defineComponent, onBeforeUnmount, PropType, ref } from 'vue'
/* util */
import { 
  chatIMMessageToAIMessage,
  getAssistantLoadingMessage, 
  getAssistantStopMessage, 
  getSystemMessageByContent, 
  getUserMessageByContent,
  isAssistantMessage,
  isNotUserMessage,
  isUserMessage
} from '@src/component/business/BizChatPanelNew/chat/util'
import { cloneDeep, isAsyncFunction, isEmpty, isFalsy, isFunction, isNotEmpty, isNotEqual, isString, isUndefined } from '@src/util/type'
import { uuid } from '@src/util/lang/string'
import Platform from '@src/util/platform'
import { parse_with_default_value } from '@src/util/lang/object';
/* util */
import { getRootWindowInitData } from '@src/util/window'
/* types */
import { ChatBusinessSourceDocumentType } from '@src/component/business/BizChatPanelNew/chat/types'
import { ChatConversationResult } from '@model/param/out/AI'
import { SettingGPTPromptWordItem, SettingGPTServiceItem } from '@gpt/types'
import Page from '@model/Page'
import { SettingGPTServiceTypeEnum } from '@src/modules/setting/gpt/model';
import MsgModel from '@model/MsgModel';
import { aiChatAgentConversation } from '@src/api/AIv2API';
import { AiChatAgentConversationParamType } from '@src/modules/ai/model/param';
import { AiChatAgentConversationResultType } from '@src/modules/ai/model/result';
import { AIAgentAppComponentType, AIAgentBaseType, AIAgentType } from '@src/modules/ai/types';
import { AIAgentLogSourceEnum, AiModelEnum } from '@src/modules/ai/model/enum';
import ModelItem from '@src/modules/ai/components/model-item';
import { userCenterGetTokenInfo } from '@src/util/userCenter';
import { isFalse } from 'pub-bbx-utils';
import eventBus from '@src/util/eventBus'
import {getCurrentInstance} from 'vue'
import { isJSONObject } from "@src/util/lang/object"
import { findComponentDownward, findComponentsDownward } from '@src/util/assist';

type ChatAIInputBarComponent = InstanceType<typeof ChatAIInputBar>;
type ChatAIMessageListElement = InstanceType<typeof HTMLDivElement>;

export type ChatAIViewProps = {
  isShare: boolean
}

export interface ChatAIViewSetupState {
  
}

export enum ChatAIViewEventEnum {
  Input = 'input',
  AppTag = 'appTag',
  ChangeModel = 'changeModel'
}

export default defineComponent({
  name: ComponentNameEnum.ChatAIView,
  components: {
    [ChatAIInputBar.name as string]: ChatAIInputBar,
    [ChatAIMessageList.name as string]: ChatAIMessageList,
    [ChatAIHistoryMessageTip.name as string]: ChatAIHistoryMessageTip,
    VueMarkdown,
    [ChatAIMessageTest.name as string]: ChatAIMessageTest
  },
  props: {
    userId: {
      type: String,
      default: ''
    },
    robotId: {
      type: [String, Number],
      default: ''
    },
    isSendMessageToServer: {
      type: Boolean,
      default: true
    },
    tenantId: {
      type: String,
      default: ''
    },
    isShare: {
      type: Boolean,
      default: false
    },
    stream: {
      type: Boolean,
      default: false
    },
    currentApp: {
      type: Object as PropType<AIAgentAppComponentType>,
      default: () => ({})
    },
    currentAgent: {
      type: Object as PropType<AIAgentType>,
      default: () => ({})
    },
    disabledAgentEnabledValidate: {
      type: Boolean,
      default: false
    },
    isDisabled: {
      type: Boolean,
      default: false
    },
    chatSystemMessage: {
      type: String,
      default: ''
    },
    onAppTag: {
      type: Function
    },
    onChangeModel: {
      type: Function
    },
    mode:{
      type: String,
      default: ''
    },
    isViews: {
      type: Boolean,
      default: false
    },
    source: {
      type: String as PropType<AIAgentLogSourceEnum>,
      default: AIAgentLogSourceEnum.INSIDE
    },
    doorId: {
      type: String,
      default: ''
    }
  },
  setup(props: ChatAIViewProps, { slots, emit }) {
    const instance = getCurrentInstance()?.proxy || {}
    /**
     * @description 当时预览和调试聊太时，需要加入输入输出变量参数
     */
    // @ts-ignore
    const handleChildData = (data) => {
      // @ts-ignore
      const {variable, outVariable} = props.currentApp
      // @ts-ignore
      const baseParams = instance?.getChatBaseParams()
      delete data[""]
      // @ts-ignore
      instance.varDataList = {
        ...baseParams,
        variableValueMap:data,
        variable,
        outVariable
      }
    }

    // @ts-ignore
    const handleValid = (data) => {
      // @ts-ignore
      instance.validValue = data
    }
    eventBus.$on('sendDataToParent', handleChildData)
    eventBus.$on('sendValid', handleValid)
    // 加载状态
    const { loading, showLoading, hideLoading } = useLoading()
    // 是否是外部分享的
    const _isShare = computed(() => props.isShare)
    
    // 发送新消息
    const { 
      fetchSendNewMessage,
      loading: fetchSendNewMessageLoading
    } = useChatFetchSendNewMessage(_isShare)
    
    // 删除消息
    const {
      fetchDeleteMessage,
      loading: fetchDeleteMessageLoading
    } = useChatFetchDeleteMessage(_isShare)
    
    // 获取会话
    const {
      fetchChatConversation,
      conversationInfo,
      loading: fetchConversationLoading
    } = useChatFetchConversation(_isShare)
    
    // 获取历史消息
    const { 
      fetchHistoryMessage, 
      setMessageList,
      setMessagePage,
      messageList, 
      messagePage,
      loading: fetchHistoryMessageLoading
    } = useChatFetchHistoryMessage(_isShare)
    
    // 内容安全检查
    const {
      fetchContentSecurityText,
      loading: fetchContentSecurityTextLoading
    } = useChatFetchContentSecurityText()
    
    // 查询问题是否在标准库中
    const {
      fetchQuestionInStandardLib,
      loading: fetchQuestionInStandardLibLoading,
      standardQuestion
    } = useChatFetchQuestionInStandardLib(_isShare)
    
    const page = ref({
      list: []
    })


    onBeforeUnmount(() =>{
      eventBus.$off('sendDataToParent', handleChildData)
      eventBus.$off('sendValid', handleValid)
    })
    return {
      page,
      loading,
      messageList,
      messagePage,
      fetchConversationLoading,
      conversationInfo,
      fetchSendNewMessageLoading,
      fetchHistoryMessageLoading,
      fetchContentSecurityTextLoading,
      fetchQuestionInStandardLibLoading,
      standardQuestion,
      
      setMessageList,
      setMessagePage,
      showLoading,
      hideLoading,
      fetchDeleteMessage,
      fetchSendNewMessage,
      fetchHistoryMessage,
      fetchChatConversation,
      fetchContentSecurityText,
      fetchQuestionInStandardLib,
      handleChildData,
      handleValid,
      ...useStateForAiChat(),
    }
    
  },
  data() {
    return {
      messages: [] as OpenAIMessageItemType[],
      focusNumber: 0,
      isLastAssistantMessageError: false,
      messageList: [] as OpenAIMessageItemType[],
      page: new Page({
        pageNum: 1
      }),
      isFirstClickHistoryMessageTip: true,
      chatLoading: false,
      foldHistoryMessageList: [] as OpenAIMessageItemType[],
      id: '',
      loadingInstance: null as any,
      controller: null as unknown as AbortController,
      isStreaming: false,
      isInitFail: false,
      model: AiModelEnum.DOU_BAO,
      isThinking: false,
      answering:false,
      validValue: [],
      varDataList: {},
      firstView:true,
      isStop: false,
      isShowScrollToBottomButton: false,
      isAutoScrollToBottom: true
    }
  },
  computed: {
    attrs(): Record<string, any> {
      return {
        directives: [
          {
            name: 'loading',
            value: this.loading
          }
        ]
      }
    },
    /** 
     * @description 输入框组件
    */
    chatAIInputBarComponent(): ChatAIInputBarComponent {
      return this.$refs.ChatAIInputBarComponent as ChatAIInputBarComponent
    },
    /** 
     * @description 消息列表组件
    */
    chatAIMessageListElement(): ChatAIMessageListElement {
      return this.$refs.ChatAIMessageListElement as any
    },
    /** 
     * @description 会话 id
    */
    conversationId(): string {
      return this.conversationInfo?.id
    },
    /** 
     * @description 获取会话参数
    */
    conversationParams(): GetChatConversationShareModel {
      
      let params: GetChatConversationShareModel = {
        robotId: this.robotId as string,
        type: 'ai',
        client: 0,
        share: Number(this.isShare)
      }
      
      if (this.isShare) {
        params = {
          ...params,
          userId: this.getUserId(),
          loginUserId: this.getUserId(),
          tenantId: this.tenantId
        }
      }
      
      return params
    },
    /** 
     * @description 是否禁用
    */
    disabled(): boolean {
      return (
        this.fetchSendNewMessageLoading
        || this.fetchHistoryMessageLoading
        || this.fetchContentSecurityTextLoading
        || this.loading
        || this.fetchConversationLoading
        || this.chatLoading
        || this.isDisabled
        || this.isInitFail
      )
    },
    /** 
     * @description 历史消息参数
    */
    historyMessageParams(): GetChatHistoryMessageModel {
      return {
        conversationId: this.conversationId,
        pageNum: this.page.pageNum,
        pageSize: 20
      }
    },
    /** 
     * @description 是否有下一页
    */
    isHaveNextPage(): boolean {
      
      if (isFalsy(this.isFirstClickHistoryMessageTip)) {
        return Boolean(this.messagePage?.hasNextPage)
      }
      
      return Boolean(this.messagePage?.list.length)
      
    },
    /** 
     * @description 是否显示历史消息折叠
    */
    isShowHistoryFoldMessage(): boolean {
      return (
        isFalsy(this.isHaveNextPage)
        && Boolean(this.messagePage?.list.length)
      )
    },
    /** 
     * @description 是否是最后一条消息是用户消息
    */
    isLastMessageIsUserMessage(): boolean {
      return this.lastMessage?.role == OpenAIMessageRoleEnum.User
    },
    /** 
     * @description 最后一条消息索引
    */
    lastMessageIndex(): number {
      return this.messages.length - 1
    },
    /** 
     * @description 最后一条消息
    */
    lastMessage(): OpenAIMessageItemType | undefined {
      return this.messages[this.lastMessageIndex]
    },
    /** 
     * @description 样式类名
    */
    viewClassNames(): Record<string, boolean> {
      return {
        'chat-ai-view-v2': true,
        [`${ComponentNameEnum.ChatAIView}--focus`]: this.focusNumber > 0,
        'chat-ai-view-v2-frist': this.firstView,
      }
    },
    robotAppId(): string {
      return String(this.currentAgent?.id || '')
    },
    appTagClass(): Record<string, boolean> {
      return {
        'chat-ai-view-footer-app-tag-v2': true,
        'chat-ai-view-footer-app-tag--disabled': this.disabled
      }
    },
    /** 
     * @description 是否允许修改 AI 模型
    */
    allowModifyAIModel(): boolean {
      
      let app = this.currentApp || {}
      let agentApp = this.currentAgent?.aiAgentAppDetail

      if (agentApp) {
        app = agentApp
      }
      
      const setting = app?.setting || {}
      const allowModifyAIModel = setting?.allowModifyAIModel
      
      return isFalsy(isFalse(allowModifyAIModel))
    },
    welcomeTips(){
      return this.currentApp?.welcomeMessage
    },
    defaultQuestion(): string[] {
      const defaultQuestion = this.currentApp?.defaultQuestion || []
      return defaultQuestion.map((item: string) => {
        return item.trim()
      }).filter((item: string) => {
        return isNotEmpty(item)
      })
    },
    currentUser(){
      // @ts-ignore
      return window?.loginUser || window?.parent?.loginUser || {}
    },
    welcomeTip(){
      return '你好 '
    },
  },
  watch: {
    loading: {
      immediate: true,
      handler(loading) {
        if (loading) {
          this.loadingInstance = Loading.service({
            target: this.$el as HTMLElement,
            fullscreen: true,
            lock: true,
            text: '正在启用您的智能 AI 助手...'
          })
        } else {
          this.loadingInstance?.close()
        }
      }
    },
    messages:{
      handler(val){
        if(val?.length){
          for(let i = 0;i < val.length;i++){
            if(val[i].role === 'human'){
              this.changeFirstQuestion(val[i]?.content)
              break;
            }
          }
        }
      },
      deep:true,
    },
    firstView: {
      immediate: true,
      handler() {
        this.$nextTick(() => {
          this.addScrollToBottomButtonEvent()
        })
      }
    }
  },
  mounted() {
    this.initializeFetch()
    this.getIsShowScrollToBottomButton()
    this.addScrollToBottomButtonEvent()
  },
  beforeDestroy() {
    this.removeScrollToBottomButtonEvent()
  },
  methods: {
    getIsShowScrollToBottomButton() {
      setInterval(() => {
        const messageListElement = this.$refs.ChatAIMessageListElement as HTMLElement
        if (!messageListElement || this.isStreaming) {
          return
        }
        // 允许 1-5 像素的误差
        const threshold = 5
        const isAtBottom = (messageListElement.scrollTop + messageListElement.clientHeight) >= (messageListElement.scrollHeight - threshold)
        // 判断不在底部停留时，显示一键移动到底部操作
        this.isShowScrollToBottomButton = !isAtBottom
      }, 1000)
    },
    addScrollToBottomButtonEvent() {
      const chatAIMessageListElement = this.$refs.ChatAIMessageListElement as HTMLElement
      chatAIMessageListElement?.addEventListener('scroll', this.addScrollToBottomButtonEventHandler.bind(this))
    },
    removeScrollToBottomButtonEvent() {
      const chatAIMessageListElement = this.$refs.ChatAIMessageListElement as HTMLElement
      chatAIMessageListElement?.removeEventListener('scroll', this.addScrollToBottomButtonEventHandler.bind(this))
    },
    addScrollToBottomButtonEventHandler() {
      // this.isAutoScrollToBottom = false
      console.log('addScrollToBottomButtonEventHandler', this.isAutoScrollToBottom)
    },
    onScrollToBottomButtonClickHandler() {
      const messageListElement = this.$refs.ChatAIMessageListElement as HTMLElement
      messageListElement.scrollTo({
        top: messageListElement.scrollHeight,
        behavior: 'smooth'
      })
    },
    activeHandler() {
      this.$nextTick(() => {
        this.scrollMessageListToBottom()
      })
    },
    /** 
     * @description 添加用户消息
    */
    addUserMessage(content: string, isSendToServer: boolean) {
      // 添加一条用户消息
      const userMessage = getUserMessageByContent(content)
      // 是否推送到服务器
      const _isSendToServer = isSendToServer
      // 推送消息
      return this.pushMessage(userMessage, _isSendToServer)
    },
    /** 
     * @description 添加助理加载消息
    */
    addAILoadingMessage() {
      
      // 如果最后一条消息不是用户消息，则不添加助理加载消息
      if (isFalsy(this.isLastMessageIsUserMessage)) {
        return
      }
      
      // 添加一条助理加载消息
      const assistantLoadingMessage = getAssistantLoadingMessage()
      // 不推送到服务器
      const isSendToServer = false
      // 推送消息
      return this.pushMessage(assistantLoadingMessage, isSendToServer)
      
    },
    /** 
     * @description 添加助理停止消息
    */
    addAIStopMessage() {
      
      // 如果最后一条消息不是用户消息，则不添加助理加载消息
      if (isFalsy(this.isLastMessageIsUserMessage)) {
        return
      }
      
      // 添加一条助理停止
      const assistantStopMessage = getAssistantStopMessage()
      // 不推送到服务器
      const isSendToServer = false
      // 推送消息
      return this.pushMessage(assistantStopMessage, isSendToServer)
      
    },
    /** 
     * @description 清空消息
    */
    clearMessages() {
      this.messages = []
    },
    /** 
     * @description 清空输入框消息
    */
    clearChatAIInputBarMessageContent() {
      this.chatAIInputBarComponent?.clearMessageContent()
    },
    /** 
     * @description 删除消息
    */
    deleteMessage(index: number) {
      this.messages.splice(index, 1)
    },
    /** 
     * @description 删除助理加载消息
    */
    deleteAILoadingMessage() {
      
      // 获取最后一条消息
      const lastMessage = this.lastMessage
      // 获取默认的助理加载消息
      const assistantLoadingMessage = getAssistantLoadingMessage()
      // 判断最后一条消息是否是助理加载消息
      const isLastMessageIsAssistantLoadingMessage = (
        lastMessage?.role == OpenAIMessageRoleEnum.Assistant 
        && lastMessage?.content == assistantLoadingMessage.content
      )
      
      // 删除助理加载消息
      const lastMessageIndex = this.messages.length - 1
      isLastMessageIsAssistantLoadingMessage && this.deleteMessage(lastMessageIndex)
    },
    /** 
     * @description 内容安全文本检测
    */
    fetchContentSecurity(messageContent: string) {
      
      const params = {
        content: messageContent
      }
      
      return (
        this.fetchContentSecurityText(params).then(result => {
          
          return result
          
        }).catch(error => {
          
          return false
          
        })
      )
      
    },
    getUserId() {
      
      // 非分享，获取当前租户的用户 id
      if (isFalsy(this.isShare)) {
        const rootWindowInitData = getRootWindowInitData()
        const userId = rootWindowInitData?.user?.userId || ''
        return userId
      }
      
      return this.userId
    },
    /**
     * @description 获取聊天基础参数
     */
    getChatBaseParams(): AiChatAgentConversationParamType {
      return {
        tenantId: this.tenantId,
        userId: this.getUserId(),
        id: Number(this.robotId),
        agentId: Number(this.robotId),
        question: '',
        model: this.model,
        disabledAgentEnabledValidate: this.disabledAgentEnabledValidate,
        source: this.source,
        doorId: this.doorId,
        conversationId: this.conversationId,
      }
      
    },
    /** 
     * @description 初始化
    */
    initialize() {
      this.clearMessages()
      this.initSystemMessage()
    },
    async initializeFetchBase(initAfterCallback: VoidFunction) {
      try {
        
        this.isFirstClickHistoryMessageTip = true
        this.page.pageNum = 1
        this.messageList = []
        
        this.showLoading()

        if (this.isSendMessageToServer) {
          await this.fetchChatConversation(this.conversationParams)
        }
        
        // 初始化后异步回调
        if (isAsyncFunction(initAfterCallback)) {
          await initAfterCallback()
        }
        
        // 初始化后同步回调
        if (isFunction(initAfterCallback)) {
          initAfterCallback()
        }
        
        const IMMessages = this.messageList
        
        // 如果没有历史消息，则初始化一条系统消息
        if (isEmpty(IMMessages)) {
          this.initialize()
          return
        }
        
        this.$nextTick(() => {
          this.scrollMessageListToBottom()
        })
        
      } catch (error) {
        console.error(error)
      } finally {
        this.hideLoading()
      }
    },
    /** 
     * @description 初始化请求
    */
    async initializeFetch() {
      this.initializeFetchBase(this.initAfterBaseCallback)
    },
    async initializeFetchWithChangeApp() {
      
      const initAfterCallback = () => {
        // @ts-ignore
        this.initAfterChangeAppCallback()?.then(() => {
          this.addHistoryLastMessage()
        })
      }
      
      await this.initializeFetchBase(initAfterCallback)

      // 初始化变量数据列表
      this.varDataList = {}
      const messageTest = findComponentDownward(this, ComponentNameEnum.ChatAIMessageTest)
      if (messageTest) {
        messageTest.initVarDataListEvent()
      }
      
    },
    initAfterBaseCallback() {
      
      // 获取历史消息 - 如果是分享，则不获取历史消息
      if (this.isShare) {
        return
      }
      
      // return this.fetchHistoryMessage(this.historyMessageParams)
      
    },
    initAfterChangeAppCallback() {
      return this.initAfterBaseCallback()
    },
    addHistoryLastMessage() {
      
      // 如果没有历史消息，则不添加
      if (isFalsy(this.isHaveNextPage)) {
        return
      }
      
      // 原始消息列表
      const originMessageList = this.messagePage?.list || []
      // 消息列表，需要反转下数据
      const messages = originMessageList.map(chatIMMessageToAIMessage).reverse()
      
      // 最后一条消息
      const lastMessage = messages[messages.length - 1]
      // 倒数第二条消息
      const secondLastMessage = messages[messages.length - 2]
      
      // 最后一条消息是用户消息
      const isLastMessageIsUserMessage = isUserMessage(lastMessage)
      // 最后一条消息是助理消息
      const isLastMessageIsAssistantMessage = isAssistantMessage(lastMessage)
      // 倒数第二条消息是用户消息
      const isSecondLastMessageIsUserMessage = isUserMessage(secondLastMessage)
      
      // 如果最后一条消息是用户消息, 则只添加这条消息，并且删除消息列表中的最后一条消息
      if (isLastMessageIsUserMessage) {
        this.unshiftMessage(lastMessage)
        this.deleteMessagePageListHandler(1)
        return
      }
      
      // 如果最后一条消息是助理消息, 且倒数第二条消息是用户消息, 则只添加这两条消息, 并且删除消息列表中的最后两条消息
      if (isLastMessageIsAssistantMessage && isSecondLastMessageIsUserMessage) {
        
        this.unshiftMessage(lastMessage)
        this.unshiftMessage(secondLastMessage)
        
        this.deleteMessagePageListHandler(2)
        
        return
      }
      
    },
    deleteMessagePageListHandler(deleteNumber: number) {
      const newMessageList = this.messagePage.list.reverse().slice(0, -deleteNumber).reverse()
      this.updateMessagePageHandler(newMessageList)
    },
    updateMessagePageHandler(newMessageList: ChatIMMessageItemType[]) {
      
      const newMessagePage = new Page({
        ...this.messagePage,
        list: newMessageList
      })
      
      this.setMessagePage(newMessagePage)
      
    },
    /** 
     * @description 进入页面时，默认初始化一条系统消息，用于显示
    */
    initSystemMessage() {
      const systemMessage = getSystemMessageByContent(
        this.currentApp?.welcomeMessage
        || this.chatSystemMessage
        || DEFAULT_CHAT_SYSTEM_MESSAGE
      )
      const isSendToServer = false
      const delay = 0
      const isSystem = true
      this.pushMessage(systemMessage, isSendToServer, delay, isSystem)
    },
    /** 
     * @description 初始化 AI 应用聊天 id
    */
    initId(id: string | undefined | null) {
      this.id = id || ''
    },
    unshiftMessage(message: OpenAIMessageItemType) {
      
      if (isUndefined(message.likeStatus)) {
        message.likeStatus = undefined
      }
      
      this.messages.unshift(message)
      
      this.$nextTick(() => {
        this.scrollMessageListToBottom()
      })
      
      setTimeout(() => {
        this.scrollMessageListToBottom()
      }, 300)
      
    },
    sleep(duration: number) {
      return new Promise((resolve, reject) => {
        setTimeout(resolve, duration);
      })
    },
    /** 
     * @description 添加消息
    */
    async pushMessage(
      message: OpenAIMessageItemType, 
      isSendToServer: boolean = true, 
      delay: number = 0, 
      isSystem: boolean = false
    ) {
      
      if (isUndefined(message.likeStatus)) {
        message.likeStatus = undefined
      }

      if (isSystem) {
        message.isSystem = true
      }
      
      this.messages.push(message)
      
      this.$nextTick(() => {
        this.scrollMessageListToBottom()
      })
      
      setTimeout(() => {
        this.scrollMessageListToBottom()
      }, 300)

      if (isFalsy(this.isSendMessageToServer)) {
        return
      }
      
      if (isFalsy(isSendToServer)) {
        return
      }
      
      if (isUserMessage(message)) {
        
        if (delay) {
          await this.sleep(delay)
        }
        
        return this.sendNewMessageByUser(message)
      }
      
      if (isNotUserMessage(message)) {
        
        if (delay) {
          await this.sleep(delay)
        }
        
        return this.sendNewMessageByBot(message)
      }
      
    },
    /** 
     * @description 设置消息列表
    */
    setMessages(messages: OpenAIMessageItemType[]) {
      this.messages = messages
    },
    sendNewMessageByUser(message: OpenAIMessageItemType) {
      
      let params: SendChatConversationContentShareModel = {
        sender: this.getUserId(),
        content: message.content || '',
        contentType: "text",
        type: "user",
        conversationId: this.conversationId,
        share: Number(this.isShare)
      }
      
      if (this.isShare) {
        params = {
          ...params,
          tenantId: this.tenantId,
          loginUserId: this.getUserId(),
          userId: this.getUserId()
        }
      }
      
      return this.fetchSendNewMessage(params).then(conversationContentId => {
        message.id = conversationContentId
      })
      
    },
    initChat() {

    },
    sendNewMessageByBot(message: OpenAIMessageItemType) {
      
      let params: SendChatConversationContentShareModel = {
        sender: this.robotId as string,
        content: message.content || '',
        contentType: "text",
        type: "ai",
        conversationId: this.conversationId,
        share: Number(this.isShare)
      }
      
      if (this.isShare) {
        params = {
          ...params,
          tenantId: this.tenantId,
          loginUserId: this.getUserId(),
          userId: this.getUserId()
        }
      }
      
      return this.fetchSendNewMessage(params).then(conversationContentId => {
        message.id = conversationContentId
      })
      
    },
    /** 
     * @description 历史消息点击事件
    */
    onHistoryMessageTipClickHandler() {
      
      if (this.disabled) {
        return
      }
      
      if (this.foldHistoryMessageList.length) {
        
        const messages = [
          ...this.foldHistoryMessageList,
          ...this.messages
        ]
        
        this.setMessages(messages)
        this.foldHistoryMessageList = []
        this.messagePage.hasNextPage = false
        
        return
      }
      
      if (
        this.isFirstClickHistoryMessageTip 
        && this.page.pageNum == 1 
        && isNotEmpty(this.messagePage?.list)
      ) {
        
        // 原始的后端消息列表，需要反转下数据
        const originServerMessages = this.messagePage.list.map(chatIMMessageToAIMessage).reverse()
        
        // 当前的消息列表 Map 对象
        const currentMessagesMap = new Map()
        this.messages.forEach(message => {
          currentMessagesMap.set(message.id, message)
        })
        
        // 消息列表根据 id 去重
        const removeDuplicatesMessages = originServerMessages.filter(message => {
          return isFalsy(currentMessagesMap.has(message.id))
        })
        
        // 设置消息列表
        this.setMessages([...removeDuplicatesMessages, ...this.messages])
        
        // 更新状态 - 不是第一次点击历史消息
        this.isFirstClickHistoryMessageTip = false
        
        return
      }
      
      this.page.pageNum++
      
      // this.fetchHistoryMessage(this.historyMessageParams).then((messageList: ChatIMMessageItemType[]) => {
      //   // 消息列表，需要反转下数据
      //   const messages = messageList.map(chatIMMessageToAIMessage).reverse()
      //   this.setMessages([...messages, ...this.messages])
      // })

    },
    /** 
     * @description 历史消息折叠点击事件
    */
    onHistoryMessageTipFoldClickHandler() {
      
      const historyMessageList = this.messages.filter(message => message?.isHistory)
      this.foldHistoryMessageList = cloneDeep(historyMessageList)
      
      const isNotShowHistoryMessageList = this.messages.filter(message => isFalsy(message?.isHistory))
      
      this.setMessages(isNotShowHistoryMessageList)
      
      this.messagePage.hasNextPage = true
      
    },
    /** 
     * @description 输入框聚焦事件
    */
    onInputBarFocusHandler() {
      this.focusNumber++
    },
    async onStopHandler() {
      try {

        this.isStop = true
        
        if (this.stream) {
          this.answering = false
          this.controller?.abort()
        } else {
          await aiChatAgentConversation({} as AiChatAgentConversationParamType)
        }
        
        this.deleteAILoadingMessage()
        this.addAIStopMessage()
        
      } catch (error) {
        console.error(error)
      }
    },
    onQuestionHandler(question: string) {
      if (this.isStreaming || this.chatLoading) {
        return
      }
      // 推送消息到服务器
      const isUserMessageSendToServer = true
      // 是否清空输入框内容
      const isClearInputContent = true
      // 发送消息
      this.onSendButtonClickHandler(question, isUserMessageSendToServer, isClearInputContent)
    },
    /** 
     * @description 重新回答 事件
    */
    onRefreshHandler() {
      
      // 当前的消息列表
      const messages = this.messages
      
      // 最后一条用户消息内容
      let lastUserMessageContent = ''
      // 最后第二条消息索引
      let lastSecondMessageIndex = messages.length - 2
      
      // 倒数第二条消息是用户消息，则取这条消息的内容
      const lastSecondMessage = messages[lastSecondMessageIndex]
      const isLastSecondMessageIsUserMessage = isUserMessage(lastSecondMessage)
      
      if (isFalsy(isLastSecondMessageIsUserMessage)) {
        throw new Error('倒数第二条消息不是用户消息')
      }
      
      const lastMessage = messages[messages.length - 1]
      const lastMessageId = lastMessage?.id
      
      // 删除当前 AI 回复的消息
      this.fetchDeleteMessage({
        conversationId: this.conversationId,
        conversationContentId: lastMessageId
      })
      
      lastUserMessageContent = lastSecondMessage?.content || ''
      
      // 然后清空 最后的两条消息 (一条是用户消息 + 一条是 AI 之前的回复消息)
      this.setMessages(
        messages.slice(0, lastSecondMessageIndex)
      )
      
      // 当前这次重新回答的用户消息，不推送到服务器
      const isUserMessageSendToServer = false
      // 是否清空输入框内容
      const isClearInputContent = false
      // 发送消息
      this.onSendButtonClickHandler(lastUserMessageContent, isUserMessageSendToServer, isClearInputContent)
      
    },
    /** 
     * @description 发送消息按钮点击事件
    */
    async onSendButtonClickHandler(messageContent: string, isSendToServer: boolean = true, isClearInputContent: boolean = true) {
      try {

        console.log('onSendButtonClickHandler messageContent', messageContent)
        console.log('onSendButtonClickHandler isSendToServer', isSendToServer)
        console.log('onSendButtonClickHandler isClearInputContent', isClearInputContent)
        
        // 正在加载中，则不发送消息
        if (this.loading) {
          return
        }
        
        if(
          // @ts-ignore
          this.varDataList?.variable?.length > 0 
          && (
            !this.validValue.every(item => item === true)
          )
        ) {
          const testFormComponents = findComponentsDownward(this, 'testForm') as Record<string, any>[]
          testFormComponents.forEach(component => {
            component.validateForm()
          })
          return
        }

        this.firstView = false;
        
        this.chatLoading = true
        
        const content = messageContent.trim()
        // 消息内容为空，则不发送消息
        if (isFalsy(content)) {
          this.$message.error('请输入问题')
          // 清空输入框消息
          isClearInputContent && this.clearChatAIInputBarMessageContent()
          return
        }
        
        // 添加用户消息
        await this.addUserMessage(content, isSendToServer)
        
        // 默认添加一条助理加载消息
        setTimeout(() => {
          this.addAILoadingMessage()        
        })
        
        // 清空输入框消息
        isClearInputContent && this.clearChatAIInputBarMessageContent()
        
        // 发送消息
        const result = await this.sendMessage(content)
        
        this.$nextTick(() => {
          this.scrollMessageListToBottom()
        })
        
        // 添加助理回复消息
        if (result) {
          const isSendToServer = true
          const delay = 1000
          await this.pushMessage(result, isSendToServer, delay)
        }
        
        this.$nextTick(() => {
          this.scrollMessageListToBottom()
        })
        
        const times = [0, 300, 500, 1000]
        times.forEach(time => {
          setTimeout(() => {
            this.scrollMessageListToBottom()
          }, time)
        })
        
      } finally {
        this.chatLoading = false
        this.isStreaming = false
        this.isStop = false
        this.hideLoading()
      }
    },
    /** 
     * @description 发送消息会话问题 到 服务端
    */
    async sendMessage(content: string) {
      
      const loadingMessage = getAssistantLoadingMessage()
      
      // 过滤掉空消息
      let validMessages = this.messages.filter(message => Boolean(message?.content))
      // 过滤掉助理加载消息
      validMessages = validMessages.filter(message => {
        return isNotEqual(message?.content, loadingMessage.content)
      })
      // 过滤掉历史消息
      validMessages = validMessages.filter(message => isFalsy(message?.isHistory))
      // 过滤掉已停止的消息
      validMessages = validMessages.filter(message => isFalsy(message?.isStop))
      
      // 过滤掉 AI 回复的超长消息
      validMessages = validMessages.filter(message => {
        return Number(message?.content?.length || 0) <= 1000
      })
      
      // 如果上次助理消息是错误消息，则只取最后一条消息，没错则取最后20条消息
      const messageNumber = this.isLastAssistantMessageError ? 1 : 20
      const contextMessages = validMessages.slice(-messageNumber)
      
      const newContextMessages = contextMessages.map(message => {
        return {
          content: message.content,
          type: message.role
        }
      })
      
      const baseParams: AiChatAgentConversationParamType = this.getChatBaseParams()
      
      // let params: AiChatAgentConversationParamType = {
      //   ...baseParams,
      //   question: content
      // }
      let params = 
        JSON.stringify(this.varDataList) !== "{}" || ""  
        ? {...this.varDataList, question: content, conversationId: this.conversationId } 
        : {...baseParams, question: content, conversationId: this.conversationId }
      
      // 如果流状态为 true，则使用流接收消息
      if (this.stream) {
        return this.sendMessageWithStream(params)
      }
      
      try {
        
        // @ts-ignore
        const result = await aiChatAgentConversation(params)
        this.isLastAssistantMessageError = isFalsy(result?.success)

        // 删除 AI 助理加载消息
        this.deleteAILoadingMessage()
        
        if (isFalsy(result?.success)) {
          this.$message.error(result?.message)
          return
        }
        
        const data = result?.data || {} as AiChatAgentConversationResultType
        // AI 回复的消息内容
        let resultContent = data?.text || '系统繁忙，请稍后再试'
        
        if (isString(data)) {
          resultContent = data as unknown as string
        }
        
        const message: OpenAIMessageItemType = {
          // 消息内容
          content: resultContent,
          // 消息类型
          role: OpenAIMessageRoleEnum.Assistant,
          id: uuid()
        }
        
        return message
        
      } catch (error) {
        
        this.deleteAILoadingMessage()
        this.isLastAssistantMessageError = true
        
        console.error(error)

        
      }
      
    },
    getSspAccessToken() {
      return localStorage.getItem('accessToken')
    },
    /** 
     * @description 发送消息会话问题 (流式)
    */
    async sendMessageWithStream(params: Record<string, any>) { 
      
      console.log('sendMessageWithStream isAutoScrollToBottom', this.isAutoScrollToBottom)
      
      this.isAutoScrollToBottom = true
      const controller = new AbortController();
      this.controller = controller;
      let eventCount = 0;
      let buffer = ''; // 添加缓冲区存储不完整的数据
      this.isStreaming = true
      let text = ''
      let businessSourceDocumentsJSON = '';

      const sspAccessToken = this.getSspAccessToken()

      const url = this.isShare ? '/api/voice/outside/xiaobao/chat/handle/share/handleChatStream' : '/api/voice/outside/xiaobao/chat/handle/handleChatStream'
      
      try {
        const tokenInfo = userCenterGetTokenInfo() as Record<string, any>
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'token': tokenInfo?.token || '',
            'door-token': this.isShare ? sspAccessToken || '' : ''
          },
          signal: controller.signal,
          body: JSON.stringify(params),
        });
      
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const reader = response.body?.getReader();
        if (!reader) {
          throw new Error('Response body is null');
        }
        
        const decoder = new TextDecoder();
      
        this.answering = true
        const endThinkTagText = '</think>'
        let isFirst = true

        while (true) {
          const { value, done } = await reader.read();
          if (done) {
            this.answering = false
            console.log(`流式响应结束，共收到 ${eventCount} 条消息`);
            break;
          }
      
          // 解码当前数据块并添加到buffer
          buffer += decoder.decode(value, { stream: true });
          
          try {
            if (isFirst) {
              const data = decoder.decode(value, { stream: true })
              const isErrorJSON = isJSONObject(data)
              if (isErrorJSON) {
                const error = parse_with_default_value(data, {} as Record<string, any>)
                const isFail = MsgModel.isFail(error)
                if (isFail) {
                  this.$message.error(error?.message || '网络异常，请稍后再试')
                  this.deleteAILoadingMessage()
                  this.answering = false
                  return
                }
              }
            }
          } catch (error) {
            console.warn(error)
          } finally {
            isFirst = false
          }
          
          // 按行分割处理数据
          const lines = buffer.split('\n');
          
          // 保留最后一行（可能不完整）
          buffer = lines.pop() || '';
        
          // 处理完整的行
          for (const line of lines) {
            if (line.trim() === '') continue; // 跳过空行
            if (line.startsWith('data:')) {
              eventCount++;
              const message = line.replace(/^data:\s*/, '').trim();
              
              console.log(`第 ${eventCount} 条消息:`, message);
              
              if (message) {
                // 更新助理消息
                let textValue = JSON.parse(message).data.text || ''

                // 如果  textValue 包含 </think>, 则在 </think> 前面加一个空的 div
                if (textValue.includes(endThinkTagText)) {
                  const endThinkTagIndex = textValue.indexOf(endThinkTagText)
                  const before = textValue.slice(0, endThinkTagIndex)
                  const after = textValue.slice(endThinkTagIndex)
                  textValue = before + `<div></div>` + after
                }

                text+= textValue
                this.messages[this.messages.length - 1].content = text;
                // 滚动消息列表到底部
                this.scrollMessageListToBottom();
                if (text) {
                // 删除 AI 助理加载消息
                this.deleteAILoadingMessage()
              }
              }
            }
          }
        }
      
      } catch (error) {
        console.error('Stream error:', error);
      }
      
      this.isLastAssistantMessageError = isFalsy(text);

      if (this.isStop) {
        this.deleteAILoadingMessage()
        this.addAIStopMessage()
        return
      }

      if (isFalsy(text) && !this.isStop) {
        
        this.$message.error('网络异常，请稍后再试')
        
        if (isFalsy(this.lastMessage?.content)) {
          this.messages.pop()
        }

        this.deleteAILoadingMessage()
        
        return
      }
      
      const businessSourceDocuments = parse_with_default_value(businessSourceDocumentsJSON, [])
      const message: OpenAIMessageItemType = {
        // 消息内容
        content: text,
        // 消息类型
        role: OpenAIMessageRoleEnum.Assistant,
        // 业务数据来源列表
        businessSourceDocuments: businessSourceDocuments,
        id: uuid()
      }
      
      const lastMessageIndex = this.messages.length - 1
      this.deleteMessage(lastMessageIndex)
      
      return message
    },
    /** 
     * @description 滚动消息列表到底部
    */
    scrollMessageListToBottom() {

      const chatAIMessageListElement = this.$refs.ChatAIMessageListElement as HTMLDivElement
      
      console.log('scrollMessageListToBottom', this.isAutoScrollToBottom, chatAIMessageListElement)

      if (this.isAutoScrollToBottom && chatAIMessageListElement) {
        chatAIMessageListElement.scrollTo({
          top: chatAIMessageListElement.scrollHeight + 1000,
          behavior: 'smooth'
        })
      }
      
    },
    /** 
     * @description 设置输入框消息内容
    */
    setInputMessageContent(messageContent: string) {
      this.chatAIInputBarComponent?.onInputHandler(messageContent)
    },
    /** 
     * @description 内容安全文本检测
     * @param {string} content 消息内容
     * 
    */
    async validateContentSecurity(content: string) {
      
      const isPaasContentSecurity = await this.fetchContentSecurity(content)
      
      // 如果不通过，则不发送消息, 并将当前输入框的值设置为原始消息内容
      if (isFalsy(isPaasContentSecurity)) {
        
        this.setInputMessageContent(content)
        this.hideLoading()
        
        const errorMessage = '消息内容含有敏感词，请重新输入'
        
        if (isFalsy(isPaasContentSecurity)) {
          Platform.alert(errorMessage)
        }
        
        throw new Error(errorMessage)
        
      }
      
    },
    fetchQuestionInStandardLibImpl(content: string) {
      
      const params = {
        question: content,
        tenantId: this.tenantId
      }
      
      try {
        return this.fetchQuestionInStandardLib(params)
      } catch (error) {
        return Promise.resolve(null)
      }
      
    },
    onClickAppTagHandler() {
      
      if (this.disabled) {
        return
      }
      
      this.$emit(ChatAIViewEventEnum.AppTag)
    },
    onPromptWordClickHandler(value: string) {
      this.setInputMessageContent(value)
    },
    onModelDropdownCommand(model: AiModelEnum) {
      this.model = model
      this.$emit(ChatAIViewEventEnum.ChangeModel, model)
    },
    changeModelHandler(model: AiModelEnum) {
      this.model = model || AiModelEnum.DOU_BAO
    },
    handlerChangeWelcomeMessage(welcomeMessage: string) {
      const systemMessageItem = this.messages.find(message => message.isSystem)
      if (systemMessageItem) {
        systemMessageItem.content = welcomeMessage
      }
    },
    renderModelDropdown() {

      if (!this.allowModifyAIModel) {
        return null
      }

      return (
        <el-dropdown
          trigger="click"
          onCommand={this.onModelDropdownCommand}
        >

            <ChatAIAppTag
              class="chat-ai-view-model-dropdown"
            >
              <div class="ai-agent-detail-header-model-dropdown">
                <ModelItem value={this.model} />
              </div>
            </ChatAIAppTag>

          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command={AiModelEnum.DEEPSEEK}>
              <ModelItem value={AiModelEnum.DEEPSEEK} nowModel={this.model} />
            </el-dropdown-item>
            <el-dropdown-item command={AiModelEnum.TONG_YI}>
              <ModelItem value={AiModelEnum.TONG_YI} nowModel={this.model} />
            </el-dropdown-item>
            <el-dropdown-item command={AiModelEnum.DOU_BAO}>
              <ModelItem value={AiModelEnum.DOU_BAO} nowModel={this.model}/>
            </el-dropdown-item>
            <el-dropdown-item command={AiModelEnum.MOON_SHOT}>
              <ModelItem value={AiModelEnum.MOON_SHOT} nowModel={this.model}/>
            </el-dropdown-item>
            <el-dropdown-item command={AiModelEnum.DING_DING}>
              <ModelItem value={AiModelEnum.DING_DING} nowModel={this.model}/>
            </el-dropdown-item>
            {/* <el-dropdown-item command={AiModelEnum.SHB}>
              <ModelItem value={AiModelEnum.SHB} nowModel={this.model}/>
            </el-dropdown-item> */}
          </el-dropdown-menu>
        </el-dropdown>
      )
    },
    renderScrollToBottomButtonWrapper() {

      if (this.firstView || !this.isShowScrollToBottomButton) {
        return null
      }
      
      return (
        <div class="chat-ai-view-scroll-to-bottom-button-wrapper">
          {this.renderScrollToBottomButton()}
        </div>
      )
    },
    renderScrollToBottomButton() {
      return (
        <div 
          class="chat-ai-view-scroll-to-bottom-button"
          onClick={this.onScrollToBottomButtonClickHandler}
        >
          <i class="iconfont icon-more" />
        </div>
      )
    }
  },
  render() {
    return (
      <div class={this.viewClassNames} >
        {this.firstView ? <div class="first-view-box" >
          <div class="first-view">
            <div class="font-32 font-w-500 mar-b-24 overHideCon-1">{this.welcomeTip}{this.currentUser?.staffId ? Platform.isOpenData ?  <open-data type="userName" openid={this.currentUser.staffId}></open-data> : this.currentUser.displayName : ''}</div>
            {
              this.currentApp?.variable?.length > 0 && (
                <div class="first-view-content">
                  <chat-ai-message-test
                    currentApp={this.currentApp}
                    isViews={this.isViews}
                  >
                  </chat-ai-message-test>
                </div>
              )
            }
            {this.welcomeTips || this.defaultQuestion?.length ? <div class="first-view-content">
              <p>{this.welcomeTips}</p>
              {
                this.defaultQuestion?.length ? <p class="font-12" style={this.welcomeTips ? 'border-width: 1px 0px 0px 0px;border-style: solid;border-color: #E4E7ED;color:#8c8c8c;padding-top:8px;' : ''}>你可以这样问</p> : ''
              }
              {this.defaultQuestion.map((question, index) => (
                <div 
                  class="chat-ai-message-list-item-question-item mar-b-8"
                  onClick={() => this.onQuestionHandler(question)}
                >
                  {question}
                  <i class="el-icon-arrow-right" />
                </div>
              ))}
            </div> : ''}
          </div> 
        </div>
        : 
        <div 
          class="chat-ai-view-message-list"
          ref="ChatAIMessageListElement"
        >
          
          {this.isHaveNextPage && (
            <chat-ai-history-message-tip
              onClick={this.onHistoryMessageTipClickHandler}
            >
            </chat-ai-history-message-tip>
          )}
          
          {this.isShowHistoryFoldMessage && (
            <chat-ai-history-message-tip
              isDone
              onClick={this.onHistoryMessageTipFoldClickHandler}
            >
            </chat-ai-history-message-tip>
          )}
          
          <chat-ai-message-list 
            currentApp={this.currentApp}
            isStreaming={this.isStreaming}
            isShare={this.isShare}
            value={this.messages}
            robotId={this.robotId}
            robotAppId={this.robotAppId}
            userId={this.userId}
            tenantId={this.tenantId}
            onRefresh={this.onRefreshHandler}
            onStop={this.onStopHandler}
            onQuestion={this.onQuestionHandler}
            answering={this.answering}
            isViews={this.isViews}
            varDataList={this.varDataList}
          >
          </chat-ai-message-list>
          
        </div>
        }
        
        
        
        {/* start 底部 */}
        <div class={['chat-ai-view-footer', 'chat-ai-view-footer-first', this.firstView ? 'max-w-654' : '']}>
          
            {/* {this.renderScrollToBottomButtonWrapper()} */}
          
            <chat-ai-input-bar
              ref="ChatAIInputBarComponent"
              disabled={this.disabled}
              answering={this.answering || this.chatLoading}
              onSend={this.onSendButtonClickHandler}
              onFocus={this.onInputBarFocusHandler}
              onStop={this.onStopHandler}
            >
              <div class={this.appTagClass}>

              {this.mode === 'setPreview' ? this.currentAgent.type === 0 ? <ChatAIAppTag
                value={this.currentApp?.name}
                onClick={this.onClickAppTagHandler}
              >
              </ChatAIAppTag> : '' :  <ChatAIAppTag
                value={this.currentApp?.name}
                onClick={this.onClickAppTagHandler}
              >
              </ChatAIAppTag>}

              {this.renderModelDropdown()}

              </div>
            </chat-ai-input-bar>
          
          
        </div>
        {/* end 底部 */}
        
      </div>
    ) as any
  }
})