
.chat-ai-index {
  
  height: 100%;
  width: 100%;
  
  .chat-ai-view-message-list {
    &::-webkit-scrollbar {
      display: none;
      width: 0;
    }
  }
  
}

.chat-ai-frame {
  height: 100%;
  width: 100%;
  overflow: hidden;
  position: relative;
  
  iframe {
    height: 100%;
    width: 100%;
    border: none;
  }
  
}

.chat-ai-view {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: auto;
}

.chat-ai-view--focus {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom); 
}

.chat-ai-view-message-list {
  background-color: #fff;
  flex: 1;
  overflow-y: auto;
  padding-top: 20px;
  width: 100%;
  max-width: 100%;
  max-width: 848px;
  .chat-ai-message-list {
    width: 100%;
    max-width: 100%;
  }
  
}

.chat-ai-view-v2 {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow: auto;
    align-items: center;
  .chat-ai-view-footer-app-tag-v2 {
    background-color: transparent;
    padding: 0 20px;
    .chat-ai-app-tag-v2 {
      cursor: pointer;
    }
  }
  .chat-ai-view-footer-prompt-word-list {
    padding: 0 24px;
    .chat-ai-prompt-tag {
      margin-top: 12px;
      margin-right: 12px;
    }
  }
}
.chat-ai-view-v2{
  justify-content: center;
}

.chat-ai-view-footer-app-tag--disabled {
  cursor: not-allowed;
}

.chat-ai-view-footer-app-tag-v2 {
  display: flex;
  align-items: center;
  position: absolute;
  bottom: 26px;
  z-index: 99;
  left: 12px;
}

.chat-ai-view-model-dropdown {
  display: flex;
  margin-left: 12px;
  align-items: center;
}


.chat-ai-view-footer{
  position: relative;
}

.first-view-box{
  display: flex;
  justify-content: center;
  width: 100%;
  max-width: 702px;
  padding: 24px;
  background-color: #fff;
  .first-view{
    width: 100%;
    height: 100%;
    
    display: flex;
    flex-direction: column;
    align-items: center;
    max-width: 848px;
    .first-view-content{
      padding: 16px;
      margin: 16px 24px;
      background-color: #FAFAFA;
      width: 100%;
    }
  }
}


.chat-ai-view-footer-first{
  z-index: 103;
  display: flex;
  justify-content: center;
  width: 100%;
}

.max-w-654{
  max-width: 702px !important;
}

.first-view-box {
  .chat-ai-message-test {
    padding: 0;
    .test-box {
      background-color: transparent;
      padding: 0;
    }
  }
  .first-view-content + .first-view-content {
    margin-top: 0;
  }
}

.ai-edit-preview{
  .chat-ai-view-v2 {
    .chat-ai-view-message-list {
      padding-top: 0;
    }
  }
}

.chat-ai-view-scroll-to-bottom-button-wrapper {
  position: absolute;
  top: -50px;
  left: 50%;
  transform: translateX(-10px);
}

.chat-ai-view-scroll-to-bottom-button {
  cursor: pointer;
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background: #FFFFFF;
  box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
}