/* components */
import ChatAIView from '@src/component/business/BizChatPanelNew/chat/view'
/* enum */
import { ComponentNameEnum } from '@src/component/business/BizChatPanelNew/chat/model/enum'
/* scss */
import '@src/component/business/BizChatPanelNew/chat/index.scss'
/* vue */
import { defineComponent, ref, PropType } from 'vue'
/* types */
import { SettingGPTServiceItem } from '@gpt/types'
import { AIAgentAppComponentType, AIAgentBaseType, AIAgentType } from '@src/modules/ai/types'
import { AIAgentLogSourceEnum, AiModelEnum } from '@src/modules/ai/model/enum'

type ChatAIViewComponent = InstanceType<typeof ChatAIView>

export type ChatAIIndexProps = {
  
}

export interface ChatAIIndexSetupState {
  
}

export enum ChatAIIndexEventEnum {
  Input = 'input',
  AppTag = 'appTag',
  ChangeModel = 'changeModel'
}

export default defineComponent({
  name: ComponentNameEnum.ChatAIIndex,
  components: {
    ChatAIView
  },
  props: {
    disabledAgentEnabledValidate: {
      type: Boolean,
      default: false
    },
    isShare: {
      type: Boolean,
      default: false
    },
    isSendMessageToServer: {
      type: Boolean,
      default: true
    },
    agentId: {
      type: [String, Number],
      default: ''
    },
    tenantId: {
      type: String,
      default: ''
    },
    userId: {
      type: String,
      default: ''
    },
    stream: {
      type: Boolean,
      default: false
    },
    currentApp: {
      type: Object as PropType<AIAgentAppComponentType>,
      default: () => ({})
    },
    currentAgent: {
      type: Object as PropType<AIAgentBaseType>,
      default: () => ({})
    },
    disabled: {
      type: Boolean,
      default: false
    },
    chatSystemMessage: {
      type: String,
      default: ''
    },
    onAppTag: {
      type: Function
    },
    onChangeModel: {
      type: Function
    },
    mode:{
      type: String,
      default: ''
    },
    isViews: {
      type: Boolean,
      default: false
    },
    source: {
      type: String as PropType<AIAgentLogSourceEnum>,
      default: AIAgentLogSourceEnum.INSIDE
    },
    doorId: {
      type: String,
      default: ''
    }
  },
  computed: {
    //
  },
  created() {
    // 
  },
  methods: {
    init() {
      (this.$refs.ChatAIView as ChatAIViewComponent)?.initializeFetch()
    },
    initWithChangeApp() {
      (this.$refs.ChatAIView as ChatAIViewComponent)?.initializeFetchWithChangeApp()
    },
    onAppTagHandler() {
      this.$emit(ChatAIIndexEventEnum.AppTag)
    },
    changeModelHandler(model: AiModelEnum) {
      (this.$refs.ChatAIView as ChatAIViewComponent)?.changeModelHandler(model)
    },
    onChangeModelHandler(model: AiModelEnum) {
      this.$emit(ChatAIIndexEventEnum.ChangeModel, model)
    },
    handlerChangeWelcomeMessage(welcomeMessage: string) {
      (this.$refs.ChatAIView as ChatAIViewComponent)?.handlerChangeWelcomeMessage(welcomeMessage)
    }
  },
  render() {
    return (
      <div class={[ComponentNameEnum.ChatAIIndex, 'bg-w']}>
        
        <ChatAIView  
          ref="ChatAIView" 
          isDisabled={this.disabled}
          isShare={this.isShare}
          tenantId={this.tenantId} 
          robotId={this.agentId}
          isSendMessageToServer={this.isSendMessageToServer}
          userId={this.userId}
          stream={this.stream}
          currentApp={this.currentApp}
          currentAgent={this.currentAgent as AIAgentType}
          disabledAgentEnabledValidate={this.disabledAgentEnabledValidate}
          chatSystemMessage={this.chatSystemMessage}
          onAppTag={this.onAppTagHandler}
          onChangeModel={this.onChangeModelHandler}
          mode={this.mode}
          isViews={this.isViews}
          source={this.source}
          doorId={this.doorId}
        />
        
      </div>
    ) as any
  }
})
