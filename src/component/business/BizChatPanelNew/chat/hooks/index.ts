/* api */
import { 
  getHistoryMessage,
  getChatConversation, 
  sendChatConversationContent, 
  deleteChatConversationContent, 
  getChatConversationShare, 
  sendChatConversationContentShare,
  getHistoryMessageShare,
  deleteChatConversationContentShare,
} from "@src/api/AIv2API"

/* api */
import {
  chatLikeMessage, 
  chatDisLikeMessage, 
  contentSecurityText,
  chatLikeMessageShare, 
  chatDisLikeMessageShare, 
  queryQuestionInStandardLibShare, 
  queryQuestionInStandardLib 
} from "@src/api/AIApi"

/* hooks */
import { useLoading } from "@hooks/useLoading"
/* vue */
import { Ref, ref, reactive, computed } from "vue"
/* type */
import { ChatIMMessageItemType } from "@src/component/business/BizChatPanelNew/chat/model"
/* model */
import Page from "@model/Page"
import { 
  DeleteChatConversationContentModel,
  GetChatContentSecurityModel,
  GetChatConversationModel,
  GetChatConversationShareModel,
  GetChatDisLikeMessageModel, 
  GetChatDisLikeMessageShareModel, 
  GetChatHistoryMessageModel, 
  GetChatLikeMessageModel, 
  GetChatLikeMessageShareModel, 
  QueryQuestionInStandardLibShareModel, 
  SendChatConversationContentModel, 
  SendChatConversationContentShareModel
} from "@model/param/in/AI"
import MsgModel from "@model/MsgModel"
/* util */
import Platform from '@src/util/platform'
/* types */
import { ChatConversationType } from "@src/component/business/BizChatPanelNew/chat/types"
import { SettingGPTQuestionItemType } from "@src/modules/setting/gpt/types"

/** 
 * @description 发送新消息
*/
function useChatFetchSendNewMessage(isShare: Ref<boolean>) {
  
  // 加载状态
  const { loading, showLoading, hideLoading } = useLoading()
  
  const conversationContentId = ref('')
  
  const fetchSendNewMessage = (params: SendChatConversationContentShareModel) => {
    
    showLoading()
    
    const fetchFunction = isShare.value ? sendChatConversationContentShare : sendChatConversationContent
    
    return (
      fetchFunction(params).then(result => {
        
        if (MsgModel.isSuccess(result)) {
          conversationContentId.value = result?.result || ''
        } else {
          Platform.alert(result?.message || '')
        }
        
        return conversationContentId.value
        
      }).finally(() => {
        hideLoading()
      })
    )
  }
  
  return {
    fetchSendNewMessage,
    conversationContentId,
    loading
  }
  
}

/** 
 * @description 删除会话消息
*/
function useChatFetchDeleteMessage(isShare: Ref<boolean>) {
  
  // 加载状态
  const { loading, showLoading, hideLoading } = useLoading()
  
  const conversationContentId = ref('')
  
  const fetchDeleteMessage = (params: DeleteChatConversationContentModel) => {
    
    showLoading()

    const fetchFunction = isShare.value ? deleteChatConversationContentShare : deleteChatConversationContent
    
    return (
      fetchFunction(params).then(result => {
        
        const isSuccess = MsgModel.isSuccess(result)
        
        if (isSuccess) {
          // 
        } else {
          Platform.alert(result?.message || '')
        }
        
        return isSuccess
        
      }).finally(() => {
        hideLoading()
      })
    )
  }
  
  return {
    fetchDeleteMessage,
    conversationContentId,
    loading
  }
  
}

/** 
 * @description 获取会话消息列表
*/
function useChatFetchHistoryMessage(isShare: Ref<boolean>) {
  
  // 加载状态
  const { loading, showLoading, hideLoading } = useLoading()
  // 会话消息列表
  const messageList: Ref<ChatIMMessageItemType[]> = ref([])
  const messagePage: Ref<Page> = ref(new Page())

  const fetchFunction = isShare.value ? getHistoryMessageShare : getHistoryMessage
  
  const fetchHistoryMessage = (params: GetChatHistoryMessageModel) => {
    
    showLoading()
    
    return (
      fetchFunction(params).then(result => {
        
        const list = result?.result?.list || []
        
        if (MsgModel.isSuccess(result)) {
          messageList.value = list
          messagePage.value = result?.result || new Page()
        } else {
          Platform.alert(result?.message || '')
        }
        
        return list
        
      }).finally(() => {
        hideLoading()
      })
    )
  }
  
  const setMessageList = (list: ChatIMMessageItemType[]) => {
    messageList.value = list
  }
  
  const setMessagePage = (page: Page) => {
    messagePage.value = page
  }
  
  return {
    fetchHistoryMessage,
    setMessageList,
    setMessagePage,
    loading,
    messagePage,
    messageList
  }
}

/** 
 * @description 聊天会话消息点赞
*/
function useChatFetchLikeMessage(isShare: Ref<boolean>) {
  
  // 加载状态
  const { loading, showLoading, hideLoading } = useLoading()
  
  const fetchLikeMessage = (params: GetChatLikeMessageShareModel) => {
    
    showLoading()
    
    const fetchFunction = isShare.value ? chatLikeMessageShare : chatLikeMessage
    
    return (
      fetchFunction(params).then(result => {
        
        if (MsgModel.isSuccess(result)) {
          // 
        } else {
          Platform.alert(result?.message || '')
        }
        
      }).finally(() => {
        hideLoading()
      })
    )
  }
  
  return {
    fetchLikeMessage,
    loading
  }
}

/** 
 * @description 聊天会话消息点踩
*/
function useChatFetchDisLikeMessage(isShare: Ref<boolean>) {
  
  // 加载状态
  const { loading, showLoading, hideLoading } = useLoading()
  
  const fetchDisLikeMessage = (params: GetChatDisLikeMessageShareModel) => {
    
    showLoading()
    
    const fetchFunction = isShare.value ? chatDisLikeMessageShare : chatDisLikeMessage
    
    return (
      fetchFunction(params).then(result => {
        
        if (MsgModel.isSuccess(result)) {
          // 
        } else {
          Platform.alert(result?.message || '')
        }
        
      }).finally(() => {
        hideLoading()
      })
    )
  }
  
  return {
    fetchDisLikeMessage,
    loading
  }
}

/** 
 * @description 检测内容安全
*/
function useChatFetchContentSecurityText() {
  
  const isPassContentSecurity: Ref<boolean> = ref(false)
  
  // 加载状态
  const { loading, showLoading, hideLoading } = useLoading()
  
  const fetchContentSecurityText = (params: GetChatContentSecurityModel) => {
    
    showLoading()
    
    return (
      contentSecurityText(params).then(result => {
        
        isPassContentSecurity.value = Boolean(result?.data)
        
        if (MsgModel.isSuccess(result)) {
          // 
        } else {
          Platform.alert(result?.message || '')
        }
        
        return isPassContentSecurity.value
        
      })
      .finally(() => {
        hideLoading()
      })
    )
  }
  
  return {
    fetchContentSecurityText,
    loading,
  }
}

/** 
 * @description 获取会话信息
*/
function useChatFetchConversation(isShare: Ref<boolean>) {
  
  // 加载状态
  const { loading, showLoading, hideLoading } = useLoading()
  // 会话信息
  const conversationInfo: Ref<ChatConversationType> = ref({} as ChatConversationType)
  
  const fetchChatConversation = (params: GetChatConversationShareModel) => {
    
    showLoading()
    
    const fetchFunction = isShare?.value ? getChatConversationShare : getChatConversation
    
    return (
      fetchFunction(params).then(result => {
        
        if (MsgModel.isSuccess(result)) {
          conversationInfo.value = result?.data || result?.result || {} as ChatConversationType
        } else {
          Platform.alert(result?.message || '')
        }
        
      }).finally(() => {
        hideLoading()
      })
    )
  }
  
  return {
    fetchChatConversation,
    loading,
    conversationInfo
  }
}

/** 
 * @description 查询问题是否在标准库中
*/
function useChatFetchQuestionInStandardLib(isShare: Ref<boolean>) {
  
  // 加载状态
  const { loading, showLoading, hideLoading } = useLoading()
  // 标准库问题
  const standardQuestion: Ref<SettingGPTQuestionItemType | null> = ref(null)
  
  const fetchQuestionInStandardLib = (params: QueryQuestionInStandardLibShareModel) => {
    
    showLoading()
    
    const fetchFunction = isShare.value ? queryQuestionInStandardLibShare : queryQuestionInStandardLib
    
    return (
      fetchFunction(params).then(result => {
        
        if (MsgModel.isSuccess(result)) {
          standardQuestion.value = MsgModel.getData<SettingGPTQuestionItemType>(result, {})
        } else {
          Platform.alert(result?.message || '')
        }
        
        return standardQuestion.value
        
      })
      .finally(() => {
        hideLoading()
      })
    )
  }
  
  return {
    fetchQuestionInStandardLib,
    loading,
    standardQuestion
  }
}

export const useStoreForAiChat:any = reactive({
  robotIsAnswering:false,
  chatViewWidth:25,
  firstQuestion:'',
})

export const useStateForAiChat = ()=>{
  const robotIsAnswering = computed(()=>useStoreForAiChat.robotIsAnswering)
  const changeRobotIsAnswering = (val:boolean)=>{
    useStoreForAiChat.robotIsAnswering = val
  }

  const chatViewWidth = computed(()=>useStoreForAiChat.chatViewWidth)
  const changeChatViewWidth = (val:number)=>{
    useStoreForAiChat.chatViewWidth = val
  }
  
  
  const firstQuestion = computed(()=>useStoreForAiChat.firstQuestion)
  const changeFirstQuestion = (val:any)=>{
    useStoreForAiChat.firstQuestion = val
  }
  return {
    robotIsAnswering,
    changeRobotIsAnswering,
    chatViewWidth,
    changeChatViewWidth,
    firstQuestion,
    changeFirstQuestion,
  }
}

export {
  useChatFetchSendNewMessage,
  useChatFetchHistoryMessage,
  useChatFetchLikeMessage,
  useChatFetchDisLikeMessage,
  useChatFetchContentSecurityText,
  useChatFetchConversation,
  useChatFetchDeleteMessage,
  useChatFetchQuestionInStandardLib
}