
.chat-ai-operate-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 10px;
  
  button {
    height: 38px !important;
  }
  
}

.chat-ai-operate-button-block-v2 {
  cursor: pointer;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  opacity: 1;
  /* 自动布局 */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0px;
  background-color: #E6E8EB;
  color: #fff;
}
.chat-ai-operate-button-v2{
  .is-answering{
    background-color: transparent !important;
    color: $color-primary;
    i{
      font-size: 28px !important;
    }
  }
  .share-page{
    color: #6F47FF !important;
  }
  .is-active{
    background-color: transparent !important;
    color: $color-primary;
    border: 1px solid $color-primary;
  }
}


.chat-ai-operate-button--disabled {
  .chat-ai-operate-button-block-v2 {
    cursor: not-allowed;
  }
}