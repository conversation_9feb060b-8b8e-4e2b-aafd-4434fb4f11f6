/* enum */
import { ComponentNameEnum } from '@src/component/business/BizChatPanelNew/chat/model/enum'
/* scss */
import '@src/component/business/BizChatPanelNew/chat/components/operate-button/index.scss'
/* vue */
import { defineComponent } from 'vue'

export type ChatAIOperateButtonProps = {
  disabled: boolean;
}

export interface ChatAIOperateButtonSetupState {
  
}

export enum ChatAIOperateButtonEventEnum {
  Input = 'input',
  Send = 'send'
}

export default defineComponent({
  name: ComponentNameEnum.ChatAIOperateButton,
  emits: [
    ChatAIOperateButtonEventEnum.Input,
    ChatAIOperateButtonEventEnum.Send
  ],
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    answering: {
      type: Boolean,
      default: false
    },
    messageContentIsNull: {
      type: Boolean,
      default: false
    },
  },
  setup(props: ChatAIOperateButtonProps, { slots, emit }) {
    
  },
  computed: {
    classNames(): Record<string, boolean> {
      return {
        'chat-ai-operate-button-v2': true,
        [`${ComponentNameEnum.ChatAIOperateButton}--disabled`]: this.answering ? false : this.disabled
      }
    },
    /** 
     *  @description 发送按钮文案
    */
    sendButtonText(): string {
      return this.disabled ? '发送' : '发送'
    },
    isInSharePage(){
      return this.$route.path == '/ai/agent/chat'
    },
  },
  methods: {
    onSendButtonClickHandler() {
      if (this.disabled) return
      this.$emit(ChatAIOperateButtonEventEnum.Send)
    },
    onStopButtonClickHandler(){
      this.$emit('stop')
    }
  },
  render() {
    return (
      <div class={this.classNames}>
        {
          this.answering ? <div 
          class={['chat-ai-operate-button-block-v2', 'is-answering', this.isInSharePage ? 'share-page' : '']}
          onClick={this.onStopButtonClickHandler}
        >
          <i class="iconfont icon-tingzhi font-28-i"></i>
        </div> : <div 
          class={['chat-ai-operate-button-block-v2', this.messageContentIsNull ? '' : 'is-active']}
          onClick={this.onSendButtonClickHandler}
        >
          <i class="iconfont icon-fasong"></i>
        </div> 
        }
        
        
      </div>
    ) as any
  }
})
