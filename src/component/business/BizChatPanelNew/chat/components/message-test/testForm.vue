<template>
    <el-form ref="formRef" :model="inputValues" :rules="rules()">
        <el-form-item prop="inputValue" >
            <el-input
            ref="inputRef"
            v-trim:blur
            :placeholder="item.name ? item.name : `变量${index+1}`" 
            @input=inputHandle($event,item,index)
            :value="inputValues.inputValue"
            ></el-input>
        </el-form-item>
    </el-form>
</template>

<script>
import { ref,onMounted, toRefs,onBeforeUnmount } from 'vue'
import EventBus from '@src/util/eventBus';
export default {
    name: 'testForm',
    props:{
        item: {
            type: Object,
            default: () => ({})
        },
        index: {
            type: Number,
            default: 0
        },
        varInputList: {
            type: Object,
            default: () => ({})
        }
    },
    setup(props, { emit }){
        const { item } = toRefs(props)
        const inputRef = ref(null)
        const inputValues = ref({
            inputValue:''
        })
        const formRef = ref(null)
        const validateKey = (rule, value, callback) => {
            if(props.item.required && value === ''){
                callback(new Error('请输入'))
            }else{
                callback()
            }
        }
        const validateChange = (rule, value, callback) => {
            if(props.item.key === ''){
                callback(new Error('变量Key字段不能为空'))
            }else{
                callback()
            }
        }
        const rules =()=>{
            return {
                
                inputValue: [
                            { required: props.item.required,
                              message: '请输入',
                               trigger: 'blur' 
                            },
                            {
                                trigger: 'blur',
                                validator: validateKey
                            },
                            {
                                trigger:['change','blur'],
                                validator: validateChange
                            }
                        ]
                    }
                }

        // 输入框事件
        const inputHandle=(e, item, index)=>{
            inputValues.value.inputValue = e
            emit('myinput', {[item.key]: e}, index)
        }
        
        // 校验函数
            function validateForm() {
            if(!props.item.required && props.item.key === ''){
                return false
            }
            if(!props.item.required){
                return true
            }
            return new Promise((resolve) => {
                    formRef.value.validate((valid) => {
                        resolve(valid)
                    })
            })
        }
        onMounted(() => {
            // if(inputRef.value){
            //     inputRef.value.focus()
            //     validateForm()
            // }
        })

        onBeforeUnmount(() => {
            // delete props.varInputList[props.item.key]
            // emit('deletInput', props.index)
            // EventBus.$emit('sendDataToParent', props.varInputList)
        })


            return {
                inputValues,
                inputHandle,
                rules,
                formRef,
                validateForm,
                inputRef
            }
    }

}

</script>

<style scoped>
.el-form-item--small.el-form-item{
   margin-bottom: 0 !important;
}
.el-form-item {
  margin-bottom: 0 !important;
}
</style>