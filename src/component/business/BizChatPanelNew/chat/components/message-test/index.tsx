import { watch,computed, defineComponent, getCurrentInstance, PropType,ref } from "vue";
import { ComponentNameEnum } from '@src/component/business/BizChatPanelNew/chat/model/enum'
import './index.scss'
import EventBus from '@src/util/eventBus';
import testForm from './testForm.vue'
import { AIAgentAppComponentType } from '@src/modules/ai/types'
export default defineComponent({
    name: ComponentNameEnum.ChatAIMessageTest,
    components:{
        testForm
    },
    props: {
        currentApp: {
            type: Object as PropType<AIAgentAppComponentType>,
            default: () => ({})
        },
        isViews: {
            type: Boolean,
            default: false
        },
        varDataListValue: {
            type: Object as PropType<Record<string, any>>,
            default: () => ({})
        }
    },

    setup(props,{emit}){
      const varDataList = computed(() => {
        return props.currentApp.variable || []
      })
      const varInputList = ref({})
      const validList = ref([])
      const isCollege = ref(true)
      const instance= getCurrentInstance()?.proxy || {}
      const inputHandle =async (val: any, index:number) => {
            validataForm(index)
            const obj = Object.assign(varInputList.value, val)
            EventBus.$emit('sendDataToParent',obj)
      }

      const deleteInput = (index:number) => {
        // 删除后，需要将对应的校验进行删除和发事件总线
        validList.value.splice(index, 1);
        EventBus.$emit('sendValid',validList.value)
      }

      watch(()=>varDataList.value,(newvalue)=>{
        newvalue.forEach((item,index)=>{
            validataForm(index)
        })
        
      },{
        deep:true
      })

      // 校验集合
      const validataForm = async (index: number) => {
        const formRef = instance.$refs[`formRef_${index}`]
        const allLabels = props.currentApp.variable.map(item => item.key);
        const hasDuplicates = allLabels.some((value,index) => {
            return allLabels.indexOf(value) !== index
        })
        const valid = await formRef.validateForm() || false
        // 确保 validList 数组长度足够
        while (validList.value.length <= index) {
            validList.value.push(false);
        }
        validList.value[index] = valid
        if(hasDuplicates){
            validList.value[index] = false
        }
        EventBus.$emit('sendValid',validList.value)
      }

      const toggleIsCollege  = () => {
        isCollege.value = !isCollege.value;
      }
      
      return {
        varDataList,
        inputHandle,
        validataForm,
        varInputList,
        isCollege,
        toggleIsCollege,
        deleteInput,
      }
    },
    methods: {
        initVarDataListEvent() {

            console.log(this.varDataListValue)
    
            const variableValueMap = this.varDataListValue?.variableValueMap || {}
            if (Object.keys(variableValueMap).length > 0) {
    
              const variable = this.varDataListValue?.variable || []
    
              variable.forEach((item, index) => {
                 
                const ref = this.$refs[`formRef_${index}`]
                const key = item.key
                const value = variableValueMap[key]
    
                if (ref && value) {
                  ref.inputHandle(value, item, index)
                }
    
              })
    
              this.varInputList = {
                ...variableValueMap
              }
    
            } else {
              EventBus.$emit('sendDataToParent', this.varInputList)
            }
    
        }
    },
    mounted() {
        this.initVarDataListEvent()
    },
    render(){
        return(
      
            <div class={ComponentNameEnum.ChatAIMessageTest} >
                <div class="test-box">
                <div class="chat-header" onClick={this.toggleIsCollege }>
                    <span class="chat-header-text">聊天设置</span>
                    <div><i class={`${this.isCollege ? "iconfont icon-down": "iconfont icon-right1"}`}></i></div>
                </div>
                <div class='chat-body' v-show={this.isCollege}>
                   {
                    this.varDataList.map((item,index) => {
                        return (
                            <div class="chat-ai-message-test-var" >
                                <div>
                                    <div class="test-header">
                                        <span v-show={item.required} style={{color:'red'}}>*</span>
                                        <div>{item.name ? item.name : `变量${index+1}`}</div>
                                    </div>
                                    <div class="test-body">
                                        <test-form item={item} 
                                        index={index} 
                                        onMyinput={this.inputHandle} 
                                        ref={`formRef_${index}`}
                                        varInputList={this.varInputList}
                                        ondeletInput={this.deleteInput}
                                        ></test-form>
                                    </div>
                                </div>
                                
                            </div>
                        )
                    })
                } 
                </div>
                <span v-show={!this.isViews} class="test-text">聊天开始后，聊天设置无法修改。</span>
                </div>
            </div>
        )
        
    }
})