/* enum */
import { ComponentNameEnum } from '@src/component/business/BizChatPanelNew/chat/model/enum'
/* scss */
import '@src/component/business/BizChatPanelNew/chat/components/app-tag/index.scss'
/* vue */
import { defineComponent } from 'vue'

export type ChatAIAppTagProps = {
  
}

export interface ChatAIAppTagSetupState {
  
}

export enum ChatAIAppTagEventEnum {
  Input = 'input',
  Click = 'click'
}

export default defineComponent({
  name: ComponentNameEnum.ChatAIAppTag,
  emits: [
    ChatAIAppTagEventEnum.Input,
    ChatAIAppTagEventEnum.Click,
  ],
  props: {
    value: {
      type: String,
      default: ''
    },
    onClick: {
      type: Function
    }
  },
  setup(props: ChatAIAppTagProps, { slots, emit }) {
    
  },
  computed: {
    
  },
  methods: {
    onInputHandler(value: string) {
      this.$emit(ChatAIAppTagEventEnum.Input, value)
    },
    onClickHandler() {
      this.$emit(ChatAIAppTagEventEnum.Click)
    },
    renderValue() {
      return (
        <span>
          {this.value}
        </span>
      )
    },
    renderContent() {
      return this.$slots.default
    },
    renderContentWrapper() {
      if (this.value) {
        return this.renderValue()
      }
      return this.renderContent()
    }
  },
  render() {
    return (
      <div 
        class="chat-ai-app-tag-v2"
        onClick={this.onClickHandler}
      >
        {this.renderContentWrapper()}
        <span>
          <i class="iconfont icon-down"></i>
        </span>
      </div>
    ) as any
  }
})
