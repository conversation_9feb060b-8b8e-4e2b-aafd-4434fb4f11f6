
.chat-ai-text-input-v2 {
  
  display: flex;
  
  .el-textarea {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .el-textarea__inner {
    line-height: 2;
    min-height: 120px !important;
    border-radius: 8px;
  }
  
  .el-textarea__inner:focus {
    box-shadow: 0 0 0 1px $color-primary-light-6 inset;;
  }
  
  .van-field {
    display: inline-block;
    width: 100%;
    
    font-size: 14px;
    
    padding: 6px 10px;
    margin: 8px 0px;
    
    position: relative;
    top: 0;
    
    background-color: #fff;
    border-radius: 6px;
    
    flex: 1;
    max-height: 100px;
    min-height: 30px;
    
    overflow-y: auto;
    
    display: flex;
    align-items: center;
  }
  
}