/* enum */
import { ComponentNameEnum } from '@src/component/business/BizChatPanelNew/chat/model/enum'
import KeyCodeEnum from '@model/enum/KeyCodeEnum'
/* scss */
import '@src/component/business/BizChatPanelNew/chat/components/text-input/index.scss'
/* vue */
import { defineComponent } from 'vue'

export type ChatAITextInputProps = {
  value: string;
  disabled: boolean;
}

export interface ChatAITextInputSetupState {
  
}

export enum ChatAITextInputEventEnum {
  Input = 'input',
  Focus = 'focus',
  Enter = 'enter'
}

export default defineComponent({
  name: ComponentNameEnum.ChatAITextInput,
  emits: [
    ChatAITextInputEventEnum.Input,
    ChatAITextInputEventEnum.Focus,
    ChatAITextInputEventEnum.Enter
  ],
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    value: {
      type: String,
      default: ''
    }
  },
  setup(props: ChatAITextInputProps, { slots, emit }) {
    
  },
  computed: {
    
  },
  methods: {
    enter() {
      this.$emit(ChatAITextInputEventEnum.Enter, this.value)
    },
    onInputHandler(value: string) {
      this.$emit(ChatAITextInputEventEnum.Input, value)
    },
    onFocusHandler() {
      this.$emit(ChatAITextInputEventEnum.Focus)
    }
  },
  render() {
    return (
      <div class='chat-ai-text-input-v2'>
        
        <el-input
          disabled={this.disabled}
          type="textarea"
          confirm-type="done"
          value={this.value}
          onInput={this.onInputHandler}
          onFocus={this.onFocusHandler}
          confirm-hold
          minRows={4}
          autosize
          show-confirm-bar={false}
          maxlength="600"
          placeholder="请输入问题"
          nativeOnKeyup={(event: KeyboardEvent) => {
            
            const isHaveSpecialKey = event.altKey || event.ctrlKey || event.shiftKey
            
            // 如果有特殊按键，不处理
            if (isHaveSpecialKey) {
              return
            }
            
            // 回车搜索 (暂时先这么蠢)
            if (event.keyCode === KeyCodeEnum.Enter) {
              event.preventDefault()
              this.enter()
            }
            
          }}
        />
        
      </div>
    ) as any
  }
})
