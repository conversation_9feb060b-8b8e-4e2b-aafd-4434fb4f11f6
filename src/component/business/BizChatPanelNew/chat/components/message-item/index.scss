

.chat-ai-message-item {
  width: 100%;
  max-width: 100%;
  height: auto;
  padding: 0 24px;
  position: relative;
  margin-bottom: 15px;
  display: flex;
  flex-direction: row;
}

.chat-ai-message-list-item-bottom {
  display: flex;
  flex: 1;
  flex-wrap: wrap;
  width: 100%;
}

.chat-ai-message-list-item-name {
  width: 100%;
  font-size: 12px;
  color: #bfbfbf;
  padding: 4px 0 4px 0;
}

.chat-ai-message-list-item-message-block-v2 {
  padding: 16px;
  max-width: calc(90% - 40px);
  min-height: 20px;
  min-width: 110px;
  font-size: 14px;
  text-align: left;
  word-break: break-all;
  position: relative;
  background-color: #fff;
  overflow-x: auto;
  
  .el-tag {
    cursor: pointer;
    background-color: #ECF5FF;
  }
  
}
.chat-ai-message-list-item-left{
  .chat-ai-message-list-item-message-block-v2{
    max-width: 100% !important;
  }
}

.chat-ai-message-list-item-message-session-business-source-documents-block {
  position: relative;
  
  .chat-ai-message-list-item-message-session-business-source-documents-title {
    position: absolute;
    top: 4px;
  }
  
  .el-tag {
    margin-bottom: 6px;
    margin-right: 6px;
    max-width: 335px;
    @include text-ellipsis();
  }
  
  span:nth-of-type(2) {
    margin-left: 60px;
    max-width: 200px;
  }
  
}

.chat-ai-message-list-item-message-session-business-source-documents-title {
  font-size: 12px;
  color: #8C8C8C;
}

.chat-ai-message-list-item-message-session-tip-block {
  cursor: pointer;
  font-size: 12px;
  color: #8C8C8C;
  margin-top: 8px;
}

.chat-ai-message-list-item-message-time {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  font-size: 12px;
  color: #bfbfbf;
  margin-top: 4px;
}

.chat-ai-message-list-item-operator {
  
  margin-top: 6px;
  width: 90%;
  max-width: calc(90% - 40px);
  display: flex;
  justify-content: space-between;
  
  i {
    cursor: pointer;
    color: #8C8C8C;
    display: inline-block;
    position: relative;
    width: 28px;
  }
  
  i {
    &::after {
      content: "";
      display: inline-block;
      width: 1px;
      height: 12px;
      background-color: #bfbfbf;
      position: absolute;
      top: 5px;
      right: 5px;
    }
  }
  
  i:last-child {
    &::after {
      display: none;
    }
  }
  
}

.chat-ai-message-list-item-operator-other {
  display: flex;
}

.chat-ai-message-list-item-operator-left {
  display: flex;
  .chat-ai-message-list-item-operator-item-v2 + .chat-ai-message-list-item-operator-item-v2 {
    margin-left: 10px;
  }
}

.chat-ai-message-list-item-operator-item-v2 {
  cursor: pointer;
  padding: 2px 4px;
  i {
    width: 20px !important;
    &::after {
      display: none;
    }
  }
  span {
    font-size: 13px;
  }
  &:hover {
    // color: $color-primary;
    // i {
    //   color: $color-primary;
    // }
    border-radius: 4px;
    /* light/fill/--el-fill-color */
    background: #F0F2F5;
  }
}

.chat-ai-message-list-item-message-content {
  th {
    min-width: 100px;
  }
  .markdown-body {
    img {
      width: 100%;
    }
    ol {
      padding-left: 16px;
      list-style-type: decimal !important;
    }
    ul {
      padding-left: 16px;
      list-style-type: decimal !important;
    }
    li::marker {
      color: #262626;
      font-weight: bold;
      content: "· "; /* 自定义标记内容 */
    }
    li li {
      &::marker {
        font-weight: bold;
        content: "· "; /* 自定义标记内容 */
      }
    }
    ol > li::marker {
      content: counter(list-item) ". ";
    }
    think {
      color: #8c8c8c;
      border-left: 4px solid #ddd;
      padding-left: 10px;
      height: 100%;
      display: block;
      margin-bottom: 12px;
      p:nth-of-type(1) {
        margin-top: 12px;
      }
    }
  }
}

.chat-ai-message-list-item-like-status {
  .chat-ai-message-list-item-operator-like-icon {
    color: $color-primary;
  }
  .chat-ai-message-list-item-operator-dislike-icon {
    color: #8C8C8C;
  }
}

.chat-ai-message-list-item-dislike-status {
  .chat-ai-message-list-item-operator-like-icon {
    color: #8C8C8C;
  }
  .chat-ai-message-list-item-operator-dislike-icon {
    color: $color-danger;
  }
}

.chat-ai-message-list-item-stop-status {
  .chat-ai-message-list-item-message-content {
    color: #8C8C8C;
  }
}

/* start left */
.chat-ai-message-list-item-left  {
  
  .chat-ai-message-list-item-message-content {
    border-radius: 4px;
    line-height: 24px;
  }
  
  .chat-ai-message-list-item-message-block-v2 {
    // border-radius: 0px 8px 8px 8px;
    // border: 0.5px solid rgba(25, 31, 37, 0.12);
  }
  
  .chat-ai-message-list-item-message-time {
    justify-content: flex-start;
  }
  
  .chat-ai-message-list-item-message-session {
    width: 100%;
  }
  
  .chat-ai-message-list-item-message-block-v2 {
    position: relative;
    .ai-textToReports__fullScreen {
      position: absolute;
    }
  }
  
}
/* end left */

/* start right */
.chat-ai-message-list-item-right {
  
  flex-direction: row-reverse;
  
  .chat-ai-message-list-item-message-session {
    display: flex;
    justify-content: flex-end;
    width: 100%;
  }
  
  .chat-ai-message-list-item-bottom {
    flex-direction: row-reverse;
  }
  
  .chat-ai-message-list-item-message-block-v2 {
    border-radius: 8px 0px 8px 8px;
    // border: 0.5px solid rgba(0, 209, 178, 0.32);
    min-width: 0;
    background-color: #F5F8FA;
  }
  
  .chat-ai-message-list-item-name {
    display: flex;
    justify-content: flex-end;
  }
  
  .chat-ai-message-avatar {
    margin: 0;
    margin-left: 10px;
  }
  
}
/* end right */

.chat-ai-message-list-item-questions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 8px;
}

.chat-ai-message-list-item-question-item {
  cursor: pointer;
  background: #fff;
  padding: 12px 16px;
  width: fit-content;
  line-height: 22px;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 4px 12px;
  gap: 8px;
  border-radius: 12px;
  background: #F0F2F5;

  &:hover {
    background: #f0f2f5;
  }

  i {
    color: #86909c;
    font-size: 12px;
  }
}