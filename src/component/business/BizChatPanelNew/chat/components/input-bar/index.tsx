/* components */
import ChatAITextInput from '@src/component/business/BizChatPanelNew/chat/components/text-input'
import ChatAIOperateButton from '@src/component/business/BizChatPanelNew/chat/components/operate-button'
/* enum */
import { ComponentNameEnum } from '@src/component/business/BizChatPanelNew/chat/model/enum'
/* scss */
import '@src/component/business/BizChatPanelNew/chat/components/input-bar/index.scss'
/* vue */
import { defineComponent } from 'vue'

export type ChatAIInputBarProps = {
  value: string;
  disabled: boolean;
}

export interface ChatAIInputBarSetupState {
  
}

export enum ChatAIInputBarEventEnum {
  Input = 'input',
  Send = 'send',
  Focus = 'focus'
}

export default defineComponent({
  name: ComponentNameEnum.ChatAIInputBar,
  components: {
    [ChatAITextInput.name]: ChatAITextInput,
    [ChatAIOperateButton.name]: ChatAIOperateButton,
  },
  emits: [
    ChatAIInputBarEventEnum.Input,
    ChatAIInputBarEventEnum.Send,
    ChatAIInputBarEventEnum.Focus
  ],
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    answering: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      messageContent: ''
    }
  },
  computed: {
    classNames(): Record<string, boolean> {
      return {
        'chat-ai-input-bar-v2': true,
        [`${ComponentNameEnum.ChatAIInputBar}--disabled`]: this.disabled
      }
    },
    messageContentIsNull(){
      return this.messageContent.trim() == ''
    },
  },
  methods: {
    /** 
     * @description 清空消息内容
    */
    clearMessageContent() {
      setTimeout(() => {
        this.clearMessageContentImpl()
      }, 200)
    },
    /** 
     * @description 清空消息内容实现
    */
    clearMessageContentImpl() {
      this.messageContent = ''
    },
    /** 
     * @description 输入框输入事件
    */
    onInputHandler(value: string) {
      this.messageContent = value
    },
    /** 
     * @description 输入框聚焦事件
    */
    onFocusHandler() {
      this.$emit(ChatAIInputBarEventEnum.Focus)
    },
    /** 
     * @description 发送按钮点击事件
    */
    onSendButtonClickHandler() {
      if(this.messageContentIsNull) return
      this.$emit(ChatAIInputBarEventEnum.Send, this.messageContent)
    },
    onStopButtonClickHandler(){
      this.$emit('stop')
    },
    /** 
     * @description 渲染文本输入框
    */
    renderTextInput() {
      return (
        <div class="chat-ai-input-bar-text-input">
          {/* start 消息内容输入框 */}
          <chat-ai-text-input
            disabled={this.disabled}
            value={this.messageContent}
            onInput={this.onInputHandler}
            onFocus={this.onFocusHandler}
            onEnter={this.onSendButtonClickHandler}
          />
          {/* end 消息内容输入框 */}
          
          {/* start 消息操作按钮 */}
          <chat-ai-operate-button
            disabled={this.disabled}
            messageContentIsNull={this.messageContentIsNull}
            onSend={this.onSendButtonClickHandler}
            answering={this.answering}
            onStop={this.onStopButtonClickHandler}
          >
          </chat-ai-operate-button>
          {/*  消息操作按钮 */}
        </div>
      )
    },
    /** 
     * @description 渲染文本输入框视图
    */
    renderTextInputView() {
      return (
        <div class="chat-ai-input-bar-view">
          {this.renderTextInput()}
        </div>
      )
    }
  },
  render() {
    return (
      <div class='chat-ai-input-bar-v2'>
        
        {/* start 输入框 */}
        {this.renderTextInputView()}
        {/* end 输入框 */}
        {this.$slots.default}
        
      </div>
    ) as any
  }
})
