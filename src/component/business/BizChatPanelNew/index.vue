
<template>
  <div class="biz-chat" v-if="showIcon">
    
    <biz-float-icon
      :x="x"
      :y="y"
      :topSafeArea="topSafeArea"
      :leftSafeArea="leftSafeArea"
      @updateX="updateX"
      @updateY="updateY"
      @dragend="onDragend"
      @click="openDrawerWrapper"
    >
      <div
        class="biz-chat-btn"
        type="primary"
      >
        <div class="biz-chat-btn-img-block">
          <img :src="xiaoBaoIcon" alt="小宝AI">
        </div>
      </div>
    </biz-float-icon>
    
    <el-drawer
      :modal="false"
      :wrapperClosable="true"
      custom-class="biz-chat-recommend-drawer"
      append-to-body
      :size="`${this.chatViewWidth}%`"
      direction="rtl"
      ref="ElDrawer"
      :visible="visible"
      @close="closeDrawer"
      @open="onOpenHandler"
    > 
      
      <div class="biz-chat-panel-header-content">
        <header-app
          v-if="isShowAppService"
          @close="closeElementDrawer"
          @back="onBackHandler"
        />
        <biz-chat-panel-header
          v-else
          @close="closeElementDrawer"
          :currentAgent="currentAgent"
          :currentApp="currentApp"
        />
      </div>
      
      <div class="biz-chat-panel-content">
        
        <app
          ref="AppServiceView"
          v-show="isShowAppService"
          :currentAgent="currentAgent"
          :currentApp="currentApp"
          :list="agentList"
          @cancel="onCancelHandler"
          @confirm="onConfirmHandler"
        />
        
        <chat
          v-if="isFinishInit"
          v-show="!isShowAppService"
          ref="ChatView" 
          :stream="stream"
          :tenantId="tenantId" 
          :agentId="agentId"
          :currentAgent="currentAgent"
          :currentApp="currentApp"
          @appTag="onAppTagClickHandler"
          @changeModel="onChangeModelHandler"
        />
        
      </div>
      
    </el-drawer>
    
  </div>
</template>

<script>
import { provide, ref, defineComponent } from 'vue'
/* component */
import chat from '@src/component/business/BizChatPanelNew/chat'
import BizChatPanelHeader from '@src/component/business/BizChatPanelNew/header'
import headerApp from '@src/component/business/BizChatPanelNew/header-app'
import app from '@src/component/business/BizChatPanelNew/app'
import BizFloatIcon from '@src/component/business/BizFloatIcon'
/* model */
import StorageKeyEnum from '@model/enum/StorageKeyEnum'
/* util */
import { getLocalesOssUrl } from '@src/util/assets'
import { isFalsy } from 'pub-bbx-utils'
import _ from 'lodash'
import { getUserId } from '@src/util/storage.ts'
/* api */
import { getAIAgentDetail, getAIAgentAppDetail, getHomeAIAgentList } from '@src/api/AIv2API'
/* model */
import MsgModel from '@model/MsgModel'
/* enum */
import { AIAgentTemplateEnum } from '@src/modules/ai/model/enum'

const xiaoBaoIcon = getLocalesOssUrl('xiao-bao-animation.gif')
const userId = getUserId()
import {
  useStateForAiChat,
} from '@src/component/business/BizChatPanelNew/chat/hooks'

export default defineComponent({
  name: 'biz-chat-panel-new',
  components: {
    chat,
    BizFloatIcon,
    headerApp,
    app,
    [BizChatPanelHeader.name]: BizChatPanelHeader
  },
  props: {
    tenantId: {
      type: String,
      default: ''
    },
    systemAIAgent: {
      type: Object,
      default: () => {}
    },
    navCollapse: {
      type: Boolean,
      default: false
    }
  },
  setup(){
    return {
      ...useStateForAiChat()
    }
  },
  data() {
    return {
      visible: false,
      xiaoBaoIcon,
      x: 0,
      y: 0,
      xDifference: 0,
      yDifference: 0,
      needInitChat: false,
      isShowAppService: false,
      currentAgent: null,
      currentApp: null,
      isFinishInit: false,
      agentList: [],
      model: localStorage.getItem(`${userId}-${StorageKeyEnum.ChatModel}`) || 0,
      outsideAgentId: null,
      showIcon: false,
    }
  },
  computed: {
    leftSafeArea() {
      return this.navCollapse ? 50 : 185
    },
    topSafeArea() {
      return 50
    },
    agentId() {
      return this.currentAgent?.id || String(this.systemAIAgent?.id)
    },
    stream() {
      return true
    },
    chatViewIndexComponent() {
      return this.$refs.ChatView || {}
    },
    chatViewViewComponent() {
      return this.chatViewIndexComponent?.$refs?.ChatAIView || {}
    },
    chatAIMessageListElement() {
      return this.chatViewViewComponent?.chatAIMessageListElement || {}
    }
  },
  watch: {
    visible(value) {
      if (isFalsy(value)) {
        this.saveChatAIMessageListElementScroll()
      } else {
        this.initChatAIMessageListElementScroll()
      }
    }
  },
  mounted() {

    this.initFloatIconPosition()

    this.initRobotAgent().then(() => {

      this.isFinishInit = true

      this.$nextTick(() => {
        this.initModel()
      })

    })

    this.addResizeListener()

  },
  beforeMount() {
    this.removeResizeListener()
  },
  methods: {
    initModel() {
      const userId = getUserId()
      const model = localStorage.getItem(`${userId}-${StorageKeyEnum.ChatModel}`)
      if (model) {
        this.$refs.ChatView?.changeModelHandler?.(Number(model))
      } else {
        this.$refs.ChatView?.changeModelHandler?.(this.systemAIAgent?.model)
      }
    },
    onChangeModelHandler(model) {
      const userId = getUserId()
      localStorage.setItem(`${userId}-${StorageKeyEnum.ChatModel}`, model)
      this.model = model
    },
    addResizeListener() {
      window.addEventListener('resize', this.initFloatIconPositionBaseDebounce)
    },
    removeResizeListener() {
      window.removeEventListener('resize', this.initFloatIconPositionBaseDebounce)
    },
    async outsideInit(agentId) {
      
      await this.initRobotAgent(agentId)
      
      this.init()
      this.initChat()

    },
    async outsideOpen(agentId) {
      this.outsideAgentId = agentId
      this.openDrawer()
      this.outsideInit(agentId)
    },
    async fetchAIAgent(agentId) {
      try {

        const aiAgentId = agentId || this.agentId
        
        const params = {
          id: aiAgentId,
          verifyPermissions: true
        }
        const res = await getAIAgentDetail(params)
        
        const isFail = MsgModel.isFail(res)
        if (isFail) {
          const message = MsgModel.getMessage(res)
          this.$message.error(message)
          return
        }
        
        // 获取 Agent 详情
        const agent = MsgModel.getData(res, {})
        const agentList = agent?.unionAiAgent || []
        // 更新应用列表
        this.agentList = [
          agent,
          ...agentList
        ].filter(item => {
          return item.template != AIAgentTemplateEnum.TaskFormChat && item.template != AIAgentTemplateEnum.TaskForm
        })
        this.currentAgent = agent
        this.currentApp = agent?.aiAgentAppDetail || {}
        
      } catch (error) {
        console.error(error)
      }
    },
    async fetchAgentList() {
       try {
        
        const res = await getHomeAIAgentList()
        
        const isFail = MsgModel.isFail(res)
        if (isFail) {
          const message = MsgModel.getMessage(res)
          this.$message.error(message)
          return
        }
        
        // 获取 Agent 详情
        const agentList = MsgModel.getData(res, [])
        const agent = agentList[0] || {}
        
        this.agentList = agentList
        this.currentAgent = agent
        
        if (agentList.length == 0) {
          this.showIcon = false
          return
        }
        
        // 获取 Agent 详情
        const agentTemplate = agent?.template
        const isSystemAgent = agentTemplate == AIAgentTemplateEnum.System
        if (isSystemAgent) {
          const agentId = agent.id
          await this.fetchAgentDetail(agentId)
        }
        // 获取 Agent App 详情
        await this.fetchAIAgentWithUnionAiAgent()

        this.showIcon = true
        
      } catch (error) {
        console.error(error)
      }
    },
    async fetchAgentDetail(agentId) {
      try {
        
        const params = {
          id: agentId,
          verifyPermissions: true
        }
        const res = await getAIAgentDetail(params)
        
        const isFail = MsgModel.isFail(res)
        if (isFail) {
          const message = MsgModel.getMessage(res)
          this.$message.error(message)
          return
        }
        
        // 获取 Agent 详情
        const agent = MsgModel.getData(res, {})
        const agentList = agent?.unionAiAgent || []
        // 更新应用列表
        this.agentList = [
          agent,
          ...agentList
        ].filter(item => {
          return item.template != AIAgentTemplateEnum.TaskFormChat && item.template != AIAgentTemplateEnum.TaskForm
        })

      } catch (error) {
        console.error(error)
      }
    },
    async initRobotAgent(agentId) {
      if (agentId) {
        await this.fetchAIAgent(agentId)
      } else {
        await this.fetchAgentList()
      }
    },
    init() {
      this.$refs.ChatView.init()
    },
    initWithChangeApp() {
      this.$refs.ChatView.initWithChangeApp()
    },
    initFloatIconPosition() {
      
      const x = localStorage.getItem(StorageKeyEnum.CHAT_PANEL_X)
      const y = localStorage.getItem(StorageKeyEnum.CHAT_PANEL_Y)
      const clientWidth = document.documentElement.clientWidth
      const clientHeight = document.documentElement.clientHeight
      
      if (x && y) {
        this.x = Number(x)
        this.y = Number(y)
        this.xDifference = clientWidth - this.x
        this.yDifference = clientHeight - this.y
        return
      }
      
      
      this.x = clientWidth - 130
      this.y = clientHeight - 130
      this.xDifference = clientWidth - this.x
      this.yDifference = clientHeight - this.y
      
    },
    initFloatIconPositionBase() {
      
      const clientWidth = document.documentElement.clientWidth
      const clientHeight = document.documentElement.clientHeight
      
      const x = clientWidth - this.xDifference
      const y = clientHeight - this.yDifference
      
      this.x = x
      this.y = y
      
    },
    initFloatIconPositionBaseDebounce: _.debounce(function() {
      this.initFloatIconPositionBase()
    }, 500),
    initChat() {
      
    },
    closeDrawer() {
      this.visible = false
    },
    openDrawerWrapper() {

      console.log('openDrawerWrapper')

      this.openDrawer()

      const isOutsideAgentId = this.outsideAgentId
      const systemAIAgentId = this.systemAIAgent?.id
      if (isOutsideAgentId && systemAIAgentId && isOutsideAgentId != systemAIAgentId) {
        this.outsideAgentId = null
        this.outsideInit(systemAIAgentId)
      }
      
    },
    openDrawer() {      
      this.visible = true
      this.$nextTick(() => {
        this.initModel()
      })
    },
    closeElementDrawer() {
      this.$refs.ElDrawer.closeDrawer()
    },
    initChatAIMessageListElementScroll() {
      this.$nextTick(() => {
        const ChatView = this.$refs.ChatView
        const ChatAIView = ChatView?.$refs?.ChatAIView
        const ChatAIMessageListElement = ChatAIView?.$refs?.ChatAIMessageListElement
        if (ChatAIMessageListElement) {
          ChatAIMessageListElement.scrollTop = ChatAIMessageListElement.scrollHeight
        }
      })
    },
    saveChatAIMessageListElementScroll() {
      
    },
    updateX(value) {
      const clientWidth = document.documentElement.clientWidth
      this.x = value
      this.xDifference = clientWidth - this.x
    },
    updateY(value) {
      const clientHeight = document.documentElement.clientHeight
      this.y = value
      this.yDifference = clientHeight - this.y
    },
    /** 
     * @description 拖动结束
     * 1. 保存当前元素的 X 坐标
     * 2. 保存当前元素的 Y 坐标
    */
    onDragend() {
      // 如果需要将坐标保存到 localStorage 中，那么就打开下面的注释
      // localStorage.setItem(StorageKeyEnum.CHAT_PANEL_X, this.x)
      // localStorage.setItem(StorageKeyEnum.CHAT_PANEL_Y, this.y)
    },
    onOpenHandler() {
      
      if (this.needInitChat) {
        this.initChat()
        this.needInitChat = false
      }
      
    },
    onAppTagClickHandler() {
      this.isShowAppService = true
    },
    onBackHandler() {
      this.isShowAppService = false
    },
    onCancelHandler() {
      this.onBackHandler()
      this.$refs.AppServiceView.resetValue()
    },
    async fetchAIAgentWithUnionAiAgent() {
      try {
        
        const params = {
          id: this.currentAgent?.id,
          verifyPermissions: true
        }
        const res = await getAIAgentDetail(params)
        
        const isFail = MsgModel.isFail(res)
        if (isFail) {
          const message = MsgModel.getMessage(res)
          this.$message.error(message)
          return
        }
        
        // 获取 Agent 详情
        const agent = MsgModel.getData(res, {})
        const app = agent?.aiAgentAppDetail || {}
        this.currentApp = app
        
      } catch (error) {
        console.error(error)
      }
    },
    async onConfirmHandler(id) {
      
      this.onBackHandler()
      
      this.currentAgent = this.agentList.find(item => {
        return item.id == id
      })

      await this.fetchAIAgentWithUnionAiAgent()
      
      this.initWithChangeApp()
      this.initChat()
      this.changeFirstQuestion('')
      
    }
  }
})
</script>

<style lang="scss">

.biz-chat {
  position: fixed;
  right: 24px;
  bottom: 32px;
}

.biz-chat-btn {
  cursor: pointer;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  padding: 0 !important;
  background-color: none;
  
  i {
    font-size: 30px !important;
  }
  
}

.biz-chat-btn-img-block {
  height: 60px;
  width: 60px;
  border-radius: 50%;
  overflow: hidden;
  img {
    width: 100%;
    height: 100%;
  }
}

.biz-chat-recommend-drawer {
  min-width:460px;
  .el-drawer {
    box-shadow: 0 8px 8px -5px rgba(0,0,0,.2), 0 8px 8px 2px rgba(0,0,0,.04), 0 4px 8px 5px rgba(0,0,0,.01); 
  }
  .biz-chat-panel-content {
    background-color: #fff;
  }
  .el-drawer__header {
    display: none;
  }
  .el-drawer__body {
    display: flex;
    flex-direction: column;
  }
  .biz-chat-panel-content {
    flex: 1;
    overflow: hidden;
  }
}
</style>