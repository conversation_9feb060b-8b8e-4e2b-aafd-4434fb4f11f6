
.chat-ai-app-service-radio {
  padding: 16px;
  overflow-y: auto;
  .el-radio {
    border: none !important;
    border-radius: 8px;
    background-color: #fff;
    height: 74px;
    width: 100%;
    display: flex;
    align-items: center;
    padding: 16px !important;
    margin-left: 0 !important;
    margin-top: 12px !important;
  }
  .el-radio-group {
    width: 100%;
  }
  .el-radio__inner {
    width: 14px !important;
    height: 14px !important;
  }
  .el-radio {
    &:nth-of-type(1),
    &:nth-of-type(2) {
      height: 74px;
      .chat-ai-app-service-radio__item__name__description {
        white-space: pre-wrap;
      }
    }
  }
}

.chat-ai-app-service-radio__item__name__description {
  @include text-ellipsis-2;
}

.chat-ai-app-service-radio__item-two-row {
  height: 74px !important;
  .chat-ai-app-service-radio__item__name__description {
    white-space: pre-wrap;
  }
}
.chat-ai-app-service-radio__item {
  .chat-ai-app-service-radio__item__name__description{
    white-space: pre-wrap;
  }
}

.chat-ai-app-service-radio__item__icon {
  display: flex;
  align-items: center;
  margin-right: 8px;
  img {
    border-radius: 8px;
    width: 36px;
    height: 36px;
  }
}

.chat-ai-app-service-radio__item {
  display: flex;
  align-items: center;
  .chat-ai-app-service-radio__item__name__title {
    height: 22px;
    font-size: 14px;
    font-weight: 500;
    line-height: 22px;
    color: #262626;
  }
  .chat-ai-app-service-radio__item__name__description {
    font-size: 12px;
    line-height: 20px;
    color: #8C8C8C;
    font-weight: normal;
  }
}

.chat-ai-app-service-list {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.chat-ai-app-service-radio {
  flex: 1;
}

.chat-ai-app-service-list__footer {
  height: 64px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 16px;
  border-top: 1px solid #DCDEE0;
}