<template>
  <div class="biz-chat-panel-header">
    <div class="biz-chat-panel-header-left">
      <!-- <el-image class="avatar" :src="xiaoBaoIcon" /> -->
      <div class="biz-chat-panel-header-left-content overHideCon-1">{{ firstQuestion ? firstQuestion : currentAgentName }}</div>
    </div>

    <div class="biz-chat-panel-header-right">
      <el-tooltip class="item" effect="dark" :content="chatViewWidth !== 100 ? $t('common.base.fullScreen') : $t('ai.app.header.tip1')" placement="bottom">
        <span class="icon-box" @click="changeWidth">
          <i v-show="chatViewWidth !== 100" class="iconfont icon-quanping3" />
          <i v-show="chatViewWidth === 100" class="iconfont icon-zhedie" />
        </span>
      </el-tooltip>
      <el-tooltip class="item" effect="dark" :content="$t('ai.app.header.tip2')" placement="bottom">
        <span class="icon-box">
          <i class="iconfont icon-close" @click="close" />
        </span>
      </el-tooltip>
      
    
    </div>
  </div>
</template>

<script>
import { defineComponent } from 'vue';
import { useStateForAiChat } from '@src/component/business/BizChatPanelNew/chat/hooks';
/* util */
import { getLocalesOssUrl } from '@src/util/assets';

const xiaoBaoIcon = getLocalesOssUrl('/xiao-bao-icon.png');

export default defineComponent({
  name: 'biz-chat-panel-header',
  props: {
    currentAgent: {
      type: Object,
      default: () => {},
    },
    currentApp: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      xiaoBaoIcon,
    };
  },
  setup() {
    const { chatViewWidth, changeChatViewWidth, firstQuestion } = useStateForAiChat();
    function changeWidth() {
      chatViewWidth.value === 100 ? changeChatViewWidth(25) : changeChatViewWidth(100);
    }
    return {
      chatViewWidth,
      changeWidth,
      firstQuestion,
    };
  },
  computed: {
    currentAgentName() {
      return this.currentAgent?.name || '';
    },
    currentAppName() {
      return this.currentApp?.name || '';
    },
  },
  methods: {
    close() {
      this.$emit('close');
    },
  },
});
</script>

<style lang="scss" scoped>
.biz-chat-panel-header {
  background: #ffffff;
  height: 58px;
  padding: 16px;
  display: flex;
  align-content: center;
  justify-content: space-between;

  .el-image {
    width: 26px;
    height: 26px;
    margin-right: 8px;
  }
}

.biz-chat-panel-header-left {
  font-weight: bold;
  display: flex;
  align-items: center;
  flex: 1;
  max-width: 50%;
  .biz-chat-panel-header-left-content {
    flex: 1;
  }
}

.biz-chat-panel-header-right {
  display: flex;
  align-items: center;
}
.icon-box {
  /* 自动布局子元素 */
  width: 32px;
  height: 28px;
  /* 自动布局 */
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0px;
  cursor: pointer;
  &:hover {
    background: #f0f2f5;
    border-radius: 4px;
  }
}
</style>
