<template>
  <div class="biz-chat-panel-header-app">
    
    <div 
      class="biz-chat-panel-header-app-left"
      @click="back"
    >
      <i class="iconfont icon-left" />
      <span> 智能体 </span>
    </div>
    
    <div class="biz-chat-panel-header-app-right">
      <i class="iconfont icon-fe-close" @click="close" />
    </div>
    
  </div>
</template>

<script>
import { defineComponent } from 'vue'
/* util */
import { getLocalesOssUrl } from '@src/util/assets'

const xiaoBaoIcon = getLocalesOssUrl('/xiao-bao-icon.png')


export default defineComponent({
  name: 'biz-chat-panel-header-app',
  data() {
    return {
      xiaoBaoIcon
    }
  },
  methods: {
    back() {
      this.$emit('back')
    },
    close() {
      this.$emit('close')
    },
  }
});
</script>

<style lang="scss" scoped>
.biz-chat-panel-header-app {
  background: #FFFFFF;
  height: 52px;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.biz-chat-panel-header-app-left {
  font-weight: bold;
  display: flex;
  align-items: center;
  cursor: pointer;
  i {
    margin-right: 8px;
  }
}

.biz-chat-panel-header-app-title {
  font-weight: bold;
}

.biz-chat-panel-header-app-right {
  cursor: pointer;
}
</style>