
.chat-ai-index {
  height: 100%;
  width: 100%;
  
  .chat-ai-view-message-list {
    &::-webkit-scrollbar {
      display: none;
      width: 0;
    }
  }
  
}

.chat-ai-frame {
  height: 100%;
  width: 100%;
  overflow: hidden;
  position: relative;
  
  iframe {
    height: 100%;
    width: 100%;
    border: none;
  }
  
}

.chat-ai-view {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: auto;
}

.chat-ai-view--focus {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom); 
}

.chat-ai-view-message-list {
  background-color: #f1f4fe;
  flex: 1;
  overflow-y: auto;
  padding-top: 20px;
  width: 100%;
  max-width: 100%;
  
  .chat-ai-message-list {
    width: 100%;
    max-width: 100%;
  }
  
}

.chat-ai-view {
  .chat-ai-view-footer-app-tag {
    background-color: #f1f4fe;
    padding: 0 24px;
    margin-top: 12px;
    .chat-ai-app-tag {
      cursor: pointer;
    }
  }
  .chat-ai-view-footer-prompt-word-list {
    padding: 0 24px;
    .chat-ai-prompt-tag {
      margin-top: 12px;
      margin-right: 12px;
    }
  }
}

.chat-ai-view-footer-app-tag--disabled {
  cursor: not-allowed;
}