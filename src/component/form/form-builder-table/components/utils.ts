import { t as $t } from '@src/locales';

enum FormTypeList {
    attachment = 'attachment',
    date = 'date',
    address = 'address',
    location = 'location',
    logistics = 'logistics',
    warehouse = 'warehouse',
    user = 'user',
    select = 'select',
    cascader = 'cascader',
    outDataSource = 'outDataSource',
    currency = 'currency',
    formula = 'formula',
    richText = 'richText',
    tag = 'tag',
}
interface LogisticsCompany {
    id: number;
    name: string;
    type: string;
}
interface LogisticsInfo {
    company: LogisticsCompany;
    no: string;
    phone?: string;
}
interface AttachMent {
    fileSize: string;
    filename: string;
    id: string;
    url: string;
}

interface WareHouse {
    positionId: number;
    positionName: string;
    warehouseId: number;
    warehouseName: string;
    warehouseNo: string;
}

interface User {
    cellPhone: string;
    displayName: string;
    head: string;
    staffId: string;
    tagId: string;
    tagType: number;
    userId: string;
}

interface Tag {
    id: string;
    name: string;
    logo: string;
    typeId: number;
    userCount?: number;
    tagChildren?: Tag[];
}

interface Currency {
    currency: string;
    number: number;
}

interface OutDataSource {
    name: string;
    id: number;
}

export function formTypeToView(row: Record<string, any>, column: object, formType: string, fieldName: string): string {
    const value = row[fieldName as string];
    // if (!value) return ""
    if (formType === FormTypeList.attachment) {
        return formatAttachment(value)
    }

    if (formType === FormTypeList.date) {
        return formatDate(value, column) || ''
    }

    if (formType === FormTypeList.address) {
        return formatAddress(value) || ''
    }

    if (formType === FormTypeList.location) {
        return $t('common.base.tip.fieldIsOnlyUploadInMobile')
    }

    if (formType === FormTypeList.logistics) {
        return formatLogistics(value, fieldName) || ''
    }

    if (formType === FormTypeList.warehouse) {
        return formatWareHouse(value) || ''
    }

    if (formType === FormTypeList.user) {
        return formatUser(value, column) || ''
    }

    if (formType === FormTypeList.select) {
        return formatSelect(value) || ''
    }

    if (formType === FormTypeList.cascader) {
        return formatCascader(value, column)
    }

    if (formType === FormTypeList.outDataSource) {
        return formatOutDataSource(value)
    }

    if (formType === FormTypeList.currency) {
        return formatCurrency(value)
    }

    if (formType === FormTypeList.formula) {
        return formatFormula(value, column)
    }

    if (formType === FormTypeList.richText) {
        return formatRickText(value)
    }

    if (formType === FormTypeList.tag) {
        return formatTag(value)
    }

    return row[fieldName] || '';
}

// 格式化部门
function formatTag(value: Tag[]) {
    if (!Array.isArray(value)) {
        value = [value]
    }
    if (value.length === 0) return "";
    return value.map((item: Tag) => {
        return item.name
    }).join(",")
}

/**
 * @description 格式化富文本
 * */ 
export function formatRickText(value: string) {
    if (!value) return '';
    return value
        .replace(/data:[^;]*;base64,[^\"]*/g, '')
        .replace(/<[^>]+>/g, '')
        .replace(/&nbsp;/g, '');
}


/**
 * @description 格式化附件
 */
function formatAttachment(value: AttachMent[]) {
    const attachmentData = value
    return attachmentData.map((item: { filename: any }) => {
        return item.filename ?? '';
    }).join(",");
}

/**
 * @description 时间戳转xxxx-xx-xx模式
 */
function formatDate(timestamp: string | number, column: any): string {
    if (!timestamp) return ''
    let date: Date = new Date(parseInt(timestamp.toString(), 10));

    // 辅助函数：根据格式化字符串格式化日期
    function formatWithPattern(date: Date, pattern: string): string {
        const year = date.getFullYear().toString();
        const month = ('0' + (date.getMonth() + 1)).slice(-2);
        const day = ('0' + date.getDate()).slice(-2);
        const hours = ('0' + date.getHours()).slice(-2);
        const minutes = ('0' + date.getMinutes()).slice(-2);
        const seconds = ('0' + date.getSeconds()).slice(-2);

        return pattern
            .replace('yyyy', year)
            .replace('MM', month)
            .replace('dd', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    }

    // @ts-ignore
    if (column?.setting && (typeof column?.setting.dateType === 'string')) {
        // @ts-ignore
        return formatWithPattern(date, column.setting.dateType)
    }
    // 如果日期存在默认值
    if (column?.setting.defaultValueConfig && column?.setting.defaultValueConfig.isCurrentDate) {
        return timestamp as string
    }

    // @ts-ignore
    return formatWithPattern(date, 'yyyy-MM-dd')
}


enum address {
    province = 'province',
    city = 'city',
    dist = 'dist',
    country = 'country',
    address = 'address',
}

/**
 * @description 地址格式化
 * @param value
 */
function formatAddress(value: Record<address, string>) {
    let { province, city, dist, address } = value;
    if (!province) return "";

    let arr = [province];
    if (!city) return arr[0];

    arr.push(city);
    if (dist) arr.push(dist);

    if (address) arr.push(address);

    return arr.join("");
}

/**
 * @description 物流信息格式化
 */
function formatLogistics(value: LogisticsInfo[], fieldName: string) {
    if (fieldName.includes('logistics_company')) {
        return value.map((item: LogisticsInfo) => {
            return item.company?.name
        }).join("")
    }

    if (fieldName.includes('logistics_no')) {
        return value.map((item: LogisticsInfo) => {
            return item.no
        }).join("")
    }

    if (fieldName.includes('logistics_phone')) {
        return value.map((item: LogisticsInfo) => {
            return item.phone
        }).join("")
    }
}

/**
 * @description 格式化仓库
 */
function formatWareHouse(value: WareHouse) {
    // @ts-ignore
    if (!value.warehouseName || !value.positionName) {
        return ''
    }
    return `${value.warehouseName}${value.positionName ? `-${value.positionName}` : ''}`;
}

/**
 * @description 格式化用户
 */
function formatUser(value: User[] | User, column: any) {
    if (!Array.isArray(value)) {
        value = [value]
    }
    if (value.length === 0) return "";
    return value.map((item: User) => {
        return item.displayName
    }).join(",")
}

/**
 * @description 格式化下拉多选
 * @param value
 */
function formatSelect(value: string[]) {
    if (value === undefined) return '';
    if (typeof value === 'string') return value
    return value.join(",")
}

/**
 * @description 格式化多级菜单
 * @param value
 * @param column
 */
function formatCascader(value: string[], column: any) {
    if (column.setting && column.setting.isMulti) {
        return value.map(item => {
            return item.split('/')[item.split('/').length - 1]
        }).join(",")
    }
    return value.join(" / ")
}

/**
 * @description 格式化外部数据源 这个在测试环境有效，其他环境无效
 * @param value
 */
function formatOutDataSource(value: OutDataSource) {
    return value.name;
    // const { setting } = column;
    // if (setting.isMulti) {
    //     return value.map(item => {
    //         return item.name;
    //     }).join(",")
    // } else {
    //     return value.name;
    // }
}


/**
 * @description 格式化金额
 * @param value
 */
function formatCurrency(value: Currency | undefined): string {
    if (!value) return ''
    return !value.number ? '' : `${value.number} ${value.currency}`
}

/**
 * @description 格式化公式
 */
function formatFormula(value: string, column: any) {
    if (column.setting.defaultValueConfig && column.setting.defaultValueConfig.isNotModify === 1) {
        if (!value) return '请输入'
        return value
    }
    return value
}