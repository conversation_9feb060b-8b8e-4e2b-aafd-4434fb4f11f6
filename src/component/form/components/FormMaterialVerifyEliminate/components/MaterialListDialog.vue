<template>
  <div>
    <base-modal
      class="material-list-dialog-container"
      :show.sync="visible"
      :title="title"
      width="900px"
      @closed="reset"
      append-to-body
    >
      <div v-if="warehouseVerifyEnable" class="mar-l-10">
        {{ isLocationGray ? $t('common.form.preview.materialReturn.label4') : $t('common.form.preview.materialReturn.label3')}}
      </div>
      <div :class="['search-box', {'search-warehouse': warehouseVerifyEnable}]">
        <!--  从仓库核销需要显示仓库，仓位，库位下拉选择    -->
        <el-cascader
          v-if="warehouseVerifyEnable"
          ref="cascader"
          :key="CascaderKey"
          :props="propsConfig"
          clearable
          v-model="warehouseInfo"
          filterable
          @change="searchMaterialsByWarehouse"
        >
        </el-cascader>
        <el-input
          :placeholder="$t('common.form.preview.materialVerifyEliminate.placeholder1')"
          prefix-icon="el-icon-search"
          v-model="keyWord"
          @input="search"
        >
        </el-input>
      </div>
      <div class="table-box" v-loading="loading">
        <material-table-new-version :currentType="currentType" :warehouseVerifyEnable="warehouseVerifyEnable" ref="materialTableNewVersion" :data="copyData" :ruleOptions="ruleOptions" :isShowSettlementRules="isShowSettlementRules" :field="field" :border="true" :isShowSettleType="isShowSettleType" />
      </div>
      <div class="paging-box">
        <el-pagination
          background
          layout="prev, pager, next"
          :total="total"
          :page-size="pageSize"
          @current-change="handlePageChange"
        >
        </el-pagination>
      </div>


      <div class="dialog-footer" slot="footer">
        <el-button :disabled="pending" @click="visible = false">{{$t('common.base.cancel')}}</el-button>
        <el-button type="primary" :disabled="pending" @click="submit">{{$t('common.base.submit')}}</el-button>
      </div>
      <!-- end 出库方式 -->
    </base-modal>

  </div>
</template>

<script>
/* api */
import * as WareHouseApi from '@src/api/WareHouseApi';

/* components */
import MaterialTableNewVersion from './MaterialTableNewVersion.vue';
import _ from 'lodash'
import i18n from '@src/locales'

import { getCustomerArrdess } from '@src/filter/fmt.js';
/*utils*/
import { isEmpty } from 'pub-bbx-utils'
import useFormMultiLanguage from '@hooks/useFormMultiLanguage'
import { handlerMaterialList } from '@src/modules/task/view/components/materialUtil.js';
import {getRootWindow} from "@src/util/dom";

export default {
  name: 'material-list-dialog',
  props: {
    field: {
      type: Object,
      default: () => ({})
    },
    card: {
      type: Object,
      default: () => ({})
    },
    taskId: {
      type: String,
      default: ''
    },
    task: {
      type: Object,
      default: () => ({})
    },
    templateId: {
      type: String,
      default: ''
    },
    // 已选中数据
    value: {
      type: Array,
      default: () => ([])
    },
    columns: {
      type: Array,
      default: () => ([])
    },
    ruleOptions: {
      type: Array,
      default: () => ([])
    },
    // 是否需要被填写申请物料
    showReplacedMaterialCheck:{
      type:Boolean,
      default:false
    },
    // 规则灰度
    isShowSettlementRules: {
      type: Boolean,
      default: false
    },
    isShowSettleType: {
      type: Boolean,
      default: false
    },
    // 产品质保的状态
    serviceQualityStatus: {
      type: String,
      default: ''
    },
    formValue: {
      type: Object,
      default: (() => {})
    },
  },
  data() {
    return {
      CascaderKey:0,
      propsConfig: {
        label:'label',
        value:'value',
        lazy: true,
        lazyLoad:this.lazyLoad,
        checkStrictly: true,
      },
      warehouseInfo: '',
      pending: false,
      visible: false,
      // 完成出库的物料信息
      data: [],
      params: {},
      disabled: false,
      total:0,
      keyWord: '',
      copyData:[],
      pageSize:5,
      pageNum:1,
      title: i18n.t('common.form.preview.materialVerifyEliminate.btn1'),
      internationalGray: false,
      // 仓库仓位库位
      warehouseId: '', // 仓库
      warehousePositionId: '', // 仓位
      locationsId: '', // 库位,
      currentType: '',
      warehousePositionOptions: [], // 存储下仓位的选项
      loading: false,
      personWarehouseId: '', // 个人库id
    }
  },
  computed: {
    /*是否开启指定仓库灰度*/
    isAppointWarehouseGray() {
      const RootWindow = getRootWindow(window)
      return Boolean(RootWindow.grayAuth?.APPOINT_WAREHOUSE_SWITCH)
    },
    /** 物料清单附加组件配置 */
    cardConfig() {
      return JSON.parse(this.card.config || '{}');
    },
    /*是否从仓库核销*/
    warehouseVerifyEnable() {
      return this.currentType === 'warehouse';
    },
    /*是否开启库位灰度*/
    isLocationGray() {
      const RootWindow = getRootWindow(window)
      return Boolean(RootWindow.grayAuth?.LOCATION_SWITCH)
    },
    /* 是否只能申领被工单占用的库存*/
    onlyTaskOccupy() {
      return this.field?.setting?.onlyTaskOccupy || false
    },
    taskPropertiesFileName() {
      return this.field?.setting?.taskPropertiesFileName || ''
    },
  },
  mounted() {
    const { internationalGray } = useFormMultiLanguage()
    this.internationalGray = internationalGray
  },
  methods: {
    searchMaterialsByWarehouse() {
      if (this.$refs.cascader) {
        const node = this.$refs.cascader?.getCheckedNodes()?.[0] ?? '';
        node && this.$refs.cascader?.panel?.lazyLoad(node);
      }
    },
    async lazyLoad(node, resolve) {
      try {
        const { level, value, loaded } = node;

        if (loaded) return resolve();

        let nodes = [];

        if (level === 0) {
          // 加载二级节点：仓位
          nodes = await this.getAppointWarehouse();
        }else if (level === 1) {
          // 加载二级节点：仓位
          this.warehouseId = value;
          nodes = await this.getAppointWarehousePosition();
          this.warehousePositionOptions = _.cloneDeep(nodes);
        } else if (level === 2) {
          // 加载三级节点：库位
          this.warehousePositionId = value;
          nodes = await this.getAppointLocation();
        }
        resolve(nodes);
      } catch (e) {
        console.error(e);
      } finally {
        // 调用查询物料的接口
        this.getWarehousePersonData()
      }
    },

    // 根据物料核销系统设置配置的类型，'指定仓库'返回指定的仓库，'发起人的部门所在备件仓'两种种类型去返回
    async getAppointWarehouse() {
      try {
        const { result } = await WareHouseApi.getWarehouseInTask({ taskTypeId: this.templateId, taskId: this.taskId })

        const warehouseId = result?.[0]?.id ?? ''
        this.warehouseInfo = [warehouseId]

        return result?.map(item => {
          return {
            label: item.name,
            value: item.id,
            leaf: false,
          }
        })

      } catch (e) {
        console.error(e)
      }
    },

    // 根据物料核销系统设置配置的类型，指定仓位返回指定的仓位（判断仓位是否还在），发起人的部门所在仓位（根据当前的仓库去返回所有的仓位）
    async getAppointWarehousePosition() {
      try {
        let params = { warehouseId: this.warehouseId, taskTypeId: this.templateId, taskId: this.taskId }

        // 开利冷链定制，需要传一个工单属性
        if(!isEmpty(this.taskPropertiesFileName)) {
          params.taskProperties = this.formValue?.[this.taskPropertiesFileName]
        }
        const { result } = await  WareHouseApi.getPositionListInTask( params)

        return result?.map(item => {
          return {
            label: item.name,
            value: item.id,
            leaf: !this.isLocationGray, // 是否叶子节点根据库位的灰度判断，有库位就不是叶子节点
          }
        })

      } catch (e) {
        console.error(e)
      }
    },

    // 根据物料核销系统设置配置的类型，指定库位返回指定的库位（判断仓位是否还在），发起人的部门所在仓位（这种类型根据当前的仓库仓位去返回所有的库位）
    async getAppointLocation() {
      try {
        const { result } = await WareHouseApi.getLocationListInTask({
          warehouseId: this.warehouseId,
          positionIds: [this.warehousePositionId],
          taskTypeId: this.templateId,
          taskId: this.taskId
        })

        return  result?.map(item => {
          return {
            label: item.warehouseLocationSn,
            value: item.id,
            leaf: true,
          }
        })

      } catch (e) {
        console.error(e)
      }
    },

    // 搜索
    search() {
      // 根据keyword 对静态 data 数据进行筛选

      // 物料核销从仓库核销筛选走接口
      if(this.type === 'warehouse'){
        return this.getWarehousePersonData()
      }else if(this.type === 'personalLibrary'){
        if(this.personWarehouseId) {
          return this.getWarehousePersonData(this.personWarehouseId)
        } {
          return this.getPersonalWarehouseList()
        }
      }

      let data = this.data;
      let keyWord = this.keyWord;
      if (keyWord) {
        data = data.filter(item => {
          return item.materialName.indexOf(keyWord) > -1 || item.materialSn.indexOf(keyWord) > -1
        });
      }
      if(data.length){
        this.copyData = _.cloneDeep(data.slice(0, 5))
        this.total = data.length
      }else{
        this.copyData = []
        this.total = 0
      }
    },
    // 页数改变
    handlePageChange(val) {
      this.pageNum = val
      if(this.type === 'warehouse'){
        this.getWarehousePersonData()
      }else if(this.type === 'personalLibrary'){
        this.getPersonalWarehouseList()
      }else{
        // 前端通过val对数据进行分页
        this.copyData = this.data.slice((val - 1) * this.pageSize, val * this.pageSize);
      }
    },
    /**
    * @description 打开弹窗
    */
    open(type) {
      this.data = []
      this.copyData = []
      this.keyWord = ''
      this.$refs?.materialTableNewVersion?.outsideSetTableData()
      if(!type){
        this.title = this.$t('common.form.preview.materialVerifyEliminate.btn1')
        this.getTaskListMaterial()
      }else if(type === 'personalLibrary'){
        this.title = this.$t('common.form.preview.materialVerifyEliminate.btn2')
        this.getPersonalWarehouseList(type)
      }else if(type === 'warehouse'){ // 从仓库核销
        this.title = this.$t('task.materialVerify.setting.text12')
        ++ this.CascaderKey
      }
      this.type = type
      this.currentType = type;
      this.pending = false
      this.visible = true
    },
    /* 处理剩余库存，如果页面已经选择的物料，相对应的num需要处理
    * parentTableData:物料核销已经选择的物料
    * */
    handlerAvailableNum(parentTableData = [], item) {
      let availableNum = item.num
      parentTableData?.forEach(val => {
        let materialId = val.materialId ?? val.primaryId
        let warehousePositionId = val.warehousePositionId ?? val.positionId

        /*判断库位是否匹配(多加一个灰度判断，开启指定仓库才会判断库位，避免后端接口返回了库位)
           desc:这段代码的逻辑是通过locationId 判断两个位置是否匹配。
                 如果 this.isAppointWarehouseGray 为 true，则检查 locationId；否则，默认认为匹配
           * 1.如果库位都有值，判断相等才会合并，isLocationMatch才可能为true
           * 2.如果库位都没有值，isLocationMatch为true
           * 3.isAppointWarehouseGray灰度没有开启isLocationMatch为true
           * */
        let isLocationMatch = false;

        // 如果没有开启指定仓库灰度，直接返回 true
        if (!this.isAppointWarehouseGray) {
          isLocationMatch = true;
        } else if ((isEmpty(val.locationId) && isEmpty(item.warehouseLocationId)) || (val.locationId == item.warehouseLocationId)) { // 如果需要检查仓库灰度，进一步判断 locationId
          isLocationMatch = true;
        }

        if(
            Number(materialId) === item.materialId && // 物料Id相同
            val.warehouseId === item.warehouseId && // 仓库ID相同
            Number(warehousePositionId) === item.warehousePositionId && // 仓位Id相同
            isLocationMatch // 库位
        ){
          availableNum = item.num - val.number
        }
      })
      return availableNum < 0 ? 0 : availableNum
    },
    /**
    * @description 获取已出库的物料清单
    */
    getTaskListMaterial(){
      let params = {
        taskId: this.task?.id || '',
        needAreaPrice: this.field?.setting?.useRegionalPricing || false,
        address: getCustomerArrdess(this.task),
        onlyTaskOccupy: this.onlyTaskOccupy, // 是否只能申领被工单占用的库存
      }
      WareHouseApi.getOutWarehouseMaterialFinished(params).then(res=>{
        if(res.success){
          let dataList = res.result || []
          const parentTableData = this.$parent.tableValue || []
          let materialData = dataList.map(item => {
            item.num = this.handlerAvailableNum(parentTableData, item)
            item.number = 0
            item.availableNum = item.num // 统一可用库存字段名
            item.positionId = item.warehousePositionId
            item.snList = item.snList || [] 
            item.settlementRulesId = '' // 结算规则

            // 库位
            item.locationId = item?.warehouseLocationId ?? ''
            item.locationName = item?.warehouseLocationName ?? ''

            return item
          })
          this.data = handlerMaterialList(materialData, this.columns)
          
          if(this.data.length){
            this.copyData = _.cloneDeep(this.data.slice(0, 5))
            this.total = this.data.length
          }else{
            this.copyData = []
            this.total = 0
          }
        }
      })
    },
    // 获取个人库的仓库信息
    getPersonalWarehouseList() {
      // 个人库数据
      WareHouseApi.getPersonalInventoryWarehouse()
        .then((result) => {
          
          if (result?.success) {
            let warehouse = result?.data.warehouse || {}
            this.personWarehouseId = warehouse.id;
            this.getWarehousePersonData(warehouse.id)
          }
          
        }).catch(error => {
          console.error(error);
        })
    },
    /**
    * @description 获取个人库和仓库的物料清单
    */
    getWarehousePersonData(id){
      let params = {
        keyword: this.keyWord,
        pageNum:this.pageNum,
        pageSize:this.pageSize,
        warehouseId:id,
        personWarehouse: this.type === 'personalLibrary' ? true : false,
        needAreaPrice: this.field?.setting?.useRegionalPricing || false,
        address: getCustomerArrdess(this.task),
        onlyTaskOccupy: this.onlyTaskOccupy, // 是否只能申领被工单占用的库存
        taskId: this.taskId,
      }

      // 如何核销指定仓库，参数修改
      if(this.warehouseVerifyEnable) {
        params.appointWarehouse = true; // 是否核销指定仓库
        params.warehouseId = this.warehouseInfo?.[0] ?? ''; // 仓库
        params.positionId = this.warehouseInfo?.[1] ?? ''; // 仓位
        params.locationId = this.warehouseInfo?.[2] ?? ''; // 库位

        // 开利定制，查询的时候只选中仓库的时候是不需要传仓位的，仓位是空的，但是开利需要把仓位也传过去
        if(isEmpty(params.positionId) && !isEmpty(this.taskPropertiesFileName) && this.warehousePositionOptions?.length === 1) {
          params.positionId = this.warehousePositionOptions?.[0]?.value ?? '';
        }

      }
      this.loading= true;
      WareHouseApi.getWarehousePersonDataApi(params).then(res=>{
        if(res.success){
          let dataList = handlerMaterialList((res.result?.list || []), this.columns)

          let materialData = dataList.map(item => {
            item.number = 1
            // 处理num
            const parentTableData = this.$parent.tableValue || []
            item.num = this.handlerAvailableNum(parentTableData, item)

            item.availableNum = item.num // 统一可用库存字段名
            // 可核销数量小于0，number不能为1
            if (item.availableNum<1) {
              item.number = 0
            }
            item.positionId = item.warehousePositionId
            item.snList = item.snList || [] 
            item.settlementRulesId = '' // 结算规则
            // 没有物料质保状态，就取产品的质保状态
            item.warrantyStatus = item.warrantyStatus ? item.warrantyStatus : this.serviceQualityStatus
            // 是否个人库
            item.isPerson = item?.isPerson ?? false
            // 判断当前查询的物料是否选择指定仓库拿到的，后端用这个字段去扣库存
            item.isAppointWarehouse = this.warehouseVerifyEnable
            return item
          })

          this.data = materialData
          this.total = res.result.total 
          if(this.data.length){
            this.copyData = _.cloneDeep(this.data)
          }else{
            this.copyData = []
          }
        }
      }).finally(() => {
        this.loading= false;
      })
    },
    /**
    * @description 提交
    * @param {Object} formParams 添加物料
    */
    async submit() {
      let tableData = this.getTableData()
      tableData = tableData.length && tableData.filter(item => item.number)
      if (!tableData.length) return this.$message.warning(this.$t('common.form.preview.materialVerifyEliminate.tip1'))

      if (this.isShowSettlementRules) {
        // 选中的数据只要有一个没有勾选规则就提示
        const isPass = tableData.some(item => item?.settlementRulesId?.toString().length <= 0)
        if(isPass) {
          return this.$message.warning(this.$t('zhongqi.tipList.tips2'))
        }
      }
      if(this.internationalGray) {
        tableData = tableData.map(item=> {
          return {
            ...item,
            salePriceCurrency: item.salePriceCurrency || 'CNY'
          }
        })
      }

      // 开启配件结算方式 则必填
      if (this.isShowSettleType) {
        const isExist = tableData.some(item => !item?.settleType)
        if (isExist) return this.$message.warning(this.$t('common.form.preview.materialVerifyEliminate.tip4'))
      }

      let copyData = []
      // 允许显示被替换的物料 
      if(this.showReplacedMaterialCheck){
        tableData = tableData.map(item=>{
          item.replacedMaterial = {
            name:item.name || item.materialName,
            materialName:item.name || item.materialName,
            materialSn:item.materialSn || item.sn,
            label:item.materialName,
            value:item.materialId,
            materialId:item.materialId,
            id:item.materialId,
            warrantyStatus:item.warrantyStatus,
            snManage:item.snManage,
            image:item.image,
            images:item.images,
            property:item.property || item.materialProperty,
            unit:item.unit || item.materialUnit,
            salePrice:item.salePrice || 0, 
            salePriceCurrency:item.salePriceCurrency || 'CNY', 
            num:1,
            isReplaced:true,
            selectBy:this.type,
          }
          return item
        })
        tableData.forEach(item => {
          if(item.number){
            for(let i = 0; i < item.number; i++){
              let obj = {
                ...item,
                number:1,
              }
              copyData.push(obj)
            }
          }
        })
      }else{
        // 如果是SN管理的
        tableData.forEach(item => {
          // TODO 国际化待办
          if(item.snManage === '是'){
            if(item.number){
              for(let i = 0; i < item.number; i++){
                let obj = {
                  ...item,
                  number:1,
                }
                copyData.push(obj)
              }
            }
            // 处理批次号并且没有sn的情况
          } else if(item.batchNumManage === 1 || item.batchNumManage === '是') {
            if(item.number){
              // 历史处理过的数量
              // let pushedNum = 0 
              let applyNum = item.number
              // 根据后端的的批次号列表循环
              for(let i = 0; i < (item?.materialRelationList || []).length; i++){
                const batchOrSnItem = item.materialRelationList[i] 
                applyNum -= batchOrSnItem.num
                // 判断总加的数量是否满足自己输入的数量
               
                let obj = {
                  ...item,
                  number: applyNum >= 0 ? batchOrSnItem.num : applyNum + batchOrSnItem.num,
                }
                copyData.push(obj)
              
                
                if(applyNum <= 0) break
              }
            }
          } else{
            copyData.push(item)  
          }
        })
      }

      this.$emit('input', {
        data:copyData,
        refsName: 'materialListDialog'
      })
    },
    getTableData() {
      return this.$refs?.materialTableNewVersion?.multipleSelection || []
    },
    close() {
      this.visible = false
      this.reset()
    },
    reset() {
      this.initForm()
    },
    initForm() {
      this.disabled = false
    }
  },
  components: {
    [MaterialTableNewVersion.name]: MaterialTableNewVersion
  }
}
</script>

<style lang="scss" scoped>
.material-list-dialog-container {
  ::v-deep .base-modal-body {
    padding: 16px 0 !important;
  }
}
.search-box , .table-box {
  padding:10px;
}
.search-box {
  width: 50%;
}
.paging-box{
  text-align: right;
}

.search-warehouse {
  width: 100%;
  display: flex;
  justify-content: space-between;

  .el-input {
    width: 220px !important;
  }
  .el-cascader {
    width: 320px !important;
  }
}
</style>