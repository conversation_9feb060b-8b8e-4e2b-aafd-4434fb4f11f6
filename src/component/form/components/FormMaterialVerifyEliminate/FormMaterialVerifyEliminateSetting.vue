<template>
  <div class="form-setting-panel">
    <!-- start 标题 -->
    <div class="form-setting-group form-common-setting">
      <h3 class="form-setting-panel-title">{{ field.displayName }}</h3>
      <div class="form-design-warning">{{$t('common.form.tip.materialVerifyEliminate.tips1')}}</div>
    </div>
    <!-- end 标题 -->
    
    <!-- start 描述信息 -->
    <form-describe-setting
      :field="field"
      :mode="mode"
      @input="updateForDom"
    ></form-describe-setting>
    <!-- end 描述信息 -->
    
    <!-- start 校验 -->
    <div class="form-setting-group form-setting-item">
      <h4 class="form-item-title">{{$t('common.base.check')}}</h4>
      <div class="form-item-box">
        <!-- 必填 -->
        <form-required-setting :field="field" @input="update"></form-required-setting>
      </div>
    </div>
    <!-- end 校验 -->

    <!-- 结算规则设置 S -->
    <div class="form-setting-group form-common-setting" v-if="isShowSettlementRules">
      <h4 class="form-item-title">{{ $t('zhongqi.setting.text2') }}</h4>
      <div class="form-item-box">{{ $t('zhongqi.setting.text1') }}</div>
      <settlement-rules ref="settlementRules" @saveRule="saveRule" :fields="fields" :field="field"/>
    </div>
    <!-- 结算规则设置 E -->
    <!-- start 物料关联显示字段 -->
    <div class="form-setting-group form-setting-item">
      <h4 class="form-item-title">{{ $t('common.form.preview.material.label2') }}</h4>
      <div class="form-item-box">
        <div class="form-setting-item">
          <el-select
            class="form-material-verify-select"
            :value="materialFieldsValue(field)"
            multiple
            :multiple-limit="10"
            @change="columsSelectHandler"
          >
            <el-option
              v-for="item in materialVerifyEliminateColums"
              :key="item.field"
              :label="item.label"
              :value="item.field"
              :disabled="checkFieldDisabledHandler(item.field)"
            ></el-option>
          </el-select>
        </div>
      </div>
    </div>
    <!-- end 物料关联显示字段 -->

    <!--  物料核销方式Start  -->
    <div v-if="isAppointWarehouseGray" class="form-setting-group form-setting-item">
      <h4 class="form-item-title">{{$t('task.materialVerify.setting.text0')}}</h4>
      <!-- 可从物料申领单中核销 -->
      <div class="form-item-box">
        <el-checkbox :value="field.setting.canFromMaterialRequisition" @input="updateSetting(...arguments,'canFromMaterialRequisition')">
          {{$t('task.materialVerify.setting.text1')}}
          <el-tooltip :content="$t('task.materialVerify.setting.text8')">
            <span class="iconfont icon-info-circle"></span>
          </el-tooltip>
        </el-checkbox>
      </div>
      <!-- 可从个人库中核销:需要开启个人库灰度 -->
      <div v-if="!isTaskCloudWarehousePerson" class="form-item-box">
        <el-checkbox :value="field.setting.canFromPersonalLibrary" @input="updateSetting(...arguments,'canFromPersonalLibrary')">
          {{$t('task.materialVerify.setting.text2')}}
          <el-tooltip :content="$t('task.materialVerify.setting.text9')">
            <span class="iconfont icon-info-circle"></span>
          </el-tooltip>
        </el-checkbox>
      </div>
      <!-- 可从指定仓库核销 -->
      <div class="form-item-box">
        <el-checkbox class="mar-t-8" :value="field.setting.canFromAppointWarehouse" @input="updateSetting(...arguments,'canFromAppointWarehouse')">
          {{$t('task.materialVerify.setting.text3')}}
          <el-tooltip :content="$t('task.materialVerify.setting.text10')">
            <span class="iconfont icon-info-circle"></span>
          </el-tooltip>
        </el-checkbox>

        <div v-show="field.setting.canFromAppointWarehouse" class="warehouse-select">
          <el-select class="mar-t-8" v-model="field.setting.appointWarehouseType" @change="(val)=>changeAppointWarehouseType(val,'appointWarehouseType')" :placeholder="$t('task.materialVerify.setting.text4')">
            <el-option :label="$t('task.materialVerify.setting.text6')" value="promoterTag"></el-option>
            <el-option :label="$t('task.applyWarehouse.optionType.option1')" value="pointWarehouse"></el-option>
          </el-select>

          <!--   仓库     -->
          <template v-if="field.setting.appointWarehouseType == 'pointWarehouse'">
            <el-select
              class="mar-t-8"
              v-model="field.setting.appointWarehouseId"
              :placeholder="$t('common.form.placeHolder.materialReturn.pla2')"
              filterable
              clearable
              @change="(val)=>changeAppointWarehouse(val, 'appointWarehouseId')"
            >
              <el-option v-for="item in availableAllWarehouses" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>

            <!--  仓位   -->
            <el-select collapse-tags multiple :disabled="appointWarehouseEmpty('appointWarehouseId')" class="mar-t-8" v-model="field.setting.appointPositionId" :placeholder="$t('common.form.placeHolder.materialReturn.pla3')" filterable clearable @change="(val)=>changeAppointPosition(val,'appointPositionId')">
              <el-option v-for="item in availableAllPositions" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>

            <!--   库位(需要开启库位灰度)      -->
            <el-select collapse-tags multiple v-if="isLocationGray" :disabled="appointPositionEmpty('appointPositionId')" class="mar-t-8" v-model="field.setting.appointLocationId" :placeholder="$t('task.materialVerify.setting.text5')" filterable clearable>
              <el-option v-for="item in availableAllLocations" :key="item.id" :label="item.warehouseLocationSn" :value="item.id"></el-option>
            </el-select>
          </template>

        </div>

        <div class="only-tas-occupy">
          <div>{{ $t('task.materialVerify.setting.text7') }}</div>
          <el-switch v-model="field.setting.onlyTaskOccupy" class="setting-spare-switch"></el-switch>
        </div>
      </div>
    </div>
    <!--  物料核销方式End  -->

    <!-- 其他设置 start -->
    <div class="form-setting-group form-setting-item" v-if="allowPublicSet">
      <h4 class="form-item-title">{{$t('common.base.otherSet')}}</h4>
      <!-- 是否需要填写被替换物料 start -->
      <div class="form-item-box">
        <el-checkbox :value="field.setting.replacedMaterialCheck" @input="updateSetting(...arguments,'replacedMaterialCheck')">
          {{$t('common.form.tip.materialVerifyEliminate.tips2')}}
        </el-checkbox>
      </div>
      <!-- 是否需要填写被替换物料 end -->
      <!-- 仅可从物料申领中添加物料 start -->
      <div v-if="!isAppointWarehouseGray" class="form-item-box">
        <el-checkbox :value="field.setting.onlyFromMaterial" @input="updateSetting(...arguments,'onlyFromMaterial')">
          {{$t('common.form.tip.materialVerifyEliminate.tips6')}}
        </el-checkbox>
      </div>
      <!-- 仅可从物料申领中添加物料 end -->
      <div class="form-item-box">
        <el-checkbox v-if="isAreaPrice" :value="field.setting.useRegionalPricing" @input="updateSetting(...arguments,'useRegionalPricing')">
          {{$t('common.base.useRegionalPricing')}}
        </el-checkbox>
      </div>

      <!-- 核销后是否在客户名下生成新产品 start-->
      <div class="form-item-box">
        <el-checkbox :value="field.setting.createNewProduct" @input="updateSetting(...arguments,'createNewProduct')">
          {{$t('common.form.tip.materialVerifyEliminate.createProductTips')}}
          <el-tooltip  placement="top" popper-class="form-msg-setting-tooltip">
            <div slot="content">
              <div class="tooltip">{{$t('common.form.tip.materialVerifyEliminate.createProductTips1')}}</div>
            </div>
            <i class="iconfont icon-question"></i>
          </el-tooltip>
        </el-checkbox>
      </div>
      <!-- 核销后是否在客户名下生成新产品 end-->

      <!-- 二次确认表单字段 start-->
      <div v-if="field.setting.createNewProduct" class="form-setting-item">
        <h4 class="form-item-title">{{$t('common.form.tip.materialVerifyEliminate.reconfirmTitle')}}</h4>
        <el-select
          class="form-material-verify-select"
          :value="reconfirmValue(field)"
          multiple
          @change="reconfirmColumsHandler"
        >
          <el-option
            v-for="item in reconfirmColums"
            :key="item.field"
            :label="item.label"
            :value="item.field"
            :disabled="item.disabled"
          ></el-option>
        </el-select>
      </div>
      <!-- 二次确认表单字段 end-->

      <!-- 无物料消耗备注 start-->
      <div class="form-item-box">
        <el-checkbox :value="field.setting.noDataPlaceHolderSwitch" @input="updateNoDataPlaceHolderSetting(...arguments, 'noDataPlaceHolderSwitch')">
          {{$t('common.form.tip.materialVerifyEliminate.noDataRemark')}}
        </el-checkbox>
      </div>
      <div v-show="field.setting.noDataPlaceHolderSwitch" class="form-type-text-lang">
        <el-input :value="defaultNoDataPlaceHolder" @input="handleInput"></el-input>
        <base-select-language 
          v-if="isShow"
          :title="$t('common.base.description')"
          :field="field"
          :is-require="false"
          :defaultOption="{
            formType:'text',
          }"
          :maxlength="TitleShowMaxLengthMax"
          defaultFormType="text"
          :defaultValue="defaultNoDataPlaceHolder"
          :defaultValueLanguage="noDataPlaceHolder || {}"
          @save="save"
        >
        </base-select-language>
      </div>
      <!-- 无物料消耗备注 end-->

      <!-- start 慈星定制 配件结算方式 -->
      <div class="form-item-box" v-if="isCiXing">
        <el-checkbox :value="field.setting.isShowSettleType" @input="updateSettleType">配件结算方式</el-checkbox>
        <div class="settlement-method-list" v-show="field.setting.isShowSettleType">
          <div class="settlement-method-list-item" v-for="(option, index) in field.setting.settleTypeOptions" :key="index">
            <el-input :value="option" disabled></el-input>
          </div>
        </div>
      </div>
      <!-- end 慈星定制 配件结算方式 -->

      <div class="form-item-box">
        <el-checkbox :value="field.setting.canApproveStateAllowWriteOff" @input="updateSetting(...arguments,'canApproveStateAllowWriteOff')">
        {{$t('task.materialVerify.setting.text15')}}
        <el-tooltip  placement="top" popper-class="form-msg-setting-tooltip">
            <div slot="content">
              <div class="tooltip"> {{$t('task.materialVerify.setting.text16')}}</div>
            </div>
            <i class="iconfont icon-question"></i>
          </el-tooltip>
        </el-checkbox>
        <div v-if="field.setting.canApproveStateAllowWriteOff" class="warehouse-select">
          <el-select class="mar-t-8" v-model="field.setting.approveStateAllowWriteOff" @change="(val)=>changeAppointWarehouseType(val,'approveStateAllowWriteOff')">
            <el-option :label="$t('task.materialVerify.setting.text6')" value="taskOperator"></el-option>
            <el-option :label="$t('task.materialVerify.setting.text17')" value="taskExecutor"></el-option>
            <el-option :label="$t('task.applyWarehouse.optionType.option1')" value="taskAppointWarehouse"></el-option>
          </el-select>
           <!--仓库-->
          <template v-if="field.setting.approveStateAllowWriteOff == 'taskAppointWarehouse'">
            <el-select
              class="mar-t-8"
              v-model="field.setting.taskApproveAppointWarehouseId"
              :placeholder="$t('common.form.placeHolder.materialReturn.pla2')"
              filterable
              clearable
              @change="(val)=>changeAppointWarehouse(val, 'taskApproveAppointWarehouseId')"
            >
              <el-option v-for="item in availableAllWarehouses" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>

            <!--仓位-->
            <el-select collapse-tags multiple :disabled="appointWarehouseEmpty('taskApproveAppointWarehouseId')" class="mar-t-8" v-model="field.setting.taskApproveAppointPositionIds" :placeholder="$t('common.form.placeHolder.materialReturn.pla3')" filterable clearable @change="(val)=>changeAppointPosition(val,'taskApproveAppointPositionIds')">
              <el-option v-for="item in taskApproveAvailableAllPositions" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>

            <!--库位(需要开启库位灰度) -->
            <el-select collapse-tags multiple v-if="isLocationGray" :disabled="appointPositionEmpty('taskApproveAppointPositionIds')" class="mar-t-8" v-model="field.setting.taskApproveAppointLocationIds" :placeholder="$t('task.materialVerify.setting.text5')" filterable clearable>
              <el-option v-for="item in taskApproveAvailableAllLocations" :key="item.id" :label="item.warehouseLocationSn" :value="item.id"></el-option>
            </el-select>
          </template>
        </div>

      </div>
      
    </div>
    <!-- 其他设置 end -->
  </div>
</template>

<script>
import { languagesList, isMultiLangModules } from '@src/component/util/multiLang/index';
import * as config from '@src/component/form/config';
import locales, { t } from '@src/locales';

import SettingMixin from '@src/component/form/mixin/setting';
import { settingProps } from '@src/component/form/components/props';

import SettlementRules from '@src/component/form/components/FormServiceIterm/components/SettlementRules.vue';
import { getRootWindow } from '@src/util/dom';
import { getOpenMultiLanguage } from '@hooks/useFormMultiLanguage'

// util
import { findComponentUpward } from '@src/util/assist';
import { MATERIAL_VERIFY_ELIMINATE_SETTLE_TYPE_OPTIONS } from '@src/component/form/config';
import { getLocationPageList, getWarehousePositionSearch, searchWarehouse} from "@src/api/WareHouseApi";
import { isEmpty } from "pub-bbx-utils";

export default {
  name: 'form-materialverifyeliminate-setting',
  mixins: [SettingMixin],
  props: settingProps,
  components: {
    [SettlementRules.name]: SettlementRules
  },
  data(){
    
    return {
      languagesList,
      systemFields: [],
      reconfirmFixedFieldValues: ['name', 'serialNumber', 'catalogId'],
      noDataPlaceHolderData: {
        zh: '',
        en: ''
      },
      availableAllWarehouses: [],
      availableAllPositions: [],
      availableAllLocations: [],
      taskApproveAvailableAllPositions: [], //工单审批指定仓位
      taskApproveAvailableAllLocations: [], //工单审批指定库位
    }
  },
  methods: {
    /**指定仓库是否为空 */
    appointWarehouseEmpty(type) {
      return isEmpty(this.field?.setting?.[type]);
    },
    /**指定仓位是否为空 */
    appointPositionEmpty(type) {
      return isEmpty(this.field?.setting?.[type]);
    },
    /*查询仓库*/
    async searchWarehouse() {
      // 查询系统可用仓库
      try {
        const { data, success } = await searchWarehouse({enabled: true});

        if(!success) return;

        this.availableAllWarehouses = data
      } catch (e) {
        console.error(e)
      } finally {
        // 重置指定仓库
        this.resetSettingsIfNotExist('appointWarehouseId', 'appointPositionId', 'appointLocationId'); 
        // 重置审批人指定仓库
        this.resetSettingsIfNotExist('taskApproveAppointWarehouseId', 'taskApproveAppointPositionIds', 'taskApproveAppointLocationIds');
      }
    },
    /** 如果仓库被删了，重置仓库 */
    resetSettingsIfNotExist(warehouseIdKey, positionIdsKey, locationIdsKey) {
      if(this.availableAllWarehouses?.every(item => item.id !== this.field?.setting?.[warehouseIdKey])) {
        this.field.setting = {
          ...this.field.setting,
          [warehouseIdKey]: '',
          [positionIdsKey]: [],
          [locationIdsKey]: [],
        };
      }
    },
    /*查询仓位*/
    async getWarehousePositionSearch(type) {
      try {
        // 查询当前仓库下面的仓位
        const params = {
          warehouseId: this.field.setting?.[type] ?? '',
          enabled: true,
        }
        const { data, success } = await getWarehousePositionSearch(params);

        if(!success) return;

        if(type == 'appointWarehouseId') {
          this.availableAllPositions = data;
        }else {
          this.taskApproveAvailableAllPositions = data;
        }
      } catch (e) {
        console.error(e)
      } finally {
        // 过滤不存在仓位
        const { appointPositionId, taskApproveAppointPositionIds } = this.field?.setting ?? {};
        const filterPositions = (ids, availablePositions) => ids?.filter(id => availablePositions?.some(pos => pos.id === id)) ?? [];

        if(type == 'appointWarehouseId' && appointPositionId?.length ) {
          this.field.setting.appointPositionId = filterPositions(appointPositionId, this.availableAllPositions);
        }
        if(type == 'taskApproveAppointWarehouseId' && taskApproveAppointPositionIds?.length) {
          this.field.setting.taskApproveAppointPositionIds = filterPositions(taskApproveAppointPositionIds, this.taskApproveAvailableAllPositions);
        }
      }
    },

    /*查询库位*/
    async getLocationPageList(type) {
      try{
        // 查询当前仓位下面的库位
        const params = {
          warehouseId: type == 'appointPositionId' ? this.field.setting.appointWarehouseId : this.field.setting.taskApproveAppointWarehouseId,
          warehousePositionIdList:this.field.setting?.[type] ?? [],
          enabled: true,
          pageSize: 0,
        }
        const { data, success } = await getLocationPageList(params);

        if(!success) return;
        if(type == 'appointPositionId') {
          this.availableAllLocations = data?.list ?? [];
        }else {
          this.taskApproveAvailableAllLocations = data?.list ?? [];
        }
      } catch (e) {
        console.error(e)
      } finally {
        // 过滤被删除的库位
        if(this.field?.setting?.appointLocationId?.length) {
          this.field.setting.appointLocationId = this.field.setting.appointLocationId.filter(item =>
              this.availableAllLocations?.some(zitem => zitem.id == item)
          ) ?? []
        }

        const { taskApproveAppointLocationIds } = this.field?.setting ?? {}
        if(taskApproveAppointLocationIds?.length) {
          this.field.setting.taskApproveAppointLocationIds = taskApproveAppointLocationIds.filter(item =>
            this.taskApproveAvailableAllLocations?.some(zitem => zitem.id == item)
          ) ?? []
        }
      }
    },

    /*选择指定仓库类型*/
    changeAppointWarehouseType(val, type) {
      this.updateSetting(val, type)

      if(val !== 'pointWarehouse' ||  val !== 'taskAppointWarehouse') return;

      this.searchWarehouse();
    },

    /*选择仓库*/
    changeAppointWarehouse(val, type) {
      this.updateSetting(val, type)
      if (type == 'appointWarehouseId' && isEmpty(this.field.setting.appointWarehouseId)) {
        this.field.setting.appointPositionId = [];
        this.field.setting.appointLocationId = [];
        return
      }

      if(type == 'taskApproveAppointWarehouseId' && isEmpty(this.field.setting.taskApproveAppointWarehouseId)) {
        this.field.setting.taskApproveAppointPositionIds = [];
        this.field.setting.taskApproveAppointLocationIds = [];
        return
      }

      this.getWarehousePositionSearch(type);
    },

    /*选择仓位*/
    changeAppointPosition(val, type) {
      this.updateSetting(val, type)

      const { appointPositionId, taskApproveAppointPositionIds } = this.field.setting;

      if(type == 'appointPositionId' && isEmpty(appointPositionId)) {
        this.field.setting.appointLocationId = [];
        return;
      };
      if(type == 'taskApproveAppointPositionIds' && isEmpty(taskApproveAppointPositionIds)) {
        this.field.setting.taskApproveAvailableAllLocations = [];
        return;
      };

      this.getLocationPageList(type);

    },

    save(data){
      this.noDataPlaceHolder = data
      this.updateSetting(data, 'noDataPlaceHolder')
    },
    handleInput(value) {
      this.noDataPlaceHolder[locales.locale] = value
      this.updateNoDataPlaceHolderSetting(this.noDataPlaceHolder, 'noDataPlaceHolder')
    },
    updateForDom(event){
      let el = event.target;
      let prop = el.dataset.prop;
      let value = el.value;
      
      this.update(value, prop)
    },
    update(value, prop, isSetting = false) {
      this.$emit('input', {value, prop, isSetting});
    },
    updateNoDataPlaceHolderSetting(value, type) {
      this.$set(this.field.setting, type, value)
      this.$set(this.field.setting, 'noDataPlaceHolder', this.noDataPlaceHolder)
    },
    // 更新setting
    updateSetting(value, type){
      this.$set(this.field.setting, type, value)
    },
    // 保存质保规则
    saveRule(value) {
      this.update(value, 'ruleList', true);
    },
    /**
    * @description 回执信息-物料核销必选字段
    * 
    */
    checkFieldDisabledHandler(item) {
      return this.fixedFieldValues.includes(item);
    },
    // 更新setting
    columsSelectHandler(val){
      const selectField = this.materialVerifyEliminateColums.filter(x => val.some(v => v === x.field));
      this.updateSetting(selectField, 'materialFields');
    },
    materialFieldsValue(field){
      const selectVal = field?.setting?.materialFields?.map(x => x.field) || [];

      // 无值时候设置默认值 - 必选字段设置
      if(selectVal.length <= 0){
        let newValue = [...this.fixedFieldValues, ...selectVal];
        this.columsSelectHandler(newValue)
        return newValue;
      }

      // 有值时需过滤掉已被删除的关联显示字段
      let selectMateriaValue = selectVal.filter(item => {
        let index = this.materialVerifyEliminateColums.findIndex(
          field => item === field.field
        );
        return index > -1;
      });

      return selectMateriaValue;
    },
    /**
    * @description 二次确认关联选中字段
    */
    reconfirmValue(field){
      const selectVal = field?.setting?.showFields?.map(x => x.field) || []
      return [...new Set([...this.reconfirmFixedFieldValues, ...selectVal])]
    },
    reconfirmColumsHandler(val){
      const selectField = this.reconfirmColums.filter(x => val.some(v => v === x.field))
      this.updateSetting(selectField, 'showFields')
    },
    // 更新配件结算方式
    updateSettleType(value) {
      this.update(value, 'isShowSettleType', true)

      // 设置默认结算方式
      if (!this.field?.setting?.settleTypeOptions?.length) {
        this.update(MATERIAL_VERIFY_ELIMINATE_SETTLE_TYPE_OPTIONS, 'settleTypeOptions', true)
      }
    },
  },
  computed: {
    noDataPlaceHolder() {
      return this.field?.setting?.noDataPlaceHolder || this.noDataPlaceHolderData
    },
    defaultNoDataPlaceHolder() {
      return this.noDataPlaceHolder[locales.locale]
    },
    // 描述信息展示最大数量
    TitleShowMaxLengthMax() {
      return config.FIELD_PLACEHOLER_LENGTH_MAX;
    },
    // 事件表单+回执表单显示多语言配置
    isShow() {
      const isOpenMultiLanguage = getOpenMultiLanguage()
      return isOpenMultiLanguage && isMultiLangModules(this.mode) && this.field.formType !== 'relationCustomer' && this.field.formType !== 'relationProduct';
    },
    isShowSettlementRules() {
      const RootWindow = getRootWindow(window)
      return RootWindow.grayAuth?.settlementRule || false
    }, 
    // 固定选项-物料核销必选字段
    fixedFieldValues(){
      return ['name', 'sn'];
    },
    /**
    * @description 二次确认关联字段
    */
    reconfirmColums() {
      return [
        { label: this.$t('common.form.tip.materialReturn.reconfirmLabel1'), field: 'name', disabled: true, width: 200 },
        { label: this.$t('common.form.tip.materialReturn.reconfirmLabel2'), field: 'serialNumber', disabled: true, width: 200 },
        { label: this.$t('common.form.tip.materialReturn.reconfirmLabel3'), field: 'catalogId', disabled: true, width: 200 },
        { label: this.$t('common.form.tip.materialReturn.reconfirmLabel4'), field: 'customer' },
        // { label: this.$t('common.form.tip.materialReturn.reconfirmLabel5'), field: 'qualityStatus' },
        { label: this.$t('common.form.tip.materialReturn.reconfirmLabel6'), field: 'qualityStartTime', width: 200 },
        { label: this.$t('common.form.tip.materialReturn.reconfirmLabel7'), field: 'qualityEndTime', width: 200 }
      ]
    },
    /**
    * @description 回执信息-物料核销可选字段
    */
    materialVerifyEliminateColums() {
      let { materialFiels } = findComponentUpward(this, 'form-design') || {};


      const materialArr = materialFiels.map((item) => {
        return {
          ...item,
          label: item.displayName,
          field: item.fieldName,
          isSystem: item.isSystem,
          formType: item?.setting?.originalFormType
        }
      })?.filter(item => !['logistics'].includes(item?.setting?.originalFormType)) ?? []

      return materialArr;
    },
    // 是否开启区域定价灰度
    isAreaPrice() {
      const RootWindow = getRootWindow(window)
      return RootWindow.grayAuth?.areaPrice
    },
    // 慈星灰度
    isCiXing() {
      const RootWindow = getRootWindow(window)
      return RootWindow.grayAuth?.CIXING_MATERIAL_SETTLE
    },
    // 是否开启库位灰度
    isLocationGray() {
      const RootWindow = getRootWindow(window)
      return Boolean(RootWindow.grayAuth?.LOCATION_SWITCH)
    },
    // 是否开启个人库灰度
    isTaskCloudWarehousePerson() {
      const RootWindow = getRootWindow(window)
      return RootWindow.grayAuth?.taskCloudWarehousePerson || false
    },
    /*是否开启指定仓库灰度*/
    isAppointWarehouseGray() {
      const RootWindow = getRootWindow(window)
      return Boolean(RootWindow.grayAuth?.APPOINT_WAREHOUSE_SWITCH)
    },
  },
  mounted() {
    if(!this.field?.setting?.showFields?.length){
      this.reconfirmColumsHandler(this.reconfirmFixedFieldValues)
    }

    if(this.isAppointWarehouseGray) {
      // 初始化核销指定仓库信息
      this.searchWarehouse();
      const { taskApproveAppointWarehouseId, taskApproveAppointPositionIds } = this.field?.setting ?? {}

      if(!isEmpty(this.field?.setting?.appointWarehouseId)) {
        this.getWarehousePositionSearch('appointWarehouseId');

        if(!isEmpty(this.field?.setting?.appointPositionId)) {
          this.getLocationPageList('appointPositionId');
        }
      }

      if(!isEmpty(taskApproveAppointWarehouseId)) {
        this.getWarehousePositionSearch('taskApproveAppointWarehouseId');

        if(!isEmpty(taskApproveAppointPositionIds)) {
          this.getLocationPageList('taskApproveAppointPositionIds');
        }
      }
    }
  },
}
</script>
<style lang="scss" scoped>
.form-material-verify-select {
  width: 100%;
}
::v-deep .el-select {
  .el-tag__close {
    display: none;
  }
}
.form-type-text-lang{
  display: flex;
  align-items: center;
  textarea {
    flex: 1;
  }
  p{
    width: 32px;
    height: 32px;
    background: #F5F8FA;
    border-radius: 4px;
    border: 1px solid #CBD6E2;
    text-align: center;
    line-height: 30px;
    margin-left: 8px;
    .iconfont{
      font-size: 18px;
      color: #595959;
    }
  }
}
.warehouse-select {
  margin-left: 24px;
}
.only-tas-occupy {
  display: flex;
  margin-top: 12px;

  .setting-spare-switch {
    margin-left: 8px;
    flex-shrink: 0;
  }
}
</style>