<template>
  <div class="form-cascader">
    <el-cascader-v2
      ref="formCascader"
      v-if="version==='new'"
      :value='modifyValue'
      :options='tree'
      :placeholder="placeholder"
      :show-all-levels="showAllLevels && isShowAllLevels"
      :props='propsV2'
      clearable
      :popper-class='popperClass'
      expand-trigger="hover"
      :disabled="disabled || isDisabled"
      @input="inputForValueWithCascader"
      @change="onChangeHandler"
      filterable
      :collapse-tags="collapseTags"
      :before-filter="beforeFilter"
      @visible-change="visibleChange"
    >
      <template slot-scope="{ node, data }">
        <slot :node="node" :data="data"></slot>
      </template>
  
    </el-cascader-v2>
    <el-cascader-v2
      v-else
      :options="options" 
      :props="props" 
      :value="modifyValue"
      :disabled="disabled || isDisabled"
      :show-all-levels="isShowAllLevels"
      @visible-change="cascaderVisibleHandler"
      @input="inputForValueWithCascader"
      @change="onChangeHandler"
      filterable
      clearable/>
  </div>
</template>

<script>
import form from '../../mixin/form';
/* utils */
import { addClass, removeClass } from '@src/util/dom';
import {Cascader} from 'element-ui';
/* constants */
const CascaderFocusClassName = 'body-for-cascader';

export default {
  name: 'form-cascader',
  mixins: [form],
  props: {
    value: {
      type: Array,
      default: () => []
    },
    version:{
      type:String,
      default:''
    },
    tree:{
      type:Array,
      default:() => []
    },
    propsV2:{
      type:Object,
      default:function(){
        return {
          value:'id',
          label:'name',
          children:'tasks',
          expandTrigger:'hover',
        }
      }
    },
    popperClass:{
      type:String,
      default:""
    },
    showAllLevels:{
      type:Boolean,
      default:false
    },
    collapseTags:{
      type:Boolean,
      default:false
    },
    // 是否支持远程搜索 目前只有产品列表&产品模板的产品类型支持
    remoteSearch: {
      type:Boolean,
      default:false
    },
    isDisabled: {
      type:Boolean,
      default:false
    }
  },
  computed: {
    isShowAllLevels() {
      if (this.isMulti) return this.displayMode == 2;
      return this.displayMode != 1;
    },
    options() {
      // 过滤掉空值，兼容多语言环境下某个语言有数据，其它数据里面都是空值的情况
      return (this.field?.setting?.dataSource || []).filter(d => d?.value)
    },
    modifyValue(){
      const newValue = this.value || []
      return this.isMulti
          ? newValue.map(item => item.split('/'))
          : newValue;
    },
    isMulti(){
      return !!this.field?.setting?.isMulti
    },
    // 选项显示模式
    displayMode() {
      return this.field?.setting?.displayMode;
    },
    // 是否允许选择任意层级
    checkStrictly() {
      return !!this.field?.setting?.checkStrictly;
    },
    props(){
      return {        
          value: 'value',
          label: 'value',
          children: 'children',
          multiple: this.isMulti,
          checkStrictly: this.checkStrictly,
        }
      
    },
  },
  mounted() {
    // 因为beforeFilter 在inputValue为空不触发因此加上此逻辑，inputValue为空需要重载初始化的源数据
    if(this.$refs.formCascader && this.remoteSearch) {
      this.$watch('$refs.formCascader.inputValue', function (newVal, oldVal) {
        if(!newVal) {
          this.$emit('beforeFilter', newVal);
        }
      })
    }
  },
  methods: {
    visibleChange() {
      this.$nextTick(() => {
        const $el = document.querySelectorAll('.el-cascader-panel .el-cascader-node[aria-owns]');
        Array.from($el).map((item) => item.removeAttribute('aria-owns'));
      });
    },
    cascaderVisibleHandler(visible) {
      visible
        ? addClass(document.body, CascaderFocusClassName)
        : removeClass(document.body, CascaderFocusClassName)
    },
    inputForValueWithCascader(value) {
      // this.cascaderBlurHandler();
      if(this.isMulti){
        let newValue = value.map(item=>{
          return item.join('/')
        })
        return this.inputForValue(newValue)
      }
      this.inputForValue(value);
    },
    // 此方法加上目的是在需要远程搜索时 用远程搜索的数据实时替换cascader的options tree
    beforeFilter(value) {
      if(this.remoteSearch) {
        this.$emit('beforeFilter', value);
        return false;
      }
    },
    onChangeHandler(newValue) {
      this.$emit('change', newValue);
    }
  },
  components:{
    'el-cascader-v2':Cascader
  }
}
</script>

<style lang="scss">
.form-cascader .el-cascader{
  width: 100%;

  .el-cascader__search-input {
    margin-left: 10px;
  }
  .el-input__inner{
    &[style="height: 34px;"]{
      height: 32px !important;
    }
  }
}

.body-for-cascader {
  .el-cascader-menu {
    .el-cascader-menu__item {
      display: flex;
      align-items: center;
      line-height: 21px;
        
      & > div {
        height: 21px;
        width: 100%;
        line-height: 21px;
        position: relative;
      }

    }
  }
}
</style>

