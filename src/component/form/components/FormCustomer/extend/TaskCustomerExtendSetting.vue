<template>
  <div class="form-setting-panel task-customer-setting">
    <!-- start 标题 -->
    <div class="form-setting-group form-common-setting">
      <h3 class="form-setting-panel-title">{{ field.displayName }}</h3>
      <div class="form-design-warning">{{$t('common.form.tip.systemTemplateTips')}}</div>
    </div>
    <!-- end 标题 -->

    <!-- start 关联项 -->
    <div class="form-setting-group form-setting-item">
      <h4 class="form-item-title">{{$t('common.base.concatOption')}}</h4>
      <div class="form-item-box">
        <el-checkbox v-model="customerOption.address" @input="updateOptions($event, 'address')">{{$t('common.form.preview.customer.addressLabel')}}</el-checkbox>
      </div>
      <div class="form-item-box">
        <el-checkbox v-model="customerOption.linkman" @input="updateOptions($event, 'linkman')">{{$t('common.form.preview.customer.linkmanLabel')}}</el-checkbox>
      </div>
      <div class="form-item-box" v-show="!isBasicEditionHideProduct">
        <el-checkbox v-model="customerOption.product" @input="updateOptions($event, 'product')">{{$t('common.form.preview.customer.productLabel')}}</el-checkbox>
        <el-checkbox v-model="customerOption.productNotNull" @input="updateOptions($event, 'productNotNull')" :disabled="!customerOption.product">{{$t('common.form.preview.customer.productIsRequire')}}</el-checkbox>
        <!-- 是否对客户可见 -->
        <el-checkbox  v-if="['event', 'task'].includes(field.tableName)" v-model="customerOption.productIsShow" @input="updateOptions($event, 'productIsShow')" :disabled="(!customerOption.product && field.tableName !== 'event') || field.tableName === 'task'">
          {{$t('common.form.tip.customer.tips1')}}
          <el-tooltip :content="customerVisibleTip" placement="top">
            <i class="iconfont icon-question"></i>
          </el-tooltip>
        </el-checkbox>
      </div>
    </div>
    <!-- end 关联项 -->
    
    <!-- start 字段权限 -->
    <template v-if="field.tableName === 'task' || field.tableName === 'event' ">
      <div class="form-setting-group form-setting-item">
        <h4 class="form-item-title">{{$t('common.base.fieldPermissions')}}</h4>
        <form-visible-setting :field="field" @input="update" :label="$t('common.form.tip.customer.tips5')" :tooltipValue="$t('common.form.tip.customer.tips6')" :isCustomerDesensitizationMode="true" v-if="field.tableName === 'task'"></form-visible-setting>
        <form-visible-setting :field="field" @input="update" :label="$t('common.form.tip.customer.tips3')" :tooltipValue="$t('common.form.tip.customer.tips4')" :isDesensitizationMode="true" v-if="field.tableName === 'task'"></form-visible-setting>
        <!-- 移动端列表展示 -->
        <mobile-show-setting  :field="field" :fields="fields" :is-event-mode="isEventMode" @input="update"  v-if="isTaskMode || isOnlyEventMode || isOnlyProductMode"></mobile-show-setting>
        <div class="form-mobile-show">
          <el-checkbox v-model="customerMobileShowOption.product" @input="modifyMobileShow" :disabled="!customerOption.product">{{$t('common.form.preview.customer.productLabel')}}</el-checkbox>
          <el-checkbox v-model="customerMobileShowOption.linkman" @input="modifyMobileShow" :disabled="!customerOption.linkman">{{$t('common.form.preview.customer.linkmanLabel')}}</el-checkbox>
        </div>
      </div>
    </template>
    <!-- end 字段权限 -->

    <!-- start 其他设置 -->
    <template v-if="field.tableName === 'task'">
      <div class="form-setting-group form-setting-item">
        <h4 class="form-item-title">{{$t('common.base.otherSet')}}</h4>
        <div class="form-item-box">
          <el-checkbox v-model="showCustomerProduct" @input="addCustomerProduct">{{$t('task.tip.addCustomerProduct')}}
            <el-tooltip  placement="top" :content="$t('task.tip.addCustomerProductTip')">
              <i class="iconfont icon-question"></i>
            </el-tooltip>
          </el-checkbox>
        </div>
      </div>
    </template>
    <!-- end 其他设置 -->
  </div>
</template>

<script>
import SettingMixin from '@src/component/form/mixin/setting';
import { isBasicEditionHideProduct } from '@shb-lib/version'
import { settingProps } from '@src/component/form/components/props';
const appShowfields = [ 'product','linkman']
/* util*/
import { t } from '@src/locales'

export default {
  name: 'task-customer-extend-setting',
  mixins: [SettingMixin],
  props: settingProps,
  data(){
    return {
      showCustomerProduct: this.field.setting.showCustomerProduct || false
    }
  },
  computed: {
    customerOption() {
      return this.field.setting.customerOption || {}
    },
    customerMobileShowOption() {
      return this.field.setting.customerMobileShowOption || {}
    },
    // 基础版功能是否显示
    isBasicEditionHideProduct() {
      return isBasicEditionHideProduct() 
    },
    // 客户可见性的提示
    customerVisibleTip() {
      let msg = t('common.form.tip.formVisibleCustomerSetting.tips1')
      return {
        task: t('customer.customerVisibleTip'),
        event: t('common.form.tip.formVisibleCustomerSetting.tips2', {msg}),
      }?.[this.field.tableName] ?? ''
    },
  },
  watch: {
    'field.isAppShow':function (newValue,oldValue){
      if(!newValue){
        appShowfields.forEach(f=>{
          this.customerMobileShowOption[f]=false
        })
        this.modifyMobileShow()
      }
    },
  },
  methods: {
    updateForDom(event) {
      let el = event.target;
      let prop = el.dataset.prop;
      let value = el.value;

      this.update(value, prop);
    },
    updateOptions(value, prop, isSetting = false) {

      //关联项没有产品和联系人则取消联系人和产品的可见
      if(!value && appShowfields.includes(prop)){
        this.customerMobileShowOption[prop] = value
      }

      this.$emit('updateOptions', {value, prop, isSetting});
    },
    update(value, prop, isSetting = false) {
      this.$emit('input', {value, prop, isSetting});
    },
    modifyMobileShow() {
      this.update(this.customerMobileShowOption, 'customerMobileShowOption', true);
    },
    addCustomerProduct(value){
      this.update(value, 'showCustomerProduct', true)
    }
  }
}
</script>

<style lang="scss" scoped>
.task-customer-setting {
  .form-design-warning {
    margin-bottom: 24px;
  }
  .form-mobile-show{
    width: 100%;
    padding: 3px 16px 12px 16px;
    background: #FAFAFA;
    margin-bottom: 20px;
    margin-top: 8px;
    .el-checkbox {
      margin-right: 20px;
    }
  }
}
</style>
