<template>
  <div class="form-visible-setting">
    <div class="form-setting-item">
      <el-checkbox v-model="visibleConfig.visible" @change="update" :true-label="1" :false-label="0">
        {{ label }}
        <el-tooltip  placement="top" popper-class="form-msg-setting-tooltip">
          <div slot="content">
            <div class="tooltip" v-html="tooltipValue"></div>
          </div>
          <i class="iconfont icon-question"></i>
        </el-tooltip>
      </el-checkbox>
    </div>
    <div :class="['form-setting-item', {'role-setting': showAllRole}]" v-if="visibleConfig.visible == 1">

      <template v-if="showAllRole">
        <el-cascader
          v-model="roleChoose"
          :options="allRoleList"
          :props="casProps"
          clearable
          @change="updateRole"
        >
        </el-cascader>
      </template>
      <template v-else>
        <!-- start 选择角色 -->
        <biz-role-select
          v-model="visibleConfig.role"
          @change="update"
          :placeholder="$t('common.base.roleChoosePlaceholder')"
          :roles="roleList"
          :getLabel="getRoleNameLabelHandler"
          multiple
          clearable
        >
        </biz-role-select>
        <!-- end 选择角色 -->
      </template>
      
      <!-- <el-select v-model="visibleConfig.role" @change="update" :placeholder="$t('common.base.roleChoosePlaceholder')" multiple clearable>
        <el-option v-for="item in roleList" :key="item.id" :label="getRoleNameLabel(item.name)" :value="item.id"></el-option>
      </el-select> -->
      
    </div>
  </div>
</template>

<script>
/* props */
import { settingProps } from '@src/component/form/components/props';

import { findComponentUpward } from '@src/util/assist';
import { RoleNameLabelEnum } from '@model/enum/LabelEnum'
import i18n from '@src/locales';
import {getRootWindow} from "@src/util/dom";

export default {
  name: 'form-visible-setting',
  props: {
    ...settingProps,
    label: {
      type: String,
      default:i18n.t('common.base.visibility')
    },
    tooltipValue: {
      type: String,
      default:`${i18n.t('common.form.tip.formVisibleSetting.tips1')}<br>${i18n.t('common.form.tip.formVisibleSetting.tips2')}`
    },
    // 是否脱敏模式
    isDesensitizationMode: {
      type: Boolean,
      default: false
    },
    // 客户是否脱敏模式
    isCustomerDesensitizationMode: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      casProps:{
        value: 'id',
        label: 'name',
        children: 'children',
        multiple: true,
        checkStrictly: false, // 支持任意一级选择
      },
      roleChoose: [],
      DEPT_ROLE: 'dept',
      PROVIDE_ROLE: 'provide',
    }
  },
  computed: {
    configKey(){
      
      if(this.isCustomerDesensitizationMode) return 'desensitizationCustomerConfig'

      return this.isDesensitizationMode ? 'desensitizationVisibleConfig' : 'visibleConfig'
    },
    visibleConfig() {
      return this.field.setting[this.configKey] || { visible: 0, role: [], providerRoleList: [] }
    },
    formDesignCom() {
      return findComponentUpward(this, 'form-design');
    },
    /** 
    * @description 获取角色列表
    */
    roleList() {
      return this.formDesignCom?.roleList ?? []
    },
    // 获取服务商角色
    provideRoleLost() {
      return this.formDesignCom?.providerRoles ?? ''
    },
    // 获取model
    currentModel() {
      return this.formDesignCom?.mode ?? ''
    },
    // 如果是工单，需要显示服务商角色，拼接二级数组内部角色&服务商角色
    allRoleList() {
      return [
        {
          id: this.DEPT_ROLE,
          name: '内部角色',
          children: this.roleList
        },
        {
          id: this.PROVIDE_ROLE,
          name: '服务商角色',
          children: this.provideRoleLost
        }
      ]
    },
    // 是否显示所有角色
    showAllRole() {
      return ['task', 'task_receipt'].includes(this.currentModel) && this.isProviderManager;
    },
    // 是否开启服务商灰度
    isProviderManager() {
      const RootWindow = getRootWindow(window)
      return RootWindow.grayAuth?.providerManager ?? false
    },
  },
  methods: {
    // 获取系统预置角色名称的多语言label
    getRoleNameLabel(name) {
      return RoleNameLabelEnum[name] || name
    },
    getRoleNameLabelHandler(option = {}) {
      const name = option.label || option.name
      return this.getRoleNameLabel(name)
    },
    update() {
      this.$emit('input', this.visibleConfig, this.configKey, true);
    },
    updateRole() {
      this.visibleConfig.role = this.roleChoose?.filter(item => item?.[0] === this.DEPT_ROLE)?.map(item => item?.[1])?.filter(Boolean) ?? []
      this.visibleConfig.providerRoleList = this.roleChoose?.filter(item => item?.[0] === this.PROVIDE_ROLE)?.map(item => item?.[1])?.filter(Boolean) ?? []

      this.$emit('input', this.visibleConfig, this.configKey, true);
    },
  },
  watch: {
    visibleConfig: {
      immediate: true,
      deep: true,
      handler(val) {
        // 拼接回显
        const rolesChoose=  val?.role?.map(item => [this.DEPT_ROLE, item])
        const providerRoleChoose=  val?.providerRoleList?.map(item => [this.PROVIDE_ROLE, item]) ?? []
        this.roleChoose = [...rolesChoose, ...providerRoleChoose]
      },
    }
  },
}
</script>

<style lang="scss" scoped>
.form-visible-setting {
  .form-setting-item {
    ::v-deep .el-select,
    .el-select {
      width: 100%;
      padding-left: 24px;
      margin: 8px 0 4px;
    }
  }
  .role-setting {
    padding-left: 24px;
    margin: 8px 0 4px;
    .el-cascader{
      width: 100%;
    }
  }
}
</style>