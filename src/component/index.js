import ElementUI from './element-ui';
import PubBBxVue2 from 'pub-bbx-pc-vue2';

// 通用组件
import BaseOpenData from './common/BaseOpenData';
import BaseModal from './common/BaseModal';
import BasePanel from './common/BasePanel';
import BaseResizable from './common/BaseResizable';
import BaseUpload from './common/BaseUpload';
import BaseTree from './common/BaseTree';
import BaseTreeDept from './common/BaseTreeDept';
import BaseImport from './common/BaseImport';
import BaseExport from './common/BaseExport';
import BaseExportGroup from './common/BaseExportGroup';
import BaseExportGroupProduct from './common/BaseExportGroupProduct';
import BaseFileItem from './common/BaseFileItem';
import BaseTabBar from './common/BaseTabBar';
import BaseTimeline from './common/BaseTimeline';
import BaseTimelineV2 from './common/BaseTimelineV2/index.ts';
import BaseComment from './common/BaseComment';
import BaseSpin from './common/BaseSpin';
import BaseButton from './common/BaseButton';
import BaseSelect from './common/BaseSelect';
import BaseContextMenu from './common/BaseContextMenu'
import BaseTable from './common/BaseTable';
import BaseDistPickerInternationalMulti from './common/BaseDistPickerInternationalMulti';
import { BaseDistPicker, BaseDistPickerInternational, BaseDist } from './common/BaseDistPicker';
import BaseWindow from './common/BaseWindow';
import BaseSteps from './common/BaseSteps';
import BaseStepsLine from './common/BaseStepsLine';
import BaseCascader from './common/BaseCascader';
import BaseSelectionBar from './common/BaseSelectionBar';
import BaseSelectionPanel from './common/BaseSelectionPanel';
import BaseServiceStar from './common/BaseServiceStar';
import BaseCollapse from './common/BaseCollapse';
import BaseCalculationFormula from './common/BaseCalculationFormula'
import BaseFlod from './common/BaseFlod/index.ts';
import BaseErrorPage from './common/BaseErrorPage/index'
import BaseTags from './common/BaseTags/index.ts'
import BaseAlert from './common/BaseAlert/index.ts'
import BaseLanguageDropdown from './common/BaseLanguageDropdown/index.ts'
import BaseSelectLanguage from './common/BaseSelectLanguage/index'
import BaseSelectApp from './common/BaseSelectApp/index'
import BaseSelectCondition from './common/BaseSelectCondition/index'
import ConfigContact from './common/ConfigContact'
import BaseFilterTagList from './common/BaseFilterTagList/index.ts'
import BaseTimezone from './common/BaseTimezone/index.ts'
import BaseTrajectory from './common/BaseTrajectory/index'
import BaseAddressSwitch from './common/BaseAddressSwitch/index'
import BaseNestDraggable from './common/BaseNestDraggable/index'
import BaseOperationBtns from './common/BaseOperationBtns/index'
import BaseSave from './common/BaseSave';

// 可快速调用的组件
import BaseMapDisplay from './common/BaseMapDisplay';
import BaseMapPicker from './common/BaseMapPicker';
import BaseModalVideo from './common/BaseModalVideo';
import BaseModalPDF from './common/BaseModalPDF';
import BaseContact from './common/BaseContact/index.tsx';
import BaseSelectUser from './common/BaseSelectUser/index.tsx';
import BaseUserLabel from './common/BaseUserLabel/index.tsx';

// 业务组件
import BizUserSelect from './business/BizUserSelect';
import BizTeamSelect from './business/BizTeamSelect';
import BizFormRemoteSelect from './business/BizFormRemoteSelect';
import BizSearchForm from './business/BizSearchForm';
import BizSearchPanel from './business/BizSearchPanel';
import BizSelectionPanel from './business/BizSelectionPanel';
import BizSearchCustomerSelect from './business/BizSearchCustomerSelect';
import BizSearchProductSelect from './business/BizSearchProductSelect';
import BizProcess from './business/BizProcess';
import BizProcessTime from './business/BizProcessTime';
import BizSelectColumn from './business/BizSelectColumn';
import BizVersionLimitDialog from '@src/component/business/BizVersionLimitDialog/index.tsx';
import BizSendMessage from './business/BizSendMessage';
import BizSendEmail from './business/BizSendEmail';
import BizBatchRemind from './business/BizBatchRemind';
import BizBatchUpdate from './business/BizBatchUpdate';
import BizBatchEdit from './business/BizBatchEdit';
import BizFilePreview from './business/BizFilePreview';
import BizButtonGroup from './business/BizButtonGroup';
import BizRemoteSelect from '@src/component/business/BizRemoteSelect'
import BizListView from '@src/component/business/BizListView/index.tsx'
import BizTable from '@src/component/business/BizTable/index.tsx'
import BizFormDiaLog from '@src/component/business/BizFormDialog'
import BizCustomerTagSelect from '@src/component/business/BizCustomerTagSelect/index.ts'
import BizLayoutModal from '@src/component/business/BizLayoutModal/index'
import BizProductTypeRelationModifyDialog from '@src/component/business/BizProductTypeRelationModifyDialog/index.tsx'
import BizListCheckboxSelect from '@src/component/business/BizListCheckboxSelect/index.ts'
import BizWorkWxChatToolBarDialog from '@src/component/business/BizWorkWxChatToolBarDialog/index.tsx'
import BizWxChatButton from '@src/component/business/BizWxChatButton/index.ts'
import BizCallIcon from '@src/component/business/BizCallIcon/index.js'
import BizWebSocket from '@src/component/business/BizWebSocket/index.tsx'
import BizCallCenterRecord from '@src/component/business/BizCallCenterRecord/index.js'

import BizChatPanel from '@src/component/business/BizChatPanel/index.js'
import BizChatPanelNew from '@src/component/business/BizChatPanelNew/index.js'
import BizFloatIcon from '@src/component/business/BizFloatIcon/index.js'

import BizAtTextarea from '@src/component/business/BizAtTextarea/index'
import BizComment from '@src/component/business/BizComment/index'
import BizCommentHtml from '@src/component/business/BizComment/commentHtml'
import BizUserCard from '@src/component/business/BizUserCard'
import BizCropperUpload from '@src/component/business/BizCropperUpload/index.js'
import BizLogisticsNo from '@src/component/business/BizLogisticsNo'
import BizRoleSelect from '@src/component/business/BizRoleSelect/entry'
import BizThemeColor from '@src/component/business/BizThemeColor/entry'
import BizFlowProcessChart from '@src/component/business/BizFlowProcessChart/index.js'
import BizIntelligentTags from '@src/component/business/BizIntelligentTags'

import SampleTooltip from './common/SampleTooltip';

import BaseEditor from './common/BaseEditor'

import BaseListForNoData from './common/BaseListForNoData'
import BaseAddOnPagination from './common/BaseAddOnPagination'
import BaseViewRichText from './common/BaseViewRichText'
//甘特图表格
import BaseGante from './common/BaseGante'

// 自定义组件
import GuideCompoment from './guide'

import contextmenu from './contextmenu'

import LenovoSelect from './compomentV2/LenovoSelect'

import BaseTableForUsual from './compomentV2/BaseTable'

import Form from './form';

// publink-ui
// import PublinkUIComponents from '@src/component/publink-ui/index.ts'

// ui组件
import UiWxTag from '@src/component/ui/UiWxTag/index.ts'
import UiEmpty from '@src/component/ui/UiEmpty/index.ts'
import UiTitle from '@src/component/ui/UiTitle/index.ts'
import UiSeparator from '@src/component/ui/UiSeparator/index.ts'
import ComponentNameEnum from '@model/enum/ComponentNameEnum';

const BizWebSocketInstall = (vue) => {
  vue.component(ComponentNameEnum.BizWebSocket, BizWebSocket)
}

const components = [
  ElementUI,
  PubBBxVue2,
  BaseModal,
  BasePanel,
  BaseResizable,
  BaseUpload,
  BaseTree,
  BaseTreeDept,
  BaseContact,
  BaseMapPicker,
  BaseModalVideo,
  BaseModalPDF,
  BaseDistPicker,
  BaseDistPickerInternational,
  BaseDistPickerInternationalMulti,
  BaseDist,
  BaseMapDisplay,
  Form,
  BaseImport,
  BaseExport,
  BaseExportGroup,
  BaseExportGroupProduct,
  BaseFileItem,
  BaseTabBar,
  BaseTimeline,
  BaseTimelineV2,
  BaseComment,
  BaseSpin,
  BaseButton,
  BaseSelect,
  BaseSteps,
  BaseStepsLine,
  BaseSave,
  ...BaseErrorPage,
  BaseSelectionPanel,
  BaseTags,
  BaseAlert,
  BaseContextMenu,
  BaseTrajectory,
  BaseAddressSwitch,
  BaseNestDraggable,
  BaseOperationBtns,
  BaseTable,
  BaseOpenData,
  BaseLanguageDropdown,
  BaseSelectLanguage,
  BaseSelectApp,
  BaseSelectCondition,
  BaseFilterTagList,
  BizUserSelect,
  BizTeamSelect,
  BizFormRemoteSelect,
  BizSearchForm,
  BizSearchPanel,
  BizSelectionPanel,
  BizSearchCustomerSelect,
  BizSearchProductSelect,
  BizProcess,
  BizProcessTime,
  BizSelectColumn,
  BizVersionLimitDialog,
  BizSendMessage,
  BizSendEmail,
  BizBatchRemind,
  BizBatchUpdate,
  BizBatchEdit,
  BizFilePreview,
  BizButtonGroup,
  BizLayoutModal,
  BizRemoteSelect,
  ...BizListView,
  ...BizTable,
  BizFormDiaLog,
  BizCustomerTagSelect,
  BizProductTypeRelationModifyDialog,
  BizListCheckboxSelect,
  BizWorkWxChatToolBarDialog,
  BizWxChatButton,
  BizCallIcon,
  BizCallCenterRecord,
  BizWebSocketInstall,
  BizCropperUpload,
  ConfigContact,
  BizAtTextarea,
  BizComment,
  BizCommentHtml,
  BizUserCard,
  ...BizLogisticsNo,
  BizRoleSelect,
  BizChatPanel,
  BizChatPanelNew,
  BizFloatIcon,
  BizThemeColor,
  BizIntelligentTags,
  
  BaseTimezone,
  BaseWindow,
  SampleTooltip,
  BaseCascader,
  BaseSelectionBar,
  BaseEditor,
  BaseServiceStar,
  BaseCollapse,
  BaseCalculationFormula,
  BaseListForNoData,
  BaseAddOnPagination,
  BaseSelectUser,
  BaseUserLabel,
  BaseGante,
  BaseViewRichText,
  
  GuideCompoment,
  ...BaseFlod,
  LenovoSelect,
  contextmenu,
  BizFlowProcessChart,
  
  // ...PublinkUIComponents,
  
  BaseTableForUsual,

  UiWxTag,
  UiEmpty,
  UiTitle,
  UiSeparator
]

export default {
  install(Vue) {
    components.forEach(component => Vue.use(component))
  }
}
