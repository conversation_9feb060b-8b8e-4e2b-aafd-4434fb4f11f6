<template>
  <div class="resizable-container">
    <slot></slot>
    <!-- 左侧列 -->
    <div
      class="resizable-column left"
      :style="{ width: leftWidth + 'px' }"
      :class="{ 'is-closed': leftClosed }"
    >
      <slot name="left"></slot>
    </div>

    <!-- 左侧拖动条 -->
    <div class="divider" @mousedown="startDrag('left')"></div>

    <!-- 中间列 -->
    <div
      class="resizable-column center"
      :style="{ width: centerWidth + 'px' }"
    >
      <slot name="center"></slot>
    </div>

    <!-- 右侧拖动条 -->
    <div class="divider" @mousedown="startDrag('center')"></div>

    <!-- 右侧列 -->
    <div class="resizable-column right" :class="rightFlex1 ? 'flex-1' : ''">
      <slot name="right"></slot>
    </div>
  </div>
</template>

<script>
// TODO: 适用于左中右布局的页面(参考知识库2.0列表页面)，并且同时可以左右拖动左边和中间。有其它类似功能可以自己进行改造
export default {
  name: 'base-resizable',
  props: {
    leftClosed: { type: Boolean, default: false }, // 左侧收起
    leftInitialWidth: { type: Number, default: 200 },
    centerInitialWidth: { type: Number, default: 200 },
    minLeftWidth: { type: Number, default: 120 },
    maxLeftWidth: { type: Number, default: 500 },
    minCenterWidth: { type: Number, default: 120 },
    maxCenterWidth: { type: Number, default: 500 },
    rightFlex1: { type: Boolean, default: true }, // 默认右边宽度为剩余空间
  },
  data() {
    return {
      leftWidth: this.leftInitialWidth,
      centerWidth: this.centerInitialWidth,
      isDragging: false,
      dragSide: '',
      startX: 0,
      startLeftWidth: 0,
      startCenterWidth: 0,
    };
  },
  watch: {
    leftInitialWidth(newVal) {
      this.leftWidth = newVal;
    },
    centerInitialWidth(newVal) {
      this.centerWidth = newVal;
    },
    leftClosed(newVal) {
      if (newVal) {
        this.leftWidth = 0
      } else {
        this.leftWidth = this.leftInitialWidth
      }
    },
  },
  methods: {
    startDrag(side) {
      this.dragSide = side;
      this.isDragging = true;
      this.startX = event.clientX;
      this.startLeftWidth = this.leftWidth;
      this.startCenterWidth = this.centerWidth;
      document.addEventListener('mousemove', this.onDrag);
      document.addEventListener('mouseup', this.stopDrag);
    },
    onDrag(event) {
      if (!this.isDragging) return;
      const dx = event.clientX - this.startX;

      if (this.dragSide === 'left') {
        const newLeft = this.startLeftWidth + dx;
        this.leftWidth = Math.min(this.maxLeftWidth, Math.max(this.minLeftWidth, newLeft));
        this.$emit('leftWidthChange', this.leftWidth)
      } else if (this.dragSide === 'center') {
        const newCenter = this.startCenterWidth + dx;
        this.centerWidth = Math.min(this.maxCenterWidth, Math.max(this.minCenterWidth, newCenter));
        this.$emit('centerWidthChange', this.centerWidth)
      }
    },
    stopDrag() {
      this.isDragging = false;
      document.removeEventListener('mousemove', this.onDrag);
      document.removeEventListener('mouseup', this.stopDrag);
    },
  },
};
</script>

<style lang="scss" scoped>
.resizable-container {
  display: flex;
  height: 100%;
  width: 100%;
  overflow: hidden;

  .resizable-column {
    height: 100%;
    overflow: auto;
  }

  .resizable-column.is-closed {
    width: 0;
    min-width: auto;
  }

  .flex-1 {
    flex: 1;
  }

  .divider {
    width: 5px;
    cursor: ew-resize;
    background-color: #fff;
  }
}
</style>