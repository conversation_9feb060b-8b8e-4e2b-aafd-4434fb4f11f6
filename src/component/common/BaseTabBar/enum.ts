export enum BaseTabBarUsualEnum {
	/**
	 * @des tabBar所需List参数中每一个参数的唯一值的key
	 */
	TabBarListItemKey = 'tabName',
	/**
	 * @des tabBar所需List参数中每一个参数描述的key
	 */
	TabBarListItemLabel = 'tabLabel',
	/**
	 * @des tabBar所需List参数中每一个参数控制显隐的key
	 */
	TabBarListItemShow = 'tabShow',
	/**
	 * @des tabBar所需List参数中每一个参数代表的类型
	 */
	TabBarListItemType = 'tabType',
	/**
	 * @des 附加组件item标识
	 */
	TabBarCardInfoType = 'is-cardinfo',
	/**
	 * @des 附加组件的标识前缀
	 */
	TabCardInfoSingle = 'add-on-',
	/**
	 * @des 附加组件id
	 */
	TabCardCardId = 'cardId',
	/**
	 * @des 附加组件类型
	 */
	TabCardCardType = 'inputType'
}

export enum BaseTabBarCardTypeEnum {
	Multiple = 'multiple',
}

export enum StorageHttpParamsForModuleType {
	Product = 'product',
	Customer = 'customer',
	Task = 'task',
	TaskPlan = 'taskPlan',
	Event = 'event',
	ProjectType = 'project',
	TaskManage = 'project_task',
	User = 'user'
}

export enum StorageHttpParamsForTerminalType {
	PC = 'pc',
	Mobile = 'mobile'
}