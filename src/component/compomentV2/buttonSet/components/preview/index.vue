<template>
  <div class="button-set-preview-box">
    <div class="button-set-preview-title">{{ $t('setting.buttonSet.text3') }}（{{ $t('setting.buttonSet.text14') }}）</div>
    <div class="button-set-table-preview">
      <div class="button-set-table-preview-btn-box">
        <template v-for="(item, index) in previewValue">
          <el-button class="button-set-table-preview-btn-item" :type="item.viewType" :key="index">
            
          <template v-if="item.render">
            <span v-html="item.render()"></span>
          </template>
          <template v-else>
             {{ item.name }}
          </template>
           
           </el-button>
        </template>
      </div>
      <el-table :data="tableData" border header-row-class-name="common-table-header" style="width: 100%" :class="['common-list-table', 'bg-w', 'bbx-normal-list-box']">
        <el-table-column prop="date" > </el-table-column>
        <el-table-column prop="name" > </el-table-column>
        <el-table-column prop="name" > </el-table-column>
        <el-table-column prop="name" > </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
import { defineComponent, ref, computed } from 'vue';
import { ButtonSetDetailForButtonStyleTypeEnum } from '@src/component/compomentV2/buttonSet/enum'
import { t } from '@src/locales'
export default defineComponent({
  name: 'ButtonSetPreview',
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    previewButtonList: {
      type: Array,
      default: () => [
         {
          name: t('common.event.actionStatus.createInner'),
          viewType: ButtonSetDetailForButtonStyleTypeEnum.Primary,
        },
        {
          name: t('common.base.sendEmail'),
          viewType: ButtonSetDetailForButtonStyleTypeEnum.Gray,
        },
        {
          name: t('customer.sendActivitySurvey'),
          viewType: ButtonSetDetailForButtonStyleTypeEnum.Gray,
        },
        {
          name: t('trainingManagement.trainingCorse.studentAdmin.remind'),
          viewType: ButtonSetDetailForButtonStyleTypeEnum.Gray,
        },
        {
          name: t('common.base.bulkEdit'),
          viewType: ButtonSetDetailForButtonStyleTypeEnum.Gray,
        },
        {
          name: t('smartPlan.title'),
          viewType: ButtonSetDetailForButtonStyleTypeEnum.Gray,
        },
        {
          name: t('common.base.bulkEdit'),
          viewType: ButtonSetDetailForButtonStyleTypeEnum.Gray,
        },
      ],
    }
  },
  setup(props) {
    const tableData = [
      {
        date: '',
        name: '',
        address: '',
      },
      {
        date: '',
        name: '',
        address: '',
      },
      {
        date: '',
        name: '',
        address: '',
      },
    ];
    const previewValue = computed(()=>{
      return [
        ...props.previewButtonList,
        ...props.value
      ]
    })
    return {
      tableData,
      previewValue,
    };
  },
});
</script>
<style lang="scss" scoped>
.button-set-preview-title {
  padding: 12px;
  background-color: #f5f8fa;
}
.button-set-table-preview-btn-box {
  .button-set-table-preview-btn-item {
    margin-bottom: 4px;
    margin-left: 0px;
    margin-right: 12px;
  }
  margin-bottom: 12px;
}
.button-set-table-preview {
  padding: 16px;
}

</style>
<style lang="scss">
  .common-list-table {
    border: none;
    .common-table-header th {
      background: #fafafa;
      color: $text-color-primary;
      font-weight: normal;
    }
  }
</style>
