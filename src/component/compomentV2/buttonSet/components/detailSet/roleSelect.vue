<template>
    <div class="role-select">
        <el-select 
          style="width: 100%;"
          :value="selectUserOptions.map(item => item.value)" 
          multiple 
          :placeholder="$t('common.base.pleaseSelect')" 
          clearable 
          collapse-tags 
          @click.native="openSelectUser" 
          @remove-tag="handleRemoveTag"
          @clear="nowItem.role = []"
          ref="roleSelect"
        >
          <el-option v-for="item in selectUserOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
    </div>
</template>

<script>
import { getSelectUserLabelListForAiLabel } from "@src/component/common/BaseSelectUser";
import { t } from '@src/locales'
export default {
    name: 'RoleSelect',
    props: {
        nowItem: {
            type: [Object, null],
            default: null,
        },
    },
    data() {
        return {
        };
    },
    computed: {
        selectUserOptions() {
            return this.nowItem.role.map(i => {
                return {
                    label: i.displayName || i.name,
                    value: i.id || i.userId,
                };
            });
        }
    },
    methods: {
        handleRemoveTag(val) {
            this.nowItem.role = this.nowItem.role.filter(i => i.id != val && i.userId!= val);
        },
        async openSelectUser() {
            this.$refs.roleSelect.blur();
            try {
                const options = {
                    title: '选择人员或部门',
                    showServiceProvider: true,
                    isTag: true,
                    isShowUser: false,
                    isShowDepartmentUser: true,
                    isCustomDataAuth: true,
                    showDelete: false,
                    isCanChooseIntelligentTags: true,
                    selectedAll: this.nowItem.role || [],
                    fetchLabelList: getSelectUserLabelListForAiLabel,
                    isShowDynamic: false,
                    DynamicTabName: t('component.baseSelectUser.dynamicGain'),
                    // 自定义渲染动态获取内容
                    renderDynamicContent: (h, context) => {
                    const isSelectedByUserId = context.isSelectedByUserId
                    return (
                        <div class="base-select-user-select-panel-container">
                        <div class='base-select-user-select-panel-left'>
                            <el-tree
                            class="tree-user"
                            data={this.treeData}
                            default-expand-all
                            node-key="id"
                            ref="tree"
                            highlight-current
                            current-node-key={1}
                            onNode-click={this.getTaskTypeUser}
                            props={this.defaultProps}>
                            </el-tree>
                        </div>
                        <div class="base-select-user-select-panel-right">
                            <div class="base-select-user-service-provider-right" v-loading={this.userLoading}>
                            <div class="base-select-user-infinite-scroll-user">
                                <div class='base-select-user-user-list'>
                                {!this.userLoading && this.selectUserTypeList.length > 0 && this.selectUserTypeList.map((item) => {
                                    return (
                                    <SelectUser
                                        value={item}
                                        selected={isSelectedByUserId(item.id)}
                                        onLabelChange={(e) => {
                                        this.labelChange(e, context, isSelectedByUserId(item.id))
                                        }}
                                    >
                                    </SelectUser>
                                    )
                                })}
                                {
                                    !this.userLoading && this.selectUserTypeList.length === 0 && (
                                    <div class='task-no-data'>
                                        <NoDataViewNew
                                        notice-msg={t('common.base.tip.noData')}
                                        ></NoDataViewNew>
                                    </div>
                                    )
                                }
                                </div>
                            </div>

                            </div>
                        </div>
                        </div>
                    )
                    }
                }
                const res = await this.$fast.select.multi.all(options)
                this.nowItem.role = res.data.all.map(i => {
                    delete i.tagChildren;
                    delete i.userList;
                    return i;
                }) || [];
            } catch(err) {
                console.error('[handleAddAuthObject error]', err)
            }
        }
    },
};
</script>