<template>
  <div class="detail-set-box">
    <el-form :model="nowItem" :rules="formRulers" ref="formDom" label-position="top">
      <el-form-item :label="$t('common.paas.view.designer.workFlow.buttonName')" prop="name">
        <div class="flex">
          <el-input v-model="nowItem.name" :placeholder="$t('common.placeholder.input2')" :maxlength="nameMaxLength" @input="changeNameLanguage"></el-input>
          <base-select-language 
            :field="nowItem"
            :is-require="false"
            :defaultOption="{
              formType:'text',
            }"
            defaultFormType="text"
            :defaultValue="nowItem.name"
            :defaultValueLanguage="nowItem.nameLanguage || {}"
            @save="save"
          >
          </base-select-language>
        </div>
      </el-form-item>
      <el-form-item :label="$t('setting.buttonSet.text4')" prop="viewType">
        <el-select class="w-100-p" v-model="nowItem.viewType" :placeholder="$t('common.base.pleaseSelect')">
          <template #prefix>
            <div class="type-symbol" :class="getNowTypeColor(nowItem.viewType)"></div>
          </template>
          <el-option v-for="item in typeChooseArr" :key="item.value" :label="item.label" :value="item.value"><div class="flex-x al-center"><div class="type-symbol-inner" :class="getNowTypeColor(item.value)"></div>{{ item.label }}</div></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('common.base.visibility')" prop="role">
        <role-select :nowItem="nowItem"></role-select>
      </el-form-item>
      <el-form-item :label="$t('setting.buttonSet.text5')" prop="position">
        <el-checkbox-group v-model="nowItem.position">
          <div v-for="(item, index) in showPositionChooseArr">
            <el-checkbox :key="index" :disabled="item.disabled" :label="item.value">{{ item.label }}</el-checkbox>
            <div v-if="item.value === 'pcList' && pcListCheckboxTip" class="tip">{{pcListCheckboxTip}}</div>
          </div>
        </el-checkbox-group>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { defineComponent, ref, computed, watch } from 'vue';
import { useStateForButtonSetDetailSetConfig } from '@src/component/compomentV2/buttonSet/common';
import { commonStore } from '@src/store/commonStore';
import roleSelect from './roleSelect.vue';
import i18n, { t } from '@src/locales';
import { ButtonGetTriggerModuleEnum } from 'pub-bbx-global/pageType/dist/enum/ButtonSetEnum';
const nameMaxLength = 10;

export default defineComponent({
  name: 'ButtonSetDetailBaseSet',
  props: {
    nowItem: {
      type: [Object, null],
      default: null,
    },
    mode: {
      type: String,
      default: '',
    },
  },
  components: {
    roleSelect,
  },
  setup(props, { emit }) {
    const formRulers = ref({
      name: [
        { required: true, message: t('common.placeholder.input2'), validator: (rule, value, callback) => {
            if (value.trim() === '') {
              callback(new Error(t('common.placeholder.input2')));
            } else {
              callback();
            }
          }, },
        { max: nameMaxLength, message: t('common.base.tip.maxCharacterTip', { num: nameMaxLength }) },
      ],
      position: [
        {
          required: true,
          message: t('common.placeholder.select'),
          validator: (rule, value, callback) => {
            if (value.length === 0) {
              callback(new Error(t('common.placeholder.select')));
            } else {
              callback();
            }
          },
          trigger: 'change',
        },
      ],
    });
    const formDom = ref(null);

    const roleList = computed(() => {
      let arr = commonStore.getRoleList();
      arr = arr.map(i => {
        return {
          label: i.name,
          value: isNaN(i.id * 1) ? i.id : i.id * 1,
        };
      });
      return arr;
    });

    const pcListCheckboxTip = computed(()=> {
      let tipText = ''
      switch(props.mode) {
        case ButtonGetTriggerModuleEnum.TASK:
          tipText = t('common.page.buttonSet.taskListTip')
          break
        case ButtonGetTriggerModuleEnum.EVENT:
         tipText = t('common.page.buttonSet.eventListTip')
          break
        case ButtonGetTriggerModuleEnum.CONTRACT:
          tipText = t('common.page.buttonSet.contractListTip')
          break
        case ButtonGetTriggerModuleEnum.PROJECT:
          tipText = t('common.page.buttonSet.projectListTip')
          break
      }
      return tipText
    })

    let { typeChooseArr, showPositionChooseArr, typeColorEnum } = useStateForButtonSetDetailSetConfig();
    // if(props.mode == 'TASK') {
    //   showPositionChooseArr = showPositionChooseArr.filter(i => i.value != 'pcList');
    // }
    function getNowTypeColor(type) {
      return typeColorEnum[type];
    }
    async function validate() {
      try {
        let res = await formDom.value.validate();
        return Promise.resolve(res);
      } catch (error) {
        Promise.resolve(false);
      }
    }

    function changeNameLanguage(e) {
      const language = props.nowItem.nameLanguage;
      language[i18n.locale] = e;
      // emit('updateItem', )
    }

    function save(val) {
      props.nowItem.name = val[i18n.locale];
      props.nowItem.nameLanguage = val;
    }
    return {
      formRulers,
      typeChooseArr,
      showPositionChooseArr,
      roleList,
      getNowTypeColor,
      nameMaxLength,
      formDom,
      validate,
      changeNameLanguage,
      save,
      pcListCheckboxTip
    };
  },
});
</script>
<style lang="scss" scoped>
.detail-set-box {
  padding: 16px;
}
</style>
<style lang="scss">
.type-symbol {
  width: 16px;
  height: 16px;
  border-radius: 5px;
  margin-top: 8px;
  margin-left: 5px;
}
.type-symbol-inner{
  width: 16px;
  height: 16px;
  border-radius: 5px;
  margin-right: 8px;
}
.type-primary {
  background-color: $color-primary;
}
.type-gray {
  background-color: #f5f8fa;
  border: 1px solid #CBD6E2;
}
.tip{
  // width: 100px;
  // position: absolute;
  color: #8c8c8c;
  font-size: 14px !important;
  margin-left: 22px;
  margin-top: -10px;
}
</style>
