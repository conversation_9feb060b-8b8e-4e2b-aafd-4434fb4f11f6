<template>
  <div class="button-set-box">
    <div class="left-operation" :class="[isExpand ? 'is-expand' : '']">
      <ButtonSetEdit ref="buttonSetEditDom" class="mar-b-16" :value="btnList" :now-focus-item="nowFocusItemIndex" @update="updateArr" @changeFocus="changeFocus"></ButtonSetEdit>
      <ButtonSetPreview v-bind="$attrs" :value="btnList"></ButtonSetPreview>
      <div class="operation-expand">
        <div :class="[isExpand ? 'rotate-180' : '']" @click="isExpand = !isExpand"><i class="iconfont icon-right1 font-12-i"></i></div>
      </div>
    </div>
    <div class="right-operation">
      <ButtonSetDetailSet ref="buttonSetDetailSetDom" v-show="!isExpand" :now-item="nowFocusItem" @changeTrigger="changeTrigger" @changeLinker="changeLinker" @changeCode="changeCode" @changeBtnListEventType="changeBtnListEventType" :mode="mode" :taskTypeId="taskTypeId"></ButtonSetDetailSet>
    </div>
  </div>
</template>
<script>
import { defineComponent, ref, nextTick } from 'vue';
import ButtonSetEdit from '@src/component/compomentV2/buttonSet/components/edit/index.vue';
import ButtonSetPreview from '@src/component/compomentV2/buttonSet/components/preview/index.vue';
import ButtonSetDetailSet from '@src/component/compomentV2/buttonSet/components/detailSet/index.vue';
import { commonStore } from '@src/store/commonStore';
import { ButtonSetDetailForButtonConcatEventEnum, ButtonSetDetailModuleEnum } from 'pub-bbx-global/pageType/dist/enum/ButtonSetEnum';
import { useStateForButtonSetDetailSetConfig, getHttpParamsForButtonSetTriggerList, buttonPrimaryKey } from 'src/component/compomentV2/buttonSet/common';
import Platform from '@src/util/platform'
import { t } from '@src/locales'
export default defineComponent({
  name: 'ButtonSet',
  props: {
    mode: {
      type: String,
      default: '',
    },
    taskTypeId:{
      type: String,
      dafault: '',
    }
  },
  setup(props) {
    const btnList = ref([]);
    const buttonSetEditDom = ref(null);
    const isExpand = ref(false);
    const nowFocusItem = ref(null);
    const nowFocusItemIndex = ref(null);
    const buttonSetDetailSetDom = ref(null);
    const { validateForButtonSet } = useStateForButtonSetDetailSetConfig();

    

    function updateArr(arr) {
      btnList.value = arr;
    }
    function initArr(arr){
      btnList.value = arr;
      buttonSetEditDom.value.initBtnList(arr)
      initPage()
    }
    async function validateNowFocusItem() {
      let res = await buttonSetDetailSetDom.value.validate();
      if (!res && isExpand.value === true) {
        isExpand.value = false;
      }
      return Promise.resolve(res);
    }
    async function changeFocus(idArr) {
      if (!idArr) {
        nowFocusItem.value = null;
        nowFocusItemIndex.value = [];
        return;
      }
      const endId = idArr[idArr.length - 1];
      const nowFocusItem_ = btnList.value.find(item => item[buttonPrimaryKey] === endId);
      // if (nowFocusItem.value && nowFocusItem.value[buttonPrimaryKey] !== endId) {
      //   let res = await validateNowFocusItem();
      //   if (!res) return;
      // }
      nowFocusItem.value = nowFocusItem_;
      nowFocusItemIndex.value = idArr;
      buttonSetDetailSetDom.value.initComData();
    }

    function changeTrigger(value) {
      let item = nowFocusItem.value.event?.find(i => i.type === ButtonSetDetailForButtonConcatEventEnum.Trigger);
      item.execute = value;
    }

    function changeLinker(value) {
      let item = nowFocusItem.value.event?.find(i => i.type === ButtonSetDetailForButtonConcatEventEnum.Linker);
      item.execute = value;
    }

    function changeCode(value) {
      let item = nowFocusItem.value.event?.find(i => i.type === ButtonSetDetailForButtonConcatEventEnum.Code);
      item.codeContent = value;
    }

    function changeBtnListEventType(value) {
      nowFocusItem.value.event[0] && (nowFocusItem.value.event[0].type = value)
    }

    async function getValue() {
      if (btnList.value.length === 0) {
        return Promise.resolve([]);
      }
      // if (nowFocusItem.value) {
      //   if (!(await validateNowFocusItem())) {
      //     return Promise.resolve(false);
      //   }
      // }
      const allValidateRes = await validateForButtonSet(btnList.value);

      if (allValidateRes.pass === false) {
        const { pathId, errorModule, type,name } = allValidateRes;
        // 校验不通过
        if (pathId) {
          await changeFocus(pathId);
          nextTick(() => {
            buttonSetDetailSetDom.value.showAndValidate(errorModule);
          });
          
        }
        if(type == 'code'){
          Platform.alert(t('system.tips.text2', {name }))
        }else {
          Platform.alert(t('system.tips.text1'))
        }
        return Promise.resolve(false);
      }
      console.log(allValidateRes, '<---allValidateRes');

      return Promise.resolve(btnList.value);
    }

    function initPage(){
      changeFocus(null)
    }

    // 初始化角色列表
    commonStore.initRoleList();

    const params = getHttpParamsForButtonSetTriggerList(props.mode,props.taskTypeId);
    // 初始化触发器列表
    commonStore.initButtonSetChooseUpdateTriggerList(params);
    return {
      btnList,
      updateArr,
      buttonSetEditDom,
      isExpand,
      nowFocusItem,
      changeFocus,
      buttonSetDetailSetDom,
      nowFocusItemIndex,
      changeTrigger,
      changeLinker,
      changeCode,
      changeBtnListEventType,
      getValue,
      initArr,
    };
  },
  components: {
    ButtonSetEdit,
    ButtonSetPreview,
    ButtonSetDetailSet,
  },
});
</script>
<style lang="scss" scoped>
.button-set-box {
  display: flex;
  padding: 12px;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  .left-operation {
    background-color: #fff;
    padding: 12px;
    height: 100%;
    position: relative;
    width: calc(100% - 336px);
    &.is-expand {
      width: 100%;
    }
    .operation-expand {
      position: absolute;
      left: 100%;
      top: calc(50% - 26px);
      width: 10px;
      height: 52px;
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
      background-color: #fff;
      z-index: 99;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }
  }
  .rotate-180 {
    transform: rotateZ(180deg);
  }
  .right-operation {
    margin-left: 16px;
    background-color: #fff;
    height: 100%;
    position: relative;
  }
}
</style>
