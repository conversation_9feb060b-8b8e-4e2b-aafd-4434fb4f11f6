<template>
  <div id="normal-preview-world">
    <transition name="modal-fade">
      <div v-show="show" class="base-modal-mask" @click.self="close()">
        <div class="base-preview-world">
          <!-- 弹窗头部——Start -->
          <div class="base-modal-header base-preview-world-header">
            <div class="title">
              <h3 v-if="title">{{ title }}</h3>
              <div class="info">{{ fileSize }}</div>
            </div>
            <button @click="toDownload" type="button" v-if="isHaveFileDownloadAuth">
              <i class="iconfont iconColor icon-cloud-download"></i>
            </button>
            <button
                type="button"
                class="base-modal-header-close"
                @click="close()"
            >
              <i class="iconfont iconColor icon-fe-close"></i>
            </button>
          </div>
          <!-- 弹窗头部——End -->

          <!-- 弹窗内容——Start -->
          <div class="base-modal-content base-preview-world-content">
            <slot></slot>
          </div>
          <!-- 弹窗内容——End -->
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
/* vue*/
import { defineComponent, reactive, toRefs, computed} from 'vue'
export default defineComponent({
  name: "PreviewFile",
  props: {
    info: {
      type: Object,
      default: (() => {})
    },
    title: {
      type: String,
      default: ''
    },
  },
  setup(props, { emit }) {

    const state = reactive({
      show: false,
      isFullscreen: false,
    })

    const fileSize = computed(() => props.info?.fileSize ?? '')
    // 是否有下载文件的权限
    const isHaveFileDownloadAuth = computed(() => props.info?.fileDownloadAuth ?? true)


    // 下载
    function toDownload() {
      emit('toDownload')
    }


    function open() {
      state.show = true
    }

    function close() {
      state.show = false
      emit('close')
    }

    return {
      ...toRefs(state),
      fileSize,
      open,
      close,
      toDownload,
      isHaveFileDownloadAuth,
    }
  }
})
</script>

<style lang="scss" scoped>
.base-modal-mask {
  z-index: 3000;
}
.base-preview-world {
  width: 100%;
  height: 100%;
  background: transparent;
  display: flex;
  justify-content: center;
  flex-direction: column;

  &-header {
    flex-shrink: 0;
    background-color: #32373a;
    font-size: 16px;
    color: #ffffff;
    font-weight: normal;
    display: flex;
    align-items: center;

    .info {
      font-size: 14px;
      color: #8f8f8f;
      display: inline-flex;
      align-items: flex-end;
      padding-left: 15px;
    }

    .title {
      flex: 1 0 auto;
      display: flex;
      justify-content: center;

      h3 {
        display: inline-block;
        flex: 0 1 auto;
        margin: 0;
        height: 24px;
        line-height: 24px;
        font-weight: 600;
        font-size: 18px;
        max-width: 300px;
        @include text-ellipsis();
      }
    }

    button {
      height: 24px;
      line-height: 24px;
      width: 24px;
      color: #fff;
    }
  }

  &-content {
    height: calc(100% - 44px);

    .docx-wrapper {
      padding-top: 0 !important;
    }
  }

}
</style>