<!--  -->
<template>
  <div class="exmaination-record-box">
    <el-table border header-row-class-name="common-list-table-header__v2" v-if="tableHeight" border ref="elTableDom" v-loading="recordLoading" :data="page.list" style="width: 100%" :max-height="tableHeight" stripe>
      <el-table-column show-overflow-tooltip prop="finishTime" v-for="(field, index) in columns" :key="index" :label="field.displayName" :fixed="field.fix" :type="field.columnType" :width="field.width || null">
        <template #default="scope">
          <slot :name="`fieldName_${field.fieldName}`" :data="scope.row" :scope="scope">
            <template v-if="field.formType == 'operation'">
              <template v-if="tableOperationArr(scope.row, field).length <= 1">
                <div v-for="(item, index) in tableOperationArr(scope.row, field)" :key="index">
                  <div type="text" :class="item.class" :disabled="item.disabled" @click="item.clickFnc && item.clickFnc(scope.row, field, scope)">{{ item.label }}</div>
                </div>
              </template>
              <el-popover v-else placement="bottom" :visible="tableOperationShow[scope.row.id]" trigger="hover" popper-style="padding:0;width:auto;">
                <template #reference>
                  <span class="just-cur-point c-primary">{{ $t('common.base.more') }}<i class="iconfont bbx-icon-arrow-down" style="margin-left: 5px"></i></span>
                </template>
                <div v-for="(item, index) in tableOperationArr(scope.row, field)" :key="index">
                  <span class="overHideCon-1 just-cur-point hover-bg-primary pad-8" @click="item.clickFnc && item.clickFnc(scope.row, field, scope), delete tableOperationShow[scope.row.id]">{{ item.label }}</span>
                </div>
              </el-popover>
            </template>
            <template v-else-if="field.formType == 'user'">
              <BaseUserTag v-if="field.userInfoForUserTag" :props-data="field.userInfoForUserTag(scope.row)"></BaseUserTag>
            </template>
            <template v-else>
              {{ formateFormValueForTable(field, scope.row) }}
            </template>
          </slot>
        </template>
      </el-table-column>
      <template #empty>
        <NoDataViewNew v-show="!recordLoading" />
      </template>
    </el-table>
    <div class="flex-x al-center pad-t-16" v-if="needPage">
      <div class="flex-1">
        {{ $t('common.base.table.totalPiece', { count: page.total }) }}
      </div>
      <el-pagination v-model:page-size="page.pageSize" background :page-sizes="[10, 20, 30, 40, 50, 100]" :page-size="page.pageSize" :current-page="page.pageNum" layout="sizes,prev, pager,next, jumper" :total="page.total" @current-change="handleCurrentChange" @size-change="handleSizeChange" />
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, nextTick, watch } from 'vue';
import { formatDate, createPage } from 'pub-bbx-utils';
import { t } from '@src/locales';
import NoDataViewNew from '@src/component/common/NoDataViewNew';
import BaseUserTag from '@src/component/compomentV2/baseUserTag/index.vue';
import platform from '@src/platform';
import { formateFormValueForTable } from '@src/util/form';
export default defineComponent({
  name: 'JustCommonTable',
  components: {
    NoDataViewNew,
    BaseUserTag,
  },
  props: {
    tableHeight: {
      type: Number,
      default: 300,
    },
    searchParams: {
      type: [Object, null],
      default: null,
    },
    type: {
      type: String,
      default: '',
    },
    columns: {
      type: Array,
      default: () => [],
    },
    fetchData: {
      type: Function,
      default: null,
    },
    needPage: {
      type: Boolean,
      default: true,
    },
    diyData: {
      type: Boolean,
      default: false,
    },
    diyListData: {
      type: Array,
      default: () => [],
    },
  },
  setup(props, { emit }) {
    const page = ref(createPage());
    const recordLoading = ref(false);
    const elTableDom = ref(null);
    function tableOperationArr(row, field) {
      return field.operationArr(row) || [];
    }
    const tableOperationShow = ref({});

    function setPageListData(val) {
      page.value.list = val;
      page.value.total = val.length || 0
    }
    watch(
      () => props.diyListData,
      () => {
        page.value = createPage();
        setPageListData(props.diyListData);
      },
      {
        immediate:true
      }
    );
    function getRecordList() {
      nextTick(() => {
        elTableDom.value.$refs.bodyWrapper.scrollTo(0, 0);
      });
      recordLoading.value = true;
      let fnc = props.fetchData
        ? props.fetchData
        : () => {
            return Promise.resolve({
              code: 0,
              data: {
                list: [1],
                total: 1,
              },
            });
          };
      fnc({
        ...props.searchParams,
        pageNum: page.value.pageNum,
        pageSize: page.value.pageSize,
      })
        .then(res => {
          if (res.code === 0 || res.status === 0) {
            setPageListData(res.data?.list || []);
            page.value.total = res.data.total;
          } else {
            throw res;
          }
        })
        .catch(err => {
          platform.notification({
            title: t('common.base.toast'),
            message: err?.message || err,
            type: 'error',
          });
        })
        .finally(() => {
          setTimeout(() => {
            recordLoading.value = false;
          }, 500);
        });
    }

    function handleCurrentChange(pageNum) {
      page.value.pageNum = pageNum;
      getRecordList();
    }
    function handleSizeChange(pageSize) {
      page.value.pageSize = pageSize;
      page.value.pageNum = 1;
      getRecordList();
    }

    function initPage() {
      if (!props.diyData) {
        page.value = createPage();
        getRecordList();
      }
    }
    initPage();
    return {
      formatDate,
      recordLoading,
      page,
      handleSizeChange,
      handleCurrentChange,
      elTableDom,
      initPage,
      tableOperationArr,
      tableOperationShow,
      formateFormValueForTable,
      setPageListData,
    };
  },
});
</script>
<style lang="scss" scoped></style>
