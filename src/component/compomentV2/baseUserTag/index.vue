<!--  -->
<template>
  <span :class="propsData.canClick ? 'c-primary cur-point' : ''">
    <open-data v-if="isOpenData" :openid="propsData.id" />
    <span v-else>{{ propsData.name }}</span>
  </span>
</template>

<script>
import { defineComponent } from 'vue';
import { isOpenData } from '@src/util/platform';
export default defineComponent({
  name: 'BaseUserTag',
  props: {
    propsData: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    return {
      isOpenData,
    };
  },
});
</script>
<style lang="scss" scoped></style>
