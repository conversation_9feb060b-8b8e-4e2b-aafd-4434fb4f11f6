<template>
    <base-modal class="setting-modal" :title="$t('common.wiki.selectWikiDoc')" width="510px" :show.sync="dialogVisible">
        <div class="wiki_setting_box">
            <el-input
                class="search_input"
                v-model="keywords"
                :placeholder="$t('common.placeholder.inputTitleSearch')"
                @change="searchRole"
                @clear="clear"
                clearable
            >
                <i slot="prefix" class="el-input__icon el-icon-search"></i>
            </el-input>
            <div class="wiki_box">
                <div class="wiki_num">
                    <p>
                        <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">
                            {{ checkAllWord }}
                        </el-checkbox>
                    </p>
                    <p><i class="iconfont icon-info"></i>{{ $t('component.wikiDialog.showTip') }}</p>
                </div>
                <div v-loading="loading" class="tree-wrap mar-t-5">
					<el-tree
						ref="documentTree"
						:data="documentTree"
						:props="documentTreeProps"
						:default-checked-keys="wikiIds"
						:disabled="loading"
						node-key="id"
						show-checkbox
                        :default-expand-all="isExpand"
                        :filter-node-method="filterNode"
                        @check-change="handleCheckChange">
                        <span class="custom-tree-node" slot-scope="{ node, data }">
                            <i v-if="(typeof data.id) !== 'string'" class="iconfont icon-fenlei1"></i>
                            <span>{{ node.label }}</span>
                        </span>
                    </el-tree>
				</div>
            </div>
        </div>
        <div slot="footer" class="dialog-footer">
            <el-button @click="closeDialog">{{$t('common.base.cancel')}}</el-button>
            <el-button type="primary" @click="change">{{$t('common.base.confirm')}}</el-button>
        </div>
    </base-modal>
</template>

<script>
import { t } from '@src/locales'

export default {
    name: 'wiki-dialog',
    props: {
        documentTree: {
            type: Array,
            default: () => []
        },
        wikiIds: {
            type: Array,
            default: () => []
        },
        allIds: {
            type: Array,
            default: () => []
        },
        num: {
            type: String,
            default: ''
        },
        isExpand: {
            type: Boolean,
            default: false
        }
    },
    data () {
		return {
			dialogVisible: false,
            keywords: '',
			documentTreeProps: {
				label: 'name',
				children: 'children',
			},
			loading: false,
            doList: [],
            checkAllWord: t('common.wiki.allDoc'),
            checkAll: false,
            isIndeterminate: true,
            isChange: false,
            openOrNot: false,
		}
	},
    watch: {
        wikiIds: {
            deep: true,
            handler() {
                this.doList = this.wikiIds.filter(item=> typeof item === 'string')
                this.checkAllWord = this.word()
            },
        },
        allIds: {
            deep: true,
            handler() {
                const isEmptyWikiIds = this.wikiIds.length === 0 
                const isIdsLengthEqual = this.wikiIds.length == this.allIds.length
                this.isIndeterminate = isIdsLengthEqual || !isEmptyWikiIds
                this.checkAll = !isIdsLengthEqual || isEmptyWikiIds ? false : true
            }
        },
        keywords(val) {
            this.$refs.documentTree.filter(val);
        }
    },
    methods: {
        filterNode(value, data) {
            if (!value) return true;
            return data.name.includes(value);
        },
        openDialog() {
            this.dialogVisible = true;
        },
        closeDialog() {
            this.dialogVisible = false;
            this.keywords = ''
        },
        searchRole() {
            // this.$emit('search', this.keywords);
            // this.isExpand()
            // this.openOrNot = false;
            // setTimeout(() => {
            //     this.isExpand()
            // }, 1000);
        },
        clear() {
            // this.$emit('clear');
        },
        handleCheckChange(data, checked, indeterminate) {
            this.isChange = true
			const ids = this.$refs.documentTree.getCheckedKeys();
            this.doList = []
            this.doList = ids.filter(item=> typeof item === 'string')
            this.checkAllWord = this.word()
            this.isIndeterminate = (ids.length == this.allIds.length) || ids.length === 0 ? false : true;
            this.checkAll = (ids.length !== this.allIds.length) || ids.length === 0 ? false : true;
        },
        change() {
			const ids = this.$refs.documentTree.getCheckedKeys();
            // const wikiIds = this.mapTree(this.documentTree, new Set(ids));
            // console.log(ids)
            this.$emit('update', ids);
        },
        // 全选
        handleCheckAllChange(val) {
            this.checkAll = val;
            if (val) {
                this.isIndeterminate = false;
                this.doList = this.allIds.filter(item=> typeof item === 'string')
                this.checkAllWord = this.word()
                this.$refs.documentTree.setCheckedKeys(this.allIds);
            } else {
                this.doList = []
                this.checkAllWord = this.word()
                this.$refs.documentTree.setCheckedKeys([]);
            }
        },
        word() {
            if (this.isChange) {
                return this.doList.length ? t('common.wiki.selectedDoc', { length:  this.doList.length}) : t('common.wiki.allDoc')
            } else {
                if (this.num == t('common.wiki.selectWikiDoc')) {
                    return t('common.wiki.allDoc')
                } else {
                    return this.wikiIds.length ? this.num : t('common.wiki.allDoc')
                }
            }
        },
        /**
		 * 处理选中id
		 */
		mapTree(list) {
			let arr = [];
			for (let v of list) {
				if (o.has(v.id)) {
					arr.push(v.id);
				} else if (v.children && v.children.length) {
					arr.push(...this.mapTree(v.children, o));
				}
			}
			return arr;
		},
    }
}
</script>


<style lang="scss" scoped>
.setting-modal{
	.wiki_setting_box{
        width: 500px;
        max-height: 530px;
        margin: 0 auto;
        .search_input{
            width: 450px;
            margin: 24px 0 10px 25px;
        }
        .wiki_box{
            margin: 0 24px 24px;
            border-radius: 4px;
            border: 1px solid #E8E8E8;
            .wiki_num{
                display: flex;
                justify-content: space-between;
                height: 40px;
                line-height: 40px;
                background: #FAFAFA;
                border-radius: 4px 4px 0px 0px;
                padding: 0 16px;
                color: #595959;
                p{
                    font-size: 14px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #595959;
                }
                .iconfont{
                    margin-right: 5px;
                }
            }
        }
    }
}
</style>