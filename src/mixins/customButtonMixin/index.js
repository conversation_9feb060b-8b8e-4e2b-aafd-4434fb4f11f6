import { havePageButtonSetGray } from '@src/util/grayInfo'
import { pageButtonClick, getPageButtonListForView } from '@src/component/compomentV2/buttonSet/common'
import { ButtonGetTriggerModuleEnum, ButtonSetDetailForShowPositionEnum, ButtonSetDetailForButtonConcatEventEnum } from 'pub-bbx-global/pageType/dist/enum/ButtonSetEnum'
export default {
    name: 'custom-button-mixin',
    data() {
      return {
        customButtonList: [],
        pageButtonLoading: false
      };
    },
    computed: {
        isOpenCustomButtonsGray() {
            return havePageButtonSetGray()
        },
    },
    methods: {
        customButtonPending(item) {
            return [ButtonSetDetailForButtonConcatEventEnum.Trigger, ButtonSetDetailForButtonConcatEventEnum.Code].includes(item.event[0] && item.event[0].type) && this.pageButtonLoading
        },
        handleCustomButtonClick(...args) {
            //@ts-ignore
            return pageButtonClick(...args)
        },
        handleFetchForButtonList(...args) {
            //@ts-ignore
            if(this.isOpenCustomButtonsGray) {
                //@ts-ignore
                getPageButtonListForView(...args)
            }
        },
        handleFetchForButtonListForListForTemplateId(moduleName, templateId ='') {
            if(!templateId) return
            this.handleFetchForButtonList(moduleName, ButtonSetDetailForShowPositionEnum.PcList, {}, (list)=> this.customButtonList = list, templateId)
        },
        handleFetchForButtonListForDetailForTemplateId(moduleName, templateId ='') {
            if(!templateId) return
            this.handleFetchForButtonList(moduleName, ButtonSetDetailForShowPositionEnum.PcDetail, {}, (list)=> this.customButtonList = list, templateId)
        },
        handlePageButtonClick(item, multipleSelection = [], fields = []) {
            pageButtonClick(item, multipleSelection, {
              fields,
              multipleSelection: multipleSelection,
              js_vm: this,
            }, ()=>{
              this.pageButtonLoading = true
            }, null, ()=>{
              this.pageButtonLoading = false
            })
          },
    }
  }