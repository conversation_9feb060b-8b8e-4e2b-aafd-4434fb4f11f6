import Platform from '@src/platform';
import http from '@src/util/http';
import { t } from '@src/locales'
import StorageModuleEnum from '@model/enum/StorageModuleEnum'
import { storageGet, storageSet } from '@src/util/storage.ts'
import { MessageBox } from 'element-ui';
/* util */
import { exportAlert } from '@src/util/alert';

const MAX_COUNT = 999999999;

export default {
  name: 'base-export-mixin',
  data() {
    return {

    };
  },
  methods: {
    // 表单形式导出  修改
    formExport(params) {
      let form = document.createElement('form');
      this.$refs.bridge.appendChild(form);
    
      for (let prop in params) {
        let input = document.createElement('input');
        input.name = prop;
        input.value = params[prop];
        form.appendChild(input);
      }
    
      form.method = this.method;
      form.action = this.action;
      form.submit();
    
      this.visible = false;
      this.pending = false;
    
      setTimeout(() => {
        this.$refs.bridge.removeChild(form);
      }, 150);
    },
    // ajax形式导出
    async ajaxExport(params) {
      let ajax = null;
      
      // 是否是立即下载
      if (this.isDownloadNow) {
        if (this.downloadUrl) {
          this.billExport(params);
          return;
        }
        ajax = http
          .axios(this.method, this.action, params, false, {
            responseType: 'blob'
          })
          .then(blob => {
            let link = document.createElement('a');
            let url = URL.createObjectURL(blob);
            link.download = this.fileName;
            link.href = url;
            this.$refs.bridge.appendChild(link);
            link.click();
    
            this.visible = false;
            this.pending = false;
            setTimeout(() => {
              URL.revokeObjectURL(url);
              this.$refs.bridge.removeChild(link);
            }, 150);
          })
          .catch(err => console.error(err));
      } else {
        ajax = http
          .axios(this.method, this.action, params, this.emulateJSON)
          .then(res => {
            this.visible = false;
            this.pending = false;
            
            this.exportAlert(res, params);
    
            // if (res.status == 0) {
            //   window.parent.showExportList();
            //   window.parent.exportPopoverToggle(true);
            // }
          })
          .catch(err => console.error(err));
      }
    
      return ajax;
    },
    async billExport(params) {
      let ajax = null;
      let token = await http.post(this.downloadUrl, params, false);
      let url = `${this.action}?token=${token.data}`;
      ajax = http
        .axios(this.method, url, {}, false, { responseType: 'blob' })
        .then(blob => {
          let link = document.createElement('a');
          let url = URL.createObjectURL(blob);
          link.download = this.fileName;
          link.href = url;
          this.$refs.bridge.appendChild(link);
          link.click();
    
          this.visible = false;
          this.pending = false;
          setTimeout(() => {
            URL.revokeObjectURL(url);
            this.$refs.bridge.removeChild(link);
          }, 150);
        })
        .catch(err => console.error(err));
    
      return ajax;
    },
    
    async exportData() {
      if (this.isCheckedEmpty()) {
        return Platform.alert(t('common.base.exportModal.atLeastOne'));
      }
    
      this.pending = true;
    
      // 如果提供验证函数，则进行验证
      if (typeof this.validate == 'function') {
        let validateRes = await this.validate(this.ids, MAX_COUNT);
        if (validateRes) {
          this.pending = false;
          this.visible = false;
          return Platform.alert(validateRes);
        }
      }
      
      let params = this.buildParamsFunc();
      
      this.setExportStorageData(
        this.storageKey, 
        {
          checkedMap: this.checkedMap,
          checkedGroup: this.checkedGroupArr,
          isCheckedAll: this.isCheckedAll,
          tooltip: this.tooltip,
          isMergeCells: this.isMergeCells,
        }
      )
      
      return navigator.userAgent.indexOf('Trident') >= 0
        ? this.formExport(params)
        : this.ajaxExport(params);
    },
    exportAlert(result, params) {
      let isAlertFunction = typeof this.alert == 'function';
      let isSuccess = result.status == 0;

      // if(isSuccess && isAlertFunction) {
      //   this.alert(result, params);
      // } else {
      //   Platform.alert(result.message)
      // }
      
      // 看过全局文件，alert传参均为普通alert，故直接修改这边，业务传参alert无效
      if(isSuccess) {
        
        const message = result.message
        
        exportAlert(message);
        
      } else {
        Platform.alert(result.message)
      }
    },

    async setExportStorageData(key, data) {
      if(!key || key === 'empty') return
      storageSet(key, data, StorageModuleEnum.Export)
    },

    async getExportStorageData(key) {
      const defaultObj = { checkedMap: null, checkedGroup: null, isCheckedAll: true, tooltip: true, isMergeCells: true };
      return await storageGet(key, defaultObj, StorageModuleEnum.Export)
    }
  }
}