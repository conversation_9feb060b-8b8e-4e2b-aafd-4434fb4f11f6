import Vue from '@src/common/entry'
import http from '@src/util/http'
import { useNewVue } from '@hooks/useNewVue'
import { CreateElement } from 'vue'
import store from '@src/modules/setting/authorizationView/store'
import EntryView from '@src/modules/setting/authorizationView/view/index'
const { createVue, extendVue } = useNewVue()

Vue.prototype.$http = http
Vue.prototype.$eventBus = createVue()

let app  = {
    store,
    render(h: CreateElement) {
        return h(EntryView)
    }
}
export default app
