.authorization-view-block__content-view{
    height: 100%;
    box-sizing: border-box;
    // padding: 12px;
    display: flex;
    flex-direction: column;
    .authorization-view-block__content-view-top{
        background: #F5F7F7;
        padding: 12px;
        border-radius: 4px;
        margin: 12px 12px 0 12px;
        .authorization-view-block__content-top-operator{
            display: flex;
            justify-content: space-between;
            align-items: center;
            // margin-top: 12px;
            .main-title{
                font-size: 16px;
                color:  $text-color-main;
            }
        }
        .des {
            font-size: 14px;
            color: $text-color-secondary;
            margin-bottom: 0;
            margin-top: 4px;
        }
    }
    &-main {
        flex: 1;
        min-height: 0;
        display: flex;
        flex-direction: column;
        .authorization-view-block__content-view-tabs{
            height: 50px;
            margin: 12px 12px 0 12px;
            box-sizing: border-box;
            .el-tabs__nav{
                min-width: 192px;
                width: 192px;
            }
            .el-tabs__item{
                padding: 0 10px;
                // margin: 0 12px;
            }
        }
    }



    .authorization-view-block__distribute-account{
        height: 100%;
        overflow: hidden;
        margin: 0 24px 12px 12px;
        &-operator{
            display: flex; 
            gap: 0 8px;
            .search{
                width: 240px;
            }
        }
        .bbx-normal-list-box {
            margin-top: 12px;
            // border-top: 1px solid #EBEEF5;
            // border-left: 1px solid #EBEEF5;;
            .el-table__fixed-right-patch{
                border-bottom: none;
            }
            th.el-table__cell.gutter:last-of-type{
                border-bottom: none !important;
            }
        }
    }


    .authorization-view-block__permissions{
        height: 100%;
        overflow: auto;
        &-list{
            padding: 0;
            margin: 0;
            &-item{
                padding-bottom: 24px;
                .authorization-view-block__permissions-module{
                    font-size: 14px;
                    line-height: 22px;
                    color: $text-color-main;
                    margin-bottom: 10px;
                }
                .authorization-view-block__permission-split{
                    margin: 6px 0;
                    color: $text-color-regular;
                    line-height: 22px;
                    font-size: 14px;
                }
                .authorization-view-block__permissions-conditions{
                    background: #F5F7F7;
                    padding: 12px;
                    display: flex;
                    flex-direction: column;
                    gap: 8px 0;
                    border-radius: 4px;
                    &-item{
                        color: $text-color-regular;
                    }
                }
                &:not(:last-of-type) {
                    border-bottom: 1px solid #E4E7ED;
                    margin-bottom: 12px;
                }
            }
        }
    }

    .authorization-view-block__tab-content{
        flex: 1;
        .el-scrollbar__wrap{
            overflow: auto;
        }
    }
    .setting-authorization-view__form-checkbox{
        margin: 0 12px 12px 12px;
    }

}