.setting-authorization-view{
    height: 100%;
    width: 100%;
    &__header{
        background: #fff;
        padding: 8px 16px;
        height: 60px;
        .title{
            font-size: 16px;
            font-weight: 500;
            line-height: 24px;
            color:$text-color-primary;
            margin-bottom: 0;
        }
        .sub-title{
            color: $text-color-regular;
            font-size: 12px;
            line-height: 20px;
        }
    }
    &__content{
        display: grid;
        grid-template-columns: 280px 1fr;
        padding: 12px;
        height: calc(100% - 60px);
        gap: 0 12px;
        &-left{
            width: 280px;
            height: 100%;
            background: #fff;
            border-radius: 4px;
            padding: 0;
            position: relative;
            overflow: hidden;
            .setting-authorization-view__list{
                height: calc(100% - 42px);    
                .el-scrollbar__wrap{
                    overflow: auto;
                }
                .el-scrollbar__view{
                    display: flex;
                    flex-direction: column;
                    list-style: none;
                    padding: 12px;
                    gap: 0 4px;
                    margin-bottom: 0;
                }
                &-item{
                    width: calc(100% - 10px);
                    color:$text-color-primary;
                    font-size: 14px;
                    line-height: 22px;
                    padding: 5px 12px;
                    border-radius: 4px;
                    &:not(:last-child) {
                        margin-bottom: 4px;
                    }
                    cursor: pointer;
                    &.active{
                        background: $color-primary-hover;
                        color: $color-primary;
                    }
                    &:hover{
                       background: $color-primary-hover; 
                       color: $color-primary;
                    }
                }
            }
            .add-btn{
                width: calc(100% - 24px);
                position: absolute;
                left: 50%;
                bottom: 12px;
                transform: translateX(-50%);
            }
        }
        &-main{
            flex: 1;
            background: #fff;
            border-radius: 4px;
            // padding: 12px;
            overflow: hidden;
        }
    }
}