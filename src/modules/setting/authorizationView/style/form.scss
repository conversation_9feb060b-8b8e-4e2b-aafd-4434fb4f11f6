.setting-authorization-view__form{
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    &-content{
        height: calc(100% - 56px);
        // padding: 12px 24px 0 12px;
        .el-scrollbar__wrap{
            height: 100%;
            overflow: auto;
        }

    }
    &-view{
        padding: 12px 24px 0 12px;
    }
    &-main{
        background: #F5F7F7;
        border-radius: 4px;
        padding: 0;
        .form-builder{
            width: 100%;
            margin: 0;
            background-color: transparent;
        }
    }
    &-reference{
        display: flex;
        align-items: center;
        margin:24px 12px 16px 12px;
        &-name{
            margin-right: 8px;
        }
        .icon-jieshishuoming1{
            margin-left: 8px;
            font-size: 16px;
        }
    }

    &-footer{
        height: 56px;
        display: flex;
        align-items: center;
        padding-left: 12px;
        border-top: 1px solid #E4E7ED;
    }
    .form-item-auth__obj{
        overflow: hidden;
        &.err{
            .auth-obj-input{
                border-color: #f56c6c !important;
            }
        }
        .auth-obj-input{
            display: flex;
            height: auto !important;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            padding: 4px 15px 4px 10px;
        }
        .icon{
            display: inline-block;
            transform: translateX(4px);
            &.clear{
                display: none;
            }
        }
        .placeholder-text {
            line-height: 22px;
        }
        .rangeList{
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            max-height: 68px;
            overflow-y: auto;
            gap: 4px;
            line-height: 0;
            .el-tag{
                height: 100%;
            }
        }
    }
}