
import store from '@src/modules/setting/authorizationView/store'
import { computed, ComputedRef, ref } from 'vue'
import { SettingViewListItem, RootVuexPropertyEnum, ViewStatusEnum, RootViewStatusParamsType } from '@src/modules/setting/authorizationView/model'
import { useUrlParams } from '@hooks/useWindow'
import { isEmpty } from 'lodash'

export const useViewList = ()=> {
    const loading = ref(false)
    const { setViewStatusParams } = useViewState()
    const urlParams = useUrlParams<Record<string, any>>()
    const viewList = computed<SettingViewListItem[] >(()=> store.state.viewList.filter((item: SettingViewListItem)=> !item.defView))

    const fetchViewList = async (init = false)=> {
        try {
            loading.value = true
            await store.dispatch(RootVuexPropertyEnum.FetchViewList)

            if(!isEmpty(viewList.value) && viewList.value.length > 0 && init) {
                setViewStatusParams({
                    status: ViewStatusEnum.Detail,
                    params: {
                        id: urlParams.value?.id ? urlParams.value.id : viewList.value?.[0]?.id
                    }
                })
            }
           
            loading.value = false
        } catch(err) {
            loading.value = false
        }
    }
    return {
        viewList,
        loading,
        fetchViewList
    }
}

export function useViewState() {

    const viewStatusParams: ComputedRef<RootViewStatusParamsType> = computed(()=> store.state.viewStatusParams)

    const viewStatus = computed(()=> store.state.viewStatusParams.status)
    const viewParams = computed(()=> store.state.viewStatusParams.params)

    const setViewStatusParams = (value: RootViewStatusParamsType)=> {
        store.commit(`${RootVuexPropertyEnum.SetAuthorizationViewStatus}`, value)
    }

    const goViewBack = ()=> {
      store.commit(`${RootVuexPropertyEnum.PopAuthorizationViewStatus}`)
  }

    return {
        viewStatusParams,
        viewStatus,
        viewParams,
        setViewStatusParams,
        goViewBack
    }
}
