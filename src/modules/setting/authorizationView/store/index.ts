import { fetchGetSettingViewList } from '@src/api/WorkbenchApi';
import Vue from '@src/common/entry'
import { Message } from 'element-ui';
import { nth } from 'lodash';
import Vuex from 'vuex'
import { RootVuexPropertyEnum, ViewStatusEnum, SettingViewListItem, RootViewStatusParamsType } from '../model';


Vue.use(Vuex);

const store = new Vuex.Store<any>({
    state: {
        viewStatusParams: {
            viewStatusStack: [],
            status: ViewStatusEnum.Placeholder,
            param: {}
        },
        viewList: []
    },
    mutations: {
        [RootVuexPropertyEnum.SetAuthorizationViewStatus]: (state, value: RootViewStatusParamsType)=> {
            const viewStatusStack = (state.viewStatusParams.viewStatusStack || []).slice(0)
            viewStatusStack.push({ ...state.viewStatusParams, viewStatusStack: []})

            if(viewStatusStack.length > 5) {
                viewStatusStack.shift()
            }
            
            state.viewStatusParams = { ...value, viewStatusStack }
        },
        [RootVuexPropertyEnum.PopAuthorizationViewStatus]: (state)=> {
            const lastViewStack = nth(state.viewStatusParams?.viewStatusStack || [], -1) as unknown as RootViewStatusParamsType
            const viewStatusStack = (state.viewStatusParams.viewStatusStack || []).slice(0)
            viewStatusStack.pop()
            
            if(lastViewStack) {
                state.viewStatusParams = { ...lastViewStack, viewStatusStack }
            }
        }
    },
    actions: {
        [RootVuexPropertyEnum.FetchViewList]: async ({ state })=> {
            const result = await fetchGetSettingViewList()
            const { data, success, message = '' } = result

            if(!success) return Message.warning(message)

            state.viewList = data

            return result
        }
    }

})

export default store