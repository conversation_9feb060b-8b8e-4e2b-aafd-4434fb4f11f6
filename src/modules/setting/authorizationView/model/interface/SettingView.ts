import { AuthTreeNode } from "@src/modules/account/role/component/AuthTree/model";

export interface SettingViewListItem {
    id: number
    viewName: string
    defView: boolean
}

export interface SettingViewDetail{
    id: number
    viewName: string
    viewDesc: string
    cardList: AuthTreeNode[]
    authObj: SettingViewDetailAuthObjItem[]
}

export interface SettingViewDetailAuthObjItem{
    id: number
    objId: string
    objType: string
    objName: string,
    staffId: string | null,
    viewId: string | null
}