import { t } from '@src/locales';

export const getFormFields = (isDisabled: boolean) => {
    return [
        {
            fieldName: 'viewName',
            displayName: t('common.label.designation'),
            formType: 'text',
            placeHolder: t('common.placeholder.inputName'),
            isSystem: 1,
            isNull: 0,
            width: 210,
            maxlength: 20,
            disabled: isDisabled
          },
          {
            fieldName: 'viewDesc',
            formType: 'textarea',
            displayName: t('common.label.describe'),
            placeHolder: t('common.label.inputdescribe'),
            isSystem: 1,
            isNull: 1,
            width: 210,
            maxlength: 500
          },
          {
            fieldName: 'authObj',
            displayName: t('common.auth.authorization'),
            formType: 'select',
            placeHolder: t('common.label.select'),
            isSystem: 1,
            isNull: 0,
            width: 210,
            maxlength: 20,
            disabled: isDisabled
          },
    ]
}
    