import { computed, defineComponent, onBeforeMount } from "vue";
import AuthorizationViewHeader from '@src/modules/setting/authorizationView/view/components/AuthorizationViewHeader'
import AuthorizationViewLeftPanel from '@src/modules/setting/authorizationView/view/components/AuthorizationViewLeftPanel'
import AuthorizationViewContent from '@src/modules/setting/authorizationView/view/components/AuthorizationViewContent'
import AuthorizationViewPlaceholder from '@src/modules/setting/authorizationView/view/components/AuthorizationViewPlaceholder'
import { useViewList, useViewState } from "@src/modules/setting/authorizationView/hooks/useStore";
import { ViewStatusEnum } from "@src/modules/setting/authorizationView/model";
import { isEmpty } from "lodash";
import '@src/modules/setting/authorizationView/style/index.scss'

export default defineComponent({
    setup() {
        const { viewList, fetchViewList, loading} = useViewList()
        const { viewStatus, setViewStatusParams, goViewBack} = useViewState()
        
        const isPlaceholder = computed(()=> isEmpty(viewList.value) && viewStatus.value === ViewStatusEnum.Placeholder )

        onBeforeMount(()=> {
            fetchViewList(true)
        })

        return ()=> !loading.value ?    
                <div class="setting-authorization-view">
                    <AuthorizationViewHeader></AuthorizationViewHeader>
                    { isPlaceholder.value 
                        ?
                        <AuthorizationViewPlaceholder /> 
                        : 
                    <div class="setting-authorization-view__content">
                        <AuthorizationViewLeftPanel></AuthorizationViewLeftPanel>
                        <AuthorizationViewContent></AuthorizationViewContent>
                    </div> 
                    }
                </div>
                : 
                null
        }
})