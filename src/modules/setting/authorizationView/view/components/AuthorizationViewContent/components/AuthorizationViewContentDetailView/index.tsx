import { SettingViewDetail, ViewStatusEnum } from "@src/modules/setting/authorizationView/model";
import FunctionModules from '@src/modules/setting/authorizationView/view/components/AuthorizationViewContent/components/AuthorizationViewContentDetailView/FunctionModules'
import DistributeAccount from '@src/modules/setting/authorizationView/view/components/AuthorizationViewContent/components/AuthorizationViewContentDetailView/DistributeAccount'
import { computed, defineComponent, h, PropType, ref, toRefs } from "vue";
import '@src/modules/setting/authorizationView/style/view.scss'
import { isEmpty } from "lodash";
import { useViewState, useViewList } from "@src/modules/setting/authorizationView/hooks/useStore";
import { fetchRemoveSettingView } from "@src/api/WorkbenchApi";
import { Message, MessageBox } from "element-ui";
import { t } from '@src/locales';


export enum TabTypeEnum {
    DistributeAccount = 'DistributeAccount',
    AuthorizationFunctionModules = 'FunctionModules'
}


export default defineComponent({
    components: {
        FunctionModules,
        DistributeAccount
    },
    props: {
        settingViewDetail: {
            type: Object as PropType<SettingViewDetail>,
            default: ()=> ({})
        }
    },
    emits: ['refresh'],
    setup(props, { emit }) {
        const { settingViewDetail } = toRefs(props)
        const { setViewStatusParams } = useViewState()
        const { viewList, fetchViewList } = useViewList()
        // 当前选中的tab
        const currentTab = ref(TabTypeEnum.DistributeAccount);

        // tab列表
        const tabs = computed(()=> [
        {
            name: t('common.base.assignAccount'),
            value: TabTypeEnum.DistributeAccount
        },{
            name: t('common.base.authorizationModule'),
            value: TabTypeEnum.AuthorizationFunctionModules
        }])

        const handleEdit = ()=> {
            setViewStatusParams({
                status: ViewStatusEnum.Edit,
                params: { id: settingViewDetail.value.id }
            })
        }

        const handleCopy = ()=> {
            setViewStatusParams({
                status: ViewStatusEnum.Copy,
                params: { id: settingViewDetail.value.id }
            })
        }

        const handleDelete = async ()=> {
            try{
                await MessageBox.confirm(t('common.auth.suretoRemove'), t('common.base.toast'))
                const { success } = await fetchRemoveSettingView(settingViewDetail.value.id) 
                if(success) {
                    Message.success(t('common.base.deleteSuccess'))
                    await fetchViewList()

                    const params = {
                        status: ViewStatusEnum.Detail,
                        params: {}
                    }
                    if(viewList.value.length > 0) {
                        params.params = { id: viewList.value?.[0]?.id }
                    } else {
                        params.status = ViewStatusEnum.Create
                        params.params = { id: -1 }
                    }
                    setViewStatusParams(params)
                    // eventBus.$emit('changeActiveGroup', '')
                }
            } catch(e) {
                console.error('handleDelete error', e)
            }
        }

        const handleTabClick = (v: any)=> {
            currentTab.value = v.name
        }

        const handleRefresh = ()=> {
            emit('refresh')
        }


        return ()=> (
            <div 
                class="authorization-view-block__content-view"
             >
                <div class="authorization-view-block__content-view-top">
                    <div class="authorization-view-block__content-top-operator">
                        <h1 class="main-title">{settingViewDetail.value.viewName}</h1>
                        <div class="operator-list">
                            { <el-button type="plain-third" onClick={handleEdit}>{t('common.base.edit')}</el-button> }
                            { <el-button type="plain-third" onClick={handleCopy}>{t('common.base.copy')}</el-button> }
                            { <el-button type="plain-third" onClick={handleDelete}>{t('common.base.delete')}</el-button> }
                        </div>
                    </div>
                    <p class="des">{settingViewDetail.value.viewDesc}</p>
                </div>
                <div class="authorization-view-block__content-view-main">
                    <el-tabs value={currentTab.value} class="authorization-view-block__content-view-tabs" stretch { ...{on: { 'tab-click': handleTabClick }} }>
                        {tabs.value.map(item=> {
                            return <el-tab-pane label={item.name} name={item.value} key={item.name}></el-tab-pane>
                        })}
                    </el-tabs>
                    { h(currentTab.value, { key: currentTab.value, props: { profile: settingViewDetail.value  }, on: { refresh: handleRefresh } }) }
                </div>
            </div>
        )
    }
}) as any