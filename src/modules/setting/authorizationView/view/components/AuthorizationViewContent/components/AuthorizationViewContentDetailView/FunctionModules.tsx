import { SettingViewDetail } from "@src/modules/setting/authorizationView/model";
import { computed, defineComponent, PropType, toRefs } from "vue";
import { AuthListItem } from '@src/modules/account/role/component';
import { t } from '@src/locales';

export default defineComponent({
    props: {
        profile: {
            type: Object as PropType<SettingViewDetail>,
            default: ()=> ({})
        },
    },
    setup(props) {
        const { profile } = toRefs(props)
        const menusAuthSettingList = computed(()=> profile.value?.cardList || [])
        return ()=> (
            <el-scrollbar class="authorization-view-block__tab-content">
                <div class="setting-authorization-view__form-checkbox">
                    {menusAuthSettingList.value.map(item => {
                        return <AuthListItem data={item} disabled={true} checkedBoxMainTitle={t('common.auth.systemItems')}/>
                    })}
                </div>
            </el-scrollbar>
        )
    }
})