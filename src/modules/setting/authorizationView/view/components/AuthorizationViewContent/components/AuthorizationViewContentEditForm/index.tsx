import { getFormFields } from "@src/modules/setting/authorizationView/model/FormFields";
import { AuthListItem } from '@src/modules/account/role/component'
import { initialize } from '@src/component/form/util/index'
import { computed, defineComponent, PropType, ref, toRefs, watchEffect, getCurrentInstance, nextTick, onMounted } from "vue";
import { t } from "@src/locales";
import '@src/modules/setting/authorizationView/style/form.scss'
import { useViewList, useViewState } from "@src/modules/setting/authorizationView/hooks/useStore";
import { buildAuthority, changeFrontSettingMenuItemValueToServeValue, covertSettingViewAuthorityListToServe } from "@src/modules/setting/authorizationView/utils/utils";
import { fetchGetSettingMenusViewDetail, fetchSaveSettingViewList } from "@src/api/WorkbenchApi";
import { SettingViewDetail, ViewStatusEnum } from "@src/modules/setting/authorizationView/model";
import { AuthTreeNode } from "@src/modules/account/role/component/AuthTree/model";
import { getBaseSelectUserResultBySelectUserItems, getSelectUserLabelListForAiLabel } from "@src/component/common/BaseSelectUser";
import { isOpenData } from '@src/util/platform'
import { cloneDeep, isEmpty } from "lodash";
import { Message } from "element-ui";

export default defineComponent({
    props: {
        settingViewDetail: {
            type: Object as PropType<SettingViewDetail>,
            default: ()=> ({})
        }
    },
    setup(props, { emit }) {
        const { settingViewDetail } = toRefs(props)
        //@ts-ignore
        const { proxy: currentCtx  } = getCurrentInstance()
        const formRef = ref(null)
        const { viewList, fetchViewList } = useViewList();
        const { viewStatus, goViewBack, setViewStatusParams } = useViewState()
        const selectExampleView = ref()
        const referenceMenusAuthSettingList = ref<AuthTreeNode[]>([])
        const authObjRef = ref(null)
        const loading = ref(false)
        const form = ref({})
        const formFields = computed(()=> getFormFields(false))

        const menusAuthSettingList = computed(()=> isEmpty(referenceMenusAuthSettingList.value) ? settingViewDetail.value?.cardList || [] : referenceMenusAuthSettingList.value)


        const handleSubmit = async ()=> {
            loading.value = true
            try {
                const params: Record<string, any> = {
                    ...form.value,
                    cardIds: changeFrontSettingMenuItemValueToServeValue(menusAuthSettingList.value)
                }

                // @ts-ignore
                const validateRes = await formRef.value?.validate(false);
                if (!validateRes) return;

                if(isEmpty(params.cardIds)) {
                    return Message.warning(t('common.base.settingoption'))
                }

                if(Reflect.has(settingViewDetail.value, 'id') && viewStatus.value !== ViewStatusEnum.Copy &&  settingViewDetail.value.id != -1) {
                    params.viewId = settingViewDetail.value.id
                }
                if(params?.authObj) {
                    params.authObj = covertSettingViewAuthorityListToServe(cloneDeep(getBaseSelectUserResultBySelectUserItems(params.authObj)))
                }

                const { success, data = '', message = '' } = await fetchSaveSettingViewList(params)
                if(success) {
                    Message.success(t('common.base.saveSuccess'))
                    await fetchViewList()
                    if(data) {
                        setViewStatusParams({
                            status: ViewStatusEnum.Detail,
                            params: { id: data }
                        })
                    }
                } else {
                    Message.warning(message)
                }
            } catch(err) {
                console.error('[ handleSubmit error ]', err)
            } finally{
                loading.value = false
            }
        }

        const handleUpdateFormValue = ({ field, newValue }: any)=> {
            //@ts-ignore
            form.value[field.fieldName] = newValue
        }

        const handleExampleViewSelectChange = async (v: number| string)=> {
            const selectId = v !== '' ? v : -1
            selectExampleView.value = selectId !== -1 ? selectId : ''

            const { data, success, message = '' } = await fetchGetSettingMenusViewDetail(selectId as unknown as number)
            if(!success) {
                return Message.warning(message)
            }

            referenceMenusAuthSettingList.value = AuthTreeNode.transformByMenusGroup(data.cardList || [], false)
        }

        const handleCancel = ()=> {
            goViewBack()
        }
        const handleAddAuthObject = async ()=> {

            try {
                const options = {
                    //@ts-ignore
                    selectedAll: form.value['authObj'] || [],
                    title: t('common.auth.selectpersonnel'),
                    showServiceProvider: false,//是否显示服务商
                    selectedDepartments:  [],
                    selectedUsers: [],
                    selectedRoles: [],
                    isTag: true,
                    isShowUser: true,
                    isShowDepartmentUser: false,
                    isCustomDataAuth: false,
                    showDelete: false,
                    isCanChooseIntelligentTags: true,
                    fetchLabelList: getSelectUserLabelListForAiLabel
                }
                //@ts-ignore
                const res = await currentCtx.$fast.select.multi.all(options)
                //@ts-ignore
                form.value['authObj'] = res.data?.all || []

                nextTick(()=> {
                    document.querySelector('.form-item-auth__obj')?.dispatchEvent(new CustomEvent('form.validate', {bubbles: true}));
                });

            } catch(err) {

            }

        }

        const handleClear = (value: any)=> {
            //@ts-ignore
            const index =  form.value['authObj'].findIndex((item: { userId: any; id: any; })=> item.userId || item.id == value.userId || value.id )
            if(index > -1) {
                 //@ts-ignore
                form.value['authObj'].splice(index, 1)
            }
        }

        const renderFormBuilderScopedSlot = ()=> {
            return {
                'authObj': ({ field, ...arg }: any)=> (
                    <form-item label={field.displayName} class="form-item-auth__obj" ref={authObjRef} isNotNull>
                        {/* @ts-ignore */}
                        <div class={["el-input__inner auth-obj-input",isEmpty(form.value['authObj']) ? null : 'value' ]} onClick={handleAddAuthObject}>
                            <div class="rangeList">
                                {/* @ts-ignore */}
                                { isEmpty(form.value['authObj']) 
                                            ? 
                                        <span class="placeholder-text">{ t('common.base.pleaseSelect') }</span>
                                        :
                                        //@ts-ignore
                                        Array.isArray(form.value['authObj']) && form.value['authObj'].map(item=> {
                                            return  (
                                            <el-tag
                                                key={item.userId || item.id}
                                                size="mini"
                                                closable
                                                disable-transitions
                                                type="info"
                                                onClose={()=> handleClear(item)}

                                            >
                                               { isOpenData && item.staffId ? 
                                                   <open-data type="userName" openid={item.staffId} />
                                                :
                                                 item.displayName || item.name
                                               }
                                            </el-tag>
                                            )
                                        })
                                               
                                    }
                            </div>
                            <i class="el-icon-arrow-down icon normal" /> 
                        </div>
                    </form-item>
                )
            }
        }
        const addAuthObjectFieldEvent = ()=> {
            // 触发注册事件，用于注册字段到外层 FormItem 组件，和 FormBuilder 组件
            const field = formFields.value[formFields.value.length -1]
            //@ts-ignore
            let params = {value: ()=> form.value['authObj'], fieldName: field.fieldName, field: field};
            let event = new CustomEvent('form.add.field', {detail: params, bubbles: true})
            //@ts-ignore
            nextTick(()=> document.querySelector('.form-item-auth__obj')?.dispatchEvent(event));
        }

        watchEffect(()=> {
            const { viewDesc = '', viewName = '', authObj = [] } = settingViewDetail.value || {}
            form.value = initialize(formFields.value , { viewDesc, viewName, authObj: buildAuthority(authObj) })
        })

        onMounted(()=> {
            addAuthObjectFieldEvent()
        })

        return ()=> (
            <div class="setting-authorization-view__form">
                <el-scrollbar class="setting-authorization-view__form-content">
                    <div class="setting-authorization-view__form-view">
                        <div class="setting-authorization-view__form-main">
                            <form-builder ref={formRef} value={form.value} fields={formFields.value} onUpdate={handleUpdateFormValue} scopedSlots={renderFormBuilderScopedSlot()}></form-builder>
                        </div>

                        <div class="setting-authorization-view__form-reference">
                            <span class="setting-authorization-view__form-reference-name">
                                {t('common.auth.authorizationgroup')}
                            </span>
                            
                            <el-select value={selectExampleView.value} placeholder={t('common.base.pleaseSelect')} clearable onChange={handleExampleViewSelectChange}>
                            {
                                viewList.value.map(viewItem => { 
                                    return (
                                        <el-option
                                            key={viewItem.id}
                                            label={viewItem.viewName || ''}
                                            value={viewItem.id}
                                        />
                                    )
                                })
                            }
                            </el-select>
                            
                            <el-tooltip content={t('common.auth.authorizationcontent')} placement="top">
                                <i class="iconfont icon-jieshishuoming1"></i>
                            </el-tooltip>
                        </div>

                        <div class="setting-authorization-view__form-checkbox">
                            {menusAuthSettingList.value.map(item => {
                                return <AuthListItem data={item} disabled={false} checkedBoxMainTitle={t('common.auth.systemItems')}/>
                            })}
                        </div>
                    </div>
                </el-scrollbar>
                <div class="setting-authorization-view__form-footer">
                    <el-button type="primary" disabled={loading.value} onClick={handleSubmit}>
                        {t('common.base.save')}
                    </el-button>
                    <el-button type="ghost" disabled={loading.value} onClick={handleCancel}>
                        {t('common.base.cancel')}
                    </el-button>
                </div>
            </div>
        )
    }
}) as any