
import { computed, defineComponent, getCurrentInstance, PropType, ref, toRefs } from "vue";
/* api */
import { getSelectUserLabelListForAiLabel } from "@src/component/common/BaseSelectUser";
/* utils */
import { Message, MessageBox } from "element-ui";
import { t } from '@src/locales';
/* component */
import BaseListForNoData from '@src/component/common/BaseListForNoData';
import { SettingViewDetail, SettingViewDetailAuthObjItem } from "@src/modules/setting/authorizationView/model";
import { fetchRemoveSettingViewAuthority, fetchSaveSettingViewAuthority } from "@src/api/WorkbenchApi";
import { covertSettingViewAuthorityListToServe } from "@src/modules/setting/authorizationView/utils/utils";

export default defineComponent({
    props: {
        profile: {
            type: Object as PropType<SettingViewDetail>,
            default: ()=> ({})
        },
        canEdit: {
            type: Boolean,
            default: ()=> true
        }
    },
    setup(props, { emit }) {
        const { profile, canEdit } = toRefs(props)
        const { proxy: currentCtx } = getCurrentInstance() || {};
        // 搜索的值
        const searchValue = ref('')
        // 当前授权对对象的列表
        const viewList = computed(()=> profile.value.authObj)

        // 搜索结果值
        const currentDataList = computed(()=> {
            if(searchValue.value) return viewList.value.filter((item: any)=> item.objName.includes(searchValue.value))
            return viewList.value
        })

        // table的插槽渲染
        const renderTableScopedSlots = ()=> {
            return {
                default: (scope: {row: any})=> {
                    return scope.row['objName']
                }
            }
        }

        /**
         * @des 相关搜索的input事件
         * @param {any} v:string
         * @returns {any}
         */
        const handleInput = (v: string)=> {
            searchValue.value = v
        }

        /**
         * @des 相关添加授权对象的方法
         * @returns {any}
         */
        const handleAddAuthObject = async ()=> {
            try {
                const options = {
                    title: t('common.auth.selectpersonnel'),
                    showServiceProvider: false,//是否显示服务商
                    selectedDepartments:  [],
                    selectedUsers: [],
                    selectedRoles: [],
                    isTag: true,
                    isShowUser: true,
                    isShowDepartmentUser: false,
                    selectedAll: [],
                    isCustomDataAuth: false,
                    showDelete: false,
                    isCanChooseIntelligentTags: true,
                    fetchLabelList: getSelectUserLabelListForAiLabel
                }
                //@ts-ignore
                const res = await currentCtx.$fast.select.multi.all(options)

                const { success, message = '' } = await fetchSaveSettingViewAuthority({ viewId: profile.value.id, authorityList: covertSettingViewAuthorityListToServe(res.data)})
                success ? emit('refresh') : Message.warning(message)
            } catch(err) {
                console.error('[handleAddAuthObject error]', err)
            }
        }

        /**
         * @des 相关移除事件
         * @param {any} {type
         * @param {any} id}:CreteCustomDataAuthListItemDetailViewListItem
         * @returns {any}
         */
        const handleRemove = async ({ objType, id }: SettingViewDetailAuthObjItem)=> {
            try {
                await MessageBox.confirm(t('common.auth.suretoRemove'), t('common.base.toast'))
                const { success, message = '' } = await fetchRemoveSettingViewAuthority(id)
                success ? emit('refresh') : Message.warning(message)
            } catch(err) {
                console.error('[handleRemove error]', err)
            }
        }

        return () => (
            <div class="authorization-view-block__distribute-account">
                <div class="authorization-view-block__distribute-account-operator">
                    <el-input class="search" value={searchValue.value} placeholder={t('common.base.search')}  onInput={handleInput}>
                        <i class="el-icon-search el-input__icon" slot="prefix"></i>
                    </el-input>
                    { canEdit.value ?  <el-button type="plain-third" onClick={handleAddAuthObject}>{t('common.auth.addauthorization')}</el-button> : null }
                </div>
                <el-table
                    class="bbx-normal-list-box"
                    headerRowClassName='base-table-header-v3'
                    rowClassName='base-table-row-v3'
                    data={currentDataList.value}
                    headerCellStyle={{ background: '#fafafa', color: '#262626' }}
                    row-key={(row: SettingViewDetailAuthObjItem)=> row.id + row.objType}
                    height="calc(100% - 50px)"
                    border
                    v-table-style>
                    <template slot="empty">
                        {/* @ts-ignore */}
                        <BaseListForNoData></BaseListForNoData>
                    </template>
                    <el-table-column
                        prop={'showName'}
                        label={t('common.auth.authorization')}
                        scopedSlots={renderTableScopedSlots()}
                        show-overflow-tooltip
                        resizable>
                    </el-table-column>
                    { canEdit.value ?   
                        <el-table-column
                            fixed="right"
                            label={t('common.base.table.col.operator')}
                            width="140"
                            scopedSlots={{
                                default: (scope: { row: SettingViewDetailAuthObjItem }) => {
                                    return (
                                        <el-button type="text" size="small" onClick={()=> handleRemove(scope.row)}>{t('common.base.remove')}</el-button> 
                                    )
                                },
                            }}>
                        </el-table-column> : null 
                    }
                </el-table>
            </div>
        )
    }
})