import { computed, defineComponent, PropType, ref, watchEffect } from "vue";
import AuthorizationViewContentEditForm from '@src/modules/setting/authorizationView/view/components/AuthorizationViewContent/components/AuthorizationViewContentEditForm'
import AuthorizationViewContentDetailView from '@src/modules/setting/authorizationView/view/components/AuthorizationViewContent/components/AuthorizationViewContentDetailView'
import { fetchGetSettingMenusViewDetail } from "@src/api/WorkbenchApi";
import { AuthTreeNode } from "@src/modules/account/role/component/AuthTree/model";
import { SettingViewDetail, SettingViewListItem, ViewStatusEnum } from "@src/modules/setting/authorizationView/model";
import { Message } from "element-ui";
import { useViewState } from "../../../hooks/useStore";

export default defineComponent({
    name: 'SettingAuthorizationViewLeftContent',
    props: { },
    setup() {
        const settingViewDetail = ref<SettingViewDetail>()
        const loading = ref(false)

        const { viewStatus, viewParams, setViewStatusParams, goViewBack} = useViewState()

        const isDetailView = computed(()=> viewStatus.value === ViewStatusEnum.Detail)
        const isFormView = computed(()=> [ViewStatusEnum.Create, ViewStatusEnum.Edit, ViewStatusEnum.Copy].includes(viewStatus.value))

        const getSettingMenusViewDetail = async ()=> {

            if(!viewParams.value?.id && viewStatus.value !== ViewStatusEnum.Create) return

            try {
                loading.value = true
                const { data, success, message = '' } = await fetchGetSettingMenusViewDetail(viewParams.value?.id || -1)
                if(!success) {
                    return Message.warning(message)
                }

                settingViewDetail.value = {
                    ...data,
                    cardList: AuthTreeNode.transformByMenusGroup(data.cardList || [], false)
                }
            } catch (err) {
                console.error('[ getSettingMenusViewDetail error ]', err)
            } finally {
                loading.value = false
            }
        }

        watchEffect(()=> {
            getSettingMenusViewDetail()
        })



        return ()=> (
            <div class="setting-authorization-view__content-main"
            {...{
                directives: [{
                    name: 'loading', value: loading.value
                }]
            }}>
               {isDetailView.value ? <AuthorizationViewContentDetailView key={settingViewDetail.value?.id + 'detail'} settingViewDetail={settingViewDetail.value} onRefresh={()=> getSettingMenusViewDetail()}></AuthorizationViewContentDetailView> : null}
               {isFormView.value ?  <AuthorizationViewContentEditForm key={settingViewDetail.value?.id + 'form'} settingViewDetail={settingViewDetail.value} onRefresh={()=> getSettingMenusViewDetail()}></AuthorizationViewContentEditForm> : null}
            </div>
        )
    }
})