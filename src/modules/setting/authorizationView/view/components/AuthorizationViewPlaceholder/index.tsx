
import { computed, defineComponent } from "vue";
import { getOssUrl } from "@src/util/assets";
import '@src/modules/setting/authorizationView/style/placeholder.scss'
import { useViewState } from "../../../hooks/useStore";
import { ViewStatusEnum } from "../../../model";
import { t } from '@src/locales';

export default defineComponent({
    setup() {
        const { viewStatusParams, setViewStatusParams, goViewBack} = useViewState()
        
        const placeholderImg = computed(()=> getOssUrl('setting_auth_view_placeholder.png'))
        const handleCreate = ()=> {
            setViewStatusParams({
                status: ViewStatusEnum.Create,
                params: { id: -1}
            })
        }
        
        return ()=> (
            <div class="authorization-view-placeholder">
                <div class="authorization-view-placeholder__content">
                    <img class="img" src={placeholderImg.value}></img>
                    <h1 class="title">{t('common.auth.setView')}</h1>
                    <p class="sub-title">{t('common.auth.authorizationmanagement')}</p>
                    <el-button class="mt_8" type="primary" onClick={handleCreate}>{t('common.base.addView')}</el-button>
                </div>
            </div>
        )
    }
})