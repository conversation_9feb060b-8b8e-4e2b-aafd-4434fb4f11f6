import { defineComponent, PropType, toRefs } from "vue";
import { SettingViewListItem, ViewStatusEnum } from "@src/modules/setting/authorizationView/model";
import { useViewList, useViewState } from "../../../hooks/useStore";
import { t } from '@src/locales';

export default defineComponent({
    name: 'SettingAuthorizationViewLeftPanel',
    props: { },
    setup() {
      const { viewList } = useViewList()
      const { viewStatusParams, setViewStatusParams, goViewBack} = useViewState()


      const handleViewClick = (item: SettingViewListItem) => {
        setViewStatusParams({
          status: ViewStatusEnum.Detail,
          params: { id: item.id }
        })
      }

      const handleCreateClick = ()=> {
        setViewStatusParams({
          status: ViewStatusEnum.Create,
          params: { id: -1}
        })
      }

      return ()=> (
          <div class="setting-authorization-view__content-left">
            <el-scrollbar tag="ul" class="setting-authorization-view__list">
              { viewList.value.map((item: SettingViewListItem)=> {
                  return <li class={["setting-authorization-view__list-item", viewStatusParams.value?.params?.id == item.id ? 'active' : null]} onClick={()=> handleViewClick(item)}>{item.viewName}</li>
              }) }
            </el-scrollbar>
            <el-button class="add-btn" type="primary" onClick={handleCreateClick}>{t('common.base.addView')}</el-button>
          </div>
      )
    }
})