import { BaseSelectUserResult } from "@src/component/common/BaseSelectUser";
import { AuthTreeNode } from "@src/modules/account/role/component/AuthTree/model";
import { SettingViewDetailAuthObjItem } from "@src/modules/setting/authorizationView/model";

export const changeFrontSettingMenuItemValueToServeValue = (settingMenusAuthNodes: AuthTreeNode[])=> {
    const result = new Set();

    function traverse(node: AuthTreeNode, path: AuthTreeNode[] = []) {
        const newPath = [...path, node];

        // 如果是三级节点且被选中，就收集路径上的所有 ID
        if (!node.children.length && node.checked) {
            newPath.forEach(n => result.add(n.id));
        }

        // 继续递归
        if (node.children) {
            node.children.forEach(child => traverse(child, newPath));
        }
    }

    settingMenusAuthNodes.forEach(node => traverse(node));
    return Array.from(result);
}


export const covertSettingViewAuthorityListToServe = (userSelectResult: BaseSelectUserResult) => {
    const dataKeyMap = {
        userIds: 'user',
        departmentIds: 'tag',
        roleIds: 'role',
        // deptManagerIds: 'tagManager',
        // serviceProviderIds: 'sp',
        intelligentTagsIds: 'label'
    }

    const resultArray: { objType: string; objId: any; }[] = []

    for(let [k, v] of Object.entries(dataKeyMap)) {
        //@ts-ignore
        const value = userSelectResult[k]
       if(Array.isArray(value)) {
            value.map(item=> {
                resultArray.push({ objType: v, objId: item})
            })
        }

    }

    return resultArray
}

export const buildAuthority = (data: SettingViewDetailAuthObjItem[])=> {
    let userType = {
      'user': 1,//用户
      'tag': 2,//部门
      'role': 3,//角色
      'label':  8 // 标签
    }
    return data.map(item => {
      let { objId, objName, objType, staffId} = item
      const result = {
        id: objId,
        name: objName,
        //@ts-ignore
        type: userType[objType],
        //@ts-ignore
        typeId: userType[objType],
        staffId,
      }
      //@ts-ignore
      if(userType[objType] ==1) {
        //@ts-ignore
        result.userId = objId
      }
      return result
    })
  }