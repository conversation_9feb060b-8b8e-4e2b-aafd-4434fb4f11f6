/* enum */
import ComponentNameEnum from '@model/enum/ComponentNameEnum'
/* model */
import { SettingGPTDialogEventEnum } from '@src/modules/setting/gpt/model'
/* vue */
import { defineComponent, PropType, watch } from 'vue'
import { CreateElement } from 'vue'
/* hooks */
import { useDialog } from '@hooks/useDialog'
/* util */
import { isAsyncFunction, isFalsy } from '@src/util/type'
import Log from '@src/util/log'
/* scss */
import '@src/modules/setting/gpt/components/dialog.scss'

export default defineComponent({
  name: ComponentNameEnum.SettingGPTViewDialog,
  props: {
    asyncConfirmFunction: {
      type: Function as PropType<() => Promise<boolean>>,
      default: null,
    },
    className: {
      type: String as PropType<string>,
      default: '',
    },
    confirmText: {
      type: String as PropType<string>,
      default: '确认',
    },
    disabled: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    title: {
      type: String as PropType<string>,
      default: '',
    },
    visible: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    width: {
      type: String as PropType<string>,
    },
    showFooter: {
      type: Boolean as PropType<boolean>,
      default: true,
    },
    maskCloseable: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    onConfirm: {
      type: Function
    },
    onClose: {
      type: Function
    },
    btnLoading:{
      type:Boolean,
      default:false,
    },
  },
  setup(props, { emit }) {
    
    const { visible: insideVisible, showDialog, hideDialog } = useDialog()
    
    const onDialogCloseHandler = () => {
      emit(SettingGPTDialogEventEnum.Close)
    }
    
    const onDialogConfirmHandler = () => {
      emit(SettingGPTDialogEventEnum.Confirm)
    }
    
    watch(() => props.visible, (visible) => {
      if (visible) {
        showDialog()
      } else {
        hideDialog()
      }
    }, {
      immediate: true,
    })
    
    return {
      insideVisible,
      
      showDialog,
      hideDialog,
      
      onDialogCloseHandler,
      onDialogConfirmHandler,
    }
    
  },
  methods: {
    close() {
      this.hideDialog()
      this.onDialogCloseHandler()
    },
    /** 
     * @description 取消按钮点击事件
    */
    cancel() {
      this.hideDialog()
      this.onDialogCloseHandler()
    },
    /** 
     * @description 确认按钮点击事件
    */
    async confirm() {
      
      let confirmed = true
      
      if (isAsyncFunction(this.asyncConfirmFunction)) {
        confirmed = await this!.asyncConfirmFunction()
      }
      
      if (isFalsy(confirmed)) {
        Log.error('asyncConfirmFunction failed', this.confirm.name)
        return
      }
      
      this.onDialogConfirmHandler()
      
    },
    /**
    * @description 获取属性列表
    * @return {Record<string, any>} 属性列表
    */  
    getAttributes(): Record<string, any> {
      return {
        class: [
          this.className,
          ComponentNameEnum.SettingGPTViewDialog
        ],
        props: {
          title: this.title,
          show: this.insideVisible,
          width: this.width,
          appendToBody: true,
          maskCloseable: this.maskCloseable
        },
        on: {
          'update:show': () => {
            this.hideDialog()
            this.onDialogCloseHandler()
          }
        }
      }
    },
    /** 
     * @description 打开弹窗
    */
    open() {
      this.showDialog()
    },
    renderFooterWrapper() {
      if (isFalsy(this.showFooter)) {
        return null
      }
      return this.renderFooter()
    },
    renderFooter() {
      return (
        <div class="dialog-footer" slot="footer">
          
          <el-button 
            disabled={this.disabled}
            onClick={this.cancel}
            class="dialog-footer__cancel"
          >
            取消
          </el-button>
          
          {/* start 确定按钮 */}
          <el-button 
            type="primary" 
            disabled={this.disabled}
            onClick={this.confirm}
            loading={this.btnLoading}
          >
            { this.confirmText }
          </el-button>
          {/* end 确定按钮 */}
          
        </div>
      )
    }
  },
  render(h: CreateElement) {
    
    const attrs = this.getAttributes()
    
    return (
      <base-modal {...attrs}>
        
        { this.$slots.default }
        
        { this.renderFooterWrapper() }
        
      </base-modal>
    )
  }
})
