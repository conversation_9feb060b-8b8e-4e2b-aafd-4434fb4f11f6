/* api */
import { saveValueAddedClue } from '@src/api/Clue'
/* components */
import { 
  SettingGPTViewHeader,
  SettingGPTViewContent
} from "@src/modules/setting/gpt/components";
/* enum */
import ComponentNameEnum from "@model/enum/ComponentNameEnum";
/* images */
import { getLocalesOssUrl } from '@src/util/assets'
/* vue */
import { defineComponent } from "vue";
/* scss */
import "@src/modules/setting/gpt/views/index/close.scss";
/* util */
import platform from "@src/platform";
import { postPage, getRootWindow } from 'pub-bbx-utils'
import { t } from '@src/locales'
import { isFalsy } from '@src/util/type';

const settingGPTDescriptionImage1 = getLocalesOssUrl('/setting-gpt-description-1.png')
const settingGPTDescriptionImage2 = getLocalesOssUrl('/setting-gpt-description-2.png')
const settingGPTDescriptionImage3 = getLocalesOssUrl('/setting-gpt-description-3.png')
const StorageKey = 'clueType9'
const ImageRef = 'setting-gpt-close-view-content-desc-item-image'

export default defineComponent({
  name: ComponentNameEnum.SettingGPTCloseView,
  props: {
    
  },
  setup(props, { emit }) {
    
  },
  computed: {
    subTitle(): string {
      return `
      Hello，我是您的企业专属智能助手，您也可以叫我小宝 <br> 
      作为一个企业Al助手，我不仅具备强大的自然语言处理技术，还深度理解企业的业务需求，<br>
      能够帮助您提高工作效率，助力您的企业实现更大的商业价值。
      `
    },
    descriptionList(): Array<Record<string, any>> {
      return [
        {
          image: settingGPTDescriptionImage1,
          title: "企业知识智能问答",
          subTitle: '帮助员工快速答疑解惑，助力员工精准定位问题，开启智能问答时代！',
          url: 'https://doc.shb.ltd/shb_xoqazk/ia4qctp2ly559d1m/eka8tf9wv8x43arx/xgs8aywpqgn7ymug.html'
        },
        {
          image: settingGPTDescriptionImage2,
          title: "小宝BI",
          subTitle: '通过与小宝BI对话，即可轻松输出各类业务数据报表，小宝BI帮你让繁琐的报表工作变的简单！',
          url: 'https://doc.shb.ltd/shb_xoqazk/ia4qctp2ly559d1m/eka8tf9wv8x43arx/xgs8aywpqgn7ymug.html'
        },
        {
          image: settingGPTDescriptionImage3,
          title: "AI智能翻译",
          subTitle: '帮助不同语种、不同的内容形式，跨越语言障碍，开启企业全球视野！',
          url: 'https://doc.shb.ltd/shb_xoqazk/ia4qctp2ly559d1m/eka8tf9wv8x43arx/xgs8aywpqgn7ymug.html'
        }
      ]
    }
  },
  methods: {
    handleApply(){
      
      if (!sessionStorage.getItem(StorageKey)) {
        sessionStorage.setItem(StorageKey, 'true')
        saveValueAddedClue({ type: 9 })
      }
      
      postPage({
        action: 'shb.system.openChat',
        data: {},
      })
      
    },
    openButtonClickHandler() {
      this.handleApply()
    },
    renderOpenButton() {
      return (
        <el-button
          type="primary"
          onClick={this.openButtonClickHandler}
        >
          联系客服试用
        </el-button>
      )
    },
    renderDescriptionList() {
      return (
        <div class="setting-gpt-close-view-content-desc-list">
          {
            this.descriptionList.map((item: Record<string, any>, index) => {
              return this.renderDescriptionItem(item, index)
            })
          }
        </div>
      )
    },
    renderDescriptionItem(item: Record<string, any>, index: number) {
      return (
        <div class="setting-gpt-close-view-content-desc-item">
          
          <div class="setting-gpt-close-view-content-desc-item-image">
            <img src={item.image} ref={ImageRef + index} />
          </div>
          
          <div class="setting-gpt-close-view-content-desc-item-title">
            { item.title }
          </div>
          
          <div class="setting-gpt-close-view-content-desc-item-sub-title">
            { item.subTitle }
          </div>
          
          <div class="setting-gpt-close-view-content-desc-item-button">
            <el-button
              type="plain-third"
              onClick={() => this.openDescriptionItem(item)}
            >
              了解更多
            </el-button>
          </div>
          
        </div>
      )
    },
    openDescriptionItem(item: Record<string, any>) {
      platform.openLink(item.url)
    },
    modifyImageHeight() {
      Object.keys(this.$refs).forEach((key: string) => {
        
        if (isFalsy(key.includes(ImageRef))) {
          return
        }
        
        const image = this.$refs[key] as HTMLImageElement
        const clientWidth = image.clientWidth
        const clientHeight = clientWidth / 3
        
        // @ts-ignore
        this.$refs[key]!.setAttribute('style', `height: ${clientHeight}px`)
        
      })
    }
  },
  mounted() {
  },
  render() {
    return (
      <div class={ComponentNameEnum.SettingGPTCloseView}>
        
        <SettingGPTViewHeader
          subTitle={this.subTitle}
        >
          <div slot="button">
            {this.renderOpenButton()}
          </div>
        </SettingGPTViewHeader>
        
        <SettingGPTViewContent>
          
          { this.renderDescriptionList() }
          
        </SettingGPTViewContent>
        
      </div>
    )
  }
});