<!--  -->
<template>
  <div
    id="normal-setting-components-box"
    v-loading.fullscreen.lock="fullscreenLoading"
  >
  <template v-if="checkModuleUrlMatch('PRODUCT_PRODUCT_TYPE')">
    <div class="flex-x box-12 bg-w mar-btm-10">
      <div class="flex-1">
        <div class="font-16 font-w-600">{{$t('product.setting.productFieldSetTitle')}}</div>
        <div class="common-item-tips">{{$t('product.setting.productFieldSetDes')}}</div>
      </div>
      <el-button type="primary" @click="openTab('PageSettingProduct')"
      >{{$t('common.form.setField')}}</el-button
      >
    </div>
    
    <div class="flex-x box-12 bg-w">
      <div class="flex-1">
        <div
          class="font-16 font-w-600 border-btm"
          style="margin-right: -84px;margin-left: -16px;padding-left: 16px;">
          {{$t('product.setting.productFunctionSet')}}
        </div>
        <div class="font-w-600">{{$t('common.base.mobileSearch')}}</div>
        <div class="common-item-tips">{{$t('product.setting.allowSearchProductInMobile')}}</div>
      </div>
      <div style="margin-top: 23px;">
        <el-switch
          v-model="productSearchOnMobile"
          @change="saveFunc($event, 'productSearchOnMobile')"
        ></el-switch>
        {{ productSearchOnMobile ? $t('common.base.enable') : $t('common.base.disable') }}
      </div>
    </div>

    <div class="flex-x box-12 bg-w align-base">
      <div class="flex-1">
        <div class="font-w-600 subscript">
          {{$t('product.setting.allowCreateProductIsNotConcatCustomerLabel')}}<span class="subscript-product" v-if="showNew"
          >new</span
          >
        </div>
        <div class="common-item-tips">
          {{$t('product.setting.allowCreateProductIsNotConcatCustomerDes')}}
        </div>
      </div>
      <div>
        <el-switch
          v-model="productCreateUnBindCustomer"
          @change="saveFunc($event, 'productCreateUnBindCustomer')"
        ></el-switch>
        {{ productCreateUnBindCustomer ? $t('common.base.enable') : $t('common.base.disable') }}
      </div>
    </div>

    <div class="flex-x box-12 bg-w mar-btm-10 align-base">
      <div class="flex-1">
        <div class="font-w-600 subscript">
          {{ $t('product.setting.allowUpdateProductLabel') }}
        </div>
        <div class="common-item-tips">
          {{ $t('product.setting.allowUpdateProductDes') }}
        </div>
      </div>
      <div>
        <el-switch
          v-model="productConfigSwitch.IMPORT_PRODUCT_UPDATE_IF_EXIST"
          @change="productIfSameSnUpdateProductChange()"
        ></el-switch>
        {{ productConfigSwitch.IMPORT_PRODUCT_UPDATE_IF_EXIST ? $t('common.base.enable') : $t('common.base.disable') }}
      </div>
    </div>

    <div class="flex-x box-12 bg-w mar-btm-10">
      <div class="flex-1">
        <div class="font-w-600 subscript">
          {{$t('product.setting.prodcutIsNotConnectCustomerResolveDes')}}
        </div>
        <div class="check-box">
          <el-checkbox-group v-model="allowUnbindCusProduct" @change="editTenantConfig()">
            <el-checkbox :label="2">
              {{$t('product.setting.prodcutIsNotConnectCustomerResolveLabel1')}}
            </el-checkbox>
            <el-checkbox :label="1">
              {{$t('product.setting.prodcutIsNotConnectCustomerResolveLabel2')}}
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
    </div>

    <div class="bg-w" v-if="initData.openSuperCodePro">
      <div class="flex-1 box-12 border-btm mar-btm-0">
        <div class="font-16 font-w-600 ">{{$t('product.setting.productTypeSetTitle')}}</div>
      </div>
      <div class="flex-x box-12">
        <div class="flex-1">
          <div class="font-w-600">{{$t('product.setting.productTypeFieldSetTitle')}}</div>
          <div class="common-item-tips">
            {{$t('product.setting.productTypeFieldSetDes')}}
          </div>
        </div>
        <el-button
          type="primary"
          @click="openTab('PageProductField')"
        >{{$t('common.form.setField')}}</el-button>
      </div>
      <div class="flex-x box-12 bg-w align-base">
        <div class="flex-1">
          <div class="font-w-600">{{$t('product.setting.productMenuSplitSetTitle')}}</div>
          <div class="common-item-tips">
            {{$t('product.setting.productMenuSplitDes')}}
          </div>
        </div>
        <div>
          <el-switch
            v-model="productMenuSplit"
            @change="saveFunc($event, 'productMenuSplit')"
          ></el-switch>
          {{ productMenuSplit ? $t('common.base.enable') : $t('common.base.disable') }}
        </div>
      </div>
    </div>
    
    <!-- 产品质保管理迁移到新的页面了，目前先保留，暂时为false，后续在删除吧 -->
    <div class="flex-x box-12 bg-w mar-btm-10 align-base module-scroll-quality" v-if="isShowQuality && false">
      <div class="flex-1">
        <div class="font-16 font-w-600">
          {{$t('product.setting.productQualitySetTitle')}}
          <i
            @click="SavePoppe"
            class="iconfont icon-warning-circle-fill el-tooltip"
            style="cursor: pointer"
          ></i>
        </div>
        <div class="common-item-tips">
          {{$t('product.setting.productQualitySetDes')}}
        </div>
      </div>
      <div class="mar-top-5" style="position: relative">
        <el-switch
          v-model="qualitySwitch"
          @change="qualitySave($event, 'qualitySwitch')"
        ></el-switch>
        {{ qualitySwitch ? $t('common.base.enable') : $t('common.base.disable') }}
        <el-popover
          v-model="isPopover"
          placement="bottom"
          width="206"
          trigger="click"
          popper-class="popperclass"
        >
          <div class="poppeText">
            <i
              style="color: #faad14; margin-right: 9px"
              class="
                el-icon-warning
                iconWarning
                el-tooltip
              "
            ></i>
            {{$t('product.setting.productQualityGuide')}}
          </div>
          <div class="poppeSave">
            <el-button @click="SavePoppe" type="primary"
            >{{$t('common.base.clickForMoreInformation')}}</el-button
            >
          </div>
        </el-popover>
      </div>
    </div>

    <!-- 纯客服云版本、服务云标准版、服务云标准版+客服云不显示超级二维码设置 -->
    <template v-if="!initData.openSuperCodePro && _isShowProductQrcode">
      <div class="flex-x box-12 bg-w">
        <div class="flex-1">
          <div class="font-16 font-w-600 border-btm">{{$t('product.setting.superCodeSetTitle')}}</div>
          <div class="font-w-600">{{$t('product.setting.openSuperCodeSetLabel')}}</div>
          <div class="common-item-tips">
            {{$t('product.setting.openSuperCodeSetDes')}}
          </div>
        </div>
        <div class="mar-top-52">
          <el-switch
            v-model="qrcodeEnabled"
            @change="saveFunc($event, 'qrcodeEnabled')"
          ></el-switch>
          {{ qrcodeEnabled ? $t('common.base.enable') : $t('common.base.disable') }}
        </div>
      </div>
      <div class="flex-x box-12 bg-w">
        <div class="flex-1">
          <div class="font-w-600">{{$t('product.setting.customerInfoInSuperCodeLabel')}}</div>
          <div class="common-item-tips">
            {{$t('product.setting.customerInfoInSuperCodeDes')}}
          </div>
        </div>
        <div class="mar-top-12">
          <el-switch
            v-model="hideDefaultLinkman"
            @change="saveFunc($event, 'hideDefaultLinkman')"
          ></el-switch>
          {{ hideDefaultLinkman ? $t('common.base.enable') : $t('common.base.disable') }}
        </div>
      </div>
      <div class="flex-x box-12 bg-w mar-btm-10" v-if="qrcodeEnabled">
        <div class="flex-1">
          <div class="font-w-600">{{$t('product.setting.productCodeManageTitle')}}</div>
          <div class="common-item-tips">{{$t('product.setting.productCodeManageDes')}}</div>
          <div class="font-12 mar-top-5">
            <el-button type="primary" @click="openQrcodeDialog()"
            >{{$t('product.setting.editProductCodeBtn')}}</el-button
            >
          </div>
        </div>
        <div class="mar-top-12">
          <el-button type="primary" @click="openTab('PageProductQrcode')"
          >{{$t('common.base.goTo')}}</el-button
          >
        </div>
      </div>
    </template>
  </template>
    <!--产品注册功能 start-->

    <div class="flex-x box-12 bg-w align-base group-header module-scroll-register" :class="[ isHasModuleKey ? 'none' : null ]" v-if="ifRegisterEffect && checkModuleUrlMatch('PRODUCT_REGISTRATION')">
      <div class="flex-1">
        <div class="font-16 font-w-600 group-header-title">
          {{$t('product.setting.productRegisterSet.title')}}
          <i
            @click="saveRegisterPoppe()"
            class="iconfont icon-warning-circle-fill el-tooltip"
            style="cursor: pointer"
          ></i>
        </div>
        <!-- <div class="font-w-600">移动端查询</div> -->
        <div class="group-header-tips common-item-tips" :class="{ 'is-close': !register.PRODUCT_REGISTRATION.isOpen }">
          {{$t('product.setting.productRegisterSet.des')}}
        </div>
      </div>
      <div>
        <el-switch
          :active-value="1"
          :inactive-value="0"
          v-model="register.PRODUCT_REGISTRATION.isOpen"
          @change="registerSave('registerSwitchAll')"
        ></el-switch>
        {{ register.PRODUCT_REGISTRATION.isOpen ? $t('common.base.enable') : $t('common.base.disable') }}
      </div>
    </div>

    <div class="group-box mar-btm-10" v-if="register.PRODUCT_REGISTRATION.isOpen && ifRegisterEffect && checkModuleUrlMatch('PRODUCT_REGISTRATION')">
      <div class="group-item">
        <div class="group-item-name">{{$t('common.base.baseSet')}}</div>

        <div class="group-item-child flex-x box-12 bg-w">
          <div class="flex-1">
            <div class="font-14 font-w-600 group-item-child-label">
              {{$t('product.setting.productRegisterFieldSet.title')}}
            </div>
            <div class="common-item-tips">{{$t('product.setting.productRegisterFieldSet.des')}}</div>
          </div>
          <el-button
            type="primary"
            @click="openTab('PageSettingProductRegister')"
          >{{$t('common.form.setField')}}</el-button
          >
        </div>

        <div class="group-item-child flex-x box-12 bg-w align-base">
          <div class="flex-1">
            <div class="font-14 font-w-600 group-item-child-label">
              {{$t('product.setting.productRegisterAutoUpdateToCustomer.title')}}
              <el-tooltip
                trigger="hover"
              >
                <div slot="content" style="width: 420px">
                  {{$t('product.setting.productRegisterAutoUpdateToCustomer.des')}}
                </div>
                <i class="el-icon-question" />
              </el-tooltip>
            </div>
            <div class="common-item-tips" v-if="register.AUTO_UPDATE_CUSTOMER.isOpen">{{$t('product.setting.productRegisterAutoUpdateToCustomer.tips')}}</div>
            <div
              class="font-12 mar-top-5"
              v-if="register.AUTO_UPDATE_CUSTOMER.isOpen"
            >
              <biz-form-remote-select
                class="user-type-select"
                ref="TeamUserBizFormRemoteSelect"
                v-model="personsSetting.AUTO_UPDATE_CUSTOMER"
                :placeholder="$t('common.placeholder.select')"
                :remote-method="fetchUsers"
                :inputAlwayTop="true"
              >
                <div
                  class="allot-users-option"
                  slot="option"
                  slot-scope="{ option }"
                >
                  <contact-user-item
                    :user="option"
                    :show-user-state="true"
                    :state-color="stateColor"
                    :show-tag="true"
                  />
                </div>
              </biz-form-remote-select>
              <el-button
                class="user-save"
                type="primary"
                :disabled="!isAutoUpdateCustomerChange"
                @click="registerSave('AUTO_UPDATE_CUSTOMER', 'user')">
                {{$t('common.base.save')}}
              </el-button>
            </div>
          </div>
          <div>
            <el-switch
              :active-value="1"
              :inactive-value="0"
              v-model="register.AUTO_UPDATE_CUSTOMER.isOpen"
              @change="registerSave('AUTO_UPDATE_CUSTOMER', 'switch')"
            ></el-switch>
            {{ register.AUTO_UPDATE_CUSTOMER.isOpen ? $t('common.base.enable') : $t('common.base.disable') }}
          </div>
        </div>

        <div class="group-item-child flex-x box-12 bg-w align-base">
          <div class="flex-1">
            <div class="font-14 font-w-600 group-item-child-label">
              {{$t('product.setting.productRegisterAutoUpdateToProduct.title')}}
              <el-tooltip
                trigger="hover"
              >
                <div slot="content" style="width: 420px">
                  {{$t('product.setting.productRegisterAutoUpdateToProduct.des')}}
                </div>
                <i class="el-icon-question" />
              </el-tooltip>
            </div>
            <div class="common-item-tips" v-if="register.AUTO_UPDATE_PRODUCT.isOpen">{{$t('product.setting.productRegisterAutoUpdateToProduct.tips')}}</div>
            <div
              class="font-12 mar-top-5"
              v-if="register.AUTO_UPDATE_PRODUCT.isOpen"
            >
              <biz-form-remote-select
                class="user-type-select"
                ref="TeamUserBizFormRemoteSelect"
                v-model="personsSetting.AUTO_UPDATE_PRODUCT"
                :placeholder="$t('common.placeholder.select')"
                :remote-method="fetchUsers"
                :inputAlwayTop="true"
              >
                <div
                  class="allot-users-option"
                  slot="option"
                  slot-scope="{ option }"
                >
                  <contact-user-item
                    :user="option"
                    :show-user-state="true"
                    :state-color="stateColor"
                    :show-tag="true"
                  />
                </div>
              </biz-form-remote-select>
              <el-button
                class="user-save"
                type="primary"
                :disabled="!isAutoUpdateProductChange"
                @click="registerSave('AUTO_UPDATE_PRODUCT', 'user')">
                {{$t('common.base.save')}}
              </el-button>
            </div>
          </div>
          <div>
            <el-switch
              :active-value="1"
              :inactive-value="0"
              v-model="register.AUTO_UPDATE_PRODUCT.isOpen"
              @change="registerSave('AUTO_UPDATE_PRODUCT', 'switch')"
            ></el-switch>
            {{ register.AUTO_UPDATE_PRODUCT.isOpen ? $t('common.base.enable') : $t('common.base.disable') }}
          </div>
        </div>

        <div class="group-item-child flex-x box-12 bg-w align-base">
          <div class="flex-1">
            <div class="font-14 font-w-600 group-item-child-label">{{$t('product.setting.allowCustomerCreatProductWhenProductIsNotInSystemTips')}}</div>
          </div>
          <div>
            <el-switch
              :active-value="1"
              :inactive-value="0"
              v-model="register.REGISTER_WITHOUT_SYS.isOpen"
              @change="registerSave('REGISTER_WITHOUT_SYS')"
            ></el-switch>
            {{ register.REGISTER_WITHOUT_SYS.isOpen ? $t('common.base.enable') : $t('common.base.disable') }}
          </div>
        </div>

        <div class="group-item-child  flex-x box-12 bg-w align-base">
          <div class="flex-1">
            <div class="font-14 font-w-600 group-item-child-label">{{$t('product.setting.limitProductRegisterSet.title')}}</div>
            <div
              class="flex acenter common-item-tips"
              v-if="register.REGISTRATION_LIMIT.isOpen"
            >
            {{$t('product.setting.limitProductRegisterSet.des')}}
                <!-- @input.native="v => register.REGISTRATION_LIMIT.configNumValue = v.target.value.replaceAll(/\D/g, '')" -->
              <el-input
                type="number"
                class="pl8 pr8 mr10 ml10 product_register_input"
                @keydown.native="keyLimit"
                @blur="registerSave"
                :step="1"
                v-model="register.REGISTRATION_LIMIT.configNumValue"
                placeholder=""
              ></el-input
              >{{$t('product.setting.limitProductRegisterSet.numberOfCustomer')}}
            </div>
          </div>
          <div>
            <el-switch
              :active-value="1"
              :inactive-value="0"
              v-model="register.REGISTRATION_LIMIT.isOpen"
              @change="registerSave"
            ></el-switch>
            {{ register.REGISTRATION_LIMIT.isOpen ? $t('common.base.enable') : $t('common.base.disable') }}
          </div>
        </div>

        <div class="group-item-child flex-x box-12 bg-w align-base">
          <div class="flex-1">
            <div class="font-14 font-w-600 group-item-child-label">{{$t('product.setting.productApproveSet.title')}}</div>
            <div class="flex acenter common-item-tips">
              {{$t('product.setting.productApproveSet.des')}}
            </div>
            <div
              class="font-12 mar-top-5"
              v-if="register.REGISTRATION_APPROVE.isOpen"
            >
              <!-- <base-select-user-input
                class="base-select-user-input-item"
                multiple 
                :collapse="false"
                v-model="personsSetting.REGISTRATION_APPROVE" 
              >
              </base-select-user-input> -->
              <biz-form-remote-select
                class="user-type-select"
                ref="TeamUserBizFormRemoteSelect"
                v-model="personsSetting.REGISTRATION_APPROVE"
                :placeholder="$t('common.placeholder.select')"
                :remote-method="fetchApprovers"
                multiple
                :inputAlwayTop="true"
              >
                <div
                  class="allot-users-option"
                  slot="option"
                  slot-scope="{ option }"
                >
                  <contact-user-item
                    :user="option"
                    :show-user-state="true"
                    :state-color="stateColor"
                    :show-tag="true"
                  />
                </div>
              </biz-form-remote-select>
              <el-button
                class="user-save"
                type="primary"
                :disabled="!isRegisterApproverChange"
                @click="registerSave('REGISTRATION_APPROVE', 'user')">{{$t('common.base.save')}}</el-button>
            </div>
          </div>
          <div>
            <el-switch
              :active-value="1"
              :inactive-value="0"
              v-model="register.REGISTRATION_APPROVE.isOpen"
              @change="registerSave('REGISTRATION_APPROVE', 'switch')"
            ></el-switch>
            {{ register.REGISTRATION_APPROVE.isOpen ? $t('common.base.enable') : $t('common.base.disable') }}
          </div>
        </div>
      </div>


      <div class="group-item">
        <div class="group-item-name">{{$t('common.base.otherSet')}}</div>

        <div class="group-item-child flex-x box-12 bg-w">
          <div class="flex-1">
            <div class="font-14 font-w-600 group-item-child-label">{{$t('product.setting.otherSet.scanRulerSet.title')}}</div>
            <div class="common-item-tips">{{$t('product.setting.otherSet.scanRulerSet.des')}}</div>
          </div>
          <el-button
            type="primary"
            @click="toScanConfigDialog()"
          >{{$t('product.setting.otherSet.scanRulerSet.btn')}}</el-button
          >
        </div>

        <div class="group-item-child flex-x box-12 bg-w">
          <div class="flex-1">
            <div class="font-14 font-w-600 group-item-child-label">{{$t('product.setting.otherSet.customerGuideSet.title')}}</div>
            <div class="common-item-tips">{{$t('product.setting.otherSet.customerGuideSet.des')}}</div>
          </div>
          <el-button
            type="primary"
            @click="toGuideConfigDialog()"
          >{{$t('product.setting.otherSet.customerGuideSet.btn')}}</el-button
          >
        </div>

        <div class="group-item-child flex-x box-12 bg-w align-base">
          <div class="flex-1">
            <div class="font-14 font-w-600 group-item-child-label">
              {{$t('product.setting.otherSet.wxPublicSet.title')}}<span
                style="margin-left: 12px;font-weight: normal; cursor: pointer;"
                :style="{color: getThemeColor}"
                @click="openTab('PageSettingDoMyself')"
              >{{$t('product.setting.otherSet.wxPublicSet.btn')}}</span
              >
            </div>
            <div class="common-item-tips">{{$t('product.setting.otherSet.wxPublicSet.des')}}</div>
          </div>
          <div>
            <el-switch
              :active-value="1"
              :inactive-value="0"
              v-model="register.WECHAT_QRCODE_IMAGE_SWITCH.isOpen"
              @change="registerSave('WECHAT_QRCODE_IMAGE_SWITCH')"
            ></el-switch>
            {{ register.WECHAT_QRCODE_IMAGE_SWITCH.isOpen ? $t('common.base.enable') : $t('common.base.disable') }}
          </div>
        </div>

        <div class="group-item-child flex-x box-12 bg-w">
          <div class="flex-1">
            <div class="font-14 font-w-600 group-item-child-label">{{$t('product.setting.otherSet.productRegisterCopyUrl.title')}}</div>
            <div class="font-12 mar-top-12 flex acenter">
              <el-input
                v-trim:blur
                disabled
                class="task-select-search-input input-with-append-search task-mr12"
                :value="productRegisterUrl"
              >
                <el-button type="primary" slot="append" @click="onCopy(productRegisterUrl)"
                >{{$t('common.base.copy')}}</el-button
                >
              </el-input>
            </div>
          </div>
        </div>

        <div class="group-item-child flex-x box-12 bg-w align-base" v-if="registerItems.REGISTER_NOTICE">
          <div class="flex-1">
            <div class="font-14 font-w-600 group-item-child-label">
              {{$t('product.setting.otherSet.productRegisterNote.title')}}
              <el-tooltip
                trigger="hover"
              >
                <div slot="content" style="width: 420px">
                  {{$t('product.setting.otherSet.productRegisterNote.tips')}}
                </div>
                <i class="el-icon-question" />
              </el-tooltip>
            </div>
            <div class="flex acenter common-item-tips">
              {{$t('product.setting.otherSet.productRegisterNote.tips2')}}
            </div>
            <div
              class="font-12 sms-select"
              v-if="registerItems.REGISTER_NOTICE.isOpen"
            >
              <div
                :key="noticeType.label"
                v-for="noticeType in registerNoticeTypes"
                class="sms-select-item">
                <div class="sms-select-item-label">{{ noticeType.name }}</div>
                <span class="sms-select-item-tips">{{$t('product.setting.otherSet.productRegisterNote.label')}}</span>
                  <!-- :value="registerItems.REGISTER_NOTICE.configValue[noticeType.label]" -->
                  <!-- @input="templateInput()" -->
                <el-select
                  class="sms-select-item-input"
                  v-if="templateShow"
                  :popper-append-to-body="false"
                  clearable
                  v-model="registerItems.REGISTER_NOTICE.configValue[noticeType.label]"
                  :placeholder="$t('common.placeholder.selectSomething', {0:$t('common.base.messageTemplate')})">
                  <el-option
                    v-for="item in messageTemplate || []"
                    :label="item.name"
                    :value="item.id"
                    :key="item.id">
                  </el-option>
                </el-select>
              </div>
              <el-button
                class="sms-select-save"
                type="primary"
                :disabled="!isRegisterNoticeChange"
                @click="registerSaveEach('REGISTER_NOTICE', 'user')">{{$t('common.base.save')}}</el-button>
            </div>
          </div>
          <div>
            <el-switch
              :active-value="1"
              :inactive-value="0"
              v-model="registerItems.REGISTER_NOTICE.isOpen"
              @change="registerSaveEach('REGISTER_NOTICE', 'switch')"
            ></el-switch>
            {{ registerItems.REGISTER_NOTICE.isOpen ? $t('common.base.enable') : $t('common.base.disable') }}
          </div>
        </div>

        <!--start 触发器 -->
        <div v-if="isShowTrigger && !isHasModuleKey" class="product-trigger">
          <div class="empty"></div>
          <trigger-list-setting 
            :subTitle="$t('common.connector.trigger.otherSubTitle')" 
            bizType="PRODUCT"
            bizTypeId="2"
          />
        </div>
        <!--end 触发器 -->
      </div>
    </div>
    <!--产品注册功能 end-->

    <!--start 触发器 -->
    <div v-if="isHasModuleKey && checkModuleUrlMatch('PRODUCT_MANAGEMENT_TRIGGER')" class="product-trigger product-trigger-box">
      <!-- <div class="empty"></div> -->
      <trigger-list-setting 
        :subTitle="$t('common.connector.trigger.otherSubTitle')" 
        bizType="PRODUCT"
        bizTypeId="2"
      />
    </div>
    <!--end 触发器 -->

    <!-- 客户注册引导设置弹框  start-->
    <el-dialog
      class="register-guide-dialog"
      :title="$t('product.setting.customerRegisterGuideDialog.title')"
      :close-on-click-modal="false"
      v-if="customerGuidanceConfigBackUp"
      :visible.sync="customersRegisterDialogVisible"
      width="480px"
      @close="customersRegisterDialogClosed">
      <el-form class="customerGuide" ref="customersFormRef" label-position="top">
        <el-form-item :label="$t('product.setting.customerRegisterGuideDialog.label1')">
          <textarea
            class="textarea-guide"
            v-model="register.CUSTOMER_GUIDANCE.configStrValue.explanation"
            :placeholder="$t('common.placeholder.inputSomething', {data1:''})"
            rows="3"
            data-prop="placeHolder"
          ></textarea>
        </el-form-item>
        <el-form-item :label="$t('product.setting.customerRegisterGuideDialog.label2')">
          <div class="el-proposal-text">{{$t('product.setting.customerRegisterGuideDialog.tips1')}}</div>
          <upFile v-if="customersRegisterDialogVisible" @productMg="getImgUrl" @removePic="removePic" :upload-text="uploadText" :img-url='register.CUSTOMER_GUIDANCE.configStrValue.pic_url'></upFile>
          <ul class="uploadTips">
            <li class="tips">
              {{$t('product.setting.customerRegisterGuideDialog.tips2')}}
            </li>
            <li class="tips">{{$t('product.setting.customerRegisterGuideDialog.tips3')}}</li>
            <li class="tips">{{$t('product.setting.customerRegisterGuideDialog.tips4')}}</li>
          </ul>
          <div class="el-example-text" style="cursor: pointer;" :style="{color: getThemeColor}" @click="viewExamples">{{$t('common.base.checkTemplate')}}</div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="customersRegisterDialogVisible = false"
        >{{$t('common.base.cancel')}}</el-button
        >
        <el-button
          type="primary"
          :disabled="pending"
          :loading="pending"
          @click="registerSave"
        >{{$t('common.base.save')}}</el-button
        >
      </span>
    </el-dialog>
    <!-- 客户注册引导设置弹框  end-->
    <!-- 查看示例弹窗 start -->
    <el-dialog
      :title="$t('common.base.checkTemplate')"
      class="register-dialog register-example-dialog"
      :visible.sync="isShowExImage"
      width="440px"
      :before-close="handleClose" >
      <div
        class="image-view">
        <el-image
          ref="viewImg"
          class="image-view-img"
          :previewSrcList="previewSrcList"
          :src="previewSrcList[0]"
        >
        </el-image>
        <div class="image-view-shadow">
          <i class="iconfont icon-zoomin" @click="imgBtnClick()"></i>
        </div>
      </div>
    </el-dialog>
    <!-- 查看示例弹窗 end -->
    <div id="product-set-guide"></div>
    <GuidanceDialog
      v-if="currGuideType"
      :title="guideSetting[currGuideType].title"
      :steps="guideSetting[currGuideType].steps"
      :moreUrl="guideSetting[currGuideType].moreUrl"
      ref="GuidanceDialog" />
    <!-- <FormDialog ref="FormDialog" @refresh="getProductMenu" /> -->
    <QrcodeGuide ref="QrcodeGuide" @saveFunc="configSave"></QrcodeGuide>

    <!-- 扫码规则设置弹框  start-->
    <el-dialog
      v-if="registerItems.SCAN_CODE_RULE"
      class="register-scan-config-dialog register-dialog"
      :title="$t('product.setting.scanRulerSet.title')"
      :close-on-click-modal="false"
      :visible.sync="dialogVisible['SCAN_CODE_RULE']"
      width="600px"
      @close="scanConfigDialogClosed">
      <el-form
        ref="scanConfigForm"
        :model="registerItems.SCAN_CODE_RULE.configValue"
        :rules="scanConfigRules"
        
        label-position="top">
        <el-form-item :label="$t('product.setting.scanRulerSet.label1')" prop="codeType">
          <el-radio-group v-model="registerItems.SCAN_CODE_RULE.configValue.codeType">
            <el-radio label="text">{{$t('common.base.text')}}</el-radio>
            <el-radio label="url">{{$t('common.base.linkUrl')}}</el-radio>
          </el-radio-group>
          <div style="color: #faad14;margin-top: 7px" v-if="registerItems.SCAN_CODE_RULE.configValue.codeType === 'text'">
            {{$t('product.setting.scanRulerSet.des')}}
          </div>
        </el-form-item>
        <div v-if="registerItems.SCAN_CODE_RULE.configValue.codeType === 'url'">
          <el-form-item :label="$t('product.setting.scanRulerSet.label2')" prop="codeUrlExample">
            <el-input
              type="text"
              maxlength="1000"
              v-model="registerItems.SCAN_CODE_RULE.configValue.codeUrlExample">
            </el-input>
          </el-form-item>
          <el-form-item :label="$t('product.setting.scanRulerSet.label3')" prop="identifyParam">
            <el-input
              type="text"
              maxlength="1000"
              v-model="registerItems.SCAN_CODE_RULE.configValue.identifyParam">
            </el-input>
          </el-form-item>
          <el-form-item :label="$t('product.setting.scanRulerSet.label4')">
            <input type="hidden" :value="exampleCodeData">
            {{ exampleCodeData }}
          </el-form-item>
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible['SCAN_CODE_RULE'] = false"
        >{{$t('common.base.cancel')}}</el-button
        >
        <el-button
          type="primary"
          :disabled="dialogSaveLoading.SCAN_CODE_RULE"
          :loading="dialogSaveLoading.SCAN_CODE_RULE"
          @click="scanConfigSave('SCAN_CODE_RULE', 'dialog')"
        >{{$t('common.base.save')}}</el-button
        >
      </span>
    </el-dialog>
    <!-- 扫码规则设置弹框  end-->
  </div>
</template>

<script>
import _ from 'lodash';
import {
  saveFunc,
  productConfig,
  // getProductMenu,
  // moveProductType,
  // delTreeList,
  qualitySave,
  getFirstSettingUnBindCustomer,
  getConfig,
  setConfig,
  isDisplay,
  getTenantConfig,
  editTenantConfig,
  editTenantSingleConfig,
  getQualityConfigGray,
} from '@src/api/ProductV2Api';

import { registerUrl, registerApproverList } from '@src/api/productRegisterApi';
import { userList } from '@src/api/TeamApi';
import { safeNewDate } from '@src/util/time';

import QrcodeGuide from './components/QrcodeGuide';

import { storageGet, storageSet } from '@src/util/storage';
import GuideContent from '@src/component/guide/contentCom/ProductSet.vue';
import FormDialog from './components/FormDialog';
import GuidanceDialog from './components/GuidanceDialog';

import ContactUserItem from "@src/component/common/BaseContact/ContactUserItem.vue";

/* mixin */
import ThemeMixin from '@src/mixins/themeMixin/index.ts';
import QualityMixin from '@src/mixins/qualityMixin/index.tsx';
import { isEnterpriseEdition } from '@shb-lib/version';

import { asyncErrorCaptured } from '@src/util/onerror/index'
import { PRODUCT_PRODUCT_SET } from '@src/component/guide/productV2Store';
/* api */
import http from "@src/util/http";
import UpFile from '@src/modules/doMyself/wxSet/components/upFile.vue';
import { getRootWindowInitData } from '@src/util/window'

import { parse } from '@src/util/querystring';
import { openAccurateTab } from '@src/util/platform'
import { PageRoutesTypeEnum } from 'pub-bbx-global/pageType/dist/enum/PageRoutesEnum'
import i18n from '@src/locales'

import TriggerListSetting from '@src/modules/connector/components/trigger-list-setting/index.vue';
import { getRootWindow } from '@src/util/dom';
import { getLocalesOssUrl } from '@src/util/assets'

const guideImage = getLocalesOssUrl('/wx/register-guide.png')
/* util */
import { isOpenTriggerGray } from '@src/util/grayInfo'
import { checkModuleUrlMatch } from 'pub-bbx-utils'

/* mixin */
import { VersionControlProductMixin } from '@src/mixins/versionControlMixin'

export default {
  name: 'product-set',
  mixins: [ThemeMixin, QualityMixin, VersionControlProductMixin],
  inject: ['initData'],
  components: {
    FormDialog,
    QrcodeGuide,
    GuidanceDialog,
    UpFile,
    ContactUserItem,
    TriggerListSetting,
  },
  data() {
    return {
      isShowExImage:false, //查看示例
      imgUrl:null,
      uploadText:i18n.t('common.base.uploadImg'),
      pending: false,
      thumbnailList: [],
      customersRegisterDialogVisible: false,
      fullscreenLoading: false,
      productSearchOnMobile: false,
      qualitySwitch: true,
      productCreateUnBindCustomer: false,
      productMenuSplit: false,
      productConfigSwitch: {
        IMPORT_PRODUCT_UPDATE_IF_EXIST: false
      },
      // productMenuList: [],
      qrcodeEnabled: true,
      hideDefaultLinkman: true,
      hasSettingUnBindCustomer: true,
      isPopover: true,
      select: [],
      register: {
        PRODUCT_REGISTRATION: {
          configCode: 'PRODUCT_REGISTRATION',
          isOpen: 0
        },
        CUSTOMER_GUIDANCE: {
          // 引导
          configCode: 'CUSTOMER_GUIDANCE',
          configStrValue: {
            explanation: '',
            pic_url: ''
          }
        },
        REGISTRATION_LIMIT: {
          // 注冊限制
          configCode: 'REGISTRATION_LIMIT'
        },
        REGISTRATION_APPROVE: {
          // 注冊审批
          configCode: 'REGISTRATION_APPROVE'
        },
        REGISTER_WITHOUT_SYS: {

        },
        AUTO_UPDATE_PRODUCT: {
          configStrValue: {}
        },
        AUTO_UPDATE_CUSTOMER: {
          configStrValue: {}
        },
        WECHAT_QRCODE_IMAGE_SWITCH: {

        }
      },
      srcList: [],

      productRegisterUrl: '',

      ifRegisterEffect: false,

      stateColor: {}, // 用户工作状态颜色

      currGuideType: '',

      previewSrcList: [guideImage],

      customerGuidanceConfigBackUp: void 0,

      registerApproverStr: '[]',
      allowUnbindCusProduct: [],
      specialItems: ['REGISTRATION_APPROVE', 'AUTO_UPDATE_PRODUCT', 'AUTO_UPDATE_CUSTOMER'], // 包含开关及人员选择的项，特殊处理

      personsSettingBackUp: { // 备份，用于数据比对
        AUTO_UPDATE_PRODUCT: [],
        AUTO_UPDATE_CUSTOMER: [],
        REGISTRATION_APPROVE: []
      },
      personsSetting: {
        AUTO_UPDATE_PRODUCT: [],
        AUTO_UPDATE_CUSTOMER: [],
        REGISTRATION_APPROVE: []
      },

      registerItems: {},
      dialogVisible: {
        SCAN_CODE_RULE: false
      },
      dialogSaveLoading: {
        SCAN_CODE_RULE: false
      },
      registerNoticeTypes: [{
        label: 'waitAuditTemplateId',
        name: i18n.t('product.setting.registerTypeStatus.type1')
      }, {
        label: 'passTemplateId',
        name: i18n.t('product.setting.registerTypeStatus.type2')
      }, {
        label: 'refuseTemplate',
        name: i18n.t('product.setting.registerTypeStatus.type3')
      }],

      templateShow: true,

      messageTemplate: null,

      scanConfigRules: {
        codeType: { required: true, message: i18n.t('product.setting.scanConfigRules.tips1') },
        codeUrlExample: [
          { required: true, message: i18n.t('product.setting.scanConfigRules.tips2') },
          {
            validator: (rule, value, cb) => {
              if (value.length > 1000) {
                cb(new Error(i18n.t('product.setting.scanConfigRules.tips3')))
              }
              cb()
            },
            trigger: 'blur'
          }
        ],
        identifyParam: [
          { required: true, message: i18n.t('product.setting.scanConfigRules.tips4') },
          {
            validator: (rule, value, cb) => {
              if (value.length > 1000) {
                cb(new Error(i18n.t('product.setting.scanConfigRules.tips3')))
              }
              if (!this.exampleCodeData) {
                cb(new Error(i18n.t('product.setting.scanConfigRules.tips5')))
              }
              cb()
            },
            trigger: 'blur'
          }
        ]
      }
    };
  },
  computed: {
    /** 
     * @description 是否显示触发器
     * 1. 触发器灰度
     * 2. 非纯客服云版本
    */
    isShowTrigger() {
      return isOpenTriggerGray()
    },
    tenantType() {
      return getRootWindowInitData()?.tenantType
    },
    // 60天后不显示，改功能8月12上线
    showNew() {
      return safeNewDate().getTime() < safeNewDate('2021-10-12').getTime();
    },
    /* 是否显示质保信息 */
    isShowQuality() {
      return this._isShowProductQuality
    },
    isAutoUpdateProductChange(){
      return this.ckeckObjArrayDiff(this.personsSettingBackUp.AUTO_UPDATE_PRODUCT,this.personsSetting.AUTO_UPDATE_PRODUCT, 'userId');
    },
    isAutoUpdateCustomerChange(){
      return this.ckeckObjArrayDiff(this.personsSettingBackUp.AUTO_UPDATE_CUSTOMER,this.personsSetting.AUTO_UPDATE_CUSTOMER, 'userId');
    },
    isRegisterApproverChange(){
      return this.ckeckObjArrayDiff(this.personsSettingBackUp.REGISTRATION_APPROVE,this.personsSetting.REGISTRATION_APPROVE, 'userId');
    },
    isRegisterNoticeChange () {
      const config = this.registerItems.REGISTER_NOTICE
      const data = config?.configValue
      const dataBk = JSON.parse(config.configStrValue)
      return data.waitAuditTemplateId !== dataBk.waitAuditTemplateId ||
        data.passTemplateId !== dataBk.passTemplateId ||
        data.refuseTemplate !== dataBk.refuseTemplate
    },
    exampleCodeData() {
      const data = this.registerItems.SCAN_CODE_RULE?.configValue || {}
      if (data.codeType !== 'url' || !data.codeUrlExample || !data.identifyParam) return ''
      const urlSerach = (data.codeUrlExample || '').split('?')[1] || ''
      return new URLSearchParams(urlSerach).get(data.identifyParam) || ''
    },
    guideSetting(){
      return {
        quality: {
            title: this.$t('product.setting.guideSetting.title'),
            moreUrl: this.tenantType == 2 ? 'https://docs.qq.com/doc/p/ff09ae8df6a4462b93b1ef216d8f530bdbe632c4?dver=2.1.27237808' : ( this.tenantType == 4 ? 'https://publink-hz.feishu.cn/wiki/wikcnlfgvwd8cb5uy9KrIum0aXb' : 'https://doc.shb.ltd/shb_xoqazk/ia4qctp2ly559d1m/wtr4x2a43al72qae/ge6p2uecxuvng1c2.html'),
            steps: [
              {
                type: "image",
                url: "https://she-dev.oss-cn-hangzhou.aliyuncs.com/acs/newfiles/7416b42a-25cc-11e7-a500-00163e12f748/202109/0999c9f9-9a14-4969-96a3-eb91d8b1ed49.jpg",
                text: this.$t('product.setting.guideSetting.des1'),
              },
              {
                type: "video",
                url: "https://she-dev.oss-cn-hangzhou.aliyuncs.com/acs/newfiles/7416b42a-25cc-11e7-a500-00163e12f748/202109/edc2e02f-c218-48f7-9020-e8d35252c7f1.mp4",
                text: this.$t('product.setting.guideSetting.des2'),
              },
              {
                type: "video",
                url: "https://she-dev.oss-cn-hangzhou.aliyuncs.com/acs/newfiles/7416b42a-25cc-11e7-a500-00163e12f748/202109/e91dd8c8-3442-48d8-bb1c-c61a2e483464.mp4",
                text: this.$t('product.setting.guideSetting.des3'),
              },
              {
                type: "video",
                url: "https://she-dev.oss-cn-hangzhou.aliyuncs.com/acs/newfiles/7416b42a-25cc-11e7-a500-00163e12f748/202109/24ba407d-cb4a-4d4f-9725-5b434742cb99.mp4",
                text: this.$t('product.setting.guideSetting.des4'),
              },
              {
                type: "video",
                url: "https://she-dev.oss-cn-hangzhou.aliyuncs.com/acs/newfiles/7416b42a-25cc-11e7-a500-00163e12f748/202109/e8b35503-d78b-4c3e-807f-0d90989f90fd.mp4",
                text: this.$t('product.setting.guideSetting.des5'),
              },
            ]
          },
        register: {
          title: this.$t('product.setting.guideSetting.title2'),
          moreUrl: this.tenantType == 4 ? 'https://publink-hz.feishu.cn/wiki/wikcnvqkkUZPaDhYnZuyoI2w3Oe' : 'https://doc.shb.ltd/shb_xoqazk/ia4qctp2ly559d1m/wtr4x2a43al72qae/mqxh7zbix4cm038a.html',
          steps: [
            {
              type: "video",
              url: "https://shb-pro.oss-cn-hangzhou.aliyuncs.com/shb-resource/%E5%8A%9F%E8%83%BD%E4%BB%8B%E7%BB%8D1.mp4",
              text: this.$t('product.setting.guideSetting.des6'),
            },
            {
              type: "video",
              url: "https://shb-pro.oss-cn-hangzhou.aliyuncs.com/shb-resource/%E5%8A%9F%E8%83%BD%E4%BB%8B%E7%BB%8D2.mp4",
              text: this.$t('product.setting.guideSetting.des7'),
            },
            {
              type: "video",
              url: "https://shb-pro.oss-cn-hangzhou.aliyuncs.com/shb-resource/%E5%8A%9F%E8%83%BD%E4%BB%8B%E7%BB%8D3.mp4",
              text: this.$t('product.setting.guideSetting.des8'),
            },
            {
              type: "video",
              url: "https://shb-pro.oss-cn-hangzhou.aliyuncs.com/shb-resource/%E5%8A%9F%E8%83%BD%E4%BB%8B%E7%BB%8D4.mp4",
              text: this.$t('product.setting.guideSetting.des9'),
            },
            {
              type: "video",
              url: "https://shb-pro.oss-cn-hangzhou.aliyuncs.com/shb-resource/%E5%8A%9F%E8%83%BD%E4%BB%8B%E7%BB%8D5.mp4",
              text: this.$t('product.setting.guideSetting.des10'),
            },
            {
              type: "video",
              url: "https://shb-pro.oss-cn-hangzhou.aliyuncs.com/shb-resource/%E5%8A%9F%E8%83%BD%E4%BB%8B%E7%BB%8D6.mp4",
              text: this.$t('product.setting.guideSetting.des11'),
            },
          ]
        }
      }
    },
    isHasModuleKey() {
      return this.$route.query.moduleKey
    }
  },
  watch: {
    isOpenQuality: {
      handler(newValue) {
        this.qualitySwitch = newValue;
      },
      immediate: true
    },
    exampleCodeData() {
      this.$refs['scanConfigForm'] && this.$refs['scanConfigForm'].validateField('exampleCodeData')
    }
  },
  async mounted() {
    // this.isDisplay()
    this.getProductRegisterConfigGray()
    // this.getAllotList();
    // this.getProductMenu();
    this.productConfig();
    this.getTenantConfig()
    this.fetchTemplate()
    let res = await getFirstSettingUnBindCustomer();
    if (res.code === 0) {
      this.hasSettingUnBindCustomer = res.result;
    }
    this.$nextTick(() => {
      if (
        storageGet(PRODUCT_PRODUCT_SET)
        && storageGet(PRODUCT_PRODUCT_SET) > 0
      )
        return this.$Guide().destroy('product-set-guide');
      this.$Guide(
        [
          {
            content: '',
            haveStep: false,
            gStyle: 'top:35px',
            id: 'product-set-guide',
            domId: 'product-set-guide-1',
            finishBtn: 'OK',
            diyContent: true,
            diyContentDom: GuideContent
          }
        ],
        0,
        '',
        e => {
          return new Promise((resolve, reject) => {
            resolve();
          });
        }
      )
        .create()
        .then(res_ => {
          if (res_) storageSet(PRODUCT_PRODUCT_SET, '1');
        });
    });
    this.registerUrl()
    
    this.$nextTick(()=>{
      // 页面滚动
      const { type } = parse(window.location.search) || {};
      if(type && !this.isHasModuleKey) {
        this.scrollToView(`.module-scroll-${type}`);
      }
    })
  },
  methods: {
    checkModuleUrlMatch,
    scrollToView(className){
      setTimeout(() => {
        const target = document.querySelector(className);
        target && target.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
          inline: 'start',
        });
      }, 500);
    },
    scanConfigSave(type, secType) {
      this.$refs['scanConfigForm'].validate((valid) => {
        if (valid) {
          this.registerSaveEach(type, secType)
        } else {
          return false;
        }
      })
    },
    fetchTemplate() {
      this.$http.get('/vipsms/getRegisterTemplate')
        .then(res => {

          this.messageTemplate = res.data || []
        })
        .catch(err => {
          console.error('fetchTemplate', err);
        })
      this.checkTemplate()
    },
    checkTemplate() {
      if(!this.messageTemplate || !this.registerItems.REGISTER_NOTICE) return
      const configValue = this.registerItems.REGISTER_NOTICE.configValue
      const { waitAuditTemplateId, passTemplateId, refuseTemplate } = configValue
      if(!this.messageTemplate.find(v => v.id === waitAuditTemplateId)) {
        configValue.waitAuditTemplateId = ''
      }
      if(!this.messageTemplate.find(v => v.id === passTemplateId)) {
        configValue.passTemplateId = ''
      }
      if(!this.messageTemplate.find(v => v.id === refuseTemplate)) {
        configValue.refuseTemplate = ''
      }
    },
    ckeckObjArrayDiff(arr = [], arrInitial = [], mainField) {
      let isDifferent = false;
      if(arr.length !== arrInitial.length) {
        isDifferent = true;
      } else {
        isDifferent = !!arr.find(obj => !arrInitial.find(obj2 => obj[mainField] === obj2[mainField]))
      }
      return isDifferent;
    },
    imgBtnClick() {
      this.$refs.viewImg.clickHandler()
    },
    toGuideConfigDialog() {
      this.customerGuidanceConfigBackUp = { ...this.register.CUSTOMER_GUIDANCE.configStrValue }
      this.customersRegisterDialogVisible = true
    },
    toScanConfigDialog() {
      this.dialogVisible['SCAN_CODE_RULE'] = true
    },
    registerUrl() {
      registerUrl().then(res => {
        if (res.code !== 0) {
          this.$platform.notification({
            title: this.$t('common.base.toast'),
            message: r.message || '',
            type: 'error'
          });
          return
        }
        this.productRegisterUrl = res.result
      }).catch(err => {
        this.$platform.notification({
          title: this.$t('common.base.toast'),
          message: this.$t('product.setting.getRegisterUrlErrorTips'),
          type: 'error'
        });
      })
    },
    keyLimit(e) {
      if (e.key === 'Enter') {
        e.target.blur()
      } else if(/\D/.test(e.key) && ![8, 37, 38, 39, 40, 46].includes(e.keyCode)) {
        e.preventDefault();
        return false
      }
    },
    //查看示例
    viewExamples(){
      this.isShowExImage = true
      // this.srcList.push(this.register.CUSTOMER_GUIDANCE.configStrValue.pic_url)
    },
    //关闭示例
    handleClose(){
      this.isShowExImage = false
    },
    //获取图片地址
    getImgUrl(vl){
      this.register.CUSTOMER_GUIDANCE.configStrValue.pic_url = vl.url
    },
    removePic() {
      this.register.CUSTOMER_GUIDANCE.configStrValue.pic_url = ''
    },
    onCopy(text) {
      let input = document.createElement('input')
      input.value = text
      input.setAttribute('style', 'position:fixed;top:-100%')
      document.body.appendChild(input)
      input.select()
      document.execCommand('copy')
      input.remove()
      this.$message.success(this.$t('common.base.tip.copySuccess'));
    },
    /*
    产品注册 start
    */
    isDisplay(init) {
      isDisplay()
        .then(r => {
          if (r.status == 0) {
            this.ifRegisterEffect = !!r.data;
            if (r.data) {
              this.initializeStateColor();
              return getConfig({
                configCode: [
                  'PRODUCT_REGISTRATION',
                  'CUSTOMER_GUIDANCE',
                  'REGISTRATION_LIMIT',
                  'REGISTRATION_APPROVE',
                  'REGISTER_WITHOUT_SYS',
                  'AUTO_UPDATE_PRODUCT',
                  'AUTO_UPDATE_CUSTOMER',
                  'WECHAT_QRCODE_IMAGE_SWITCH',
                  'REGISTER_NOTICE',
                  'SCAN_CODE_RULE'
                ]
              });
            }
          }
        })
        .then(r => {
          if (!r) return
          if (r.status == 0) {
            r.data.forEach(item => {
              const code = item.configCode
              if (['REGISTRATION_APPROVE'].includes(code)) {
                if(item.configStrValue === '{}') item.configStrValue = '[]'
                this.personsSettingBackUp.REGISTRATION_APPROVE = JSON.parse(item.configStrValue)
                this.personsSetting.REGISTRATION_APPROVE = JSON.parse(item.configStrValue)
                this.personsSetting.REGISTRATION_APPROVE.forEach(v => {
                  v.label = v.displayName
                  v.value = v.userId
                })
              }
              if (code.includes('AUTO_UPDATE')) {
                let user = item.configStrValue && item.configStrValue !== '{}' ? JSON.parse(item.configStrValue) : null
                if(user) {
                  user.label = user.displayName
                  user.value = user.userId
                }
                this.personsSettingBackUp[code] = user ? [user] : []
                this.personsSetting[code] = user ? [user] : []
                // item.configStrValue.userId = item.configStrValue.userId ? [item.configStrValue.userId] : []
              }
              if (['CUSTOMER_GUIDANCE'].includes(code)) {
                item.configStrValue = JSON.parse(item.configStrValue) || {};
              }
              if (['SCAN_CODE_RULE', 'REGISTER_NOTICE'].includes(code)) {
                this.registerDeal(item)
                return
              }
              this.register[code] = item;
            });
          }
        });
    },
    registerSave(type, secType) {
      if(this.register.REGISTRATION_LIMIT.isOpen){
        if(!this.register.REGISTRATION_LIMIT.configNumValue){
          this.register.REGISTRATION_LIMIT.configNumValue = 1
        }
        if(this.register.REGISTRATION_LIMIT.configNumValue){
          if(this.register.REGISTRATION_LIMIT.configNumValue > 9999){
              this.$platform.toast(this.$t('product.setting.registerMaxCountTips'), 'warning');
               this.register.REGISTRATION_LIMIT.configNumValue = 9999
              return;
          }
        }
      }
      for(let key of this.specialItems) {
        if (type === key && this.register[type].isOpen && !this.personsSetting[type]?.length) {
          let tips = {
            switch: this.$t('product.setting.aboutRegisterTips1'),
            user: this.$t('product.setting.aboutRegisterTips2')
          }
          if (type === 'REGISTRATION_APPROVE') {
            tips = {
              switch: this.$t('product.setting.aboutRegisterTips3'),
              user: this.$t('product.setting.aboutRegisterTips4')
            }
          }
          tips[secType] && this.$platform.toast(tips[secType], 'warning');
          return;
        }
      }
      let arr = [], obj = JSON.parse(JSON.stringify(this.register));

      for (let i in obj) {
        let code = obj[i].configCode

        // 携带人员选择的特殊项如果非当前项，就不传参，否则容易有问题
        let toContinue = false
        for (let key of this.specialItems) {
          if (code === key && type !== key) {
            toContinue = true
            continue
          }
        }
        if (toContinue) continue

        if (['CUSTOMER_GUIDANCE'].includes(code)) {
          obj[i].configStrValue = JSON.stringify(obj[i].configStrValue);
        }
        for(let key of ['AUTO_UPDATE_PRODUCT', 'AUTO_UPDATE_CUSTOMER']) {
          if (code === key) {
            let data = this.personsSettingBackUp[key][0] || {}
            if (secType === 'user') {
              let {userId, staffId, displayName} = this.personsSetting[key][0] || {}
              data = {
                userId,
                staffId,
                displayName
              }
            }

            obj[i].configStrValue = JSON.stringify(data)
          }
        }

        if(['REGISTRATION_APPROVE'].includes(code)) {
          let data = this.personsSettingBackUp.REGISTRATION_APPROVE || []
          if (secType === 'user') {
            data = this.personsSetting.REGISTRATION_APPROVE || []
            data = data.map(v => ({
              userId: v.userId,
              staffId: v.staffId,
              displayName: v.displayName
            }))
          }
          obj[i].configStrValue = JSON.stringify(data)
        }
        arr.push(obj[i]);
      }
      setConfig(arr).then(r => {
        if (r.status == 0) {
          if(this.customersRegisterDialogVisible) {
            // this.customersRegisterDialogClosed();
            this.customersRegisterDialogVisible = false;
          }
          this.isDisplay(true);
        } else {
          this.$platform.notification({
            title: this.$t('common.base.tip.saveFail'),
            message: r.message || '',
            type: 'error'
          });
        }
      });

      // if (
      //   (type === 'REGISTRATION_APPROVE' && secType === 'switch' && this.register.REGISTRATION_APPROVE.isOpen)
      //   // || (type === 'registerSwitchAll' && this.register.PRODUCT_REGISTRATION.isOpen)
      // ) {
      //   this.$nextTick(() => {
      //     window.scrollTo(document.documentElement.clientWidth, document.documentElement.clientHeight);
      //   })
      // }
    },
    customersRegisterDialogClosed() {
      // this.$refs.customersFormRef.resetFields();
      this.register.CUSTOMER_GUIDANCE.configStrValue = this.customerGuidanceConfigBackUp
      // this.customersRegisterDialogVisible = false;
    },

    // 扫码设置弹框关闭
    scanConfigDialogClosed() {
      const config = this.registerItems.SCAN_CODE_RULE
      config.configValue = JSON.parse(config.configStrValue)
    },

    // 产品注册配置单独保存（SCAN_CODE_RULE， REGISTER_NOTICE）
    registerSaveEach(type, secType) {
      let arr = [], obj = { ...this.registerItems[type] };

      if (!obj) return

      if (secType && secType !== 'switch') {
        obj.configStrValue = JSON.stringify(obj.configValue)
        delete obj.configValue
      }
      
      // arr.push(this.register.find(v => v.configCode === 'PRODUCT_REGISTRATION'))
      arr.push(obj);

      if (secType === 'dialog') {
        this.dialogSaveLoading[type] = true
      }

      setConfig(arr).then(r => {
        if (r.status == 0) {
          this.registerUpdateEach(type);
          if (secType === 'dialog') {
            this.dialogVisible[type] = false
            this.dialogSaveLoading[type] = false
          }
        } else {
          this.$platform.notification({
            title: '保存失败',
            message: r.message || '',
            type: 'error'
          });
        }
      })
    },

    // 产品注册配置单独更新
    registerUpdateEach (type, secType) {
      getConfig({
        configCode: [type]
      }).then(r => {
        if (!r) return
        if (r.status == 0) {
          let data = r.data[0]
          if (!data) return

          this.registerDeal(data)
        }
      })
    },

    // 产品注册数据处理（SCAN_CODE_RULE， REGISTER_NOTICE）
    registerDeal(config) {
      switch (config.configCode) {
        default:
          this.registerDefaultDeal(config)
          break;
      }
      if (config.configCode === 'REGISTER_NOTICE') {
        this.checkTemplate()
      }
    },

    // 产品注册配置默认处理方式
    registerDefaultDeal(config) {
      const code = config.configCode
      config.configValue = JSON.parse(config.configStrValue)
      this.$set(this.registerItems, code, config)
      // this.registerItems[code] = config
      // this.personsSettingBackUp[code] = config.configStrValue
    },
    /*
   产品注册 end
   */
    /* --------------------- 其他 ---------------------*/
    async getTenantConfig() {
      const params = {
        configCode: [
          'UNBINDCUS_PRODUCT_AUTHORITY',
          'IMPORT_PRODUCT_UPDATE_IF_EXIST',
          'productMenuSplit'
        ]
      };
      let [err, res] = await asyncErrorCaptured(getTenantConfig, [params]);
      if (err) return
      if (res.status !== 0) {
        this.$platform.alert(res.message);
        return
      }
      res.data?.forEach(config => {
        const {configCode, configNumValue, isOpen} = config;
        switch(configCode) {
          case 'UNBINDCUS_PRODUCT_AUTHORITY': 
            if (configNumValue === 3) {
              this.allowUnbindCusProduct = [1, 2]
            } else if (configNumValue) {
              this.allowUnbindCusProduct.push(configNumValue)
            }
            break;
          case 'IMPORT_PRODUCT_UPDATE_IF_EXIST':
            this.productConfigSwitch[configCode] = !!isOpen
            break;
          case 'productMenuSplit':
            this.productMenuSplit = !!isOpen
            break;
          default:
            break;
        }
      })
    },
    async editTenantConfig() {
      const params = [{
        configCode: 'UNBINDCUS_PRODUCT_AUTHORITY',
        configNumValue: this.allowUnbindCusProduct.length === 2 ? 3 : (this.allowUnbindCusProduct[0] || 0),
        configStrValue: '{}',
        isOpen: 0
      }];
      let [err, res] = await asyncErrorCaptured(editTenantConfig, [params]);
      if (err) return
      if (res.status !== 0) {
        this.$platform.alert(res.message);
        return
      }
      let data = (res.data || [])[0]?.configNumValue
      if (data === 3) {
        this.allowUnbindCusProduct = [1, 2]
      } else if (data) {
        this.allowUnbindCusProduct.push(data)
      }
    },
    
    async productIfSameSnUpdateProductChange() {
      const params = [{
        configCode: 'IMPORT_PRODUCT_UPDATE_IF_EXIST',
        configNumValue: 0,
        configStrValue: '{}',
        isOpen: this.productConfigSwitch.IMPORT_PRODUCT_UPDATE_IF_EXIST ? 1 : 0
      }];
      editTenantConfig(params)
    },
    async configSave(obj) {
      const params = {
        state: obj.state,
        flow: obj.flow
      };
      let res = await saveFunc(params);
      if (res.status === 0) {
        this.productConfig();
      } else {
        this.$platform.alert(res.message);
      }
    },
    // 产品二维码设置弹窗
    openQrcodeDialog() {
      this.$refs.QrcodeGuide.open();
    },
    // 启用/禁用 功能设置
    async saveFunc(state, flow) {
      const params = {
        state,
        flow
      };
      if (flow === 'qrcodeEnabled' && state) {
        await this.openQrcodeDialog();
      } else if (
        flow === 'productCreateUnBindCustomer'
        && state
        && !this.hasSettingUnBindCustomer
      ) {
        this.$confirm(
          this.$t('product.setting.saveFuncTips'),
          this.$t('common.base.toast'),
          {
            confirmButtonText: '',
            cancelButtonText: this.$t('common.base.cancel'),
            type: 'warning'
          }
        )
          .then(async () => {
            this.hasSettingUnBindCustomer = true;
            let res = await saveFunc(params);
            if (res.status === 0) {
              this.productConfig();
            } else {
              this.$platform.alert(res.message);
            }
          })
          .catch(err => {
            this.productCreateUnBindCustomer = false;
            console.error(err);
          });
      } else if (flow === 'productMenuSplit') {
        state = state == true ? 1 : 0
        this.setProductMenuSplit(state)
      } else {
        let res = await saveFunc(params);
        if (res.status === 0) {
          this.productConfig();
        } else {
          this.$platform.alert(res.message);
        }
      }
    },
    // 设置产品目录分级展示
    setProductMenuSplit(state) {
      editTenantSingleConfig({
        configCode: 'productMenuSplit',
        isOpen: state
      }).then(res => {
        if (res.status !== 0) {
          this.productMenuSplit = !state
          this.$platform.alert(res.message)
        }
      })
    },
    // 获取产品类型选项
    // async getProductMenu() {
    //   let res = await getProductMenu();
    //   if (res.code === 0) {
    //     this.productMenuList = res.result;
    //   }
    // },
    // async moveMenu(id, moveType) {
    //   let res = await moveProductType({ id, moveType });
    //   if (res.code === 0) {
    //     // this.$message({
    //     //   showClose: true,
    //     //   duration: 1500,
    //     //   message: '排序成功',
    //     //   type: "success",
    //     // });
    //     this.getProductMenu();
    //   } else {
    //     this.$message({
    //       showClose: true,
    //       duration: 1500,
    //       message: res.message,
    //       type: 'error'
    //     });
    //   }
    // },
    // async deleteProductMenu(id) {
    //   try {
    //     if (!(await this.$platform.confirm('确认删除该产品类型选项？'))) return;

    //     const params = { ids: [id] };
    //     const result = await delTreeList(params);
    //     if (result.success) {
    //       this.$message({
    //         showClose: true,
    //         duration: 1500,
    //         message: '删除成功',
    //         type: 'success'
    //       });
    //       this.getProductMenu();
    //     } else {
    //       this.$message({
    //         showClose: true,
    //         duration: 1500,
    //         message: result.message,
    //         type: 'error'
    //       });
    //     }
    //   } catch (error) {
    //     console.error('delete remark error', error);
    //   }
    // },
    // createProductMenu() {
    //   this.$refs.FormDialog.openDialog('create');
    // },
    // editProductMenu(row) {
    //   this.$refs.FormDialog.openDialog('edit', row);
    // },
    getProductRegisterConfigGray() {
      getQualityConfigGray({
        code: 'PRODUCT_REGISTER'
      }).then(res => {
        if(res.errorCode === 0 && res.data.PRODUCT_REGISTER) {
          this.isDisplay()
          if (!localStorage.getItem('isRegisterGuidance')) {
            this.saveRegisterPoppe(true)
          }
        }
      })
    },
    // 获取开关状态
    async productConfig() {
      let res = await productConfig();
      if (res.status === 0) {
        this.productSearchOnMobile = res.data.productConfig.productSearchOnMobile;
        this.qrcodeEnabled = res.data.productConfig.qrcodeEnabled;
        this.productCreateUnBindCustomer = res.data.productConfig.productCreateUnBindCustomer;
      } else {
        this.$platform.alert(res.message);
      }
    },
    // 启用/禁用 质保功能设置（因为服务端不想复用之前的接口，所以要新开接口）
    async qualitySave(state, configCode) {
      const params = {
        state,
        configCode
      };
      let res = await qualitySave(params);
      if (res.errorCode !== 0) {
        this.$platform.alert(res.message);
      }
    },
    // 打开标签页
    openTab(pageType) {
      openAccurateTab({
        type: PageRoutesTypeEnum[pageType],
      })
    },
    SavePoppe() {
      this.openGuidanceDialog('quality')
      localStorage.setItem('isGuidance', 1);
      this.isPopover = false;
    },
    saveRegisterPoppe(setStorage) {
      this.openGuidanceDialog('register')
      setStorage && localStorage.setItem('isRegisterGuidance', 1);
    },
    openGuidanceDialog(type) {
      this.currGuideType = type
      this.$nextTick(() => {
        this.$refs.GuidanceDialog.visible = true;
      })
    },
    /** 初始化工作状态的颜色 */
    initializeStateColor() {
      http
        .get("/setting/getStateColorMap")
        .then(
          (res) => (this.stateColor = _.assign({}, this.stateColor, res || {}))
        )
        .catch((err) => console.error(err));
    },
    // 查询注册审核用户列表
    fetchApprovers(params) {
      let { keyword, pageNum } = params
      this.params = {
        pageNum,
        keyword,
      }

      return registerApproverList(this.params).then((res) => {
        if (!res || !res.list) return;

        res.list = res.list.map((item) =>
          Object.freeze({
            label: item?.displayName || "",
            value: item?.userId || "",
            ...item,
          })
        )

        return res;
      })
    },
    // 查询用户列表
    fetchUsers(params) {
      let { keyword, pageNum } = params
      this.params = {
        pageNum,
        keyword,
      }

      return userList(this.params).then((res) => {
        if (!res || !res.list) return;

        res.list = res.list.map((item) =>
          Object.freeze({
            label: item?.displayName || "",
            value: item?.userId || "",
            ...item,
          })
        )

        return res;
      })
    },
  },
  directives: {
    'el-select-loadmore': {
      bind(el, binding) {
        const SELECT_DOM = el.querySelector(
          '.select-loadmore .el-select-dropdown__wrap'
        );
        SELECT_DOM.addEventListener('scroll', () => {
          const condition = SELECT_DOM.scrollHeight - SELECT_DOM.scrollTop <= SELECT_DOM.clientHeight;
          if (condition) {
            binding.value();
          }
        });
      }
    },
    'catalog-loadmore': {
      bind(el, binding) {
        const SELECT_DOM = el.querySelector(
          '.catalog-loadmore .el-select-dropdown__wrap'
        );
        SELECT_DOM.addEventListener('scroll', () => {
          const condition = SELECT_DOM.scrollHeight - SELECT_DOM.scrollTop <= SELECT_DOM.clientHeight;
          if (condition) {
            binding.value();
          }
        });
      }
    }
  },
  created() {
    if (localStorage.getItem('isGuidance')) {
      this.isPopover = false;
    }
  }
};
</script>
<style lang="scss" scoped>
 .product-trigger-box{
  background: #fff;
  min-height: 100vh;
  .trigger-list-setting{
    height: 100%;
  }
}
.align-base {
  align-items: baseline !important;
}
.subscript {
  position: relative;
}
.subscript-product {
  background-color: rgb(233, 106, 62);
  border-radius: 2px;
  width: 30px;
  height: 15px;
  background-size: contain;
  position: absolute;
  top: -8px;
  margin-left: 5px;
  line-height: 13px;
  font-weight: 300;
  text-align: center;
  font-size: 12px;
  color: #fff;
  &::after {
    content: '';
    position: absolute;
    top: 12px;
    left: -2px;
    border-width: 0 4px 4px;
    border-style: solid;
    border-color: transparent transparent rgb(233, 106, 62);
    transform: rotate(90deg);
  }
}
.el-radio {
  margin-right: 24px;
}
::v-deep .el-radio__label {
  padding-left: 5px;
}
.pointer {
  cursor: pointer;
}
.mar-btm-10 {
  margin-bottom: 10px;
}
.border-btm {
  padding-bottom: 8px;
  margin-bottom: 10px;
  &:not(.is-close) {
    border-bottom: 1px solid #eee;
  }
  &.mar-btm-0 {
    margin-bottom: 0;
  }
}
.mar-top-52 {
  margin-top: 52px;
}
.mar-top-12 {
  margin-top: 12px;
}
.mar-top-5 {
  margin-top: 5px;
}
.pad-bottom-10 {
  padding-bottom: 10px;
}
.pad-top-0 {
  padding-top: 0;
}
.pad-bottom-0 {
  padding-bottom: 0;
}
.select-popper {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.desk-setting-table {
  .desk-setting-table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }

  i {
    cursor: pointer;
    font-size: 13px;
    color: #909399;
  }
}

.template-wrap {
  background: $bg-color-l2;
  padding: 20px;
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;

  .template-item {
    margin-right: 20px;
    width: 200px;
    height: 420px;
    cursor: pointer;
    position: relative;

    .template-name {
      margin-top: 10px;
      margin-bottom: 0;
      color: $text-color-primary;
    }
    .template-ass {
      display: flex;
      justify-content: space-between;
      margin-top: 5px;

      ::v-deep .el-button {
        padding: 0;
        font-size: 12px;
      }
      .hasBind {
        font-size: 12px;
        color: $text-color-secondary;
      }
    }

    .template-son {
      height: 340px;
    }
    .template-default {
      overflow: hidden;
      position: relative;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
    }

    .template-blank {
      border: 1px dashed $color-border-l1;
      background-color: #fff;
      font-size: 16px;
      color: $text-color-gray;
      text-align: center;

      i {
        font-size: 30px;
        display: block;
        margin-top: 120px;
      }
    }
    .default-info {
      color: $text-color-secondary;
    }
    .template-mark {
      position: absolute;
      width: 200px;
      top: 0;
      background-color: #00000066;
      display: flex;
      align-items: center;
      justify-content: space-around;
      padding: 0 20px;
      .mark-bg {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: #00000033;
        color: #fff;
        text-align: center;
        line-height: 30px;
      }
    }
  }
}
.warning {
  background-color: #fffbe6;
  border: 1px solid #ffe58f;
  border-radius: 2px;
  color: $text-color-regular;
  padding: 12px;
  display: flex;
  width: 100%;

  i {
    color: #ffe58f;
    font-size: 24px;
    margin-right: 10px;
    transform: translateX(-6px);
  }

  .warning-input {
    width: 160px;
  }
}
::v-deep .popperclass {
  width: 206px;
  right: 0px;
  top: 30px;
}
.poppeSave {
  text-align: center;
  margin-top: 15px;
}
.poppeText {
  text-align: center;
}

.check-box {
  ::v-deep .el-checkbox {
    margin-top: 5px;
    display: block;
  }
  
  ::v-deep .el-checkbox__label {
    font-size: 12px;
  }
}

.common-item {
  &-label {
    line-height: 14px;
  }
  &-tips {
    font-size: 14px;
    color: #8C8C8C;
    line-height: 14px;
    margin-top: 12px;
  }
}

.group-header {
  margin-top: 10px;
  padding: 16px;
  border-bottom: 1px solid #E8E8E8;
  &.none{
    margin-top: 0;
  }
  &-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    line-height: 16px;
  }
  // &-tips {
  //   font-size: 14px;
  //   color: #8C8C8C;
  //   line-height: 14px;
  //   margin-top: 12px;
  // }
}
.group-item {
  background: #fff;
  padding-top: 8px;
  padding-bottom: 4px;
  &-name {
    background: #fff;
    font-size: 14px;
    font-weight: 600;
    color: #262626;
    margin-top: 20px;
    margin-bottom: 8px;
    margin-left: 16px;
    border-left: 2px solid #00C2BE;
    line-height: 1;
    padding-left: 8px;
  }
  &-child {
    padding-left: 16px;
    &-label {
      line-height: 14px;
    }
    // &-tips {
    //   font-size: 14px;
    //   color: #8C8C8C;
    //   line-height: 14px;
    //   margin-top: 12px;
    // }
  }
}
.group-box {
  .group-item:not(:first-child) {
    border-top: 1px dashed #D9D9D9;
  }
}

.sms-select {
  margin-top: 16px;
  .sms-select-item:not(:first-child) {
    margin-top: 12px;
  }
  .sms-select-item {
    &-label {
      font-size: 14px;
      font-weight: 600;
      color: #262626;
      line-height: 20px;
    }
    &-tips {
      // width: 130px;
      text-align: right;
      font-size: 14px;
      color: #595959;
      line-height: 22px;
    }
    &-input {
      width: 300px;
    }
  }
  .sms-select-save {
    margin: 24px 0 8px 74px;
  }
}


</style>

<style lang="scss">
.product_register_input.el-input--small {
  .el-input__inner {
    height: 28px;
    line-height: 28px;
  }

  width: 90px;
  input::-webkit-outer-spin-button, input::-webkit-inner-spin-button { -webkit-appearance: none; }
  input[type="number"]{ -moz-appearance: textfield; }
}
.el-image-viewer__img {
  width: auto;
  height: auto;
}

.product-trigger {
  .empty {
    width:100%;
    height:12px;
    background: #ecf0f5;
  }
  .trigger-list-setting {
    padding: 20px;
    .trigger-list-setting-header-sub-title {
      width: auto; 
    }
    .trigger-list {
      @include dynamic-card-list(1, 'trigger-list-item');
      .trigger-list-item {
        max-width: inherit;
      }
    }
  }
}
</style>
<style lang="scss" scoped>
::v-deep .el-upload-list--picture-card .el-upload-list__item {
  overflow: hidden;
  background-color: #fff;
  border: 1px solid #c0ccda;
  border-radius: 6px;
  box-sizing: border-box;
  width: 104px;
  height: 104px;
  margin: 0 8px 8px 0;
  display: inline-block;
}
::v-deep .el-upload--picture-card {
  width: 104px;
  height: 104px;
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  text-align: center;
  line-height: 50px;
  i {
    font-size: 24px;
    color: #8c939d;
  }
  .el-icon-plus {
    display: block;
    margin: 28px auto 0;
  }
  .textarea-guide{
    width: 87%;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #595959;
    line-height: 22px;
  }
}
.register-guide-dialog {
  ::v-deep .el-upload--picture-card {
    margin-bottom: 19px;
  }
}
::v-deep .el-upload {
  display: inline-block;
  outline: 0;
}
::v-deep .avatar-uploader .el-upload:hover {
  border-color: #d9d9d9;
}
.textarea-guide {
  width: 100%;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #bfbfbf;
  line-height: 22px;
}
.el-proposal-text {
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.65);
  margin-bottom: 4px;
}
.customerGuide ul,
li {
  padding: 0;
  list-style: none;
}
.uploadTips {
  margin-top: -8px;
  margin-bottom: 4px;
  .tips {
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #8C8C8C;
    line-height: 20px;
  }

}
  .image-view{
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center; 
    // height:500px;
    overflow-y: auto;
  }
.el-example-text {
  cursor: pointer;
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: $color-primary-light-6;
  line-height: 17px;
}

.register-guide-dialog, .register-dialog {
  ::v-deep .el-dialog__header {
    padding: 16px 20px;
    .el-dialog__headerbtn {
      top: 0px;
    }
  }
}
::v-deep .register-guide-dialog {
  .el-dialog__body {
    padding: 14px 20px 22px;
  }
}
::v-deep .register-example-dialog {
  .el-dialog__body {
    padding: 11px 126px 32px;
  }
}

.user-type-select {
  display: inline-block;
  vertical-align: middle;
  ::v-deep .el-select {
    width: 387px;
  }
}

.base-select-user-input-item {
  width: 387px;
}

.user-save {
  vertical-align: middle;
  margin-left: 9px;
}
.image-view {
  position: relative;
  .image-view-shadow {
    display: none;
  }
  &:hover .image-view-shadow {
    display: block;
    position: absolute;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    // background: rgba(0,0,0,.5);
    // border-radius: 30px;
    i {
      color: #fff;
      position: absolute;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(0,0,0,.5);
      border-radius: 4px;
      font-size: 22px;
      cursor: pointer;
    }
  }
}

.operation-icon+.operation-icon {
  margin-left: 4px;
}

::v-deep .el-select-dropdown__item {
  padding: 0 20px;
}
</style>
