<template>
  <div class="product-quality-management">
    <div :class="['product-propaganda-page', isHasModuleKey ? 'product-propaganda-page__none' : null]">
      <propaganda-page :propagandaData="productWarranty" v-if="!isOpenSystemIntegrationGray"></propaganda-page>
    </div>
    <product-quality-management-title v-if="isShowQuality"></product-quality-management-title>

    <div class="product-quality-management-service-item mt_12" v-if="isBgiSaas">
      <product-quality-management-table></product-quality-management-table>
    </div>
  </div>
</template>
<script>
import { defineComponent, computed } from 'vue';

import { isEnterpriseEdition } from '@shb-lib/version';

import productQualityManagementTitle from './productQualityManagementTitle.vue';
import productQualityManagementTable from './productQualityManagementTable.vue';
import PropagandaPage from '@src/component/compomentV2/PropagandaPage';

import { getRootWindow } from '@src/util/dom';
/* mixin */
import { VersionControlProductMixin } from '@src/mixins/versionControlMixin'
import { productWarranty } from '@model/enum/PropaganPageEnum'

export default defineComponent({
  name: 'product-quality-management',
  mixins: [VersionControlProductMixin],
  components: {
    productQualityManagementTitle,
    productQualityManagementTable,
    PropagandaPage,
  },
  setup() {
    
    // 华大基因灰度
    const isBgiSaas = computed(() => {
      const RootWindow = getRootWindow(window)
      return Boolean(RootWindow.grayAuth?.bgiSaas)
    })

    // 系统集成灰度
    const isOpenSystemIntegrationGray = computed(() => {
      const RootWindow = getRootWindow(window);
      return Boolean(RootWindow?.grayAuth?.THIRD_APP);
    })
    
    return {
      isBgiSaas,
      productWarranty,
      isOpenSystemIntegrationGray,
    };
  },
  computed: {
    isShowQuality() {
      return this._isShowProductQuality
    },
    isHasModuleKey() {
      return this.$route.query.moduleKey
    }
  }
});
</script>
<style lang="scss" scoped>
.product-quality-management {
  width: 100%;
  height: 100%;
  padding: 12px;
  &-service-item {
    height: calc(100% - 96px);
  }
  .product-propaganda-page{
    width: 100%;
    margin-bottom: 12px;
    display: flex;
    justify-content: end;
    &__none{
      margin: 0;
    }
  }
}
</style>
