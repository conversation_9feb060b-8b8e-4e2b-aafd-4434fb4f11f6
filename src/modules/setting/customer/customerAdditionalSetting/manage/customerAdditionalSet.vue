<template>
  <div id="normal-setting-components-box" class="customer-additional-container" :class="cardType==='product'?'product-additional-container':''">
    
    <template v-if="cardType==='customer'">
      <component :is="cardType === 'customer' ? 'customer-nav-bar' : ''" current="additional" />
    </template>
    
    <div class="customer-additional-right" v-loading="loading">
      <div class="customer-tab-header">
        <div class="customer-tabs">
          <el-tabs v-model="activeTab" @tab-click="switchTab" id="customer-additional-tabs-guide">
            <el-tab-pane name="customer-added" :label="$t('customer.setting.additionalSet.label1')">
              <div class="tabs_msg">{{$t('customer.setting.additionalSet.des1',{data1:showText})}}</div>
            </el-tab-pane>
            <el-tab-pane name="customer-import" :label="$t('customer.setting.additionalSet.label2')">
              <div class="tabs_msg">{{$t('customer.setting.additionalSet.des2',{data1:showText})}}</div>
            </el-tab-pane>
          </el-tabs>
        </div>
        <div class="lh-52" v-if="activeTab=='customer-added'">
          <el-button type="primary" :loading="false" @click="onClickCreateHandler">
            <i class="iconfont icon-add2"></i>{{$t('common.base.create')}}
          </el-button>
          
        </div>
      </div>

      <!-- staxrt 已添加附加组件 -->
      <div class="customer-type-list" v-show="activeTab=='customer-added'" >
        <customer-card-item
          class="customer-type-item"
          v-for="(item, idx) in cardList"
          :key="item.id"
          :card.sync="cardList[idx]"
          :cardType='cardType'
          @statusChange='initCard'
          @edit="onEditConnectorCardHandler"
          @update="onUpdateConnectorCardHandler"
        ></customer-card-item>
      </div>
      <!-- end 已添加附加组件 -->

      <!-- start 从模版库添加 -->
      <div class="customer-type-list" v-show="activeTab=='customer-import'">
        <template-library :card-sys-list="cardSysList" @importSuc='initCard' :cardType='cardType'></template-library>
      </div>
      <!-- end 从模版库添加 -->

      <!-- start 重命名 -->
      <edit-cuspro-name-dialog ref="batchCardnameDialog" :cardType='cardType' @editCardSubmit="refreshDataList"></edit-cuspro-name-dialog>
      <!-- end 重命名 -->
      
      <!-- start 新建附加组件或连接器弹窗 -->
      <ConnectorModuleAddCardDialog
        :visible="isShowNewCreateCardModal"
        @cardCreate="onCardCreateHandler"
        @connectorCreate="onConnectorCreateHandler"
        @close="onCloseNewCreateCardModal"
      />
      <!-- end 新建附加组件或连接器弹窗 -->
      
      <!-- start 新建连接器弹窗 -->
      <ConnectorModuleCreateConnectorDialog
        :title="connectorCreateModalTitle"
        :visible="isShowCreateConnectorModal"
        :bizTypeId="bizTypeId"
        :toBizType="toBizType"
        :fromBizType="fromBizType"
        :fromBizTypeName="fromBizTypeName"
        isShowSelectLanguage
        @close="onCloseCreateConnectorModal"
        @finish="initCard"
      />
      <!-- end 新建连接器弹窗 -->
      
      <!-- start 编辑连接器弹窗 -->
      <ConnectorModuleEditConnectorDialog
        :title="connectorEditModalTitle" 
        :fromBizTypeName="fromBizTypeName"
        :visible="isShowEditConnectorModal"
        :connectorInfo="connectorInfo"
        @close="onCloseEditConnectorModal"
        @finish="initCard"
      />
      <!-- end 编辑连接器弹窗 -->
      
    </div>
  </div>
</template>

<script>
import CustomerNavBar from '../../components/CustomerNavBar';
import ProductNavBar from '@src/modules/setting/productV2/productAdditionalSetting/component/ProductNavBar';
import templateLibrary from '../../components/templateLibrary';
import * as SettingCusProApi from '@src/api/SettingCusProApi';
import CustomerCardItem from '../../components/CustomerCardItem';
import EditCardnameDialog from '../../components/EditCardnameDialog';
import i18n from '@src/locales'
/* mixins */
import { ConnectorModuleCardSettingCustomerMixin, ConnectorModuleCardSettingMixin } from '@src/modules/connector'

export default {
  name:'customer-additional',
  mixins: [ConnectorModuleCardSettingCustomerMixin, ConnectorModuleCardSettingMixin],
  data(){
    return{
      loading:false,
      activeTab:'customer-added',
      cardList:[],
      cardSysList:[]
    }
  },
  inject:['initData'],
  computed:{
    cardType(){
      return this.initData.cardType;
    },
    showText(){
      return this.isCustomer ? i18n.t('common.form.type.customer') : i18n.t('common.form.type.product')
    },
    isCustomer() {
      return this.cardType === 'customer';
    }
  },
  mounted(){
    this.initCard();
  },
  methods:{
    refreshDataList() {
      if(!this.$route.query.moduleKey) return 
      this.initCard()
    },
    onUpdateConnectorCardHandler() {
      this.initCard();
    },
    // 新建
    addTaskCard(){
      this.$refs.batchCardnameDialog.openDialog();
    },
    switchTab(tab){
      if(this.activeTab == tab) return;
      if(this.activeTab == 'customer-added'){
        this.initCard();
      }
      if(this.activeTab == 'customer-import'){
        this.initCardSysList()
      }
    },
    // 获取列表
    async initCard(){
      try{
        let {result,code,message}=await SettingCusProApi.getAllCardList({cardType:this.cardType});
        this.loading=false;
        if(code===0){
          this.cardList=result;
        }else{
          this.$message.error(message);
        }
      }catch(err){
        console.error(err);
        this.loading=false;
      }
    },
    // 获取模板库列表
    async initCardSysList(){
      try{
        let {code,message,result}=await SettingCusProApi.getTemplateList({cardType:this.cardType});
        if(code===0){
          this.cardSysList=[];
          if(this.cardType==='customer'){
            let cardAll=[],quanyi=[],hetong=[],shichang=[],feiyong=[],wuliu=[];
            if(result && result.length>0){
              cardAll=result.slice();
              const textObj = {
                '全部': this.$t('common.base.all'),
                '会员': this.$t('common.base.member2'),
                '合同': this.$t('contract.contract'),
                '市场': this.$t('event.setting.constant.additional_type_tabs.name7'),
                '费用': this.$t('event.setting.constant.additional_type_tabs.name6'),
                '物流': this.$t('task.detail.components.logistics')
              }
              cardAll.forEach(item=>{
                item.typeName = textObj['全部'];
                if(item.templateName==='会员信息'){
                  item['type']='会员';
                  item.typeName = textObj['会员'];
                  quanyi.push(item);
                }else if(item.templateName==='合同信息'){
                  item['type']='合同';
                  item.typeName = textObj['合同'];
                  hetong.push(item);
                }else if(item.templateName==='跟进记录'){
                  item['type']='市场';
                  item.typeName = textObj['市场'];
                  shichang.push(item);
                }else if(item.templateName==='商机管理'){
                  item['type']='市场';
                  item.typeName = textObj['市场'];
                  shichang.push(item);
                }else if(item.templateName==='回款记录' || item.templateName==='开票记录' || item.templateName==='发票信息'){
                  item['type']='费用';
                  item.typeName = textObj['费用'];
                  feiyong.push(item);
                }else if(item.templateName==='发货记录'){
                  item['type']='物流';
                  item.typeName = textObj['物流'];
                  wuliu.push(item);
                }
              });
              this.cardSysList.push(
                {name: this.$t('common.base.all'),list:cardAll},
                {name: textObj['会员'],list:quanyi},
                {name: textObj['合同'],list:hetong},
                {name: textObj['市场'],list:shichang},
                {name: textObj['费用'],list:feiyong},
                {name: textObj['物流'],list:wuliu});
            }
          }else if(this.cardType==='product'){
            let cardAll=[],caigou=[],shichang=[],zhiliang=[],guige=[];
            if(result && result.length>0){
              cardAll=result.slice();
              const textObj = {
                '全部': this.$t('common.base.all'),
                '采购': this.$t('product.purchase'),
                '市场': this.$t('event.setting.constant.additional_type_tabs.name7'),
                '质量': this.$t('event.setting.constant.additional_type_tabs.name3'),
                '规格': this.$t('product.productType.type.standard')
              }
              cardAll.forEach(item=>{
                item.typeName = textObj['全部'];
                if(item.templateName==='供应商信息' || item.templateName==='合同信息'){
                  item['type']='采购';
                  item.typeName = textObj['采购'];
                  caigou.push(item);
                }else if(item.templateName==='产品价格策略'){
                  item['type']='市场';
                  item.typeName = textObj['市场'];
                  shichang.push(item);
                }
                else if(item.templateName==='巡检方案' || item.templateName==='产品故障信息' || item.templateName==='产品质保信息'){
                  item['type']='质量';
                  item.typeName = textObj['质量'];
                  zhiliang.push(item);
                }
                else if(item.templateName==='配件清单'){
                  item['type']='规格';
                  item.typeName = textObj['规格'];
                  guige.push(item);
                }
              });
              this.cardSysList.push(
                {name:textObj['全部'],list:cardAll},
                {name:textObj['采购'],list:caigou},
                {name:textObj['市场'],list:shichang},
                {name:textObj['质量'],list:zhiliang},
                {name:textObj['规格'],list:guige}
              )
            }
          }
        }else{
          this.$message.error(message)
        }
      }catch(err){
        console.error(err);
      }
    },
    onOpenShowAddCardModal() {
      this.addTaskCard();
    }
  },
  components:{
    CustomerNavBar,
    ProductNavBar,
    CustomerCardItem,
    [EditCardnameDialog.name]:EditCardnameDialog,
    templateLibrary
  }
}
</script>

<style lang="scss">
// tabs标签页
.el-tabs {
  height: 100%;
  display: flex;
  flex-direction: column;

  &__header {
    margin: 0;

    .el-tabs__item {
      padding: 0 24px !important;
      color: $text-color-regular;
      font-weight: normal;

      &.is-active {
        color: $color-primary;
      }
    }
  }
  &__nav-wrap {
    &:after {
      height: 0;
    }
  }

  &__content {
    flex: 1;
    overflow: auto;
  }
}
</style>
<style lang="scss" scoped>
.customer-additional-container {
  display: flex;
  overflow: auto;
  height: 100vh;
  padding: 16px;
  .customer-additional-right {
    width: 100%;
    .customer-tab-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
      padding: 16px;
      width: 100%;
      background: #FFFFFF;
      border-radius: 4px;

      .customer-tabs {
        .tabs_msg {
          width: 763px;
          margin-top: 14px;
          color: #666666;
          line-height: 17px;
          font-size: 12px;

          span {
            color: #333333;
          }
        }
      }
    }

    .lh-52 {
      margin-top: 30px; 
      .el-button{
        padding: 8px 15px;
        .iconfont{
          font-size: 14px;
          margin-right: 8px;
        }
      }
     
    }

    .customer-type-list {
      display: flex;
      flex-flow: wrap;
      align-content: flex-start;
      height: calc(100% - 125px);
      overflow-y: auto;
      @include dynamic-card-list(2, 'customer-type-item');
      .customer-type-item {
        margin: 0 12px 12px 0;
      }
    }
    .customer-type-template{
      height: calc(100% - 108px);
    }
  }
}

// transtion
.flip-list-move {
  transition: transform 0.5s;
}

.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}
</style>