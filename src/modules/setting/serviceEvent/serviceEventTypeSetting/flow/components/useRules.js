import {
  fieldArrayToObject
} from '@src/util/array'
import i18n from '@src/locales'
import { isShowEventConvertTask, getVersionControlMapByWindow } from '@shb-lib/version'

const _versionControlMap = getVersionControlMapByWindow()

export const flowRules = [{
  show: true,
  value: 'start',
  label: i18n.t('event.setting.serviceEventTypeSetting.flow.startState')
}, {
  show: true,
  value: 'finish',
  label: i18n.t('event.setting.serviceEventTypeSetting.flow.finishState')
}, {
  show: isShowEventConvertTask(_versionControlMap),
  value: 'convert2Task',
  label: i18n.t('event.setting.serviceEventTypeSetting.flow.convertTaskState')
},{
  show: true,
  value: 'close',
  label: i18n.t('common.base.close')
}]

/** 流程状态*/
export const flowState = [{
  value: 'allocated',
  label: i18n.t('event.setting.serviceEventTypeSetting.flow.allocatedState')
}, {
  value: 'processing',
  label: i18n.t('event.setting.serviceEventTypeSetting.flow.processingState')
}, {
  value: 'finished',
  label: i18n.t('event.setting.serviceEventTypeSetting.flow.finishedState')
}]

/** 根据事件状态选择可执行编辑附加组件的流程节点*/
export const flowMapState = {
  // 待处理
  allocated: [{
    label: i18n.t('event.setting.serviceEventTypeSetting.flow.startState'),
    value: 'start',
  }, {
    label: i18n.t('event.setting.serviceEventTypeSetting.flow.convertTaskState'),
    value: 'convert2Task',
  }],
  // 处理中
  processing: [{
    label: i18n.t('event.setting.serviceEventTypeSetting.flow.finishState'),
    value: 'finish',
  }, {
    label: i18n.t('event.setting.serviceEventTypeSetting.flow.convertTaskState'),
    value: 'convert2Task',
  }],
  //  完成
  finished: [],
}

export const getFlowRuleOptions = (keys) => {
  if (Array.isArray(keys)) {
    if (keys.length === 0) {
      return flowRules
    }
    let option = [];
    keys.forEach(item => {
      let values = flowMapState[item] || [];
      option.push(...values)
    });
    option = toFieldArray(fieldArrayToObject(option, 'value'))
    return option
  } else if (keys) {
    return flowMapState[keys] || []
  }
  return flowRules
}

function toFieldArray(flowMapObject) {
  let arr = [];
  flowRules.forEach(item => {
    if (flowMapObject[item.value]) {
      arr.push(item)
    }
  })
  return arr
}