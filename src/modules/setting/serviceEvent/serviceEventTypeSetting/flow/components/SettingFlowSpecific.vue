<template>
  <div class="flow-setting-specific-wrap">
    <!--  智能派单    -->
    <smart-dispatch-setting v-show="isShowSmartDispatch"  ref="SmartDispatchSettingRef" :bizType="AgentBizTypeEnum.EVENT" :bizTypeId="eventTypeId"/>

    <!-- 事件表单设置 -->
    <template v-if="showFormPreview">
      <h2>{{ type === 'create' ?  $t('event.setting.serviceEventTypeSetting.flow.component.settingFlowSpecific.btn1') : $t('event.setting.serviceEventTypeSetting.flow.component.settingFlowPreview.btn2')}}</h2>
      <p class="link-cls w120" @click="$emit('onOpenFormDesignDialog')">
        {{ type === 'create' ? $t('event.setting.serviceEventTypeSetting.flow.component.settingFlowPreview.btn4') : $t('event.setting.serviceEventTypeSetting.flow.component.settingFlowPreview.btn5') }}
      </p>
    </template>

    <!-- 审批 -->
    <template v-if="showApprove">
      <h2>{{$t('event.setting.serviceEventTypeSetting.flow.component.settingFlowSpecific.title1')}}</h2>
      <approve-setting
        :flowType="type"
        :taskTypeId="eventTypeId"
        :options="approveOptions"
        :approve-setting="flowSetting.approver"
        @change="setting => handleChangeApproveSetting(setting, 'approver')"
      />
    </template>

    <!-- 分配审批 -->
    <template v-if="showDistribution">
      <h2>{{$t('event.setting.serviceEventTypeSetting.flow.component.settingFlowSpecific.title2')}}</h2>
      <approve-setting
        :flowType="type"
        :taskTypeId="eventTypeId"
        :options="approveOptions"
        :approve-setting="flowSetting.distributionApprover"
        @change="
          setting => handleChangeApproveSetting(setting, 'distributionApprover')
        "
      />
    </template>

    <!-- 转派审批 -->
    <template v-if="showReassignment">
      <h2>{{$t('event.setting.serviceEventTypeSetting.flow.component.settingFlowSpecific.title3')}}</h2>
      <approve-setting
        flowType="reAllot"
        :taskTypeId="eventTypeId"
        :options="approveOptions"
        :approve-setting="flowSetting.transferApprove"
        @change="
          setting => handleChangeApproveSetting(setting, 'transferApprove')
        "
      >
        <template slot='reassign'>
          <el-radio :label="3" class="ml-12">{{$t('event.setting.serviceEventTypeSetting.flow.component.settingFlowSpecific.label1')}}</el-radio>
        </template>
      </approve-setting>


    </template>

    <!-- 超时提醒 -->
    <template v-if="showOvertime">
      <over-time-setting
        :type="type"
        :over-time-setting="flowSetting.overTimeSetting"
        @change="setting => handleUpdateOverTimeSetting(setting, 'overTime')"
      ></over-time-setting>
    </template>

    <!-- 客户评价 -->
    <div class="customer_evaluation" v-if="showIFrame">
      <!-- 满意度方案设置 -->
      <template v-if="customerSatisfaction">
        <h2>{{$t('event.setting.serviceEventTypeSetting.flow.component.settingFlowSpecific.title4')}}</h2>
        {{$t('event.setting.serviceEventTypeSetting.flow.component.settingFlowSpecific.des1')}}
        <el-select
          :placeholder="$t('event.setting.serviceEventTypeSetting.flow.component.settingFlowSpecific.pla1')"
          v-model="flowSetting.questionId"
          @change="value => handleUpdateEvaluation(value, 'questionId')"
        >
          <el-option
            v-for="item in evaluationOptions"
            :key="item.satisfactionId"
            :label="item.satisfactionName"
            :value="item.satisfactionId"
          >
          </el-option>
        </el-select>
        <div class="link-cls w150" @click="handleRouterGoEvaluation" v-if="auth.SATISFACTION_CONFIG_EDIT">
          {{$t('event.setting.serviceEventTypeSetting.flow.component.settingFlowSpecific.des2')}}
        </div>
      </template>

      <!-- 短信回访 -->
      <h2>
        {{$t('event.setting.serviceEventTypeSetting.flow.component.settingFlowSpecific.title5')}}
        <el-switch
          v-model="flowSetting.autoReviewState"
        />
      </h2>
      <div class="desc">
        {{$t('event.setting.serviceEventTypeSetting.flow.component.settingFlowSpecific.des3')}}
      </div>
      <template v-if="flowSetting.autoReviewState">
        <h2>{{$t('event.setting.serviceEventTypeSetting.flow.component.settingFlowSpecific.title6')}}
          <el-switch
            v-model="flowSetting.delayBack"
          />
        </h2>
        <div class="flex-wrap" v-show="flowSetting.delayBack">
          {{$t('event.setting.serviceEventTypeSetting.flow.component.settingFlowSpecific.des4')}}
          <el-input
            class="w-87"
            onkeyup="this.value=this.value.replace(/\D/g,'')"
            v-model="flowSetting.delayBackMin"
          ></el-input>
          {{$t('event.setting.serviceEventTypeSetting.flow.component.settingFlowSpecific.des5')}}
        </div>
      </template>
    </div>

    <!--start 触发器 -->
    <!-- 事件隐藏客户评价/纯客服云版本隐藏触发器 -->
    <trigger-list-setting 
      v-if="triggerGray && type !== 'evaluate' && type !== 'close'"
      style="margin-top:12px;"
      :subTitle="$t('common.connector.trigger.subTitle', {curFlowName})" 
      bizType="EVENT"
      :bizTypeId="eventTypeId" 
      :bizActionCode="type"
    />
    <!--end 触发器 -->

  </div>
</template>

<script>
/* components */
import SmartDispatchSetting from '@src/component/compomentV2/SmartDispatchSetting/index.vue'
import ApproveSetting from './ApproveSetting.vue';
import OverTimeSetting from './OverTimeSetting.vue';
import TriggerListSetting from '@src/modules/connector/components/trigger-list-setting/index.vue';
import { FLOW_MAP } from '../../../constant';
import { getRootWindow } from '@src/util/dom';
import { getRootWindowInitData } from '@src/util/window'
/* enum */
import { AgentBizTypeEnum } from '@src/component/compomentV2/SmartDispatchSetting/model/enum.ts'
export default {
  name:'setting-flow-specific',
  props: {
    type: {
      type: String,
      default: 'create',
    },
    flowSetting: {
      type: Object,
      default: () => ({}),
    },
    evaluationOptions: {
      type: Array,
      default: () => [],
    },
    customerSatisfaction: {
      type: Boolean,
      default: false,
    },
    eventTypeId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      AgentBizTypeEnum,
    };
  },
  methods: {
    /** 更新审批设置*/
    handleChangeApproveSetting(setting, key) {
      this.$set(this.flowSetting, key, setting);
    },
    /** 更新超时提醒数据(由对象的"引用关系"实现)*/
    handleUpdateOverTimeSetting(setting, key) {
      this.$set(this.flowSetting, key, setting);
    },
    /** 更新满意度方案*/
    handleUpdateEvaluation(setting, key) {
      this.$set(this.flowSetting, key, setting);
    },
    /** 处理路由跳转客户满意度*/
    handleRouterGoEvaluation() {
      //  TODO: 处理路由跳转客户满意度
      this.$emit('openSetReviewQuestion')
    },
  },
  computed: {
    // 是否开启了智能机器人灰度
    isHaveSmartAgent() {
      const RootWindow = getRootWindow(window)
      return RootWindow.grayAuth?.SMART_DISPATCH ?? false;
    },
    // 是否显示智能派单模块
    isShowSmartDispatch() {
      return this.isHaveSmartAgent && this.type === 'allot'
    },
    //  表单设置
    showFormPreview() {
      return ['create', 'finish'].includes(this.type);
    },
    //  客户满意度
    showIFrame() {
      return ['evaluate'].includes(this.type);
    },
    //  审批
    showApprove() {
      return ['start', 'finish', 'close'].includes(this.type);
    },
    //  分配审批审批
    showDistribution() {
      return ['allot'].includes(this.type);
    },
    //  转派审批
    showReassignment() {
      return ['allot'].includes(this.type);
    },
    //  超时
    showOvertime() {
      return ['allot', 'start', 'finish'].includes(this.type);
    },
    //  审批人设置
    approveOptions() {
      let options = [
        {
          value: 'leader',
          label: this.$t('event.setting.serviceEventTypeSetting.flow.component.settingFlowSpecific.label2'),
        },
        {
          value: 'users',
          label: this.$t('event.setting.serviceEventTypeSetting.flow.component.settingFlowSpecific.label3'),
        },
        {
          value: 'createUser',
          label: this.$t('event.setting.serviceEventTypeSetting.flow.component.settingFlowSpecific.label4'),
        },
        {
          value: 'userAdmin',
          label: this.$t('event.setting.serviceEventTypeSetting.flow.component.settingFlowSpecific.label5'),
        },
        {
          value: 'promoter',
          label: this.$t('event.setting.serviceEventTypeSetting.flow.component.settingFlowSpecific.label6'),
        },
      ];

      //  TODO: 根据不同 type 设置不同审批人
      switch (this.type) {
      case 'create':
        break;
      case 'allot':
        break;
      default:
      }
      return options;
    },
    curFlowName() {
      // 当前流程的节点
      return FLOW_MAP[this.type]?.name || ''
    },
    auth() {
      const initData = getRootWindowInitData()
      return initData.user?.auth || {}
    },
    triggerGray(){
      const RootWindow = getRootWindow(window);
      return RootWindow.grayAuth?.TRIGGER || false;
    }
  },
  watch: {
    type: {
      handler(newV) {
        // form-preview props 设置
        switch (newV) {
        case 'create':
        case 'finish':
          break;
        case 'allot': //  分配
          break;
        case 'evaluate': //  客户评价
          break;
        default:
        }
      },
      immediate: true,
    },
  },
  components: {
    ApproveSetting,
    [OverTimeSetting.name]:OverTimeSetting,
    TriggerListSetting,
    [SmartDispatchSetting.name]: SmartDispatchSetting,
  },
};
</script>

<style lang="scss" scoped>
.flow-setting-specific-wrap {
  padding: 16px;
  width: 100%;
  height: calc(100vh - 40px - 48px);
  background: #ffffff;
  border-radius: 0px 0px 4px 4px;
  overflow-y: auto;
  color: #999;

  .smart-dispatch {
    margin-bottom: 16px;
  }
  /** 审批设置 radio*/
  ::v-deep .el-radio-group {
    display: flex;
  }
  .customer_evaluation{
    color: #999;
  }
}

h2 {
  margin: 12px 0;
  width: 100%;
  display: flex;
  justify-content: flex-start;
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  .el-switch {
    margin-left: 12px;
  }
  &:nth-of-type(1) {
    margin-top: 0;
  }
}

.desc {
  margin-top: 12px;
  margin-bottom: 8px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-size: 14px;
  font-weight: 400;
  color: #999;
}

.link-cls {
  &.w120 {
    width: 120px;
  }
  &.w150 {
    width: 150px;
  }
  margin-top: 8px;
  font-size: 14px;
  color: $color-primary-light-6;
  cursor: pointer;
}

.flex-wrap {
  line-height: 32px;
  .el-select {
    margin: 0 5px;
  }
  .el-input {
    margin: 0 5px;
  }
  .w-87 {
    width: 87px;
  }
}
</style>
