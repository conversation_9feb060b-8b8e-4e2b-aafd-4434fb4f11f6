interface VipApproveSetting {
  appId: string;
  formTemplateId: string
}

/** 服务事件模型*/
class ServiceEventConfig {
  /** id*/
  id: string = '';
  /** 名称*/
  name: string = '';
  /** 启用状态*/
  enabled: Boolean = false;
  /** 基础配置*/
  config: BaseConfig = new BaseConfig();
  /** 流程设置*/
  flowSetting: FlowSetting = new FlowSetting();
  /** 高级设置*/
  options: OptionsSetting = new OptionsSetting();
}

/** 基础配置*/
class BaseConfig {
  /** 标签颜色*/
  color: String = 'rgb(115,127,124)';
}

/** 流程设置*/
export class FlowSetting {
  /** 新建事件*/
  create: ServiceEventFlow = new ServiceEventFlow();
  /** 分配事件*/
  allot: ServiceEventFlow = new ServiceEventFlow();
  /** 开始事件*/
  start: ServiceEventFlow = new ServiceEventFlow();
  /** 完成事件*/
  finish: ServiceEventFlow = new ServiceEventFlow();
  /** 客户评价*/
  evaluate: ServiceEventFlow = new ServiceEventFlow();
  /** 关闭事件*/
  close: ServiceEventFlow = new ServiceEventFlow();
}

/** 服务事件高级设置*/
class OptionsSetting {
  /** 允许事件负责人将事件设为暂停状态*/
  pause: OptionStateWithApprover = new OptionStateWithApprover();
  /** 允许事件在完成前被取消*/
  off: OptionStateWithApprover = new OptionStateWithApprover();
  /** 允许事件负责人将事件转为工单*/
  transform: OptionStateWithApprover = new OptionStateWithApprover();
}

class OptionStateWithApprover {
  state: Boolean = false;
  approver: ApproverSetting = new ApproverSetting();
}

/** 服务事件流程设置*/
class ServiceEventFlow {
  /** 描述*/
  description: String = '';
  /** 启用状态*/
  state: Boolean = false;
  /** 新建(回执)表单模板 id*/
  templateId: String = '';
  /** 超时提醒*/
  overTimeSetting: OverTimeSetting = new OverTimeSetting();
  /** 审批设置*/
  approver: ApproverSetting = new ApproverSetting();
  /** 分配审批*/
  distributionApprover: ApproverSetting = new ApproverSetting();
  /** 转派审批*/
  transferApprove: ApproverSetting = new ApproverSetting();
  /** 满意度方案ID*/
  questionId: String = '';
  /** 是否启用自动回访*/
  autoReviewState: Boolean = false;
  /** 短信发送提醒延期发送*/
  delayBack: Boolean = false;
   /** 短信发送提醒延迟分钟时间*/
  delayBackMin: Number = 0;
}

/** 超时设置*/
export class OverTimeSetting {
  constructor(overTimeState?: string) {
    this.overTimeState = overTimeState || '';
  }
  /** 流程节点*/
  overTimeState: String = '';
  /** 启用状态*/
  overTimeStatus: Boolean = true;
  /** 超时时间设置*/
  overTime: String = '0';
  /** 前/后设置(1 前 0 后)*/
  isAhead: Number = 1;
  /** 超时提醒前(后)时间设置(分钟)*/
  minutes: String = '0';
  /** 超时提醒类型 0 无需通知其它人, 1 负责部门主管, 2 指定人员*/
  remindType: String | Number = 2;
  /** 提醒指定人员*/
  reminders: Person[] = [];
}

/** 人员设置*/
class Person {
  /** 人员名称 */
  displayName: String = '';
  /** 人员头像 */
  head: String = '';
  /** 员工id */
  staffId: String = '';
  /** 用户id */
  userId: String = '';
}

/** 审批设置*/
export class ApproverSetting {
  /** 审批层级*/
  level: Number = 0;
  /** 第一阶段审批类型*/
  leader: String = '';
  /** 第一级审批的指定人员*/
  approvers: any[] = [];
  /** 多级审批(二级及以上审批)*/
  multiApproverSetting: ApproverSetting[] = [];
  /** 是否是高级审批*/
  vipApprove?: Number = 0;
  /** 高级审批的参数 */
  vipApproveSetting?: VipApproveSetting;
}

export default ServiceEventConfig;
