import {FlowSetting, ApproverSetting, OverTimeSetting} from '../flow/models/ServiceEventConfig'
import _ from 'lodash';

/** 初始化服务事件类型数据转化为页面可支持的数据模型
* @param sourceConfig 服务事件源数据
*/
export const convertServiceEventConfig = (sourceConfig) => {
  let { id, name, isSystem, flowSetting, config = {}, nameLanguage = {} } = sourceConfig;
 
  let { color = 'rgb(115, 127, 124)', autoCompleteEvent, allowPause, allowConvert, completeCancel} = config;

  let eventTypeConfig = {
    id,
    name,
    isSystem,
    flowSetting: new FlowSetting(), //  配置设置
    config: {
      color,
      autoCompleteEvent,
      allowPause: initConfigSetting(allowPause), // 允许暂停
      completeCancel: initConfigSetting(completeCancel), // 允许取消
      allowConvert: initConfigSetting(allowConvert) //  允许转工单
    },
    nameLanguage, // 多语言
  };

  // 老数据新增创建事件节点
  if(!flowSetting.hasOwnProperty('create')){
    flowSetting['create'] = {}
  }

  // 流程步骤赋值初始化
  let sourceOverTimeSetting = sourceConfig.overTimeSetting
  Object.keys(flowSetting).forEach(key => {
    let {
      state = true,
      overTime = 0,
      leader = 'none',
      approvers = [],
      level,
      questionId = '',
      autoReviewState = false,
      delayBack = false,
      delayBackMin = 0,
      multiApproverSetting = [],
      overTimeSetting = {},
      vipApprove = 0,
      vipApproveSetting = {},
    } = flowSetting[key];

    let newOverTimeSetting = Object.assign(new OverTimeSetting(key), overTimeSetting);
    if(newOverTimeSetting.remindType == 2) {
      newOverTimeSetting.remindType = null
    }
    // 旧数据处理超时提醒??
    // if(overTime > 0) {
    //   newOverTimeSetting = {
    //     ...overTimeSetting,
    //     ...sourceOverTimeSetting,
    //     overTimeStatus: true,
    //     overTime,
    //     remindType: sourceOverTimeSetting.remindType ?? null,
    //     reminders: sourceOverTimeSetting.reminders || []
    //   }
    // }
    // 兼容旧审批格式
    let approveSetting = compatibleOldApprove({
      leader,
      approvers,
      level,
      multiApproverSetting,
      vipApprove,
      vipApproveSetting,
    });
    // 转派审批兼容
    let transferApprove = Object.assign({
      state: true,
    }, compatibleOldApprove(sourceConfig.flowSetting[key].transferApprove || new ApproverSetting()))  

    let newObject = {};
    if(key == 'allot') { // 分配
      newObject = {
        distributionApprover: approveSetting,
        transferApprove,
        overTimeSetting: newOverTimeSetting
      }
    }else if(key == 'evaluate') { // 客户评价
      newObject = {
        questionId,
        autoReviewState, 
        delayBack,
        delayBackMin
      }
    }else { // 新建、开始、分配、完成
      newObject.approver = approveSetting
      if(key != 'create'){
        newObject.overTimeSetting = newOverTimeSetting;
      }
    }
    eventTypeConfig.flowSetting[key] = Object.assign({
      state,
      overTime
    }, newObject);
  })


  
  //  todo附加组件数据
  eventTypeConfig.cardSetting = {
    ...(sourceConfig.cardSetting || {})
  }
  return eventTypeConfig;
}

/** 将页面支持的数据模型转化为接口所需的格式*/
export const saveEventConfigParams = (serviceEventConifg) => {
  let eventTypeConfig = _.cloneDeep(serviceEventConifg);
  const {id, name, config, flowSetting, nameLanguage } = eventTypeConfig;
  const {color, autoCompleteEvent, allowPause, completeCancel, allowConvert } = config;

  let paramsConfig = {
    typeId: id,
    name,
    color,
    config: { 
      autoCompleteEvent,
      allowPause,
      completeCancel,
      allowConvert
    },
    flowSetting: eventTypeConfig.flowSetting,
    nameLanguage // 多语言
  };
  // 高级设置参数处理
  let configOptions = ['allowPause', 'completeCancel', 'allowConvert']
  configOptions.map(key=> {
    paramsConfig.config[key] = {
      state: config[key].state,
      ...config[key].approver
    }
  })
  //  流程步骤设置
  Object.keys(flowSetting).forEach(key => {
    let {state = true, overTime = 0, questionId, autoReviewState, delayBack, delayBackMin, transferApprove = {}, overTimeSetting = new OverTimeSetting(key)} = flowSetting[key];
    if(overTimeSetting.overTime > 0) {
      overTime = overTimeSetting.overTime;
    }
    let newObject = {};
    if(key == 'allot') {
      // 转派审批设置
      if(transferApprove?.level === 3 ){
        const { leader, approvers, multiApproverSetting} = flowSetting[key].distributionApprover
        transferApprove = Object.assign(transferApprove, { leader, approvers, multiApproverSetting})
      }
      newObject = {
        ...flowSetting[key].distributionApprover,
        transferApprove,
        overTimeSetting
      } 
    }else if(key == 'evaluate') {
      newObject = {
        questionId,
        autoReviewState, 
        delayBack,
        delayBackMin
      } 
    }else {
      newObject = flowSetting[key].approver;
      const noNeedOverTime = ['create', 'close']
      if(!noNeedOverTime.includes(key)){
        newObject.overTimeSetting = overTimeSetting;
      }
    }
    paramsConfig.flowSetting[key] = Object.assign({
      state,
      overTime
    }, newObject);
  })

  //  附加组件保存
  paramsConfig.cardSetting = eventTypeConfig.cardSetting

  return paramsConfig
}

/** 兼容旧审批结构 */
function compatibleOldApprove (setting = {}, key) {
  let {leader, level, approvers} = setting;
  if(level === undefined) {
    if(leader == 'none' || leader == undefined){
      level = 0;
    }else{
      level = 1;
    }
  }

  if(leader === 'none' && !level === 3){
    level = 0;
    leader = ''
  }
 

  return {
    ...setting,
    leader,
    level
  };
}
/** 初始化高级设置 */
function initConfigSetting(params) {
  if (params){
    const { state, ...rest } = params;
    return {
      state,
      approver: rest
    }
  }

  return { 
    state: false,
    approver: new ApproverSetting()
  }
}