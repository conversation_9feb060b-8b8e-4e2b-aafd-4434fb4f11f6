import TableNameEnum from '@model/enum/TableNameEnum.ts';
import i18n from '@src/locales'

/** 服务事件类型色彩*/
export const TYPE_COLORS = [
  'rgb(115,127,124)',
  'rgb(38,111,255)',
  'rgb(82,85,255)',
  'rgb(133,82,255)',
  'rgb(188,82,255)',
  'rgb(255,82,212)',
  'rgb(255,149,38)',
  'rgb(110,207,64)',
  'rgb(0,184,213)',
  'rgb(11,161,148)'
];

/** 服务事件流程*/
export const FLOW_MAP = {
  create: {
    name: i18n.t('event.setting.constant.flow_map.name1'),
    desc: i18n.t('event.setting.constant.flow_map.des1'),
    icon: 'icon-xinjian',
    isSystem: true,
  },
  allot: {
    name: i18n.t('event.setting.constant.flow_map.name2'),
    desc: i18n.t('event.setting.constant.flow_map.des2'),
    icon: 'icon-zhipai',
    isSystem: true
  },
  start: {
    name: i18n.t('event.setting.constant.flow_map.name3'),
    desc: i18n.t('event.setting.constant.flow_map.des3'),
    icon: 'icon-kaishi',
    isSystem: false
  },
  finish: {
    name: i18n.t('event.setting.constant.flow_map.name4'),
    desc: i18n.t('event.setting.constant.flow_map.des4'),
    icon: 'icon-wancheng',
    isSystem: true
  },
  evaluate: {
    name: i18n.t('event.setting.constant.flow_map.name5'),
    desc: i18n.t('event.setting.constant.flow_map.des5'),
    icon: 'icon-smile-fill1',
    isSystem: false
  },
  close: {
    name: i18n.t('common.event.closeEvent'),
    desc: i18n.t('event.setting.constant.flow_map.des6'),
    icon: 'icon-jieshu',
    isSystem: true
  },
}

/** 服务事件设置步骤*/
export const SETTING_STEP = [{
  stepName: i18n.t('event.setting.constant.setting_step.name1'),
  compName: 'flow-setting-panel',
},
{
  stepName: i18n.t('event.setting.constant.setting_step.name2'),
  compName: 'other-setting-panel',
},
{
  stepName: i18n.t('event.setting.constant.setting_step.name3'),
  compName: 'card-setting-panel',
}
]

/** 超时提醒人类型选择参数*/
export const OVER_TIME_REMIND_TYPE_OPTIONS = [{
  value: 0,
  label: i18n.t('event.setting.constant.over_time_remind_type_options.label1'),
},
{
  value: 1,
  label: i18n.t('event.setting.constant.over_time_remind_type_options.label2'),
},
{
  value: null,
  label: i18n.t('event.setting.constant.over_time_remind_type_options.label3'),
},
]

/** 模式名映射对象*/
export const MODE_NAME_MAP = {
  [TableNameEnum.Event]: i18n.t('event.setting.constant.mode_name_map.name1'),
  [TableNameEnum.EventReceipt]: i18n.t('event.setting.constant.mode_name_map.name2')
}

/** 附加组件系统模板归类*/
export const ADDITIONAL_TYPE_MAP = {
  '10001': 1,
  '10000': 1,
  '10006': 2,
  '10010': 2,
  '10007': 3,
  '10008': 3,
  '10003': 4,
  '10009': 5,
  '10002': 6,
  '10004': 6,
  '10005': 6,
  '10011': 7 //  工时记录
}

/** 附加组件系统模板 type -> name*/
export const ADDITIONAL_TYPE_TABS = [
  {
    type: 0,
    name: i18n.t('event.setting.constant.additional_type_tabs.name1'),
  }, {
    type: 1,
    name: i18n.t('event.setting.constant.additional_type_tabs.name2'),
  },
  {
    type: 2,
    name: i18n.t('event.setting.constant.additional_type_tabs.name3'),
  },
  {
    type: 3,
    name: i18n.t('event.setting.constant.additional_type_tabs.name4'),
  },
  {
    type: 4,
    name: i18n.t('event.setting.constant.additional_type_tabs.name5'),
  },
  {
    type: 5,
    name: i18n.t('event.setting.constant.additional_type_tabs.name6'),
  },
  {
    type: 6,
    name: i18n.t('event.setting.constant.additional_type_tabs.name7'),
  }
]

export default null