/* components */
/* enum */
import ComponentNameEnum from '@model/enum/ComponentNameEnum'
import AuthEnum from '@model/enum/AuthEnum'
/* hooks */
import { useRoleViewAuth } from '@src/modules/account/roleService/hooks'
import { useStoreMode } from '@src/modules/account/roleService/store/hooks'
/* model */
import { AuthTreeNode } from '@src/modules/account/roleService/component/AuthTree/model'
import Role, { RoleAuthDepthEnum, getRoleAuthDepthNameObj } from '@model/entity/Role/Role'
/* scss */
import '@src/modules/account/roleService/component/AuthList/index.scss'
/* vue */
import { ComponentInstance, computed, defineComponent, PropType, ref } from 'vue'
import { CreateElement, VNode } from 'vue'
/* util */
import { isEmpty, isNotEmpty } from '@src/util/type'
import { openTabSettingReportAuth } from '@src/util/business/openTab'
import { RoleCusDataAuthorityListItem } from '@src/modules/account/role/model/entity/CustomDataAuth'
import { customDataAuthGray as getCustomDataAuthGray} from '@src/util/grayInfo'

export type AuthListItemProps = {
  data: AuthTreeNode;
  disabled: boolean;
  roleCusDataAuthorityListData: RoleCusDataAuthorityListItem[],
}

export interface AuthListItemSetupState {
  
}


export enum AuthListItemEventEnum {
  Input = 'input',
  Submit = 'submit',
  HandleSetCustomDataPermission = "handleSetCustomDataPermission"
}

export type AuthListItemEmitsOptions = {
  input: () => void;
}

export type AuthListItemInstance = ComponentInstance & AuthListItemSetupState
export type AuthListItemVM = AuthListItemSetupState & AuthListItemProps & ComponentInstance

export default defineComponent({
  name: ComponentNameEnum.AuthListItem,
  emits: [
    AuthListItemEventEnum.Input,
    AuthListItemEventEnum.Submit,
    AuthListItemEventEnum.HandleSetCustomDataPermission
  ],
  props: {
    data: {
      type: Object as PropType<AuthTreeNode>,
      default: () => ({})
    },
    roleCusDataAuthorityListData: {
      type: Array as PropType<RoleCusDataAuthorityListItem[]>,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  setup(props: AuthListItemProps, { slots, emit }) {
    
    const { canShowReportLink } = useRoleViewAuth()
    const { isEditMode } = useStoreMode()
    
    const authGroupName = computed(() => {
      return props.data.name || ''
    })
    
    const children = computed(() => {
      return props.data.children || []
    })

    const currentRoleCusDataAuthorityListMap = computed(()=> {
      return props.roleCusDataAuthorityListData.reduce((acc: Record<string, RoleCusDataAuthorityListItem>, item)=> {
          acc[item.authKey] = item
        return acc
      }, {})
    })

    const customDataAuthGray = computed(()=> getCustomDataAuthGray())
    
    return {
      authGroupName,
      children,
      canShowReportLink,
      currentRoleCusDataAuthorityListMap,
      customDataAuthGray,
      isEditMode
    }
    
  },
  methods: {
    onCheckChangeHandler(node: AuthTreeNode, value: boolean) {
      node.checked = value
    },
    onExpandedChangeHandler(node: AuthTreeNode, value: boolean) {
      node.expanded = value
    },
    onRadioChangeHandler(node: AuthTreeNode, value: string | number) {
      node.depth = value as RoleAuthDepthEnum
    },
    onCheckAllHandler(node: AuthTreeNode, value: boolean) {
      
      node.checked = value
      
      node.children.forEach(child => {
        child.checked = value
      })
      
    },
    renderAuthListItemChildrenLeft(h: CreateElement, child: AuthTreeNode) {
      
      const isCheckedAll = child.children.every(child => child.checked)
      
      return (
        <div class="auth-list-item-child__left">
          
          <div class="auth-list-item-child__title">
            { child.name }
          </div>
          
          <div class="auth-list-item-child__content">
            <el-checkbox 
              disabled={this.disabled}
              value={isCheckedAll}
              onInput={(value: boolean) => {
                this.onCheckAllHandler(child, value)
              }}
            >
              {this.$t('common.base.selectAll')}
            </el-checkbox>
          </div>
          
        </div>
      )
    },
    renderAuthListItemChildrenRight(h: CreateElement, child: AuthTreeNode) {
      return (
        <div class="auth-list-item-child__right">
          
          { this.renderAuthListItemChildrenRightFeatureAuth(h, child) }
          { this.renderAuthListItemChildrenRightDataAuth(h, child) }
          
        </div>
      )
    },
    renderAuthListItemChildrenRightFeatureAuth(h: CreateElement, child: AuthTreeNode) {
      return (
        <div class="auth-list-item-child__right-feature">
          
          <div class="auth-list-item-child__title">
            {this.$t('common.auth.functionPermission')}
          </div>
          
          <div class="auth-list-item-child__content">
            {child.children.map(item => {
              return (
                <el-checkbox 
                  disabled={this.disabled}
                  value={item.checked} 
                  onInput={(value: boolean) => {
                    this.onCheckChangeHandler(item, value)
                  }}
                >
                  { item.name }
                </el-checkbox>
              )
            })}
          </div>
          
        </div>
      )
    },
    renderAuthListItemChildrenRightDataAuth(h: CreateElement, child: AuthTreeNode) {
      
      const isLastLevel = (node: AuthTreeNode) => node?.level == AuthTreeNode.lastLevel
      const isShowRadio = (node: AuthTreeNode) => node?.isShowRadio && isLastLevel(node)
      
      const dataAuths = child.children.filter(item => {
        return isShowRadio(item)
      })
      
      const isDataAuthEmpty = isEmpty(dataAuths)
      
      if (isDataAuthEmpty) {
        return null
      }
      const showDataAuth = dataAuths.length && dataAuths.find(item=>item.showAllCheck !== 0);
      
      return (
        showDataAuth &&
        <div class="auth-list-item-child__right-data">
          
          <div class="auth-list-item-child__title">
            {this.$t('common.auth.dataPermission')}
          </div>
          
          <div class="auth-list-item-child__content">
            
            {dataAuths.map(item => {
              return (
                item.showAllCheck !== 0 &&
                <div class="auth-list-item-child__right-data__row">
                  <div class="auth-list-item-child__right-data__row-name">
                    { item.name }
                  </div>
                  { this.renderAuthListItemChildrenRightDataAuthRadio(h, item) }
                  { this.renderAuthListItemReportLink(h, item) }
                </div>
              )
            })}
            
          </div>
          
        </div>
      )
    },
    renderAuthListItemReportLink(h: CreateElement ,node: AuthTreeNode) {
      
      if (this.disabled) return null

      // 开了自定义数据权限灰度
      // if(this.customDataAuthGray) {
      //   // const showLinkEnumArray = [AuthEnum.CASE_VIEW, AuthEnum.CUSTOMER_VIEW, AuthEnum.TASK_VIEW] as unknown as string []
      //   // if(this.currentRoleCusDataAuthorityListMap[node.key] && node?.depth !== 3) {
      //   if(this.currentRoleCusDataAuthorityListMap[node.key] && node.depth === 4) {
      //     return this.renderCustomDataAuthLink(this.currentRoleCusDataAuthorityListMap[node.key])
      //   }
      // }
      
      if (node.key != AuthEnum.VIP_REPORT_VIEW) return null
      
      if (!this.canShowReportLink) return null
      
      return (
        <div 
          class="auth-list-item-child__right-data__row-report link-text"
          onClick={openTabSettingReportAuth}
        >
          {`（${this.$t('account.viewReportPermission')}）`}
        </div>
      )
      
    },
    // 自定义数据权限link
    renderCustomDataAuthLink(moduleData: RoleCusDataAuthorityListItem) {
      if (!this.isEditMode) return null;
      return (
        <div 
          class="auth-list-item-child__right-data__row-report link-text"
          onClick={()=> {
            this.$emit(AuthListItemEventEnum.HandleSetCustomDataPermission, moduleData)
          }}
        >
        {isEmpty(moduleData.editConditionList) ? `${this.$t('account.viewCustomDataPermission')}` : this.$t('common.base.lookcustomData')}
      </div>
      )
    },
    renderAuthListItemChildrenRightDataAuthRadio(h: CreateElement, child: AuthTreeNode) {
      
      const isUserRadio = child?.aLevel != 2
      const isTagRadio = child?.aLevel == 2 || child?.aLevel == 3
      const showAllCheck = ref(child?.showAllCheck || 0);
      const RoleAuthDepthName = getRoleAuthDepthNameObj()
      const DataAuthArr:any = {
        0 : [],
        1 : [
          { name: RoleAuthDepthName.Dept, value: RoleAuthDepthEnum.Dept },
          { name: RoleAuthDepthName.Person, value: RoleAuthDepthEnum.Person }
        ],
        2 : [
          { name: RoleAuthDepthName.All, value: RoleAuthDepthEnum.All },
          { name: RoleAuthDepthName.Person, value: RoleAuthDepthEnum.Person }
        ],
        3 : [
          { name: RoleAuthDepthName.All, value: RoleAuthDepthEnum.All },
          { name: RoleAuthDepthName.Dept, value: RoleAuthDepthEnum.Dept },
          { name: RoleAuthDepthName.Person, value: RoleAuthDepthEnum.Person }
        ],
        4 : [
          { name: RoleAuthDepthName.All, value: RoleAuthDepthEnum.All },
          { name: RoleAuthDepthName.Dept, value: RoleAuthDepthEnum.Dept }
        ],
      }
      const AuthArr = DataAuthArr[showAllCheck.value];

      const showCustomDataAuth = this.customDataAuthGray && this.currentRoleCusDataAuthorityListMap[child.key] 

      return (
        AuthArr.length > 0 &&
        <div class="auth-list-item-child__radio">
          <el-radio-group 
            disabled={this.disabled}
            value={child.depth} 
            onInput={(value: string) => {
              this.onRadioChangeHandler(child, value)
            }}
          >
            {AuthArr.map((item:any)=> {
              return (
                <el-radio label={item.value}>
                  {item.name}
                </el-radio>
              )
            })}
            {/* {showCustomDataAuth && (
              <el-radio label={RoleAuthDepthEnum.custom}>
                {this.$t('common.base.customData')}
              </el-radio>
            )} */}
            {/* {showAllCheck &&
            <el-radio label={RoleAuthDepthEnum.All}>
              全部
            </el-radio>
            }
            
            {showPartCheck && isTagRadio && (
              <el-radio label={RoleAuthDepthEnum.Dept}>
                部门
              </el-radio>
            )}
            
            {showPersonCheck && isUserRadio && (
              <el-radio label={RoleAuthDepthEnum.Person}>
                个人
              </el-radio>
            )} */}
            
          </el-radio-group>
        </div>
      )
    },
    renderAuthListItemChildren(h: CreateElement, child: AuthTreeNode): any {
      return (
        <div class="auth-list-item-child">
          { this.renderAuthListItemChildrenLeft(h, child) }
          { this.renderAuthListItemChildrenRight(h, child) }
        </div>
      )
    },
  },
  render(h: CreateElement) {
    
    const headerClassNames = [
      'auth-list-item__header',
      this.data.expanded ? 'auth-list-item__header--expanded' : '',
    ]
    
    return (
      <div class={ComponentNameEnum.AuthListItem}>
        
        <div 
          class={headerClassNames}
          onClick={() => {
            this.onExpandedChangeHandler(this.data, !this.data.expanded)
          }}
        >
          
          <i class="iconfont icon-arrow-right">
          </i>
          
          { this.authGroupName }
          
        </div>
        
        {this.data.expanded && (
          this.children.map((child) => {
            return this.renderAuthListItemChildren(h, child)
          })
        )}
        
      </div>
    ) as any
  }
}) as any
