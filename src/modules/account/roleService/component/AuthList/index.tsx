/* components */
import { AuthListItem } from '@src/modules/account/roleService/component'
import CustomDataAuthEditViewDialog from '@src/modules/account/role/component/CustomDataAuthEditViewDialog'
import { Fragment } from 'vue-frag'
/* enum */
import ComponentNameEnum from '@model/enum/ComponentNameEnum'
/* model */
import { AuthTreeNode } from '@src/modules/account/roleService/component/AuthTree/model'
import { RoleCusDataAuthorityListItem } from '@src/modules/account/role//model/entity/CustomDataAuth'
/* scss */
import '@src/modules/account/roleService/component/AuthList/index.scss'
/* vue */
import { ComponentInstance, computed, defineComponent, PropType, ref } from 'vue'

export type AuthListProps = {
  data: AuthTreeNode[];
  disabled: boolean;
}

export interface AuthListSetupState {
  
}

export enum AuthListEventEnum {
  Input = 'input',
  Submit = 'submit',
}

export type AuthListEmitsOptions = {
  input: () => void;
}

export type AuthListInstance = ComponentInstance & AuthListSetupState
export type AuthListVM = AuthListSetupState & AuthListProps & ComponentInstance

export default defineComponent({
  name: ComponentNameEnum.AuthList,
  emits: [
    AuthListEventEnum.Input,
    AuthListEventEnum.Submit,
  ],
  props: {
    data: {
      type: Array as PropType<AuthTreeNode[]>,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    },
    roleCusDataAuthorityListData: {
      type: Array as PropType<RoleCusDataAuthorityListItem[]>,
      default: () => []
    },
  },
  setup(props: AuthListProps, { slots, emit }) {
    
    const customDataAuthEditViewDialogRef = ref(null)

    const showSettingCustomDataPermission = (v: RoleCusDataAuthorityListItem)=> {
      if(customDataAuthEditViewDialogRef.value) {
        // @ts-ignore
        customDataAuthEditViewDialogRef.value?.showModal(v, props.disabled);
      }
    }

    return {
      customDataAuthEditViewDialogRef,
      showSettingCustomDataPermission
    }
  },
  render() {
    return (
      //@ts-ignore
     <Fragment>
      <div class={ComponentNameEnum.AuthList}>
        
        {this.data.map(item => {
          return <AuthListItem data={item} disabled={this.disabled} roleCusDataAuthorityListData={this.roleCusDataAuthorityListData} onHandleSetCustomDataPermission={this.showSettingCustomDataPermission}/>
        })}
        
      </div>
      <CustomDataAuthEditViewDialog key="roleServiceCustomDataAuthEditViewDialog" roleCusDataAuthorityListData={this.roleCusDataAuthorityListData} ref="customDataAuthEditViewDialogRef"></CustomDataAuthEditViewDialog>
    </Fragment>
    ) as any
  }
}) as any
