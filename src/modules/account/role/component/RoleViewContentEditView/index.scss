.role-view-content-edit-view {
  
  height: 100%;
  padding: 12px 0 0 0;
  
  position: relative;
  
  .slot-box + .vue-waterfall-column {
    border-right: 1px solid #E8E8E8;
  }
  
  .role-view-form,
  .role-view-content-edit-view__ref-block,
  .role-view-content-edit-view-auth-tree {
    margin-left: 12px;
    margin-right: 12px;
  }
  
}

.role-view-content-edit-view__content {
  height: calc(100% - 72px);
  overflow-y: auto;
}

.role-view-content-edit-view__ref-block {
  
  margin: 12px 0;
  height: 32px;
  display: flex;
  justify-content: space-between;
  
  &-name {
    height: 22px;
    font-size: 14px;
    font-weight: 400;
    color: #262626;
    line-height: 22px;
  }
  
  .el-select {
    margin: 0 8px;
  }
  .role-view-content-edit-view__search{
    .input{
      width: 210px;
    }
  }
}

.role-view-content-edit-view__footer {
  
  box-shadow: 0px -1px 0px 0px rgba(0, 0, 0, 0.1);
  
  height: 60px;
  margin-top: 12px;
  padding-left: 16px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  
  position: absolute;
  width: 100%;
  
  .el-button {
    height: 32px;
  }
  
}

.role-view-content-edit-view-auth-tree {
  overflow-y: auto;
  overflow-x: auto;
}

.role-view-content-edit-view__copy {
  cursor: pointer;
  margin-right: 8px;
}