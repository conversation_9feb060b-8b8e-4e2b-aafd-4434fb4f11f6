/* components */
/* enum */
import ComponentNameEnum from '@model/enum/ComponentNameEnum'
import AuthEnum from '@model/enum/AuthEnum'
import { PermissionsTypeEnum } from '@src/modules/account/role/model'
/* hooks */
import { useRoleViewAuth } from '@src/modules/account/role/hooks'
import { usePermissionsType, useStoreInitData } from '@src/modules/account/role/store/hooks'
import { useStoreRoleCusDataAuthorityList } from '@src/modules/account/role/store/hooks/customDataAuth'
import { useStoreMode } from '@src/modules/account/role/store/hooks'
/* model */
import { AuthTreeNode } from '@src/modules/account/role/component/AuthTree/model'
import Role, { RoleAuthDepthEnum } from '@model/entity/Role/Role'
import { RoleCusDataAuthorityListItem } from '@src/modules/account/role//model/entity/CustomDataAuth'
/* scss */
import '@src/modules/account/role/component/AuthList/index.scss'
/* vue */
import { ComponentInstance, computed, defineComponent, PropType } from 'vue'
import { CreateElement, VNode } from 'vue'
/* util */
import { isEmpty, isNotEmpty } from '@src/util/type'
import {openTabServiceManageAuth, openTabSettingReportAuth, openTabWarehouseRoleAuth} from '@src/util/business/openTab'
import { customDataAuthGray as getCustomDataAuthGray} from '@src/util/grayInfo'
import { t } from '@src/locales'

export type AuthListItemProps = {
  data: AuthTreeNode;
  disabled: boolean;
  roleCusDataAuthorityListData: RoleCusDataAuthorityListItem[],
  checkedBoxMainTitle: string
}

export interface AuthListItemSetupState {
  
}

export enum AuthListItemEventEnum {
  Input = 'input',
  Submit = 'submit',
  HandleSetCustomDataPermission ='handleSetCustomDataPermission'
}

export type AuthListItemEmitsOptions = {
  input: () => void;
}

export type AuthListItemInstance = ComponentInstance & AuthListItemSetupState
export type AuthListItemVM = AuthListItemSetupState & AuthListItemProps & ComponentInstance

export default defineComponent({
  name: ComponentNameEnum.AuthListItem,
  emits: [
    AuthListItemEventEnum.Input,
    AuthListItemEventEnum.Submit,
    AuthListItemEventEnum.HandleSetCustomDataPermission
  ],
  props: {
    data: {
      type: Object as PropType<AuthTreeNode>,
      default: () => ({})
    },
    roleCusDataAuthorityListData: {
      type: Array as PropType<RoleCusDataAuthorityListItem[]>,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    },
    checkedBoxMainTitle: {
      type:String,
      default: ()=> t('common.auth.functionPermission')
    }
  },
  setup(props: AuthListItemProps, { slots, emit }) {
    
    const { isShowReportSetting } = useStoreInitData()

    const { permissionsType, setPermissionsType } = usePermissionsType()
    
    const { isEditMode } = useStoreMode()
    
    const currentRoleCusDataAuthorityListMap = computed(()=> {
      return props.roleCusDataAuthorityListData.reduce((acc: Record<string, RoleCusDataAuthorityListItem>, item)=> {
          acc[item.authKey] = item
        return acc
      }, {})
    })
    const customDataAuthGray = computed(()=> getCustomDataAuthGray())
    
    const authGroupName = computed(() => {
      return props.data.name || ''
    })
    
    const children = computed(() => {
      return props.data?.filterChildren ? props.data.filterChildren  : props.data.children || []
    })
    return {
      currentRoleCusDataAuthorityListMap,
      customDataAuthGray,
      authGroupName,
      children,
      isShowReportSetting,
      setPermissionsType,
      isEditMode
    }
    
  },
  methods: {
    onCheckChangeHandler(node: AuthTreeNode, value: boolean) {
      node.checked = value
    },
    onExpandedChangeHandler(node: AuthTreeNode, value: boolean) {
      node.expanded = value
    },
    onRadioChangeHandler(node: AuthTreeNode, value: string | number) {
      node.depth = value as RoleAuthDepthEnum
    },
    onCheckAllHandler(node: AuthTreeNode, value: boolean) {
      
      node.checked = value;
      
     (node?.filterChildren || node.children).forEach(child => {
        child.checked = value
      })
      
    },
    renderAuthListItemChildrenLeft(h: CreateElement, child: AuthTreeNode) {
      
      const isCheckedAll = (child?.filterChildren || child.children).every(child => child.checked)
      
      return (
        <div class="auth-list-item-child__left">
          
          <div class="auth-list-item-child__title">
            { child.name }
          </div>
          
          <div class="auth-list-item-child__content">
            <el-checkbox 
              disabled={this.disabled}
              value={isCheckedAll}
              onInput={(value: boolean) => {
                this.onCheckAllHandler(child, value)
              }}
            >
              {this.$t('common.base.selectAll')}
            </el-checkbox>
          </div>
          
        </div>
      )
    },
    renderAuthListItemChildrenRight(h: CreateElement, child: AuthTreeNode) {
      return (
        <div class="auth-list-item-child__right">
          
          { this.renderAuthListItemChildrenRightFeatureAuth(h, child) }
          { this.renderAuthListItemChildrenRightDataAuth(h, child) }
          
        </div>
      )
    },
    renderAuthListItemChildrenRightFeatureAuth(h: CreateElement, child: AuthTreeNode) {
      return (
        <div class="auth-list-item-child__right-feature">
          
          <div class="auth-list-item-child__title">
            {this.checkedBoxMainTitle}
          </div>
          
          <div class="auth-list-item-child__content">
            {(child?.filterChildren || child.children).map(item => {
              return (
                <el-checkbox 
                  disabled={this.disabled}
                  value={item.checked} 
                  onInput={(value: boolean) => {
                    this.onCheckChangeHandler(item, value)
                  }}
                >
                  { item.name }
                  { this.renderAuthListItemWarehouseLink(h, item) }
                </el-checkbox>
              )
            })}
          </div>
          
        </div>
      )
    },
    renderAuthListItemChildrenRightDataAuth(h: CreateElement, child: AuthTreeNode) {
      
      const isLastLevel = (node: AuthTreeNode) => node?.level == AuthTreeNode.lastLevel
      const isShowRadio = (node: AuthTreeNode) => node?.isShowRadio && isLastLevel(node)
      
      const dataAuths = child.children.filter(item => {
        return isShowRadio(item)
      })
      
      const isDataAuthEmpty = isEmpty(dataAuths)
      
      if (isDataAuthEmpty) {
        return null
      }
      
      return (
        <div class="auth-list-item-child__right-data">
          
          <div class="auth-list-item-child__title">
          {this.$t('common.auth.dataPermission')}
          </div>
          
          <div class="auth-list-item-child__content">
            
            {dataAuths.map(item => {
              return (
                <div class="auth-list-item-child__right-data__row">
                  <div class="auth-list-item-child__right-data__row-name">
                    { item.name }
                  </div>
                  { this.renderAuthListItemChildrenRightDataAuthRadio(h, item) }
                  { this.renderAuthListItemLink(h, item) }
                </div>
              )
            })}
            
          </div>
          
        </div>
      )
    },
    renderAuthListItemLink(h: CreateElement ,node: AuthTreeNode) {

      // 开了自定义数据权限灰度
      if(this.customDataAuthGray) {
        // const showLinkEnumArray = [AuthEnum.CASE_VIEW, AuthEnum.CUSTOMER_VIEW, AuthEnum.TASK_VIEW] as unknown as string []
        // if(this.currentRoleCusDataAuthorityListMap[node.key] && node?.depth !== 3) {
        if(this.currentRoleCusDataAuthorityListMap[node.key] && node.depth === 4) {
          return this.renderCustomDataAuthLink(this.currentRoleCusDataAuthorityListMap[node.key])
        }
      }
      
      if(node.key == AuthEnum.VIP_REPORT_VIEW) return this.renderAuthListItemReportLink()

      if(node.key == AuthEnum.NEW_PROVIDER_EDIT) return this.renderAuthListItemServiceManagetLink()

    },

    // 报表查看权限
    renderAuthListItemReportLink() {

      if (!this.isShowReportSetting) return null
      
      return (
        <div 
          class="auth-list-item-child__right-data__row-report link-text"
          onClick={openTabSettingReportAuth}
        >
          {`(${this.$t('account.viewReportPermission')})`}
        </div>
      )
      
    },
    // 自定义数据权限link
    renderCustomDataAuthLink(moduleData: RoleCusDataAuthorityListItem) {
      if (!this.isEditMode) return null;
      return (
        <div 
          class="auth-list-item-child__right-data__row-report link-text"
          onClick={()=> {
            this.$emit(AuthListItemEventEnum.HandleSetCustomDataPermission, moduleData)
          }}
        >
        {isEmpty(moduleData.editConditionList) ? `${this.$t('account.viewCustomDataPermission')}` : t('common.base.lookcustomData')}
      </div>
      )
    },

    // 查看服务商权限管理页面
    renderAuthListItemServiceManagetLink() {

      return (
          <div
              class="auth-list-item-child__right-data__row-report link-text"
              onClick={openTabServiceManageAuth}
          >
            {`(${this.$t('account.viewServiceManagePermission')})`}
          </div>
      )

    },

    // 查看仓库权限组设置
    renderAuthListItemWarehouseLink(h: CreateElement ,node: AuthTreeNode) {

      if(node.key != AuthEnum.VIP_CLOUD_WAREHOUSE_SETTING) return ;

      return (
          <span
              class="auth-list-item-child__right-data__row-report link-text"
              onClick={openTabWarehouseRoleAuth}
          >
            {`(${this.$t('account.viewWarehousePermission')})`}
          </span>
      )

    },
    renderAuthListItemChildrenRightDataAuthRadio(h: CreateElement, child: AuthTreeNode) {
      
      const isUserRadio = child?.aLevel != 2
      const isTagRadio = child?.aLevel == 2 || child?.aLevel == 3
      const showCustomDataAuth = this.customDataAuthGray && this.currentRoleCusDataAuthorityListMap[child.key] 
      return (
        <div class="auth-list-item-child__radio">
          <el-radio-group 
            disabled={this.disabled}
            value={child.depth} 
            onInput={(value: string) => {
              this.onRadioChangeHandler(child, value)
            }}
          >
            
            <el-radio label={RoleAuthDepthEnum.All}>
              {this.$t('common.base.all')}
            </el-radio>
            
            {isTagRadio && (
              <el-radio label={RoleAuthDepthEnum.Dept}>
                {this.$t('common.base.department')}
              </el-radio>
            )}
            
            {isUserRadio && (
              <el-radio label={RoleAuthDepthEnum.Person}>
                {this.$t('common.base.personal')}
              </el-radio>
            )}

            {showCustomDataAuth && (
              <el-radio label={RoleAuthDepthEnum.custom}>
                {this.$t('common.base.customData')}
              </el-radio>
            )}
          </el-radio-group>
        </div>
      )
    },
    renderAuthListItemChildren(h: CreateElement, child: AuthTreeNode): any {
      return (
        <div class="auth-list-item-child">
          { this.renderAuthListItemChildrenLeft(h, child) }
          { this.renderAuthListItemChildrenRight(h, child) }
        </div>
      )
    },
  },
  render(h: CreateElement) {
    
    const headerClassNames = [
      'auth-list-item__header',
      this.data.expanded ? 'auth-list-item__header--expanded' : '',
    ]
    
    return (
      <div class={ComponentNameEnum.AuthListItem}>
        
        <div 
          class={headerClassNames}
          onClick={() => {
            this.onExpandedChangeHandler(this.data, !this.data.expanded)
          }}
        >
          
          <i class="iconfont icon-arrow-right">
          </i>
          
          { this.authGroupName }
          
        </div>
        
        {this.data.expanded && (
          this.children.map((child) => {
            return this.renderAuthListItemChildren(h, child)
          })
        )}
        
      </div>
    ) as any
  }
}) as any
