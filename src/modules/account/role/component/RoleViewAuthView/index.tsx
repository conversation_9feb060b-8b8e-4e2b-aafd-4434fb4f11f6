/* components */
import { AuthTreeNode } from '@src/modules/account/role/component/AuthTree/model'
import { AuthList } from '@src/modules/account/role/component'
/* enum */
import ComponentNameEnum from '@model/enum/ComponentNameEnum'
import { RoleNameEnum } from '@model/biz/Role'
import EventNameEnum from '@model/enum/EventNameEnum'
/* hooks */
import { useStoreAuthGroups, useStoreFetchRoleDisPlayOrHidePhone, useStoreIsExportEncryption, useStoreLoading } from "@src/modules/account/role/store/hooks"
import useLoading from '@src/modules/account/role/hooks/useLoading'
import { useStoreRoleCusDataAuthorityList } from '@src/modules/account/role/store/hooks/customDataAuth'
import { useEventListener } from '@vueuse/core'
/* model */
import Role, { RoleAuthDepthEnum } from '@model/entity/Role/Role'
import { RoleCusDataAuthorityListItem } from '@src/modules/account/role/model/entity/CustomDataAuth'
/* scss */
import '@src/modules/account/role/component/RoleViewAuthView/index.scss'
/* vue */
import { ComponentInstance, computed, ComputedRef, defineComponent, Ref, ref, watch } from 'vue'
import { CreateElement } from 'vue'
/* util */
import { getDefaultCheckedKeys, getDefaultExpandedKeys, getWaterfallItemWidth } from '@src/modules/account/role/component/RoleViewAuthView/util'
import { findComponentsDownward, findComponentDownward, findBrothersComponents } from '@src/util/assist'
import { cloneDeep } from 'lodash'
import PinyinMatch from 'pinyin-match'

type RoleTreeRenderDataType = {
  data: AuthTreeNode;
}

export type RoleViewAuthViewProps = {
  disabled: boolean;
  searchValue: string;
}

export interface RoleViewAuthViewSetupState {
  currentSelectRole: ComputedRef<Role>;
  getCheckedKeys: () => string[];
  outsideShow: VoidFunction;
  outsideHide: VoidFunction;
  outsideGetCheckedKeys: () => Promise<string[]>;
  outsideGetAuthTreeData: () => AuthTreeNode[];
  outsideGetRoleCusDataAuthorityListDataData: ()=> RoleCusDataAuthorityListItem[];
}

export enum RoleViewAuthViewEventEnum {
  Input = 'input'
}

export type RoleViewAuthViewEmitsOptions = {
  input: () => void;
}

export type RoleViewAuthViewInstance = ComponentInstance & RoleViewAuthViewSetupState
export type RoleViewAuthViewVM = RoleViewAuthViewSetupState & RoleViewAuthViewProps & ComponentInstance

export default defineComponent({
  name: ComponentNameEnum.RoleViewAuthView,
  emits: [
    RoleViewAuthViewEventEnum.Input
  ],
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    searchValue: {
      type: String,
      default: ()=> ''
    }
  },
  data() {
    return {
      checkedKeys: [] as string[],
    }
  },
  setup(props: RoleViewAuthViewProps, { slots, emit }) {
    
    const { authGroups } = useStoreAuthGroups()
    const { fetchAuthGroupsLoading: loading } = useStoreLoading()
    const { isExportEncryption, setIsExportEncryption } = useStoreIsExportEncryption()
    const fetchRoleDisPlayOrHidePhone = useStoreFetchRoleDisPlayOrHidePhone()
    const { roleCusDataAuthorityList } = useStoreRoleCusDataAuthorityList()
    
    let waterfallItemWidth = ref(getWaterfallItemWidth())
    let waterfallIsFinishLoad = ref(false)
    
    
    let defaultCheckedKeys: Ref<string[]> = ref([])
    
    let isUseDefaultExpandedKeys = ref(false)
    
    const defaultExpandedKeys: ComputedRef<string[]> = computed(() => {
      return isUseDefaultExpandedKeys.value ? getDefaultExpandedKeys(authGroups.value) : []
    })
    
    let authTreeData = ref(
      AuthTreeNode.transformByAuthGroup(authGroups.value, props.disabled)
    )

    const roleCusDataAuthorityListData = ref(cloneDeep(roleCusDataAuthorityList.value))
    
    watch(
      authGroups, 
      (newValue) => {
        defaultCheckedKeys.value = getDefaultCheckedKeys(newValue)
        authTreeData.value = AuthTreeNode.transformByAuthGroup(authGroups.value, props.disabled)
        roleCusDataAuthorityListData.value = cloneDeep(roleCusDataAuthorityList.value)
      }, 
      {
        immediate: true
      }
    )

    const filterAuthTreeData = computed(()=> {
      if(!props.searchValue) {
        return removeFilterChildrenKey(authTreeData.value)
      }
      return filterForAuthTreeData(authTreeData.value)
    })


    function removeFilterChildrenKey(authTreeDataValue: AuthTreeNode[] = []) {

      for(let i = 0 ;i < authTreeDataValue.length; i++) {
        const authTreeDataItem = authTreeDataValue[i]
        if(authTreeDataItem?.filterChildren) {
          authTreeDataItem.filterChildren = undefined
        }

        if(Array.isArray(authTreeDataItem.children) && authTreeDataItem.children.length) {
          removeFilterChildrenKey(authTreeDataItem.children)
        }
      }
      return authTreeDataValue
    }


    function filterForAuthTreeData(authTreeDataValue: AuthTreeNode[] = authTreeData.value) {
      const authTreeDataResult: AuthTreeNode[] = []


      for(let i = 0 ;i < authTreeDataValue.length; i++) {
        const authTreeDataItem = authTreeDataValue[i]

        if(PinyinMatch.match(authTreeDataItem.name, props.searchValue)) {
          authTreeDataResult.push(authTreeDataItem)
        }

        if(Array.isArray(authTreeDataItem.children) && authTreeDataItem.children.length) {
          const childFilterArray = filterForAuthTreeData(authTreeDataItem.children)
          if(childFilterArray.length > 0) {
            const existMenuArrayIndex = authTreeDataResult.findIndex(item=> item.id === authTreeDataItem.id)
            authTreeDataItem.filterChildren = childFilterArray
            existMenuArrayIndex === -1 && authTreeDataResult.push(authTreeDataItem)
          }
        }
      }
      return authTreeDataResult
    }
    
    function onCheckboxIsExportEncryptionInputHandler() {
      
      setIsExportEncryption(!isExportEncryption.value)
      
      fetchRoleDisPlayOrHidePhone()
      
    }
    
    function outsideGetAuthTreeData() {
      return authTreeData.value
    }
    
    function outsideGetRoleCusDataAuthorityListDataData() {
      return roleCusDataAuthorityListData.value
    }

    function computedWaterfallItemWidth() {
      waterfallItemWidth.value = getWaterfallItemWidth()
    }
    
    function onWaterfallFinishLoadHandler() {
      waterfallIsFinishLoad.value = true
    }
    
    useEventListener(document, EventNameEnum.Resize, () => {
      computedWaterfallItemWidth()
    })
    
    return {
      authGroups,
      loading,
      authTreeData,
      roleCusDataAuthorityListData,
      defaultCheckedKeys,
      defaultExpandedKeys,
      isExportEncryption,
      waterfallItemWidth,
      waterfallIsFinishLoad,
      isUseDefaultExpandedKeys,
      filterAuthTreeData,
      outsideGetAuthTreeData,
      outsideGetRoleCusDataAuthorityListDataData,
      onCheckboxIsExportEncryptionInputHandler,
      onWaterfallFinishLoadHandler,
    }
    
  },
  computed: {
    defaultCheckedKeysMap(): Record<string, string> {
      return (
        this.checkedKeys.reduce(
          (acc, current) => {
            acc[current] = current
            return acc
          }, 
          {} as Record<string, string>
        )
      )
    }
  },
  watch: {
    authGroups(newValue) {
      this.checkedKeys = this.getCheckedKeys()
    }
  },
  mounted() {
    this.checkedKeys = this.getCheckedKeys()
  },
  methods: {
    getAttributes() {
      return {
        directives: [
          {
            name: 'loading',
            value: this.loading
          }
        ]
      }
    },
    getCheckedKeys() {
      
      const elTreeNodeComponents = findComponentsDownward(this, 'ElTreeNode')
      
      let checkedKeys: string[] = []
      
      let elCheckboxComponent = null

      
      elTreeNodeComponents.forEach((elTreeNodeComponent: Record<string, any>) => {
        elCheckboxComponent = findComponentDownward(elTreeNodeComponent, 'ElCheckbox')
        
        if (elCheckboxComponent?.value && elTreeNodeComponent?.node?.data?.key) {
          checkedKeys.push(elTreeNodeComponent.node.data.key)
        }
        
      })
      
      return checkedKeys
    },
    onNodeCheckHandler() {
      setTimeout(() => {
        this.checkedKeys = this.getCheckedKeys()
      }, 300)
    },
    async outsideGetCheckedKeys() {
      return this.checkedKeys
    },
    outsideSetCheckedKeys() {
      this.checkedKeys = this.getCheckedKeys()
    },
    setElTreeDefaultExpanded() {
      
      const elTreeComponents = findComponentsDownward(this, 'ElTree')
      
      let elTreeNodeComponents: Record<string, any>[] = []
      
      elTreeComponents.forEach((elTreeComponent: Record<string, any>) => {
        
        const elTreeNodeComponent = findComponentDownward(elTreeComponent, 'ElTreeNode')
        
        const brothersComponents = findBrothersComponents(elTreeNodeComponent, 'ElTreeNode').concat([elTreeNodeComponent])
        
        elTreeNodeComponents = elTreeNodeComponents.concat(brothersComponents)
        
      })
      
      elTreeNodeComponents.forEach(component => {
        component.handleExpandIconClick()
      })
      
    },
    renderExportEncrypt(h: CreateElement, data: RoleTreeRenderDataType) {
      
      if (data.data.key !== RoleNameEnum.EXPORT_IN) {
        return null
      }
      
      const isImportExportCheckboxChecked = (
        Boolean(this.defaultCheckedKeysMap[RoleNameEnum.SYSTEM])
        || Boolean(this.defaultCheckedKeysMap[RoleNameEnum.EXPORT_IN])
      )
      
      const disabled = this.disabled || !isImportExportCheckboxChecked
      
      const label = this.$t('common.label.exportEncryption')
      
      return (
        <el-checkbox
          key={label}
          label={label}
          disabled={disabled}
          value={this.isExportEncryption}
          onInput={this.onCheckboxIsExportEncryptionInputHandler}
        >
          { label }
        </el-checkbox>
      )
      
    },
    renderContent(h: CreateElement, data: RoleTreeRenderDataType) {
      
      const node = data.data
      const isLastLevel = node?.level == AuthTreeNode.lastLevel
      
      const isUserRadio = node?.aLevel != 2
      const isTagRadio = node?.aLevel == 2 || node?.aLevel == 3
      
      const disabled = this.disabled
      
      const isShowRadio = node?.isShowRadio && isLastLevel
      
      const onRadioChangeHandler = (value: string | number) => {
        node.depth = value as RoleAuthDepthEnum
      }
      
      const name = (
        <div class="role-auth-view-tree-row__name">
          { node?.name || '' }
        </div>
      )
      
      const radio = (
        <div class="role-auth-view-tree-row__radio" onClick={(event: Event) => {
          event.stopPropagation()
        }}>
          <el-radio-group disabled={disabled} value={node.depth} onInput={onRadioChangeHandler}>
            
            <el-radio label={RoleAuthDepthEnum.All}>
              {this.$t('common.base.all')}
            </el-radio>
            
            {isTagRadio && (
              <el-radio label={RoleAuthDepthEnum.Dept}>
                {this.$t('common.base.department')}
              </el-radio>
            )}
            
            {isUserRadio && (
              <el-radio label={RoleAuthDepthEnum.Person}>
                {this.$t('common.base.personal')}
              </el-radio>
            )}
            
          </el-radio-group>
        </div>
      )
      
      const exportEncrypt = this.renderExportEncrypt(h, data)
      
      if (exportEncrypt) {
        return (
          <div class="role-auth-view-tree-row">
            <div class="role-auth-view-tree-row__export">
              { name }
              { exportEncrypt }
            </div>
            { isShowRadio && radio }
          </div>
        )
      }
      
      return (
        <div class="role-auth-view-tree-row">
          { name }
          { isShowRadio && radio }
        </div>
      )
    }
  },
  render() {
    
    const attrs = this.getAttributes()
    
    const classNames = [
      ComponentNameEnum.RoleViewAuthView,
      this.waterfallIsFinishLoad ? 'role-view-auth-view-finish' : '',
      this.loading ? 'role-view-auth-view-overflow-hidden' : ''
    ]
    return (
      <div class={classNames} {...attrs}>
        <AuthList data={this.filterAuthTreeData} roleCusDataAuthorityListData={this.roleCusDataAuthorityListData} disabled={this.disabled} />
      </div>
    ) as any
  }
}) as any
