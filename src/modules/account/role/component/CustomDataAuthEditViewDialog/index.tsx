import { useDialog } from "@hooks/useDialog";
import { t } from "@src/locales";
import CustomDataAuthEditView from '@src/modules/account/role/component/CustomDataAuthEditView'
import { CreateCustomDataAuthListItemParams, RoleCusDataAuthorityListItem } from '@src/modules/account/role//model/entity/CustomDataAuth'
import { computed, defineComponent, ref, nextTick, PropType, toRefs } from "vue";
import { getOssUrl } from "@src/util/assets";
/* style */
import '@src/modules/account/role/component/CustomDataAuthPlaceholderView/style.scss'
import '@src/modules/account/role/component/CustomDataAuthEditViewDialog/style.scss'

import { useStoreRoleCusDataAuthorityList } from '@src/modules/account/role/store/hooks/customDataAuth'

import { CreateCustomDataAuthListItemParamsModuleListItemConditionItem } from "@src/modules/account/role/model/entity/CustomDataAuth";
import { Message, MessageBox } from "element-ui";
import { cloneDeep, isEmpty } from "lodash";
import { useStoreCurrentSelectRole, useStoreMode } from "@src/modules/account/role/store/hooks";
import { fetchRoleCustomDataViewUpdate, fetchRoleCustomDataViewRemove } from "@src/api/RoleApi";
import { exchangeRoleCusDataAuthorityListToServer } from "../RoleViewContentEditView/util";
export default defineComponent({
    props: {
        roleCusDataAuthorityListData: {
            type: Array as PropType<RoleCusDataAuthorityListItem[]>,
            default: () => []
        },
    },
    setup(props, { expose }) {
        const { roleCusDataAuthorityListData } = toRefs(props)
        const { visible, changeVisible, hideDialog, showDialog } = useDialog()
        const disabled = ref(false)
        const showConditionForm = ref(false)
        const defaultConditionFormValue = ref<RoleCusDataAuthorityListItem>()
        const customDataAuthEditViewRef = ref<typeof CustomDataAuthEditView>()
        const isViewAuthority = ref(false)
       

        const { setRoleCusDataAuthorityList, roleCusDataAuthorityList } = useStoreRoleCusDataAuthorityList()
        const { currentRoleId } = useStoreCurrentSelectRole()
        const { isUserViewMode  }  = useStoreMode()

        const mainImg = computed(()=> getOssUrl('custom_data_auth.png'))

        const showSubmitBtn = computed(()=> showConditionForm.value)

        const showRemoveBtn = computed(()=> !isEmpty(defaultConditionFormValue.value?.editConditionList) && currentRoleId.value)

        const modalTitle = computed(()=> {
            if(defaultConditionFormValue.value ) {
                return `${defaultConditionFormValue.value.moduleName}_自定义数据权限`
            }
            return '自定义数据权限'
        })



        const fetchSaveCusDataAuthorityForRole = async(roleCusDataAuthorityList: RoleCusDataAuthorityListItem[] )=> {
            try {
                disabled.value = true
                const { success } = await fetchRoleCustomDataViewUpdate({moduleList: exchangeRoleCusDataAuthorityListToServer(roleCusDataAuthorityList), roleId: currentRoleId.value})
                if(success) {
                    setRoleCusDataAuthorityList(roleCusDataAuthorityListData.value)
                    Message.success(t('common.base.saveSuccess'))
                }
            } catch(err) {
                console.error('[ fetchSaveCusDataAuthorityForRole error ]', err)
            } finally {
                disabled.value = false
            }
        }

        const confirm = async ()=> {
            const customDataAuthEditViewRefInstance = customDataAuthEditViewRef.value
            if(customDataAuthEditViewRefInstance) {
                customDataAuthEditViewRefInstance.$nextTick(async ()=> {
                    const validateCheckModuleListMessage = await customDataAuthEditViewRefInstance.checkModuleListIsEmpty()

                    if(validateCheckModuleListMessage) return Message.warning(validateCheckModuleListMessage as string)
                    const conditionList = customDataAuthEditViewRefInstance.formValue?.moduleList?.[0]?.conditionList || []
                    const existIndex = roleCusDataAuthorityList.value.findIndex(item=> item.authKey === defaultConditionFormValue.value?.authKey)
                    if(existIndex > -1 && defaultConditionFormValue.value) {
                        const cloneRoleCusDataAuthorityList = roleCusDataAuthorityListData.value
                        cloneRoleCusDataAuthorityList.splice(existIndex, 1,  { ...defaultConditionFormValue.value, editConditionList: conditionList })
                        // 因为移除了在详情页面的编辑所以注释了下面代码
                        // if(!isEmpty(conditionList) && isUserViewMode.value) {
                        //     await fetchSaveCusDataAuthorityForRole(cloneRoleCusDataAuthorityList)
                        // }
                    }
    
                    hideDialog()
    
                    defaultConditionFormValue.value && (defaultConditionFormValue.value.editConditionList = conditionList)
                })

            } else {
                handleUpdateAuthorityGlobalItem()
                hideDialog()
            }
        }


        /**
         * @des 初始化相关条件组的值对象
         */
        const genConditionItemValue = ()=> new CreateCustomDataAuthListItemParamsModuleListItemConditionItem();


        const initConditionFormValue = ()=> {
            const conditionList = !isEmpty(defaultConditionFormValue.value?.editConditionList) ? defaultConditionFormValue.value?.editConditionList ||  [[genConditionItemValue()]]  : [[genConditionItemValue()]]
            return {
                moduleList: [{ formId : defaultConditionFormValue.value?.formId, conditionList: conditionList}]
            }
        }

        const handleUpdateAuthorityGlobalItem = async ()=> {
            const existIndex = roleCusDataAuthorityList.value.findIndex(item=> item.authKey === defaultConditionFormValue.value?.authKey)
            const cloneRoleCusDataAuthorityList = roleCusDataAuthorityListData.value

            if(existIndex > -1) {
                cloneRoleCusDataAuthorityList[existIndex].editConditionList = []
                //  因为移除了在详情页面的编辑所以注释了下面代码
                // await fetchSaveCusDataAuthorityForRole(cloneRoleCusDataAuthorityList)
            }
        }

        const handleRemoveCustomData = async ()=> {
            await MessageBox.confirm('确定要移除吗?', '提示', {
                showCancelButton: true,
                confirmButtonText: '确定',
            })
            
           if(isUserViewMode.value) {
                // 因为移除了在详情页面的编辑所以注释了下面代码
                // try {
                //     disabled.value = true
                //     const { success } = await fetchRoleCustomDataViewRemove({roleId: currentRoleId.value})
                //     if(success) {

                //         Message.success(t('common.base.removeSuccess'))

                //         handleUpdateAuthorityGlobalItem()
                //         setRoleCusDataAuthorityList(roleCusDataAuthorityListData.value)
                //         hideDialog()
                //     }
                // } catch (err) {
                //     console.error('[ fetchRoleCustomDataViewRemove error ]', err)
                // } finally {
                //     disabled.value = false
                // }
            } else {
                handleRestForm()
                nextTick(()=> {
                    confirm()
                })
            }
        }

        const handleRestForm = ()=> {
            showConditionForm.value = false
            defaultConditionFormValue.value && (defaultConditionFormValue.value.editConditionList = [])
        }

        const handleDeleteCondition = (args: any, formValue: CreateCustomDataAuthListItemParams)=> {
            if(isEmpty(formValue.moduleList)) {
                handleRestForm()
            }
        }

        const showModal = (v: RoleCusDataAuthorityListItem, disabled:boolean)=> {
            if(!isEmpty(v)) {
                defaultConditionFormValue.value = cloneDeep(v)
            }


            !isEmpty(v?.editConditionList) ?
                showConditionForm.value = true
                :
                showConditionForm.value = false
            
            isViewAuthority.value = disabled

            showDialog()
        }

        const handleAddCusDataAuthority = ()=> {
            showConditionForm.value = !showConditionForm.value
        }

        const getModalAttrs = ()=> {
            return {
                class: 'custom-data-auth__edit-view-dialog',
                props: {
                  width: '800px',
                  title: modalTitle.value,
                  show: visible.value,
                  appendToBody: true,
                },
                on: {
                  'update:show': (v: boolean) => {
                    changeVisible(v)
                    // this.hideDialog();
                    // this.onDialogCloseHandler();
                  },
                  close: ()=> { changeVisible(false) }
                },
              }
        }

        expose({
            showModal
        })


        return ()=> (
            <base-modal {...getModalAttrs()}>
                { visible.value ?  !showConditionForm.value ? 
                    <div class="custom-data-auth__placeholder-view">
                        <div class="custom-data-auth__placeholder-view-content">
                            <p class="main">
                                <img src={mainImg.value} />
                                <p>
                                    <h1>什么是自定义数据权限？</h1>
                                    <p>售后宝支持在设置了角色权限的基础上，通过自定义数据权限的方式，帮助企业细化数据的权限范围</p>
                                    <el-button class="mt_8"  type="primary"  onClick={()=> handleAddCusDataAuthority()}>添加数据权限</el-button>
                                </p>
                            </p>
                        </div>
                    </div>
                    :
                    <CustomDataAuthEditView ref={customDataAuthEditViewRef} conditionForm={true} showModule={false} onDeleteCondition={handleDeleteCondition} customInitFormValueFun={initConditionFormValue}></CustomDataAuthEditView> 
                    :
                    null
                }
                <div class="dialog-footer" slot="footer">
                    {showRemoveBtn.value ? 
                        <el-button type="danger" disabled={disabled.value} onClick={handleRemoveCustomData}>
                            {t('common.base.remove')}
                        </el-button>
                        : null 
                    }
                    <el-button disabled={disabled.value} onClick={hideDialog}>
                        {t('common.base.cancel')}
                    </el-button>
                  
                    <el-button type="primary" disabled={disabled.value} onClick={confirm}>
                        {t('common.base.save')}
                    </el-button>
                      
                </div>
            </base-modal>
        )
    }
})