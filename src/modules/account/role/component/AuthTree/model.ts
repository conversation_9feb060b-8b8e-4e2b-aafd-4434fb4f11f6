/* model */
import { RoleAuthGroup } from "@model/entity/Role/Role"
import { MenuAuthSettingCheckboxItem } from "@src/modules/setting/authorizationView/model/interface/FormMenusAuth";


class AuthTreeNode {
  
  id: string;
  name: string;
  label: string;
  children: AuthTreeNode[];
  level: number;
  key: string;
  disabled: boolean;
  depth: number | null;
  isShowRadio: boolean;
  aLevel: number;
  checked: boolean;
  expanded: boolean;
  filterChildren?: AuthTreeNode[];
  
  static oneLevel: number = 1
  static twoLevel: number = 2
  static lastLevel: number = 3
  
  constructor(
    id: string = '', 
    name: string = '', 
    children: AuthTreeNode[] = [], 
    key: string = '', 
    level: number = 0, 
    disabled: boolean = false,
    depth: number | null = null,
    isShowRadio: boolean = false,
    aLevel: number = 3,
    checked: boolean = false,
    expanded: boolean = false
  ) {
    
    this.id = id
    this.name = name
    this.label = name
    this.children = children
    this.key = key || id
    this.level = level
    this.disabled = disabled
    this.depth = depth
    this.isShowRadio = isShowRadio
    this.aLevel = aLevel
    this.checked = checked
    this.expanded = expanded
  }
  
  static transformByAuthGroup(authGroups: RoleAuthGroup[], isDisabled: boolean = false): AuthTreeNode[] {
    
    let authTree: AuthTreeNode[] = []
    
    authGroups.forEach(authGroup => {
      
      let authTreeNode = new AuthTreeNode()
      
      if (authGroup.twoAuthorityGroupList) {
        
        authTreeNode = new AuthTreeNode(
          authGroup.groupId,
          authGroup.groupName,
          AuthTreeNode.transformByAuthGroup(
            authGroup.twoAuthorityGroupList as unknown as RoleAuthGroup[],
            isDisabled
          ),
          authGroup.groupId,
          AuthTreeNode.oneLevel,
          isDisabled,
        )
        
        authTreeNode.expanded = true
        
      }
      // @ts-ignore
      else if (authGroup.authorityList) {
        
        authTreeNode = new AuthTreeNode(
          // @ts-ignore
          authGroup.twoGroupId,
          // @ts-ignore
          authGroup.twoGroupName,
          AuthTreeNode.transformByAuthGroup(
            // @ts-ignore
            authGroup.authorityList as unknown as RoleAuthGroup[],
            isDisabled
          ),
          // @ts-ignore
          authGroup.twoGroupId,
          AuthTreeNode.twoLevel,
          isDisabled
        )
        
      }
      else {
        authTreeNode = new AuthTreeNode(
          // @ts-ignore
          authGroup?.id, 
          // @ts-ignore
          authGroup?.cnName, 
          [],
          // @ts-ignore
          authGroup?.enName,
          AuthTreeNode.lastLevel,
          isDisabled,
          // @ts-ignore
          authGroup?.depth,
          // @ts-ignore
          authGroup?.supportData == 1,
          // @ts-ignore
          authGroup?.aLevel,
          // @ts-ignore
          authGroup?.check
        )
      }
      
      authTree.push(authTreeNode)
      
    })
    
    return authTree
    
  }

  static transformByMenusGroup(authGroups: MenuAuthSettingCheckboxItem[], isDisabled: boolean = false, level = 1): AuthTreeNode[] {
    
    let authTree: AuthTreeNode[] = []
    
    authGroups.forEach(authGroup => {
    
      // 这里check是因为全选的问题
      const checked = authGroup.children?.length && authGroup.check ? authGroup.children.every(item=> item.check) : authGroup.check

      const authTreeNode = new AuthTreeNode(
          // @ts-ignore
          authGroup?.id, 
          // @ts-ignore
          authGroup?.cnName, 
          AuthTreeNode.transformByMenusGroup(
            // @ts-ignore
            authGroup?.children ? authGroup.children : [],
            isDisabled,
            level + 1
          ),
          // @ts-ignore
          authGroup?.enName,
          level,
          isDisabled,
          // @ts-ignore
          authGroup?.depth,
          // @ts-ignore
          authGroup?.supportData == 1,
          // @ts-ignore
          authGroup?.aLevel,
          // @ts-ignore
          checked
        )
      // }
      authTreeNode.expanded = authGroup.parentId === 0
      
      authTree.push(authTreeNode)
      
    })
    
    return authTree
    
  }
  
}

export {
  AuthTreeNode,
}

export default {
  
}