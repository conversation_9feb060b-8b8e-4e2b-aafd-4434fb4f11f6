<template>
  <div id="doMyself-box">
    <div v-if="newMenuList.length>0" class="flex-x al-start">
      <div class="left-menu">
        <div class="menu-title">{{ menTitle }}</div>
        <template v-for="(item, index) in newMenuList">
          <nav
            :class="`menu-list ${nowMenu == index ? 'menu-checked' : ''}`"
            :key="index"
            @click="changePage(item,index)"
          >
            <div class="icon-box">
              <i
                :class="
                  `iconfont ${item.icon} ${
                    nowMenu == index ? 'font-16 font-w-600' : 'font-14'
                  }`
                "
              ></i>
            </div>
            <span>{{ item.name }}</span>
          </nav>
        </template>
      </div>
      <component :is="newMenuList[nowMenu].comName" ref="setData" :show-nav="false"></component>
    </div>
  </div>
</template>
<script>
import { storageGet } from '@src/util/storage.ts';
import subManage from '../setting/subManage/index.vue';
import smsMessage from '../setting/smsMessage/index.vue';
import toastList from '../toastList/toastList';
import wxSet from '../wxSet/wxSet';
import doMyselfSet from '../setting/index';
import doMyselfSetV2 from '../settingV2/index';
import miniProgramSet from '../miniProgramSet/index'
/* mixin */
import ThemeMixin from '@src/mixins/themeMixin/index.ts';
import { getRootWindow } from '@src/util/dom'
import { getDoorAuth } from '@src/api/PortalApi.ts'
import * as SettingApi from '@src/api/SettingApi.ts';
/* mixin */
import { VersionControlSettingMixin } from '@src/mixins/versionControlMixin';


import { getRootWindowInitData } from '@src/util/window';
const rootWindowInitData = getRootWindowInitData();

export default {
  name: 'do-myself-view',
  mixins: [
    ThemeMixin,
    VersionControlSettingMixin
  ],
  props: {
    initData: {
      type: Object,
      default: () => ({}),
    },
  },
  provide() {
    return {
      initData: this.initData,
    };
  },
  data() {
    return {
      nowMenu: 2, // isJspAndVue ? 0 门户页面设置 1 客户自助门户 2 公众号设置 : 0 消息设置 1 订阅通知管理 2 短信消息设置 3 消息记录
      menTitle: '自助门户设置',
      isJspAndVue:false, // 是否jsp+vue页面 该页面有两种显示方式.1：jsp+vue 2:纯vue
      portalAuth: {},
      newMenuList:[],
      basicEditionAuth: {}
    };
  },
  computed: {
    linkControl() {
      // return this.initData.openLinkC;
      const RootWindow = getRootWindow(window);
      return RootWindow.grayAuth?.portal || false;
    },
    doorUpgrade() {
      // return this.initData?.doorUpgrade;
      const RootWindow = getRootWindow(window);
      return RootWindow.grayAuth?.door || false;
    },
    showPortal() {
      const RootWindow = getRootWindow(window);

      if (!RootWindow.grayAuth?.door) return false;

      if (
        this.portalAuth.doorRenovation
        || this.portalAuth.doorSwitch
        || this.portalAuth.lookShare
        || this.portalAuth.updateShare
      )
        return true;

      return false;
    },
    showMiniProgramSet(){
      const RootWindow = getRootWindow(window);
      return RootWindow.grayAuth?.appletsService
    },
    isShowWxSet() {
      return this._isShowWxSet;
    },
  },
  async created() {
    await this.getPortalAuth();
    await this.getBasicEditionAuth();
    let type = window.location.href.split('/')[
      window.location.href.split('/').length - 1
    ];
    // 带锚点的链接
    if (type.indexOf('#') > -1) type = type.split('#')[0];
    if (type.indexOf('?') > -1) type = type.split('?')[0];
    let JspAndVuePage = ['toastList']
    if (JspAndVuePage.includes(type)) {
      this.isJspAndVue = true;
    } 
    this.newMenuList = await this.getTabList()
    // 如果从消息设置进入，将侧边栏改为消息设置的内容
    if (this.isJspAndVue) {
      this.menTitle = '消息设置';
    }
    if(window.location.pathname.indexOf('/setting/serviceStation/customerPortal') > -1) {
      // /setting/serviceStation/customerPortal会重定向到/setting/doMyself/doMyselfSet
      type = 'doMyselfSet'
    }
    this.nowMenu = this.newMenuList.findIndex(v=>type.includes(v.type));
  },
  methods: {
    changePage(item, index) {
      // console.log('changePage', item); 
      // 消息设置需要特殊处理
      if (item.id === 'message' || item.id === 'email') {
        window.location.href = item.url;
        return;
      }
      let href = ''
      if(item.url.indexOf('/foundation/') > -1) {
        href = item.url;
      } else {
        href = `${this.$resourcePrefix}${item.url}`;
      }
      window.location.href = href;
      this.nowMenu = index;
    },
    // 获取售后宝基础版权限
    async getBasicEditionAuth () {

      try {
        const { data } = await SettingApi.getBasicEditionAuth();
        if (data) this.basicEditionAuth = data;
      } catch (error) {
        console.error('Caused: getBasicEditionAuth -> error', error);
      }
    },
    async getPortalAuth() {
      try {
        const { status, data } = await getDoorAuth();
        if (status !== 0) return;
        this.portalAuth = data;
      } catch (error) {
        console.error('getPortalAuth error', error);
      }
    },
    async getTabList(){
      let messageModules = []
      try {
        
        messageModules = await storageGet('setting-modules', '[]', 'keyvaluepairs');
        
      } catch (error) {
        console.log(error);
      }
      // jsp+vue 侧边栏
      let MenuList = [
        {
          id: 'message',
          name: '消息设置',
          icon: 'icon-bell-fill',
          show: this.isModuleShow('MESSAGE_DD_MESSAGE', messageModules),
          url:'/foundation/setting/message',
        },
        {
          name: '订阅通知管理',
          icon: 'icon-dingyue',
          comName: 'sub-manage',
          show: this.isModuleShow('MESSAGE_SUB_MANAGE', messageModules),
          url:'/setting/message/subManage',
        },
        {
          name: '短信消息设置',
          icon: 'icon-fuwubeizhu',
          comName: 'sms-message',
          show: this.isModuleShow('MESSAGE_SMS_MESSAGE', messageModules),
          url:'/setting/message/smsmessage',
        },
        {
          id: 'email',
          name: '邮件消息设置',
          icon: 'icon-mail-fill1',
          show: this.isModuleShow('MESSAGE_EMAIL_MESSAGE', messageModules),
          url:'/foundation/emailMessage',
        },
        {
          name: '消息记录',
          icon: 'icon-xitongrizhi',
          comName: 'toast-list',
          show: this.isModuleShow('MESSAGE_TOAST_LIST', messageModules),
          url:'/setting/doMyself/toastList', // 该页面是vue页面
          type:'toastList'
        },
      ]
      // 纯vue侧边栏
      let oldMenuList = [
        {
          name: '门户页面设置',
          icon: 'icon-formatpainter-fill',
          comName: 'portal',
          show:this.showPortal,
          url:'/portal',
          type:'portal'
        },
        {
          name: '门户其他设置',
          icon: 'icon-Gateway',
          comName: this.doorUpgrade ? 'do-myself-set-v2' : 'do-myself-set',
          show:true,
          url:'/setting/serviceStation/customerPortal',
          type:'doMyselfSet'
        },
        {
          name: '公众号设置',
          icon: 'icon-weixin2',
          comName: 'wx-set',
          show: true,
          url: '/setting/doMyself/wxSet',
          type: 'wxSet'
        },
        {
          name: '微信小程序设置',
          icon: 'icon-xiaochengxu2-copy',
          comName: 'mini-program-set',
          show:this.showMiniProgramSet,
          url:'/setting/doMyself/miniProgramSet',
          type:'miniProgramSet'
        },
      ]

      if (this.basicEditionAuth.newsConfigDto && this.basicEditionAuth.newsConfigDto.hideSubscribe) {
        MenuList = MenuList.filter(item => item.name !== '订阅通知管理')
      }

      if (!this.isShowWxSet) {
        oldMenuList = oldMenuList.filter(item => item.type !== 'wxSet')
      }
      return this.isJspAndVue ? MenuList.filter(v=>v.show) : oldMenuList.filter(v=>v.show)
    },
    isModuleShow(moduleId, messageModules){
      let flag = false;
      for (let index = 0; index < messageModules.length; index++) {
        if(messageModules[index].itemName === moduleId) {
          flag = true;
          break;
        }
      }
      return flag;  
    },
  },
  components: {
    [subManage.name]: subManage,
    [smsMessage.name]: smsMessage,
    [toastList.name]: toastList,
    [wxSet.name]: wxSet,
    [doMyselfSet.name]: doMyselfSet,
    [doMyselfSetV2.name]: doMyselfSetV2,
    [miniProgramSet.name]:miniProgramSet
  },
};
</script>
<style lang="scss">
.al-start {
  display: flex;
  align-items: flex-start;
  height: 100%;
}
.flex-1 {
  flex: 1;
}
.mar-b-12 {
  margin-bottom: 12px;
}
.mar-r-12 {
  margin-right: 12px;
}
.font-12 {
  font-size: 12px;
}
.font-14 {
  font-size: 14px;
}
.font-16 {
  font-size: 16px;
}
.al-c {
  align-items: center !important;
}
label {
  margin-bottom: 0;
}
#doMyself-box {
  padding: 16px;
  position: relative;
  box-sizing: border-box;
  height: 100vh;
  max-width: 100vw;
}
#doMyself-components-box {
  // margin-left: 10px;
  flex: 1;
  box-sizing: border-box;
  height: 100%;
  overflow-y: auto;
  border-radius: 4px;
}
::-webkit-scrollbar-track {
  background: transparent;
}
img {
  width: 100%;
  height: 100%;
}
.flex-x {
  display: flex;
  .left-menu {
    display: none;
			box-sizing: border-box;
      border-radius: 4px;
      background: #fff;
      width: 194px;
      min-width: 194px;
      height: 100%;
      margin-right: 10px;
			.menu-title {
				padding: 0 16px;
        line-height: 48px;
        margin: 0;
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #262626;
			}
			.menu-list {
				padding: 0 16px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #595959;
        line-height: 40px;
        text-decoration: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        &:hover {
          color:#595959;
          background-color: rgba(0, 0, 0, 0.0392156863);
        }
        &.menu-checked {
          background-color: $color-primary-light-1;
          color: $color-primary-light-6;
        }
        span {
          font-size: 14px;
        }
        .icon-box {
          width: 22px;
          height: 22px;
          display: flex;
          align-items: center;
          margin-right: 10px;
          i{
            font-size: 14px;
          }
        }
      }
  }
}
</style>
