<template>
  <div class="flex-1" v-loading.fullscreen.lock="fullscreenLoading" >
    <div class="flex-1 my-shop-box">
      <!-- ruler-box start-->
      <div class="box-title">{{$t('portal.setting.title1')}}</div>
      <div class="ruler-box open-setting" :class="{pt15:!setData.serviceStationConfig.selfHelpEnabled}" v-if="setData">
        <div class="ruler-set-item flex-x">
          <div class="flex-1">
            <div class="ruler-set-item-title">
              {{$t('portal.setting.title2')}}
            </div>
            <div>{{$t('portal.setting.text1')}}<span class="link-text" @click="goToPortal">{{$t('portal.setting.linkText1')}}</span></div>
          </div>

          <el-switch
            v-model="setData.serviceStationConfig.selfHelpEnabled"
            @change="saveSwitchState($event, 'selfHelpEnabled')"
            :active-text="setData.serviceStationConfig.selfHelpEnabled ? $t('common.base.enable') : $t('common.base.disable')"
          >
          </el-switch>
        </div>
      </div>
      <div class="ruler-box" v-if="setData && showSetting">
        <!-- ruler-set-list start -->

        <div class="ruler-set-item">
          <div class="ruler-set-item-title">{{$t('portal.setting.title3')}}</div>
          <el-radio-group
            v-model="setData.serviceStationConfig.loginValidate"
            @change="change($event, 'loginValidate')"
          >
            <div class="mar-b-12">
              <el-radio class="mar-r-16" :label="false"
              >{{$t('portal.setting.radioLabel1')}}</el-radio
              >
            </div>
            <div>
              <!-- 纯客服云版本不显示邮箱文案 -->
              <el-radio :label="true">
                {{ _isShowEmail ? $t('portal.setting.radioLabel2') : $t('portal.setting.radioLabel3') }}
              </el-radio>
            </div>
          </el-radio-group>
        </div>

        <div class="ruler-set-item">
          <div class="ruler-set-item-title">{{$t('portal.setting.title4')}}</div>
          <el-radio-group
            v-model="setData.serviceStationConfig.validateBySms"
            @change="change($event, 'validateBySms')"
          >
            <div class="mar-b-12">
              <el-radio class="mar-r-16" :label="true">
                {{ _isShowEmail ? $t('portal.setting.radioLabel4') : $t('portal.setting.radioLabel5') }}
              </el-radio>
            </div>
            <div>
              <el-radio :label="false">{{$t('portal.setting.radioLabel6')}}</el-radio>
            </div>
          </el-radio-group>
        </div>

        <div class="ruler-set-item">
          <div class="ruler-set-item-title">{{$t('portal.setting.title5')}}</div>
          <el-radio-group
            v-model="setData.serviceStationConfig.showAllEvent"
            @change="change($event, 'showAllEvent')"
          >
            <div class="mar-b-12">
              <el-radio class="mar-r-16" :label="false"
              >{{$t('portal.setting.radioLabel7')}}</el-radio
              >
            </div>
            <div>
              <el-radio :label="true" class="mar-r-6">{{$t('portal.setting.radioLabel8')}}</el-radio>
              <el-tooltip
                class="item"
                effect="dark"
                :content="$t('portal.setting.contentText1')"
                placement="bottom"
              >
                <i class="iconfont icon-info mar-l-6 color-999 cur-point"></i>
              </el-tooltip>
            </div>
          </el-radio-group>
        </div>

        <div class="ruler-set-item flex-x">
          <div class="flex-1">
            <div class="ruler-set-item-title">
              {{$t('portal.setting.title6')}}
            </div>
            <div>{{$t('portal.setting.text2')}}</div>
          </div>

          <el-switch
            v-model="setData.serviceStationConfig.eventCancel"
            @change="change($event, 'eventCancel')"
            :active-text="setData.serviceStationConfig.eventCancel ? $t('common.base.enable') : $t('common.base.disable')"
          >
          </el-switch>
        </div>

        <div class="ruler-set-item flex-x">
          <div class="flex-1">
            <div class="ruler-set-item-title">
              {{$t('portal.setting.title7')}}
            </div>
            <div>
              <span>{{$t('portal.setting.linkText2')}}</span>
              <template v-if="setData.serviceStationConfig.orderedUserAsCustomer">
                <el-select 
                  class="customer-label" 
                  :value="orderedUserAsCustomer" 
                  value-key="id"
                  @focus="customerLabelFocus"
                  @change="selectCustomerLabel">
                  <el-option v-for="item in customerLabelList" :key="item.id" :value="item" :label="item.tagName"></el-option>
                </el-select>
                <el-button type="text" @click="jumpCustomerLabel">{{$t('portal.setting.btn1')}}</el-button>
              </template>
            </div>
          </div>

          <el-switch
            v-model="setData.serviceStationConfig.orderedUserAsCustomer"
            @change="changeCustomerLabel($event, 'orderedUserAsCustomer')"
            :active-text="setData.serviceStationConfig.orderedUserAsCustomer ? $t('common.base.enable') : $t('common.base.disable')"
          >
          </el-switch>
        </div>

        <div class="" v-if="_isShowLinkCShop">

          <div
            class="flex-x al-center"
            style="margin-bottom: 13px;"
          >
            <div class="ruler-set-item-title" style="margin-bottom: 0;">{{$t('portal.setting.title8')}}</div>
            <el-button class="mar-l-9" style="margin-left: 10px;" type="primary" @click="goToShopCenter"
            >{{$t('portal.setting.btn2')}}</el-button
            >
          </div>

          <el-radio-group
            v-if="!isOpenServiceMall"
            v-model="setData.serviceStationConfig.showAllItem"
            @change="change($event, 'showAllItem')"
          >
            <div class="mar-b-12">
              <el-radio class="mar-r-16" :label="false"
              >{{$t('portal.setting.radioLabel9')}}</el-radio
              >
            </div>
            <div>
              <el-radio :label="true">{{$t('portal.setting.radioLabel10')}}</el-radio>
            </div>
          </el-radio-group>
        </div>

        <!-- ruler-set-list end -->
      </div>
      <!-- ruler-box end -->
      
      <!-- setting-box start -->
      <div class="setting-box" v-if="showSetting">
        <div class="flex-x box-title">
          <div class="setting-show-box-tips">{{$t('portal.setting.tipText1')}}</div>
          <div class="setting-show-box-submit flex-x">
            <!-- <el-button type="primary" @click="submitSet">
              {{ isEditMode ? '保存' : '编辑' }}
            </el-button> -->
          </div>
        </div>
        <div class="flex-x al-s">
          <div class="setting-data">
            <keep-alive>
              <component
                :is="nowSettingData.name"
                ref="setData"
                :info-data="nowSettingData.data"
                :cmp-id="nowSettingData.id"
                :icon-set-id="nowSettingData.iconSetId"
                :disabled="!isEditMode"
                :event-list="eventList"
                :tenant="tenant"
                @saveIconItem="saveIconItem"
                @deleteIconItem="deleteIconItem"
                @changeInfoData="changeInfoData"
              ></component>
            </keep-alive>
          </div>
        </div>
      </div>
      <!-- setting-box end -->
      
      <!-- setting-box start -->
      <div class="setting-box" v-if="wechatSetting && showSetting">
        <div class="flex-x box-title">
          <div class="setting-show-box-tips">{{$t('portal.setting.tipText2')}}</div>
        </div>
        <div class="flex-x al-s">
          <div class="setting-data">
            <div class="data_p">
              <p v-if="isDingTalk">{{$t('portal.setting.text3')}}<span @click="openHelpDoc2()">{{$t('common.base.clickView')}}</span></p>
              <p v-else>{{$t('portal.setting.text11')}}<span @click="openHelpDoc()">{{$t('common.base.clickView')}}</span></p>
              <p>{{$t('portal.setting.text4')}}<a @click="submitDialog = true">{{haswxInfo ? $t('portal.setting.linkText3') : $t('portal.setting.linkText4')}}</a></p>
              <p><span @click="openHelpDoc3()"><i class="iconfont icon-fabu1"></i>{{$t('portal.setting.linkText5')}}</span></p>
            </div>
          </div>
        </div>
      </div>
      <!-- setting-box end -->
    </div>

    <!-- 填写微信信息弹窗 start-->
    <el-dialog
      :title="haswxInfo ? $t('portal.setting.linkText3') : $t('portal.setting.linkText4')"
      :visible.sync="submitDialog"
      width="450px"
    >
      <div class="mar-b-18" v-if="haswxInfo">{{$t('portal.setting.text5')}}</div>
      <div class="mar-b-18" v-else>{{$t('portal.setting.text10')}}</div>
      <el-form
        :model="wxRulerFormData"
        :rules="wxRuler"
        label-position="top"
        ref="wxRulerForm"
        
        class="demo-ruleForm"
        status-icon
      >
        <div class="mar-b-18 font-w-600">{{$t('portal.setting.text6')}}</div>
        <el-form-item label="APPID" prop="publicAppId">
          <el-input
            v-model="wxRulerFormData.publicAppId"
            autocomplete="off"
            :placeholder="$t('common.base.tip.defaultPlaceholder')"
          ></el-input>
        </el-form-item>
        <el-form-item label="APPSecret" prop="publicSecret">
          <el-input
            v-model="wxRulerFormData.publicSecret"
            autocomplete="off"
            :placeholder="$t('common.base.tip.defaultPlaceholder')"
          ></el-input>
        </el-form-item>
        <div class="mar-b-18 font-w-600">{{$t('portal.setting.text7')}}</div>
        <el-form-item label="APPSecret" prop="secret">
          <el-input
            v-model="wxRulerFormData.secret"
            autocomplete="off"
            :placeholder="$t('common.base.tip.defaultPlaceholder')"
          ></el-input>
        </el-form-item>
        <el-form-item label="APPID" prop="appId">
          <el-input
            v-model="wxRulerFormData.appId"
            autocomplete="off"
            :placeholder="$t('common.base.tip.defaultPlaceholder')"
          ></el-input>
        </el-form-item>
        <div class="mar-b-18 font-w-600">{{$t('portal.setting.text8')}}</div>
        <el-form-item label="mch_id" prop="matchId">
          <el-input
            v-model="wxRulerFormData.matchId"
            autocomplete="off"
            :placeholder="$t('common.base.tip.defaultPlaceholder')"
          ></el-input>
        </el-form-item>
        <el-form-item :label="$t('portal.setting.label1')" prop="apiSecret">
          <el-input
            v-model="wxRulerFormData.apiSecret"
            autocomplete="off"
            :placeholder="$t('common.base.tip.defaultPlaceholder')"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="!haswxInfo">
        <base-button type="ghost" @event="submitDialog = false"
        >{{$t('common.base.cancel')}}</base-button
        >
        <base-button type="primary" @event="submitWxData">{{$t('common.base.makeSure')}}</base-button>
      </div>
    </el-dialog>
    <!-- 填写微信信息弹窗 end-->
  </div>
</template>
<script>
import { getRootWindowInitData } from '@src/util/window'
import { openAccurateTab } from '@src/util/platform';

import draggable from 'vuedraggable';
import _ from 'lodash';
import QRCode from 'qrcodejs2';
import BaseGallery from 'packages/BaseGallery';
import { getOssUrl } from '@src/util/assets'
import i18n from '@src/locales'
const headPhone = getOssUrl('/myShop/headPhone.png');
import {
  getRules,
  saveRules,
  getInfos,
  saveInfos,
  weChat,
  getEventList,
  getWechatSetting
} from '@src/api/LinkcApi';
import { getTenant,saveSwitchState, getCustomerTagList } from '@src/api/SettingApi.ts'
import { getDoorWeChatInfo } from '@src/api/PortalApi'
import Platform from '@src/util/platform.ts'
/* mixin */
import ThemeMixin from '@src/mixins/themeMixin/index.ts'
/* component */
import CompanyCardData from '@src/modules/doMyself/settingV2/CompanyCardData.vue'
import { PageRoutesTypeEnum } from 'pub-bbx-global/pageType/dist/enum/PageRoutesEnum'
import { getRootWindow } from '@src/util/dom';
/* mixin */
import { VersionControlOtherMixin, VersionControlLinkCMixin } from '@src/mixins/versionControlMixin'

export default {
  name: 'do-myself-set-v2',
  mixins: [ThemeMixin, VersionControlOtherMixin, VersionControlLinkCMixin],
  filters: {
    usualNum(val) {
      if (val > 10000) {
        return i18n.t('portal.setting.text9', {num: val / 10000});
      }
    },
  },
  // props: {
  //   initData: {
  //     type: Object,
  //     default: () => ({}),
  //   },
  // },
  inject: ['initData'],
  provide() {
    return {
      cancelInfoData: this.cancelInfoData,
      changeFullscreenLoading: this.changeFullscreenLoading,
    };
  },
  watch: {
    submitDialog(value) {
      if (value == false) {
        this.$refs['wxRulerForm'].resetFields();
      }
    },
  },
  computed: {
    // 是否在钉钉环境
    isDingTalk() {
      return Platform.isDingTalk()
    },
    showSetting() {
      return this.setData.serviceStationConfig?.selfHelpEnabled ?? false
    },
    // 是否开启商城灰度
    isOpenServiceMall() {
      const RootWindow = getRootWindow(window);
      return RootWindow?.grayAuth?.SERVICE_MALL ?? false;
    },
    tenantType() {
      return getRootWindowInitData()?.tenantType;
    },
  },
  data() {
    return {
      // 小程序介绍链接
      miniProgramReportlink: this.tenantType == 4 ? 'https://publink-hz.feishu.cn/wiki/wikcnwxq1JJdl9Oz6EtGgtBDHEb' : 'https://doc.shb.ltd/shb_xoqazk/ia4qctp2ly559d1m/gre7yumc5sz4ewod/fiy8kg2nmvst3uhd/wzx5uf8f5cx05prx.html',
      menuList: [
        {
          name: this.$t('portal.setting.name1'),
          icon: 'icon-Gateway',
        },
        {
          name: this.$t('common.pageTitle.pageWxSet'),
          icon: 'icon-weixin2',
        },
        {
          name: this.$t('common.pageTitle.pageSettingSmsmessage'),
          icon: 'icon-duanxin3',
        },
        {
          name: this.$t('common.pageTitle.pageToastList'),
          icon: 'icon-message',
        },
      ],
      nowMenu: 0,
      setData: '',
      radio: '1',
      value1: false,
      options: [
        {
          label: this.$t('portal.setting.radioLabel10'),
          value: true,
        },
        {
          label: this.$t('portal.setting.radioLabel9'),
          value: false,
        },
      ],
      options_value: 1,
      dataList: [
        {
          type: 'company-card',
          id: 1,
          data: {
            logoUrl: '',
            name: '',
            mobile: '',
            address: '',
            companyName: '',
          },
        },
        {
          type: 'icon-list',
          id: 2,
          data: [],
        },
        {
          type: 'shops-list',
          id: 3,
          data: [],
        },
        {
          type: 'wiki-enter',
          id: 4,
          data: [],
        },
      ],
      nowSettingDataId: -1,
      nowSettingIconId: '',
      nowSettingData: {},
      fullscreenLoading: false,
      submitDialog: false,
      orderedUserAsCustomer: {}, // 客户标签
      customerLabelList: [],
      wxRulerFormData: {
        appId: '',
        matchId: '',
        apiSecret: '',
        secret: '',
        publicAppId: '',
        publicSecret: '',
      },
      wxRuler: {
        // appId: [{ required: true, message: '请输入APPID', trigger: 'blur' }],
        matchId: [{ required: true, message: this.$t('portal.setting.tipText3'), trigger: 'blur' }],
        apiSecret: [
          { required: true, message: this.$t('portal.setting.tipText4'), trigger: 'blur' },
        ],
        publicAppId: [
          { required: true, message: this.$t('portal.setting.tipText5'), trigger: 'blur' },
        ],
        publicSecret: [
          { required: true, message: this.$t('portal.setting.tipText6'), trigger: 'blur' },
        ],
      },
      eventList: [],
      headPhone,
      isEditMode: true,
      tenant: {},
      haswxInfo: true,
      wechatSetting: false
    };
  },
  created() {
    this.fullscreenLoading = true;
    this.getWeChatInfo()
    this.getCustomerLabel()
    Promise.all([this.getSetData(), getEventList(), this.getInfos(), this.fetchTenant()])
      .then((res) => {
        if (res[0].status == 200) {
          this.setData = res[0].data;
          this.orderedUserAsCustomer = this.setData?.serviceStationConfig?.customerTags ? this.setData.serviceStationConfig.customerTags[0] : {};
        } else {
          this.$notify.close();
          this.$notify.error({
            title: this.$t('common.base.tip.httpIsError'),
            message: res[0].message,
            duration: 2000,
          });
        }
        
        if (res[1].status == 0) {
          this.$set(this, 'eventList', res[1].data);
        } else {
          this.$notify.close();
          this.$notify.error({
            title: this.$t('common.base.tip.httpIsError'),
            message: res[1].message,
            duration: 2000,
          });
        }
        if (res[2].status == 200) {
          this.transData(res[2].data).then((res_) => {
            this.$set(this, 'dataList', res_);
          });
        } else {
          this.$notify.close();
          this.$notify.error({
            title: this.$t('common.base.tip.httpIsError'),
            message: res[2].message,
            duration: 2000,
          });
        }
      })
      .finally(() => {
        this.$nextTick(() => {
          this.chooseNowSet(this.dataList[0]);
          this.fullscreenLoading = false;
        });
      });
  },
  methods: {
    selectCustomerLabel(value) {
      this.orderedUserAsCustomer = value;
      saveRules({
        type: 'orderedUserAsCustomer',
        state: this.setData?.serviceStationConfig?.orderedUserAsCustomer,
        customerTags: [
          this.orderedUserAsCustomer
        ]
      });
    },
    changeCustomerLabel(value, key) {
      if(!value) this.orderedUserAsCustomer = {}
      saveRules({
        type: key,
        state: value,
        customerTags: []
      });
    },
    getCustomerLabel() {
      getCustomerTagList().then(res => {
        if (!res.success) return
        this.customerLabelList = res?.data || []
      }).catch(err => {
        console.error(err)
      });
    },
    jumpCustomerLabel() {
      let fromId = window.frameElement.getAttribute('id');
      openAccurateTab({
        type: PageRoutesTypeEnum.PageCustomerManagement,
        fromId
      })
    },
    changePage(index) {
      if (this.nowMenu === index) {
        return;
      }
      if (index === 2) {
        window.location.href = `${this.$resourcePrefix}/setting/message/smsmessage`;
      } else if (index === 0) {
        window.location.href = `${this.$resourcePrefix}/setting/doMyself/doMyselfSet`;
      } else if (index === 1) {
        window.location.href = `${this.$resourcePrefix}/setting/doMyself/wxSet`;
      } else if (index === 3) {
        window.location.href = `${this.$resourcePrefix}/setting/doMyself/toastList`;
      }
      this.nowMenu === index;
    },
    openHelpDoc() {
      Platform.openLink(this.tenantType == 4 ? 'https://publink-hz.feishu.cn/wiki/wikcnORaIpU9PCURcoSlDUW0fMf' : 'https://doc.shb.ltd/shb_xoqazk/ia4qctp2ly559d1m/gre7yumc5sz4ewod/fiy8kg2nmvst3uhd/qq3t5qm0p3bhli3v.html');
    },
    openHelpDoc2() {
      Platform.openLink(this.tenantType == 4 ? 'https://publink-hz.feishu.cn/wiki/wikcnORaIpU9PCURcoSlDUW0fMf' : 'https://doc.shb.ltd/shb_xoqazk/ia4qctp2ly559d1m/gre7yumc5sz4ewod/axax7f0bha6kgw6l.html');
    },
    openHelpDoc3() {
      Platform.openLink(this.tenantType == 4 ? 'https://publink-hz.feishu.cn/wiki/wikcnwxq1JJdl9Oz6EtGgtBDHEb' : 'https://doc.shb.ltd/shb_xoqazk/ia4qctp2ly559d1m/gre7yumc5sz4ewod/fiy8kg2nmvst3uhd/wzx5uf8f5cx05prx.html');
    },
    getWeChatInfo() {
      getWechatSetting().then(res => {
        if (res.success) {
          this.wechatSetting = res.data
        } else {
          this.$notify.error({
            title: this.$t('common.base.tip.httpIsError'),
            message: res.msg,
            duration: 2000,
          });
        }
      })
      getDoorWeChatInfo().then(res => {
        if (res.code == '200') {
          if (res.data) {
            this.wxRulerFormData = res.data
          } else {
            this.haswxInfo = false
          }
        } else {
          this.$notify.close();
          this.$notify.error({
            title: this.$t('common.base.tip.httpIsError'),
            message: res.msg,
            duration: 2000,
          });
        }
      })
    },
    transData(data) {
      return new Promise((resolve, reject) => {
        try {
          let arr = [];
          for (let key in data) {
            let item = data[key];
            let items = {};
            if (key == 'baseInfo') {
              items['type'] = 'company-card';
              items['data'] = item;
            } else if (key == 'quickInfo') {
              items['type'] = 'icon-list';
              item.quickInfos.forEach((item_, index_) => {
                item_.cmpId = item.index;
                item_['id'] = index_ + 1;
              });
              items['data'] = item.quickInfos || [];
            } else if (key == 'shopResponse') {
              items['type'] = 'shops-list';
              items['data'] = item.shopInfos || [];
            } else if (key == 'wikiResponse') {
              items['type'] = 'wiki-enter';
              items['data'] = item.wikiInfos || [];
            }
            items['id'] = item.index;
            arr[item.index - 1] = items;
          }
          resolve(arr);
          // console.log(arr, "transData");
        } catch (error) {
          reject(error);
        }
      });
    },
    reverData() {
      return new Promise((resolve, reject) => {
        try {
          let data_ = _.cloneDeep(this.dataList);
          let obj = {};
          data_.forEach((item_, indexs_) => {
            let index_ = indexs_ + 1;
            if (item_.type == 'company-card') {
              obj['baseInfo'] = { ...item_.data, index: index_ };
            } else if (item_.type == 'icon-list') {
              obj['quickInfo'] = { quickInfos: item_.data, index: index_ };
            } else if (item_.type == 'shops-list') {
              let ids = item_.data.length > 0
                ? item_.data.map((res) => {
                  return res.num;
                })
                : [];
              obj['shopInfo'] = { marketIds: ids, index: index_ };
            } else if (item_.type == 'wiki-enter') {
              let ids = item_.data.length > 0
                ? item_.data.map((res) => {
                  return res.num;
                })
                : [];
              obj['wikiInfo'] = { wikiIds: ids, index: index_ };
            }
          });
          resolve(obj);
        } catch (error) {
          reject(error);
        }
      });
    },
    chooseNowSet(item) {
      if (item.type == 'icon-list') return;
      
      let id = item.id;
      if (this.nowSettingDataId == id) {
        return;
      }
      
      this.nowSettingDataId = id;
      
      if (id > 0) {
        let res_ = this.findNowSetData(this.nowSettingDataId).item;
        this.$set(this, 'nowSettingData', {
          name: `${res_.type}-data`,
          data: res_.data,
          id: res_.id,
        });
      }
    },
    changeThis(item) {
      let data_ = this.findNowSetData(item.id).item;
      let nowSettingData = {
        id: item.item.cmpId,
        name: `${data_.type}-data`,
        data: data_.data,
        iconSetId: item.item.id,
      };
      this.nowSettingDataId = item.item.cmpId;
      this.$set(this, 'nowSettingData', nowSettingData);
    },
    pushIcon(item) {
      let index = this.findNowSetData(item.id).index;
      this.dataList[index].data.push(item.item);
    },
    findNowSetData(id) {
      let res;
      if (id < 0) {
        return false;
      }
      try {
        for (let index = 0; index < this.dataList.length; index++) {
          const item = this.dataList[index];
          if (item.id == id) {
            res = { item, index };
            break;
          }
        }
      } catch (error) {
        console.warn(error)
      }

      return res;
    },
    saveIconItem(e) {
      let index = this.findNowSetData(e.id).index;
      this.$set(this.dataList[index].data, `${e.indexs}`, e.item);
    },
    changeInfoData(e) {
      let index = this.findNowSetData(this.nowSettingDataId).index;
      this.$set(this.dataList[index], 'data', e.item);
    },
    changeIconListData(value) {
      if (!this.dataList?.[1]) return

      this.$set(this.dataList[1], 'data', value)
    },
    deleteIconItem(e) {
      let index = this.findNowSetData(e.id).index;
      let data_ = _.cloneDeep(this.dataList[index].data);
      data_.splice(e.indexs, 1);
      this.$set(this.dataList[index], 'data', data_);
      
      this.$nextTick(()=>{
        if(data_.length < 1) this.chooseNowSet(this.dataList[0])
        else this.$refs.setShow[1].changeThis(data_[0]);
      })
    },
    cancelInfoData() {
      return;
    },
    changeFullscreenLoading(e) {
      if (this.fullscreenLoading === e) return;
      this.fullscreenLoading = e;
    },
    openLink(e) {
      this.$platform.openLink(e);
    },
    goToShopCenter() {
      const type = this.isOpenServiceMall
        ? PageRoutesTypeEnum.PageLinkcGoodsList
        : PageRoutesTypeEnum.PagePartShopSetting;
      Platform.openAccurateTab({ type });
    },
    copyUrl() {
      if (!this.setData.protalUrl) return;
      this.$copyText(`${this.setData.protalUrl}`).then(() => {
        this.$message({
          message: this.$t('portal.setting.tipText7'),
          duration: 1500,
          type: 'success',
        });
      });
    },
    submitWx() {
      this.submitDialog = true;
    },
    submitWxData() {
      this.$refs['wxRulerForm'].validate((valid) => {
        if (valid) {
          this.fullscreenLoading = true;
          let params = _.cloneDeep(this.wxRulerFormData);
          // 去除参数加密
          // let params = this.$safeParams(params_);

          weChat(params)
            .then((res) => {
              if (res.status == 200) {
                this.$message({
                  message: this.$t('portal.setting.tipText8'),
                  duration: 1500,
                  type: 'success',
                });
                this.setData.weChatQRCodeUrl = res.data;
                this.setData['writeData'] = true;
                this.submitDialog = false;
              } else {
                this.$notify.close();
                this.$notify.error({
                  title: this.$t('common.base.tip.httpIsError'),
                  message: res.message,
                  duration: 2000,
                });
              }
            })
            .finally(() => {
              this.fullscreenLoading = false;
            });
        }
      });
    },
    getSetData() {
      return getRules();
    },
    getInfos() {
      return getInfos();
    },
    change(e, key) {
      saveRules({
        type: key,
        state: e,
      });
    },
    submitSet() {      
      this.$refs.setData.saveData().then(() => {
        this.reverData().then((res) => {
          this.fullscreenLoading = true;
          saveInfos(res)
            .then((res) => {
              if (res.status == 200) {
                this.$message({
                  message: this.$t('portal.portalTip9'),
                  duration: 1500,
                  type: 'success',
                });
              } else {
                this.$notify.close();
                this.$notify.error({
                  title: this.$t('common.base.tip.httpIsError'),
                  message: res.message,
                  duration: 2000,
                });
              }
            })
            .finally(() => {
              this.fullscreenLoading = false;
            });
        });
      });
    },
    previewImg(url) {
      if (!url) return;
      if (url.target && url.target.nodeName == 'IMG')
        return BaseGallery.preview(url.target);
      let imgDom = document.createElement('img');
      imgDom.src = url;
      BaseGallery.preview(imgDom);
    },
    fetchTenant() {
      return (
        getTenant().then((result) => {
          if (result?.succ) {
            this.tenant = result.data
          } else {
            this.$platform.alert(result.message)
          }
        }).catch(error => {
          console.warn(error)
        })
      )
    },
    // 门户开关
    async saveSwitchState(e,flow){
      const params = {
        state:e,
        flow
      }
      try{
        const {status} = await saveSwitchState(params)
        if(status!==0) this.getSetData()
      }catch(err){
        console.log(err)
      }
    },
    goToPortal() {
      Platform.openAccurateTab({
        type:PageRoutesTypeEnum.PagePortal,
      });
    },
  },
  components: {
    draggable,
    [CompanyCardData.name]: CompanyCardData
  },
};
</script>
<style lang="scss">
.el-radio {
  font-weight: 400;
}
.el-switch__label {
  font-weight: 400;
}
.color-primary {
  @include fontColor();
}
.color-999 {
  color: #999;
}

.el-dialog__body {
  border-top: 1px solid rgba(0, 0, 0, 0.09);
  border-bottom: 1px solid rgba(0, 0, 0, 0.09);
  padding-top: 12px;
  padding-bottom: 0;
}

.ruler-box {
  background: #fff;
  padding: 16px;
  border-radius: 4px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;

  margin-bottom: 1px;
  .ruler-set-item-title {
    margin-bottom: 13px;
    font-weight: 600;
  }
  .ruler-set-item {
    margin-bottom: 24px;
  }
  .customer-label {
    margin: 0 10px;
  }

  &.open-setting{
    border-bottom: 0;
    border-radius: 0;
    margin-bottom: 0;
    padding-bottom: 0px;
    &.pt15{
      padding-bottom: 15px;
    }
    .ruler-set-item{
      margin-bottom: 0;
    }
  }
}
.box-title {
  height: 46px;
  font-weight: 600;
  display: flex;
  align-items: center;
  font-size: 16px;
  border-bottom: 1px solid #ddd;
  padding: 0 12px;
  background: #fff;
}
.info-box {
  > div {
    background: #fafafa;
    padding: 16px;
  }
  margin-top: 12px;
  background: #fff;
  padding: 16px;
  border-radius: 4px;

  border-top-left-radius: 0;
  border-top-right-radius: 0;
  flex-wrap: wrap;
  margin-bottom: 12px;
  a {
    @include fontColor();
  }
  .code-box {
    padding: 8px 16px 0 16px;
    border: 1px solid #ebeded;
    border-radius: 4px;
    .code-des {
      font-size: 12px;
      padding: 4px 0;
    }
    .qrcode {
      display: inline-block;
      img {
        width: 155px;
        height: 155px;
        background-color: #fff; //设置白色背景色
        box-sizing: border-box;
      }
    }
    img {
      width: 155px;
      height: 155px;
    }
  }
  .info-tips {
    height: 32px;
    line-height: 32px;
    border-radius: 3px;
    border: 1px solid #d8d8d8;
    padding: 0 6px;
  }
}
.setting-box {
  background: #fff;
  .setting-box-title {
  }
  .setting-show-box-tips {
    font-size: 16px;
    font-weight: 600;
    flex: 1;
  }
  .setting-show-box-submit {
    .tips {
      display: flex;
      align-items: center;
      font-size: 12px;
      color: #9ba3a1;
    }
  }
  .setting-show-box {
    justify-content: center;
    position: relative;
    border-right: 1px solid #ddd;

    padding: 20px 0;
    .menu-box-item {
      border: 1px dashed transparent;
      box-sizing: border-box;
    }
    .setting-show {
      background: url("../../../assets/img/iphoneX.png") no-repeat center 0;
      background-size: 100% 100%;
      // box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.12);
      position: relative;
      height: 669px;
      width: 375px;
      .setting-show-data-box {
        background: #f2f2f2;
        position: absolute;
        width: 339px;
        height: 572px;
        top: 45px;
        left: 18px;
        overflow-y: scroll;
        // background: #ff0000;
        .head-phone-box {
          position: relative;
          .head-phone {
            width: 100%;
            height: 69px;
          }
          .head-phone-con {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            margin: auto;
            height: 35px;
            line-height: 35px;
            text-align: center;
            font-weight: 600;
            border: 1px dashed transparent;
            border-bottom: none;
            width: 100%;
            padding: 0 30px;
          }
        }
      }
    }

    .menu-box-item-check {
      border-color: $color-primary !important;
    }
  }
  .setting-data {
    width: 330px;
    background: #fff;
    height: 691px;
    padding-top: 20px;
    border-radius: 4px;
  }
}
.el-switch__label {
  color: #9ba3a1;
}
::-webkit-scrollbar {
  display: none;
}
</style>
<style lang="scss" scoped>
::v-deep .el-radio__label{
  float: right;
}
::v-deep .el-tooltip{
  float: right;
}
.wx-box {
  display: flex;
  flex-direction: column;
  &-content {
    display: flex;
    flex: 1;
    &-left {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
  }
}
.wx-pv-count {
  ::v-deep .el-tooltip {
    float: none;
    color: #8C8C8C;
    cursor: pointer;
    font-size: 20px;
    position: relative;
    top: 2px;
  }
}
</style>

<style lang="scss">
.my-shop-box {
  margin-top: 0 !important;
  padding: 0 !important;
}
.setting-box {
  margin-top: 14px;
  .box-title {
    height: 54px;
    border-bottom: 0;
  }
  .setting-data {
    padding-top: 0;
    .data_p{
      padding: 0 12px 12px;
      p{
        font-size: 14px;
        line-height: 28px;
      }
      span,a{
        color: $color-primary;
        margin-left: 10px;
        cursor: pointer;
      }
    }
  }
  .form-info {
    padding-top: 0;
  }
}
</style>
