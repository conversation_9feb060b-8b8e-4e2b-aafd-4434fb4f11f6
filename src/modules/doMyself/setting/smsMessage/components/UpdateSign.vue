<template>
  <el-dialog title="修改签名" :visible.sync="showUpdateDialog" width="600px">
    <div class="base-modal-content">
      <!-- 内容主体区域 -->
      <el-form label-position="top" >
        <el-form-item label="签名名称" required>
          
          <el-input 
            v-if="isNewSmsSign"
            v-model="autograph" 
            placeholder="请输入签名名称[最多12个字]" 
            maxlength="12" 
            @input="autographInputHandle"
          >
          </el-input>
          
          <el-input 
            v-if="isOldSmsSign"
            v-model="autograph" 
            placeholder="请输入签名名称[最多18个字]" 
            maxlength="18" 
            @input="autographInputHandle"
          >
          </el-input>
          
        </el-form-item>
        
      </el-form>
      
      <div class="base-modal-content-tips">
        <p 
          class="base-modal-content-tips-title">
          说明：短信签名修改后所有非默认模板必须重新申请
        </p>
        
        <div class="base-modal-content-tips-content">
          修改签名需先进行
          <el-button 
            type="text"
            @click="onOpenQualificationDialog"
          >
            资质认证
          </el-button>
          。
        </div>
      </div>
      
      
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="showUpdateDialog = false">取 消</el-button>
      <el-button type="primary" @click="update" :disabled="pending">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
/* utils */
import { isFalsy, getRootWindow } from 'pub-bbx-utils'
import { getSmsAutograph, getSmsSupplier } from '@src/api/MessageApi'
import { SmsAutographApprovalStatusEnum, SmsSupplierEnum } from '@model/biz/SMS'

export default {
  name: 'update-sign',
  data() {
    return {
      pending: false,
      showUpdateDialog: false,
      autograph: '',
      isNewSmsSign: false,
    }
  }, 
  computed: {
    isOldSmsSign() {
      return isFalsy(this.isNewSmsSign)
    }
  },
  mounted() {
    this.getSmsSupplier()
  },
  methods: {
    getSmsSupplier() {
      this.pending = true
      return (
        getSmsSupplier()
        .then(res => {
          this.isNewSmsSign = res?.data === SmsSupplierEnum.VOLCENGINE
        })
        .catch(err => {
          this.isNewSmsSign = false
          console.error('getSmsSupplier error', err)
        })
        .finally(() => {
          this.pending = false
        })
      )
    },
    openDialog(autograph) {
      this.autograph = autograph;
      this.showUpdateDialog = true;
    },
    autographInputHandle(){
      this.autograph = this.autograph.replace(/(【|】|&)/g,'')
    },

    async update(){
      
      if (this.isNewSmsSign) {
        this.updateNew()
        return
      }
      
      this.updateOld()
      
    },
    async updateOld() {
      try {
        const len = this.autograph.length 
        if(!len || len < 3) return this.$platform.notification({
          title: '修改失败',
          message: !len ? '签名不能为空' : '签名长度为3～8个字符',
          type: 'error'
        })
        const params = {}
        params.autograph = this.autograph
        const { status, message } = await this.$http.post('/vipsms/saveAutograph', params, false)
        
        if (status !== 0) {
          this.$platform.notification({
            title: '修改失败',
            message: message || '',
            type: 'error'
          })
          return
        }
        this.showUpdateDialog = false
        this.$platform.notification({
          title: '修改成功',
          type: 'success'
        })
        this.$emit('update', this.autograph)
      } catch (e) {
        console.error(e)
      }
    },
    async updateNew() {
      try {
        const len = this.autograph.length 
        if(!len || len < 2) return this.$platform.notification({
          title: '修改失败',
          message: !len ? '签名不能为空' : '签名长度为2～12个字符',
          type: 'error'
        })
        const params = {}
        params.autograph = this.autograph
        const { status, message } = await this.$http.post('/api/message/outside/sms/manage/saveAutograph', params, false)
        
        if (status !== 0) {
          this.$platform.notification({
            title: '修改失败',
            message: message || '',
            type: 'error'
          })
          return
        }
          
        this.showUpdateDialog = false
        this.$platform.notification({
          title: '修改成功',
          type: 'success'
        })
        this.$emit('update', this.autograph)
      } catch (e) {
        console.error(e)
      }
    },
    onOpenQualificationDialog() {
      this.$emit('openQualificationDialog')
    }
  },
}
</script>

<style lang="scss" scoped>
::v-deep.el-dialog__headerbtn {
  top: 0!important;
}
.el-dialog__body {
  .el-select {
    width: 100%;
  }
}
</style>
<style lang="scss">
.base-modal-content-tips {
  display: flex;
  justify-content: space-between;
  .base-modal-content-tips-title {
    font-size: 13px;
    color: #dd4b39;
    margin-bottom: 0;
    line-height: 34px;
  }
  .base-modal-content-tips-content {
    .el-button {
      margin-left: -4px;
    }
  }
}
</style>