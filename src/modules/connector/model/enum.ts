import { t } from '@src/locales'

enum ConnectorModuleComponentNameEnum {
  
  ConnectorListView = 'connector-list-view',
  ConnectorListViewCreateDialog = 'connector-list-view-create-dialog',
  ConnectorListViewHeader = 'connector-list-view-header',
  ConnectorListViewHeaderView = 'connector-list-view-header-view',
  ConnectorListViewCardList = 'connector-list-view-card-list',
  ConnectorListViewCardItem = 'connector-list-view-card-item',
  ConnectorListViewTableIndex = 'connector-list-view-table-index',
  ConnectorListViewTable = 'connector-list-view-table',

  ConnectorSettingView = 'connector-setting-view',
  ConnectorSettingViewHeader = 'connector-setting-view-header',
  ConnectorSettingViewMain = 'connector-setting-view-main',
  
  ConnectorModuleCardItem = 'connector-module-card-item',
  
  ConnectorModuleConnectorDialogDetail = 'connector-module-connector-dialog-detail',
  ConnectorModuleConnectorDialogDetailHeader = 'connector-module-connector-dialog-detail-header',
  ConnectorModuleConnectorDialogDetailColumn = 'connector-module-connector-dialog-detail-column',
  ConnectorModuleConnectorDialogDetailAction = 'connector-module-connector-dialog-detail-action',
  ConnectorModuleConnectorDialogDetailActionQuery = 'connector-module-connector-dialog-detail-action-query',
  ConnectorModuleConnectorDialogDetailActionCreate = 'connector-module-connector-dialog-detail-action-create',
  ConnectorModuleConnectorDialogDetailSetting = 'connector-module-connector-dialog-detail-setting',
  ConnectorModuleConnectorDialogDetailTypeRadio = 'connector-module-connector-dialog-detail-setting-type-radio',
  
  ConnectorModuleAddCardDialog = 'connector-module-add-card-dialog',
  
  ConnectorModuleAddCardItem = 'connector-module-add-card-item',
  
  ConnectorModuleCreateConnectorDialog = 'connector-module-create-connector-dialog',
  ConnectorModuleCreateConnectorNameDialog = 'connector-module-create-connector-name-dialog',
  ConnectorModuleCreateConnectorDetailDialog = 'connector-module-create-connector-detail-dialog',
  
  ConnectorModuleEditConnectorDialog = 'connector-module-edit-connector-dialog',
  
  ConnectorModuleConnectorCard = 'connector-module-connector-card',
  ConnectorModuleConnectorCardMulti = 'connector-module-connector-card-multi',
  ConnectorModuleConnectorCardMultiCardTable = 'connector-module-connector-card-multi-card-table',
  ConnectorModuleConnectorCardComponentTable = 'connector-module-connector-component-table',
  ConnectorModuleConnectorCardMultiButtonGroup = 'connector-module-connector-card-multi-button-group',
  ConnectorModuleConnectorCardMultiOutsideAppPagination = 'connector-module-connector-card-multi-outside-app-pagination',
  
  ConnectorModulePaasIframeDialog = 'connector-module-paas-iframe-dialog',
  
  ConnectorModuleRuleForm = 'connector-module-rule-form',
  ConnectorModuleRuleFormAutoWidth = 'connector-module-rule-form__item-w-auto',
  ConnectorModuleRuleFormItem = 'connector-module-rule-form-item',
  ConnectorModuleRuleFormItemApp = 'connector-module-rule-form-item-app',
  ConnectorModuleRuleFormItemAppDialog = 'connector-module-rule-form-item-app-dialog',
  
  ConnectorModuleCardSettingMixin = 'connector-module-card-setting-mixin',
  ConnectorModuleCardSettingCustomerMixin = 'connector-module-card-setting-customer-mixin',

  ConnectorModuleCardSettingProjectMixin = 'connector-module-card-setting-project-mixin',
  ConnectorModuleCardSettingTaskMixin = 'connector-module-card-setting-task-mixin',
  ConnectorModuleCardSettingEventMixin = 'connector-module-card-setting-event-mixin',
  ConnectorModuleCardSettingOrderMixin = 'connector-module-card-setting-order-mixin',

  ConnectorModuleConnectorCreateMixin = 'connector-module-connector-create-mixin',
  ConnectorModuleConnectorCreateTaskMixin = 'connector-module-connector-create-task-mixin',
  ConnectorModuleConnectorCreateCustomerMixin = 'connector-module-connector-create-customer-mixin',
  ConnectorModuleConnectorCreateProductMixin = 'connector-module-connector-create-product-mixin',
  ConnectorModuleConnectorCreateEventMixin = 'connector-module-connector-create-event-mixin',
  ConnectorModuleConnectorCreateProjectMixin = 'connector-module-connector-create-project-mixin',
  ConnectorModuleConnectorCreateAddonsMixin = 'connector-module-connector-create-addons-mixin',

  ConnectorModuleConnectorRecordMixin = 'connector-module-connector-record-mixin',
  ConnectorModuleTriggerRecordMixin = 'connector-module-trigger-record-mixin',


}

const ConnectorModuleErrorMessageEnum: any = {
  ConnectorList: t('common.connector.connectorModuleErrorMessageEnum.ConnectorList'),
  ConnectDataList: t('common.connector.connectorModuleErrorMessageEnum.ConnectDataList'),
  ConnectorCardAdditionInfo: t('common.connector.connectorModuleErrorMessageEnum.ConnectorCardAdditionInfo'),
  ConnectorAllowAddConnectorData: t('common.connector.connectorModuleErrorMessageEnum.ConnectorAllowAddConnectorData'),
  DeleteConnector: t('common.connector.connectorModuleErrorMessageEnum.DeleteConnector'),
  DeleteConnectorData: t('common.connector.connectorModuleErrorMessageEnum.DeleteConnectorData'),
  CardRelationPaasApplicationList: t('common.connector.connectorModuleErrorMessageEnum.CardRelationPaasApplicationList'),
  ModuleList: t('common.connector.connectorModuleErrorMessageEnum.ModuleList'),
  ConnectorOptions: t('common.connector.connectorModuleErrorMessageEnum.ConnectorOptions'),
  SaveConnectorOptions: t('common.connector.connectorModuleErrorMessageEnum.SaveConnectorOptions')
}

enum AuthEnum {
  // 新建
  trigger_view = 'TRIGGER_CREATE',
  // 编辑
  trigger_edit = 'TRIGGER_EDIT',
  // 删除
  trigger_delete = 'TRIGGER_DELETE'
}

enum CreateConnectorDialogFieldNameEnum {
  // 关联应用表单
  RelationAppForm = 'relationAppForm',
  // 名称
  Name = 'name',
  // 描述
  Description = 'description',
  // 描述的国际化语言
  TitleLanguage = 'titleLanguage',
  // 描述的国际化语言
  DescLanguage = 'descLanguage'
}

enum ConnectorCardSingleFixedFieldNameEnum {
  // 操作人
  UserName = 'userName',
  // 更新时间
  UpdateTime = 'updateTime',
}

enum ConnectorCardMultiFixedFieldNameEnum {
  // 操作人
  UserName = 'userName',
  // 更新时间
  UpdateTime = 'updateTime',
  // 操作
  Operation = 'Operation',
}

enum ConnectorActionEnum {
  // 查询
  Select = 'SELECT',
}

enum ConnectorBizTypeEnum {
  // 工单
  Task = 'TASK',
  // paas
  Paas = 'PAAS',
  // 客户
  Customer = 'CUSTOMER',
  // 事件
  Event = 'EVENT',
  // 产品
  Product = 'PRODUCT',
  // 项目
  Project = 'PROJECT_MANAGER',

  //附加组件
  Addons = 'ADDONS',
  // 合同
  Contract = 'CONTRACT',
  // 订购单
  Indent='INDENT_ADDITIONAL'
}

enum ConnectorBizTypeIdEnum {
  // 客户
  Customer = '1',
  // 产品
  Product = '2',
}

enum ConnectorFromBizTypeEnum {
  Customer = '1',
  Product = '2',
  Indent = '7',
}

enum ConnectorOptionsActionEnum {
  // 查询
  Select = 'SELECT',
  // 更新
  Update = 'UPDATE',
  // 插入
  Insert = 'INSERT',
  // 删除
  Delete = 'DELETE',
  // 关联添加
  Add = 'ADD',
}

enum ConnectorOptionsActionMappingFieldKeyEnum {
  // 查询
  SELECT = 'supportSelect',
  // 更新
  UPDATE = 'supportUpdate',
  // 插入
  INSERT = 'supportInsert',
  // 删除
  DELETE = 'supportDelete',
}


enum ConnectorConditionTypeEnum {
  // 对应的字段
  FromField = 'fromField',
  // 固定值
  FixedValue = 'fixedValue',
  // 其他表单值
  AppFormField = 'appFormField',
  // 置空
  Empty = 'empty',
}

enum ConnectorOptionsFieldOptionValueTypeEnum {
  // 固定值
  FixedValue = 1,
  // 对应字段
  FromField = 2,
  // 外部表单
  FromAppField = 3,
}

enum ConnectorFieldOperateEnum {
  // 相等
  Equal = 'EQ',
  // 介于 在...之间
  Between = 'BETWEEN',
  // 包含
  CONTAINS = 'CONTAINS',
  // 不介于
  NOT_BETWEEN = 'NOT_BETWEEN',
  // 位于
  LOCATED = 'LOCATED',
  // 不位于
  NOT_LOCATED = 'NOT_LOCATED',
  // 不等于
  NOT_EQ = 'NOT_EQ',
  // 为空
  EMPTY = 'EMPTY',
}

enum ConnectorFieldTypeEnum {
  Address = 'address',
  Attachment = 'attachment',
  Customer = 'customer',
  Date = 'date',
  DateTime = 'datetime',
  SerialNumber = 'serialNumber',
  User = 'user',
  CustomerAddress = 'customerAddress',
  Linkman = 'linkman',
  Location = 'location',
  Product = 'product',
  // 关联工单
  RelationTask = 'relationTask',
  RelatedTask = 'related_task',
  RelatedCustomers = 'related_customers',
  // 服务部门
  Tags = 'tags',
  // 单行文本
  Text = 'text',
  JsonArray = 'jsonArray',
  JsonObject = 'jsonObject',
  aiLabel = 'aiLabel',
  // 仓位
  warehousePositionId = 'warehousePositionId',
  // 仓库
  warehouseId = 'warehouseId'
}

enum ConnectorFieldEnNameEnum {
  Linkman = 'linkman',
  LinkmanName = 'lmName',
  CustomerAddress = 'customerAddress',
  Customer = 'customer',
  Tags = 'tags',
}

enum ConnectorFieldNameEnum {
  Tags = 'tags',
  // 触发器知识库选择角色
  Role = 'role'
}

enum ConnectorAddressTypeEnum {
  // 有经纬度
  HasLatitudeOrLongitude = 1,
  // 无经纬度
  NoLatitudeOrLongitude = 0,
}

const ConnectorSourceOperateEum = {
  // TASK(1,"工单"), EVENT(2,"事件"), PRODUCT(3,"产品"), CUSTOMER(4,"客户"), PAAS(5,"PaaS")
  [ConnectorBizTypeEnum.Task]: 1,
  [ConnectorBizTypeEnum.Event]: 2,
  [ConnectorBizTypeEnum.Product]: 3,
  [ConnectorBizTypeEnum.Customer]: 4,
  [ConnectorBizTypeEnum.Paas]: 5,
  [ConnectorBizTypeEnum.Project]: 6,
}

const ConnectorBizTypeCnNameEnum: Record<string, string> = {
  // 工单
  [ConnectorBizTypeEnum.Task]: t('common.base.taskCenter'),
  // paas
  [ConnectorBizTypeEnum.Paas]: 'PAAS',
  // 客户
  [ConnectorBizTypeEnum.Customer]: t('common.base.customer'),
  // 事件
  [ConnectorBizTypeEnum.Event]: t('common.base.event'),
  // 产品
  [ConnectorBizTypeEnum.Product]: t('common.base.product'),

  [ConnectorBizTypeEnum.Project]: t('common.base.projectManage'),
}

enum TriggerSettingNodeTypeEnum {
  // 发起应用
  StartApplication = 0,
  // 触发动作
  TriggerAction = 1,
  // 条件
  Condition = 2,
  // 执行应用
  ExecuteApplication = 3,
  // 消息通知
  MessageNotification = 4,
}


enum ActionReqOrResTypeEnum {
  ToRequestFieldList = 'toRequestFieldList',
  ToResponseFieldList = 'toResponseFieldList'
}

const ConnectorOptionsActionTextEnum: Record<string, string> = {
  'SELECT': t('common.base.tableAction.query'),
  'INSERT': t('common.base.tableAction.insert'),
  'DELETE': t('common.base.tableAction.delete'),
  'UPDATE': t('common.base.tableAction.update')
}

enum AppTypeEnum {
  OutSide = 'outSide',
  InSide = 'inSide'
}

export {
  ConnectorModuleComponentNameEnum,
  ConnectorModuleErrorMessageEnum,
  CreateConnectorDialogFieldNameEnum,
  ConnectorCardSingleFixedFieldNameEnum,
  ConnectorCardMultiFixedFieldNameEnum,
  ConnectorActionEnum,
  ConnectorBizTypeEnum,
  ConnectorOptionsActionEnum,
  ConnectorConditionTypeEnum,
  ConnectorOptionsFieldOptionValueTypeEnum,
  ConnectorFieldOperateEnum,
  ConnectorFieldTypeEnum,
  ConnectorAddressTypeEnum,
  ConnectorFromBizTypeEnum,
  ConnectorBizTypeCnNameEnum,
  ConnectorSourceOperateEum,
  ConnectorFieldNameEnum,
  ConnectorBizTypeIdEnum,
  ConnectorFieldEnNameEnum,
  TriggerSettingNodeTypeEnum,
  ActionReqOrResTypeEnum,
  ConnectorOptionsActionTextEnum,
  ConnectorOptionsActionMappingFieldKeyEnum,
  AppTypeEnum,
  AuthEnum
}
