/* util */
import http from '@src/util/http'
/* model */
import MsgModel from '@model/MsgModel'
/* types */
import { 
  ConnectorGetEditOptionsParams,
  ConnectorGetEditOptionsResult,
  ConnectorGetModuleListParams, 
  ConnectorGetModuleListResult, 
  ConnectorGetOptionsParams,
  ConnectorGetOptionsResult,
  ConnectorSaveOptionsParams,
  ConnectorSaveOptionsResult,
  ConnectorDataDeleteParams,
  ConnectorDataDeleteResult,
  ConnectorCardDataListParams,
  ConnectorCardDataListResult,
  ConnectorAllowAddDataParams,
  ConnectorAllowAddDataResult,
  ConnectorCardAdditionInfoParams,
  ConnectorCardAdditionInfoResult,
  ConnectorInsertSelectCallParams,
  ConnectorInsertSelectCallResult,
  ConnectorGetFormOptionsParams,
  ConnectorGetModuleListItemParams,
  ConnectorGetAppModuleListResult,
  ConnectorGetAppModuleListItemResult,
  ConnectorDataRelationParams,
  ConnectorDataDisassociationParams
} from '@src/modules/connector/types'

const prefix = '/api/application'
/**
 * @description 新建连接器
 * @see http://yapi.shb.ltd/
 */
export function createConnector(params: any) {
  return http.post('', params)
}

/**
 * @description 更新连接器
 * @see http://yapi.shb.ltd/
 */
export function updateConnector(params: any) {
  return http.post('', params)
}

/**
 * @description 删除连接器
 * @see http://yapi.shb.ltd/
 */
export function deleteConnector(params: any) {
  return http.post('', params)
}

/**
 * @description 切换连接器开关
 * @see http://yapi.shb.ltd/
 */
export function switchConnector(params: any) {
  return http.post('', params)
}

/**
 * @description 获取连接器列表
 * @see http://yapi.shb.ltd/
 */
export function getConnectorList(params: any): Promise<any> {
  return http.post('', params)
}

/**
 * @description 查询附件组件关联应用
 * @see http://yapi.shb.ltd/
 */
export function getCardRelationPaasApplicationList(params: any): Promise<any> {
  return http.get(`${prefix}/outside/connector/getModuleList`, params)
}

/**
 * @description 获取连接器配置信息
 * @see http://yapi.shb.ltd/project/3088/interface/api/36648
 */
export function getConnectorOptions(params: ConnectorGetOptionsParams): Promise<MsgModel<ConnectorGetOptionsResult>> {
  return http.get(`${prefix}/outside/connector/getCreateOptions`, params)
}

/**
 * @description 获取连接器配置信息
 * @see http://yapi.shb.ltd/project/3088/interface/api/58673
 */
export function getConnectorOptionsV2(params: ConnectorGetOptionsParams): Promise<MsgModel<ConnectorGetOptionsResult>> {
  return http.post(`${prefix}/outside/connectorV2/getCreateOptions`, params)
}


/**
 * @description 获取编辑连接器的配置信息
 * @see http://yapi.shb.ltd/project/3088/interface/api/36762
 */
export function getEditConnectorOptions(params: ConnectorGetEditOptionsParams): Promise<MsgModel<ConnectorGetEditOptionsResult>> {
  return http.get(`${prefix}/outside/connector/getEditOptions`, params)
}

/**
 * @description 获取编辑连接器的配置信息
 * @see http://yapi.shb.ltd/project/3088/interface/api/36762
 */
export function getEditConnectorOptionsV2(params: ConnectorGetEditOptionsParams): Promise<MsgModel<ConnectorGetEditOptionsResult>> {
  return http.post(`${prefix}/outside/connectorV2/getEditOptions`, params)
}


/**
 * @description 保存连接器配置信息
 * @see http://yapi.shb.ltd/project/3088/interface/api/36660
 */
export function saveConnectorOptions(params: ConnectorSaveOptionsParams): Promise<MsgModel<ConnectorSaveOptionsResult>> {
  return http.post(`${prefix}/outside/connector/addOrUpdate`, params)
}

/**
 * @description 保存连接器配置信息
 * @see http://yapi.shb.ltd/project/3088/interface/api/58685
 */
export function saveConnectorOptionsV2(params: ConnectorSaveOptionsParams): Promise<MsgModel<ConnectorSaveOptionsResult>> {
  return http.post(`${prefix}/outside/connectorV2/addOrUpdate`, params)
}

/**
 * @description 获取模块列表
 * @see http://yapi.shb.ltd/project/3088/interface/api/36642
 */
export function getModuleList(params?: ConnectorGetModuleListParams): Promise<MsgModel<ConnectorGetModuleListResult>> {
  return http.get(`${prefix}/outside/connector/getModuleList`, params)
}

/**
 * @description 连接器获取应用列表
 * @see http://yapi.shb.ltd/project/3088/interface/api/58631
 */
export function getModuleAppList(params?: ConnectorGetModuleListParams): Promise<MsgModel<ConnectorGetAppModuleListResult>> {
  return http.get(`${prefix}/outside/connectorV2/getAppList`, params)
}

/**
 * @description 新版触发器模块使用了该组件 但是触发器获取应用列表里面又需要获取新的模块列表，连接器不同
 */
export function getModuleAppListV2(params?: any) {
  return http.get(`${prefix}/outside/trigger/v3/api/getAppList`, params)
}

/**
 * @description 获取应用列表 - 触发器模板 demo
 */
export function getModuleAppListDemo(params?: any) {
  return http.get(`${prefix}/outside/trigger/template/api/getAppList`, params)
}

/**
 * @description 获取模块下的列表
 * @see http://yapi.shb.ltd/project/3088/interface/api/58670
 */
export function getModuleAppItemsList(params?: ConnectorGetModuleListItemParams): Promise<MsgModel<ConnectorGetAppModuleListItemResult>> {
  return http.get(`${prefix}/outside/connectorV2/getModuleList`, params)
}

/**
 * @description 获取所有模块列表
 * @see http://yapi.shb.ltd/project/3088/interface/api/36642
 */
export function getAllModuleList(params?: ConnectorGetModuleListParams): Promise<MsgModel<ConnectorGetModuleListResult>> {
  return http.get(`${prefix}/outside/trigger/getModuleList`, params)
}
/**
 * @description 调用查询连接器获取数据
 * @see http://yapi.shb.ltd/project/3088/interface/api/36738
 */
export function getConnectorCardDataList(params: ConnectorCardDataListParams): Promise<MsgModel<ConnectorCardDataListResult>> {
  return http.post('/api/application/outside/call/selectCall', params)
}

/**
 * @description 调用查询连接器获取数据V2(兼容外部应用)
 * @see http://yapi.shb.ltd/project/3088/interface/api/36738
 */
export function getConnectorCardDataListV2(params: ConnectorCardDataListParams): Promise<MsgModel<ConnectorCardDataListResult>> {
  return http.post('/api/application/outside/call/selectCallV2', params)
}


/**
 * @description 调用连接器删除数据
 * @see http://yapi.shb.ltd/project/3088/interface/api/36744
 */
export function deleteConnectorData(params: ConnectorDataDeleteParams): Promise<MsgModel<ConnectorDataDeleteResult>> {
  return http.post('/api/application/outside/call/deleteCall', params)
}
/**
 * @description 调用连接器取消关联数据
 * @see http://yapi.shb.ltd/project/3088/interface/api/36744
 */
export function disassociationConnectorData(params: ConnectorDataDisassociationParams): Promise<MsgModel<ConnectorDataDeleteResult>> {
  return http.post('/api/application/outside/connector/deleteRelationshipRecord', params);
}
/**
 * @description 调用连接器关联数据
 * @see http://yapi.shb.ltd/project/3088/interface/api/36744
 */
export function addRelationshipRecord(params: ConnectorDataRelationParams): Promise<MsgModel<ConnectorDataDeleteResult>> {
  return http.post('/api/application/outside/connector/addRelationshipRecord', params);
}

/**
 * @description 是否允许继续添加数据
 * @see http://yapi.shb.ltd/project/3088/interface/api/36774
 */
export function allowAddConnectorData(params: ConnectorAllowAddDataParams): Promise<MsgModel<ConnectorAllowAddDataResult>> {
  return http.post('/api/application/outside/call/allowAddData', params)
}

/**
 * @description 获取附加组件表头和按钮的接口
 * @see http://yapi.shb.ltd/project/3088/interface/api/36798
 */
export function getCardAdditionInfo(params: ConnectorCardAdditionInfoParams): Promise<MsgModel<ConnectorCardAdditionInfoResult>> {
  return http.get('/api/application/outside/call/additionInfo', params)
}

/**
 * @description 连接器 新增时用于回显填充数据的查询接口
 * @see http://yapi.shb.ltd/project/3088/interface/api/36732
 */
export function getConnectorInsertSelectCall(params: ConnectorInsertSelectCallParams): Promise<MsgModel<ConnectorInsertSelectCallResult>> {
  return http.post('/api/application/outside/call/insertSelectCall', params);
}

/**
 * @description 连接器 检查连接器是否已经被创建
 * @see http://yapi.shb.ltd/project/3088/interface/api/37076
 */
export function checkConnectorCard(params: ConnectorGetOptionsParams): Promise<MsgModel<ConnectorSaveOptionsResult>> {
  return http.post('/api/application/outside/connector/checkConnector', params);
}

/**
 * @description 连接器控件获取模块列表
 * @see http://yapi.shb.ltd/project/3088/interface/api/36642
 */
export function getFormModuleList(params?: ConnectorGetModuleListParams): Promise<MsgModel<ConnectorGetModuleListResult>> {
  return http.get('/api/application/outside/form/getModuleList', params)
}

/**
 * @description 连接器控件查询To业务表单字段
 * @see http://yapi.shb.ltd/project/3088/interface/api/37578
 */
export function getFormOptions(params?: ConnectorGetFormOptionsParams): Promise<MsgModel<ConnectorGetOptionsResult>> {
  return http.get('/api/application/outside/form/getToOptions', params)
}

/**
 * @description 获取连接器的名称及描述
 * @see http://yapi.shb.ltd/project/3088/interface/api/38550
 */
export function getConnectorName(params?: ConnectorGetFormOptionsParams): Promise<MsgModel<ConnectorGetOptionsResult>> {
  return http.post('/api/application/outside/connector/getConnectorName', params)
}

/**
 * @description 修改连接器的名称及描述
 * @see http://yapi.shb.ltd/project/3088/interface/api/38546
 */
export function updateConnectorName(params?: ConnectorGetFormOptionsParams): Promise<MsgModel<ConnectorGetOptionsResult>> {
  return http.post('/api/application/outside/connector/updateConnectorName', params)
}

/**
 * @description 新增第三方应用数据
 * @see http://yapi.shb.ltd/project/3088/interface/api/38546
 */
export function createThirdAppFormData(params = {}) {
  return http.post('/api/application/outside/call/thirdInsertCall', params)
}

/**
 * @description 获取表单相关的权限
 */
export const getAuthTemplateList = (params = {}) => {
  return http.get(`/api/paas/outside/pc/form/auth/getCurUserFormAuth`, params);
};