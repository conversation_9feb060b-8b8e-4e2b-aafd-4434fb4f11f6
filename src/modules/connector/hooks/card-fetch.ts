/* api */
import { getConnectorCardDataListV2, deleteConnectorData, disassociationConnectorData, allowAddConnectorData, getCardAdditionInfo, createThirdAppFormData } from '@src/modules/connector/api'
/* model */
import MsgModel from '@model/MsgModel'
import { ConnectorModuleErrorMessageEnum } from '@src/modules/connector/model'
import Page from '@model/Page'
/* types */
import { 
  ConnectorDataDisassociationParams, 
  ConnectorDataDeleteParams, 
  ConnectorCardDataListParams, 
  ConnectorAllowAddDataParams, 
  ConnectorCardAdditionInfoParams, 
  ConnectorCardAdditionInfoResult 
} from '@src/modules/connector/types'
/* platform */
import platform from '@src/platform'
/* util */
import { message } from '@src/util/message'
import { isFalsy, isObject } from '@src/util/type'
import Log from '@src/util/log'
/* vue */
import { Ref, ref } from 'vue'
/* hooks */
import { useLoading } from '@hooks/useLoading'
import { t } from '@src/locales'

/** 
 * @description 获取连接器附加组件数据列表
*/
function useConnectorCardFetchConnectorDataList() {
  
  // 加载状态
  const { loading, showLoading, hideLoading } = useLoading()
  
  const dataList: Ref<Record<string, any>> = ref([])
  
  const dataListPageInfo: Ref<Page> = ref(new Page({ hasNextPage: false }))
  
  const fetchConnectorDataList = (params: ConnectorCardDataListParams) => {
    
    showLoading()
    
    return (
      getConnectorCardDataListV2(params).then(result => {
        
        if (MsgModel.isSuccess(result)) {
          
          dataListPageInfo.value = (result?.data || new Page()) as Page
          
          const list = result.data?.list || []
          
          dataList.value = list

          if (params.toBizType == 'PRODUCT') {
            dataList.value = list.map((item: any) => {
              if (isObject(item.type)) {
                item.type = item.type?.pathName ?? item.type?.catalogName
              }
              return item
            })
          }
          
        } else {
          message.error(result?.message || ConnectorModuleErrorMessageEnum.ConnectDataList)
        }
        
        return dataList.value
        
      }).finally(() => {
        hideLoading()
      })
    )
  }
  
  return {
    fetchConnectorDataList,
    loading,
    dataList,
    dataListPageInfo
  }
}

/** 
 * @description 获取连接器附加组件 字段列表和按钮列表
*/
function useConnectorCardFetchAdditionInfo() {
  
  // 加载状态
  const { loading, showLoading, hideLoading } = useLoading()
  
  const additionInfo: Ref<ConnectorCardAdditionInfoResult> = ref({
    header: [],
    buttonList: [],
    toBizTypeEnabled: false
  })
  
  const fetchConnectorCardAdditionInfo = (params: ConnectorCardAdditionInfoParams) => {
    
    showLoading()
    
    return (
      getCardAdditionInfo(params).then(result => {
        
        if (MsgModel.isSuccess(result)) {
          
          additionInfo.value = result?.data || {
            header: [],
            buttonList: [],
            toBizTypeEnabled: false
          }
          
        } else {
          message.error(result?.message || ConnectorModuleErrorMessageEnum.ConnectorCardAdditionInfo)
        }
        
        return additionInfo.value
        
      }).finally(() => {
        
        hideLoading()
        
      })
    )
  }
  
  return {
    fetchConnectorCardAdditionInfo,
    additionInfo,
    loading
  }
}

function useConnectorCardFetchDeleteData() {
  
  // 加载状态
  const { loading, showLoading, hideLoading } = useLoading()
  
  async function fetchDeleteConnectorData(params: ConnectorDataDeleteParams) {
    
    try {
      
      const confirmed = await platform.confirm(t('task.tip.deleteTip'))
      
      if (isFalsy(confirmed)) {
        return Promise.reject()
      }
      
      showLoading()
      
      const result = await deleteConnectorData(params)
      
      if (MsgModel.isSuccess(result)) {
        
        return Promise.resolve()
        
      } else {
        
        message.error(result?.message || ConnectorModuleErrorMessageEnum.DeleteConnectorData)
        
        return Promise.reject()
      }
      
      
    } catch (error) {
      
      Log.error(error, fetchDeleteConnectorData.name)
      
      return Promise.reject(error)
      
    } finally {
      
      hideLoading()
      
    }
    
  }
  
  return {
    fetchDeleteConnectorData,
    loading
  }
}

function useConnectorCardFetchDisassociationData() {
  // 加载状态
  const { loading, showLoading, hideLoading } = useLoading();
  async function fetchDisassociationConnectorData(params: ConnectorDataDisassociationParams) {
    try {
      const confirmed = await platform.confirm(t('view.template.detail.tip6'));
      if (isFalsy(confirmed)) {
        return Promise.reject();
      }
      showLoading();
      const result = await disassociationConnectorData(params);
      if (MsgModel.isSuccess(result)) {
        return Promise.resolve();
      } else {
        message.error(result?.message || t('view.template.detail.tip7'));
        return Promise.reject();
      }
    } catch (error) {
      Log.error(error, fetchDisassociationConnectorData.name);
      return Promise.reject(error);
    } finally {
      hideLoading();
    }
  }
  return {
    fetchDisassociationConnectorData,
    loading
  };
}

/** 
 * @description 获取 连接器附加组件 是否允许继续添加数据
*/
function useConnectorCardFetchConnectorAllowAddData() {
  
  // 加载状态
  const { loading, showLoading, hideLoading } = useLoading()
  
  const isAllowAddData = ref(false)
  
  const fetchAllowAddConnectorData = (params: ConnectorAllowAddDataParams) => {
    
    showLoading()
    
    return (
      allowAddConnectorData(params).then(result => {
        
        if (MsgModel.isSuccess(result)) {
          
          isAllowAddData.value = Boolean(result?.data)
          
        } else {
          message.error(result?.message || ConnectorModuleErrorMessageEnum.ConnectorAllowAddConnectorData)
        }
        
        return isAllowAddData.value
        
      }).finally(() => {
        hideLoading()
      })
    )
  }
  
  return {
    fetchAllowAddConnectorData,
    loading,
    isAllowAddData
  }
}

/** 
 * @description 创建第三方app的data
*/
function useConnectorCardFetchCreateThirdData() {
  
  // 加载状态
  const { loading, showLoading, hideLoading } = useLoading()

  
  const fetchCreateThirdAppData = (params = {}) => {
    
    showLoading()
    
    return (
      createThirdAppFormData(params).then(result => {
        
        if (MsgModel.isSuccess(result)) {
          message.success(result?.message || t('common.base.tip.createSuccess2'))
          
        } else {
          message.error(result?.message || t('common.base.tip.createFails'))
        }
        
      }).finally(() => {
        hideLoading()
      })
    )
  }
  
  return {
    fetchCreateThirdAppData,
    loading,
    // isCreateFetchLoading
  }
}

export {
  useConnectorCardFetchConnectorDataList,
  useConnectorCardFetchDeleteData,
  useConnectorCardFetchConnectorAllowAddData,
  useConnectorCardFetchAdditionInfo,
  useConnectorCardFetchCreateThirdData,
  useConnectorCardFetchDisassociationData,
}
