
.connector-module-connector-card-multi-card-table {
  .biz-table-cell {
    .el-button--text {
      padding: 0;
      min-width: auto;
    }
  }
}

// .connector-module-connector-card-multi-card-table {
//   .el-button {
//     font-size: 14px;
//   }
// }

.connector-card-multi-table-customer-row,
.connector-card-multi-table-task-row,
.connector-card-multi-table-product-row {
  
  display: flex;
  
  .connector-card-multi-table-customer-row-text,
  .connector-card-multi-table-task-row-text,
  .connector-card-multi-table-product-row-text {
    margin-right: 5px;
  }
  
  .connector-card-multi-table-customer-row-item:nth-last-of-type(1), 
  .connector-card-multi-table-task-row-item:nth-last-of-type(1), 
  .connector-card-multi-table-product-row-item:nth-last-of-type(1) {
    
    .connector-card-multi-table-customer-row-text,
    .connector-card-multi-table-task-row-text,
    .connector-card-multi-table-product-row-text {
      display: none;
    }
    
  }
  
}

.connector-card-multi-table-customer-row-item, 
.connector-card-multi-table-task-row-item, 
.connector-card-multi-table-product-row-item {
  display: flex;
}

.connector-module-connector-card-multi-card-table {
  .el-table .el-table__cell {
    font-size: 14px !important;
  }
  .el-table__body .el-table__row td .cell {
    height:  auto;
  }
  .biz-table-cell {
    .el-button {
      padding: 0px !important;
    }
  }
}