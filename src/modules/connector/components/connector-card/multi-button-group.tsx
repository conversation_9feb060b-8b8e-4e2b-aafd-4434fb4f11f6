/* model */
import { ConnectorCardInfo, ConnectorModuleComponentNameEnum } from '@src/modules/connector/model'
/* hooks */
import { useConnectorCard } from '@src/modules/connector/hooks'
/* scss */
import '@src/modules/connector/components/connector-card/multi-button-group.scss'
/* vue */
// @ts-ignore
import { ComponentInstance, ComponentRenderProxy, computed, defineComponent, PropType } from 'vue'
import { CommonComponentInstance } from '@model/VC'
import { CreateElement, VNode } from 'vue'
/* util */
import { isFalse } from '@src/util/type'
import { t } from '@src/locales'

export type ConnectorModuleConnectorCardMultiButtonGroupProps = {
  mainModuleValue: Record<string, any>;
  card: ConnectorCardInfo;
  isShowAddButton: boolean;
  isShowRelationButton: boolean;
}

export interface ConnectorModuleConnectorCardMultiButtonGroupSetupState {
  
}

export enum ConnectorModuleConnectorCardMultiButtonGroupEmitEventNameEnum {
  Input = 'input',
  Add = 'add',
  Relation = 'relation',
  Refresh = 'refresh'
}

export type ConnectorModuleConnectorCardMultiButtonGroupInstance = ComponentInstance & ConnectorModuleConnectorCardMultiButtonGroupSetupState
export type ConnectorModuleConnectorCardMultiButtonGroupVM = ComponentRenderProxy<ConnectorModuleConnectorCardMultiButtonGroupProps> & CommonComponentInstance & ConnectorModuleConnectorCardMultiButtonGroupInstance

export default defineComponent({
  name: ConnectorModuleComponentNameEnum.ConnectorModuleConnectorCardMultiButtonGroup,
  emits: [
    ConnectorModuleConnectorCardMultiButtonGroupEmitEventNameEnum.Input,
    ConnectorModuleConnectorCardMultiButtonGroupEmitEventNameEnum.Add,
    ConnectorModuleConnectorCardMultiButtonGroupEmitEventNameEnum.Relation,
    ConnectorModuleConnectorCardMultiButtonGroupEmitEventNameEnum.Refresh,
  ],
  props: {
    mainModuleValue: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({})
    },
    card: {
      type: Object as PropType<ConnectorCardInfo>,
      default: () => ({})
    },
    isShowAddButton: {
      type: Boolean as PropType<boolean>,
      default: false
    },
    isShowRelationButton: {
      type: Boolean as PropType<boolean>,
      default: false
    }
  },
  setup(props: ConnectorModuleConnectorCardMultiButtonGroupProps, { emit }) {
    
    const { canExport, canCreate, canEdit, canDelete } = useConnectorCard(props)
    
    // 是否可以显示 新增按钮
    const isShowCreateButton = computed(() => {
      return props.isShowAddButton
    })

    // 是否可以显示 关联新增按钮
    const isShowRelationButton = computed(() => {
      return props.isShowRelationButton;
    });
    
    // 是否可以显示 导出按钮
    const isShowExportButton = computed(() => false)
    
    // 是否可以显示 按钮组
    const isShowButtonGroup = computed(() => {
      return (
        isShowCreateButton.value
        || isShowExportButton.value
      )
    })
    const onRelationHandler = () => {
      emit(ConnectorModuleConnectorCardMultiButtonGroupEmitEventNameEnum.Relation);
    };
    const onAddHandler = () => {
      emit(ConnectorModuleConnectorCardMultiButtonGroupEmitEventNameEnum.Add)
    }
    const refreshTable = () => {
      emit(ConnectorModuleConnectorCardMultiButtonGroupEmitEventNameEnum.Refresh);
    }
    
    return {
      isShowButtonGroup,
      isShowRelationButton,
      isShowCreateButton,
      isShowExportButton,

      onRelationHandler,
      onAddHandler,
      refreshTable
    }
  },
  render(h: CreateElement) {
    
    if (isFalse(this.isShowButtonGroup)) {
      return null as unknown as VNode
    }

    const slotData = {refreshTable: this.refreshTable}
    
    return (
      <div class={ConnectorModuleComponentNameEnum.ConnectorModuleConnectorCardMultiButtonGroup}>

        { this.isShowRelationButton && (
          <el-button
            type="primary"
            size="mini"
            plain
            onClick={this.onRelationHandler}
          >
           { this.$t('view.template.detail.btn1') }
          </el-button>
        )}

        {this.isShowCreateButton && (
          <el-button
            type="primary"
            size="mini"
            plain
            onClick={this.onAddHandler}
          >
            {t('common.base.add')}
          </el-button>
        )}
        
        {this.isShowExportButton && (
          <el-button
            type="primary"
            size="mini"
            plain
          >
            {t('common.base.export')}
          </el-button>
        )}
        
      </div>
    )
  }
})
