/* components */
import BizTableTask from '@src/component/business/BizTable/BizTableTask'
import BaseListForNoData from '@src/component/common/BaseListForNoData/index.vue'
import BizIntelligentTagsView from '@src/component/business/BizIntelligentTags/BizIntelligentTagsView'
import BizIntelligentTagsViewToConfig from '@src/component/business/BizIntelligentTags/BizIntelligentTagsViewToConfig'
/* model */
import { 
  ConnectorCardInfo,
  ConnectorCardMultiFixedFieldNameEnum, 
  ConnectorCardMultiFixedFields, 
  ConnectorField, 
  ConnectorFieldTypeEnum, 
  ConnectorModuleComponentNameEnum 
} from '@src/modules/connector/model'
import LoginUser from '@model/entity/LoginUser/LoginUser'
import TaskAddress from '@model/entity/TaskAddress'
/* hooks */
import { useConnectorCard } from '@src/modules/connector/hooks'
/* scss */
import '@src/modules/connector/components/connector-card/multi-card-table.scss'
/* vue */
// @ts-ignore
import { ComponentInstance, ComponentRenderProxy, computed, ComputedRef, onMounted, defineComponent, PropType, ref, Ref } from 'vue'
import { CommonComponentInstance } from '@model/VC'
import { CreateElement, VNode } from 'vue'
/* service */
import { getFieldName, isSystemFiled } from '@service/FieldService'
/* util */
import { isArray, isElement, isNotArray } from '@src/util/type'
import { formatFormFieldItem } from '@src/filter/form'
import { isOpenData } from '@src/util/platform'
import { fmt_datetime } from '@src/filter/fmt'
import { openTabForCustomerView, openTabForProductView, openTabForTaskView } from '@src/util/business/openTab'
/* types */
import { ConnectorServerValueCustomerType, ConnectorServerValueProductType, ConnectorServerValueRelatedCustomersType } from '@src/modules/connector/types'
import { FieldTypeMappingEnum, TaskFieldNameMappingEnum } from '@model/enum/FieldMappingEnum'
import { t } from '@src/locales'
import _ from 'lodash';

const TableColumnDefaultWidth = '160px'

export type ConnectorModuleConnectorCardMultiCardTableProps = {
  fields: ConnectorField[];
  loading: boolean;
  values: Record<string, any>[];
  mainModuleValue: Record<string, any>;
  card: ConnectorCardInfo;
  showCreateButton: boolean;
  showEditButton: boolean;
  showDeleteButton: boolean;
  showExportButton: boolean;
  isThirdApp: boolean;
  showViewButton: boolean;
  toBizType: string;
  showRelationButton: boolean;
}

export interface ConnectorModuleConnectorCardMultiCardTableSetupState {
  
}

export enum ConnectorModuleConnectorCardMultiCardTableEmitEventNameEnum {
  Input = 'input',
  View = 'view',
  Edit = 'edit',
  Delete = 'delete',
  EditStore = 'editStore',
  Disassociation = 'disassociation',
}

export type ConnectorModuleConnectorCardMultiCardTableInstance = ComponentInstance & ConnectorModuleConnectorCardMultiCardTableSetupState
export type ConnectorModuleConnectorCardMultiCardTableVM = ComponentRenderProxy<ConnectorModuleConnectorCardMultiCardTableProps> & CommonComponentInstance & ConnectorModuleConnectorCardMultiCardTableInstance

export default defineComponent({
  name: ConnectorModuleComponentNameEnum.ConnectorModuleConnectorCardMultiCardTable,
  components: {
    BizTableTask
  },
  emits: [
    ConnectorModuleConnectorCardMultiCardTableEmitEventNameEnum.Input,
    ConnectorModuleConnectorCardMultiCardTableEmitEventNameEnum.View,
    ConnectorModuleConnectorCardMultiCardTableEmitEventNameEnum.Edit,
    ConnectorModuleConnectorCardMultiCardTableEmitEventNameEnum.Delete,
    ConnectorModuleConnectorCardMultiCardTableEmitEventNameEnum.EditStore,
    ConnectorModuleConnectorCardMultiCardTableEmitEventNameEnum.Disassociation,
  ],
  props: {
    card: {
      type: Object as PropType<ConnectorCardInfo>,
      default: () => ({})
    },
    fields: {
      type: Array as PropType<ConnectorField[]>,
      default: () => ([])
    },
    loading: {
      type: Boolean as PropType<boolean>,
      default: true
    },
    mainModuleValue: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({})
    },
    values: {
      type: Array as PropType<Record<string, any>[]>,
      default: () => ([])
    },
    showCreateButton: {
      type: Boolean as PropType<boolean>,
      default: false
    },
    showEditButton: {
      type: Boolean as PropType<boolean>,
      default: false
    },
    showDeleteButton: {
      type: Boolean as PropType<boolean>,
      default: false
    },
    showExportButton: {
      type: Boolean as PropType<boolean>,
      default: false
    },
    showRelationButton: {
      type: Boolean as PropType<boolean>,
      default: false
    },
    isThirdApp: {
      type: Boolean as PropType<boolean>,
      default: false
    },
    showViewButton: {
      type: Boolean as PropType<boolean>,
      default: true
    },
    toBizType: {
      type: String as PropType<string>,
      default: ''
    },
    selectable: {
      type: Boolean as PropType<boolean>,
      default: false
    },
  },
  setup(props: ConnectorModuleConnectorCardMultiCardTableProps, { emit }) {
    
    const { canExport, canCreate, canEdit, canDelete } = useConnectorCard(props)

    // 判断是否有一个操作列表的按钮的显示
    const hasOneButtonShow = computed(()=> props.showViewButton || props.showDeleteButton || props.showEditButton )
    
    const formFields: ComputedRef<ConnectorField[]> = computed(() => {
      
      // 所有字段列表
      const fullFields = (props.fields || []).concat(hasOneButtonShow.value ? ConnectorCardMultiFixedFields : [])
      
      return fullFields.map(field => {
        
        // TODO: 是否显示 根据 后端配置决定
        field.show = true

        if(ConnectorCardMultiFixedFieldNameEnum.Operation === field.fieldName && props.isThirdApp) field.show = false
        
        field.label = field.label || field.name || field.displayName
        
        return field
        
      })
    })

    // 是否可以显示 删除按钮
    const isShowDeleteButton = computed(() => {
      return props.showDeleteButton
    })
    // 是否可以显示 取消关联按钮
    const isShowDisassociationButton = computed(() => {
      return props.showRelationButton;
    });
    // 是否可以显示 编辑按钮
    const isShowEditButton = computed(() => {
      return props.showEditButton
    })
    
    const onConnectorCardItemEditHandler = (row: Record<string, any>) => {
      emit(ConnectorModuleConnectorCardMultiCardTableEmitEventNameEnum.Edit, row)
    }
    
    const onConnectorCardItemDeleteHandler = (row: Record<string, any>) => {
      emit(ConnectorModuleConnectorCardMultiCardTableEmitEventNameEnum.Delete, row)
    }

    const onConnectorCardItemDisassociationHandler = (row: Record<string, any>) => {
      emit(ConnectorModuleConnectorCardMultiCardTableEmitEventNameEnum.Disassociation, row);
    };

    const onConnectorCardItemEditStoreHandler = (row: Record<string, any>) => {
      emit(ConnectorModuleConnectorCardMultiCardTableEmitEventNameEnum.EditStore, row)
    }
    
    const onConnectorCardItemViewHandler = (row: Record<string, any>) => {
      // paas 暂存，往编辑页面跳转
      if (row?.isStore == 1) {
        onConnectorCardItemEditStoreHandler(row)
        return
      }
      
      emit(ConnectorModuleConnectorCardMultiCardTableEmitEventNameEnum.View, row)
    }
    const multipleSelection:Ref<any[]> = ref([])

    const handleSelection = (selection: any[]) => {
      let tv = [];
      const list:any[] = props.values || []

      tv = multipleSelection.value.filter(ms => list.every(c => c.bizId !== ms.bizId));
      tv = _.uniqWith([...tv, ...selection], _.isEqual);

      multipleSelection.value = tv;
    }
    
    return {
      formFields,
      isShowDeleteButton,
      isShowEditButton,
      isShowDisassociationButton,
      multipleSelection,
      handleSelection,
      onConnectorCardItemViewHandler,
      onConnectorCardItemEditHandler,
      onConnectorCardItemDeleteHandler,
      onConnectorCardItemDisassociationHandler
    }
  },
  computed: {
    isAddons(): boolean {
      return this.toBizType.includes('ADDITIONAL');
    }
  },
  methods: {
    /* 把选中的数据匹配出来 */
    matchSelected () {
      this.$refs.cardTable?.clearSelection?.();
      // 过滤出当前页已选中的数据
      const selected = (this.values || []).filter(c => this.multipleSelection.some(sc => sc.bizId === c.bizId)) || [];
      selected.forEach(row => {
        this.$refs.cardTable?.toggleRowSelection?.(row, true);
      });
    },
    /** 
     * @description 渲染表格插入 用于无限加载显示
    */
    renderTableAppendSlot() {
      return (
        <div class={ this.loading ? 'block-hide' : 'block-show'}>
          <BaseListForNoData  notice-msg={t('common.base.tip.noData')} table-type={'smallTable'}></BaseListForNoData>
        </div>
      )
    },
    /** 
     * @description 渲染表格列
    */
    renderTableColumnField(h: CreateElement, scope: any, column: ConnectorField) {
      
      // 渲染业务列
      const renderColumnWithBusinessValue = this.renderColumnWithBusiness(column, scope.row)
      
      // 已经是元素 直接返回
      if (isElement(renderColumnWithBusinessValue)) {
        return renderColumnWithBusinessValue
      }
      
      return (
        <div class='biz-table-cell'>
          { renderColumnWithBusinessValue }
        </div>
      )
      
    },
    /**
     * @description: 渲染业务字段
     * @param {ConnectorField} column 列
     * @param {any} row 行数据
     * @return {VNode} 元素
     */ 
    renderColumnWithBusiness(column: ConnectorField, row: any): VNode | JSX.Element | string | null {
      
      // @ts-ignore
      const formType = column.formType || column.fieldType
      
      // 用户名
      if (column.fieldName == ConnectorCardMultiFixedFieldNameEnum.UserName) {
        
        const value = row[column.fieldName]
        const staffId = row.staffId
        
        return this.renderUserName(value, staffId)
      }
      
      // 标签
      if (formType == ConnectorFieldTypeEnum.aiLabel) {
        return this.renderColumnWithAiLabel(column, row);
      }

      // 仓库
      if (column.fieldName === ConnectorFieldTypeEnum.warehousePositionId) {
        return this.renderWarehousePosition(column, row);
      }

      // 仓库
      if (column.fieldName === ConnectorFieldTypeEnum.warehouseId) {
        return this.renderWarehouse(column, row);
      }

      // 编号
      if (
        formType == ConnectorFieldTypeEnum.SerialNumber 
        || column.fieldName == ConnectorFieldTypeEnum.SerialNumber
        || (formType == TaskFieldNameMappingEnum.TaskNo && !this.isAddons)
        || formType == TaskFieldNameMappingEnum.EventNo
      ) {
        return this.renderColumnWithSerialNumber(column, row)
      }
      
      // 客户
      if (formType == ConnectorFieldTypeEnum.Customer) {
        return this.renderColumnWithCustomer(column, row)
      }
      
      // 关联客户
      if (formType == ConnectorFieldTypeEnum.RelatedCustomers) {
        return this.renderColumnWithRelatedCustomers(column, row)
      }
      
      // 关联工单
      if (formType == ConnectorFieldTypeEnum.RelatedTask || formType == ConnectorFieldTypeEnum.RelationTask) {
        return this.renderColumnWithRelatedTasks(column, row)
      }
      
      // 产品
      if (formType == ConnectorFieldTypeEnum.Product) {
        return this.renderColumnWithProduct(column, row)
      }
      
      // 客户地址
      if (formType == ConnectorFieldTypeEnum.CustomerAddress) {
        return this.renderColumnWithCustomerAddress(column, row)
      }
      
      // 客户联系人
      if (formType == ConnectorFieldTypeEnum.Linkman) {
        return this.renderColumnWithCustomerLinkman(column, row)
      }
      
      // 服务部门
      if (column.fieldName == ConnectorFieldTypeEnum.Tags) {
        return this.renderTag(column, row)
      }
      // 关联的服务部门显示逻辑
      if (column && column.setting && column.setting.fieldName && column.setting.fieldName == ConnectorFieldTypeEnum.Tags) {
        return this.renderTag(column, row);
      }
      
      // 地址
      if (formType == ConnectorFieldTypeEnum.Address) {
        return this.renderColumnWithAddress(column, row)
      }
      
      // 操作
      if (column.fieldName == ConnectorCardMultiFixedFieldNameEnum.Operation) {
        return this.renderOperation(row)
      }
      
      return this.renderColumnWithCommon(column, row)
    },
    /**
     * @description: 渲染通用字段
     * @param {ConnectorField} column 列
     * @param {any} row 行数据
     * @return {VNode} 元素
     */
    renderColumnWithCommon(column: ConnectorField, row: any): VNode {
      
      // 是否是系统字段
      const isSystem = isSystemFiled(column)
      // 字段类型
      const formType = column.formType || ''
      // 字段名称
      const fieldName = getFieldName(column)
      // 值
      const value = row[fieldName]
      
      return (
        <div>
          { formatFormFieldItem(column, value) }
        </div>
      )
      
    },
    /** 
     * @description 渲染 用户名
    */
    renderUserName(value: string, staffId: string) {
      
      const isShowOpenData = isOpenData && staffId
      
      return (
        <div>
          {isShowOpenData ? (
            <open-data type="userName" openid={staffId}></open-data>
          ) : (
            value
          )}
        </div>
      )
    },
    /**
     * @des 渲染 标签
     */
    renderColumnWithAiLabel(column: ConnectorField, row: Record<string, any>) {
      const value = row?.aiLabel || [];
      const labelNameList = value.map((item: any) => item?.name).join('，') || "";
      return (
        <div class='biz-table-cell no-base-tip'>
          { labelNameList }
        </div>
      );
    },
    // 仓位
    renderWarehousePosition(column: ConnectorField, row: Record<string, any>) {
      const value = row?.warehousePositionId || {};
      return (
        <div class='biz-table-cell no-base-tip'>
          { value?.name || '' }
        </div>
      )
    },
    // 仓库
    renderWarehouse(column: ConnectorField, row: Record<string, any>) {
      const value = row?.warehouseId || [];
      return (
        <div class='biz-table-cell no-base-tip'>
          { value?.name || '' }
        </div>
      )
    },
    /* 人员类型 */
    renderColumnWithUser(column: ConnectorField, row: Record<string, any>): any {
      
      const user: LoginUser | any = row?.[column.fieldName || ''] || {}
      
      // 单选人员
      if(isOpenData && user.staffId) {
        return (<span><open-data type="userName" openid={user.staffId}></open-data></span>)
      }
      
      // 多选人员
      if (isOpenData && isArray(user)) {
        return (
          user.map((item: LoginUser) => {
            return <open-data type="userName" openid={item.staffId}></open-data>
          })
        )
      }
      
      return user?.displayName || user?.name || ''
    },
    /* 位置类型 */
    renderColumnWithLocation(column: ConnectorField, row: Record<string, any>): string {
      const location: Record<string, any> = row?.[column.fieldName || ''] || {}
      return location?.address || ''
    },
    /* 日期类型 */
    renderColumnWithDateTime(column: ConnectorField, row: Record<string, any>): string {
      
      const fieldName: string = column.fieldName || ''
      
      return fmt_datetime(row?.[fieldName])
    },
    /* 编号 */
    renderColumnWithSerialNumber(column: ConnectorField, row: Record<string, any>) {
      
      const serialNumber: number = row?.[column.fieldName || ''] || ''
      
      return (
        <div style="display: flex;align-items: center;">
           <div class="link-text" onClick={() => this.onConnectorCardItemViewHandler(row)}>
            { serialNumber }
          </div>
          {
            row.labelList && row.labelList?.length > 0 ? (
              <BizIntelligentTagsViewToConfig
                type="table"
                tags-list={row.labelList || []}>
              </BizIntelligentTagsViewToConfig>
            ) : null
          }
        </div>
      )
      
    },
    /* 客户类型 */
    renderColumnWithCustomer(column: ConnectorField, row: Record<string, any>) {
      
      const customers = row?.[column.fieldName || ''] || []
      const customer = (
        isArray(customers) ? customers[0] || {} : customers
      ) || {}
      
      const classNames = 'view-detail-btn'
      
      const customerId = customer?.id || ''
      const customerName = customer?.name || ''
      
      return (
          <div style="display: flex;align-items: center;">
            <div
              class={classNames}
              onClick={() => openTabForCustomerView(customerId) }
            >
              { customerName }
            </div>
            {
              customers?.labelList && customers?.labelList?.length > 0 ? (
                <BizIntelligentTagsViewToConfig
                  type="table"
                  tags-list={customers.labelList || []}>
                </BizIntelligentTagsViewToConfig>
              ) : null
            }
          </div>
        
      )
    },
    /* 产品类型 */
    renderColumnWithProduct(column: ConnectorField, row: Record<string, any>) {
      
      let products: ConnectorServerValueProductType[] = row?.[column.fieldName || ''] || []
      
      if (isNotArray(products)) {
        products = [products as unknown as ConnectorServerValueProductType]
      }
      
      const classNames = 'view-detail-btn'
      
      const content = products.map((item: ConnectorServerValueProductType) => {
        return (
          <div class="connector-card-multi-table-product-row-item">
            {/* <div
              class={classNames}
              onClick={() => openTabForProductView(item.id) }
            >
              { item.name }
            </div> */}
              <BizIntelligentTagsView
                type="table"
                value={item.name}
                tags-list={item.labelList || []}
                config={{calcFontSize: '12px', tableShowType: 'icon', tableMaxLength: 1}}
                showMoreIcon={false}
                onViewClick={() => openTabForProductView(item.id)}>
              </BizIntelligentTagsView>
            <span class="connector-card-multi-table-product-row-text">
              ,
            </span>
          </div>
        )
      })
      
      return (
        <div class="connector-card-multi-table-product-row">
          { content }
        </div>
      )
    },
    /* 关联客户类型 */
    renderColumnWithRelatedCustomers(column: ConnectorField, row: Record<string, any>) {
      
      let customers: ConnectorServerValueRelatedCustomersType = row?.[column.fieldName || ''] || []
      
      if (isNotArray(customers)) {
        customers = [customers as unknown as ConnectorServerValueCustomerType]
      }
      
      const classNames = 'view-detail-btn'
      
      const content = customers.map((item: ConnectorServerValueCustomerType) => {
        return (
          <div class="connector-card-multi-table-customer-row-item">
            <div
              class={classNames}
              onClick={() => openTabForCustomerView(item.id) }
            >
              { item.name }
            </div>
            <span class="connector-card-multi-table-customer-row-text">
              ,
            </span>
          </div>
        )
      })
      
      return (
        <div class="connector-card-multi-table-customer-row">
          { content }
        </div>
      )
    },
    /* 关联工单类型 */
    renderColumnWithRelatedTasks(column: ConnectorField, row: Record<string, any>) {
      
      let tasks: Record<string, any>[] = row?.[column.fieldName || ''] || []
      
      if (isNotArray(tasks)) {
        tasks = [tasks as unknown as Record<string, any>]
      }
      
      const classNames = 'view-detail-btn'
      
      const content = tasks.map((item: Record<string, any>) => {
        return (
          <div class="connector-card-multi-table-task-row-item">
            {/* <div
              class={classNames}
              onClick={() => openTabForTaskView(item.taskId) }
            >
              { item.taskNo }
            </div> */}
            <BizIntelligentTagsView
                type="table"
                value={item.taskNo}
                tags-list={item.labelList || []}
                config={{calcFontSize: '12px', tableShowType: 'icon', tableMaxLength: 1}}
                showMoreIcon={false}
                onViewClick={() => openTabForTaskView(item.taskId)}>
            </BizIntelligentTagsView>
            <span class="connector-card-multi-table-task-row-text">
              ,
            </span>
          </div>
        )
      })
      
      return (
        <div class="connector-card-multi-table-task-row">
          { content }
        </div>
      )
    },
    /* 客户地址 */
    renderColumnWithCustomerAddress(column: ConnectorField, row: Record<string, any>) {
      
      const addressList = row?.[column.fieldName || ''] || []
      const address = (
        isArray(addressList) ? addressList[0] || {} : addressList
      ) || {}
      
      return new TaskAddress(address).toString() || address?.name || ''
    },
    /* 客户联系人 */
    renderColumnWithCustomerLinkman(column: ConnectorField, row: Record<string, any>) {
      
      const linkmanList = row?.[column.fieldName || ''] || []
      const linkman = (
        isArray(linkmanList) ? linkmanList[0] : linkmanList
      ) || {}
      
      const linkmanName = linkman?.name || ''
      
      return (
        <div>
          { linkmanName }
        </div>
      )
    },
    /* 服务部门 */
    renderTag(column: ConnectorField, row: Record<string, any>) {
      
      const tags = row?.[column.fieldName || ''] || []
      const tag = (
        isArray(tags) ? tags[0] : tags
      ) || {}
      
      const tagName = tag?.tagName || tag?.name
      
      return (
        <div>
          { tagName }
        </div>
      )
    },
    /* 地址类型 */
    renderColumnWithAddress(column: ConnectorField, row: Record<string, any>): string {
      const address: any = row?.[column.fieldName || ''] || {}
      return new TaskAddress(address).toString() || address?.name || ''
    },
    /** 
     * @description 渲染 操作
    */
    renderOperation(row: Record<string, any>) {
      return (
        <div>
          { this.renderOperationViewButton(row) }
          {/* { this.isShowEditButton && this.renderOperationEditButton(row) } */}
          { this.isShowDeleteButton && !(this.isShowDisassociationButton && row.relationData) && this.renderOperationDeleteButton(row) }
          { this.isShowDisassociationButton && row.relationData && this.renderOperationDisassociationButton(row) }
        </div>
      )
    },
    /** 
     * @description 渲染 操作 详情按钮
    */
    renderOperationViewButton(row: Record<string, any>) {
      return (
        <el-button
          type="text"
          onClick={() => this.onConnectorCardItemViewHandler(row)}
        >
          {t('common.base.detail')}
        </el-button>
      )
    },
    /** 
     * @description 渲染 操作 编辑按钮
    */
    renderOperationEditButton(row: Record<string, any>) {
      return (
        <el-button
          type="text"
          onClick={() => this.onConnectorCardItemEditHandler(row)}
        >
          {t('common.base.edit')}
        </el-button>
      )
    },
    /** 
     * @description 渲染 操作 删除按钮
    */
    renderOperationDeleteButton(row: Record<string, any>) {
      return (
        <el-button
          type="text"
          onClick={() => this.onConnectorCardItemDeleteHandler(row)}
        >
          {t('common.base.delete')}
        </el-button>
      )
    },
    /** 
     * @description 渲染 操作 取消关联按钮
    */
    renderOperationDisassociationButton(row: Record<string, any>) {
      return (
        <el-button
          type="text"
          onClick={() => this.onConnectorCardItemDisassociationHandler(row)}
        >
          { this.$t('common.base.disassociate') }
        </el-button>
      );
    }
  },
  render(h: CreateElement) {
    
    const scopedSlots = this.loading ? {} : {
      empty: () => this.renderTableAppendSlot()
    }
    
    return (
      <div class={ConnectorModuleComponentNameEnum.ConnectorModuleConnectorCardMultiCardTable}>
        <el-table
          border
          ref="cardTable"
          class="bbx-normal-list-box"
          data={this.values || []}
          headerRowClassName='common-list-table-header__v2'
          rowClassName='base-table-row-v3'
          row-key = {(row: { id: any }) => row.id}
          stripe
          scopedSlots={scopedSlots}
          onSelect={this.handleSelection}
          on-select-all={this.handleSelection}
        >
          
          { !this.selectable &&<el-table-column type="index" width="50" label={t('common.base.SN')} /> }
          {
            this.selectable &&
            <el-table-column type="selection" align="center" width="40"/>
          }
          {
            (this.formFields as ConnectorField[]).filter((column: ConnectorField) => Boolean(column.show) && !(column.fieldName === ConnectorCardMultiFixedFieldNameEnum.Operation && this.selectable)).map((column: ConnectorField) => {
              return (
                <el-table-column
                  // @ts-ignore
                  fixed={column.fixed}
                  label={column.label}
                  key={column.field || column.fieldName}
                  width={column.width}
                  minWidth={column.minWidth ? `${column.minWidth}px` : TableColumnDefaultWidth}
                  prop={column.field}
                  resizable
                  show-overflow-tooltip
                >
                  { (scope: any) => this.renderTableColumnField(h, scope, column) }
                </el-table-column>
              )
            })
            
          }
          
          <div slot='append'></div>
          <div slot="empty"></div>
          
        </el-table>
      </div>
    )
  }
})
