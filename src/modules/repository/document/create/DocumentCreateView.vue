<template>
<div class="document-create-container">
  <div class="fe-page-header">
    <div class="fe-page-header__left">
      {{ pageTitle }}
    </div>
    <div class="fe-page-header__right">
      <el-button type="plain-third" @click="goBack">{{$t('common.base.cancel')}}</el-button>
      <el-button type="primary" class="base-button green-butn" @click="saveAndSumbit();trackEventHandler('save')" :disabled="!saveCanClick || pending">{{$t('wiki.create.btn1')}}</el-button>
      <el-button type="primary" class="base-button green-butn" @click="toDraftBox();trackEventHandler('draft')" :disabled="!draftCanClick || pending">{{$t('wiki.create.btn2')}}</el-button>
      <el-button class="base-button white-butn" @click="deleteFile();trackEvent<PERSON>andler('delete')" v-if="isEdit" :disabled="!deleteCanClick || pending">{{$t('common.base.delete')}}</el-button>
    </div>
  </div>
  <div class="document-create-view" v-loading.fullscreen.lock="loading" >
    <div class="view-left" :style="{paddingRight: padding}">
      <!-- 标签 -->
      <FormIntelligentLabel @update="labelHandler" :value="params.labelObj" mode="wiki" showMode='all' />
      <!-- 顶部文章属性 -->
      <text-title :isFault="isFault"  ref="textTitle" v-model="params" class="textTitle" :key="TextRerender"></text-title>
      <!-- 富文本编辑器 -->
      <!-- 故障库新建 -->
      <template  v-if="isFault">
        <div class="label">{{$t('wiki.create.label1')}}：</div>
         <base-editor
            v-model="params.article"
            @input="getInput"
            ref="editor"
            :maxLength="5000"
            :isEdit="isEdit || isSaveData || !!copyId || !!task.taskId">
          </base-editor>
      </template>
      <base-editor
        v-else
        v-model="params.article"
        @input="getInput"
        ref="editor"
        :isEdit="isEdit || isSaveData || !!copyId || !!task.taskId">
      </base-editor>
      <p class="article-error" v-if="articleEmpty">{{$t('wiki.create.tips1')}}</p>
    </div>
    <!-- 更新日志，编辑时显示 -->
    <update-log class="view-right" v-if="isEdit" :wikiId="wikiId"></update-log>

    <request-approve @createApprove="createApprove" @reset="reset" ref="requestApproveDialog" />
  </div>
</div>
</template>

<script>
/* component */
import { Message } from 'element-ui'
import TextTitle from './component/TextTitle.vue'
import UpdateLog from '../components/UpdateLog.vue'
import RequestApprove from './component/RequestApprove.vue'
/* api */
import * as RepositoryApi from '@src/api/Repository'
/* util */
import _ from 'lodash';
/* enum */
import TenantDataLimitSourceEnum from '@model/enum/TenantDataLimitSourceEnum'
import TenantDataLimitTypeEnum from '@model/enum/TenantDataLimitTypeEnum'
import StorageKeyEnum from '@model/enum/StorageKeyEnum'
/* mixin */
import VersionMixin from '@src/mixins/versionMixin/index.ts'
import ThemeMixin from '@src/mixins/themeMixin/index.ts'
import { openAccurateTab } from '@src/util/platform'
import { PageRoutesTypeEnum } from 'pub-bbx-global/pageType/dist/enum/PageRoutesEnum'
import i18n from '@src/locales'

/* api */
import { updateWiki, createWiki } from '@src/api/LLMApi'
/* hooks */
import { useFetchSettingGPTQuestion } from '@gpt/hooks'

import { getRootWindowInitData } from '@src/util/window'
import { getRootWindow, isFalsy } from 'pub-bbx-utils'
import { storageGet, storageSet } from '@src/util/storage';

const rootWindowInitData = getRootWindowInitData()

export default {
  name: 'document-create-view',
  mixins: [VersionMixin, ThemeMixin],
  components: {
    [TextTitle.name]: TextTitle,
    [UpdateLog.name]: UpdateLog,
    [RequestApprove.name]: RequestApprove
  },
  props: {
    initData: {
      type: Object,
      default: () => ({})
    }
  },
  setup() {
    
    const { fetchQuestion, question } = useFetchSettingGPTQuestion()
    
    return {
      fetchQuestion,
      question
    }
    
  },
  data () {
    return {
      params: {
        title: '',
        article: '', // 文章内容
        permission: 0, // 文章权限
        permitShare: this.initData.wikiConfig.permitShare,
        labelObj: {}, // 设置标签参数
        label: [], // 标签
        form: {
          content: '',
          attachments: [],
          showInOwn: 0
        }, // 附件
        typeId: '', // 文章分类
        options: [],
        isOpen: 1,
        isOpenComment: 1,
        role: [],
        productTypeList:[],// 适用产品类型
        faultPerformance:'',// 故障现象
        faultReason:'',// 故障原因
        repairCostBegin:'',// 预计维修费用起始
        repairCostEnd:'',// 预计维修费用截止
      },
      interval: '',
      articleHtml: null,
      isSave: false,
      isEdit: false,
      isToDraft: false,
      wikiId: null,
      allowShare: false,
      info: {},
      reportApproveStatus: null,
      loading: false,
      articleEmpty: false,
      deleteCanClick: true,
      saveCanClick: true,
      draftCanClick: true,
      pending: false,
      isSaveData: false,
      isUpdate: false,

      isCopy: false,
      copyId: '',

      task: {},
      wikiTypeId: '', // 分类ID
      // 故障库信息 Start
      wikiType:null,
      TextRerender: Date.now(),
      questionId: null,
      // 故障库信息 End
    }
  },
  async created () {
    try {
      this.getId();
      let detail = JSON.parse(localStorage.getItem(`document_article_${ this.initData.userInfo.userId }`));
      if (detail && !this.isEdit) {
        this.isSaveData = true;
        let res = await this.$platform.confirm(this.$t('wiki.create.tips2'));
        if(res) {
          this.getArticle();
        } else {
          this.params.article = ' ';
          localStorage.removeItem(`document_article_${ this.initData.userInfo.userId }`);
        }
      }
      if(!this.isEdit) {
        this.saveArticle()
        // 检查版本数量限制
        this.checkNumExceedLimitBeforeHandler
        && this.checkNumExceedLimitBeforeHandler(
          TenantDataLimitSourceEnum.Wiki,
          TenantDataLimitTypeEnum.Wiki
        )
      }

      await this.getTypes();
      if(this.isEdit || this.isCopy) this.getArticle();

      this.task.taskId && this.taskConvertWiki()
      
      this.taskAISummaryDataProcessHandler()
      
      this.aiQuestionHandler()
      
    } catch (error) {
      console.error('error', error)
    }
  },

  beforeDestroy() {
    // 清除定时器
    clearInterval(this.interval);
  },

  updated: _.debounce(function () {
    this.$nextTick(() => {
      this.isUpdate = true;
    }) 
  }, 1000),

  methods: {
    /**
     * @description 标签处理函数
     */
    labelHandler({ newValue, oldValue }) {
      this.params.labelObj = newValue;
    },
    goBack() {
      let id = window.frameElement.dataset.id
      return this.$platform.closeTab(id)
    },
    taskConvertWiki() {
      RepositoryApi.taskConvertWiki({
        taskId: this.task.taskId,
        taskTypeId: this.task.taskTypeId
      }).then(res => {
        if(res.code === 0) {
          let article = ''
          let arr = [...res.result.cardList, ...res.result.fieldList, ...res.result.taskReceiptList]
          arr.forEach((v, i) => {
            article += `<p>${v.displayName}：${v.content}</p>`
          })

          // 没值时手动触发编辑器初始化
          if (!article) {
            this.$refs.editor.initEditor()
            this.$refs.editor.isInit = true
          }
          this.$set(this.params, 'article', article)
        }
      }).catch(err => {
        console.log(err)
      })
    },
    reset () {
      this.deleteCanClick = true;
      this.saveCanClick = true;
      this.draftCanClick = true;
      this.pending = false;
    },
    setStatus(n) {
      this.reportApproveStatus = n;
    },

    getId () {
      let array = window.location.href.split('?');
      if(array.length <= 1) return;
      let query = array[1].split('&');
      let params = [];
      query.forEach(item => {
        params.push({name: item.split('=')[0],
          value: item.split('=')[1]})
      })

      params.forEach(item => {
        if(item.name == 'wikiId') {
          this.wikiId = item.value;
          this.isEdit = true;
        }
        if(item.name === 'copyId') {
          this.copyId = item.value
          this.isCopy = true;
        }
        if(item.name === 'taskId') {
          this.task.taskId = item.value
        }
        if(item.name === 'taskNo') {
          this.task.taskNo = item.value
        }
        if(item.name === 'taskTypeId') {
          this.task.taskTypeId = item.value
        }
        if(item.name === 'wikiTypeId') {
          this.wikiTypeId = item.value
        }
        if(item.name === 'questionId') {
          this.questionId = item.value
        }
        // 故障库信息 Start
        if(item.name === 'wikiType') {
          this.wikiType = item.value
        }
        // 故障库信息 End
      })
    },
    // 获取分类二级树状结构，每次更新一次
    async getTypes () {
      try {
        this.loading = true;
        let params = {}
        if(this.wikiType){
          // 知识空间兼容老接口，可不用传参 type
          params.type = this.wikiType
        }
        let res = await RepositoryApi.getDocumentTypes(params);
        
         this.loading = false;

        if(res.success) {
          // res.result.forEach(item => {
          //   item.value = item.id.toString();
          //   item.label = item.name;
          //   if(item.subTypes.length > 0) {
          //     item.children = item.subTypes;
          //     item.children.forEach(childItem => {
          //       childItem.value = childItem.id.toString();
          //       childItem.label = childItem.name;
          //     })
          //   }
          // })
          this.params.options = res.result;

          if(this.params.options.length <= 0) {
            this.$platform.notification({
              title: this.$t('wiki.create.tips3'),
              type: 'error',
            });
            this.saveCanClick = false;
            this.draftCanClick = false;
            this.deleteCanClick = false;
          }
          // 走复制流程，不获取第一个id
          if(!this.isEdit && this.params.options.length > 0 && !this.copyId) {
            this.params.typeId = this.params.options[0].id
          //   if(this.params.typeId.length <= 0) {
          //     this.setType(this.params.options[0].id)
          //   }
          //   if(this.typeId) {
          //     this.setType(this.typeId);
          //   }
          }
          // 赋值选中的分类
          if(!this.isEdit && this.wikiTypeId) {
            this.params.typeId = Number(this.wikiTypeId)
          }
        } else {
          this.$platform.notification({
            title: res.message,
            type: 'error',
          });
        }
      } catch (err) {
        console.error(err);
        this.loading = false;
      }
    },

    // 保存并提交，新建、编辑
    async saveAndSumbit () {
      if(!await this.paramsCheck()) return;
      this.isToDraft = false;
      this.saveCanClick = false;

      if(this.initData.wikiConfig.needApprove && this.initData.wikiConfig.approvers && this.initData.wikiConfig.approvers.length > 0) {
        this.$refs.requestApproveDialog.open();
        return;
      }

      try {
        let params = this.buildParams();
        this.pending = true;   
        if(this.task.taskId) {
          params.convertId = this.task.taskId
          params.typeNo = this.task.taskNo
          params.convertType = 'task'
        } 

        const SubmitPromise = RepositoryApi.saveAndSumbit(params)
        let res = await this.checkNumExceedLimitAfterHandler(SubmitPromise)
        this.pending = false;
        
        if (res.success) {
          
          try {
            const rootWindow = getRootWindow(window)
            const fromId = window.frameElement.getAttribute('fromid');
            rootWindow.fetchQuestionDelete(this.questionId, fromId)
          } catch (error) {
            console.error(error)
          }
          
          localStorage.removeItem(`document_article_${ this.initData.userInfo.userId }`);
          
          this.$platform.notification({
            title: res.message,
            type: 'success',
          });
          
          this.saveCanClick = true;
          
          this.refreshFromTab();
          
        } else {
          this.saveCanClick = true;
          this.$platform.notification({
            title: res.message,
            type: 'error',
          });
        }
      } catch (err) {
        console.error(err)
      }
    },
    // 需要审批时，提交
    async createApprove (remark) {
      if(!this.paramsCheck()) return;
      this.isToDraft = false;
      this.$refs.requestApproveDialog.close();
      this.saveCanClick = false;

      try {
        let otherInfo = this.buildParams();
        let params = {
          objId: this.info.id || null,
          applyRemark: remark,
          source: 'wiki',
          otherInfo
        }
        if(this.task.taskId) {
          params.convertId = this.task.taskId
          params.typeNo = this.task.taskNo
          params.convertType = 'task'
        } 
        this.pending = true;          
        const ApprovePromise = RepositoryApi.createApprove(params)
        let res = await this.checkNumExceedLimitAfterHandler(ApprovePromise)
        this.pending = false;  

        if(res.success) {
          localStorage.removeItem(`document_article_${ this.initData.userInfo.userId }`);
          this.$platform.notification({
            title: res.message,
            type: 'success',
          });
          this.saveCanClick = true;
          storageSet('isCreateApprove', true);
          this.openFrame();
        } else {
          this.saveCanClick = true;
          this.$platform.notification({
            title: res.message,
            type: 'error',
          });
        }
      } catch (err) {
        console.error(err)
      }
    },

    // 存到草稿箱操作
    async toDraftBox () {
      if(!this.paramsCheck()) return;
      this.isToDraft = true;
      this.draftCanClick = false;

      try {
        let params = this.buildParams();
        this.pending = true;        
        let res = await RepositoryApi.saveDraft(params);
        this.pending = false;

        if(res.success) {
          this.$platform.notification({
            title: this.$t('wiki.create.tips4'),
            type: 'success',
          });
          localStorage.removeItem(`document_article_${ this.initData.userInfo.userId }`);
          this.draftCanClick = true;
          this.openFrame();
        } else {
          this.draftCanClick = true;
          this.$platform.notification({
            title: res.message,
            type: 'error',
          });
        }
      } catch (err) {
        console.error(err)
      }
    },

    // 文档库或草稿删除操作，所调接口不同
    async deleteFile () {
      try {
        if (!await this.$platform.confirm(this.$t('wiki.create.tips5'))) return;
        this.deleteCanClick = false;

        let params = {
          wikiIds: [this.wikiId]
        };

        this.pending = true; 
        let res = await RepositoryApi.deleteDocument(params);
        this.pending = false; 

        if(res.success) {
          localStorage.removeItem(`document_article_${ this.initData.userInfo.userId }`);
          this.$platform.notification({
            title: this.$t('wiki.create.tips6'),
            type: 'success',
          });
          this.deleteCanClick = true;
          this.openFrame();
        } else {
          this.deleteCanClick = true;
          this.$platform.notification({
            title: res.message,
            type: 'error',
          });
        }

      } catch (e) {
        console.error(e);
      }
    },

    openFrame () {
      let id = window.frameElement.dataset.id;
      this.$platform.closeTab(id);

      let fromId = window.frameElement.getAttribute('id');
      
      // this.$platform.openTab({
      //   id: 'M_INFO_DOC',
      //   title: '知识库列表',
      //   url: '/wiki',
      //   reload: true,
      //   close: true,
      //   fromId
      // });
      openAccurateTab({
        type: PageRoutesTypeEnum.PageWikiList,
        reload: true,
        fromId
      })
    },
    refreshFromTab() {
      let id = window.frameElement.dataset.id;
      this.$platform.closeTab(id);
      let fromid = window?.frameElement?.getAttribute('fromid');
      if(fromid) {
        this.$platform.refreshTab(fromid);
      }
    },

    getInput (html) {
      this.articleHtml = html;
      let imgCount = this.imgCount(html);
      this.articleEmpty = !this.$refs.editor.hasValidText() && !imgCount;

      if(imgCount > 20) {
        this.$platform.notification({
          title: this.$t('wiki.create.tips7'),
          type: 'error',
        });
        return false;
      }
      return true;
    },

    imgCount (html) {
      if(!html) return null;
      let imgReg = /<img.*?(?:>|\/>)/gi // 匹配图片中的img标签
      let arr = html.match(imgReg)  //筛选出所有的img
      return (arr && arr.length) || null;
    },

    // 编辑时获取文章信息
    async getArticle () {
      if(this.isEdit || this.isCopy) {
        try{
          let params = {
            wikiId: this.wikiId || this.copyId,
            updateReadTimes: false
          }
          let res = await RepositoryApi.getInlineDetail(params);
          this.loading = false;
          this.TextRerender = Date.now()

          if(res.success) {
            let detail = res.result;
            this.params.title = detail.title;
            this.params.permission = detail.allowShare;
            this.params.label = detail.label;
            this.params.labelObj = detail.labelList;
            this.params.form.attachments = detail.attachment;
            this.params.article = detail.content;
            this.params.role = detail.role
            this.params.isOpenComment = detail.isOpenComment || 1

            // 故障库
            // 适用产品类型
             this.params.productTypeList = detail?.productTypeList;
            // 故障现象
             this.params.faultPerformance = detail?.faultPerformance;
            // 故障原因
             this.params.faultReason = detail?.faultReason;
            // 预计维修费用起始
             this.params.repairCostBegin = detail?.repairCostBegin;
            // 预计维修费用截止
             this.params.repairCostEnd = detail?.repairCostEnd;
             
             // 获取 文章类别
             this.wikiType = detail?.wikiType;
             this.getTypes()

            if(this.copyId) delete detail.id
            this.info = detail;
            this.params.typeId = detail.typeId
            // this.setType(detail.typeId);
          } else {
            this.$platform.notification({
              title: res.message,
              type: 'error',
            });
          }
        } catch(err) {
          console.error(err)
          this.loading = false;
        }
      } else {
        let detail = localStorage.getItem(`document_article_${ this.initData.userInfo.userId }`);
        let params = JSON.parse(detail);
        this.params.title = params.title || '';
        this.params.permission = params.permission || 0;
        this.params.label = params.label || [];
        this.params.form = params.form || {};
        this.params.article = params.article || ' ';
        this.params.typeId = params.typeId;

        // 故障库
        // 适用产品类型
        this.params.productTypeList = params?.productTypeList;
        // 故障现象
        this.params.faultPerformance = params?.faultPerformance;
        // 故障原因
        this.params.faultReason = params?.faultReason;
        // 预计维修费用起始
        this.params.repairCostBegin = params?.repairCostBegin;
        // 预计维修费用截止
        this.params.repairCostEnd = params?.repairCostEnd;
        // 智能标签
        this.cacheLabelDataHandler(params?.labelObj?.label)
       
      }
    },

    /**
     * @description 缓存标签数据处理
     */
    cacheLabelDataHandler(value) {
      if (!value || !_.isPlainObject(value)) return;
      if (!Array.isArray(value.labels) && !Array.isArray(value.personalLabels)) return;
      // because it is create wiki, so don't need to check changeLabel field, just need to set labelList
      this.params.labelObj = [...value?.labels, ...value?.personalLabels].map(i => {
        return {
          labelId: i.labelId || '',
          name: i.name || ''
        }
      })
    },

    // setType (id) {
    //   this.params.options.forEach(parent => {
    //     if(parent.id == id) {
    //       this.params.typeId.splice(0);
    //       this.params.typeId.push(parent.id);
    //     }

    //     if(parent.subTypes) {
    //       parent.subTypes.forEach(child => {
    //         if(child.id == id) {
    //           this.params.typeId.splice(0)
    //           this.params.typeId.push(parent.id)
    //           this.params.typeId.push(child.id)
    //         }
    //       })
    //     }
    //   })
    // },

    // 文章暂存操作，每2分钟一次，仅在新建时暂存
    saveArticle () {
      this.interval && clearInterval(this.interval);
      this.interval = setTimeout(() => {
        if(this.isSave) {
          let detail = {
            'article': this.params.article,
            'form': this.params.form,
            'permission': this.params.permission,
            'title': this.params.title,
            // 'typeId': this.params.typeId[1] || this.params.typeId[0],
            'typeId': this.params.typeId,
            'label': this.params.label,
          }
          // 故障库,暂存数据
            // 适用产品类型
            detail.productTypeList = this.params?.productTypeList;
            // 故障现象
            detail.faultPerformance = this.params?.faultPerformance;
            // 故障原因
            detail.faultReason = this.params?.faultReason;
            // 预计维修费用起始
            detail.repairCostBegin = this.params?.repairCostBegin;
            // 预计维修费用截止
            detail.repairCostEnd = this.params?.repairCostEnd;
            // 只能标签暂存
            detail.labelObj = this.params?.labelObj

          localStorage.setItem(`document_article_${ this.initData.userInfo.userId }`, JSON.stringify(detail));
          Message.success({
            message: '文章已暂存',
            type: 'success'
          })
        }
        this.isSave = false
      }, 1000 * 60 * 2)
    },

    // 构建参数
    buildParams () {
      let params = {
        title: this.params.title,
        content: this.params.article,
        // typeId: Number(this.params.typeId[1] || this.params.typeId[0]),
        typeId: Number(this.params.typeId),
        attachment: [],
        setLabel: this.params.labelObj?.label || null,
        changeLabel: this.params.labelObj?.changeLabel || false,
      }
      // type 区分是知识空间的还是故障库的文章
      params.wikiType = this.wikiType? Number(this.wikiType) : 0

      // 文档、草稿编辑后存为草稿
      if(this.isEdit && this.isToDraft) {
        params.id = this.info.id;
      }

      // 文档、草稿编辑后提交
      if(this.isEdit && !this.isToDraft) {
        params.id = this.info.id;
      }

      if(this.params.permission == 0) {
        params.allowShare = 0;
        params.role = this.params.role
      }

      if(this.params.permission == 1) {
        params.allowShare = 1;
        params.isOpenComment = this.params.isOpenComment
      }

      if(this.params.form.attachments && this.params.form.attachments.length > 0) {
        params.attachment = this.params.form.attachments;
      }
      
      // 故障库，build数据
      if(this.isFault) {
        // 适用产品类型
        params.productTypeList = this.params.productTypeList;
         // 故障现象
        params.faultPerformance = this.params.faultPerformance;
         // 故障原因
        params.faultReason = this.params.faultReason;
         // 预计维修费用起始
        params.repairCostBegin = this.params.repairCostBegin;
        if(!['',null,undefined].includes(this.params.repairCostBegin)){
          params.repairCostBegin = Number(this.params.repairCostBegin)?.toFixed(2)
        }
        // 预计维修费用截止
        params.repairCostEnd = this.params.repairCostEnd;
        if(!['',null,undefined].includes(this.params.repairCostEnd)){
          params.repairCostEnd = Number(this.params.repairCostEnd)?.toFixed(2)
        }
      }
      return params;
    },

    // 参数校验，标题、内容不允许为空
   async paramsCheck () {
      this.$refs.textTitle.titleCheck();
      if(!this.$refs.editor.hasValidText() && this.params.article.indexOf('<img') == -1) {
        this.articleEmpty = true;
      }
      if(!await this.$refs.textTitle.titleCheck()) return false;
      if(this.articleEmpty) {
        return false;
      }
      if(!this.getInput(this.params.article)) return false;
      return true;
    },

    // TalkingData事件埋点
    trackEventHandler (type) {
      if (type === 'delete') {
        window.TDAPP.onEvent('pc：知识库编辑-删除事件');
        return;
      }
      if (type === 'save') {
        window.TDAPP.onEvent('pc：知识库-保存并提交事件');
        return;
      }
      if (type === 'draft') {
        window.TDAPP.onEvent('pc：知识库-保存草稿');
        return;
      }
    },
    /** 
     * @description 工单AI摘要数据处理
    */
    taskAISummaryDataProcessHandler() {
      // 从 sessionStorage 中获取摘要数据
      const summaryText = sessionStorage.getItem(StorageKeyEnum.AITaskSummary)
      // 如果有摘要数据，将摘要数据赋值给文章内容, 并清除 sessionStorage 中的摘要数据
      if (summaryText) {
        
        this.params.article = summaryText
        
        this.$nextTick(() => {
          this.$refs.editor.setValue(this.params.article)
        })
        
        sessionStorage.removeItem(StorageKeyEnum.AITaskSummary)
        
      }
    },
    async aiQuestionHandler() {
      
      if (isFalsy(this.questionId)) {
        return
      }
      
      const params = {
        id: this.questionId,
      }
      
      await this.fetchQuestion(params)
      
      if (isFalsy(this.question.answer)) {
        return
      }
      
      this.params.article = this.question.answer
      
      this.$nextTick(() => {
        this.$refs.editor.setValue(this.params.article)
      })
      
    }
  },
  computed: {
    pageTitle() {
      if (this.isFault) {
        return this.isEdit ? this.$t('common.wiki.editFault') : this.$t('common.wiki.createFault')
      }
      return this.isEdit ? this.$t('common.pageTitle.pageWikiEdit') : this.$t('common.pageTitle.pageWikiCreate')
    },
    padding () {
      return this.isEdit ? '40px' : '200px';
    },
    // 当前选择的是否是故障库
    isFault(){
      return this.wikiType == 1
    }
  },
  watch: {
    params: {
      handler(n) {
        if(this.isUpdate) {
          this.isSave = true;
          this.saveArticle();
        }
      },
      deep: true,
    }
  }
}
</script>

<style lang="scss" scoped>
body {
  padding: 12px;
}
.label{
  color: rgb(144, 147, 153);
  font-size:14px;
  padding-bottom: 8px;
}
.document-create-container {
  height: 100%;
  overflow: hidden;
}
.document-create-view {
  height: calc(100% - 56px);

  display: flex;

  .view-left {
    flex: 1;
    height: 100%;
    overflow: auto;
    padding: 50px 40px 30px 150px;
    background: #fff;

    .textTitle {
      padding-bottom: 10px;
    }

    .article-error {
      color: #f56c6c;
      font-size: 12px;
      line-height: 12px;
      margin: 0;
      padding-top: 5px;
    }

    .view-left-footer {
      display: flex;
      margin-top: 25px;
      margin-bottom: 100px;

      .green-butn {
        margin-left: 0px;
        margin-right: 15px;

        &:disabled {
          cursor: not-allowed;
        }
      }

      .white-butn {
        background: #fff;
        color: #333;
        border: 1px solid #E2E2E2;

        &:hover {
          border-color: #55B7B4;
          background: #66bebc;
          color: #fff;
        }

        &:disabled {
          opacity: 0.65 !important;
          cursor: not-allowed;
        }
      }
    }

  }

  .view-right {
    width: 350px;
    height: 100%;
    background: #fff;

    border-left: 1px solid #C5D9E8;
  }
}
.el-scrollbar {
  min-height: 45px;
}
.popper-class {

  .el-radio__inner {
    margin-top: 10px;
  }
}
</style>