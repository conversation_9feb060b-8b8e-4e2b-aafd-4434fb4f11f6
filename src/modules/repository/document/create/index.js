import Vue from 'vue'
import DocumentCreateView from './DocumentCreateView.vue';
import mtracker from '@src/util/mtracker';

import { Loading } from 'element-ui';
import { getAsyncDocumentCreateInitData, getAsyncDocumentEditInitData } from '@src/api/InitDataApi'
import qs from 'qs';
import platform from '@src/platform';
import { RESOURCE_PREFIX } from '@src/util/linkSwitch';
import { t } from '@src/locales'

mtracker();

const componentItem = (initData) => {
  return {
    data() {
      return {
        initData
      }
    },
    components: {
      DocumentCreateView
    },
    render(h) {
      return (
        <DocumentCreateView initData={this.initData} />
      )
    }
  }
}

Vue.component('async-component', function(resolve) {
  
  // 需要加loading 打开此注释
  // const instance = Loading.service({ fullscreen: true });
  
  let query = qs.parse(window.location.search.substr(1));
  const params = Object.assign({}, query, {})
  let data = {id:params.wikiId}
  let path = window.location.pathname
  if(path.indexOf(RESOURCE_PREFIX) > -1) path = path.replace(RESOURCE_PREFIX, '')
  const fechInitData = path === '/wiki/create/page' ? getAsyncDocumentCreateInitData : getAsyncDocumentEditInitData;
  fechInitData(data).then(result => {
    
    const initData = (result && result.data && result.data.initJson) || {}
    let title = (result && result.data && result.data.title) || ''
    if (params.wikiType == 1) {
      if (params.wikiId) {
        title = t('common.wiki.editFault')
      } else {
        title = t('common.wiki.createFault')
      }
    }
    const isSuccess = result.succ || result.success
    
    if (!isSuccess) {
      window.location.href = '/500'
      return 
    }
    
    try {
      window._init = JSON.stringify(initData)
    } catch (error) {
      console.error('initData 赋值失败')
    }
    
    // 需要加loading 打开此注释
    // instance.close();
    
    if (window.frameElement) {
      const currentTabId = window.frameElement.dataset.id;
      platform.setTabTitle({
        id: currentTabId,
        title
      })
    }
    resolve(componentItem(initData))
    
  }).catch(error => {
    console.error(error)
    window.location.href = '/500'
  })
  
});

let app = {
  render(h) {
    return <async-component />
  }
}

export default app;
