<template>
  <div class="document-list-view" >
    <div  class="document-list-view-left" :class="{ 'is-closed': leftClosed }">
      <div class="doc-button" v-if="!leftClosed">
          <el-radio-group :value="changeShowType" @input="changeShowTypeHandler">
            <el-radio-button :label="0">按知识库</el-radio-button>
            <el-radio-button :label="1">按标签</el-radio-button>
          </el-radio-group>
        </div>
      <!-- 知识库 -->
      <div class="doc-knowledge" v-if="changeShowType == 0">
      <h3 class="main-title" v-show="!leftClosed">
        <i class="iconfont icon-zhishiku_mian"></i>
        <span>{{$t('wiki.moduleName')}}</span>
      </h3>
      <!-- el-tree 存在一个问题，改变其他下拉框或者复选框，甚至打开弹窗的时候，都会莫名收起（默认全展开的时候，会莫名全展开~） -->
      <el-tree
        ref="typeTree"
        :data="treeData"
        node-key="id"
        :props="treeProps"
        class="typeTree"
        :highlight-current="true"
        @current-change="currentChange"
        :check-on-click-node="true"
        :default-expanded-keys="expandedTreeList"
        :expand-on-click-node="false">
        <span class="custom-tree-node" slot-scope="{ node, data }">
          <span v-if="node.key === 'appendNew'">
            <el-input
              class="addNew"
              @blur="addNew()"
              ref="newType"
              v-model="newType">
            </el-input>
          </span>
          <!-- <span v-else class="node-class" :class="(data.tagType && data.tagType === 'intelligentTagItem') || (data.tagType && data.tagType === 'intelligentTagsGroup')  ? 'in-tag-c' : ''">
            <template v-if="data.tagType && data.tagType === 'intelligentTagsGroup'">
               nothing... 
            </template>
            <template v-else-if="data.tagType && data.tagType === 'intelligentTagItem'">
                <i :class="['iconfont', data.labelType === 0 ? 'icon-zhinengbiao-mian': 'icon-biaoqian-mian']" :style="{ color: data.logoColor }"></i>
            </template>
            <template v-else>
              <i
                :class="{
                  'icon-fenlei1': !['draft', 'published', 'collection', 'record', 'unpublished', 'intelligentTags'].includes(node.key),
                  'icon-zuijinfangwen': node.key === 'record',
                  'icon-shoucang': node.key === 'collection',
                  'icon-yifabu': node.key === 'published',
                  'icon-weifabu': node.key === 'unpublished',
                  'icon-caogaoxiang': node.key === 'draft',
                  'icon-biaoqian-xian': node.key === 'intelligentTags'
                }"
                class="iconfont">
              </i>
            </template> -->
          <span v-else class="node-class">
            <i
              :class="{
                'icon-fenlei1': !['draft', 'published', 'collection', 'record', 'unpublished'].includes(node.key),
                'icon-zuijinfangwen': node.key === 'record',
                'icon-shoucang': node.key === 'collection',
                'icon-yifabu': node.key === 'published',
                'icon-weifabu': node.key === 'unpublished',
                'icon-caogaoxiang': node.key === 'draft',
              }"
              class="iconfont">
            </i>
            <el-tooltip class="item" effect="dark" :content="`${node.label} ${data.count || data.count === 0 ? `（${data.count}）` : ''}`" placement="top-start" :disabled="!nodeLabelEllipsis">
              <span class="span-ellipsis" @mouseover="onShowNodeLabelTipsHandler">{{ node.label }}{{data.count || data.count === 0 ? `（${data.count}）` : ''}}</span>
            </el-tooltip>
          </span>
          <span class="tree-node-expand">
            <div class="newTag" v-if="node.key === 'collection'">
              <span>NEW</span>
            </div>
            <el-button
              type="text"
              size="mini"
              class="tree-item-add tree-node-expand-btn"
              v-if="node.level === 1 && ['published','faultlibrary'].includes(data.id)"
              @click.stop="() => appendNode(node, data,{
                published:0,
                faultlibrary:1
              }[data.id])"
              >
              <i class="iconfont icon-add2"></i>
            </el-button>
            <el-dropdown
              class="tree-item-more tree-node-expand-btn"
              v-if="node.level !== 1 && node.key !== 'appendNew'"
              @command="type => handleCommand(type, node, data)">
              <el-button
                type="text"
                size="mini">
                <i class="iconfont icon-ellipsis"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="new" v-if="node.level <= 10 || node.children < 50">{{$t('wiki.list.btn.createNewCatalog')}}</el-dropdown-item>
                <el-dropdown-item command="rename">{{$t('common.base.rename')}}</el-dropdown-item>
                <el-dropdown-item command="move">{{$t('wiki.list.btn.moveTo')}}</el-dropdown-item>
                <el-dropdown-item command="delete">{{$t('common.base.delete')}}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </span>
        </span>
      </el-tree>
      </div>
      <BizIntelligentTagsFilterPanel
          v-bind="filterTagsPanelBindAttr"
          v-on="filterTagsPanelBindOn"
          :show="changeShowType == 1"
      />
      <div
        @click="leftClosed=!leftClosed"
        class="closeLeft"
        :class="{
          'is-closed': leftClosed
        }">
        <i
          class="iconfont"
          :class="{
            'icon-left1': !leftClosed,
            'icon-right1': leftClosed,
          }"></i>
      </div>
    </div>
    <div class="document-list-view-right">
      <div class="document-list-header">
        <!-- 关键词搜索框 -->
        <div class="search-input-container" ref="searchInput">
          <el-select
            v-model="ifSearchInPublished"
            class="search-type"
            :class="{
              'is-hidden' : ['draft', 'unpublished'].includes(this.currentTreeActiveId)
            }"
            >
            <el-option
              v-for="(item, index) in KeywordSearch"
              :key="index"
              :label="item"
              :value="index">
            </el-option>
          </el-select>

            <!-- @change="keyword => finalSearch = keyword" -->
          <el-input
            class="search-input"
            :placeholder="$t('common.placeholder.inputKeyWordToSearch')"
            v-model="keyword"
            @keyup.enter.native="quickSearch()"
            v-trim:blur>
            <i slot="prefix" class="el-input__icon el-icon-search"></i>
          </el-input>

          <el-button type="primary" @click="quickSearch()" native-type="submit" class="ml_12">
            {{$t('common.base.search')}}
          </el-button>
        </div>
        <div class="base-search">
          <div
            @click.self="panelSearchAdvancedToggle"
            class="advanced-search-visible-btn"
          >
            <i class="iconfont icon-filter task-mr4"></i>
            {{$t('component.advancedSearch.title')}}
          </div>
        </div>
      </div>
      <div class="document-list-bottom-box">
        <div class="document-list-bottom-nav" v-if="outsideShow">
          <div
            :key="index"
            v-for="(tab, index) in [$t('common.base.inside'), $t('common.base.outside')]"
            @click="currTab = index"
            class="document-list-bottom-nav-tab"
            :class="{active: currTab === index}">
            {{ currTitle(index) }}
          </div>
        </div>
        <hr class="nav-bottom-hr" v-if="outsideShow">
        <div v-if="currTab === 0" class="document-list-bottom">
          <div
            class="document-list-bottom-operation"
            v-if="!['collection', 'record'].includes(currentTreeActiveId) || (currentTreeActiveId === 'draft' && listMsg.list.length)">
            <div class="document-list-bottom-left">
              <div class="countTips" v-if="selection.length">
                {{$t('wiki.list.haveChoosedTips', {data1:selection.length})}}
              </div>
              <div class="operation-btns">
                <el-button
                  type="primary"
                  @click="toCreate()"
                  class="add-btn"
                  v-if="authorities.VIP_INFO_CREATE && !['record', 'collection', 'draft'].includes(currentTreeActiveId)">
                  <i class="iconfont icon-add2 task-font14"></i>{{$t('common.base.create')}}
                </el-button>
                <!-- <el-dropdown
                  v-if="isPublishedType && !selection.find(v => v.allowShare)"
                  @command="command => inlineShare(command)">
                  <el-button
                    v-if="isPublishedType"
                    :disabled="!selection.length"
                    type="plain-third">
                    <i class="iconfont icon-share1"></i>批量分享
                  </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="dept">部门</el-dropdown-item>
                    <el-dropdown-item command="person">人员</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
                <el-button
                  v-else-if="isPublishedType"
                  type="plain-third"
                  :disabled="!selection.length"
                  @click="shareDocument()">
                  <i class="iconfont icon-share1"></i>批量分享
                </el-button> -->
                <el-button
                  v-if="isPublishedType && shareEnable"
                  type="plain-third"
                  @click="manyShare()">
                  <i class="iconfont icon-share1"></i>{{$t('common.base.batchSharing')}}
                </el-button>
                <el-button
                  v-if="!['collection', 'record'].includes(currentTreeActiveId) && authorities.INFO_DELETE"
                  type="plain-third"
                  @click="deleteWiki()">
                  <i class="iconfont icon-delete"></i>{{$t('common.base.delete')}}
                </el-button>
                <el-button
                  v-if="!['collection', 'record'].includes(currentTreeActiveId) && authorities.INFO_DELETE"
                  type="primary"
                  :loading="exportAllLoading"
                  @click="exportAll">
                    {{$t('common.base.exportAll')}}
                </el-button>

                <!-- start 批量导入/更新 -->
                <el-dropdown v-if="!['collection', 'record'].includes(currentTreeActiveId) && allowBatchImportButton">

                  <el-button
                    type="plain-third"
                    :loading="batchImportLoading"
                  >
                      {{ $t('common.base.batchImport') }} / {{ $t('common.base.update') }}
                  </el-button>

                  <el-dropdown-menu slot="dropdown">

                    <el-dropdown-item>
                      <div @click="openBatchImportDialog" v-if="allowImport">
                        {{ $t('common.base.batchImport') }}
                      </div>
                    </el-dropdown-item>

                    <el-dropdown-item>
                      <div @click="openBatchImportAttachmentDialog" v-if="allowImportAttachment">
                        {{ $t('wiki.list.importAccessories') }}
                      </div>
                    </el-dropdown-item>

                    <!-- <el-dropdown-item>
                      <div @click="openBatchUpdateAttachmentDialog" v-if="allowImportAttachment">
                        批量更新附件
                      </div>
                    </el-dropdown-item> -->

                  </el-dropdown-menu>
                </el-dropdown>
                <!-- end 批量导入/更新 -->

              </div>
            </div>
            <div class="document-list-bottom-right">
              <BizIntelligentTaggingButtonMulti v-bind="taggingBindAttr" v-on="taggingBindOn" />
              <!-- 文档库排序、标签 -->
              <div class="search-sort">
                <!-- <i class="iconfont icon-swap-right"></i> -->
                <el-select v-model="params.orderDetail.column" class="search-sort">
                  <el-option @click.native="sortSearch('createtime')" value="createtime" :label="$t('common.base.column.createTime')">
                    <span>{{$t('common.base.column.createTime')}}</span>
                    <span @click="changeShow()" v-if="params.orderDetail.column == 'createtime'">
                      <i class="iconfont icon-paixuxia2" :style="{color: !sortUp ? getThemeColor : '#606266'}"></i>
                      <i class="iconfont icon-paixushang" :style="{color: sortUp ? getThemeColor : '#606266'}"></i>
                    </span>
                  </el-option>
                  <el-option @click.native="sortSearch('updatetime')" value="updatetime" :label="$t('common.base.column.updateTime')">
                    <span>{{$t('common.base.column.updateTime')}}</span>
                    <span @click="changeShow()" v-if="params.orderDetail.column == 'updatetime'">
                      <i class="iconfont icon-paixuxia2" :style="{color: !sortUp ? getThemeColor : '#606266'}"></i>
                      <i class="iconfont icon-paixushang" :style="{color: sortUp ? getThemeColor : '#606266'}"></i>
                    </span>
                  </el-option>
                  <el-option
                    @click.native="sortSearch('readTimes')"
                    v-if="!['unpublished', 'draft'].includes(currentTreeActiveId)"
                    value="readTimes" :label="$t('wiki.list.sortLabel.label1')">
                    <span>{{$t('wiki.list.sortLabel.label1')}}</span>
                    <span @click="changeShow()" v-if="params.orderDetail.column == 'readTimes'">
                      <i class="iconfont icon-paixuxia2" :style="{color: !sortUp ? getThemeColor : '#606266'}"></i>
                      <i class="iconfont icon-paixushang" :style="{color: sortUp ? getThemeColor : '#606266'}"></i>
                    </span>
                  </el-option>
                  <el-option
                    value="likeTimes"
                    :label="$t('wiki.list.sortLabel.label2')"
                    @click.native="sortSearch('likeTimes')"
                    v-if="!['unpublished', 'draft'].includes(currentTreeActiveId)">
                    <span>{{$t('wiki.list.sortLabel.label2')}}</span>
                    <span @click="changeShow()" v-if="params.orderDetail.column == 'likeTimes'">
                      <i class="iconfont icon-paixuxia2" :style="{color: !sortUp ? getThemeColor : '#606266'}"></i>
                      <i class="iconfont icon-paixushang" :style="{color: sortUp ? getThemeColor : '#606266'}"></i>
                    </span>
                  </el-option>
                </el-select>
                <el-tag class="search-tag" closable @close="closeTag" v-if="tag.show">{{tag.name}}</el-tag>
              </div>
            </div>
          </div>

           <!-- 故障库时显示 -->
           <div v-if="isFault" class="FaultTip">{{$t('wiki.list.faultTips')}}</div>
          <!-- 列表部分 -->
          <list
            v-loading="listLoading"
            class="list"
            @tag="setTag"
            :share-enable="shareEnable"
            :keyword="params.keyword"
            :init-data="initData"
            :value="listMsg"
            :id.sync="info.id"
            @toDetail="toDetail"
            @selectChange="selectChange"
            :isFault="isFault"
            ref="list"
            :wikiShare="wikiShare"
            :currentTreeActiveId="currentTreeActiveId"
            @reloadList="search({}, { type: currentTreeActiveId })"
            :noCheckbox="['collection', 'record'].includes(currentTreeActiveId)"
            v-if="listMsg.list && listMsg.list.length > 0"
          >
          <template v-slot:tagsView="{info}">
            <div @click.stop>
              <BizIntelligentTagsView
                type="detail"
                :value="info.title || info.handleTitle"
                :tagsList="info.labelList || []"
                :config="showIntelligentTagType"
              />
            </div>
          </template>
        </list>
          <div v-else class="list empty" v-loading="listLoading">
            <BaseListForNoData v-show="!listLoading" :notice-msg="$t('common.base.tip.noData')"></BaseListForNoData>
          </div>

          <!-- 页脚部分 -->
          <list-footer
            class="list-footer"
            @search="(params)=>search(params,{ type:currentTreeActiveId })"
            :total="listTotal"
            v-model="params"
            ref="listFooter"
          ></list-footer>
        </div>
        <div v-else-if="allowOutsideSearch" class="outside-box">
          <div class="site-btns">
            <el-radio-group
              v-model="currSite"
              @change="outsideLink = currLink;getOutsideSearchCount()"
              size="small">
              <el-radio-button
                :key="site.id"
                v-for="site in siteList"
                :label="site.id">
                {{ site.name }}
              </el-radio-button>
            </el-radio-group>
          </div>
          <!-- <object class="outsite-show" :data="currSiteObj.link" type=""></object> -->
          <iframe class="outsite-show" :src="outsideLink" frameborder="0"></iframe>
        </div>
      </div>
    </div>
    <!-- 高级搜索 -->
    <!-- @visibleChange="searchPanelVisibleChange" -->
    <search-panel
      :config="{
        fields: [],
      }"
      :type="currentTreeActive.type"
      ref="searchPanel">
      <div
        class="advanced-search-btn-group"
        slot="footer">
        <el-button
          type="ghost"
          @click="resetParams()">
          {{$t('common.base.reset')}}
        </el-button>
        <el-button
          type="primary"
          @click="() => powerfulSearch()"
          native-type="submit">
          {{$t('common.base.search')}}
        </el-button>
      </div>
    </search-panel>
    <move-catalog
      type="catalog"
      :moveTreeDataFromProp="moveTreeData"
      :getDataFromParent="true"
      v-model="moveDialogVisible"
      v-if="moveDialogVisible"
      @moveEnd="getTypes();getFaultTypes()"
      :beMoveData="beMoveData"
      >
    </move-catalog>

    <document-share
      ref="documentShare"
      :wikiList="wikiListForShare">
    </document-share>

    <!-- start 批量导入 -->
    <base-import
      title="批量导入"
      ref="batchImport"
      :is-import-now="false"
      :action="batchImportAction"
      uploadParamString="wikiFile"
    >
      <div slot="tip">
        <div class="base-import-warn">
          <i18n path="common.base.importModal.downloadTemplateTip" tag="p" style="margin: 0">
            <a :href="knowledgeBaseImportTemplate" place="link">
              {{$t('common.base.importModal.importTemplate')}}
            </a>
          </i18n>
        </div>
      </div>
    </base-import>
    <!-- end 批量导入 -->

    <!-- start 批量导入附件 -->
    <base-import
      title="批量导入附件"
      ref="batchImportAttachment"
      :is-import-now="false"
      :action="batchImportAttachmentAction"
      accept=".zip,.rar"
      module="WIKI"
    >
      <div class="batch-import-attachment-tip" slot="tip">

        <div>
          <p>
            1. <a :href="knowledgeBaseImportFileTemplate" place="link"> 下载 </a> 附件导入模板
          </p>
          <p>
            2. 上传文件进行批量导入
          </p>
        </div>

        <div>
          仅支持上传zip格式，每个文件大小不超过500M，每篇文章不超过30个附件。<br>
          附件名称请以“知识库编号_原文件名称”的形式命名。如“家电使用手册”的知识库编号为A1001，附件命名为“A1001_家电使用手册”。
        </div>

      </div>
    </base-import>
    <!-- end 批量导入附件 -->

    <!-- start 批量更新附件 -->
    <base-import
      title="批量更新附件"
      ref="batchUpdateAttachment"
      :is-import-now="false"
      :action="batchUpdateAttachmentAction"
      accept=".zip,.rar"
      module="WIKI"
    >
      <div class="batch-update-attachment-tip" slot="tip">

        <div>
          上传修改后的文件进行批量更新 <br>
          仅支持上传 zip,rar 格式，每个文件大小不超过 500 M，每篇文章不超过 50 个附件。<br>
          附件名称请以“知识库编号_数字”的形式命名，同一篇最多支持上传5个附件。如“家电使用手册”的知识库编号为A1001，附件命名为“A1001_1”、“A1001_2”
        </div>

      </div>
    </base-import>
    <!-- end 批量更新附件 -->

  </div>
</template>

<script>
// import ListSearch from './component/ListSearch';
import List from './component/List';
import ListFooter from '../../common/ListFooter';
import DocumentDetailView from '../detail/DocumentDetailView';
import moveCatalog from '@src/modules/repository/document/components/moveCatalog'
import documentShare from '@src/modules/repository/document/components/documentShare'

import * as RepositoryApi from '@src/api/Repository';
import {getAllWikiOutsideSearch} from '@src/api/SettingApi';
/* mixin */
import ThemeMixin from '@src/mixins/themeMixin/index.ts'
import { intelligentTagsListMixin } from '@src/modules/intelligentTags/mixins/index'

import { openTabForWikiCreate, openTabForWikiView } from '@src/util/business/openTab'

import SearchPanel from './component/SearchPanel';
import _ from 'lodash'
import { getRootWindow } from '@src/util/dom'
import i18n from '@src/locales'

/* util */
import { exportAlert } from '@src/util/alert';
import { storageGet, storageSet } from '@src/util/storage';
import { getTagsGroupToTagsList }  from '@src/modules/intelligentTags/utils/utils'

import {
  knowledgeBaseImport,
  knowledgeBaseImportFileTemplate,
  knowledgeBaseImportFile,
  knowledgeBaseUpdateFile,
  knowledgeBaseImportTemplate
} from '@src/api/Import'
/* api */
import { syncWiki, deleteWiki } from '@src/api/LLMApi'

import { getRootWindowInitData } from '@src/util/window'
const rootWindowInitData = getRootWindowInitData()

export default {
  mixins: [ThemeMixin, intelligentTagsListMixin],
  props: {
    initData: {
      // 配置信息
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      params: {
        label: '', // 标签
        keyword: '', // 搜索的关键词
        pageNum: 1,
        pageSize: this.getPageSize(),
        orderDetail: {
          // 排序
          isSystem: 1,
          column: 'createtime',
          type: '',
          sequence: 'desc',
        },
        view: 'published',
        fasterFindId: '',
      },
      tag: {
        name: '',
        show: false,
      }, // 选中的标签
      listTotal: null,

      listMsg: {}, // 列表全部数据
      info: {
        // 传给右侧详情的文档id、allowShare
        id: null,
        allowShare: 0,
        isLast: false,
      },

      selectedTreeItem: {},
      treeData: [{
        id: 'record',
        name: i18n.t('wiki.list.treeData.label1'),
        subTypes: []
      }, {
        id: 'collection',
        name: i18n.t('wiki.list.treeData.label2'),
        subTypes: []
      }, {
        id: 'published',
        name: i18n.t('wiki.list.treeData.label3'),
        type:0,
        subTypes: []
      }, {
        id: 'faultlibrary',
        name: i18n.t('wiki.list.treeData.label4'),
        type:1,
        subTypes: []
      }, {
        id: 'unpublished',
        name: i18n.t('wiki.list.treeData.label5'),
        subTypes: []
      }, {
        id: 'draft',
        name: i18n.t('wiki.list.treeData.label6'),
        subTypes: []
      }],

      sortUp: false,
      sortWay: 'createtime',

      typeOptions: [],
      currentTreeActive: {
        type:0
      },

      selection: [],

      newType: '',

      treeProps: {
        label: 'name',
        children: 'subTypes'
      },
      beMoveData: void 0,

      moveDialogVisible: false,
      moveTreeData: [],
      currTab: 0,
      currSite: '',
      siteList: [],

      outsideLink: '',
      outsideCount: 0,
      listLoading: true,

      expandedTreeList: [],

      leftClosed: false,

      ifSearchInPublished: 0,

      keyword: '',
      finalSearch: '',
      // finalSearchBackUp: '',

      isTypesAppending: false,

      wikiListForShare: [],
      shareEnable: false, // 是否有分享权限
      views: {
        0:'published',
        1:'faultlibrary'
      },
      exportAllLoading: false,
      batchImportLoading: false,
      batchImportAction: knowledgeBaseImport,
      batchImportAttachmentAction: knowledgeBaseImportFile,
      batchUpdateAttachmentAction: knowledgeBaseUpdateFile,
      knowledgeBaseImportFileTemplate,
      knowledgeBaseImportTemplate: knowledgeBaseImportTemplate,
      tenantId: rootWindowInitData?.user?.tenantId,
      nodeLabelEllipsis: false,
      changeShowType: 0, // 选择显示类型
      cloneParamsView: {}, // 备份搜索参数
      cloneCurrentNodeKey: '', // 备份当前选中节点key
      isInit: false // mounted 加载过程 从true 到 false 表示mounted完整过程记录
    }
  },
  components: {
    // [ListSearch.name]: ListSearch,
    [List.name]: List,
    [ListFooter.name]: ListFooter,
    [DocumentDetailView.name]: DocumentDetailView,
    [SearchPanel.name]:SearchPanel,
    [moveCatalog.name]:moveCatalog,
    [documentShare.name]:documentShare,
  },
  computed: {
    showIntelligentTagType() {
      return {
        normalShowType: 'text',
        normalMaxLength: 3
      }
    },
    allowCreate() {
      return this.authorities?.VIP_INFO_CREATE
    },
    allowEdit() {
      return this.authorities?.INFO_COMPILER
    },
    allowImport() {
      return this.allowCreate
    },
    allowImportAttachment() {
      return this.allowEdit
    },
    allowBatchImportButton() {
      return this.allowImport || this.allowImportAttachment
    },
    KeywordSearch() {

      // 最近访问和已收藏
      const isRecordOrCollection = ['record', 'collection'].includes(this.currentTreeActiveId)
      const recordOrCollectionOptions = [
        this.$t('wiki.list.listSearch.titleSearchSpace'),
        this.$t('wiki.list.listSearch.contentSearchSpace')
      ]
      const otherOptions = [
        this.$t('wiki.list.listSearch.title3'),
        this.$t('wiki.list.listSearch.content')
      ]

      const options = isRecordOrCollection ? recordOrCollectionOptions : otherOptions

      return options
    },
    // 当前选择的是否是故障库
    isFault(){
      return this.currentTreeActive.type === 1
    },
    // 故障库灰度
    isFaultGrayscale(){
     const RootWindow = getRootWindow(window)
     return RootWindow?.grayAuth?.faultLibrary
    },
    allowOutsideSearch() {
      return this.initData.wikiConfig.outsideSearch
    },
    currentTreeActiveId() {
      return this.currentTreeActive?.id
    },
    authorities() {
      return this.initData.userInfo.authorities
    },
    outsideShow() {
      if (!this.outsideLink || !this.allowOutsideSearch) this.currTab = 0
      return !!this.outsideLink && this.allowOutsideSearch
    },

    currSiteObj() {
      return this.siteList.find(site => site.id === this.currSite) || {}
    },
    isDefaultSite() {
      return ['搜狗', '360搜索', '必应'].includes(this.currSiteObj.name)
    },

    currLink() {
      if (!this.currSiteObj) return ''
      let link = this.currSiteObj.link || ''
      let search = this.finalSearch
      // this.params.label || this.params.keyword
      if(!search) return ''
      switch (this.currSiteObj.name) {
        case '必应':
          return `https://cn.bing.com/search?q=${search}`
        case '搜狗':
          return `https://www.sogou.com/web?query=${search}`
        case '360搜索':
          return `https://www.so.com/s?q=${search}`
        default:
          return link
      }
    },

    // 当前为published或者子分类的情况
    isPublishedType() {
      return !['draft', 'collection', 'record', 'unpublished'].includes(this.currentTreeActiveId)
    },
  },

  async mounted() {
    let currentTreeActive = this.treeData.find(tree => tree.id === 'published')
    //监听发起审批返回到未发布状态
     if(storageGet('isCreateApprove') == 'true'){
      currentTreeActive = this.treeData.find(tree => tree.id === 'unpublished');
      this.params.view = 'unpublished';
      storageSet('isCreateApprove', '');
     }
    this.currentTreeActive = currentTreeActive;
    this.shareEnable = await this.getWikiConfig();
    this.getTypes(false)
    this.getFaultTypes(false)
    this.getAllWikiOutsideSearch()
    this.search();

    this.tenantId = rootWindowInitData?.user?.tenantId

    // 故障库灰度
    if(!this.isFaultGrayscale){
      let index = this.treeData.findIndex(item=>item.id === 'faultlibrary')
      this.treeData.splice(index,1)
    }
    window.__exports__refresh = async () => {
      this.initCount()
      this.search()
    }
  },

  methods: {
    /**
     * @desc 切换tab逻辑
     */
    changeShowTypeHandler(val) {
      this.changeShowType = val
      // 当切换到按标签搜索时，知识库搜索参数的wikiTypeId置空
      if (val == 1) {
        // 备份一份选中的节点信息
        this.cloneCurrentNodeKey = this.$refs?.typeTree && this.$refs?.typeTree?.getCurrentKey() || ''
        this.cloneParamsView = {...this.params}
        this.params.wikiTypeId = ''
        this.params.view = ''
        this.resetIntelligentTagsSearchParams()
        this.search()
        return
      }
      this.$nextTick(() => {
        // 如果是从标签使用统计进来，如果没有从按知识库切进来的，给个默认参数
        this.$refs?.typeTree && this.$refs?.typeTree?.setCurrentKey(this.cloneCurrentNodeKey ? this.cloneCurrentNodeKey : 'published')
        this.params.view = this.cloneParamsView?.view || 'published'
        this.search()
      })
    },
    /**
     * @description 打开批量导入弹窗
    */
    openBatchImportDialog() {
      this.$refs.batchImport.open()
    },
    /**
     * @description 打开批量导入附件弹窗
    */
    openBatchImportAttachmentDialog() {
      this.$refs.batchImportAttachment.open()
    },
    /**
     * @description 打开批量更新附件弹窗
    */
    openBatchUpdateAttachmentDialog() {
      this.$refs.batchUpdateAttachment.open()
    },
    // 导出知识库
    exportAll() {
      this.exportAllLoading = true
      let para = { ...this.params, ...this.builderIntelligentTagsSearchParams() }

      para.flag = false
      if (para.label === '') {
        delete para.label
      }
      // 导出全部数据
      para.pageSize = this.listTotal
      RepositoryApi.exportAll(para)
        .then(res => {
          if(res.succ){
            exportAlert(res.message);
          }
        })
        .finally(() => {
          this.exportAllLoading = false;
        })
    },
    async getWikiConfig() {
      try {
        let res = await RepositoryApi.getWikiConfig()
        return res?.result?.permitShare || false;
      } catch (e) {
        console.error(e)
        return false;
      }
    },
    // 批量分享
    async manyShare() {
      if (!this.selection.length) {
        this.$platform.notification({
          title: this.$t('wiki.list.manyShareTitle'),
          type: 'error',
        })
        return
      }
      // 点击批量分享判断是否有分享权限
      let share = await this.getWikiConfig();
      if(!share) {
        this.$message.error(this.$t('wiki.list.noShareTips'))
        return
      }
      this.wikiListForShare = this.selection;
      let show = this.wikiListForShare.every(v => v.allowShare === 1);
      (this.$refs.documentShare || {}).shareDocument(show)
    },
    async wikiShare (wiki) {
      // 点击分享判断是否有分享权限
      let share = await this.getWikiConfig();
      if(!share) {
        this.$message.error(this.$t('wiki.list.noShareTips'))
        return
      }
      this.wikiListForShare = [wiki];
      let show = this.wikiListForShare.every(v => v.allowShare === 1);
      (this.$refs.documentShare || {}).shareDocument(show)
    },
    // searchPanelVisibleChange(newValue) {
    //   if (newValue) {
    //     this.finalSearchBackUp = this.finalSearch
    //   } else {
    //     console.log(1234432)
    //     this.finalSearch = this.finalSearchBackUp
    //   }
    // },
    findExpandedNode(list = [], data = this.treeData) { // 寻找所有展开的节点id
      data.forEach(data => {
        if (this.$refs.typeTree && this.$refs.typeTree.getNode(data)?.expanded === true) {
          list.push(data)
          if (data.subTypes?.length) {
            this.findExpandedNode(list, data.subTypes)
          }
        }
      })
      return list
    },
    // 初始化数量
    async initCount () {
      try {
        let res = await RepositoryApi.getDocumentViewCount();

        if(res.success) {
          this.treeData.forEach(item => {
            if(item.id == 'published') this.$set(item, 'count', res.result?.published || 0)
            if(item.id == 'unpublished') this.$set(item, 'count', res.result?.unpublished || 0)
            if(item.id == 'faultlibrary') this.$set(item, 'count', res.result?.faultLibrary || 0)
            if(item.id == 'draft') this.$set(item, 'count', res.result?.draft || 0)
          })
        }
      } catch (err) {
        console.error(err)
      }
    },
    currTitle(index) {
      if (index === 0) {
        return this.$t('wiki.list.searchResTips.tips1', {data1:this.listTotal || 0})
        // return `内部（${this.listMsg?.list?.length || 0}）`
      } else if(['搜狗', '360搜索', '必应'].includes(this.currSiteObj.name)) {
        return this.$t('wiki.list.searchResTips.tips2', {data1:this.outsideCount})
        // return `外部（${this.outsideCount}）`
      } else {
        // return '外部'
        return this.$t('wiki.list.searchResTips.tips3')
      }
    },
    quickSearch() {

      if(this.ifSearchInPublished === 0){

        // 按标题搜索
        this.params.keyword = this.keyword

        delete this.params.keyWordEnhance
        delete this.params.wikiNumber

      }else  if(this.ifSearchInPublished === 1){
        // 按正文搜索
        this.params.keyWordEnhance = this.keyword

        delete this.params.keyword
        delete this.params.wikiNumber

      }
      else if (this.ifSearchInPublished === 2) {

        // 按编号搜索
        this.params.wikiNumber = this.keyword

        delete this.params.keyWordEnhance
        delete this.params.keyword

      }

      this.finalSearch = this.params.keyword
      // 搜索锁定知识空间列表
      if(['record', 'collection'].includes(this.currentTreeActiveId)){
        // 只有最近访问和已收藏需要切换至知识空间
       this.currentTreeActive = this.treeData.find(tree => tree.id === 'published')
       this.$refs.typeTree && this.$refs.typeTree.setCurrentKey(this.currentTreeActiveId, true, false)
      }
      this.search({pageNum: 1}, { searchType: 'quickSearch',type: this.currentTreeActiveId })
      this.getOutsideSearchCount()
      this.outsideLink = this.currLink
      this.trackEventHandler('search')
    },
    async getAllWikiOutsideSearch() {
      try {
        const result = await getAllWikiOutsideSearch();
        if (result.succ) {
          this.siteList = (result.data || []).filter(v => v.state)
          this.currSite = this.siteList[0]?.id
        }
      } catch (error) {
        console.error("get dispatch rules error", error);
      }
    },

    // 分类菜单处理
    handleCommand(type, node, data) {
      if (type === 'new') {
        this.appendNode(node, data,this.currentTreeActive.type)
      } else if (type === 'rename') {
        this.editNode(node, data)
      } else if (type === 'delete') {
        this.deleteType(data.id)
      } else if (type === 'move') {
        this.beMoveData = data
        this.moveDialogVisible = true
        let key =''
        if(this.currentTreeActive.type === 0){
          // 知识空间
          key = 'published'
        }else if(this.currentTreeActive.type === 1){
          // 故障库
          key = 'faultlibrary'
        }
        this.moveTreeData = [_.cloneDeep(this.treeData.find(v => v.id === key))]
      }

      if (type == 'sync') {
        this.syncWiki(data)
      }

    },
    syncWiki() {

      const params = {
        tenantId: rootWindowInitData?.user?.tenantId,
        wikiTypeId: this.params.wikiTypeId,
      }

      syncWiki(params).then((result) => {

        const isSuccess = result?.success

        if (isSuccess) {
          this.$platform.notification({
            title: '同步中, 请稍等片刻',
            type: 'success',
          })
        } else {
          this.$platform.notification({
            title: result?.message,
            type: 'error',
          })
        }


      })
    },
    deleteLLMWiki(ids) {

      const params = {
        ids,
        tenantId: rootWindowInitData?.user?.tenantId,
      }

      deleteWiki(params).then((result) => {
      })

    },
    selectChange(value) {
      this.selection = value || []
    },
    // 删除文章
    async deleteWiki () {
      if (!this.selection.length) {
        this.$platform.notification({
          title: this.$t('wiki.list.manyShareTitle'),
          type: 'error',
        })
        return
      }
      try {
        if (!await this.$platform.confirm(this.$t('wiki.list.deleteWikiTips'))) return;
        this.deleteCanClick = false;

        let params = {
          wikiIds: this.selection.map(v => v.id)
        };

        this.pending = true;
        let res = await RepositoryApi.deleteDocument(params);
        this.pending = false;

        if(res.success) {

          localStorage.removeItem(`document_article_${ this.initData.userInfo.userId }`);

          this.$platform.notification({
            title: this.$t('wiki.list.deleteWikiSuccessTips'),
            type: 'success',
          });
          this.deleteCanClick = true;
          this.search()
          this.initCount()

          this.deleteLLMWiki(params.wikiIds)

          if (this.changeShowType == 1) {
            this.deleteTagFetch();
          }

          // this.openFrame();
        } else {
          this.deleteCanClick = true;
          this.$platform.notification({
            title: res.message,
            type: 'error',
          });
        }

      } catch (e) {
        console.error(e);
      }
    },
    // 改变排序（默认false为降序）
    changeShow(init = false) {
      this.sortUp = init ? false : !this.sortUp
    },
    // 高级搜索
    panelSearchAdvancedToggle(){
      this.$refs.searchPanel.open();
      this.$nextTick(() => {
        let forms = document.getElementsByClassName('advanced-search-form');
        for (let i = 0; i < forms.length; i++) {
          let form = forms[i];
          form.setAttribute('novalidate', true);
        }
      });
    },
    // 重置
    resetParams(ifSearch = true){
      this.searchIncludeMoreConditions = false;
      this.params = {
        label: '', // 标签
        keyword: '', // 搜索的关键词
        pageNum: 1,
        pageSize: this.params.pageSize,
        orderDetail: this.params.orderDetail,
        view: this.params.view,
        fasterFindId: '',
        wikiTypeId: this.params.wikiTypeId
      },
      this.keyword = ''
      this.finalSearch = ''
      // this.finalSearchBackUp = ''
      this.outsideLink = ''

      this.$refs.searchPanel.resetParams();
      ifSearch && this.search();
    },
    powerfulSearch () {
      this.params.keyword = this.keyword
      this.params.pageNum = 1;
      Object.assign(this.params, this.$refs.searchPanel.buildParams())
      // if (this.params.label && this.params.label !== this.finalSearchBackUp) {
      //   this.finalSearchBackUp
      // }
      this.finalSearch = this.params.label
      // this.params.moreConditions = this.$refs.searchPanel.buildParams();

      this.search({}, { searchType: 'powerfulSearch' });

      this.getOutsideSearchCount()
      // this.finalSearchBackUp = this.finalSearch

      this.outsideLink = this.currLink
    },

    toCreate() {
      // 如果当前列表是故障库及其子类
      let data = {}
      const types = {
        published : 0,
        faultlibrary : 1,
      }
      data.wikiType = 'type' in this.currentTreeActive ? this.currentTreeActive.type : types[this.currentTreeActive.id]

      this.trackEventHandler('create')
      openTabForWikiCreate({...data,wikiTypeId: this.params.wikiTypeId}) // 新建知识库传递选中的分类
    },
    sortSearch (type) {
      if(this.sortWay !== type) {
        this.changeShow(true)
      }
      this.sortWay = type // 备份，作比较

      this.params.orderDetail.sequence = this.sortUp ? 'asc' : 'desc';
      this.params.pageNum = 1;
      if(['collection', 'record'].includes(this.currentTreeActiveId)) {
        this.search({}, { type: this.currentTreeActiveId })
      } else {
        this.search()
      }
      // this.$emit('search', this.params);
    },
    // TalkingData事件埋点
    trackEventHandler (type) {
      if (type === 'create') {
        window.TDAPP.onEvent('pc：知识库列表-新建事件');
        return;
      }
      if (type === 'search') {
        window.TDAPP.onEvent('pc：知识库列表-搜索事件');
        return;
      }
      if (type === 'reset') {
        window.TDAPP.onEvent('pc：知识库列表-重置事件');
        return;
      }
    },
    // 重置排序
    initSort() {
      this.params.orderDetail.column = 'createtime'
      this.changeShow(true)
    },
    // 树形结构切换
    currentChange(data, node) {
      // const isTagGroup = Reflect.has(data, 'tagType') && data.tagType === 'intelligentTagsGroup'
      // // 如果是标签组就return
      // if (isTagGroup) return
      if(node.key === 'appendNew') return // 新建或编辑类目的时候

      // const isIntelligentList = data?.tagType && data.tagType === 'intelligentTagsList'
      // const isIntelligentTags =  data?.tagType && data.tagType === 'intelligentTagItem'
      // const isIntelligentTagsGroup = isTagGroup
      // setTimeout(() => {
        // this.$nextTick(() => {
        //   this.$refs.typeTree.setChecked(node.key, true, false)
        // })
      // }, 300)
      if (this.currentTreeActiveId === node.key) return
      if (this.currentTreeActiveId !== node.key) {
        this.clearSelection()
      }
      this.currentTreeActive = data
      // !isIntelligentTags || !isIntelligentTagsGroup  ? this.currentTreeActive = data : this.currentTreeActive = { id: data.id, name: data.name, subTypes: [], type: 0 }

      if (['collection', 'record'].includes(node.key)) {
        this.params.view = 'published'
        this.params.wikiTypeId = ''
        this.resetParams(false)
        this.ifSearchInPublished = 0;
        this.search({pageNum: 1}, { type: node.key })
        return
      }
      if (['published', 'draft', 'unpublished','faultlibrary'].includes(node.key)) {
        if(['draft', 'unpublished'].includes(node.key) && ['readTimes', 'likeTimes'].includes(this.params.orderDetail.column)) {
          this.initSort()
        }
        this.params.view = node.key
        this.params.wikiTypeId = ''
      } else {
        this.params.view = this.views[data.type]
        this.params.wikiTypeId = node.key
        // // 如果点击是标签或标签大分组
        // this.params.view = isIntelligentList || isIntelligentTags ? '' : (this.views[data.type] || '')
        // if(!(isIntelligentTags || isIntelligentList)) {
        //   this.params.wikiTypeId = node.key
        // } else {
        //     this.params.wikiTypeId = ''
        // }
      }

      this.params.pageNum = 1

      // if(isIntelligentTags) {
      //   return this.filterTagsPanelBindOn.filterItemClick(data)
      // }

      // this.filterTagsPanelBindOn.filterItemClick({ id: 'all-tags'})
      // this.listLoading = !this.listLoading;
      this.search()
    },
    // 移除树节点
    removeNode(node) {
      this.$refs.typeTree.remove(node)
    },
    // 普通类目下新增类目(输入框)
    appendNode(node, data, type=0) {
      if (this.isTypesAppending) return

      this.isTypesAppending = true
      this.newType = this.$t('wiki.list.newType')
      this.newData = {
        id: 'appendNew',
        name: '',
        subTypes: [],
        parentId: data.id,
        type: type
      }
      data.subTypes.unshift(this.newData)
      node.expanded = true
      // this.$refs.typeTree.store.nodesMap[data.id].expanded = true;
      this.$nextTick(() => {
        setTimeout(() => {
          let input = this.$refs.newType?.$el?.querySelector('input')
          input.focus()
          input.select()
        }, 300)
      })
    },
    // 已发布下新增类目(输入框)
    addNew() {
      if (!this.newType) {
        this.cancelAppend()
        return
      }
      this.sumbitType(!!this.newData.idBackUp)
    },
    // 编辑类目名称（输入框）
    editNode(node, data) {
      this.newType = data.name
      data.idBackUp = data.id
      data.id = 'appendNew'
      this.newData = data
      this.$nextTick(() => {
        setTimeout(() => {
          let input = this.$refs.newType?.$el?.querySelector('input')
          input.focus()
          input.select()
        }, 300)
        // this.$refs.newType && this.$refs.newType.$el.querySelector('input').focus();
      })
    },

    // 取消新增/编辑框
    cancelAppend() {
      if(this.newData.idBackUp) {
        this.newData.id = this.newData.idBackUp
        delete this.newData.idBackUp
      } else {
        this.removeNode(this.newData)
      }
      this.newData = void 0;
      this.newType = ''
      this.$nextTick(() => {
        this.isTypesAppending = false
      })
    },
    // 提交编辑或添加的分类
    async sumbitType (isEdit) {
      try {
        let res;
        let parentId = this.newData.parentId
        let params = {
          name: this.newType,
          id: isEdit ? this.newData.idBackUp : '',
          parentId: ['published','faultlibrary'].includes(parentId) ? '' : parentId,
          type: this.newData.type
        }
        if(isEdit) {
          res = await RepositoryApi.updateDocumentType(params);
        } else {
          res = await RepositoryApi.addDocumentType(params);
        }

        if(res.success) {
          let msg = isEdit ? this.$t('wiki.list.sumbitType.msg1') : this.$t('wiki.list.sumbitType.msg2');
          this.$platform.notification({
            title: msg,
            type: 'success',
          });
          await this.getTypes();
          await this.getFaultTypes()
          // this.$nextTick(() => {
          //   this.$refs.typeTree.setChecked('published', true, false)
          // })
          // await this.getTypesCount();
          // this.search();
        } else {
          this.$platform.notification({
            title: res.message,
            type: 'error',
          })
          this.cancelAppend()
        }
        // isEdit = false;
      } catch (err) {
        console.error(err)
        this.$platform.notification({
          title: isEdit ? this.$t('wiki.list.sumbitType.msg3') : this.$t('wiki.list.sumbitType.msg4'),
          type: 'error',
        })
        this.cancelAppend()
      }
      this.$nextTick(() => {
        this.isTypesAppending = false
      })
    },
    // 删除分类
    async deleteType (id) {
      try {
        if (!await this.$platform.confirm(this.$t('wiki.list.deleteType.tips1'))) return;
        let params = {
          typeId: id
        }
        let res = await RepositoryApi.deleteDocumentType(params);

        if (res.success) {
          this.$platform.notification({
            title: this.$t('wiki.list.deleteType.tips2'),
            type: 'success',
          });
          await this.getTypes()
          await this.getFaultTypes()
          // this.search();
        } else {
          this.$platform.notification({
            title: res.message,
            type: 'error',
          });
        }
      } catch (e) {
        console.error(e);
      }
    },
    // 获取知识空间分类二级树状结构
    async getTypes (nendSaveExpand = true) {
      return new Promise(async (resolve, reject) => {
        try {
          let res = await RepositoryApi.getDocumentTypes();
          if(res.success) {
            let expandedTreeList = this.findExpandedNode()

            let parent = this.treeData.find(v => v.id === 'published')
            parent && (parent.subTypes = res.result)

            this.$nextTick(() => {
              nendSaveExpand && (this.expandedTreeList = expandedTreeList)
              this.$refs.typeTree && this.$refs.typeTree.setCurrentKey(this.currentTreeActiveId, true, false)
            })
            this.initCount()

            resolve()
          } else {
            this.$platform.notification({
              title: res.message,
              type: 'error',
            });
            reject();
          }
        } catch (err) {
          console.error(err);
        }
      })
    },
     // 获取故障库分类二级树状结构
    async getFaultTypes (nendSaveExpand = true) {
      return new Promise(async (resolve, reject) => {
        try {
          let res = await RepositoryApi.getDocumentTypes({
            type:1
          });
          if(res.success) {
            let expandedTreeList = this.findExpandedNode()

            let parent = this.treeData.find(v => v.id === 'faultlibrary')
            parent && (parent.subTypes = res.result)

            // this.$nextTick(() => {
            //   nendSaveExpand && (this.expandedTreeList = expandedTreeList)
            //   this.$refs.typeTree.setCurrentKey(this.currentTreeActiveId, true, false)
            // })

            resolve()
          } else {
            this.$platform.notification({
              title: res.message,
              type: 'error',
            });
            reject();
          }
        } catch (err) {
          console.error(err);
        }
      })
    },
    clearSelection() {
      this.$refs.list && (this.$refs.list.checkList = [])
      this.selection = []
    }, 
    setpageNum(){
      this.params.pageNum = 1;
    },
    // 获取文档库列表，将ListSearch、ListFooter组件传递的参数合并
    async search(params, { flag = false, type, searchType } = {}) {
      if (params) Object.assign(this.params, params);
      if (flag) {
        localStorage.setItem('wiki_pageSize', this.params.pageSize);
      }
      try {
        this.clearSelection()
        let fn = RepositoryApi.getDocumentList
        if (type === 'collection') {
          fn = RepositoryApi.getAllMyCollection
        } else if (type === 'record') {
          fn = RepositoryApi.getBrowseRecord
        }
        let para = this.changeShowType == 1 ? {...this.params, ...this.builderIntelligentTagsSearchParams()} : { ...this.params }
        // 如果只是在mounted中初始化，则执行该逻辑
        if (this.isInit) {
          para = {
            ...para,
            view: ''
          }
        }

        para.flag = false
        if (['quickSearch', 'powerfulSearch'].includes(searchType)) {
          para.wikiTypeId = para.wikiTypeId
        }
        if (para.label === '') {
          delete para.label
        }
        this.listLoading = true
        let res = await fn(para);

        if (res.success) {
          if(['collection', 'record'].includes(type)) {
            let list = res.result.wiki || res.result.wikiList || []
            list.forEach(v => {
              if(v.content && v.content.includes('<')) {
                v.content = this.transHtmlToText(v.content)
              }
            })
            res.result.pageInfo.list = res.result.wiki || res.result.wikiList || []
            res.result = res.result.pageInfo
          }
          this.dealData(res)
        } else {
          this.$platform.notification({
            title: res.message,
            type: 'error',
          });
          this.dealData({
            list: [],
            total: 0
          })
        }
      } catch (err) {
        console.error(err);
      }
      this.listLoading = false
    },
    transHtmlToText(htmlText) {
      let div = document.createElement('div')
      div.innerHTML = htmlText
      return div.innerText
    },
    dealData(res) {
      if (this.$refs.list) this.$refs.list.resetScrollTop();
      this.listTotal = res.result.total;
      if (res.result.list.length == 1 && res.result.nextPage == 0) {
        res.result.list[0].isLast = true;
      }

      res.result.list.forEach((item) => {
        if(typeof item.label === 'string') {
          item.label = item.label ? JSON.parse(item.label) : []
        }
        // 标题匹配关键字
        if (this.ifSearchInPublished === 0 && item.title.indexOf('<em>') != -1) {
          let replaceReg = new RegExp('<em>', 'g');
          item.handleTitle = item.title.replace(
            replaceReg,
            '<span style="color: #FF7B00">'
          );
          let reg = new RegExp('</em>', 'g');
          item.handleTitle = item.handleTitle.replace(reg, '</span>');
        }else if (this.ifSearchInPublished === 0 && this.keyword && item.handleTitle?.indexOf(this.keyword) != -1) {
          // 标题匹配关键字,keyword直接匹配
          let replaceReg = new RegExp(this.keyword, 'g');
          item.handleTitle = item.title?.replaceAll(
            replaceReg,
            `<span style="color: #FF7B00">${this.keyword}</span>`
          );
        } else {
          item.handleTitle = item.title;
        }
        // 清除富文本自带样式
        item.handleContent = item.content.replace(/<(style|script|iframe)[^>]*?>[\s\S]+?<\/\1\s*>/gi, '')
          .replace(/<[^>]+?>/g, '')
          .replace(/\f\n\r\t\v+/g, '')
          .replace(/>/g, '');
        // 正文匹配关键字
        if (item.handleContent.indexOf('<em>') != -1) {
          let replaceReg = new RegExp('<em>', 'g');
          item.handleContent = item.handleContent.replace(
            replaceReg,
            '<span style="color: #FF7B00">'
          );
          let reg = new RegExp('</em>', 'g');
          item.handleContent = item.handleContent.replace(reg, '</span>');
        }else if (this.ifSearchInPublished === 1 && this.keyword && item.handleContent.indexOf(this.keyword) != -1) {
          // 正文匹配关键字,keyword直接匹配
          let replaceReg = new RegExp(this.keyword, 'g');
          item.handleContent = item.handleContent.replaceAll(
            replaceReg,
            `<span style="color: #FF7B00">${this.keyword}</span>`
          );
        }
      });
      this.listMsg = res.result;
      // this.toDetail(this.listMsg.list[0]);
    },

    getPageSize() {
      return parseInt(localStorage.getItem('wiki_pageSize')) || 10;
    },

    // 给子组件传过来的tag加上show属性
    setTag(tag) {
      this.tag.name = tag;
      this.tag.show = true;

      this.$refs.consoleError.setTag();
    },

    toDetail(item) {
      if(item) {
        this.info.id = item.id
        openTabForWikiView(item.id)
      } else {
        this.info.id = null
      }
    },

    resetPageNum() {
      this.$refs.listFooter.resetPageNum();
    },
    releaseCircle(e) {
      if (e.id) {
        let data = this.listMsg;
        data.list = this.listMsg.list.map((item) => {
          if (item.id == e.id) {
            item.circleState = e.circleState;
          }
          return item;
        });
        this.$set(this, 'listMsg', data);
      }
    },

    /** 选中一个目录 */
    initTreeItem(item) {
      this.selectedTreeItem = item
    },
    nodeRender(h, node) {
      return <span>{node.tagName}</span>
    },

    getOutsideSearchCount() {
      if (!this.isDefaultSite) return
      let keyword = this.params.label || this.params.keyword
      if (!keyword) {
        this.outsideCount = 0
        return
      }
      let type = ''
      switch (this.currSiteObj.name) {
        case '必应':
          type = 'bing'
          break;
        case '搜狗':
          type = 'sogou'
          break;
        case '360搜索':
          type = '360'
          break;
        default:
          break;
      }
      if(!type) {
        this.outsideCount = 0
      }
      let params = {
        keyword: this.params.label || this.params.keyword,
        type
      }
      RepositoryApi.getOutsideSearchCount(params).then(res => {
        res.code === 0 && (this.outsideCount = res.data)
      })
    },
    onShowNodeLabelTipsHandler(e) {
      if (e.target.offsetWidth < 200) {
        this.nodeLabelEllipsis = false;
      } else {
        this.nodeLabelEllipsis = true;
      }
    },
    // async fetchIntelligentGroupTagsList () {
    //   const res = await this.filterTagsPanelBindAttr.remoteFetchFun({pageSize: 999999})
    //   const intelligentTagsListCount = getTagsGroupToTagsList(res.list).filter(item => item).length || 0
    //   const intelligentTagsList = res.list.map(item=> {
    //     item.type = 'intelligentTags'
    //     // item.labelList.filter(item => { item.type = 'intelligentTags', item.tagType = 'intelligentTagItem' })
    //     item.labelList.filter(item => { item.type = 0, item.tagType = 'intelligentTagItem' })
    //     return {
    //         subTypes: Reflect.has(item, 'groupName') ? (item.labelList ?? []) : [],
    //         name: item.groupName,
    //         type: 0,
    //         tagType: 'intelligentTagsGroup'
    //       }
    //   })
    //   this.treeData.splice(this.treeData.length - 2, 0, {
    //     count: intelligentTagsListCount,
    //     id: 'intelligentTags',
    //     name: '标签筛选',
    //     subTypes: intelligentTagsList,
    //     type: 0,
    //     tagType: 'intelligentTagsList'

    //   })
    // },
    getCurrentTreeActive() {
      let currentTreeActive = this.treeData.find(tree => tree.id === 'published')
      //监听发起审批返回到未发布状态
      if(storageGet('isCreateApprove') == 'true'){
        currentTreeActive = this.treeData.find(tree => tree.id === 'unpublished');
        this.params.view = 'unpublished';
        storageSet('isCreateApprove', '');
      }
      return currentTreeActive
    }
  },
  created() {
    this.initIntelligentTagsParams('WIKI')
  },
  async mounted() {
    this.isInit = true;
    this.currentTreeActive = this.getCurrentTreeActive();
    this.shareEnable = await this.getWikiConfig();
    this.getTypes(false)
    this.getFaultTypes(false)
    this.getAllWikiOutsideSearch()
    this.search();

    this.tenantId = rootWindowInitData?.user?.tenantId

    // 故障库灰度
    if(!this.isFaultGrayscale){
      let index = this.treeData.findIndex(item=>item.id === 'faultlibrary')
      this.treeData.splice(index,1)
    }
    // this.fetchIntelligentGroupTagsList()
    // window.__exports__refresh = () => {}
    window.__exports__refresh = async () => {
      this.initCount()
      this.search()
    }
    this.isInit = false;
  },
};
</script>

<style lang="scss">
.FaultTip{
  width: 100%;
  background: $color-primary-light-1;
  padding:8px 16px;
  color: #8C8C8C;
  height: 56px;
  background-clip: content-box;
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.document-list-view {
  .list-search {
    position: relative;
    z-index: 99;
    height: 56px;
    background: #f8f8f8;
    border-bottom: 1px solid #e8eff0;
  }

  .document-list-bottom {
    display: flex;
    // height: calc(100vh - 76px);
    border-radius: 4px;
    height: 100%;

    .list {
      background: #fff;
      flex: 1;
      padding-top: 14px;
    }

    .empty {
      text-align: center;
      padding-top: 100px;

      .empty-img {
        width: 160px;
        height: 160px;
      }

      .empty-msg {
        display: block;
        padding-top: 8px;
        font-size: $font-size-base;
        color: $text-color-regular;
      }
    }

    .list-footer {
      margin-top: 3px;
      padding: 12px 16px 12px;
      text-align: right;
    }
  }
  .document-list-bottom-nav + .document-list-bottom {
    height: calc(100% - 52px);
  }
}

.search-cascader-panel {
  .el-cascader-menu__item,
  .is-active {
    line-height: 18px;
    display: flex;
    justify-content: space-between;

    & > span > .icon-qingkongshanchu {
      opacity: 0;
    }

    &:hover > span > .icon-qingkongshanchu {
      opacity: 1;
    }

    & > span > .icon-bianji {
      opacity: 0;
    }

    &:hover > span > .icon-bianji {
      opacity: 1;
    }

    .icon-bianji {
      margin-right: 5px;
      &:hover {
        color: #38a6a6;
      }
    }

    .icon-qingkongshanchu {
      margin-right: 15px;
      &:hover {
        color: #38a6a6;
      }
    }
  }
}
</style>
<style lang="scss" scoped>
  .search-input-container .search-type ::v-deep .el-input__inner:hover,
  .search-input-container .search-type ::v-deep .el-input__inner:focus,
  .search-input-container .search-type ::v-deep .el-input__inner{
    border-color: gainsboro;
  }


  .document-list-view {
    padding: 10px;
    height: 100vh;
    display: flex;
    flex-direction: row;
    .document-list-view-left {
      min-width: 296px;
      width: 296px;
      margin-right: 12px;
      position: relative;
      background: #fff;
      border-radius: 4px;
      border-start-end-radius: 0;
      &.is-closed {
        width: 0;
        min-width: auto;
        ::v-deep .typeTree {
          padding: 0;
        }
      }
      ::v-deep .el-tree {
        height: calc(100% - 48px);
        overflow: auto;
        z-index: 2;
      }

    }
    .document-list-view-right {
      display: flex;
      flex-direction: column;
      height: 100%;
      overflow: hidden;
      flex: 1;
    }
    .document-list-bottom-box {
      height: calc(100vh - 96px);
      margin-top: 12px;
      display: flex;
      flex-direction: column;
      background: #fff;
      border-radius: 4px;
      overflow: hidden;
    }
    .document-list-bottom {
      width: 100%;
      flex-direction: column;
      overflow: hidden;
      .document-list-bottom-operation {
        display: flex;
        justify-content: space-between;
        padding: 12px 16px 0;
        background: #fff;
        .document-list-bottom-right {
          flex-shrink: 0;
        }
      }
    }
  }
  .document-list-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #fff;
    padding: 16px;
    border-radius: 4px;
    .search-input-container {
      font-size: 0;
      white-space: nowrap;
      ::v-deep .el-input {
        // width: 297px;
        vertical-align: middle;
        // input {
        //   border-radius: 4px 0 0 4px;
        // }
      }
      ::v-deep .el-button {
        margin-left: 0;
        vertical-align: middle;
        border-radius: 0 4px 4px 0;
      }
    }
  }
  .document-list-footer {
    height: auto;
  }

  .addNew {
    display: inline-block;
    ::v-deep .el-input__inner {
      height: 26px;
      line-height: 26px;
      font-size: 14px;
    }
  }
  .document-list-bottom-nav {
    display: flex;
    background: #fff;
    height: 52px;
    padding: 0 16px;
    &-tab {
      font-size: 14px;
      font-weight: 400;
      line-height: 52px;
      text-align: center;
      position: relative;
      color: #111F2C;
      cursor: pointer;
      white-space: nowrap;
      margin-right: 40px;
      &.active {
        color: $color-primary-light-6;
        &:before {
          content: '';
          width: 100%;
          height: 2px;
          background: $color-primary-light-6;
          position: absolute;
          bottom: -1px;
          left: 0;
        }
      }
    }
  }
  .outside-box {
    flex: 1;
    position: relative;
    .site-btns {
      padding: 12px 0 12px 16px;
      background: #fff;
      border-bottom: 1px solid #E8E8E8;
    }
    .outsite-show {
      width: 100%;
      height: 100%;
      background: #fff;
    }
  }

  .advanced-search-visible-btn {
    font-size: 14px;
    line-height: 32px;
    @include fontColor();
    border-color: $color-primary;
    color: $color-primary;
    background: #fff;
    padding: 0 13px;
    white-space: nowrap;
    i{
      font-size: 16px;
    }
    &:hover {
      cursor: pointer;
    }
  }

  ::v-deep .el-tree {
    .el-tree-node__content {
      height: 40px;
      line-height: 40px;
      &:hover {
        background: $color-primary-light-1;
      }
    }
    .el-tree-node:focus > .el-tree-node__content {
      background: #fff;
    }
    .el-tree-node.is-current > .el-tree-node__content {
      background: $color-primary-light-1;
    }
  }
  ::v-deep .typeTree {
    padding: 0 0 12px 16px;
    .el-tree-node__content {
      padding: 0 16px;
    }
    .el-tree-node__content>.el-tree-node__expand-icon:not(.is-leaf) {
      color: #595959;
    }
    .custom-tree-node {
      display: flex;
      align-items: center;
      flex: 1;
      overflow: hidden;
      .node-class {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        color: #262626;
        font-size: 0;
        height: 40px;
        line-height: 40px;
        span {
          margin-left: 4px;
          font-size: 14px;
        }
      }
    }
    .tree-node-expand-btn {
      display: block;
      height: 100%;
      .iconfont:before {
        font-size: 18px;
      }
      .icon-add2 {
        color: #595959;
      }
    }


    .el-tree-node>.el-tree-node__children {
      overflow: visible;
    }

    .el-tree-node:hover > .el-tree-node__content {
      width: calc(100% + 16px);
      margin-left: -16px;
      &>.el-tree-node__expand-icon {
        margin-left: 16px;
      }
    }
    .el-tree-node.is-current > .el-tree-node__content {
      width: calc(100% + 16px);
      margin-left: -16px;
      &>.el-tree-node__expand-icon {
        margin-left: 16px;
      }
    }
  }
  .newTag {
    width: 36px;
    height: 18px;
    background: rgba(255, 146, 0, 0.16);
    border-radius: 11px;
    line-height: 18px;
    font-size: 12px;
    color: #FF9200;
    text-align: center;
    &>span {
      transform: scale(0.8);
      display: block;
    }
  }

  .closeLeft {
    position: absolute;
    z-index: 0;
    right: -11px;
    top: -1px;
    width: 11px;
    height: 48px;
    background: #FFFFFF;
    border: 1px solid #e5e5e5;
    border-left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #595959;
    cursor: pointer;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    &.is-closed {
      transform: translateX(-4px);
      width: 16px;
    }

    &:hover {
      background-color: $color-primary-light-1;
      border-color: $color-primary-light-2;
      .iconfont {
        color: $color-primary-light-6;
      }
    }
  }

  ::v-deep .el-tree-node__content {
    .tree-item-more {
      display: none;
      .iconfont {
        font-size: 18px;
      }
    }
    .tree-item-add {
      display: none;
    }
    &:hover {
      .tree-item-more {
        display: block;
      }
      .tree-item-add {
        display: block;
      }
    }
  }

  .operation-btns {
    font-size: 0;
    display: flex;
    flex-wrap: wrap;
    gap: 16px 0px;
    // &>* {
    //   margin-right: 12px;
    // }
    .add-btn {
      i {
        margin-right: 8px;
      }
    }
    .el-dropdown {
      margin-left: 12px;
    }
  }

  .nav-bottom-hr {
    margin: 0 16px;
    border-top: 0;
    background: #fff;
    border-bottom: 1px solid #E8E8E8;
  }


  .search-sort {
    position: relative;
    min-width: 108px;
    .iconfont {
      position: absolute;
      left: 15px;
      top: 50%;
      transform: translateY(-50%);
      z-index: 10;
      font-size: 14px;
      color: #595959;
    }
    ::v-deep .el-input__inner {
      padding: 0 12px;
      background: #F5F8FA;
      color: #595959 !important;
      border: 1px solid #CBD6E2;
    }
    ::v-deep .el-input__suffix {
      // display: none;
      right: 9px;
      i {
        color: #595959;
      }
    }
  }

  ::v-deep .search-input-container {
    .search-input {
      width: 257px;
      input {
        border-radius: 0;
      }
    }
    .search-type {
      vertical-align: middle;
      .el-input {
        width: 170px;
        height: 32px;
        input {
          border-right: 0;
          border-radius: 4px 0 0 4px;
        }
      }
      &.is-hidden {
        &+.search-input {
          width: 297px;
          input {
            border-radius: 4px 0 0 4px;
          }
        }
      }
    }
  }
  .document-list-bottom-left {
    display: flex;
    align-items: center;
    .countTips {
      font-size: 14px;
      font-weight: 400;
      color: #595959;
      line-height: 20px;
      margin-right: 24px;
    }
  }

  .main-title {
    font-size: 0;
    line-height: 40px;
    margin-top: 8px;
    margin-bottom: 0;
    padding-left: 12px;
    .iconfont {
      font-size: 18px;
      color: $color-primary-light-6;
      vertical-align: middle;
      margin-right: 8px;
    }
    span {
      vertical-align: middle;
      font-size: 16px;
      font-weight: 400;
      color: #262626;
      line-height: 22px;
    }
  }

.batch-import-tip {
  padding: 10px;
}

.batch-import-attachment-tip {
  padding-bottom: 10px;
  a {
    color: $color-primary;
  }
}

.batch-update-attachment-tip {
  padding-bottom: 10px;
  a {
    color: $color-primary;
  }
}
.document-list-bottom-right {
  display: inline-flex;
  gap: 14px;
  align-items: center;
}
.in-tag-c + .tree-node-expand {
  display: none;
}
</style>

<style lang="scss" scoped>
.document-list-view-left {
  display: flex;
  flex-direction: column;
  .doc-button {
    padding: 16px 16px 0;
    
    ::v-deep .el-radio-group {
      display: flex;
      width: 100%;
    }
    ::v-deep .el-radio-button {
      flex: 1;
    }
    ::v-deep .el-radio-button__inner {
      width: 100%;
    }
  }

  .doc-knowledge {
    height: 100%;
  }
  &.is-closed {
    ::v-deep .biz-intelligent-tags__filter-panel {
      display: none;
    }
  }
    // 标签
  ::v-deep .biz-intelligent-tags__filter-panel {
    width: 100%;
    height: 100%;
    padding: 12px 16px 0;
    transition: transform 0s !important;
    transform: none;
  }
  ::v-deep .biz-intelligent-tags__filter-panel-title {
    display: none;
  }
}
</style>