<template>
  <div class="document-list-detail document-list-detail-share" :style="{height: height}" >
    <!-- 详情头部 -->
    <div class="detail-top width1200">

      <div class="author">
        <img class="author-img" :src="initData.createUserHead" v-if="initData.createUserHead">
        <img class="author-img" :src="avatarImage" v-else>
        <div class="author-info">
          <p class="name">{{initData.createUserName}}</p>
          <p class="time">发布于：{{initData.createTime | fmt_datehour}}</p>
        </div>
      </div>

      <div class="operating">
        
        <div class="published">
          <span class="readNum">
            <i class="iconfont icon-yanjing"></i>
            <span>{{initData.readTimes}}</span>
          </span>
        </div>
      
      </div>

    </div>

    <!-- 文章详情 -->
    <div class="detail-content width1200" :style="{padding: padding}">

      <div class="info">
        <p class="title">{{initData.title}}</p>
        <div class="ql-container ql-snow content" style="border:none">
          <template v-if="initData.faultPerformance">
          <div class="ql-editor" style="padding-bottom: 0;"><label>适用产品类型：</label><span>{{ initData.productTypeList && initData.productTypeList.map(item=>item.hierarchyName).join(',') }}</span></div>
          <div class="ql-editor" style="padding-bottom: 0;"><label>故障现象：</label><span>{{initData.faultPerformance}}</span></div>
          <div class="ql-editor" style="padding-bottom: 0;"><label>故障原因：</label><span>{{initData.faultReason}}</span></div>
          <div class="ql-editor" style="padding-bottom: 0;"><label>维修预估费用(元)：</label><span v-if="initData.repairCostBegin || initData.repairCostEnd">{{initData.repairCostBegin}} <span v-if="initData.repairCostBegin && initData.repairCostEnd">~</span> {{initData.repairCostEnd}}</span></div>
          <div class="ql-editor" style="padding-bottom: 0;"><label>解决方案：</label></div>
           </template>
          <div class="ql-editor">
            <!-- 附件类文档预览 -->
            <div class="wiki-attachment" v-if="importAttachment.length">
              <preview-file :info="importAttachment[0]" />
            </div>
            <div v-else v-html="initData.content" @click="wikiClick($event)"></div>
          </div>
        </div>
      </div>
      <!-- 详情页脚部分 -->
      <div class="footer" v-if="initData.attachment && initData.attachment.length > 0">
        <!-- 要用到组件的预览方法 -->
        <base-file-item ref="baseFileItem" style="display: none;" :source="initData.attachment" :key="initData.attachment[0].id" :file="initData.attachment[0]" :fileDownloadAuth="allowDownload" size="small"></base-file-item>
        <div class="file-list">
          <div class="file-item" v-for="file in initData.attachment" :key="file.id">
            <img :src="findFileIconUrl(file.filename)" class="mar-r-8" />
            <div class="file-info">
              <div class="file-name" @click="previewFile(file)">{{ file.filename }}</div>
              <div class="file-size">{{ file.fileSize }}</div>
            </div>
            <i class="iconfont icon-download" v-if="allowDownload" @click="downloadFile(file)"></i>
          </div>
        </div>

      </div>
    </div>

    <comment-box
      class="width1200"
      v-if="initData.commentList && initData.commentList.length"
      :comment-list-parent="initData.commentList"
      :use-for-share="true"
      :init-data="initData"
      :title="initData.title">
    </comment-box>
  </div>
</template>

<script>
import PreviewFile from '@src/modules/repository/documentV2/detail/component/PreviewFile.vue'
import { findFileIconUrl } from '@src/modules/ai/views/edit/mock.ts'
import { formatDate } from 'pub-bbx-utils'
/* mixin */
import ThemeMixin from '@src/mixins/themeMixin/index.ts'

import commentBox from '../detail/component/comment'
import { getOssUrl } from '@src/util/assets'
import * as settingApi from "@src/api/SettingApi";
const avatarImage = getOssUrl('/avatar.png')
export default {
  name: 'document-detail',
  components: {
    [commentBox.name]: commentBox,
    [PreviewFile.name]: PreviewFile,
  },
  mixins: [ThemeMixin],
  props: {
    initData: {
      type: Object,
      default: () => ({})
    }
  },
  data(){
    return{
      avatarImage,
    }
  },
  computed: {
    // 是否开启知识库2.0灰度
    haveWikiV2Gray() {
      return this.initData?.WIKI_V2
    },
    // 导入文档的附件内容
    importAttachment() {
      return this.initData?.importAttachment || []
    },
    // 标签展示
    labelList() {
      return this.initData?.labelList || []
    },
    wikiDownloadAuth() {
      return this.initData?.tenantConfigs?.find(item => item.configCode === 'wikiDownloadAuth')?.isOpen == 1
    },
    // 允许下载
    allowDownload() {
      const allowDownload = this.initData?.baseWikiSetting ? this.initData?.baseWikiSetting?.externalSharing?.allowDownload : true
      return this.wikiDownloadAuth && allowDownload
    },
    height () {
      return this.showOpenFrame ? '100%' : '100vh';
    },

    padding () {
      return document.body.clientWidth < 550 ? '0 20px 20px' : '0 100px 50px';
      // return this.showOpenFrame ? '0 50px 50px' : '0 100px 50px';
    }
  },
  methods: {
    findFileIconUrl,
    // 附件类文档下载
    downloadFile(info) {
      if (!info) return
      let a = document.createElement('a');
      a.href = `/files/download?fileId=${info.id}`;
      a.download = info.originFileName ?? info.filename ?? info.name ?? this.$t('common.base.preview');
      a.click();
      window.URL.revokeObjectURL(a);
    },
    previewFile(file) {
      this.$refs.baseFileItem.handlePreviewFile(file)
    },
    wikiClick(e) {
      if(/^img$/i.test(e.target.nodeName)) {
        const img = e.target.currentSrc
        this.$previewElementImg(img, [img]);
      }
    },
  },
}
</script>

<style lang="scss">
.document-list-detail {
  // display: flex;
  flex-direction: column;
  overflow: auto;
  
  background: #fff;
  font-size: 14px;
  color: #333;

  .detail-top {
    display: flex;
    justify-content: space-between;
    height: 75px;
    padding: 16px;
    border-bottom: 1px solid #E8EFF0;
    box-sizing: border-box;

    .author {
      font-size: 0;

      .author-img {
        vertical-align: middle;
        display: inline-block;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 15px;
      }
      
      .author-info {
        vertical-align: middle;
        display: inline-block;

        .name {
          font-size: 16px;
          margin: 0 0 4px 0;
        }

        .time {
          font-size: 12px;
          color: #909399;
          margin: 0;
        }
      }
    }

    .operating {
      line-height: 42px;

      .published {
        display: inline-block;
        // margin-right: 10px;
      }

      .draftBox {
        display: inline-block;
        margin-right: 10px;
      }

      .icon-permission {
        font-size: 14px;
        color: #B0BCC3;
        margin-right: 3px;
      }

      .readNum {
        margin: 0 10px;

        .icon-yanjing {
          font-size: 12px;
          color: #B0BCC3;
          margin-left: 8px;
        }

        span {
          display: inline-block;
          padding-left: 5px;
        }
      }

      .management {

        .icon-edit {
          display: inline-block;
          width: 25px;
          height: 25px;
          font-size: 14px;
          @include fontColorPrimaryLight5();
        }

        .icon-delete {
          display: inline-block;
          width: 25px;
          height: 25px;
          font-size: 14px;
          @include fontColorPrimaryLight5();
        }
      }

      
      .icon-operating {
        display: inline-block;
        width: 30px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        font-size: 16px;
        @include fontColorPrimaryLight5();

        cursor: pointer;
      }

      .share {
        display: inline-block;

        width: 20px;
        height: 20px;
        line-height: 20px;

        cursor: pointer;

        .icon-article-share {
          font-size: 16px;
          color: $color-primary-light-6;
        }
      }

      .open {
        display: inline-block;
        height: 30px;
        line-height: 30px;
        margin-left: 10px;
        color: $color-primary-light-6;
        
        cursor: pointer;
      }

      .white-btn {
        background: #fff;
        color: #333;
        border: 1px solid #E2E2E2;

        &:hover {
          @include borderColorPrimaryLight5();
          @include backgroundColorPrimaryLight1();
          color: #fff;
        }
      }
    }
  }

  .detail-content {
    flex: 1;
    overflow: auto;

    .info {

      .title {
        margin: 0;
        padding: 35px 0;
        text-align: center;
        font-size: 32px;
      }

      .content {
        font-size: 16px;
        line-height: 30px;
        padding-bottom: 30px;

        word-break: break-all;

        p > img {
          max-width: 100%;
        }
      }
    }

    .footer {
      padding: 12px 0;
      margin-bottom: 16px;
      border-top: 1px solid #E4E7ED;

      .tags {
        // display: inline-block;
        vertical-align: top;
        font-size: 0;

        .icon-tags {
          vertical-align: middle;
          font-size: 16px;
          color: #B0BCC3;
        }

        .detail-tag {
          vertical-align: middle;
          max-width: 76px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;

          margin-left: 4px;
          border: none;
          background: #E8EFF0;
          color: #606266;
        }
      }

      .file-list {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
      }

      .file-item {
        width: 240px;
        padding: 8px;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        position: relative;
        border-radius: 4px;
        background: #F0F2F5;
        &:last-child {
          margin-right: 0;
        }
        &:hover {
          .file-name {
            color: $color-primary;
          }
          .iconfont {
            display: block;
          }
        }
        img {
          width: 28px;
          height: 28px;
        }
        .file-info {
          flex: 1;
          overflow: hidden;
          .file-name {
            @include text-ellipsis();
            font-size: 12px;
            line-height: 16px;
            cursor: pointer;
          }
          .file-size {
            font-size: 10px;
            line-height: 12px;
            color: #8C8C8C;
          }
        }
        .iconfont {
          color: #8c8c8c;
          cursor: pointer;
          display: none;
        }
      }

      .dividing-line {
        // display: inline-block;
        height: 22px;
        width: 1px;
        background: #848E92;
        margin: 0 20px;
        vertical-align: top;
      }

      .annex {
        vertical-align: top;
        display: inline-block;
        font-size: 0;

        .annex-left {
          vertical-align: top;
          display: inline-block;
          font-size: 14px;
          line-height: 35px;
          padding: 8px 0;
        }

        .annex-right {
          vertical-align: top;
          display: inline-block;
          padding: 8px;

          .base-file__preview {
            max-width: calc(100vw - 130px);
          }

          a {
            color: #333;
            line-height: 20px !important;
            font-size: 14px;
            display: inline-block;
            text-decoration: none;
            background-color: transparent;

            &:hover {
              color: #55B7B4;
              text-decoration: underline;
            }
          }

          .annex-item {
            font-size: 14px;
          }
        }
      }
    }
  }
  .width1200 {
    max-width: 1200px;
    margin: 0 auto;
  }
}

.base-file-del {
  display: none;
}
.document-list-detail-share {
  .comment-box {
    padding: 16px;
  }
}
.ql-editor{
  video,img{
    max-width: 100% !important;
    height: auto !important;
  }
  div, p, span {
    white-space: inherit !important;
  }
}
</style>