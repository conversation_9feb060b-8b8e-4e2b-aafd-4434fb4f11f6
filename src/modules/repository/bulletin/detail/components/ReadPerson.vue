<template>
  <div class="person">
    <div class="read-person" v-if="reads.length > 0">
      <span class="title">{{ $t('common.base.haveRead') }}</span>
      <div class="read-img">
        <el-tooltip placement="top" v-for="(item, index) in reads.slice(0, 5)" :key="index">
          <div slot="content">
            <template v-if="isOpenData">
              <open-data type="userName" :openid="item.staffId"></open-data>
            </template>
            <template v-else>
              {{ item.displayName }}
            </template>
          </div>
          <span class="task-pointer person-img" v-user="item.userId">
            <img :src="getUserHead(item.head)" />
          </span>
        </el-tooltip>
        <el-popover v-if="reads.length > 5" popper-class="more-person" trigger="click" width="234">
          <div slot="reference" class="more-point">···</div>
          <div class="see-more">
            <el-tooltip
              :content="item.displayName"
              placement="top"
              v-for="(item, index) in reads.slice(5)"
              :key="index"
            >
              <span class="task-pointer person-img" v-user="item.userId" :key="index">
                <img :src="getUserHead(item.head)" />
              </span>
            </el-tooltip>
          </div>
        </el-popover>
      </div>
    </div>

    <div class="read-person" v-if="unreads.length > 0">
      <span class="title right" :style="{ marginLeft: marginLeft }">{{ $t('common.base.notRead') }}</span>
      <div class="read-img">
        <el-tooltip placement="top" v-for="(item, index) in unreads.slice(0, 5)" :key="index">
          <div slot="content">
            <template v-if="isOpenData">
              <open-data type="userName" :openid="item.staffId"></open-data>
            </template>
            <template v-else>
              {{ item.displayName }}
            </template>
          </div>
          <span class="task-pointer person-img" v-user="item.userId">
            <img :src="getUserHead(item.head)" />
          </span>
        </el-tooltip>
        <el-popover v-if="unreads.length > 5" popper-class="more-person" trigger="click" width="234">
          <div slot="reference" class="more-point">···</div>
          <div class="see-more">
            <el-tooltip
              :content="item.displayName"
              placement="top"
              v-for="(item, index) in unreads.slice(5)"
              :key="index"
            >
              <span class="task-pointer person-img" v-user="item.userId" :key="index">
                <img :src="getUserHead(item.head)" />
              </span>
            </el-tooltip>
          </div>
        </el-popover>
      </div>
    </div>
  </div>
</template>

<script>
import { isOpenData } from '@src/util/platform'
import { getOssUrl } from '@src/util/assets'
const defaultAvatar = getOssUrl('/avatar.png')

export default {
  name: 'read-person',
  props: {
    reads: {
      type: Array,
      default: () => [],
    },
    unreads: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      isOpenData,
    }
  },
  computed: {
    marginLeft() {
      return this.reads.length > 0 ? '20px' : '0'
    },
  },
  methods: {
    getUserHead(src) {
      if (!src) return defaultAvatar
      return src
    },
  }
}
</script>

<style lang="scss" scoped>
.person {
  display: flex;

  .read-person {
    flex: 1;

    .title {
      vertical-align: top;
      display: inline-block;

      font-size: 15px;
      line-height: 32px;

      margin-right: 20px;
    }

    .read-img {
      display: inline-block;
      width: calc(100% - 75px);
    }

    .right {
      margin-left: 20px;
    }
  }
}
.person-img {
  display: inline-block;
  width: 32px;
  height: 32px;
  margin-right: 8px;
  margin-bottom: 5px;
  img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
}
.more-point {
  position: relative;
  vertical-align: middle;
  display: inline-block;
  width: 32px;
  height: 32px;

  font-size: 12px;
  line-height: 32px;
  text-align: center;
  color: #696974;

  margin-bottom: 5px;
  border-radius: 50%;
  border: 1px solid #e2e2ea;

  cursor: pointer;
}
.more-person {
  max-height: 300px;
  overflow: auto;
}
.more-person {
  .see-more {
    .person-img {
      margin-right: 10px;
      margin-bottom: 8px;
      &:nth-child(5n) {
        margin-right: 0;
      }
    }
  }
}
</style>
