<template>
  <div
    class="bulletin-list-detail"
    ref="bulletinDetail"
    :style="{height: height}"
    v-if="detailShow"

  >
    <template v-if="detail && detail.title">
      <!-- 详情头部 -->
      <div class="detail-top">
        <div class="author">
          <span class="task-pointer" v-user="detail.createUser">
            <img class="author-img" :src="detail.createUserHead" v-if="detail.createUserHead" />
            <img class="author-img" :src="repositoryImage1" v-else />
          </span>
          <div class="author-info">
            <p class="name">
              <template v-if="isOpenData">
                <open-data type="userName" :openid="detail.createUserStaffId"></open-data>
              </template>
              <template v-else>
                {{detail.createUserName}}
              </template>
            </p>
            <p class="time">{{$t('wiki.bulletin.detail.des1')}}：{{detail.createTime | fmt_datehour}}</p>
          </div>
        </div>

        <div class="operating">
          <!-- <div class="common">
            <span class="item-num">已读（{{detail.readNum}}）</span>
            <span class="item-num">未读（{{detail.unreadNum}}）</span>
          </div>-->

          <span class="management" v-if="allowDelete">
            <i
              class="iconfont icon-delete icon-operating"
              @click="deleteArticle();trackEventHandler('delete')"
            ></i>
          </span>

          <!--          <span
            class="open"
            v-if="allowEdit && linkControl && isShowSelfServicePortal"
            @click="changeRelease();trackEventHandler('share')"
          >
            <i class="iconfont icon-quanziguanli icon-article-share" style="margin-right:4px"></i>
            {{detail.circleState == 1 ? '取消发布' : '发布到圈子'}}
          </span>-->

          <span class="open" @click="openFrame" v-if="isList">{{$t('common.base.openInNewPage')}}</span>
        </div>
      </div>

      <!-- 文章详情 -->
      <div class="detail-content" :style="{padding: padding}">
        <div class="info">
          <p class="title">{{detail.title}}</p>
          <div class="ql-container ql-snow content" style="border:none">
            <div class="ql-editor">
              <div v-html="detail.content"></div>
            </div>
          </div>
        </div>
        <!-- 详情页脚部分 -->
        <div
          class="footer"
          v-if="(reads.reads.length > 0 || reads.unreads.length > 0) || (detail.attachment && detail.attachment.length > 0)"
        >
          <!-- 已读、未读人员显示 -->
          <read-person :reads="reads.reads" :unreads="reads.unreads"></read-person>
          <!-- 附件部分 -->
          <div class="annex" v-if="detail.attachment && detail.attachment.length > 0">
            <span class="annex-left">{{$t('common.base.attachment')}}：</span>
            <div class="annex-right">
              <div class="base-comment-attachment base-file__preview">
                <base-file-item
                  :source="detail.attachment"
                  v-for="file in detail.attachment"
                  :key="file.id"
                  :file="file"
                  size="small"
                ></base-file-item>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
  <div v-else class="bulletin-list-detail empty">
    <div>
      <img class="empty-img" :src="noDataImage" />
    </div>
    <span class="empty-msg">{{deleteMsg || $t('common.base.tip.noData')}}</span>
  </div>
</template>

<script>
import { isOpenData, openAccurateTab } from '@src/util/platform'
import * as RepositoryApi from '@src/api/Repository';
import * as Lang from '@src/util/lang/index.js';
import { isShowSelfServicePortal } from '@shb-lib/version'
/* mixin */
import ThemeMixin from '@src/mixins/themeMixin/index.ts'
import { getRootWindow } from '@src/util/dom'
import { PageRoutesTypeEnum } from 'pub-bbx-global/pageType/dist/enum/PageRoutesEnum'
import { getOssUrl } from '@src/util/assets'
const noDataImage = getOssUrl('/no_data.png')
const repositoryImage1 = getOssUrl('/avatar.png')
/* components */
import ReadPerson from '@src/modules/repository/bulletin/detail/components/ReadPerson.vue'
export default {
  name: 'bullet-detail',
  mixins: [ThemeMixin],
  props: {
    info: {
      type: Object,
      default: () => ({}),
    },
    initData: {
      type: Object,
      default: () => ({}),
    },
    infoEdit: {
      type: Object,
      default: () => ({}),
    },
    isList: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      repositoryImage1,
      noDataImage,
      isOpenData,
      allowEdit: this.infoEdit
        ? this.infoEdit.VIP_INFO_NOTICE_CREATE
          && this.infoEdit.VIP_INFO_NOTICE_CREATE == 3
        : this.initData.userInfo.authorities.VIP_INFO_NOTICE_CREATE
          && this.initData.userInfo.authorities.VIP_INFO_NOTICE_CREATE == 3,
      // allowShow: this.infoEdit ? (this.infoEdit.INFO_VIEW ? this.infoEdit.INFO_VIEW : false) : (this.initData.userInfo.authorities.INFO_VIEW ? this.initData.userInfo.authorities.INFO_VIEW : false),
      noticeId: '',
      form: this.buildForm(), // 附件存储格式
      params: {
        noticeId: '',
      },
      detail: {}, // 文章详情
      reads: {
        reads: [],
        unreads: [],
      },
      showMoreRead: false, // 是否显示已读人员浮框
      showMoreNoRead: false, // 是否显示未读人员浮框
      loading: false,
      deleteMsg: null,
      detailShow: true,
    };
  },
  components: {
    ReadPerson,
  },
  mounted() {
    this.getId();
  },
  methods: {
    getId() {
      if (window.location.href.indexOf('?') != -1) {
        let array = window.location.href.split('?')[1].split('&');
        let params = [];

        array.forEach((item) => {
          params.push({ name: item.split('=')[0], value: item.split('=')[1] });
        });
        params.forEach((item) => {
          if (item.name == 'noticeId') {
            this.noticeId = item.value;
          }
        });
        // if(!this.allowEdit && !this.allowShow) {
        //   this.detailShow = false;
        //   this.deleteMsg = '您没有权限查看该页面';
        //   return;
        // }
        this.getBulletinDetail();
        this.getReadPerson();
        this.getUnreadPerson();
      }
    },
    // 获取通知公告详情
    async getBulletinDetail() {
      try {
        this.detailShow = true;
        this.showMoreNoRead = false;
        this.showMoreRead = false;
        this.params.noticeId = this.info.id
          ? this.info.id
          : this.noticeId
            ? this.noticeId
            : null;
        if (!this.params.noticeId) {
          this.detail = null;
          this.detailShow = false;
          return;
        }

        this.loading = true;
        let res = await RepositoryApi.getBulletinDetail(this.params);
        this.loading = false;

        if (res.success) {
          if (res.message == '已删除') {
            this.detail = null;
            this.deleteMsg = this.$t('common.base.tip.haveDelete');
            this.detailShow = false;
          } else {
            this.detail = res.result;
            this.detailShow = true;
          }
        } else {
          this.$platform.notification({
            title: res.message,
            type: 'error',
          });
        }
      } catch (err) {
        console.error(err);
        this.loading = false;
      }
    },

    // 获取最近5条已读未读用户
    async getReadOrNotLatest() {
      try {
        let res = await RepositoryApi.getReadOrNotLatest(this.params);
        if (res.success) {
          this.reads.reads = res.result.reads;
          this.reads.unreads = res.result.unreads;
        } else {
          this.$platform.notification({
            title: res.message,
            type: 'error',
          });
        }
      } catch (err) {
        console.error(err);
      }
    },

    // 获取所有已读用户,点击加号时获取
    async getReadPerson() {
      try {
        let res = await RepositoryApi.getReadPerson(this.params);
        if (res.success) {
          this.reads.reads = res.result;
        } else {
          this.$platform.notification({
            title: res.message,
            type: 'error',
          });
        }
      } catch (err) {
        console.error(err);
      }
    },

    // 获取所有未读用户，点击加号时获取
    async getUnreadPerson() {
      try {
        let res = await RepositoryApi.getUnreadPerson(this.params);
        if (res.success) {
          this.reads.unreads = res.result;
        } else {
          this.$platform.notification({
            title: res.message,
            type: 'error',
          });
        }
      } catch (err) {
        console.error(err);
      }
    },

    buildForm() {
      return {
        content: '',
        attachments: [],
        showInOwn: 0,
      };
    },

    // 新页面打开通知公告详情
    openFrame() {
      let fromId = window.frameElement.getAttribute('id');

      // this.$platform.openTab({
      //   id: `bulletin_detail_${this.params.noticeId}`,
      //   title: '信息公告详情',
      //   url: `/info/notice/detail/page?noticeId=${this.params.noticeId}`,
      //   reload: true,
      //   close: true,
      //   fromId,
      // });
      openAccurateTab({
        type: PageRoutesTypeEnum.PageNoticeDetail,
        key: this.params.noticeId,
        params: `noticeId=${this.params.noticeId}`,
        reload: true,
        fromId
      })
    },
    // 删除通知公告
    async deleteArticle() {
      try {
        if (!(await this.$platform.confirm(this.$t('wiki.bulletin.detail.tips1')))) return;
        let res = await RepositoryApi.deleteBulletin(this.params);
        if (res.success) {
          this.$platform.notification({
            title: this.$t('wiki.list.deleteWikiSuccessTips'),
            type: 'success',
          });

          if (!this.isList) {
            let id = window.frameElement.dataset.id;
            this.$platform.closeTab(id);

            let fromId = window.frameElement.getAttribute('id');
            // this.$platform.openTab({
            //   id: 'M_INFO_NOTICE',
            //   title: '信息公告',
            //   url: '/info/notice',
            //   reload: true,
            //   close: true,
            //   fromId,
            // });
            openAccurateTab({
              type: PageRoutesTypeEnum.PageInfoNotice,
              reload: true,
              fromId
            })
          } else {
            if (this.info.isLast) {
              this.$parent.resetPageNum();
            } else {
              this.$emit('search');
            }
          }
        } else {
          this.$platform.notification({
            title: res.message,
            type: 'error',
          });
        }
      } catch (e) {
        console.error(e);
      }
    },

    // TalkingData事件埋点
    trackEventHandler(type) {
      if (type === 'delete') {
        window.TDAPP.onEvent('pc：信息公告详情-删除事件');
        return;
      }
    },
    changeRelease() {
      this.$confirm(
        this.detail.circleState == 1 ? this.$t('wiki.list.list.changeRelease.tips1'): this.$t('wiki.detail.des4'),
        this.$t('common.base.toast'),
        {
          confirmButtonText: this.$t('common.base.confirm'),
        }
      )
        .then(() => {
          RepositoryApi.releaseCircle({
            id: this.detail.id,
            circleState: 1 - this.detail.circleState * 1,
          }).then((res) => {
            if (res.code == 0) {
              this.detail.circleState = 1 - this.detail.circleState * 1;
              this.$message({
                message: this.detail.circleState == 1 ? this.$t('wiki.list.list.changeRelease.tips4') : this.$t('common.base.tip.cancelSuccess'),
                duration: 1500,
                type: 'success',
              });

              this.$emit('releaseCircle', {
                id: this.detail.id,
                circleState: this.detail.circleState * 1,
              });
            } else {
              this.$message({
                message: res.message,
                duration: 1500,
                type: 'error',
              });
            }
          });
        })
        .catch(() => {});
    },
  },
  computed: {
    height() {
      return this.isList ? '100%' : '100vh';
    },

    padding() {
      return this.isList ? '0 50px' : '0 100px';
    },

    marginLeft() {
      return this.reads.reads.length > 0 ? '20px' : '0';
    },

    // 联客商城灰度开关
    linkControl() {
      // return this.initData.openLinkC;
      const RootWindow = getRootWindow(window);
      return this.initData.openLinkC || RootWindow.grayAuth?.portal || false;
    },
    isShowSelfServicePortal() {
      return isShowSelfServicePortal()
    },
    allowDelete() {
      const auth = this.infoEdit || this.initData?.userInfo?.authorities
      return auth?.VIP_INFO_NOTICE_DELETE
    }
  },
};
</script>

<style lang="scss">
.bulletin-list-detail {
  display: flex;
  flex-direction: column;
  overflow: hidden;

  background: #fff;

  .detail-top {
    display: flex;
    justify-content: space-between;
    height: 60px;
    padding: 10px 24px 10px 16px;
    border-bottom: 1px solid #e8eff0;

    .author {
      font-size: 0;

      .author-img {
        vertical-align: middle;
        display: inline-block;
        width: 34px;
        height: 34px;
        border-radius: 50%;
        margin-right: 15px;
      }

      .author-info {
        vertical-align: middle;
        display: inline-block;

        .name {
          font-size: 14px;
          margin-bottom: 4px;
        }

        .time {
          font-size: 12px;
          color: #909399;
          margin: 0;
        }
      }
    }

    .operating {
      line-height: 40px;
      color: $color-primary-light-6 !important;

      .common {
        display: inline-block;
        margin-right: 10px;

        .item-num {
          margin-left: 10px;
        }
      }

      .management {
        .icon-edit {
          display: inline-block;
          width: 25px;
          height: 25px;
          font-size: 14px;
        }

        .icon-delete {
          display: inline-block;
          width: 25px;
          height: 25px;
          font-size: 16px;
        }
      }

      .icon-operating {
        display: inline-block;
        width: 30px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        font-size: 14px;

        cursor: pointer;
      }

      .open {
        display: inline-block;
        height: 30px;
        line-height: 30px;
        margin-left: 10px;
        cursor: pointer;
      }
    }
  }

  .detail-content {
    flex: 1;
    overflow: auto;

    .info {
      .title {
        margin: 0;
        padding: 16px 0;
        text-align: center;
        font-size: 20 px;
      }

      .content {
        font-size: 16px;
        line-height: 30px;
        padding-bottom: 30px;

        word-break: break-all;

        p > img {
          max-width: 100%;
        }
      }
    }

    .footer {
      padding: 12px;
      box-shadow: 0px 2px 8px 0px rgba(144, 171, 184, 0.5);
      border-radius: 4px;
      font-size: 0;
      margin-bottom: 50px;

      .annex {
        vertical-align: top;
        display: inline-block;
        font-size: 0;
        padding-top: 12px;

        .annex-left {
          vertical-align: top;
          display: inline-block;
          font-size: 14px;
          line-height: 35px;
        }

        .annex-right {
          vertical-align: top;
          display: inline-block;

          .annex-item {
            font-size: 14px;
          }

          .base-file-info {
            white-space: inherit;
          }
        }
      }
    }
  }
}

.empty {
  text-align: center;
  padding-top: 100px;
  height: 100vh;

  .empty-img {
    width: 160px;
    height: 160px;
  }

  .empty-msg {
    display: block;
    padding-top: 8px;
    font-size: $font-size-base;
    color: $text-color-regular;
  }
}

.more-person {
  max-height: 300px;
  overflow: auto;
}

.base-file-del {
  display: none;
}
</style>
