<template>
  <div class="recommend-body-wrap" :class="[`${mode}-recommend-body-wrap`, !pattern ? 'card-recommend-body-wrap' : '']">
    <div v-if="!pattern" class="recommend-body" :data-id="item.id" :class="[`${mode}-recommend-body`, currentWikiId == item.id ? 'isActive' : '', isBatchOperateStatus ? 'isBatch' : '']" @click.stop.prevent="toDetail">
      <div class="check-wrap" v-if="isBatchOperateStatus">
        <el-checkbox v-model="item.checked" @change="handleChecked"></el-checkbox>
      </div>
      <div class="recommend-item">
        <div class="recommend-item-title-wrap">
          <img :src="findFileIconUrl(item.importFileType || 'wiki')" class="mar-r-10" />
          <h4 class="recommend-item-title">{{ stripHTML(item.title) }}</h4>
          <span class="approve-status" v-if="item.wikiApproveUsers && item.wikiApproveUsers.length">{{ $t('common.base.underReview') }}</span>
        </div>
        <div class="title-label-tags hidden-text" v-if="item.labelList && item.labelList.length > 0" @click.stop>
          <BizIntelligentTagsView type="detail" :value="item.title || item.handleTitle" :tagsList="item.labelList || []" :config="showIntelligentTagType" />
        </div>
        <div class="recommend-item-text hidden-text" style="border: none">
          <div class="tinymce-editor">
            <!-- 故障库信息 -->
            <div class="recommend-content" v-if="item.wikiType === 1">{{$t('wiki.list.list.label3')}}：{{item.faultPerformance}}；{{$t('wiki.list.list.label4')}}：{{item.faultReason}}；{{$t('wiki.list.list.label5')}}：{{ stripHTML(item.handleContent || item.content) }}。</div>
            <div v-else class="recommend-content">{{stripHTML(item.desc || '')}}{{!item.importFileType ? stripHTML(item.handleContent || item.content) : ''}}</div>
          </div>
          <img v-if="item.imgUrls && item.imgUrls.length" :src="item.imgUrls[0]" class="img-item mar-l-8" :class="!stripHTML(item.desc || '') && !stripHTML(item.handleContent || item.content) ? 'width-100' : ''" />
          <div class="img-preview mar-l-8" v-else-if="stripHTML(item.desc || '') && item.importFileType">
            <img :src="findFileIconUrl(item.importFileType)" />
          </div>
        </div>
        <div class="recommend-item-preview" v-if="stripHTML(item.desc || '') == '' && item.importFileType && !isImage(item.importFileType)">
          <img :src="findFileIconUrl(item.importFileType)" />
        </div>
      </div>

      <div class="status-view status-new">
        <span class="status-view-each-v2">
          <i class="iconfont icon-yuedu"></i>
          <b>{{ formatTimes(item.readTimes || wikiCountData.readTimes || 0) }}</b>
        </span>
        <span class="status-view-each-v2 canClick">
          <i class="iconfont icon-comment"></i>
          <b>{{ formatTimes(item.commentTimes || wikiCountData.commentTimes || 0) }}</b>
        </span>
        <span class="status-view-each-v2 canClick">
          <i
            class="iconfont icon-shoucang"
          ></i>
          <b>{{ formatTimes(item.collectionTimes || wikiCountData.collectionTimes || 0) }}</b>
        </span>
        <span class="status-view-each-v2 canClick">
          <i
            class="iconfont icon-dianzan"
          ></i>
          <b>{{ formatTimes(item.likeTimes || wikiCountData.likeTimes || 0) }}</b>
        </span>
      </div>
    </div>

    <div v-if="pattern" class="recomment-list" :class="`${mode}-recommend-list`"  @click="toDetail">
      <div class="list-left">
        <img style="width: 32px;height: 32px;" :src="findFileIconUrl(item.importFileType || 'wiki')" class="mar-r-10" />
        <div class="list-left-msg">
          <h4 class="recommend-item-title  limit-long">{{ stripHTML(item.title) }}</h4>
          <span class="menu-msg limit-long">{{ item.type }}</span>
        </div>
      </div>


      <div class="status-view status-new">
        <span class="status-view-each-v2">
          <i class="iconfont icon-yuedu"></i>
          <b>{{ formatTimes(item.readTimes || wikiCountData.readTimes || 0) }}</b>
        </span>
        <span class="status-view-each-v2 canClick">
          <i class="iconfont icon-comment"></i>
          <b>{{ formatTimes(item.commentTimes || wikiCountData.commentTimes || 0) }}</b>
        </span>
        <span class="status-view-each-v2 canClick">
          <i
            class="iconfont icon-shoucang"
          ></i>
          <b>{{ formatTimes(item.collectionTimes || wikiCountData.collectionTimes || 0) }}</b>
        </span>
        <span class="status-view-each-v2 canClick">
          <i
            class="iconfont icon-dianzan"
          ></i>
          <b>{{ formatTimes(item.likeTimes || wikiCountData.likeTimes || 0) }}</b>
        </span>
      </div>
    </div>
</div>
</template>
<script>
import { findFileIconUrl } from '@src/modules/ai/views/edit/mock.ts'
import * as RepositoryApi from '@src/api/Repository';
import { isImage } from '@src/modules/im/imChat/utils/tools.js';
export default {
  name: 'recommend-item',
  props: {
    item: {
      type: Object,
      default: () => ({}),
    },
    currentWikiId: {
      type: String,
      default: '',
    },
    mode: {
      type: String,
      default: 'detail',
    },
    // 是否是批量操作状态
    isBatchOperateStatus: {
      type: Boolean,
      default: false,
    },
    pattern:{
      type: Number,
      default: 0,//card
    }
  },
  data() {
    return {
      wikiCountData: {},
      wikiStateData: {},
    };
  },
  computed: {
    showIntelligentTagType() {
      return {
        normalShowType: 'text',
        normalMaxLength: 2,
      };
    },
  },
  mounted() {
    this.getWikiData();
  },
  methods: {
    isImage,
    findFileIconUrl,
    toDetail() {
      if (this.isBatchOperateStatus) {
        this.handleChecked(!this.item.checked);
        return;
      }
      this.$emit('toDetail', this.item);
    },
    handleChecked(value) {
      this.$emit('checked', this.item, value);
    },
    formatTimes(count) {
      if(Number(count) < 0) return 0
      if (count > 999) return '999+'
      return count
    },
    getWikiData() {
      if (this.mode == 'detail') {
        RepositoryApi.getWikiData({
          id: this.item.id,
        }).then(res => {
          if (res.code === 0) {
            this.wikiCountData = res.result?.times || {};
            this.wikiStateData = res.result?.state || {};
          } else {
            this.$platform.notification({
              title: res.message,
              type: 'error',
            });
          }
        });
      }
    },
    stripHTML(val) {
      if (!val) return ''
      val = val.replace(/\s*/g, '');  //去掉空格
      val = val.replace(/<[^>]+>/g, ''); //去掉所有的html标记
      val = val.replace(/↵/g, '');     //去掉所有的↵符号
      val = val.replace(/[\r\n]/g, '') //去掉回车换行
      val = val.replace(/&nbsp;/g, '') //去掉空格
      const arrEntities = {'lt': '<', 'gt': '>', 'nbsp': ' ', 'amp': '&', 'quot': '"'};
      return val.replace(/&(lt|gt|nbsp|amp|quot);/ig, function (all, t) {
        return arrEntities[t];
      }) || '';
    },
  },
};
</script>
<style lang="scss" scoped>
.recomment-list{
  padding: 12px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  overflow: hidden;
  &.detail-recommend-list:hover {
    box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.12);
    .recommend-item-title {
      color: $color-primary-light-6;

    }
  }
  .list-left{
    display: flex;
    flex-direction: row;
    .list-left-msg{
      display: flex;
      flex-direction: column;
      .limit-long{
        flex-shrink: 1;
        max-width: 322px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin: 0;
        cursor: pointer;
      }
      .menu-msg{
        color: #8C8C8C;
        line-height: 16px;
        font-size: 12px;
      }
    }
  }
}
.card-recommend-body-wrap {
  width: calc(50% - 8px);
  min-width: 260px;
}
.recommend-body {
  &-wrap {
    &.list-recommend-body-wrap {
      width: 100%;
    }
    .status-view {
      text-align: center;
      line-height: 20px;
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 12px;
      &-each-v2 {
        color: #595959;
        display: flex;
        align-items: center;
        .iconfont {
          margin-right: 4px;
        }
        b {
          font-weight: normal;
        }
      }
    }
  }
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  cursor: pointer;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #E4E7ED;
  width: 100%;
  padding: 12px;
  gap: 8px;
  height: 194px;
  &.isBatch {
    padding-left: 34px;
  }
  &.isActive {
    background: #e6fffb !important;
  }
  &.list-recommend-body {
    min-width: 300px;
    height: auto;
    min-height: 160px;
    border: none;
    border-radius: 0;
    &:hover {
      box-shadow: none;
      background: #f5f8fa;
    }
  }
  .check-wrap {
    position: absolute;
    left: 12px;
  }
  .recommend-item {
    position: relative;
    gap: 8px;
    display: inline-flex;
    flex-direction: column;
    width: 100%;
  }
  &-title {
    margin-bottom: 4px;
    height: 25px;
    font-size: 18px;
    font-weight: bold;
    color: #262626;
    line-height: 25px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex-shrink: 0;
  }
  .title-label {
    margin-left: 0;
    min-height: 22px;
    display: inline-block;
    width: 100%;
    margin-bottom: 12px;
    overflow: hidden;
    span:first-child {
      margin-left: 0;
    }
  }
  &.detail-recommend-body:hover {
    box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.12);
    .recommend-item-title {
      color: $color-primary-light-6;

    }
  }

  .recommend-item-title {
    flex: 1;
    @include text-ellipsis();
    line-height: 24px;
    font-size: 14px;
    margin-bottom: 0;
  }
  .recommend-item-title-wrap {
    display: flex;
    align-items: center;
    img {
      width: 20px;
      height: 20px;
    }
    .approve-status {
      line-height: 22px;
      text-align: right;
      color: #FAAD14;
      margin-left: 10px;
    }
  }
  &-content {
    font-size: 14px;
    font-weight: 400;
    color: #595959;
    line-height: 20px;
  }
  .title-label-tags {
    display: flex;
    flex-wrap: wrap;
    white-space: nowrap;
    .biz-intelligent-tags__view {
      width: 100%;
      ::v-deep .biz-intelligent-tags__view-list {
        overflow: hidden;
      }
      .biz-intelligent-tags__view-more-btn {
        position: absolute;
        right: 0;
        bottom: 0;
      }
    }
  }
  .recommend-item-text {
    border: none;
    flex: 1;
    display: flex;
    flex-direction: row;
  }
  .tinymce-editor {
    flex: 1;
    overflow: hidden;
    .recommend-content {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 4;
      overflow: hidden;
      cursor: pointer;
      color: #595959;
      font-size: 12px;
      font-weight: normal;
      line-height: 20px;

      ::v-deep & > * {
        font-size: 14px;
        font-weight: 400;
        color: #595959;
        line-height: 20px;
      }
    }
  }
  .recommend-item-preview, .img-preview {
    width: 100%;
    height: 80px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    background: #F5F8FA;
    img {
      width: 32px;
      height: 32px;
    }
  }
  .img-preview {
    width: 80px;
  }
}
.img-item {
  width: 80px;
  height: 80px;
  object-fit: cover;
  &.width-100 {
    width: 100%;
    margin-left: 0;
  }
}
.hidden-text {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

</style>
