<template>
  <div ref="previewFileRef" class="document-preview-file" v-loading="loading">
    <template v-if="isPdf">
      <iframe
        class="preview-pdf"
        :style="{ height: pdfIframeHeight }"
        embedded="true"
        @load="onLoad"
        :src="`${url}#toolbar=0`"
      ></iframe>
    </template>

    <template v-else-if="isImg">
      <img class="preview-img" :src="url" @click.stop="previewImg()" />
    </template>

    <template v-else-if="isVideos">
      <video
        class="preview-video"
        :src="url"
        controls
        autoplay
        muted
      >
        {{$t('common.base.tip.windowErrorTips1', {data1:'video'})}}
      </video>
    </template>

    <template v-else-if="isDocx">
      <vue-office-docx
        v-show="previewSuccess"
        :src="url"
        @error="errorHandler"
        @rendered="renderedHandler"
      />
      <iframe
        v-if="showPreview"
        id="preview-other"
        class="preview-other-type"
        :src="fileUrl"
        @load="onLoad"
      ></iframe>
    </template>

    <template v-else-if="isXlsx">
      <vue-office-excel
        v-show="previewSuccess"
        :src="url"
        :options="excelOptions"
        @error="errorHandler"
        @rendered="renderedHandler"
      />
      <iframe
        v-if="showPreview"
        id="preview-other"
        class="preview-other-type"
        :src="fileUrl"
        @load="onLoad"
      ></iframe>
    </template>

    <div class="document-preview-file-txt" v-else-if="isTxt">
      <pre class="document-preview-file-txt-content">{{ fileUrl }}</pre>
    </div>

    <template v-else>
      <iframe
        id="preview-other"
        class="preview-other-type"
        :style="{ height: otherIframeHeight + 'px' }"
        embedded="true"
        :src="fileUrl"
        @load="onLoad"
      ></iframe>
    </template>
  </div>
</template>

<script>
/* vue*/
import { defineComponent, getCurrentInstance, onMounted, ref, computed, reactive, toRefs} from 'vue'
/* word*/
import VueOfficeDocx from '@vue-office/docx'
import '@vue-office/docx/lib/index.css'
/* excel*/
import VueOfficeExcel from '@vue-office/excel'
import '@vue-office/excel/lib/index.css'
/* util*/
import { isImage, isVideo } from '@src/modules/im/imChat/utils/tools.js';
/* components*/


export default defineComponent({
  name: "PreviewFile",
  props: {
    info: {
      type: Object,
      default: (() => {})
    }
  },
  components: {
    VueOfficeDocx,
    VueOfficeExcel,
  },
  setup(props) {
    const previewFileRef = ref()
    const loading = ref(false)
    const pdfIframeHeight = ref('100vh')
    const otherIframeHeight = ref(800)
    const excelOptions = {
      // 自定义样式
      style: `
        body {
          font-family: 'Arial', sans-serif;
          background-color: #f5f5f5;
          padding: 20px;
        }
        table {
          border-collapse: collapse;
          width: 100%;
        }
        th, td {
          border: 1px solid #ddd;
          padding: 8px;
          text-align: left;
        }
        th {
          background-color: #f2f2f2;
        }
      `,
      // 大文件优化选项
      useWorker: true, // 使用 Web Worker 避免主线程阻塞
      virtualScroll: true, // 启用虚拟滚动，适用于超长文档
    }
    // 当前所在的组件实例
    const { proxy } = getCurrentInstance()

    const previewState = reactive({
      previewSuccess: false,
      showPreview: false // docx和excel通过kkFileView去预览，因为vue-office预览失败了
    });

    // 文件后缀
    const fileSuffix =  computed(() => props.info?.filename?.substring(props.info?.filename.lastIndexOf(".") + 1).toLowerCase() || '')
    // 文件url
    const url = computed(() => props.info?.url ?? '')

    // 判断是否是pdf文件
    const isPdf = computed(() => ['pdf'].includes(fileSuffix.value))

    // 判断是否是ppt文件
    const isPpt = computed(() => ['ppt', 'pptx'].includes(fileSuffix.value))

    // 判断是否是图片
    const isImg = computed(() => isImage(props.info?.filename))

    // 判断是否是视频
    const isVideos = computed(() => isVideo(props.info?.filename))

    // 判断是否是word:docx文件
    const isDocx = computed(() => ['docx'].includes(fileSuffix.value))

    // 判断是否是excel:xlsx文件
    const isXlsx = computed(() => ['xlsx'].includes(fileSuffix.value))

    // 判断是否是txt文件
    const isTxt = computed(() => ['txt'].includes(fileSuffix.value))

    const fileUrl = ref('');

    async function init() {
      try {
        if (!isTxt.value && !isImg.value && !isVideos.value) {
          loading.value = true;
        }
        
        if(!isXlsx.value && !isDocx.value && !isPdf.value && !isImg.value && !isVideos.value) {
          const response = await fetch(url.value);
          fileUrl.value = isTxt.value ? await response.text() : `/fileview/onlinePreview?url=${encodeURIComponent(Base64.encode(response.url))}`;
        }
      } catch (e) {
        console.error(e)
      }
    }

    // vue-office预览失败
    async function errorHandler() {
      try {
        console.log('加载失败');
        previewState.previewSuccess = false;
        //通过fileview去预览
        const response = await fetch(url.value);
        fileUrl.value = `/fileview/onlinePreview?url=${encodeURIComponent(Base64.encode(response.url))}`;
        previewState.showPreview = true;
      } catch (e) {
        console.error(e);
      };
    }

    // vue-office预览成功
    function renderedHandler() {
      console.log('加载成功');
      previewState.previewSuccess = true;
      loading.value = false;
    }

    // iframe加载完成
    function onLoad() {
      loading.value = false;
    }

    function previewImg() {
      proxy.$previewElementImg(url.value)
    }

    onMounted( () => {
      // pdf高度设置，宽高比为9:16
      if (isPdf.value && previewFileRef?.value?.offsetWidth) {
        pdfIframeHeight.value = previewFileRef.value.offsetWidth * 16 / 9 + 'px'
      }
      // ppt高度设置，宽高比为16:9
      if (isPpt.value && previewFileRef?.value?.offsetWidth) {
        otherIframeHeight.value = previewFileRef.value.offsetWidth * 9 / 16
      }
      init();
    })

    return {
      previewFileRef,
      loading,
      pdfIframeHeight,
      otherIframeHeight,
      excelOptions,
      fileUrl,
      url,
      isPdf,
      isImg,
      isVideos,
      isDocx,
      isXlsx,
      isTxt,
      ...toRefs(previewState),
      errorHandler,
      renderedHandler,
      onLoad,
      previewImg,
    }
  }
})
</script>

<style lang="scss" scoped>
.document-preview-file {
  .preview-pdf {
    width: 100%;
    height: 100vh;
    background: transparent;
  }
  .preview-other-type {
    width: 100%;
    height: 800px;
  }
  .preview-img {
    cursor: pointer;
    max-width: 100%;
  }
  .preview-video {
    min-width: 300px;
    max-width: 100%;
    min-height: 200px;
    height: auto;
  }
  &-txt {
    height: 100%;
    overflow: auto;

    &-content {
      font-size: 12px;
      width: 1000px;
      background: white;
      word-break: break-word;
      white-space: break-spaces;
      margin: 0 auto;
      padding: 12px;
    }
  }
}
</style>
<style lang="scss">
.document-preview-file {
  .docx-wrapper {
    padding: 0;
    .docx {
      width: 100% !important;
      margin-bottom: 0px !important;
    }
  }
  .vue-office-excel {
    min-height: 500px;
  }
  .x-spreadsheet-table {
    min-height: 459px !important;
  }
}
</style>