<template>
  <div class="comment-box">
    <div class="comment-container-v2" v-if="allowAddComment">
      <div class="content-left">
        <span class="task-pointer">
          <img class="author-img" :src="headImage" />
        </span>
      </div>
      <div class="content-right">
        <biz-at-textarea class="biz-at-textarea mar-b-8" ref="replyInput" search-url="/message/user/lists" name-key="displayName" v-model="replyContent">
          <template slot="item" slot-scope="item">
            <img :src="head(item.user.head)" />
            <span>{{ item.user.displayName }}</span>
          </template>
          <textarea class="base-at-textarea" @input="inputContent" :placeholder="$t('wiki.detail.component.comment.pla1')" maxlength="1000" :rows="3" @blur="inputBlur($event)"></textarea>
        </biz-at-textarea>
        <common-emoji ref="commonEmoji" :input-ref="$refs.replyInput" @changeInput="value => (replyContent = value)"> </common-emoji>
        <el-button class="comment-button" :loading="addLoading" @click="addComment()" type="primary">
          {{ $t('common.base.comment') }}
        </el-button>
      </div>
    </div>
    <div class="comment-main">
      <div class="comment-list">
        <div :key="comment.parentComment.id" v-for="comment in commentList" class="comment-items">
          <comment-item :allowAddComment="allowAddComment" :use-for-share="useForShare" :is-top-comment="true" :comment="comment.parentComment || {}" :init-data="initData" @commentChange="commentChange" @refresh="refresh">
            <div class="subcomment-box" slot="subComment" v-if="comment.childrenComment && comment.childrenComment.length">
              <comment-item :allowAddComment="allowAddComment" :use-for-share="useForShare" :key="subComment.id" v-for="subComment in comment.childrenComment" :comment="subComment" @refresh="refresh" @commentChange="commentChange" :init-data="initData" :title="title"> </comment-item>
            </div>
          </comment-item>
        </div>
      </div>
    </div>
    <el-button class="addComment" v-if="!this.allComment" @click="getComment()">更多评论</el-button>
  </div>
</template>

<script>
import { getWikiComment, addComment } from '@src/api/Repository';
import commentItem from './components/commentItem';
import ListFooter from '@src/modules/repository/common/ListFooter';
import commonEmoji from '@src/component/emoji';
import { getOssUrl } from '@src/util/assets';
import { enCodeAtText } from '@src/util/atText';
import { cutAtTextContent } from 'pub-bbx-utils';
import { useTenantId } from '@hooks/useRootWindow.ts';
import commentInput from '@src/modules/repository/documentV2/detail/component/commentInput.vue';

const defaultAvatar = getOssUrl('/avatar.png');

export default {
  name: 'comment-box',
  components: {
    [commentItem.name]: commentItem,
    [ListFooter.name]: ListFooter,
    [commonEmoji.name]: commonEmoji,
    [commentInput.name]: commentInput,
  },
  inject: ['headImage'],
  watch: {
    headImage(value) {
      console.log(value, 'head3');
    },
  },
  props: {
    // headImage:{
    //     type : String,
    //     default : ''
    // },
    wikiId: {},
    labels: {
      type: Array,
      default: () => [],
    },
    fontClass: {},
    initData: {
      type: Object,
    },
    useForShare: {
      type: Boolean,
      default: false,
    },
    commentListParent: {
      type: Array,
      default: () => [],
    },
    commentPermission: {
      type: Number,
      default: 0,
    },
    title: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      commentList: this.useForShare ? this.commentListParent : [],

      pageInfo: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },

      replyContent: '',
      addLoading: false,
      atUsers: [],
    };
  },
  computed: {
    allComment() {
      return this.pageInfo.pageSize > this.pageInfo.total;
    },
    allowAddComment() {
      return this.commentPermission == 1
    },
  },
  mounted() {
    !this.useForShare && this.wikiId && this.getWikiComment();
  },
  watch: {
    wikiId() {
      !this.useForShare && this.getWikiComment();
    },
  },
  methods: {
    getComment() {
      this.pageInfo.pageSize += 10;
      this.getWikiComment();
    },
    head(src) {
      if (!src) return defaultAvatar;
      return src;
    },
    inputBlur(e) {
      this.$refs.commonEmoji && this.$refs.commonEmoji.contentBlur(e.target);
    },
    addEmoji(emoji) {
      this.replyContent = emoji.text;
    },
    getWikiComment() {
      getWikiComment({
        wikiId: this.wikiId,
        ...this.pageInfo,
      })
        .then(res => {
          if (res.code !== 0) {
            this.$platform.notification({
              title: res.message,
              type: 'error',
            });
            return;
          }
          this.commentList = res.result?.commentList || [];
          this.pageInfo.total = res.result?.pageInfo?.total || 0;
        })
        .catch(err => {
          console.log(err);
        });
    },
    addComment() {
      if (!this.replyContent) {
        return this.$platform.notification({
          title: this.$t('wiki.detail.component.comment.tips1'),
          type: 'warning',
        });
      }

      let replyContent = enCodeAtText(this.replyContent, this.atUsers);
      let receivers = [];
      this.atUsers.forEach(item => {
        if (this.replyContent.indexOf(`${item.displayName} `) > -1) {
          receivers.push(item.userId);
        }
      });
      let receiversString = receivers.join(',');
      let query = '';
      if (this.atUsers.length > 0) {
        let queryData = {
          receivers: receiversString,
          tenantId: useTenantId().value,
          content: cutAtTextContent(this.replyContent),
          sendUserName: this.initData?.userInfo?.displayName,
          bizId: this.wikiId,
          bizNo: this.title,
        };
        query = '?';
        for (let i in queryData) {
          query += `&${i}=${queryData[i]}`;
        }
      }
      this.addLoading = true;
      addComment(
        {
          wikiId: this.wikiId,
          content: replyContent,
        },
        query
      )
        .then(res => {
          if (res.code !== 0) {
            this.$platform.notification({
              title: res.message,
              type: 'error',
            });
            return;
          }
          this.$platform.notification({
            title: this.$t('common.base.tip.releaseSuccess'),
            type: 'success',
          });
          this.replyContent = '';
          this.atUsers = [];
          this.refresh();
          this.$emit('commentChange');
        })
        .catch(err => {
          console.log(err);
        })
        .finally(() => {
          this.addLoading = false;
        });
    },
    refresh() {
      this.getWikiComment();
    },
    commentChange() {
      this.$emit('commentChange');
    },
    inputContent(event) {
      let value = event.target.value;
      this.replyInput = value;
    },
  },
};
</script>

<style lang="scss">
.tinymce-editor {
  .wiki-content {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    // -webkit-line-clamp: 2;
    overflow: hidden;
    ::v-deep p {
      font-size: 14px;
      font-weight: 400;
      color: #595959;
      line-height: 20px;
    }
  }
}
.comment-box {
  margin-top: 12px;
  border-radius: 4px;
  background: #fff;
  .common-emoji {
    top: 9px;
    transform: none;
  }
  // padding: 32px 100px;
}
.comment-input {
  position: relative;
  ::v-deep .el-button {
    margin-top: 12px;
    float: right;
    margin-bottom: 16px;
  }
  ::v-deep .el-textarea__inner {
    height: 70px;
    padding: 10px 22px 10px 16px;
    background: #f5f5f5;
    border-radius: 4px;
    border: 1px solid #e8e8e8;
    font-size: 14px;
    font-weight: 400;
    color: #262626 !important;
    line-height: 20px;
    &::-webkit-input-placeholder {
      color: #8c8c8c !important;
    }
    &::-moz-placeholder {
      color: #8c8c8c !important;
    }
    &:-ms-input-placeholder {
      color: #8c8c8c !important;
    }
  }
}
.comment-main {
  clear: both;
  padding-top: 10px;
  & > h3 {
    margin-bottom: 9px;
  }
}
.subcomment-box {
  margin-top: 16px;
  border-radius: 4px;
  overflow: hidden;
}
.comment-items {
  & > .comment-item-v2 {
    border-bottom: 1px solid #e5e6e5;
  }
}
.comment-count {
  font-size: 16px;
  font-weight: 400;
  color: #8c8c8c;
  line-height: 22px;
  margin-left: 4px;
}
.comment-container-v2 {
  position: relative;
  display: flex;
  flex-direction: row;
  gap: 16px;
  align-self: stretch;
  .content-left {
  }
  .content-right {
    width: 100%;
    .comment-button {
      padding: 5px 16px;
      height: 32px;
    }
    .base-at-textarea {
      height: 60px;
      min-height: 60px;
      padding: 9px 12px;
    }
  }
}
.author-img {
  vertical-align: middle;
  display: inline-block;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  margin-right: 8px;
}
.addComment {
  width: 100%;
}
</style>
