<template>
  <div class="recommend-box">
    <div class="recomment-tab">
      <h3 style="margin: auto 0;">{{ $t('common.base.Recommend') }} </h3>
      <!-- <el-dropdown @command="handleCommand" placement="bottom">
       9<i class="el-icon-arrow-down el-icon--right"></i>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="0">5</el-dropdown-item>
          <el-dropdown-item command="1">5</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown> -->
      <el-dropdown class="select-right" @command="handleCommand">
        <span >
          <i :class="['iconfont',pattern ?'icon-liebiao':'icon-piliang']"></i> {{patternList[pattern]}}
        </span>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="0">{{ patternList[0] }}</el-dropdown-item>
            <el-dropdown-item command="1">{{ patternList[1] }}</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      
    </div>
    <div :class=" pattern ? 'recommend-list' : 'recommend-card'">
      <recommend-item v-for="item in recommendData" :key="item.id" @toDetail="toDetail(item)" :item="item" mode="detail" :pattern="pattern"> </recommend-item>
    </div>
  </div>
</template>

<script>
import { getOtherWiki } from '@src/api/Repository';
import { openTabForWikiView } from '@src/util/business/openTab';
import BizIntelligentTagsView from '@src/component/business/BizIntelligentTags/BizIntelligentTagsView.tsx';
import recommendItem from './recommendItem.vue';

export default {
  name: 'recommend-box',
  components: {
    BizIntelligentTagsView,
    recommendItem,
  },
  props: {
    wikiId: {},
    labels: {
      type: Array,
      default: () => [],
    },
    fontClass: {},
    inList: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      recommendData: [],
      pattern: 0,
      patternList: ['图文卡片','详情列表'],
    };
  },
  mounted() {
    this.wikiId && this.getOtherWiki();
  },
  watch: {
    wikiId() {
      this.getOtherWiki();
    },
  },
  methods: {
    handleCommand(command){
      this.pattern = Number(command)
    },
    // labelMark(text, type) {
    //   if (type === 'content') {
    //     let div = document.createElement('div')
    //     div.innerHTML = text
    //     text = div.innerText
    //   }
    //   let labels = this.labels
    //   if (!labels?.length) return type === 'content' ? `<p>${text}</p>` : text
    //   let reg = new RegExp(labels.join('|'), 'g')
    //   text = text.replaceAll(reg, '<span class="color-mark">$&</span>')
    //   return type === 'content' ? `<p>${text}</p>` : text
    // },
    getOtherWiki() {
      getOtherWiki({
        wikiId: this.wikiId,
      }).then(res => {
        if (res.code === 0) {
          res.result?.forEach(item => {
            if (!item.label) {
              item.label = [];
              return;
            }
            item.label = JSON.parse(item.label);
          });
          this.recommendData = res.result || [];
        }
      });
    },
    toDetail(item) {
      if (this.inList) {
        return this.$emit('selectSearchResult', item)
      }
      openTabForWikiView(item.id);
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .color-mark {
  color: $color-primary-light-6 !important;
}
.recommend-box {
  background: #fff;
  margin-top: 12px;
  border-radius: 4px;
  & > h3 {
    font-size: 16px;
    font-weight: bold;
    color: #262626;
    line-height: 22px;
    margin-bottom: 0;
  }
  .recomment-tab{
    display: flex;
    justify-content: space-between;
    .select-right{
      background: #F5F8FA;
      color: #262626;
      padding: 4px 8px;
      display: flex;
      border-radius: 4px;
      cursor: pointer;
    }
  }
}
.recommend-card {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-top: 16px;
  gap: 16px;
}
.recommend-list {
  display: flex;
  flex-direction: column;
  margin-top: 16px;
}
</style>
