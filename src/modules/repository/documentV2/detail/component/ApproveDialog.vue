<template>
  <base-modal :title="$t('common.base.approve')" :show.sync="visible" width="600px" class="approve-performance-modal" @closed="reset">
    <ul class="approve-info">
      <li>
        <label>{{ $t('wiki.detail.approveDialog.label1') }}：</label> {{ approveData.objNo }}
      </li>
      <li>
        <label>{{ $t('wiki.detail.approveDialog.label2') }}：</label> {{ $t('wiki.moduleName') }}
      </li>
      <li>
        <label>{{ $t('wiki.detail.approveDialog.label3') }}：</label> {{ approveData.typeName }}
      </li>
      <li>
        <label>{{ $t('wiki.detail.approveDialog.label4') }}：</label> {{ approveData.action }}
      </li>
      <li>
        <label>{{ $t('wiki.detail.approveDialog.label5') }}：</label> {{ approveData.action }} {{ approveData.objNo }}
      </li>
      <li>
        <label>{{ $t('wiki.detail.approveDialog.label6') }}：</label>
        <template v-if="isOpenData">
          <open-data type="userName" :openid="approveData.proposerStaffId"></open-data>
        </template>
        <template v-else>
          {{ approveData.proposerName }}
        </template>
      </li>
      <li>
        <label>{{ $t('wiki.detail.approveDialog.label7') }}：</label> {{ approveData.createTime | fmt_datetime }}
      </li>
      <li>
        <label>{{ $t('wiki.detail.approveDialog.label8') }}：</label> {{ approveData.applyRemark }}
      </li>
      <li>
        <label>{{ $t('wiki.detail.approveDialog.label9') }}：</label>
        <el-radio v-model="form.state" label="success">{{ $t('common.base.agree') }}</el-radio>
        <el-radio v-model="form.state" label="fail">{{ $t('common.label.disagree') }}</el-radio>
      </li>
      <li>
        <label>{{ $t('wiki.detail.approveDialog.label10') }}：</label>
        <el-input type="textarea" v-model="form.approveRemark" resize="none" rows="3" :maxlength="500"></el-input>
      </li>
    </ul>

    <p class="tip">{{ $t('wiki.detail.approveDialog.tips1') }}</p>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">{{ $t('common.base.cancel') }}</el-button>
      <el-button type="primary" @click="onSubmit" :disabled="pending">{{ $t('common.base.confirm') }}</el-button>
    </div>
  </base-modal>
</template>

<script>
import { isOpenData, openAccurateTab } from '@src/util/platform';
import * as RepositoryApi from '@src/api/Repository';
import { PageRoutesTypeEnum } from 'pub-bbx-global/pageType/dist/enum/PageRoutesEnum';
import { t } from '@src/locales';

export default {
  name: 'approve-dialog',
  props: {
    approveData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      isOpenData,
      visible: false,
      pending: false,
      init: false,
      form: {
        state: 'success',
        approveRemark: '',
      },
      rules: {
        approveRemark: [
          { required: true, message: t('wiki.detail.approveDialog.tips2'), trigger: 'blur' },
          { max: 500, message: t('wiki.detail.approveDialog.tips3'), trigger: 'blur' },
        ],
      },
    };
  },
  methods: {
    open() {
      this.init = true;
      this.visible = true;
    },
    onSubmit() {
      if (this.form.state == 'fail' && !this.form.approveRemark) {
        this.$platform.notification({
          title: this.$t('wiki.detail.approveDialog.tips4'),
          type: 'error',
        });
        return;
      }

      this.visible = false;
      this.pending = true;

      RepositoryApi.operateApprove({
        ...this.form,
        id: this.approveData.id,
        source: 'wiki',
      })
        .then(res => {
          this.pending = false;
          if (res.success) {

            if (res.status)
              return this.$platform.notification({
                title: this.$t('common.base.appreoveFail'),
                message: res.message || '',
                type: 'error',
              });

            this.$platform.notification({
              title: this.$t('common.base.approveSuccess'),
              type: 'success',
            });

            if (this.approveData.isList) {
              this.$parent.search();
              return;
            }
            if (this.approveData.inList) {
              this.$emit('approveSuccess');
              return;
            }

            if ((this.approveData.wikiId && this.form.state == 'fail') || !this.approveData.wikiId) {
              this.approveData.wikiId = this.approveData.objId;
            }

            let id = window.frameElement.dataset.id;
            this.$platform.closeTab(id);

            let detailFromId = window.frameElement.getAttribute('id');
            let fromId = window.frameElement.getAttribute('fromid');
            this.$platform.refreshTab(fromId);
            return openAccurateTab({
              type: PageRoutesTypeEnum.PageWikiV2Detail,
              key: this.approveData.wikiId,
              params: `wikiId=${this.approveData.wikiId}`,
              reload: true,
              fromId: detailFromId,
            });
          }
          this.$platform.notification({
            title: res.message,
            type: 'error',
          });
        })
        .catch(e => console.error('e', e));
    },

    reset() {
      this.init = false;
    },
  },
};
</script>

<style lang="scss">
.approve-performance-modal {
  .approve-info {
    margin: 0;
    padding: 15px 0 0;

    li {
      list-style: none;
      display: flex;
      word-break: break-all;

      label {
        width: 100px;
        margin: 0;
        line-height: 26px;
        flex-shrink: 0;
      }
    }
  }

  .tip {
    margin: 10px 0 0;
    line-height: 26px;
    font-size: 12px;
    color: #999;
  }

  .base-modal-body {
    padding: 0 30px;
  }

  .dialog-footer {
    text-align: right;
    /*padding: 15px 0 ;*/
  }
}

.el-form {
  padding-top: 15px;

  .el-form-item {
    margin: 0 !important;

    .el-form-item__content {
      line-height: 26px;

      .el-textarea {
        width: calc(100% - 100px);
      }

      .el-form-item__error {
        margin-left: 100px;
      }
    }
  }
}
</style>
