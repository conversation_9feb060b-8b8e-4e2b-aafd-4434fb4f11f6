<template>
  <div class="comment-container">
    <div class="content-left">
      <span class="task-pointer">
        <img class="author-img" :src="headImage" />
      </span>
    </div>
    <div class="content-right">
      <biz-at-textarea class="biz-at-textarea" ref="replyInput" search-url="/message/user/lists" name-key="displayName" v-model="replyContent">
        <template slot="item" slot-scope="item">
          <img :src="head(item.user.head)" />
          <span>{{ item.user }}</span>
        </template>
        <textarea class="base-at-textarea" @input="inputContent" :placeholder="$t('wiki.detail.component.comment.pla1')" maxlength="1000" :rows="3" @blur="inputBlur($event)"></textarea>
      </biz-at-textarea>
      <common-emoji ref="commonEmoji" :input-ref="$refs.replyInput" @changeInput="value => (replyContent = value)"> </common-emoji>
      <el-button class="comment-button" :loading="addLoading" @click="addComment()" type="primary">
        {{ $t('common.base.comment') }}
      </el-button>
    </div>
  </div>
</template>

<script>
import commonEmoji from '@src/component/emoji';
import { getOssUrl } from '@src/util/assets';

const defaultImg = getOssUrl('/avatar.png');


export default {
  name: 'comment-input',
  components: {
    [commonEmoji.name]: commonEmoji,
  },
  inject: ['headImage'],
  props: {
    replyContent: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      commentText: '',
      addLoading: false,
    };
  },

  methods: {
    head(src) {
      if (!src) return defaultImg;
      return src;
    },
    inputContent(event) {
      let value = event.target.value;
      this.replyInput = value;
      this.$emit('update:replyContent', value);
    },
    inputBlur(e) {
      this.$refs.commonEmoji && this.$refs.commonEmoji.contentBlur(e.target);
    },
    addComment() {
      this.$emit('addComment');
    },
  },
};
</script>
<style lang="scss" scoped>
.comment-container {
  position: relative;
  display: flex;
  flex-direction: row;
  gap: 16px;
  align-self: stretch;
  height: 100px;
  .content-left {
  }
  .content-right {
    // width: 570px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    .comment-button {
      padding: 5px 16px;
      width: 60px;
      height: 32px;
    }
  }
}
.author-img {
  vertical-align: middle;
  display: inline-block;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  margin-right: 8px;
}
.base-at-textarea {
  height: 60px;
  width: 570px;
  padding: 9px 12px;
}
</style>
