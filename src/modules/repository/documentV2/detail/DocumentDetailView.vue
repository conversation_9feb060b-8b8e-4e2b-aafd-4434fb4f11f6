<template>
  <div class="document-list-detail-wrap">
    <div class="document-list-detail-v2" :class="inList ? 'inList' : ''" :style="{ height: height }" v-if="detailShow" v-loading="loading">
      <!-- 详情头部 -->

      <div class="detail-top">
        <div class="detail-header-left">
          <div class="title-main-text">
            <div class="max-title">
              {{ detail.title }}
            </div>
            <el-tag v-if="detail.allowShare" type="success">{{ $t('wiki.detail.public') }}</el-tag>
            <span class="title-label-tags">
              <BizIntelligentTagsView v-if="intelligentTagsList.length" type="detail" :tagsList="intelligentTagsList" />
            </span>
            <IntelligentTagsTaggingView v-bind="tagsSingleComponentAttrs" v-on="tagsSingleComponentOn" :showTagsViewList="false" />
          </div>
          <div class="author-info">
            <p class="name">
              {{ $t('wiki.detail.lastEdit') }}：
              <template v-if="isOpenData">
                <open-data type="userName" :openid="detail.createUserStaffId"></open-data>
              </template>
              <template v-else>
                {{detail.updateUserName || detail.createUserName}}
              </template>
            </p>
            <p class="time mar-l-5">
              <span>{{ (detail.updateTime || detail.createTime) | fmt_datehour }}</span>
              <span>{{ $t('common.wiki.reviewTimes') }}：{{ formatTimes(wikiCountData.readTimes) }}</span>
            </p>
          </div>
        </div>
        <div class="detail-header-right">
          <el-button type="primary" @click="approve" v-if="showDetailApprove && detail.examineState == 1">
            {{ $t('common.base.approve') }}
          </el-button>
          <el-button type="plain-third" @click="revoke" v-if="detail.examineState == 1 && revokeShow">
            {{ $t('common.base.withdraw') }}
          </el-button>
          <!-- 草稿发布 -->
          <el-button v-if="allowDraftEdit && !showDetailApprove" type="primary" :disabled="pending" @click="saveAndSubmit()">{{ $t('common.base.release') }}</el-button>
          <!-- 编辑 -->
          <el-button v-if="allowEdit && !importAttachment.length && !showDetailApprove" type="plain-third" @click="editArticle()">{{ $t('common.base.edit') }}</el-button>
          <!-- 分享 -->
          <el-button
            v-if="!detail.isDraft && shareEnable"
            type="primary"
            @click="
              wikiShare(detail);
              trackEventHandler('share');
            "
            class="base-button green-butn"
            >{{ $t('common.base.share') }}</el-button
          >
          <el-popover style="margin-left: 12px" v-model="moreListPopoverVisible" placement="top" width="124" popper-class="more-list-popover" trigger="click">
            <div class="more-list">
              <div v-if="allowEdit && !importAttachment.length && !showDetailApprove" class="more-item" @click="copyArticle()">{{ $t('common.base.copy') }}</div>
              <div v-if="importAttachment.length" class="more-item" @click="chooseFile">
                {{ $t('common.wiki.updateFile') }}
                <input type="file" ref="input" @change="handleChange">
              </div>
              <div
                v-if="detail.examineState === null && allowEdit"
                class="more-item"
                @click="
                  moreListPopoverVisible = false;
                  moveCatalogVisible = true;
                "
              >
                {{ $t('wiki.list.btn.moveTo') }}
              </div>
              <!-- 下载（目前只支持附件类文档下载） -->
              <div class="more-item" @click="downloadFile(importAttachment[0])" v-if="importAttachment.length && allowDownload">{{ $t('common.base.download') }}</div>
              <!-- 文档设置 -->
              <div class="more-item" v-if="(allowCreate || allowEdit) && !showDetailApprove" @click="openDialog('edit')">{{ $t('common.base.documentSetting') }}</div>
              <!-- 关联信息 -->
              <div class="more-item" @click="openDialog('relationInfo')" v-if="showRelationBtn">{{ $t('wiki.detail.relationInfo') }}</div>
              <div
                class="more-item"
                @click="
                  logDrawerShow = true;
                  moreListPopoverVisible = false;
                "
              >
                {{ $t('wiki.component.updateLog.title1') }}
              </div>
              <div
                v-if="isList"
                class="more-item"
                @click="
                  openFrame();
                  trackEventHandler('open');
                "
              >
                {{ $t('common.base.openInNewPage') }}
              </div>
              <div
                v-if="allowDelete && !showDetailApprove"
                class="more-item"
                @click="
                  deleteArticle();
                  trackEventHandler('detele');
                "
              >
                {{ $t('common.base.delete') }}
              </div>
            </div>
            <el-button class="setting-btn" slot="reference"><i class="iconfont icon-MoreOutlined" style="width: 14px; height: 14px"></i></el-button>
          </el-popover>
        </div>
      </div>
      <div :class="classNames" v-if="detail && detail.title">
        <!-- 文章详情 -->
        <div class="detail-content">
          <div class="info">
            <!-- 描述 -->
            <div class="detail-desc mar-b-16" v-if="detail.desc">{{ detail.desc }}</div>
            <!-- 故障库Start -->
            <div v-if="isFault" class="Fault tinymce-editor">
              <div class="FaultList">
                <label>{{ $t('wiki.detail.label6') }}：</label><span>{{ detail.faultPerformance }}</span>
              </div>
              <div class="FaultList">
                <label>{{ $t('wiki.detail.label7') }}：</label><span>{{ detail.faultReason }}</span>
              </div>
              <div class="FaultList">
                <label>{{ $t('wiki.detail.label8') }}：</label>
                <span v-html="detail.content" class="wiki-content" @click="wikiClick($event)"> </span>
              </div>
            </div>
            <!-- 故障库End -->
            <div v-else class="ql-container ql-snow content" :class="fontClass" style="border: none">
              <div class="tinymce-editor">
                <!-- 附件类文档预览 -->
                <div class="wiki-attachment" v-if="importAttachment.length && !loading">
                  <div class="file-list" v-if="isXlsx">
                    <!-- 要用到组件的预览方法 -->
                    <base-file-item ref="baseFileItem2" style="display: none;" :source="importAttachment" :key="importAttachment[0].id" :file="importAttachment[0]" :fileDownloadAuth="allowDownload" size="small"></base-file-item>
                    <div class="file-item" @click="previewFile(importAttachment[0], true)">
                      <img :src="findFileIconUrl(importAttachment[0].filename)" class="mar-r-8" />
                      <div class="file-info">
                        <div class="file-name">{{ importAttachment[0].filename }}</div>
                        <div class="file-size">{{ importAttachment[0].fileSize }}</div>
                      </div>
                    </div>
                  </div>
                  <preview-file v-else :key="previewKey" :info="importAttachment[0]" />
                </div>
                <div v-else v-html="detail.content" class="wiki-content" @click="wikiClick($event)"></div>
              </div>
            </div>
          </div>
          <!-- 详情页脚部分 -->
          <div class="footer" v-if="detail.attachment && detail.attachment.length > 0">
            <!-- 要用到组件的预览方法 -->
            <base-file-item ref="baseFileItem" style="display: none;" :source="detail.attachment" :key="detail.attachment[0].id" :file="detail.attachment[0]" :fileDownloadAuth="allowDownload" size="small"></base-file-item>
            <div class="file-list">
              <div class="file-item" v-for="file in detail.attachment" :key="file.id">
                <img :src="findFileIconUrl(file.filename)" class="mar-r-8" />
                <div class="file-info">
                  <div class="file-name" @click="previewFile(file)">{{ file.filename }}</div>
                  <div class="file-size">{{ file.fileSize }}</div>
                </div>
                <i class="iconfont icon-download" v-if="allowDownload" @click="downloadFile(file)"></i>
              </div>
            </div>
          </div>
          <div class="status-view" v-if="detail.examineState === null">
            <span class="status-view-each-v2 canClick mar-r-48" @click="wikiStateData.isLike ? wikiCancelLike() : wikiLike()">
              <i
                class="iconfont"
                :class="{
                  'icon-dianzan': !wikiStateData.isLike,
                  'icon-like-fill': !!wikiStateData.isLike,
                }"
              ></i>
              <b>{{ handleLikeTimes(wikiCountData.likeTimes || 0) }}</b>
            </span>
            <span class="status-view-each-v2 canClick" @click="wikiStateData.isCollection ? cancelCollection() : addCollection()">
              <i
                class="iconfont"
                :class="{
                  'icon-shoucang': !wikiStateData.isCollection,
                  'icon-yishoucang': !!wikiStateData.isCollection,
                }"
              ></i>
              <b>{{ handleCollectionTimes(wikiCountData.collectionTimes || 0) }}</b>
            </span>
          </div>
        </div>

        <BaseBarV3 v-if="detail.examineState === null" :bar-list="tabBarList" :now-item="activeTab" @changeItem="tabBarChangeItem"></BaseBarV3>

        <!-- 评论 -->
        <comment-box
          v-if="detail.examineState === null && commentShow && activeTab === 'comment-box'"
          :wiki-id="detail.id"
          :commentPermission="commentPermission"
          @commentChange="getWikiData()"
          :init-data="initData"
          :title="detail.title"></comment-box>
        <!-- 推荐 -->

        <recommend-box v-if="detail.examineState === null && recommendShow && activeTab === 'more-recomment'" :font-class="fontClass" :labels="detail.label" :wiki-id="detail.id" :inList="inList" @selectSearchResult="selectSearchResult"> </recommend-box>

        <approve-dialog :approve-data="approveData" ref="approveDialog" @approveSuccess="approveSuccess" />

        <el-drawer :title="$t('common.base.updateLog')" size="280" :visible.sync="logDrawerShow">
          <update-log class="view-right" :wiki-id="wikiId" :no-header="true"></update-log>
        </el-drawer>
      </div>

      <move-catalog 
        type="wiki"
        v-model="moveCatalogVisible"
        v-if="moveCatalogVisible"
        @moveEnd="handleMoveSuccess()"
        :beMoveData="detail"
        :getDataFromParent="true"
        :typeName="detail.type"
        :moveTreeDataFromProp="isFault ? FaultLibrary : Library"
      ></move-catalog>

      <!-- 分享 -->
      <document-share ref="documentShare" :wiki-list="wikiListForShare"> </document-share>
    </div>
    <div v-else class="document-list-detail-v2 empty">
      <div>
        <img class="empty-img" :src="noDataImage" />
      </div>
      <span class="empty-msg">{{ deleteMsg || $t('common.base.tip.noData') }}</span>
    </div>
    <!-- 文档设置弹窗 -->
    <edit-title-dialog mode="detail" ref="editTitleDialog" :wikiDownloadAuth="wikiDownloadAuth" :wikiConfig="initData.wikiConfig" :wikiId="wikiId" @input="titleSubmitSuccess" />
    <!-- 关联信息弹窗 -->
    <relation-info-dialog :detail="detail" ref="relationInfoDialog" />
    <!-- 发起审批 -->
    <request-approve @createApprove="createApprove" ref="requestApproveDialog" />
  </div>
</template>

<script>
import { isOpenData, openAccurateTab } from '@src/util/platform';
import * as RepositoryApi from '@src/api/Repository';
import { formatDate } from 'pub-bbx-utils';
import { storageSet } from '@src/util/storage';

import ApproveDialog from './component/ApproveDialog.vue';
import RequestApprove from '@src/modules/repository/documentV2/create/component/RequestApprove.vue';
import BaseBarV3 from '@src/component/common/BaseTabBar/BaseTabBarV3.vue';
import EditTitleDialog from '@src/modules/repository/documentV2/create/component/EditTitleDialog.vue';
import RelationInfoDialog from 'src/modules/repository/documentV2/detail/component/RelationInfoDialog.vue';

import { isShowSelfServicePortal } from '@shb-lib/version';
/* mixin */
import ThemeMixin from '@src/mixins/themeMixin/index.ts';
import { intelligentTagsDetailMixin } from '@src/modules/intelligentTags/mixins/index.ts';
import VersionMixin from '@src/mixins/versionMixin/index.ts';
import DocumentMixin from '@src/modules/repository/documentV2/mixin/documentMixin.js'

import { openTabForTaskView, openTabForWikiCreate } from '@src/util/business/openTab';

import UpdateLog from '../components/UpdateLog.vue';

import recommendBox from './component/recommendBox';
import commentBox from './component/comment';
import moveCatalog from '@src/modules/repository/documentV2/components/moveCatalog';
import documentShare from '@src/modules/repository/documentV2/components/documentShare';
import PreviewFile from '@src/modules/repository/documentV2/detail/component/PreviewFile.vue'
import { getRootWindow } from '@src/util/dom';
import { PageRoutesTypeEnum } from 'pub-bbx-global/pageType/dist/enum/PageRoutesEnum';
import i18n from '@src/locales';
import Uploader from '@src/util/uploader';
import { getOssUrl } from '@src/util/assets';
import { findFileIconUrl } from '@src/modules/ai/views/edit/mock.ts'

/* api */
import { syncWiki, deleteWiki } from '@src/api/LLMApi';
import { uuid } from '@src/util/lang/string'
import { getRootWindowInitData } from '@src/util/window';
import { getRootWindowInitDataUser } from '@src/util/window';
const rootWindowInitData = getRootWindowInitData();

const noDataImage = getOssUrl('/no_data.png');
const avatarImage = getOssUrl('/avatar.png');

export default {
  name: 'document-detail',
  mixins: [VersionMixin, ThemeMixin, intelligentTagsDetailMixin, DocumentMixin],
  provide() {
    return {
      headImage: this.getHead(),
    };
  },
  props: {
    info: {
      type: Object,
      default: () => ({}),
    },
    initData: {
      type: Object,
      default: () => ({}),
    },
    infoEdit: {
      type: Object,
      default: () => ({}),
    },
    isList: {
      type: Boolean,
      default: false,
    },
    rightWidth: {
      type: Number,
      default: 0,
    },
    inList: {
      type: Boolean,
      default: false,
    },
    // 列表传过来的id
    currentWikiId: {
      type: String,
      default: '',
    },
  },
  components: {
    [ApproveDialog.name]: ApproveDialog,
    [RequestApprove.name]: RequestApprove,
    [UpdateLog.name]: UpdateLog,
    [recommendBox.name]: recommendBox,
    [commentBox.name]: commentBox,
    [moveCatalog.name]: moveCatalog,
    [documentShare.name]: documentShare,
    [PreviewFile.name]: PreviewFile,
    [EditTitleDialog.name]: EditTitleDialog,
    [RelationInfoDialog.name]: RelationInfoDialog,
    BaseBarV3,
  },
  created() {
    this.updateIntelligentTagsModule('WIKI');
    this.activeTab = this.tabBarList[0]?.tabName || '';
  },
  data() {
    return {
      avatarImage,
      noDataImage,
      isOpenData,
      allowDraftEdit: false, // 允许草稿发布
      allowDelete: false,
      form: this.buildForm(), // 附件存储格式
      wikiId: '', // 通知公告id
      showDetailApprove: false,
      approveData: {},
      isReview: false,
      refuseInfo: {
        text: '',
      },
      show: false,
      rules: {
        text: [
          {
            required: true,
            message: i18n.t('wiki.detail.tips1'),
            trigger: 'blur',
          },
        ],
      },
      detail: {}, // 文章详情
      pending: false,
      loading: false,
      deleteMsg: null,
      revokeShow: false,
      url: '',
      detailShow: true,

      logDrawerShow: false,

      count: {
        good: 1,
        comment: 2,
        favorite: 3,
        watch: 4,
      },
      checkGood: true,
      checkFavorite: true,

      operationAuth: {},
      wikiCountData: {},
      wikiStateData: {},

      moreListPopoverVisible: false,

      likeLoading: false,
      collectionLoading: false,

      moveCatalogVisible: false,

      wikiListForShare: [],
      shareEnable: false, // 是否有分享权限
      // 故障库
      FaultLibrary: [],
      Library: [],
      wikiApproveUsers: [],
      activeTab: '',
      headImage: '',
      previewKey: 0,
    };
  },
  async mounted() {
    this.init();
  },

  methods: {
    findFileIconUrl,
    // 附件类文档下载
    downloadFile(info) {
      if (!info) return
      let a = document.createElement('a');
      a.href = `/files/download?fileId=${info.id}`;
      a.download = info.originFileName ?? info.filename ?? info.name ?? this.$t('common.base.preview');
      a.click();
      window.URL.revokeObjectURL(a);
    },
    previewFile(file, useBaseFileItem2) {
      if (useBaseFileItem2) {
        return this.$refs.baseFileItem2.handlePreviewFile(file)
      }
      this.$refs.baseFileItem.handlePreviewFile(file)
    },
    // 触发input click事件选择文件
    chooseFile () {
      if(this.pending) return this.$platform.alert(this.$t('common.base.uploadModal.tips1'));
      this.$refs.input.value = null;
      this.$refs.input.click();
    },
    // 选择文件上传后调用编辑接口
    handleChange(event){
      const files = event.target.files;
      if(!files || !files.length) return;
      if (files[0].size > Uploader.FILE_MAX_SIZE) return this.$platform.alert(this.$t('common.validate.updateLimit', { size: '200M', imgType: this.$t('common.base.file') }))

      this.pending = true;
      files[0].id = uuid()
      this.$emit('onFileAdd', files[0])
      Uploader.batchUploadWithParse({files, action: '/files/upload/wiki', source: 'wiki'}).then(result => {
        let {success, error} = result;

        if(error.length > 0){
          this.pending = false
          let message = error.map(item => item.message).join('\n');
          files[0].message = message
          this.$emit('onFileAddError', files[0])
          return this.$platform.alert(message)
        }

        if(success.length > 0){
          this.$emit('onFileAddSuccess', files[0])
          const params = this.buildParams()
          params.importAttachment = success;
          this.pending = true
          RepositoryApi.saveAndSumbit(params).then(res => {
            this.pending = false
            if (res.success) {
              this.$message.success(res.message)
              this.getDocumentDetail()
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    },
    saveAndSubmit() {
      if (this.initData.wikiConfig.needApprove && this.initData.wikiConfig.approvers && this.initData.wikiConfig.approvers.length > 0) {
        this.$refs.requestApproveDialog.open();
        return;
      }
      const params = this.buildParams()
      this.pending = true
      RepositoryApi.saveAndSumbit(params).then(res => {
        this.pending = false
        if (res.success) {
          this.$message.success(res.message)
          this.getDocumentDetail()
          this.$emit('releaseSuccess')
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 需要审批时，提交
    async createApprove(remark) {
      this.$refs.requestApproveDialog.close();

      try {
        let otherInfo = this.buildParams();
        // 审批时需要传allowShare
        otherInfo.allowShare = otherInfo.baseWikiSetting?.externalSharing?.allowExternalShare ? 1 : 0
        let params = {
          objId: this.currWikiId,
          applyRemark: remark,
          source: 'wiki',
          otherInfo,
        };
        this.pending = true;
        const ApprovePromise = RepositoryApi.createApprove(params);
        let res = await this.checkNumExceedLimitAfterHandler(ApprovePromise);
        this.pending = false;

        if (res.success) {
          localStorage.removeItem(`document_article_${this.initData.userInfo.userId}`);
          this.$platform.notification({
            title: res.message,
            type: 'success',
          });
          storageSet('isCreateApprove', true);
          this.getDocumentDetail()
          if (this.inList) {
            this.$emit('submitApproveSuccess')
          } else {
            this.refreshFromTab()
          }
        } else {
          this.$platform.notification({
            title: res.message,
            type: 'error',
          });
        }
      } catch (err) {
        console.error(err);
      }
    },
    buildParams() {
      return  {
        title: this.detail.title,
        content: this.detail.content ?? this.detail.title, // 导入的附件文档没有content，用title代替
        desc: this.detail.desc,
        typeId: Number(this.detail.typeId),
        attachment: this.detail.attachment || [],
        setLabel: this.detail.labelObj?.label || null,
        changeLabel: this.detail.labelObj?.changeLabel || false,
        baseWikiSetting: this.detail.baseWikiSetting || null,
        wikiType: this.detail.wikiType,
        id: this.detail.id,
      }
    },
    async init() {
      document.title = this.$t('common.pageTitle.pageWikiV2Detail')
      this.shareEnable = await this.getWikiConfig(); // 获取知识库配置
      this.getId();
      this.addBrowseRecord();
    },
    openDialog(action) {
      if (action === 'edit') {
        const baseWikiSetting = this.detail.baseWikiSetting
        const params = {
          id: this.detail.id,
          wikiType: this.detail.wikiType,
          title: this.detail.title,
          content: this.detail.content ?? this.detail.title,
          desc: this.detail.desc,
          typeId: Number(this.detail.typeId),
          attachment: this.detail.attachment || [],
          importAttachment: this.detail.importAttachment || [],
          setLabel: this.detail.labelObj?.label || null,
          changeLabel: this.detail.labelObj?.changeLabel || false,
          options: this.isFault ? this.FaultLibrary : this.Library,
          allStaff: baseWikiSetting ? baseWikiSetting.visibleRange?.allStaff : true,
          userList: JSON.parse(baseWikiSetting?.visibleRange?.userList || '[]'),
          userIds: baseWikiSetting?.visibleRange?.userIds || [],
          roleIds: baseWikiSetting?.visibleRange?.roleIds || [],
          departmentIds: baseWikiSetting?.visibleRange?.departmentIds || [],
          allowExternalShare: baseWikiSetting?.externalSharing?.allowExternalShare || false,
          allowExternalPeopleViewComment: baseWikiSetting ? baseWikiSetting?.externalSharing?.allowExternalPeopleViewComment : true,
          commentPermission: baseWikiSetting ? baseWikiSetting?.externalSharing?.commentPermission : 1,
          allowDownload: baseWikiSetting ? baseWikiSetting?.externalSharing?.allowDownload : true,
        }
        this.$refs.editTitleDialog.openDialog(params);
      } else if (action == 'relationInfo') {
        this.$refs.relationInfoDialog.open()
      }
    },
    titleSubmitSuccess(params) {
      this.$emit('titleSubmitSuccess', params)
      this.getDocumentDetail()
    },
    selectSearchResult(item) {
      this.$emit('selectSearchResult', item)
    },
    //获得头像
    getHead() {
      const { head } = getRootWindowInitDataUser();
      this.headImage = head || avatarImage;
      return this.headImage;
    },
    handleCardClick(card) {
      console.log('Card clicked:', card);
    },
    tabBarChangeItem(item) {
      this.activeTab = item.tabName;
    },
    /**格式化人员 */
    formatApprovers() {
      return (this.detail.wikiApproveUsers || []).map(obj => obj.displayName).join('，');
    },
    wikiClick(e) {
      if (/^img$/i.test(e.target.nodeName)) {
        const img = e.target.currentSrc;
        this.$previewElementImg(img, [img]);
      }
    },
    async getWikiConfig() {
      try {
        let res = await RepositoryApi.getWikiConfig();
        return res?.result?.permitShare || false;
      } catch (e) {
        console.error(e);
        return false;
      }
    },
    async wikiShare(wiki) {
      // 点击分享的时候需要判断是否有分享权限
      let share = await this.getWikiConfig();
      if (!share) {
        this.$message.error(this.$t('wiki.detail.tips2'));
        return;
      }
      this.wikiListForShare = [wiki];
      let show = this.wikiListForShare.every(v => v.allowShare === 1);
      (this.$refs.documentShare || {}).shareDocument(show);
    },
    buildForm() {
      return {
        content: '',
        attachments: [],
        showInOwn: 0,
      };
    },

    getId() {
      if (this.currentWikiId) {
        this.wikiId = this.currentWikiId;
        this.getDocumentDetail();
        return;
      }
      if (window.location.href.indexOf('?') != -1) {
        let array = window.location.href.split('?')[1].split('&');
        let params = [];
        array.forEach(item => {
          params.push({ name: item.split('=')[0], value: item.split('=')[1] });
        });

        let shenpi = false;
        params.forEach(item => {
          if (item.name == 'wikiId') {
            this.wikiId = item.value;
          }
          if (item.name == 'objId') {
            this.wikiId = item.value;
          }
          if (item.name == 'id') {
            this.wikiId = item.value;
          }
          if (item.name == 'action' && item.value == 'approve') {
            this.isReview = true;
          }
          if (item.name == 'from' && item.value == 'approve') {
            shenpi = true;
          }
        });
        if (!shenpi && !this.initData.userInfo.authorities.INFO_VIEW && !this.initData.userInfo.authorities.INFO_COMPILER && !this.initData.userInfo.authorities.VIP_INFO_CREATE) {
          this.deleteMsg = this.$t('wiki.detail.tips3');
          this.detailShow = false;
          return;
        }
        this.getDocumentDetail();
        // this.getFileOperationAuth()
      }
    },

    async getApproveDetail() {
      try {
        let params = {
          objId: this.currWikiId,
        };
        let res = await RepositoryApi.getApprove(params);

        if (res.success) {
          this.approveData = res.result;
          this.approveData.createTime = formatDate(this.approveData.createTime);
          this.approveData.approvers.forEach(item => {
            if (item.userId == this.initData.userInfo.userId) this.showDetailApprove = true;
          });
          this.approveData.isList = this.isList;
          this.approveData.inList = this.inList;
          if (this.detail.originalId) {
            this.approveData.wikiId = this.detail.originalId;
          }
          if (this.approveData.proposer == this.initData.userInfo.userId) {
            this.revokeShow = true;
          }
        } else {
          this.$platform.notification({
            title: res.message,
            type: 'error',
          });
        }
      } catch (err) {
        console.error(err);
      }
    },
    getDetailTypes() {
      if (this.detail.wikiType === 1) {
        this.getFaultTypes();
      } else {
        this.getTypes();
      }
    },

    // 获取文档库详情
    async getDocumentDetail() {
      try {
        let params = {
          wikiId: this.currWikiId,
          updateReadTimes: true,
        };
        this.detailShow = true;
        if (!params.wikiId) {
          this.detail = null;
          this.detailShow = false;
          return;
        }
        this.loading = true;
        let res = await RepositoryApi.getInlineDetail(params);
        this.loading = false;

        if (res.success) {
          // TODO-bdz 中文判断
          if (res.message == '已删除') {
            this.detail = null;
            this.deleteMsg = this.$t('wiki.detail.tips4');
            this.detailShow = false;
          } else {
            this.getWikiData();
            this.detail = res.result;
            this.activeTab = this.tabBarList[0]?.tabName || '';

            // 根据知识库类型，获取对应的目录菜单
            this.getDetailTypes()

            let authorities = this.initData.userInfo.authorities;
            if (authorities.INFO_COMPILER || authorities.INFO_DELETE) {
              this.allowDelete = !!authorities.INFO_DELETE;
            } else {
              if (this.detail.createUser == this.initData.userInfo.userId && authorities.VIP_INFO_CREATE) {
                this.allowDelete = true;
              } else {
                this.allowDelete = false;
              }
            }
            if (this.detail.createUser == this.initData.userInfo.userId && authorities.VIP_INFO_CREATE && this.detail.isDraft === 1) {
              this.allowDraftEdit = true;
            }
            if (this.isReview) {
              this.getApproveDetail();
              this.approve();
              return;
            }
            if (this.detail.examineState && this.detail.examineState == 1) {
              this.getApproveDetail();
            }
          }
        } else {
          this.$platform.notification({
            title: res.message,
            type: 'error',
          });
        }
      } catch (err) {
        console.error(err);
        this.loading = false;
      } finally {
        // 列表加载详情时不修改title
        if (this.currentWikiId) return;
        this.$platform.setTabTitle({
          id: window.frameElement.dataset.id,
          title: this.detail?.title || this.$t('wiki.detail.wikiDetail'),
        });
      }
    },
    refreshFromTab() {
      let fromid = window?.frameElement?.getAttribute('fromid');
      if (fromid) {
        this.$platform.refreshTab(fromid);
      }
    },

    // 新页面打开通知公告详情
    openFrame() {
      this.moreListPopoverVisible = false;
      let fromId = window.frameElement.getAttribute('id');

      // this.$platform.openTab({
      //   id: `document_detail_${this.detail.id}`,
      //   title: '知识库列表',
      //   url: `/wiki/detail/page?wikiId=${this.detail.id}`,
      //   reload: true,
      //   close: true,
      //   fromId,
      // });
      openAccurateTab({
        type: PageRoutesTypeEnum.PageWikiV2Detail,
        key: this.detail.id,
        params: `wikiId=${this.detail.id}`,
        reload: true,
        fromId,
      });
    },

    // 编辑文章操作，查询详情接口，有人正在编辑提示不跳转
    async editArticle() {
      try {
        let params = {
          wikiId: this.currWikiId,
          updateReadTimes: false,
        };
        let res = await RepositoryApi.getInlineDetail(params);

        if (res.success) {
          let detail = res.result;

          if (detail.isLock && detail.examineState != 1) {
            this.$platform.notification({
              title: this.$t('wiki.detail.title1'),
              type: 'error',
            });
          } else {
            let fromId = window.frameElement.getAttribute('id');

            // this.$platform.openTab({
            //   id: `wiki_create_${params.wikiId}`,
            //   title: '知识库编辑',
            //   url: `/wiki/edit/page?wikiId=${params.wikiId}`,
            //   reload: true,
            //   close: true,
            //   fromId,
            // });
            openAccurateTab({
              type: this.detail.wikiType ? PageRoutesTypeEnum.PageWikiEdit : PageRoutesTypeEnum.PageWikiV2Edit,
              key: params.wikiId,
              params: `wikiId=${params.wikiId}&wikiType=${this.detail.wikiType}`,
              reload: true,
              fromId,
            });
          }
        } else {
          this.$platform.notification({
            title: res.message,
            type: 'error',
          });
        }
      } catch (err) {
        console.error(err);
      }
    },
    deleteLLMWiki(ids) {
      const params = {
        ids,
        tenantId: rootWindowInitData?.user?.tenantId,
      };

      deleteWiki(params).then(result => {});
    },
    // 删除文章
    async deleteArticle() {
      this.moreListPopoverVisible = false;
      try {
        if (!(await this.$platform.confirm(this.$t('common.base.tip.areYouSureYouWantDeletIt')))) return;

        let params = {
          wikiIds: [this.detail.id],
        };

        let res = await RepositoryApi.deleteDocument(params);

        if (res.success) {
          this.deleteLLMWiki(params.wikiIds);
          this.$emit('deleteSuccess')

          this.$platform.notification({
            title: '文章已删除成功。',
            type: 'success',
          });

          if (!this.isList && !this.inList) {
            let id = window.frameElement.dataset.id;
            this.$platform.closeTab(id);

            let fromId = window.frameElement.getAttribute('id');
            // this.$platform.openTab({
            //   id: 'M_INFO_DOC',
            //   title: '知识库列表',
            //   url: '/wiki',
            //   reload: true,
            //   close: true,
            //   fromId,
            // });
            openAccurateTab({
              type: PageRoutesTypeEnum.PageWikiV2List,
              reload: true,
              fromId,
            });
          } else {
            if (this.info.isLast) {
              this.$parent.resetPageNum();
            } else {
              this.$emit('search');
            }
          }
        } else {
          this.$platform.notification({
            title: res.message,
            type: 'error',
          });
        }
      } catch (e) {
        console.error(e);
      }
    },

    search() {
      this.$emit('search');
    },

    approve() {
      this.$refs.approveDialog.open();
    },
    // 审批成功
    approveSuccess() {
      this.getDocumentDetail()
      this.$emit('approveSuccess')
    },

    async revoke() {
      try {
        if (!(await this.$platform.confirm(this.$t('common.base.tip.areYouSureYouWantWithdrawIt')))) return;

        let params = {
          id: this.approveData.id,
        };

        let res = await RepositoryApi.revoke(params);

        if (res.success) {
          if (!this.isList && !this.inList) {
            window.location.reload();
          } else {
            this.$emit('search');
            this.$emit('operateSuccess')
          }
        } else {
          this.$platform.notification({
            title: res.message,
            type: 'error',
          });
        }
      } catch (e) {
        console.error(e);
      }
    },

    // TalkingData事件埋点
    trackEventHandler(type) {
      if (type === 'delete') {
        window.TDAPP.onEvent('pc：知识库详情-删除事件');
        return;
      }
      if (type === 'share') {
        window.TDAPP.onEvent('pc：知识库详情-分享事件');
        return;
      }
      if (type === 'open') {
        window.TDAPP.onEvent('pc：知识库详情-新页面打开');
        return;
      }
    },
    copyArticle() {
      openTabForWikiCreate({ copyId: this.currWikiId, wikiType: this.detail.wikiType });
    },

    addCollection() {
      if (this.collectionLoading) return;
      this.collectionLoading = true;
      RepositoryApi.addCollection({
        wikiId: this.currWikiId,
      })
        .then(res => {
          if (res.code === 0) {
            this.$platform.notification({
              title: this.$t('wiki.detail.tips5'),
              type: 'success',
            });
            this.getWikiData();
            this.$emit('addCollectionSuccess', this.currWikiId)
          } else {
            this.$platform.notification({
              title: res.message,
              type: 'error',
            });
          }
        })
        .finally(() => {
          this.collectionLoading = false;
        });
    },

    cancelCollection() {
      if (this.collectionLoading) return;
      this.collectionLoading = true;
      RepositoryApi.cancelCollection({
        wikiId: this.currWikiId,
      })
        .then(res => {
          if (res.code === 0) {
            this.$platform.notification({
              title: this.$t('wiki.detail.tips6'),
              type: 'success',
            });
            this.getWikiData();
            this.$emit('cancelCollectionSuccess', this.currWikiId)
          } else {
            this.$platform.notification({
              title: res.message,
              type: 'error',
            });
          }
        })
        .finally(() => {
          this.collectionLoading = false;
        });
    },

    wikiLike() {
      if (this.likeLoading) return;
      this.likeLoading = true;
      RepositoryApi.wikiLike({
        wikiId: this.currWikiId,
      })
        .then(res => {
          if (res.code === 0) {
            this.$platform.notification({
              title: this.$t('wiki.detail.tips7'),
              type: 'success',
            });
            this.getWikiData();
            this.$emit('likeSuccess', this.currWikiId)
          } else {
            this.$platform.notification({
              title: res.message,
              type: 'error',
            });
          }
        })
        .finally(() => {
          this.likeLoading = false;
        });
    },

    wikiCancelLike() {
      if (this.likeLoading) return;
      this.likeLoading = true;
      RepositoryApi.wikiCancelLike({
        wikiId: this.currWikiId,
      })
        .then(res => {
          if (res.code === 0) {
            this.$platform.notification({
              title: this.$t('wiki.detail.tips8'),
              type: 'success',
            });
            this.getWikiData();
            this.$emit('cancelLikeSuccess', this.currWikiId)
          } else {
            this.$platform.notification({
              title: res.message,
              type: 'error',
            });
          }
        })
        .finally(() => {
          this.likeLoading = false;
        });
    },

    getWikiData() {
      RepositoryApi.getWikiData({
        id: this.currWikiId,
      }).then(res => {
        if (res.code === 0) {
          this.wikiCountData = res.result?.times || {};
          this.wikiStateData = res.result?.state || {};
        } else {
          this.$platform.notification({
            title: res.message,
            type: 'error',
          });
        }
      });
    },

    getFileOperationAuth() {
      RepositoryApi.getFileOperationAuth({
        id: this.currWikiId,
      })
        .then(res => {
          if (res.code === 0) {
            this.operationAuth = res.result || {};
          } else {
            this.$platform.notification({
              title: res.message,
              type: 'error',
            });
          }
        })
        .catch(err => {
          console.log(err);
        });
    },

    // 增加个人浏览记录
    addBrowseRecord() {
      RepositoryApi.addBrowseRecord({
        wikiId: this.currWikiId,
      }).catch(err => {
        console.error(err);
      });
    },

    // 获取知识空间分类二级树状结构
    async getTypes() {
      let res = await RepositoryApi.getDocumentTypes();
      if (res.success) {
        this.Library = res.result;
      }
    },
    // 获取故障库分类二级树状结构
    async getFaultTypes() {
      let res = await RepositoryApi.getDocumentTypes({
        type: 1,
      });
      if (res.success) {
        this.FaultLibrary = res.result;
      }
    },
    handleMoveSuccess() {
      this.$emit('moveSuccess')
    },
    handleLikeTimes(num) {
      if (!num) return this.$t('wiki.detail.like')
      if (num > 999) num = '999+'
      return this.$t('wiki.detail.sbLike', {num})
    },
    handleCollectionTimes(num) {
      if (!num) return this.$t('wiki.detail.collection')
      if (num > 999) num = '999+'
      return this.$t('wiki.detail.sbCollection', {num})
    },
    formatTimes(num) {
      if (Number(num) > 999) return '999+'
      return num || 0
    },
  },
  computed: {
    tabBarList() {
      return [
        {
          tabName: 'more-recomment',
          disabled: true,
          tabLabel: i18n.t('wiki.detail.recommendBox.title1'),
          tabShow: this.recommendShow,
          position: 'right',
          needTrack: true,
          undefined: true,
        },
        {
          tabName: 'comment-box',
          disabled: true,
          tabLabel: i18n.t('wiki.detail.articleComment'),
          tabShow: this.commentShow && this.commentPermission,
          position: 'right',
          needTrack: true,
          undefined: true,
        },
      ].filter(item => item.tabShow)
    },
    // 当前选择的是否是故障库
    isFault() {
      return this.detail.wikiType === 1;
    },
    allowCreate() {
      return this.initData?.userInfo.authorities.VIP_INFO_CREATE;
    },
    allowEdit() {
      return this.initData?.userInfo.authorities.INFO_COMPILER;
    },
    height() {
      return this.isList ? '100%' : '100vh';
    },

    fontClass() {
      return document.body.clientWidth > 1800 ? 'font-class' : '';
    },

    // 联客商城灰度开关
    linkControl() {
      // return this.initData.openLinkC;
      const RootWindow = getRootWindow(window);
      return this.initData.openLinkC || RootWindow.grayAuth?.portal || false;
    },
    isShowSelfServicePortal() {
      return isShowSelfServicePortal();
    },
    currWikiId() {
      return this.info.id ?? this.wikiId ?? this.currentWikiId ?? this.$route.query.wikiId ?? null;
    },
    importAttachment() {
      return this.detail?.importAttachment || []
    },
    isXlsx() {
      const fileName = this.importAttachment[0]?.filename || ''
      if (!fileName) return false
      // 文件后缀
      const fileSuffix =  fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase() || ''
      return ['xlsx'].includes(fileSuffix)
    },
    commentShow() {
      return this.initData?.wikiConfig?.comment;
    },
    recommendShow() {
      return this.initData?.wikiConfig?.relevantRecommend;
    },
    // 是否允许评论
    commentPermission() {
      // baseWikiSetting为null时默认为1
      if (!this.detail?.baseWikiSetting) return 1;
      return this.detail?.baseWikiSetting?.externalSharing?.commentPermission || 0
    },
    // 允许下载
    allowDownload() {
      const allowDownload = this.detail?.baseWikiSetting ? this.detail?.baseWikiSetting?.externalSharing?.allowDownload : true
      return this.wikiDownloadAuth && allowDownload
    },
    authorities() {
      return this.initData?.userInfo?.authorities || {};
    },
    showRelationBtn() {
      return this.detail.relationInfo?.length || this.detail.productTypeList?.length || this.detail.repairCostBegin || this.detail.repairCostEnd
    },
    classNames() {
      return {
        'document-detail-box': true,
        'width1000': this.rightWidth > 1160 && this.rightWidth < 1400,
        'width1200': this.rightWidth > 1400,
      }
    }
  },
  watch: {
    currentWikiId(newVal, oldVal) {
      if (newVal && newVal !== oldVal) {
        this.previewKey++;
        this.init();
      }
    },
  },
};
</script>

<style lang="scss">
.document-list-detail-wrap {
  overflow: hidden;
  height: 100%;
  .bbx-base-tab-bar-box .right-tab-selector {
    display: none !important;
  }
}
.title-main-text {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
  .el-tag {
    border-radius: 20px;
  }
  .biz-intelligent-tags__tagging-view {
    flex: none;
  }
  .max-title {
    max-width: 400px;
    font-size: 16px;
    font-weight: bold;
    @include text-ellipsis();
  }
}
.Fault {
  width: 100;
  .FaultList {
    margin-top: 8px;
    display: flex;
    label {
      flex: 0 1 80px;
      min-width: 80px;
    }
  }
}

.document-list-detail-v2 {
  display: flex;
  flex-direction: column;
  overflow: auto;
  height: 100%;
  padding: 12px;
  background: transparent;

  &.inList {
    padding: 0;
    height: 100% !important;
    .document-detail-box {
      max-width: 800px;
      min-width: 600px;
      &.width1000 {
        width: 1000px;
        max-width: 1000px;
      }
      &.width1200 {
        width: 1200px;
        max-width: 1200px;
      }
    }
    .detail-top {
      top: 0;
    }
  }

  .document-detail-box {
    background: #fff;
    border-radius: 4px;
    width: 100%;
    padding: 24px 32px;
    flex: 1;
    margin: 0 auto;
  }

  .detail-top {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    gap: 16px;
    padding: 12px 16px;
    background-color: #fff;
    border-bottom: 1px solid #e8eff0;
    position: sticky;
    top: -12px;
    z-index: 99;

    .author-info {
      display: flex;
      align-items: center;
      @include text-ellipsis();
    }
    .name, .time {
      font-size: 12px;
      margin: 0;
      color: #595959;
      line-height: 20px;
    }
    .time {
      display: flex;
      align-items: center;
      gap: 16px;
    }
    .operating {
      line-height: 40px;

      .published {
        display: inline-block;
      }

      .draftBox {
        display: inline-block;
        margin-right: 10px;
      }

      .icon-permission {
        font-size: 14px;
        color: #b0bcc3;
        margin-right: 3px;
      }

      .management {
        .icon-edit {
          display: inline-block;
          width: 25px;
          height: 25px;
          font-size: 14px;
          color: $color-primary-light-6;
        }

        .icon-delete {
          display: inline-block;
          width: 25px;
          height: 25px;
          font-size: 16px;
          color: $color-primary-light-6;
        }
      }

      .icon-operating {
        display: inline-block;
        width: 30px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        font-size: 16px;
        color: $color-primary-light-6;

        cursor: pointer;
      }

      .share {
        display: inline-block;

        width: 20px;
        height: 20px;
        line-height: 20px;

        cursor: pointer;

        .icon-article-share {
          font-size: 16px;
          color: $color-primary-light-6;
        }
      }

      .open {
        display: inline-block;
        height: 30px;
        line-height: 30px;
        margin-left: 10px;
        color: $color-primary-light-6;

        cursor: pointer;
      }

      .green-btn {
        background: #55b7b4;
        border: transparent;
        margin-left: 10px;
      }

      .white-btn {
        background: #fff;
        color: #333;
        border: 1px solid #e2e2e2;

        &:hover {
          @include borderColorPrimaryLight5();
          @include backgroundColorPrimaryLight1();
          color: #fff;
        }
      }
    }
  }
  .detail-header-left {
    max-width: 75%;
    overflow: hidden;
  }
  .title-label {
    margin-left: 8px;
    display: inline;
    vertical-align: middle;
    span {
      background: $color-primary-light-1;
      border-radius: 4px;
      border: 1px solid $color-primary-light-1;
      height: 22px;
      line-height: 22px;
      font-size: 12px;
      font-weight: 400;
      color: $color-primary-light-6;
      display: inline-block;
      box-sizing: border-box;
      padding: 0 8px;
      margin-left: 4px;
      vertical-align: text-top;
    }
  }

  .detail-content {
    flex: 1;
    overflow: hidden;
    // padding: 0 100px 16px 100px;
    .info {
      .detail-desc {
        padding: 8px;
        border-radius: 8px;
        background: #F5F8FA;
        font-size: 12px;
        line-height: 16px;
        color: #595959;
      }
      .title {
        margin: 0;
        padding: 16px 0 13px;
        display: inline-block;
        font-size: 18px;
        font-weight: bold;
        color: #262626;
        line-height: 25px;
        .title-label-tags {
          font-weight: normal;
        }
      }
      .title-main-tags {
        display: inline-flex;
        width: auto;
        gap: 4px;
        line-height: 25px;
        vertical-align: middle;
        .biz-intelligent-tags__tagging-view {
          flex-shrink: 0;
          font-weight: normal;
        }
      }

      .content {
        padding-bottom: 16px;
        word-break: break-all;

        p > img {
          max-width: 100%;
          height: auto;
        }
        p > video {
          max-width: 100%;
          object-fit: contain;
          height: auto !important;
        }

        // p {
        //   line-height: 28px;
        // }
      }

      .font-class {
        font-size: 16px !important;

        p {
          line-height: 30px;
        }
      }
    }
    .file-list {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
    }
    .wiki-attachment .file-list {
      min-height: 400px;
      justify-content: center;
      align-items: center;
      .file-item {
        width: 300px;
        height: 150px;
        padding: 16px 24px;
        flex-direction: column;
        justify-content: center;
        cursor: pointer;
        img {
          width: 48px;
          height: 48px;
          margin-bottom: 12px;
          margin-right: 0;
        }
        .file-info {
          flex: none;
          text-align: center;
          width: 100%;
          white-space: nowrap;
          .file-name {
            font-size: 14px;
            line-height: 22px;
          }
          .file-size {
            font-size: 12px;
            line-height: 16px;
          }
        }
      }
    }
    .file-item {
      width: 240px;
      padding: 8px;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      position: relative;
      border-radius: 4px;
      background: #F0F2F5;
      &:last-child {
        margin-right: 0;
      }
      &:hover {
        .file-name {
          color: $color-primary;
        }
        .iconfont {
          display: block;
        }
      }
      img {
        width: 28px;
        height: 28px;
      }
      .file-info {
        flex: 1;
        overflow: hidden;
        .file-name {
          @include text-ellipsis();
          font-size: 12px;
          line-height: 16px;
          cursor: pointer;
        }
        .file-size {
          font-size: 10px;
          line-height: 12px;
          color: #8C8C8C;
        }
      }
      .iconfont {
        color: #8c8c8c;
        cursor: pointer;
        display: none;
      }
    }

    .footer {
      padding: 12px 0;
      margin-bottom: 16px;
      border-top: 1px solid #E4E7ED;

      .tags {
        // display: inline-block;
        vertical-align: top;
        font-size: 0;
        padding-bottom: 10px;

        .icon-tags {
          vertical-align: middle;
          font-size: 16px;
          color: #b0bcc3;
        }

        .detail-tag {
          vertical-align: middle;
          max-width: 76px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;

          margin-left: 4px;
          border: none;
          background: #e8eff0;
          color: #606266;
        }
      }

      .dividing-line {
        // display: inline-block;
        height: 22px;
        width: 1px;
        background: #848e92;
        margin: 0 20px;
        vertical-align: top;
      }
    }
  }
}

.empty {
  text-align: center;
  padding-top: 100px;
  height: 100vh;
  background: #ffffff;

  .empty-img {
    width: 160px;
    height: 160px;
  }

  .empty-msg {
    display: block;
    padding-top: 8px;
    font-size: $font-size-base;
    color: $text-color-regular;
  }
}

.base-file-del {
  display: none;
}

.detail-attrs {
  display: flex;
  width: 550px;
  flex-wrap: wrap;
  margin-left: 24px;
  justify-content: space-between;
  &-each {
    // width: 49%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-size: 14px;
    line-height: 22px;
  }
  .approvers {
    cursor: pointer;
    .iconfont {
      color: #8c8c8c;
      &:hover {
        color: #595959;
      }
    }
  }
}
.link {
  color: $color-primary-light-6;
  cursor: pointer;
}

.tinymce-editor {
  padding: 0;
  .wiki-content {
    font-size: 14px;
    font-weight: 400;
    color: #262626;
    line-height: 26px;
    p {
      font-size: 14px;
      font-weight: 400;
      color: #262626;
      line-height: 26px;
    }
    div, p, span {
      white-space: inherit !important;
    }
    img {
      cursor: pointer;
      max-width: 100%;
    }
  }
}
.detail-attrs-catalog {
  display: flex;
  &-main {
    direction: rtl;
    flex: 1;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}

.detail-top-left {
  display: flex;
}
.detail-top-right {
  display: flex;
}

.icon-quanzi.active {
  color: $color-primary-light-6;
}

.management {
  font-size: 0;
}
.header-right-icon-each {
  margin-right: 12px;
  .iconfont {
    font-size: 18px;
  }
  .iconfont:hover {
    color: $color-primary-light-6;
  }
  .iconfont:focus {
    color: $color-primary-light-6;
  }
}
.icon-ellipsis:hover {
  color: $color-primary-light-6;
}

.detail-status {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  color: #262626;
  &.status1 {
    color: #ff9200;
  }
  &.status2 {
    color: #ff4d4f;
  }
}

.detail-content {
  .status-view {
    text-align: center;
    &-each-v2 {
      color: #595959;
      line-height: 20px;
    }
    .canClick {
      cursor: pointer;
    }
    b {
      font-size: 14px;
      font-weight: 400;
      vertical-align: middle;
    }
    .iconfont {
      font-size: 24px;
      font-weight: 400;
      vertical-align: middle;
      &:before {
        margin-right: 4px;
      }
    }
    .icon-like-fill {
      color: #ff7043;
    }
    .icon-yishoucang {
      color: $color-primary-light-6;
    }
  }
}

.more-item {
  font-size: 14px;
  font-weight: 400;
  color: #666666;
  line-height: 34px;
  cursor: pointer;
  padding: 0 20px;
  input[type='file']{
    display: none !important;
  }
  &:hover {
    color: $color-primary;
    background: #ECF5FF;
  }
}
.check-btns {
  font-size: 0;
}
.management .iconfont {
  cursor: pointer;
}

.detail-attrs-each {
  color: #595959;
  margin-right: 4px;
}
.outsideStatus {
  span > b {
    font-weight: normal;
    color: #8c8c8c;
    font-size: 12px;
  }
}
.detail-page-header {
  padding: 12px 16px;
  /* height: 70px; */
  display: flex;
  justify-content: space-between;
  box-shadow: 0px 1px 0px 0px #f4f7f5;
  background-color: white;
  position: relative;
  z-index: 100;
}
.detail-header-right {
  display: flex;
  align-items: flex-start;
}
.setting-btn {
  width: 32px;
  height: 32px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  ::v-deep span {
    transform: rotate(90deg);
  }
}
</style>

<style lang="scss">
.more-list-popover {
  padding: 0 0 10px;
  .iconfont {
    font-size: 18px;
  }
}
.more-approve {
  padding: 10px 12px;
}
</style>