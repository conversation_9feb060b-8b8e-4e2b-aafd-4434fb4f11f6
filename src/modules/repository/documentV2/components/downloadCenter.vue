<!--  -->
<template>
  <div class="download-center-box">
    <div class="download-title-box flex-x al-c" :class="isNoTask || isSuccess ? 'download-success' : ''">
      <div class="status-icon-box mar-r-8" :class="isDownloading ? 'uploading-icon' : ''">
        <i v-if="isDownloading" class="iconfont icon-sync c-primary"></i>
        <i v-if="isNoTask || isSuccess" class="iconfont icon-chenggong c-success"></i>
      </div>

      <div class="flex-1 overHideCon-1">
        <template v-if="isDownloading"> {{ $t('common.base.tip.uploading') }} {{ `${successFileCount}/${taskList.length}` }} </template>
        <template v-else-if="isNoTask || isSuccess">
          <div class="">{{ $t('common.base.tip.uploadSuccess') }} {{ `${successFileCount}/${taskList.length}` }}</div>
        </template>
      </div>
      <div class="operation-box flex-x al-c mar-l-8">
        <div class="cur-point flex-x al-c" @click="showList = !showList">
          <i class="iconfont font-14" :class="showList ? 'icon-up1' : 'icon-down'"></i>
        </div>

        <i class="iconfont icon-close just-cur-point font-14 mar-l-8" @click="clearCenter"></i>
      </div>
    </div>
    <div class="download-task-list-box" v-show="showList">
      <DownloadCenterItem v-for="(item, index) in taskList" :key="index" :item-data="item" />
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, computed } from 'vue';
import { t } from '@src/locales';
import { documentV2Store } from '@src/modules/repository/documentV2/store';
import { FileDownloadStatusEnum, FileTaskControllerStatusEnum } from '@src/modules/repository/documentV2/model';
import DownloadCenterItem from '@src/modules/repository/documentV2/components/downloadCenterItem.vue';
import { MessageBox } from 'element-ui';
export default defineComponent({
  components: { DownloadCenterItem },
  setup(props, { emit }) {
    const showList = ref(false);
    const taskList = computed(() => documentV2Store.getDownloadTaskList());

    const successFileCount = ref(0);
    const status = computed(() => {
      if (!taskList.value.length) {
        return FileTaskControllerStatusEnum.NoTask;
      }
      let status_;
      let successFile = 0;
      let i;

      for (i = 0; i < taskList.value.length; i++) {
        const fileItem = taskList.value[i];
        if (fileItem.downloadType === FileDownloadStatusEnum.Downloading || fileItem.downloadType === FileDownloadStatusEnum.WaitDownload) {
          if (fileItem.progress !== 100) {
            status_ = FileTaskControllerStatusEnum.Downloading;
            break;
          } else {
            successFile++;
          }
        }
      }
      if (i == taskList.value.length) {
        status_ = successFile > 0 ? FileTaskControllerStatusEnum.Success : FileTaskControllerStatusEnum.NoTask;
        successFileCount.value = successFile;
      }

      return status_;
    });
    const isDownloading = computed(() => status.value === FileTaskControllerStatusEnum.Downloading);
    const isNoTask = computed(() => status.value === FileTaskControllerStatusEnum.NoTask);
    const isSuccess = computed(() => status.value === FileTaskControllerStatusEnum.Success);
    function clearCenter() {
      if (isDownloading.value) {
        MessageBox.confirm(t('common.wiki.cancelUploadTips'), t('common.base.toast')).then(res => {
          documentV2Store.deleteDownloadTaskList();
        });
      } else {
        documentV2Store.deleteDownloadTaskList();
      }
    }
    return {
      taskList,
      isDownloading,
      isNoTask,
      isSuccess,
      successFileCount,
      showList,
      clearCenter,
    };
  },
});
</script>
<style lang="scss">
.download-center-box {
  position: fixed;
  width: 360px;
  top: 0;
  right: 10px;
  z-index: 99;
  background-color: #fff;
  box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  .download-title-box {
    padding: 8px 16px;
  }
  .hide-list {
    transform: rotateZ(180deg);
  }
  .uploading-icon {
    -webkit-animation: uploading 2s linear infinite;
  }
  .download-success {
    background-color: #f0f9eb;
  }
  .c-primary {
    color: $color-primary;
  }
  .c-success {
    color: $color-success;
  }
}
.download-task-list-box {
  max-height: 500px;
  overflow-y: auto;
  border-top: 1px solid $border-color-base;
}

@-webkit-keyframes uploading {
  from {
    transform: rotateZ(180deg);
  }
  to {
    transform: rotateZ(-180deg);
  }
}
</style>
