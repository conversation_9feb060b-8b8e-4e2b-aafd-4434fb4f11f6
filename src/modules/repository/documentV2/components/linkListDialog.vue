<template>
  <div>
    <base-modal :title="$t('wiki.component.documentShare.linkList')" class="link-modal" width="500px" :show.sync="show">
      <div class="link-modal-content">
        <div class="link-item mar-b-12" v-for="item in linkList" :key="item.id">
          <div class="link-item-left">
            <!-- 文档类型 -->
            <div class="document-type mar-r-10"></div>
            <div class="document-content">
              <div class="document-title">{{ item.title }}</div>
              <div class="document-url">{{ item.url }}</div>
            </div>
          </div>
          <div class="link-item-right">
            <el-button type="plain-third" plain @click="outlineShare(item)">{{ $t('common.base.copyLink') }}</el-button>
            <div class="image-erweima mar-l-8">
              <i @click="openQr(item)" class="iconfont icon-erweima" style="font-size: 20px; color: #595959"></i>
            </div>
          </div>
        </div>
      </div>
    </base-modal>

    <qrCodeDialog :share-url="shareUrl" :shareTitle="shareTitle" ref="qrCodeDialog" />
  </div>
</template>

<script>
import Clipboard from 'clipboard';
import qrCodeDialog from './qrCodeDialog.vue';

export default {
  name: 'link-list-dialog',
  props: {
    linkList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      show: false,
      shareUrl: '',
      shareTitle: '',
    };
  },
  components: {
    qrCodeDialog,
  },
  methods: {
    open() {
      this.show = true;
    },
    openQr(item) {
      this.shareUrl = item.url;
      this.shareTitle = item.title;
      this.$refs.qrCodeDialog.open();
    },
    // 外部分享，将连接添加至剪切板
    outlineShare(item) {
      // 外部文章选择外部分享时
      let _this = this;
      let url = item.url;

      // 获取body
      let body = document.getElementsByTagName('body')[0];

      let copyFrom = document.createElement('a');
      copyFrom.setAttribute('id', 'target');
      copyFrom.setAttribute('target', '_blank');
      copyFrom.setAttribute('href', url);
      copyFrom.innerHTML = url;

      // 创建按钮
      let agent = document.createElement('button');
      // body增加超链接
      body.appendChild(copyFrom);
      // body增加按钮
      body.appendChild(agent); // 采用Clipboard.js方案 // trouble：没有可以传入的HTML元素，但我们可以动态创建一个DOM对象作为代理，复制超链接

      let clipboard = new Clipboard(agent, {
        target() {
          return document.getElementById('target');
        },
      });

      clipboard.on('success', function (e) {
        _this.$platform.notification({
          title: _this.$t('wiki.component.documentShare.tips1'),
          type: 'success',
        });
      });

      clipboard.on('error', function (e) {
        _this.$platform.notification({
          title: _this.$t('wiki.component.documentShare.tips2'),
          type: 'error',
        });
      });
      // 点击按钮
      agent.click();
      // 移除创建的元素
      body.removeChild(copyFrom);
      body.removeChild(agent);
    },
  },
};
</script>

<style lang="scss" scoped>
.link-modal-content {
  .link-item {
    display: flex;
    align-items: center;
    &-left {
      display: flex;
      align-items: center;
      flex: 1;
      .document-content {
        flex: 1;
        @include text-ellipsis();
      }
      .document-title {
        line-height: 20px;
        font-weight: 500;
      }
      .document-url {
        font-size: 12px;
        line-height: 16px;
        color: #8c8c8c;
      }
    }
    &-right {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      .image-erweima {
        width: 32px;
        height: 32px;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        border: 1px solid #cbd6e2;
        border-radius: 4px;
        background: #f5f8fa;
        cursor: pointer;
      }
    }
  }
}
</style>
