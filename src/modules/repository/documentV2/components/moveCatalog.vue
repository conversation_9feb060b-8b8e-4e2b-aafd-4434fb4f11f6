<template>
  <el-dialog custom-class="moveDialog" :visible.sync="visible" width="500px" :title="$t('wiki.component.moveCatalog.title1')">
    <div class="select-count mar-b-12" v-if="isBatchOperateStatus">{{ $t('wiki.list.selectCount', { num: selection.length }) }}</div>
    <div class="move-title">{{ $t('wiki.component.moveCatalog.title1') }}</div>
    <div class="url-input mar-b-12">
      <el-input v-model="moveAddress" :disabled="true"> </el-input>
    </div>
    <el-tree ref="typeMoveTree" class="typeMoveTree" :data="getDataFromParent ? moveTreeDataFromProp : moveTreeData" node-key="id" :highlight-current="true" :props="treeProps" :filter-node-method="moveTreeFilter" @current-change="data => (currMoveChoose = data)" :expand-on-click-node="false">
      <span class="custom-tree-node" slot-scope="{ node, data }">
        <span class="node-class">
          <i
            :class="{
              'icon-fenlei1': !['published'].includes(node.key),
              'icon-yifabu': node.key === 'published',
            }"
            class="iconfont"
          >
          </i>
          <span>{{ node.label }}</span>
        </span>
      </span>
    </el-tree>

    <div slot="footer">
      <el-button
        @click="
          visible = false;
          currMoveChoose = void 0;
        "
        >{{ $t('common.base.cancel') }}</el-button
      >
      <el-button type="primary" :disabled="!currMoveChoose" :loading="moveLoading" @click="type === 'catalog' ? moveWikiType() : moveWiki()">{{ $t('common.base.move') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { isEmpty } from '@src/util/type';
import { moveWiki, batchMoveWiki, moveWikiType, getDocumentTypes } from '@src/api/Repository';

export default {
  name: 'move-catalog',
  props: {
    value: Boolean,
    // visible: Boolean,
    type: {
      // 'catalog', 'wiki'
      type: String,
      require: true,
    },
    beMoveData: Object, // 来源于列表或详情
    moveTreeDataFromProp: Array,
    getDataFromParent: {
      type: Boolean,
      default: false,
    },
    selection: {
      type: Array,
      default: () => [],
    },
    // 是否是批量操作
    isBatchOperateStatus: {
      type: Boolean,
      default: false,
    },
    currentTreeActiveId: {
      type: [String, Number],
      default: '',
    },
    // 详情移动时的层级
    typeName: {
      type: String,
      require: true,
    },
  },
  data() {
    return {
      visible: this.value,
      moveTreeData: [],
      treeProps: {
        label: 'name',
        children: 'subTypes',
      },
      currMoveChoose: void 0,
      moveLoading: false,
      moveAddress: '',
    };
  },
  watch: {
    value(newValue) {
      this.visible = newValue;
    },
    visible(newValue, oldValue) {
      this.$emit('input', newValue);
      if (newValue) {
        this.getTypes();
      } else {
        this.currMoveChoose = void 0;
      }
    },
    currMoveChoose(newValue, oldValue) {
      if (!this.moveAddress) {
        this.moveAddress = newValue.name;
      } else {
        const flag = oldValue?.subTypes?.filter(data => data.id === newValue.id) || [];
        if (!isEmpty(flag)) {
          this.moveAddress += '/' + newValue.name;
        } else {
          this.moveAddress = newValue.name;
        }
      }
    },
  },
  mounted() {
    if (!this.moveAddress) {
      // 详情移动时默认显示的层级
      if (this.typeName) {
        this.moveAddress = this.typeName
      }
      // 列表批量移动并且不是第一级时默认显示的层级
      if (this.isBatchOperateStatus && this.currentTreeActiveId !== 'published') {
        this.moveAddress = this.selection?.[0]?.type
      }
    }
    if (!this.getDataFromParent) {
      this.getTypes();
    } else if (this.type === 'catalog') {
      // 去掉树中当前待移动的分类
      this.$nextTick(() => {
        this.$refs.typeMoveTree.remove(this.beMoveData);
      });
      this.$watch('moveTreeDataFromProp', (newValue, oldValue) => {
        if (newValue) {
          this.$nextTick(() => {
            this.$refs.typeMoveTree.remove(this.beMoveData);
          });
        }
      });
    }
  },
  methods: {
    // 底下有分类的时候需要一同过滤，否则无法过滤~
    moveTreeFilter(value, data, node) {
      return data.id !== this.beMoveData.id;
    },
    // 移动分类
    async moveWikiType() {
      // 批量操作移动的是文章
      if (this.isBatchOperateStatus) return this.moveWiki();
      try {
        let operationId = this.beMoveData?.id;
        let targetId = this.currMoveChoose?.id;
        if (operationId === targetId) {
          this.$platform.notification({
            title: this.$t('wiki.component.moveCatalog.title2'),
            type: 'error',
          });
          return;
        }
        let params = {
          operationId,
          targetId: targetId === 'published' ? void 0 : targetId,
        };
        this.moveLoading = true;
        let res = await moveWikiType(params);

        if (res.success) {
          this.$platform.notification({
            title: this.$t('wiki.component.moveCatalog.tips2'),
            type: 'success',
          });
          // this.$emit('closeVisible')
          this.visible = false;
          this.currMoveChoose = void 0;
          this.$emit('moveEnd');
          // await this.getTypes()
        } else {
          this.$platform.notification({
            title: res.message,
            type: 'error',
          });
        }
      } catch (e) {
        console.error(e);
      }
      this.moveLoading = false;
    },
    // 移动文章
    async moveWiki() {
      try {
        // TODO: 待联调
        let wikiId = this.beMoveData?.id;
        let wikiTypeId = this.currMoveChoose?.id;

        if (this.beMoveData?.typeId === wikiTypeId || (this.isBatchOperateStatus && this.currentTreeActiveId === wikiTypeId)) {
          this.$platform.notification({
            title: this.$t('wiki.component.moveCatalog.tips3'),
            type: 'error',
          });
          return;
        }
        let params = {
          wikiId,
          wikiTypeId,
        };

        if (this.isBatchOperateStatus) {
          params = {
            typeId: wikiTypeId,
            ids: this.selection.map(item => item.id)
          }
        }

        this.moveLoading = true;
        let fn = this.isBatchOperateStatus ? batchMoveWiki : moveWiki;
        let res = await fn(params);

        if (res.success) {
          this.$platform.notification({
            title: this.$t('wiki.component.moveCatalog.tips5'),
            type: 'success',
          });
          this.visible = false;
          this.currMoveChoose = void 0;
          this.$emit('moveEnd');
        } else {
          this.$platform.notification({
            title: res.message,
            type: 'error',
          });
        }
      } catch (e) {
        console.error(e);
      }
      this.moveLoading = false;
    },
    // 获取分类二级树状结构
    async getTypes() {
      return new Promise(async (resolve, reject) => {
        try {
          let res = await getDocumentTypes();
          if (res.success) {
            this.moveTreeData = res.result;
            resolve();
          } else {
            this.$platform.notification({
              title: res.message,
              type: 'error',
            });
            reject();
          }
        } catch (err) {
          console.error(err);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .moveDialog {
  .select-count {
    line-height: 38px;
    padding: 0 16px;
    border-radius: 4px;
    color: #e6a23c;
    background: #fcf6ec;
  }
  .move-title {
    line-height: 32px;
  }
  .el-tree {
    height: 200px;
    overflow: auto;
    .el-tree-node__content {
      height: 40px;
      line-height: 40px;
      &:hover {
        background: #e6fffb;
      }
    }
    .el-tree-node.is-current > .el-tree-node__content {
      background: #e6fffb;
    }
    .el-tree-node:focus > .el-tree-node__content {
      background: fff;
    }
  }
}
::v-deep .typeMoveTree {
  height: 256px !important;
  border: 1px solid #e4e7ed;
  .el-tree-node__content > .el-tree-node__expand-icon:not(.is-leaf) {
    color: #595959;
  }
  .custom-tree-node {
    display: flex;
    align-items: center;
    flex: 1;
    overflow: hidden;
    .node-class {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      color: #262626;
      font-size: 0;
      height: 40px;
      line-height: 40px;
      span {
        margin-left: 4px;
        font-size: 14px;
      }
    }
  }
}
</style>
