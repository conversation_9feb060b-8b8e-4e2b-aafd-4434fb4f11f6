<!--  -->
<template>
  <div class="download-task-item">
    <div class="flex-x al-c download-task-item-box">
      <div class="download-task-file-box mar-r-8">
        <img class="file-icon-list" :src="findFileIconUrl(name)" />
      </div>
      <div class="download-task-item-content flex-1">
        <div class="overHideCon-1">{{ name }}{{ itemData.downloadType }}</div>
        <div class="font-12 overHideCon-1" :class="isError ? 'c-danger' : ''">
          {{ showTips() }}
        </div>
      </div>
      <div class="download-task-status-box flex-x al-c">
        <i v-if="isDownloading && downloaded" class="iconfont icon-chenggong c-success"></i>
        <i v-else-if="isDownloading && !downloaded" class="iconfont icon-sync c-primary" :class="isDownloading && !downloaded ? 'uploading-icon' : ''"></i>
        <i v-else-if="isError" class="iconfont icon-close-circle"></i>
      </div>
    </div>
    <el-progress v-if="isDownloading || isWaitDownload" :percentage="nowProgress" :show-text="false" :stroke-width="2" :duration="10" />
  </div>
</template>

<script>
import { defineComponent, ref, computed } from 'vue';
import { FileDownloadStatusEnum } from '@src/modules/repository/documentV2/model';
import { findFileIconUrl } from '@src/modules/ai/views/edit/mock.ts';

export default defineComponent({
  props: {
    itemData: {
      type: [Object, File],
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    let interval;
    const downloaded = ref(false);
    const isError = computed(() => props.itemData.downloadType === FileDownloadStatusEnum.Error);
    const isDownloading = computed(() => {
      if (props.itemData.downloadType === FileDownloadStatusEnum.Downloading) {
        clearInterval(interval);
        interval = setInterval(() => {
          if (nowProgress.value == 100 || props.itemData.progress == 100) {
            nowProgress.value = 100;
            setTimeout(() => {
              downloaded.value = true;
            }, 300);

            return clearInterval(interval);
          }
          let addNumber = 5;
          if (nowProgress.value >= 50) {
            addNumber = 3;
          } else if (nowProgress.value >= 65) {
            addNumber = 2;
          } else if (nowProgress.value >= 85) {
            addNumber = 1;
          }
          if (nowProgress.value >= 98) {
            nowProgress.value = 98;
          } else {
            nowProgress.value = nowProgress.value + addNumber;
          }
        }, 300);
      }
      return props.itemData.downloadType === FileDownloadStatusEnum.Downloading;
    });
    const isWaitDownload = computed(() => props.itemData.downloadType === FileDownloadStatusEnum.WaitDownload);
    const nowProgress = ref(props.itemData.progress);

    function showTips() {
      if (isDownloading.value || isWaitDownload.value) {
        return props.itemData.path;
      }
      return props.itemData.message;
    }
    return {
      showTips,
      isError,
      isDownloading,
      isWaitDownload,
      nowProgress,
      name: props.itemData.name,
      findFileIconUrl,
      downloaded,
    };
  },
});
</script>
<style lang="scss" scoped>
.download-task-item-box {
  padding: 14px 16px;
}
.download-task-file-box,
.download-task-status-box {
  width: 32px;
  height: 32px;
}
.file-icon-list {
  width: 32px;
  height: 32px;
}
.c-primary {
  color: $color-primary;
}
.c-danger {
  color: $color-danger;
}
.c-success {
  color: $color-success;
}
</style>
