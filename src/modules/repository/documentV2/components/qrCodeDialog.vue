<template>
  <el-dialog :title="$t('common.base.qrcode')" :visible.sync="visible" width="380px" top="35vh" :show-close="true" :close-on-click-modal="false">
    <div>
      <div class="share-modal" id="qrcode" ref="qrcode"></div>
    </div>
    <div slot="footer">
      <el-button @click="visible = false">{{ $t('common.base.cancel') }}</el-button>
      <el-button type="primary" @click="downloadQrcode()">{{ $t('portal.saveQrCode') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import QRCode from 'qrcodejs2';

export default {
  name: 'qrCodeDialog',
  props: {
    shareUrl: {
      type: String,
      default: '',
    },
    shareTitle: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      visible: false,
    };
  },
  watch: {
    shareUrl(newValue) {
      if (newValue) {
        // this.createQrcode()
      }
    },
  },
  mounted() {
    // this.createQrcode()
  },
  methods: {
    open() {
      this.visible = true;
      this.$nextTick(() => {
        this.createQrcode();
      });
    },
    /* 下载二维码 */
    downloadQrcode() {
      let qrcode = document.getElementById('qrcode');
      let canvas = qrcode.getElementsByTagName('canvas');

      // 创建a标签，将canvas变成png图片
      let a = document.createElement('a');
      a.href = canvas[0].toDataURL('image/png');
      // 设置下载文件的名字
      a.download = this.shareTitle || this.$t('wiki.component.documentShare.externalShareTitle3');

      a.click();
    },
    /* 生成二维码 */
    createQrcode() {
      // 清除已经生成的二维码
      if (this.$refs.qrcode) this.$refs.qrcode.innerHTML = '';

      // 生成二维码
      new QRCode('qrcode', {
        width: 512,
        height: 512,
        text: this.shareUrl,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.share-modal {
  width: 132px;
  height: 132px;
  ::v-deep img {
    width: 100%;
    height: 100%;
  }
}
</style>
