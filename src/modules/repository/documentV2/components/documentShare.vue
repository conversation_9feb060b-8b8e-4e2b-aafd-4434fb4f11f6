<template>
  <div>
    <base-modal :title="$t('common.base.share')" class="type-modal" width="500px" :show.sync="shareBoxShow">
      <div class="select-count mar-b-12" v-if="isBatchOperateStatus">{{ $t('wiki.list.selectCount', { num: wikiList.length }) }}</div>
      <div class="wiki-share-wrap" v-if="shareBoxShow">
        <p>{{ $t('wiki.component.documentShare.label1') }}</p>

        <div class="share-input">
          <ChooseUserPower :options="selectUserOptions" @update="changeSelectValue"></ChooseUserPower>
          <el-button type="primary" :loading="shareLoading" @click="submitShare">{{ $t('common.base.send') }}</el-button>
        </div>
        <div class="export-box" v-if="shareUrls.length">
          <div class="earth-box">
            <i class="iconfont icon-earth earth-size"></i>
          </div>
          <div class="export-right">
            <span>{{ $t('wiki.component.documentShare.label3') }}</span>
            <span class="tip-text">{{ $t('wiki.component.documentShare.label4') }}</span>
          </div>
        </div>
        <div class="flex-y jus-c al-c" v-show="shareUrls.length == 1">
          <!-- <div class="share-modal" id="qrcode" ref="qrcode"></div> -->
          <div class="url-copy">
            <div class="url-line">
              <div class="link-input">
                <el-input :placeholder="realShareUrls" :disabled="true"> </el-input>
              </div>

              <el-button class="share-btn" @click="outlineShare">{{ $t('common.base.copyLink') }}</el-button>
            </div>
            <div class="image-erweima">
              <i @click="openQr()" class="iconfont icon-erweima" style="font-size: 20px; color: #595959"></i>
            </div>
          </div>
        </div>
        <div class="link-wrap" v-if="shareUrls.length > 1">
          <el-button type="primary" @click="openLinkListDialog">{{ $t('wiki.component.documentShare.viewLink') }}</el-button>
        </div>
      </div>
    </base-modal>

    <qrCodeDialog :share-url="shareUrls[0]" :shareTitle="titleList[0]" ref="qrCodeDialog" />
    <linkListDialog ref="linkListDialog" :linkList="linkList" />
  </div>
</template>

<script>
import { getShareLink, shareDocument } from '@src/api/Repository';
import Clipboard from 'clipboard';

import { BaseSelectUserModeEnum } from '@src/component/common/BaseSelectUser';

import { debounce } from 'lodash';
import ChooseUserPower from '@src/component/compomentV2/ChooseUserPower/index.vue';
import qrCodeDialog from './qrCodeDialog.vue';
import linkListDialog from './linkListDialog.vue';

export default {
  name: 'document-share',
  props: {
    // bodyType: {
    //   type: String,
    //   default: 'icon'
    // },
    wikiList: {
      type: Array,
    },
    isBatchOperateStatus: {
      type: Boolean,
      default: false,
    },
    shareTitle: {
      type: String,
      default: ''
    },
  },
  components: {
    ChooseUserPower,
    qrCodeDialog,
    linkListDialog,
  },
  data() {
    return {
      shareBoxShow: false,
      shareUrls: [],
      // wikiList: [],
      shareInfo: {
        selectedUsers: [],
        selectedDepts: [],
      },
      shareLoading: false,
      shareType: 0,
      outsideShow: true, //是否显示外部分享按钮
      selectUserOptions: {
        max: -1,
        mode: BaseSelectUserModeEnum.Filter,
        isCanChooseRole: false,
      },
      linkList: [],
      titleList: [],
    };
  },
  watch: {
    async shareBoxShow(value) {
      if (value) {
        await this.getShareUrl();
      }
    },
  },
  computed: {
    realShareUrls() {
      return this.shareUrls?.length && this.shareUrls.join('\n');
    },
  },
  async mounted() {
    // await this.getShareUrl();
    if (this.shareUrls.length == 1) {
      this.createQrcode();
    }
  },
  methods: {
    openQr() {
      this.$refs.qrCodeDialog.open();
    },
    openLinkListDialog() {
      this.$refs.linkListDialog.open();
    },
    // 分享
    shareDocument(showEnable) {
      // 批量分享或分享功能都是外部分享才显示外部分享按钮
      this.outsideShow = showEnable;
      this.shareBoxShow = true;
      this.shareType = 0;
      // if (this.wikiList.find(v => v.allowShare)) {
      //   this.getShareUrl();
      // }
      //  else {
      //   this.inlineShare();
      // }
    },
    // 内部分享，选择人员或者组织
    inlineShare(type = 'person') {
      this.shareBoxShow = false;
      type === 'dept' ? this.selectTeam() : this.choosePerson();
    },

    // 外部分享，将连接添加至剪切板
    outlineShare() {
      // 外部文章选择外部分享时
      let _this = this;
      let url = this.shareUrls.join('\n');

      // 获取body
      let body = document.getElementsByTagName('body')[0];

      let copyFrom = document.createElement('a');
      copyFrom.setAttribute('id', 'target');
      copyFrom.setAttribute('target', '_blank');
      copyFrom.setAttribute('href', url);
      copyFrom.innerHTML = url;

      // 创建按钮
      let agent = document.createElement('button');
      // body增加超链接
      body.appendChild(copyFrom);
      // body增加按钮
      body.appendChild(agent); // 采用Clipboard.js方案 // trouble：没有可以传入的HTML元素，但我们可以动态创建一个DOM对象作为代理，复制超链接

      let clipboard = new Clipboard(agent, {
        target() {
          return document.getElementById('target');
        },
      });

      clipboard.on('success', function (e) {
        _this.$platform.notification({
          title: _this.$t('wiki.component.documentShare.tips1'),
          type: 'success',
        });
      });

      clipboard.on('error', function (e) {
        _this.$platform.notification({
          title: _this.$t('wiki.component.documentShare.tips2'),
          type: 'error',
        });
      });
      // 点击按钮
      agent.click();
      // 移除创建的元素
      body.removeChild(copyFrom);
      body.removeChild(agent);
    },

    async getShareUrl() {
      this.shareUrls = [];
      try {
        let protocol = window.location.protocol;
        let host = window.location.host;
        let originalUrls = [];
        this.linkList = [];
        this.wikiList
          .filter(v => v.allowShare)
          .forEach(v => {
            originalUrls.push(`${protocol}//${host}/v_open/wiki/view?wikiId=${v.id}`);
            this.linkList.push({
              id: v.id,
              title: v.title,
            });
          });
        this.shareUrls = await getShareLink({
          originalUrls,
        });
        this.shareUrls.forEach((url, ind) => {
          this.linkList[ind].url = url;
          this.titleList.push(this.linkList[ind].title)
        });
        // this.outlineShare()
      } catch (error) {
        console.error('error', error);
      }
    },

    /** 选择团队  */
    selectTeam() {
      let options = {
        max: -1,
        mode: BaseSelectUserModeEnum.Filter,
        isCanChooseRole: false,
      };

      this.$fast.select.multi
        .all(options)
        .then(res => {
          if (res.status === 0) {
            let data = res.data || {};
            let users = data.users || [];
            let depts = data.depts || [];
            let serviceProviders = data.serviceProviders || [];

            this.shareInfo.selectedUsers = users;
            this.shareInfo.selectedDepts = depts.concat(serviceProviders);
            // this.$el.dispatchEvent(
            //   new CustomEvent("form.validate", { bubbles: true })
            // );
            this.submitShare();
          }
        })
        .catch(err => console.error(err));
    },

    // 点击加号显示标签输入框
    choosePerson() {
      let max = -1;

      let options = {
        title: this.$t('wiki.component.documentShare.title1'),
        seeAllOrg: true,
        max,
        mode: BaseSelectUserModeEnum.Filter,
      };

      return this.$fast.select.multi
        .user(options)
        .then(result => {
          if (result.status == 0) {
            let data = result.data || {};
            let users = data.users || [];

            this.shareInfo.selectedUsers = users;
            // this.$el.dispatchEvent(
            //   new CustomEvent("form.validate", { bubbles: true })
            // );
            this.submitShare();
          }
        })
        .catch(err => console.error(err));
    },

    // 内部分享选择人员确定后
    async submitShare() {
      if (!this.shareInfo.selectedUsers || !this.shareInfo.selectedDepts) return this.$platform.toast(this.$t('wiki.component.documentShare.title1'), 'error');

      try {
        let userIds = this.shareInfo.selectedUsers.map(item => item.userId);
        let tagIds = this.shareInfo.selectedDepts.map(item => item.id);
        let params = {
          wikiIds: this.wikiList.map(v => v.id),
          userIds,
          tagIds,
        };
        this.shareLoading = true;

        let res = await shareDocument(params);
        this.shareLoading = false;
        if (res.success) {
          this.$platform.notification({
            title: this.$t('wiki.component.documentShare.title2'),
            type: 'success',
          });
          this.shareBoxShow = false;
        } else {
          this.$platform.notification({
            title: res.message,
            type: 'error',
          });
        }
      } catch (err) {
        console.error(err);
        this.shareLoading = false;
      }
    },
    confirm: debounce(function () {
      if (this.shareType == 0) {
        this.inlineShare('dept');
      } else if (this.shareType == 1) {
        this.inlineShare();
      } else {
        this.shareBoxShow = false;
        // this.getShareUrl()
      }
    }, 100),
    changeSelectValue(value) {
      const { users, depts } = value;
      this.shareInfo.selectedUsers = users;
      this.shareInfo.selectedDepts = depts;
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .type-modal {
  ::deep(.base-modal-body) {
    overflow: hidden;
    padding: 24px;
    .wiki-share-wrap {
      height: 250px;
    }
  }

  .base-modal-footer {
    text-align: right;
  }
  .select-count {
    line-height: 38px;
    padding: 0 16px;
    border-radius: 4px;
    color: #e6a23c;
    background: #fcf6ec;
  }

  .el-button:hover,
  .el-button:focus {
    color: $color-primary-light-5;
    border-color: #cce9e9;
    background-color: $color-primary-light-1;
  }

  .el-button--primary:hover,
  .el-button--primary:focus {
    background: #77c5c3;
    border-color: #77c5c3;
    color: #ffffff;
  }

  .green-btn {
    background: $color-primary-light-5;
    border: transparent;
  }
  .external-share {
    display: flex;
    justify-content: flex-start;
    .external-share-item {
      width: 166.5px;
      height: 32px;
      line-height: 32px;
      border-radius: 4px;
      background: #f5f5f5;
      text-align: center;
      cursor: pointer;
      #qrcode {
        // display: none;
      }
      .iconfont {
        color: $color-primary;
      }
      &:first-child {
        margin-right: 12px;
      }
    }
  }
}
.share-input {
  width: 452px;
  display: flex;
  gap: 8px;
  margin: 4px 0;
  .el-button {
    height: 32px;
  }
}
.tip-text {
  color: #8c8c8c;
  font-size: 12px;
  line-height: 20px;
}
.export-box {
  width: 452px;
  height: 44px;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin: 16px 0 8px;
  gap: 8px;
  .export-right {
    width: 404px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 2px;
  }
}
.earth-box {
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #13c2c2;
  border-radius: 10px;
  .earth-size {
    font-size: 24px;
    color: white;
  }
}
.url-copy {
  width: 100%;
  height: 56px;
  display: flex;
  justify-content: space-between;
  padding: 12px;
  background: #f5f8fa;
  border-radius: 4px;
  .url-line {
    width: 388px;
    height: 32px;
    display: flex;
    flex-direction: row;
  }
}
.link-input {
  width: 301px;
  height: 32px;
  border-radius: 4px 0 0 4px !important;
}
.link-wrap {
  padding: 12px;
  border-radius: 4px;
  background: #f5f8fa;
}
.share-btn {
  background: #13c2c2;
  margin: 0px -1px;
  color: white;
  border-radius: 0 4px 4px 0 !important;
}
.share-btn:focus {
  outline: none;
}
.image-erweima {
  width: 32px;
  height: 32px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  border: 1px solid #cbd6e2;
  border-radius: 4px;
  background: #f5f8fa;
  cursor: pointer;
}
::v-deep .el-dialog__body {
  height: 198px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}
</style>
