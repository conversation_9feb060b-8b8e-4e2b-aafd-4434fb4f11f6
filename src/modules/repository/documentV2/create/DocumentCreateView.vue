<template>
  <div class="document-create-container-v2">
    <div class="fe-page-header">
      <div class="fe-page-header__left">
        <!-- {{ isEdit ? $t('common.pageTitle.pageWikiEdit') : $t('common.pageTitle.pageWikiCreate') }} -->
        <h4 style="margin-bottom: 0">{{ this.params.title }}</h4>
        <!-- 标签 -->
        <el-tooltip effect="dark" :content="$t('common.base.edit')" placement="bottom">
          <span class="header-right-icon-each" @click="openDialog('edit')">
            <i class="iconfont icon-edit-square1"> </i>
          </span>
        </el-tooltip>
        <FormIntelligentLabel @update="labelHandler" :value="params.labelObj" mode="wiki" :config="showIntelligentTagType" />
      </div>
      <div class="fe-page-header__right">
        <el-button type="plain-third" @click="goBack">{{ $t('common.base.cancel') }}</el-button>
        <el-button
          v-if="!isEdit || info.isDraft == 1"
          type="plain-third"
          @click="
            toDraftBox();
            trackEventHandler('draft');
          "
          :disabled="!draftCanClick || pending"
          >{{ $t('common.base.saveDraft') }}</el-button
        >
        <el-button
          type="primary"
          class="base-button green-butn"
          @click="
            saveAndSumbit();
            trackEventHandler('save');
          "
          :disabled="!saveCanClick || pending"
          >{{ $t('common.base.release') }}</el-button
        >
      </div>
    </div>
    <div class="document-create-view" v-loading.fullscreen.lock="loading">
      <div class="view-left">
        <!-- 富文本编辑器 -->
        <form-richtext
          ref="editor"
          :value="params.content"
          :placeholder="$t('common.placeholder.input2')"
          @input="getInput"
          :config="initEditorConfig()"
          :isUploadBase64Img="true"
        />
        <p class="article-error" v-if="articleEmpty">{{ $t('wiki.create.tips1') }}</p>
      </div>

      <request-approve @createApprove="createApprove" @reset="reset" ref="requestApproveDialog" />
    </div>

    <div class="attachment-wrap">
      <el-button type="plain-third" @click="chooseFile"><i class="iconfont icon-add2"></i> {{ $t('wiki.create.textTitle.btn2') }}</el-button>
      <input type="file" ref="input" @change="handleChange" multiple>
      <div class="file-list">
        <div class="file-item" v-for="file in params.form.attachments" :key="file.id">
          <img :src="findFileIconUrl(file.filename)" class="mar-r-8" />
          <div class="file-info">
            <div class="file-name">{{ file.filename }}</div>
            <div class="file-size">{{ file.fileSize }}</div>
          </div>
          <el-button type="button" class="btn-text file-del" @click="deleteAttachment(file)">
            <i class="iconfont icon-circle-delete" style="position: relative;top: 1px"></i>
          </el-button>
        </div>
      </div>
    </div>

    <edit-title-dialog ref="editTitleDialog" :wikiConfig="initData.wikiConfig" :wikiDownloadAuth="wikiDownloadAuth" @input="titleInput" />
  </div>
</template>

<script>
/* component */
import { Message } from 'element-ui';
import EditTitleDialog from './component/EditTitleDialog.vue';
import RequestApprove from './component/RequestApprove.vue';
/* api */
import * as RepositoryApi from '@src/api/Repository';
/* util */
import _ from 'lodash';
import Uploader from '@src/util/uploader';
import { t } from '@src/locales'
import { findFileIconUrl } from '@src/modules/ai/views/edit/mock.ts'
/* enum */
import TenantDataLimitSourceEnum from '@model/enum/TenantDataLimitSourceEnum';
import TenantDataLimitTypeEnum from '@model/enum/TenantDataLimitTypeEnum';
import StorageKeyEnum from '@model/enum/StorageKeyEnum';
/* mixin */
import VersionMixin from '@src/mixins/versionMixin/index.ts';
import ThemeMixin from '@src/mixins/themeMixin/index.ts';
import DocumentMixin from '@src/modules/repository/documentV2/mixin/documentMixin.js'
import { openAccurateTab } from '@src/util/platform';
import { PageRoutesTypeEnum } from 'pub-bbx-global/pageType/dist/enum/PageRoutesEnum';

/* hooks */
import { useFetchSettingGPTQuestion } from '@gpt/hooks';

import { getRootWindow, isFalsy } from 'pub-bbx-utils';
import { storageSet } from '@src/util/storage';

export default {
  name: 'document-create-view',
  mixins: [VersionMixin, ThemeMixin, DocumentMixin],
  components: {
    [EditTitleDialog.name]: EditTitleDialog,
    [RequestApprove.name]: RequestApprove,
  },
  props: {
    initData: {
      type: Object,
      default: () => ({}),
    },
  },
  setup() {
    const { fetchQuestion, question } = useFetchSettingGPTQuestion();

    return {
      fetchQuestion,
      question,
    };
  },
  data() {
    return {
      params: {
        title: '',
        content: '', // 文章内容
        // 可见范围相关信息
        allStaff: true, // 是否全部员工可见
        userList: [], // 部分人员数据
        userIds: [], // 部分用户ID列表
        roleIds: [], // 部分角色ID列表
        departmentIds: [], // 部分部门ID列表
        // 对外分享相关权限信息
        allowExternalShare: false, // 是否允许分享给外部人员查看
        allowExternalPeopleViewComment: false, // 是否允许企业外的人查看评论
        commentPermission: this.initData.wikiConfig.comment ? 1 : 0, // 评论权限
        allowDownload: false, // 是否允许可查看人下载文档
        labelObj: {}, // 设置标签参数
        label: [], // 标签
        form: {
          content: '',
          attachments: [],
          showInOwn: 0,
        }, // 附件
        typeId: '', // 文章分类
        desc: '', // 描述
        options: [],
      },
      interval: '',
      isSave: false,
      isEdit: false,
      isToDraft: false,
      wikiId: null,
      info: {},
      loading: false,
      articleEmpty: false,
      deleteCanClick: true,
      saveCanClick: true,
      draftCanClick: true,
      pending: false,
      isSaveData: false,
      isUpdate: false,
      isFormList: false,

      isCopy: false,
      copyId: '',

      task: {},
      wikiTypeId: '', // 分类ID
      // 故障库信息 Start
      wikiType: null,
      questionId: null,
      contentMaxLength: 50000,
      // 故障库信息 End
    };
  },
  async created() {
    try {
      this.getId();
      let detail = JSON.parse(localStorage.getItem(`document_article_${this.initData.userInfo.userId}`));
      if (detail && !this.isEdit) {
        this.isSaveData = true;
        let res = await this.$platform.confirm(this.$t('wiki.create.tips2'));
        if (res) {
          this.getArticle();
        } else {
          this.params.title = this.$t('common.wiki.untitledDocument')
          this.params.content = ' ';
          localStorage.removeItem(`document_article_${this.initData.userInfo.userId}`);
        }
      }
      if (!this.isEdit) {
        this.saveArticle();
        // 检查版本数量限制
        this.checkNumExceedLimitBeforeHandler && this.checkNumExceedLimitBeforeHandler(TenantDataLimitSourceEnum.Wiki, TenantDataLimitTypeEnum.Wiki);
      }

      await this.getTypes();
      if (this.isEdit || this.isCopy) {
        this.getArticle();
      } else {
        this.params.title = this.$t('common.wiki.untitledDocument')
      }

      this.task.taskId && this.taskConvertWiki();

      this.taskAISummaryDataProcessHandler();

      this.aiQuestionHandler();
    } catch (error) {
      console.error('error', error);
    }
  },
  mounted() {
    this.fetchWikiAuthConfig()
  },

  beforeDestroy() {
    // 清除定时器
    clearInterval(this.interval);
  },

  updated: _.debounce(function () {
    this.$nextTick(() => {
      this.isUpdate = true;
    });
  }, 1000),

  methods: {
    /**
     * @description 标签处理函数
     */
    labelHandler({ newValue, oldValue }) {
      this.params.labelObj = newValue;
    },
    openDialog(action) {
      if (action === 'edit') {
        this.$refs.editTitleDialog.openDialog(this.params);
      }
    },
    titleInput(params) {
      this.params = {
        ...this.params,
        ...params,
      }
    },
    goBack() {
      let id = window.frameElement.dataset.id;
      return this.$platform.closeTab(id);
    },
    taskConvertWiki() {
      RepositoryApi.taskConvertWiki({
        taskId: this.task.taskId,
        taskTypeId: this.task.taskTypeId,
      })
        .then(res => {
          if (res.code === 0) {
            let content = '';
            let arr = [...res.result.cardList, ...res.result.fieldList, ...res.result.taskReceiptList];
            arr.forEach((v, i) => {
              content += `<p>${v.displayName}：${v.content}</p>`;
            });

            this.$set(this.params, 'content', content);
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
    reset() {
      this.deleteCanClick = true;
      this.saveCanClick = true;
      this.draftCanClick = true;
      this.pending = false;
    },

    getId() {
      let array = window.location.href.split('?');
      if (array.length <= 1) return;
      let query = array[1].split('&');
      let params = [];
      query.forEach(item => {
        params.push({ name: item.split('=')[0], value: item.split('=')[1] });
      });

      params.forEach(item => {
        if (item.name == 'isFromList') {
          this.isFromList = true
        }
        if (item.name == 'wikiId') {
          this.wikiId = item.value;
          this.isEdit = true;
        }
        if (item.name === 'copyId') {
          this.copyId = item.value;
          this.isCopy = true;
        }
        if (item.name === 'taskId') {
          this.task.taskId = item.value;
        }
        if (item.name === 'taskNo') {
          this.task.taskNo = item.value;
        }
        if (item.name === 'taskTypeId') {
          this.task.taskTypeId = item.value;
        }
        if (item.name === 'wikiTypeId') {
          this.wikiTypeId = item.value;
        }
        if (item.name === 'questionId') {
          this.questionId = item.value;
        }
        // 故障库信息 Start
        if (item.name === 'wikiType') {
          this.wikiType = item.value;
        }
        // 故障库信息 End
      });
    },
    // 获取分类二级树状结构，每次更新一次
    async getTypes() {
      try {
        this.loading = true;
        let params = {};
        if (this.wikiType) {
          // 知识空间兼容老接口，可不用传参 type
          params.type = this.wikiType;
        }
        let res = await RepositoryApi.getDocumentTypes(params);

        this.loading = false;

        if (res.success) {
          // res.result.forEach(item => {
          //   item.value = item.id.toString();
          //   item.label = item.name;
          //   if(item.subTypes.length > 0) {
          //     item.children = item.subTypes;
          //     item.children.forEach(childItem => {
          //       childItem.value = childItem.id.toString();
          //       childItem.label = childItem.name;
          //     })
          //   }
          // })
          this.params.options = res.result;

          if (this.params.options.length <= 0) {
            this.$platform.notification({
              title: this.$t('wiki.create.tips3'),
              type: 'error',
            });
            this.saveCanClick = false;
            this.draftCanClick = false;
            this.deleteCanClick = false;
          }
          // 走复制流程，不获取第一个id
          if (!this.isEdit && this.params.options.length > 0 && !this.copyId) {
            this.params.typeId = this.params.options[0].id;
            //   if(this.params.typeId.length <= 0) {
            //     this.setType(this.params.options[0].id)
            //   }
            //   if(this.typeId) {
            //     this.setType(this.typeId);
            //   }
          }
          // 赋值选中的分类
          if (!this.isEdit && this.wikiTypeId) {
            this.params.typeId = Number(this.wikiTypeId);
          }
        } else {
          this.$platform.notification({
            title: res.message,
            type: 'error',
          });
        }
      } catch (err) {
        console.error(err);
        this.loading = false;
      }
    },

    // 保存并提交，新建、编辑
    async saveAndSumbit() {
      if (!(await this.paramsCheck())) return;
      this.isToDraft = false;
      this.saveCanClick = false;

      if (this.initData.wikiConfig.needApprove && this.initData.wikiConfig.approvers && this.initData.wikiConfig.approvers.length > 0) {
        this.$refs.requestApproveDialog.open();
        return;
      }

      try {
        let params = this.buildParams();
        this.pending = true;
        if (this.task.taskId) {
          params.convertId = this.task.taskId;
          params.typeNo = this.task.taskNo;
          params.convertType = 'task';
        }

        const SubmitPromise = RepositoryApi.saveAndSumbit(params);
        let res = await this.checkNumExceedLimitAfterHandler(SubmitPromise);
        this.pending = false;

        if (res.success) {
          try {
            const rootWindow = getRootWindow(window);
            const fromId = window.frameElement.getAttribute('fromid');
            rootWindow.fetchQuestionDelete(this.questionId, fromId, false);
          } catch (error) {
            console.error(error);
          }

          localStorage.removeItem(`document_article_${this.initData.userInfo.userId}`);

          this.$platform.notification({
            title: res.message,
            type: 'success',
          });

          this.saveCanClick = true;
          if (this.isEdit) {
            // 编辑时只刷新列表数据，不切换当前选中的数据
            getRootWindow(window)?.postMessage({action: 'documentEditSuccess', wikiId: params.id}, '*')
            setTimeout(() => {
              this.goBack()
            }, 150)
          } else {
            this.refreshFromTab();
          }
        } else {
          this.saveCanClick = true;
          this.$platform.notification({
            title: res.message,
            type: 'error',
          });
        }
      } catch (err) {
        console.error(err);
      }
    },
    // 需要审批时，提交
    async createApprove(remark) {
      if (!await this.paramsCheck()) return;
      this.isToDraft = false;
      this.$refs.requestApproveDialog.close();
      this.saveCanClick = false;

      try {
        let otherInfo = this.buildParams();
        // 审批时需要传allowShare
        otherInfo.allowShare = otherInfo.baseWikiSetting?.externalSharing?.allowExternalShare ? 1 : 0
        let params = {
          objId: this.info.id || null,
          applyRemark: remark,
          source: 'wiki',
          otherInfo,
        };
        if (this.task.taskId) {
          params.convertId = this.task.taskId;
          params.typeNo = this.task.taskNo;
          params.convertType = 'task';
        }
        this.pending = true;
        const ApprovePromise = RepositoryApi.createApprove(params);
        let res = await this.checkNumExceedLimitAfterHandler(ApprovePromise);
        this.pending = false;

        if (res.success) {
          localStorage.removeItem(`document_article_${this.initData.userInfo.userId}`);
          this.$platform.notification({
            title: res.message,
            type: 'success',
          });
          this.saveCanClick = true;
          storageSet('isCreateApprove', true);
          if (this.isFromList) {
            this.refreshFromTab()
          } else {
            this.openFrame();
          }
        } else {
          this.saveCanClick = true;
          this.$platform.notification({
            title: res.message,
            type: 'error',
          });
        }
      } catch (err) {
        console.error(err);
      }
    },

    // 存到草稿箱操作
    async toDraftBox() {
      if (!await this.paramsCheck()) return;
      this.isToDraft = true;
      this.draftCanClick = false;

      try {
        let params = this.buildParams();
        this.pending = true;
        let res = await RepositoryApi.saveDraft(params);
        this.pending = false;

        if (res.success) {
          this.$platform.notification({
            title: this.$t('wiki.create.tips4'),
            type: 'success',
          });
          localStorage.removeItem(`document_article_${this.initData.userInfo.userId}`);
          this.draftCanClick = true;
          if (this.isFromList) {
            this.refreshFromTab()
          } else {
            this.openFrame();
          }
        } else {
          this.draftCanClick = true;
          this.$platform.notification({
            title: res.message,
            type: 'error',
          });
        }
      } catch (err) {
        console.error(err);
      }
    },

    // 文档库或草稿删除操作，所调接口不同
    async deleteFile() {
      try {
        if (!(await this.$platform.confirm(this.$t('wiki.create.tips5')))) return;
        this.deleteCanClick = false;

        let params = {
          wikiIds: [this.wikiId],
        };

        this.pending = true;
        let res = await RepositoryApi.deleteDocument(params);
        this.pending = false;

        if (res.success) {
          localStorage.removeItem(`document_article_${this.initData.userInfo.userId}`);
          this.$platform.notification({
            title: this.$t('wiki.create.tips6'),
            type: 'success',
          });
          this.deleteCanClick = true;
          if (this.isFromList) {
            this.refreshFromTab()
          } else {
            this.openFrame();
          }
        } else {
          this.deleteCanClick = true;
          this.$platform.notification({
            title: res.message,
            type: 'error',
          });
        }
      } catch (e) {
        console.error(e);
      }
    },

    openFrame() {
      let id = window.frameElement.dataset.id;
      this.$platform.closeTab(id);

      let fromId = window.frameElement.getAttribute('id');

      // this.$platform.openTab({
      //   id: 'M_INFO_DOC',
      //   title: '知识库列表',
      //   url: '/wiki',
      //   reload: true,
      //   close: true,
      //   fromId
      // });
      openAccurateTab({
        type: PageRoutesTypeEnum.PageWikiV2List,
        reload: true,
        fromId,
      });
    },
    refreshFromTab() {
      let id = window.frameElement.dataset.id;
      this.$platform.closeTab(id);
      let fromid = window?.frameElement?.getAttribute('fromid');
      if (fromid) {
        this.$platform.refreshTab(fromid);
      }
    },
    // 富文本自定义上传图片和视频按钮
    initEditorConfig() {
      return {
        setup: (editor) => {
          editor.ui.registry.addButton('customImageUpload', {
            icon: 'image',
            onAction: () => {
              const input = document.createElement('input');
              input.type = 'file';
              input.accept = 'image/*';
              input.onchange = async () => {
                const file = input.files[0];
                const res = await Uploader.batchUploadWithParse({
                  files: [file],
                  action: '/files/upload',
                })
                const url = res?.success?.[0]?.url
                editor.insertContent(`<img src="${url}" alt="上传的图片" />`);
                input.remove();
              };
              input.click();
            }
          });
          editor.ui.registry.addButton('customVideoUpload', {
            icon: 'embed',
            onAction: () => {
              const input = document.createElement('input');
              input.type = 'file';
              input.accept = 'video/*';
              input.onchange = async () => {
                const file = input.files[0];
                const res = await Uploader.batchUploadWithParse({
                  files: [file],
                  action: '/files/upload',
                })
                const url = res?.success?.[0]?.url
                editor.insertContent(`<video width="800" height="450" controls src="${url}"></video>`);
                input.remove();
              };
              input.click();
            }
          });
        },
        toolbar:'fontselect fontsizeselect forecolor backcolor customImageUpload customVideoUpload fullscreen | formatselect alignleft aligncenter alignright alignjustify outdent indent bold italic underline strikethrough link | ',
      }
    },

    getInput(html) {
      this.params.content = html;
      let imgCount = this.imgCount(html);
      const content = this.$refs.editor.getContent()
      this.articleEmpty = !content && !imgCount;

      if (imgCount > 30) {
        this.$platform.notification({
          title: this.$t('wiki.create.tips7'),
          type: 'error',
        });
        return false;
      }
      if (content.length > this.contentMaxLength) {
        this.$platform.notification({
          title: this.$t('common.base.formMaxLenthTips', {maxLength: this.contentMaxLength}),
          type: 'error',
        });
        return false;
      }
      return true;
    },

    imgCount(html) {
      if (!html) return null;
      let imgReg = /<img.*?(?:>|\/>)/gi; // 匹配图片中的img标签
      let arr = html.match(imgReg); //筛选出所有的img
      return (arr && arr.length) || null;
    },

    // 编辑时获取文章信息
    async getArticle() {
      if (this.isEdit || this.isCopy) {
        try {
          let params = {
            wikiId: this.wikiId || this.copyId,
            updateReadTimes: false,
          };
          let res = await RepositoryApi.getInlineDetail(params);
          this.loading = false;

          if (res.success) {
            let detail = res.result;
            this.initParams(detail)

            // 获取 文章类别
            this.wikiType = detail?.wikiType;
            this.getTypes();

            this.info = detail;
            this.$nextTick(() => {
              this.$refs.editTitleDialog.form = _.cloneDeep(this.params)
            })
          } else {
            this.$platform.notification({
              title: res.message,
              type: 'error',
            });
          }
        } catch (err) {
          console.error(err);
          this.loading = false;
        }
      } else {
        let detail = localStorage.getItem(`document_article_${this.initData.userInfo.userId}`);
        let params = JSON.parse(detail);
        this.initParams(params, true)

        // 智能标签
        this.cacheLabelDataHandler(params?.labelObj?.label);
        this.$nextTick(() => {
          this.$refs.editTitleDialog.form = _.cloneDeep(this.params)
        })
      }
    },

    /**
     * @description 缓存标签数据处理
     */
    cacheLabelDataHandler(value) {
      if (!value || !_.isPlainObject(value)) return;
      if (!Array.isArray(value.labels) && !Array.isArray(value.personalLabels)) return;
      // because it is create wiki, so don't need to check changeLabel field, just need to set labelList
      this.params.labelObj = [...value?.labels, ...value?.personalLabels].map(i => {
        return {
          labelId: i.labelId || '',
          name: i.name || '',
        };
      });
    },

    // 文章暂存操作，每2分钟一次，仅在新建时暂存
    saveArticle() {
      this.interval && clearInterval(this.interval);
      this.interval = setTimeout(() => {
        if (this.isSave) {
          let detail = {
            content: this.params.content,
            form: this.params.form,
            title: this.params.title,
            typeId: this.params.typeId,
            label: this.params.label,
            baseWikiSetting: {
              visibleRange: {
                allStaff: this.params.allStaff,
                userList: JSON.stringify(this.params.userList || []),
                userIds: this.params.userIds || [],
                roleIds: this.params.roleIds || [],
                departmentIds: this.params.departmentIds || [],
              },
              externalSharing: {
                allowExternalShare: this.params.allowExternalShare, 
                allowExternalPeopleViewComment: this.params.allowExternalShare ? this.params.allowExternalPeopleViewComment : false, 
                commentPermission: this.params.commentPermission, 
                allowDownload: this.params.allowDownload, 
              }
            }
          };
          // 智能标签暂存
          detail.labelObj = this.params?.labelObj;

          localStorage.setItem(`document_article_${this.initData.userInfo.userId}`, JSON.stringify(detail));
          Message.success({
            message: '文章已暂存',
            type: 'success',
          });
        }
        this.isSave = false;
      }, 1000 * 60 * 2);
    },

    // 构建参数
    buildParams() {
      let params = {
        title: this.params.title,
        content: this.params.content,
        desc: this.params.desc,
        typeId: Number(this.params.typeId),
        attachment: this.params.form.attachments || [],
        setLabel: this.params.labelObj?.label || null,
        changeLabel: this.params.labelObj?.changeLabel || false,
        baseWikiSetting: {
          visibleRange: {
            allStaff: this.params.allStaff,
            userList: JSON.stringify(this.params.userList || []),
            userIds: this.params.userIds || [],
            roleIds: this.params.roleIds || [],
            departmentIds: this.params.departmentIds || [],
          },
          externalSharing: {
            allowExternalShare: this.params.allowExternalShare, 
            allowExternalPeopleViewComment: this.params.allowExternalShare ? this.params.allowExternalPeopleViewComment : false, 
            commentPermission: this.params.commentPermission, 
            allowDownload: this.params.allowDownload, 
          },
        }
      }
      // type 区分是知识空间的还是故障库的文章
      params.wikiType = this.wikiType ? Number(this.wikiType) : 0;

      // 文档、草稿编辑后存为草稿
      if (this.isEdit && this.isToDraft) {
        params.id = this.info.id;
      }

      // 文档、草稿编辑后提交
      if (this.isEdit && !this.isToDraft) {
        params.id = this.info.id;
        params.importAttachment = this.info.importAttachment || []
      }
      if (params.id) {
        params.baseWikiSetting.wikiId = params.id
      }

      if (this.params.form.attachments && this.params.form.attachments.length > 0) {
        params.attachment = this.params.form.attachments;
      }
      return params;
    },
    initParams(params, isCache) {
      this.params.title = params.title || this.$t('common.wiki.untitledDocument');
      this.params.desc = params.desc || '';
      this.params.label = params.label;
      if (!isCache) this.params.labelObj = params.labelList;
      this.params.form.attachments = params.attachment || [];
      this.params.content = params.content;
      const baseWikiSetting = params.baseWikiSetting
      this.params.allStaff = baseWikiSetting ? baseWikiSetting?.visibleRange?.allStaff : true
      this.params.userList = JSON.parse(baseWikiSetting?.visibleRange?.userList || '[]');
      this.params.userIds = baseWikiSetting?.visibleRange?.userIds || []
      this.params.roleIds = baseWikiSetting?.visibleRange?.roleIds || []
      this.params.departmentIds = baseWikiSetting?.visibleRange?.departmentIds || []
      this.params.allowExternalShare = baseWikiSetting?.externalSharing?.allowExternalShare || false
      this.params.allowExternalPeopleViewComment = baseWikiSetting ? baseWikiSetting?.externalSharing?.allowExternalPeopleViewComment : true
      this.params.commentPermission = baseWikiSetting ? baseWikiSetting?.externalSharing?.commentPermission : 1
      this.params.allowDownload = baseWikiSetting ? baseWikiSetting?.externalSharing?.allowDownload : true
      if (this.copyId) delete params.id;
      this.params.typeId = params.typeId;
    },

    // 参数校验，标题、内容不允许为空
    async paramsCheck() {
      const titleCheck = await this.$refs.editTitleDialog.titleCheck()
      if (!titleCheck) {
        this.$platform.notification({
          title: this.$t('common.placeholder.inputTitle'),
          type: 'error',
        });
        return false;
      }
      if (!this.$refs.editor.getContent() && this.params.content.indexOf('<img') == -1) {
        this.articleEmpty = true;
      }
      if (this.articleEmpty) {
        this.$platform.notification({
          title: this.$t('common.placeholder.inputContent'),
          type: 'error',
        });
        return false;
      }
      if (!this.getInput(this.params.content)) return false;
      return true;
    },
    // TalkingData事件埋点
    trackEventHandler(type) {
      if (type === 'delete') {
        window.TDAPP.onEvent('pc：知识库编辑-删除事件');
        return;
      }
      if (type === 'save') {
        window.TDAPP.onEvent('pc：知识库-保存并提交事件');
        return;
      }
      if (type === 'draft') {
        window.TDAPP.onEvent('pc：知识库-保存草稿');
        return;
      }
    },
    // 选择文件
    handleChange(event){
      const files = event.target.files;
      if(!files || !files.length) return;
      let form = this.params.form;

      if(form.attachments.length + files.length > Uploader.WIKI_FILE_MAX_NUM) {
        let message = this.$t('common.base.uploadModal.uploadCountTips', {count:Uploader.WIKI_FILE_MAX_NUM});
        let max = Uploader.WIKI_FILE_MAX_NUM - form.attachments.length;

        if(max > 0 && files.length > max){
          message +=  `, ${this.$t('common.base.uploadModal.canUploadCountTips',{count:max})}`;
        }

        return platform.alert(message)
      }

      this.pending = true;

      Uploader.batchUploadWithParse({files, action: '/files/upload/wiki', source: 'wiki'}).then(result => {
        let {success, error} = result;

        if(error.length > 0){
          let message = error.map(item => item.message).join('\n');
          //此处不能return
          platform.alert(message)
        }

        if(success.length > 0){
          form.attachments = form.attachments.concat(success);
        }
      })
        .catch(err => console.error(err))
        .then(() => this.pending = false)
    },
    // 触发input click事件选择文件
    chooseFile () {
      if(this.pending) return platform.alert(this.$t('common.base.uploadModal.tips1'));
      if(this.params.form?.attachments?.length >= Uploader.WIKI_FILE_MAX_NUM) {
        return platform.alert(this.$t('common.base.uploadModal.uploadCountTips', {count:Uploader.WIKI_FILE_MAX_NUM}));
      }

      this.$refs.input.value = null;
      this.$refs.input.click();
    },
    // 删除附件
    deleteAttachment(file) {
      let index = this.params.form.attachments.indexOf(file);
      if(index >= 0) {
        this.params.form.attachments.splice(index, 1);
      }
    },
    findFileIconUrl,
    /**
     * @description 工单AI摘要数据处理
     */
    taskAISummaryDataProcessHandler() {
      // 从 sessionStorage 中获取摘要数据
      const summaryText = sessionStorage.getItem(StorageKeyEnum.AITaskSummary);
      // 如果有摘要数据，将摘要数据赋值给文章内容, 并清除 sessionStorage 中的摘要数据
      if (summaryText) {
        this.params.content = summaryText;

        this.$nextTick(() => {
          this.$refs.editor.debounceInputForValue(this.params.content);
        });

        sessionStorage.removeItem(StorageKeyEnum.AITaskSummary);
      }
    },
    async aiQuestionHandler() {
      if (isFalsy(this.questionId)) {
        return;
      }

      const params = {
        id: this.questionId,
      };

      await this.fetchQuestion(params);

      if (isFalsy(this.question.answer)) {
        return;
      }

      this.params.content = this.question.answer;

      this.$nextTick(() => {
        this.$refs.editor.debounceInputForValue(this.params.content);
      });
    },
  },
  computed: {
    showIntelligentTagType() {
      return {
        // normalShowType: 'text',
        // normalMaxLength: 2,
      };
    },
    padding() {
      return this.isEdit ? '40px' : '200px';
    },
  },
  watch: {
    params: {
      handler(n) {
        if (this.isUpdate) {
          this.isSave = true;
          this.saveArticle();
        }
      },
      deep: true,
    },
  },
};
</script>

<style lang="scss" scoped>
.label {
  color: rgb(144, 147, 153);
  font-size: 14px;
  padding-bottom: 8px;
}
.document-create-container-v2 {
  height: 100%;
  overflow: hidden;
  position: relative;
  padding: 12px;
  .fe-page-header {
    gap: 16px;
  }
  .fe-page-header .fe-page-header__left {
    max-width: 70%;
    h4 {
      max-width: 300px;
      @include text-ellipsis();
    }
  }
  ::v-deep .biz-intelligent-tags__view-list.edit {
    flex-wrap: nowrap;
  }
  ::v-deep .biz-intelligent-tagging__button.formEdit {
    min-width: 94px;
  }
  .attachment-wrap {
    position: absolute;
    width: calc(100% - 24px);
    display: flex;
    gap: 16px;
    left: 12px;
    bottom: 12px;
    background: #fff;
    padding: 16px 32px;
    border-top: 1px solid #E4E7ED;
    .el-button {
      height: 32px;
    }
    input[type='file']{
      display: none !important;
    }
    .file-list {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
    }
    .file-item {
      width: 240px;
      padding: 8px;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      position: relative;
      border-radius: 4px;
      background: #F0F2F5;
      &:last-child {
        margin-right: 0;
      }
      &:hover {
        .file-del{
          visibility: visible;
        }
      }
      img {
        width: 28px;
        height: 28px;
      }
      .file-info {
        flex: 1;
        overflow: hidden;
        .file-name {
          @include text-ellipsis();
          font-size: 12px;
          line-height: 16px;
        }
        .file-size {
          font-size: 10px;
          line-height: 12px;
          color: #8C8C8C;
        }
      }
      .file-del {
        position: absolute;
        right: -7px;
        top: -7px;
        visibility: hidden;
        color: #9a9a9a;
        vertical-align: middle;
        padding: 0;
        &:hover{
          color: #ed3f14;
          .iconfont {
            color: #ed3f14;
          }
        }
      }
    }
  }
}
.document-create-view {
  height: calc(100% - 56px);

  display: flex;

  .view-left {
    flex: 1;
    height: 100%;
    overflow: auto;
    padding-bottom: 64px;
    background: #fff;
    display: flex;
    flex-direction: column;

    .textTitle {
      padding-bottom: 10px;
    }

    ::v-deep .form-richtext {
      flex: 1;
    }
    ::v-deep .form-richtext .tox .tox-statusbar {
      display: none;
    }
    ::v-deep .form-richtext .tox-tinymce {
      height: 100% !important;
      border-color: #e0e1e2;
    }
    ::v-deep .form-richtext .tox .tox-tbtn {
      cursor: pointer;
    }
    ::v-deep .form-richtext .tox .tox-tbtn svg {
      height: initial;
    }
    ::v-deep .form-richtext .tox .tox-tbtn:hover {
      color: #fff;
    }
    ::v-deep .form-richtext .tox .tox-tbtn:hover svg {
      fill: #fff;
    }
    ::v-deep .form-richtext .tox .tox-tbtn--enabled svg {
      fill: #fff;
    }
    :v-deep .form-richtext .tox .tox-tbtn--enabled:hover svg {
      fill: #fff;
    }

    .article-error {
      color: #f56c6c;
      font-size: 12px;
      line-height: 24px;
      margin: 0;
      padding: 5px;
    }

    .view-left-footer {
      display: flex;
      margin-top: 25px;
      margin-bottom: 100px;

      .green-butn {
        margin-left: 0px;
        margin-right: 15px;

        &:disabled {
          cursor: not-allowed;
        }
      }

      .white-butn {
        background: #fff;
        color: #333;
        border: 1px solid #e2e2e2;

        &:hover {
          border-color: #55b7b4;
          background: #66bebc;
          color: #fff;
        }

        &:disabled {
          opacity: 0.65 !important;
          cursor: not-allowed;
        }
      }
    }
  }

  .view-right {
    width: 350px;
    height: 100%;
    background: #fff;

    border-left: 1px solid #c5d9e8;
  }
}
.el-scrollbar {
  min-height: 45px;
}
.popper-class {
  .el-radio__inner {
    margin-top: 10px;
  }
}
.full-screen {
  width: 100%;
  height: 100%;
}

.w {
  width: 30px;
  height: 30px;
}

.header-right-icon-each {
  margin: 12px;
  cursor: pointer;

  .iconfont {
    font-size: 18px;
  }

  .iconfont:hover {
    color: $color-primary-light-6;
  }

  .iconfont:focus {
    color: $color-primary-light-6;
  }
}
</style>
