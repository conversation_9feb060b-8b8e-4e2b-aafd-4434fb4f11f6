<template>
  <base-modal :title="$t('common.base.documentSetting')" :show.sync="visible" width="500px">
    <el-form label-position="top" :model="form" :rules="rules" ref="ruleForm">
      <el-form-item :label="`${$t('wiki.create.textTitle.label1')}：`" class="create-item item-title" prop="title">
        <el-input class="label-item" v-model="form.title" :placeholder="$t('common.placeholder.inputSomething', { data1: $t('wiki.create.textTitle.label1') })"></el-input>
      </el-form-item>

      <el-form-item :label="$t('common.base.Menu')" class="create-item" prop="typeId">
        <treeselect class="label-item" :clearable="true" :searchable="true" :open-on-click="true" :placeholder="$t('common.placeholder.inputSomething', { data1: $t('wiki.create.textTitle.label2') })" :alwaysOpen="false" :options="form.options" v-model="form.typeId" :noOptionsText="$t('common.base.tip.noData')" :noResultsText="$t('common.base.tip.noData')" :normalizer="normalizer" />
      </el-form-item>
      <el-form-item width="100%" :label="$t('common.base.description1')" class="create-item" prop="desc">
        <textarea class="text-input" :placeholder="$t('common.placeholder.input2')" rows="3" data-prop="placeHolder" v-model="form.desc" :maxlength="TitleShowMaxLengthMax"></textarea>
      </el-form-item>
      <el-form-item class="form-item">
        <div class="label-item title">
          <span class="text"><span class="form-item-required">*</span>{{ $t('common.base.viewInInsideRange') }}:</span>
          <el-checkbox class="checkbox" v-model="form.allStaff">{{ $t('common.base.allUser')}} </el-checkbox>
        </div>
        <el-select v-if="form.allStaff" class="select" readonly disabled :value="$t('common.base.allUser')"></el-select>
        <div class="borderTop" v-else>
          <el-select 
            style="width: 100%;"
            :value="selectUserOptions.map(item => item.value)" 
            multiple 
            :placeholder="$t('common.placeholder.select')" 
            clearable 
            collapse-tags 
            @click.native="openSelectUser" 
            @remove-tag="handleRemoveTag"
            @clear="form.userList = []"
            ref="usersSelect"
          >
            <el-option v-for="item in selectUserOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </div>
      </el-form-item>

      <el-form-item :label="$t('common.base.outsideShare')" class="form-item mt-18">
        <div class="container big-border" v-if="wikiConfig.permitShare">
          <div class="label-item share">
            <span class="text">{{ $t('wiki.component.setting.title1') }}</span>
            <el-switch v-model="form.allowExternalShare" :active-value="true" :inactive-value="false"> </el-switch>
          </div>
          <div v-if="form.allowExternalShare" class="label-item share borderTop">
            <span class="text">{{ $t('wiki.component.setting.title2') }}</span>
            <el-switch v-model="form.allowExternalPeopleViewComment" :active-value="true" :inactive-value="false"> </el-switch>
          </div>
        </div>
        <div class="container big-border" v-else>
          <div class="label-item share share-tip">
            <span class="text">{{ $t('wiki.component.setting.title5') }}</span>
            <el-button type="text" @click="goWikiSetting">{{ $t('common.base.toSet2') }}</el-button>
          </div>
        </div>
      </el-form-item>
      <el-form-item :label="$t('common.base.moreSetting')" class="form-item mt-18" :class="{'mt-18': !wikiConfig.permitShare}" v-if="wikiConfig.comment || wikiDownloadAuth">
        <div class="container big-border">
          <div class="label-item share" v-if="wikiConfig.comment">
            <span class="text">{{ $t('wiki.component.setting.title3') }}</span>
            <el-switch v-model="form.commentPermission" :active-value="1" :inactive-value="0"></el-switch>
          </div>
          <div class="label-item share borderTop" v-if="wikiDownloadAuth">
            <span class="text">{{ $t('wiki.component.setting.title4') }}</span>
            <el-switch v-model="form.allowDownload" :active-value="true" :inactive-value="false"></el-switch>
          </div>
        </div>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">{{ $t('common.base.cancel') }}</el-button>
      <el-button type="primary" :disabled="pending" @click="makeSure()">{{ $t('common.base.makeSure') }}</el-button>
    </div>
  </base-modal>
</template>
<script>
import FormUser from '@src/component/form/components/FormUser/FormUser.vue';
import Treeselect from '@riophae/vue-treeselect';
import * as config from '@src/component/form/config';
import * as RepositoryApi from '@src/api/Repository';
import _ from 'lodash'
import { openAccurateTab } from '@src/util/platform';
import { PageRoutesTypeEnum } from 'pub-bbx-global/pageType/dist/enum/PageRoutesEnum';

export default {
  name: 'edit-title-dialog',
  components: {
    Treeselect,
    [FormUser.name]: FormUser,
  },
  props: {
    wikiConfig: {
      type: Object,
      default: () => ({}),
    },
    wikiId: {
      type: String,
      default: '',
    },
    wikiIds: {
      type: Array,
      default: () => [],
    },
    wikiDownloadAuth: {
      type: Boolean,
      default: false,
    },
    mode: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      pending: false,
      visible: false,
      form: {},
      rules: {
        title: [
          { required: true, message: i18n.t('wiki.create.textTitle.msg1'), trigger: 'blur' },
          { max: 100, message: i18n.t('wiki.create.textTitle.msg2'), trigger: 'blur' },
        ],
        typeId: [{ required: true, message: i18n.t('wiki.create.textTitle.msg3'), trigger: 'change' }],
      },
      msg: '',
    };
  },
  computed: {
    selectUserOptions() {
      return this.form?.userList?.map(i => {
        return {
          label: i.displayName || i.name,
          value: i.id || i.userId,
        };
      }) || [];
    },
  },
  methods: {
    // 描述信息展示最大数量
    TitleShowMaxLengthMax() {
      return config.FIELD_PLACEHOLER_LENGTH_MAX;
    },
    async makeSure() {
      if (!(await this.titleCheck())) {
        return;
      }
      if (!this.form.allStaff && !this.form.userList.length) {
        this.$platform.notification({
          title: this.$t('common.placeholder.selectSomething', {0: this.$t('common.base.viewInInsideRange')}),
          type: 'error',
        });
        return false;
      }
      // 详情文档设置
      if (this.mode == 'detail') {
        let params = {
          title: this.form.title,
          content: this.form.content ?? this.form.title,
          desc: this.form.desc,
          typeId: Number(this.form.typeId),
          attachment: this.form.attachment || [],
          importAttachment: this.form.importAttachment || [],
          setLabel: this.form.labelObj?.label || null,
          changeLabel: this.form.labelObj?.changeLabel || false,
          baseWikiSetting: {
            visibleRange: {
              allStaff: this.form.allStaff,
              userList: JSON.stringify(this.form.userList || []),
              userIds: this.form.userIds || [],
              roleIds: this.form.roleIds || [],
              departmentIds: this.form.departmentIds || [],
            },
            externalSharing: {
              allowExternalShare: this.form.allowExternalShare, 
              allowExternalPeopleViewComment: this.form.allowExternalShare ? this.form.allowExternalPeopleViewComment : false, 
              commentPermission: this.form.commentPermission, 
              allowDownload: this.form.allowDownload, 
            },
          },
          wikiType: this.form.wikiType,
          id: this.form.id,
        }
        this.pending = true
        RepositoryApi.saveAndSumbit(params).then(res => {
          this.pending = false
          if (res.success) {
            this.$platform.notification({
              title: res.message,
              type: 'success',
            });
            this.$emit('input', params)
            this.visible = false;
          } else {
            this.$platform.notification({
              title: res.message,
              type: 'error',
            });
          }
        })
        return;
      }
      this.$emit('input', this.form);
      this.visible = false;
    },
    openDialog(params) {
      this.form = _.cloneDeep(params);
      this.visible = true;
    },
    normalizer(node) {
      return {
        id: node.id,
        label: node.name,
        children: node.subTypes?.length ? node.subTypes : void 0,
      };
    },
    async titleCheck() {
      return await new Promise((resolve, reject) => {
        this.msg = '';
        if (!this.form.title) {
          this.msg = this.$t('wiki.create.textTitle.msg1');
          resolve(false);
        }
        if (this.form.title.length > 100) {
          resolve(false);
        }
        this.$refs.ruleForm.validate(valid => {
          if (valid) {
            resolve(true);
          } else {
            resolve(false);
          }
        });
      }).then(res => {
        return res;
      });
    },
    async openSelectUser() {
      this.$refs.usersSelect.blur();
      try {
        const options = {
          title: this.$t('common.base.viewInInsideRange'),
          selectedAll: this.form.userList || [],
        }
        const res = await this.$fast.select.multi.all(options)
        // typeId为1代表人员，用的displayName
        this.form.userList = res.data?.all?.map(i => {
          if (i.typeId == 1) {
            return {
              name: i.displayName,
              id: i.userId,
              typeId: i.typeId, 
            }
          }
          return {
            name: i.name,
            id: i.id,
            typeId: i.typeId, 
          }
        }) || [];
        this.form.userIds = res.data?.userIds || []
        this.form.roleIds = res.data?.roleIds || []
        this.form.departmentIds = (res.data?.departmentIds || []).concat(res.data?.serviceProviderIds || [])
      } catch(err) {
        console.error('[openSelectUser error]', err)
      }
    },
    handleRemoveTag(val) {
      this.form.userList = this.form.userList.filter(i => i.id != val && i.userId!= val);
    },
    goWikiSetting(){
      openAccurateTab({
        type: PageRoutesTypeEnum.PageWikiSetting
      })
    },
  },
};
</script>

<style lang="scss" scoped>
.text-input {
  width: 100%;
  height: 80px;
  padding: 8px 2px 2px 12px;
  border-radius: 2px;
  background: #ffffff;
  border: 1px solid #e4e7ed;
}
::v-deep .el-form-item--small.el-form-item {
  margin-bottom: 18px !important;
}
::v-deep .el-form .el-form-item .el-form-item__content .el-form-item__error {
  margin-left: 0 !important;
}
.select-switch {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  .switch {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
  }
}
.form-item {
  &.mt-18 {
    margin-top: 12px!important
  }
  .label-item {
    margin: 0;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    
    &.borderTop {
      border-top: 1px solid #e4e7ed;
    }
    text {
      width: 154px;
      height: 22px;
      font-family: PingFang SC;
      font-size: 14px;
      font-weight: normal;
      line-height: 22px;
      text-align: right;
    }
    ::v-deep .el-form-item__content {
      width: 100%;
    }
  }
  .title {
    padding: 5px 0px;
    height: 32px;
    .text {
      height: 22px;
      font-size: 14px;
      font-weight: 500;
      line-height: 22px;
    }
  }
  ::v-deep .el-select {
    width: 100%;
  }
  .share {
    height: 46px;
    padding: 12px 16px;
    &-tip {
      background: #F4F4F5;
      .text {
        color: #595959;
      }
    }
  }

  .big-border {
    border-radius: 4px;
    border: 1px solid #e4e7ed;
    ::v-deep .el-form-item {
      margin-bottom: 0px;
    }
  }
  .checkbox {
    width: 78px;
    height: 22px;
  }
}
label {
  width: 84px;
  height: 22px;
  font-weight: 500;
  line-height: 22px;
}
</style>
