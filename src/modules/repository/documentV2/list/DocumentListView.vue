<template>
  <base-resizable
    ref="baseResizable"
    class="document-list-view"
    :leftClosed="leftClosed"
    :leftInitialWidth="leftInitialWidth"
    :minLeftWidth="minLeftWidth"
    :maxLeftWidth="maxLeftWidth"
    :centerInitialWidth="centerInitialWidth"
    :minCenterWidth="minCenterWidth"
    :maxCenterWidth="maxCenterWidth"
    @leftWidthChange="setLeftWidth"
    @centerWidthChange="setCenterWidth"
  >
    <template slot="left">
    <div class="document-list-view-left" :class="{ 'is-closed': leftClosed }">
      <div class="left-top-wrap">
        <div class="left-top-wrap-left">
          <BizIntelligentTagsFilterPanelOperatorButton :showDot="false" :active="filterTagPanelShow" @click="changeShowTypeHandler(changeShowType == 0 ? 1 : 0)" />
        </div>
        <div class="left-top-wrap-right">
          <el-button type="primary" v-if="![1].includes(currentTreeActive.type) && allowBatchImportButton" @click="openImportModal">{{ $t('common.base.import') }} </el-button>
          <el-button class="add-btn" :class="isFault ? 'isFault' : ''" type="plain-third" v-if="allowCreate" @click="toCreate()">{{ isFault ? $t('common.wiki.createFault') : $t('common.base.createFile') }}</el-button>
        </div>
      </div>
      <!-- 知识库 -->
      <div class="doc-knowledge" v-if="changeShowType == 0">
        <!-- el-tree 存在一个问题，改变其他下拉框或者复选框，甚至打开弹窗的时候，都会莫名收起（默认全展开的时候，会莫名全展开~） -->
        <el-tree ref="typeTree" :data="treeData" node-key="id" :props="treeProps" class="typeTree" :highlight-current="true" @current-change="currentChange" :check-on-click-node="true" :default-expanded-keys="expandedTreeList" :expand-on-click-node="false">
          <span class="custom-tree-node" :data-id="data.id" slot-scope="{ node, data }">
            <span v-if="node.key === 'appendNew'">
              <el-input class="addNew" @blur="addNew()" @keyup.enter.native="handleEnter" ref="newType" v-model="newType"> </el-input>
            </span>
            <span v-else class="node-class">
              <i
                :class="{
                  'icon-fenlei1': !['draft', 'published', 'collection', 'record', 'unpublished'].includes(node.key),
                  'icon-zuijinfangwen': node.key === 'record',
                  'icon-shoucang': node.key === 'collection',
                  'icon-yifabu': node.key === 'published',
                  'icon-weifabu': node.key === 'unpublished',
                  'icon-caogaoxiang': node.key === 'draft',
                }"
                class="iconfont"
              >
              </i>
              <el-tooltip effect="dark" :content="`${node.label} ${data.wikiCount ? `（${data.wikiCount}）` : ''}`" placement="top-start" :disabled="!nodeLabelEllipsis">
                <span class="span-ellipsis" @mouseover="onShowNodeLabelTipsHandler">{{ node.label }}{{ data.wikiCount ? `（${data.wikiCount}）` : '' }}</span>
              </el-tooltip>
            </span>
            <span class="tree-node-expand">
              <!-- <div class="newTag" v-if="node.key === 'collection'">
                <span>NEW</span>
              </div> -->
              <el-button
                type="text"
                size="mini"
                class="tree-item-add tree-node-expand-btn"
                v-if="node.level === 1 && ['published', 'faultlibrary'].includes(data.id)"
                @click.stop="
                  () =>
                    appendNode(
                      node,
                      data,
                      {
                        published: 0,
                        faultlibrary: 1,
                      }[data.id]
                    )
                "
              >
                <i class="iconfont icon-add2"></i>
              </el-button>
              <el-dropdown class="tree-item-more tree-node-expand-btn" v-if="node.level !== 1 && node.key !== 'appendNew' && (allowCreate || allowEdit || allowDelete)" @command="type => handleCommand(type, node, data)">
                <el-button type="text" size="mini">
                  <i class="iconfont icon-ellipsis"></i>
                </el-button>
                <el-dropdown-menu class="tree-item-more-menu" slot="dropdown">
                  <el-dropdown-item command="new" v-if="(node.level <= 10 || node.children < 50) && (allowCreate || allowEdit)">{{ $t('common.base.createFolder') }}</el-dropdown-item>
                  <el-dropdown-item command="rename" v-if="allowCreate || allowEdit">{{ $t('common.base.rename') }}</el-dropdown-item>
                  <el-dropdown-item command="export" v-if="allowCreate || allowEdit">{{ $t('wiki.list.btn.export2') }}</el-dropdown-item>
                  <el-dropdown-item command="move" v-if="allowEdit">{{ $t('wiki.list.btn.moveTo') }}</el-dropdown-item>
                  <el-dropdown-item command="delete" v-if="allowDelete">{{ $t('common.base.delete') }}</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </span>
          </span>
        </el-tree>
      </div>
      <BizIntelligentTagsFilterPanel v-bind="filterTagsPanelBindAttr" v-on="filterTagsPanelBindOn" :show="changeShowType == 1" />
    </div>
    </template>
    <div
      @click="leftClosed=!leftClosed"
      class="closeLeft"
      :class="{
        'is-closed': leftClosed
      }"
      :style="{ left: leftInitialWidth + 2 + 'px' }"
    >
      <i
        class="iconfont"
        :class="{
          'icon-left1': !leftClosed,
          'icon-right1': leftClosed,
        }"></i>
    </div>
    <template slot="center">
    <div class="document-list-view-center" v-loading="listLoading">
      <div class="search-wrap mar-b-8" @click="openSearchModal">
        <el-input class="search-input" :placeholder="$t('common.base.search')" readonly>
          <i slot="prefix" class="el-input__icon el-icon-search"></i>
        </el-input>
      </div>
      <!-- 批量操作 -->
      <div class="filter-wrap isBatch" v-if="isBatchOperateStatus">
        <div class="filter-wrap-left">
          <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange"></el-checkbox>
          <span class="mar-l-4">{{ this.$t('component.faultLibraryDialog.haveFaultLibraryTips', { data: checkAllLength }) }}</span>
        </div>
        <div class="filter-wrap-right">
          <div class="icon-item" v-if="isPublishedType && shareEnable" @click="manyShare()">
            <el-tooltip effect="dark" :content="$t('common.base.share')" placement="top">
              <i class="iconfont icon-fenxiang"></i>
            </el-tooltip>
          </div>
          <div class="icon-item" v-if="isPublishedType && authorities.INFO_COMPILER" @click="openTitleSettingDialog()">
            <el-tooltip effect="dark" :content="$t('common.base.edit')" placement="top">
              <i class="iconfont icon-bianji"></i>
            </el-tooltip>
          </div>
          <!-- 导出暂不做 -->
          <!-- <div class="icon-item" v-if="authorities.INFO_DELETE" @click="exportAll(true)">
            <el-tooltip effect="dark" :content="$t('common.base.export')" placement="top">
              <i class="iconfont icon-export-circle"></i>
            </el-tooltip>
          </div> -->
          <div class="icon-item" @click="handleMoveCatalog(currentTreeActive)" v-if="authorities.INFO_COMPILER">
            <el-tooltip effect="dark" :content="$t('common.base.move')" placement="top">
              <i class="iconfont icon-yidong"></i>
            </el-tooltip>
          </div>
          <div class="icon-item" v-if="authorities.INFO_DELETE" @click="deleteWiki()">
            <el-tooltip effect="dark" :content="$t('common.base.delete')" placement="top">
              <i class="iconfont icon-delete"></i>
            </el-tooltip>
          </div>
          <div class="icon-item" @click="changeBatchStatus">
            <el-tooltip effect="dark" :content="$t('system.frameView.quit')" placement="top">
              <i class="iconfont icon-fe-close"></i>
            </el-tooltip>
          </div>
        </div>
      </div>
      <div class="filter-wrap" v-else>
        <div class="filter-wrap-left">
          <i class="iconfont icon-left1" v-if="currentNode.level > 1" @click="goToParent()"></i>
          <span class="mar-l-3">{{ currentTreeActive.name }}</span>
        </div>
        <div class="filter-wrap-right" v-if="!['collection', 'record'].includes(currentTreeActiveId)">
          <el-select v-model="params.fileTypeList" :placeholder="$t('common.base.type')" @change="search()">
            <el-option v-for="item in typeList" :key="item.type" :label="item.name" :value="item.type"> </el-option>
          </el-select>
          <el-select v-model="params.orderDetail.column" class="search-sort">
            <el-option class="search-sort-option" @click.native="sortSearch('createtime')" value="createtime" :label="$t('common.base.column.createTime')">
              <span>{{ $t('common.base.column.createTime') }}</span>
              <span @click="changeShow()" v-if="params.orderDetail.column == 'createtime'">
                <i class="iconfont icon-paixuxia1" :style="{ color: !sortUp ? getThemeColor : '#606266' }"></i>
                <i class="iconfont icon-paixushang" :style="{ color: sortUp ? getThemeColor : '#606266' }"></i>
              </span>
            </el-option>
            <el-option class="search-sort-option" @click.native="sortSearch('updatetime')" value="updatetime" :label="$t('common.base.column.updateTime')">
              <span>{{ $t('common.base.column.updateTime') }}</span>
              <span @click="changeShow()" v-if="params.orderDetail.column == 'updatetime'">
                <i class="iconfont icon-paixuxia1" :style="{ color: !sortUp ? getThemeColor : '#606266' }"></i>
                <i class="iconfont icon-paixushang" :style="{ color: sortUp ? getThemeColor : '#606266' }"></i>
              </span>
            </el-option>
            <el-option class="search-sort-option" @click.native="sortSearch('readTimes')" v-if="!['unpublished', 'draft'].includes(currentTreeActiveId)" value="readTimes" :label="$t('wiki.list.sortLabel.label1')">
              <span>{{ $t('wiki.list.sortLabel.label1') }}</span>
              <span @click="changeShow()" v-if="params.orderDetail.column == 'readTimes'">
                <i class="iconfont icon-paixuxia1" :style="{ color: !sortUp ? getThemeColor : '#606266' }"></i>
                <i class="iconfont icon-paixushang" :style="{ color: sortUp ? getThemeColor : '#606266' }"></i>
              </span>
            </el-option>
            <el-option class="search-sort-option" value="likeTimes" :label="$t('wiki.list.sortLabel.label2')" @click.native="sortSearch('likeTimes')" v-if="!['unpublished', 'draft'].includes(currentTreeActiveId)">
              <span>{{ $t('wiki.list.sortLabel.label2') }}</span>
              <span @click="changeShow()" v-if="params.orderDetail.column == 'likeTimes'">
                <i class="iconfont icon-paixuxia1" :style="{ color: !sortUp ? getThemeColor : '#606266' }"></i>
                <i class="iconfont icon-paixushang" :style="{ color: sortUp ? getThemeColor : '#606266' }"></i>
              </span>
            </el-option>
            <el-option class="search-sort-option" value="commentTimes" :label="$t('wiki.list.sortLabel.label3')" @click.native="sortSearch('commentTimes')" v-if="!['unpublished', 'draft'].includes(currentTreeActiveId)">
              <span>{{ $t('wiki.list.sortLabel.label3') }}</span>
              <span @click="changeShow()" v-if="params.orderDetail.column == 'commentTimes'">
                <i class="iconfont icon-paixuxia1" :style="{ color: !sortUp ? getThemeColor : '#606266' }"></i>
                <i class="iconfont icon-paixushang" :style="{ color: sortUp ? getThemeColor : '#606266' }"></i>
              </span>
            </el-option>
            <el-option class="search-sort-option" value="collectionTimes" :label="$t('wiki.list.sortLabel.label4')" @click.native="sortSearch('collectionTimes')" v-if="!['unpublished', 'draft'].includes(currentTreeActiveId)">
              <span>{{ $t('wiki.list.sortLabel.label4') }}</span>
              <span @click="changeShow()" v-if="params.orderDetail.column == 'collectionTimes'">
                <i class="iconfont icon-paixuxia1" :style="{ color: !sortUp ? getThemeColor : '#606266' }"></i>
                <i class="iconfont icon-paixushang" :style="{ color: sortUp ? getThemeColor : '#606266' }"></i>
              </span>
            </el-option>
          </el-select>
          <el-tooltip effect="dark" :content="$t('common.base.batchOperation')" placement="top" v-if="listMsg.list.length">
            <i class="iconfont icon-piliang font-16 just-cur-point" @click="changeBatchStatus"></i>
          </el-tooltip>
        </div>
      </div>
      <div class="list-wrap" ref="listWrap" :class="listMsg.list.length == 0 ? 'list-no-data' : ''" v-loadmore="loadmoreOptions">
        <!-- 故障库时显示 -->
        <div class="fault-tip-wrap" v-if="isFault">
          <div class="fault-tip">{{ $t('wiki.list.faultTips') }}</div>
        </div>
        <div class="list-item" v-if="listMsg.list.length">
          <recommend-item v-for="item in listMsg.list" :key="item.id" :item="item" mode="list" :currentWikiId="currentWikiId" @toDetail="toDetail" @checked="handleChecked" :isBatchOperateStatus="isBatchOperateStatus"></recommend-item>
        </div>
        <div class="list-empty" v-else>{{ $t('common.wiki.noData') }}</div>
      </div>
    </div>
    </template>
    <!-- 详情内容 -->
    <template slot="right">
    <div  class="document-list-view-right" ref="listViewRight">
      <document-detail
        ref="documentDetail"
        v-if="currentWikiId"
        :initData="currentInitData"
        :key="currentWikiId"
        :currentWikiId="currentWikiId"
        :inList="true"
        :rightWidth="rightWidth"
        @onFileAdd="onFileAdd"
        @onFileAddError="onFileAddError"
        @onFileAddSuccess="onFileAddSuccess"
        @selectSearchResult="selectSearchResult"
        @titleSubmitSuccess="titleSubmitSuccess"
        @moveSuccess="moveSuccess()"
        @addCollectionSuccess="addCollectionSuccess"
        @cancelCollectionSuccess="cancelCollectionSuccess"
        @likeSuccess="likeSuccess"
        @cancelLikeSuccess="cancelLikeSuccess"
        @operateSuccess="operateSuccess()"
        @releaseSuccess="operateSuccessAndResetSelection()"
        @submitApproveSuccess="operateSuccessAndResetSelection()"
        @approveSuccess="operateSuccessAndResetSelection()"
        @deleteSuccess="operateSuccessAndResetSelection()">
      </document-detail>
    </div>
    </template>
    <move-catalog
      type="catalog"
      :moveTreeDataFromProp="moveTreeData"
      :getDataFromParent="true"
      v-model="moveDialogVisible"
      v-if="moveDialogVisible"
      @moveEnd="operateSuccessAndResetSelection()"
      :beMoveData="beMoveData"
      :isBatchOperateStatus="isBatchOperateStatus"
      :currentTreeActiveId="currentTreeActiveId"
      :selection="selection"
    >
    </move-catalog>

    <document-share ref="documentShare" :isBatchOperateStatus="isBatchOperateStatus" :wikiList="wikiListForShare"> </document-share>

    <!-- 导入 -->
    <import-modal :currentTreeActive="currentTreeActive" :treeData="treeData" ref="importModal" @importSuccess="importSuccess"></import-modal>
    <!-- 搜索 -->
    <search-modal :siteList="siteList" :allowOutsideSearch="allowOutsideSearch" ref="searchModal" @selectSearchResult="selectSearchResult"></search-modal>
    <!-- 文档设置弹窗 -->
    <title-setting-dialog mode="list" ref="titleSettingDialog" :wikiConfig="initData.wikiConfig" :wikiDownloadAuth="wikiDownloadAuth" :wikiIds="selection.map(item => item.id)" @success="operateSuccessAndResetSelection()" />
    <!-- 上传进度 -->
    <DownloadCenter :key="downloadCenterKey" v-if="!closeDownload" />
  </base-resizable>
</template>

<script>
import recommendItem from 'src/modules/repository/documentV2/detail/component/recommendItem.vue';
import ListFooter from '../../common/ListFooter';
import DocumentDetailView from '../detail/DocumentDetailView';
import moveCatalog from '@src/modules/repository/documentV2/components/moveCatalog';
import documentShare from '@src/modules/repository/documentV2/components/documentShare';
import ImportModal from './component/ImportModal';
import SearchModal from './component/SearchModal';
import TitleSettingDialog from '@src/modules/repository/documentV2/create/component/TitleSettingDialog.vue';
import DownloadCenter from '@src/modules/repository/documentV2/components/downloadCenter.vue'

import * as RepositoryApi from '@src/api/Repository';
import { getAllWikiOutsideSearch } from '@src/api/SettingApi';
import { getAsyncDocumentDetailInitData } from '@src/api/InitDataApi';
/* mixin */
import ThemeMixin from '@src/mixins/themeMixin/index.ts';
import { intelligentTagsListMixin } from '@src/modules/intelligentTags/mixins/index';
import DocumentMixin from '@src/modules/repository/documentV2/mixin/documentMixin.js'

import { openTabForWikiCreate, openTabForWikiView } from '@src/util/business/openTab';

import _ from 'lodash';
import { getRootWindow } from '@src/util/dom';
import i18n from '@src/locales';

/* util */
import { exportAlert } from '@src/util/alert';
import { storageGet, storageSet } from '@src/util/storage';
import { documentV2Store } from '@src/modules/repository/documentV2/store'
import { FileDownloadStatusEnum } from '@src/modules/repository/documentV2/model';
import EventBus from '@src/util/eventBus';

import { knowledgeBaseImport, knowledgeBaseImportFileTemplate, knowledgeBaseImportFile, knowledgeBaseUpdateFile, knowledgeBaseImportTemplate } from '@src/api/Import';
/* api */
import { syncWiki, deleteWiki } from '@src/api/LLMApi';

import { getRootWindowInitData } from '@src/util/window';
const rootWindowInitData = getRootWindowInitData();
const leftWidthStorageKey = 'documentListV2LeftWidth'
const centerWidthStorageKey = 'documentListV2CenterWidth'

export default {
  mixins: [ThemeMixin, intelligentTagsListMixin, DocumentMixin],
  props: {
    initData: {
      // 配置信息
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      leftInitialWidth: 241,
      minLeftWidth: 241,
      maxLeftWidth: 400,
      centerInitialWidth: 301,
      minCenterWidth: 301,
      maxCenterWidth: 500,
      params: {
        label: '', // 标签
        keyword: '', // 搜索的关键词
        pageNum: 1,
        pageSize: 50,
        orderDetail: {
          // 排序
          isSystem: 1,
          column: 'createtime',
          type: '',
          sequence: 'desc',
        },
        view: 'published',
        fasterFindId: '',
        fileTypeList: '',
      },
      tag: {
        name: '',
        show: false,
      }, // 选中的标签
      listTotal: null,
      rightWidth: 0,

      listMsg: {
        list: [],
      }, // 列表全部数据
      info: {
        // 传给右侧详情的文档id、allowShare
        id: null,
        allowShare: 0,
        isLast: false,
      },
      types: [],
      faultTypes: [],

      selectedTreeItem: {},
      treeData: [
        {
          id: 'record',
          name: i18n.t('wiki.list.treeData.label1'),
          subTypes: [],
        },
        {
          id: 'collection',
          name: i18n.t('wiki.list.treeData.label2'),
          subTypes: [],
        },
        {
          id: 'unpublished',
          name: i18n.t('wiki.list.treeData.label5'),
          subTypes: [],
        },
        {
          id: 'draft',
          name: i18n.t('wiki.list.treeData.label6'),
          subTypes: [],
        },
        {
          id: 'published',
          name: i18n.t('wiki.list.treeData.label3'),
          type: 0,
          subTypes: [],
        },
        {
          id: 'faultlibrary',
          name: i18n.t('wiki.list.treeData.label4'),
          type: 1,
          subTypes: [],
        },
      ],

      sortUp: false,
      sortWay: 'createtime',
      isBatchOperateStatus: false,
      checkAll: false,
      isIndeterminate: false,
      typeList: [
        {
          type: '',
          name: i18n.t('common.base.all'),
        },
        {
          type: 'selfBuiltWiki',
          name: i18n.t('common.base.document'),
        },
        {
          type: 'jpg,jpeg,png',
          name: i18n.t('common.base.image'),
        },
        {
          type: 'mp4,avi,mov',
          name: i18n.t('common.base.video'),
        },
        {
          type: 'doc,docx',
          name: 'Word',
        },
        {
          type: 'xls,xlsx',
          name: 'Excel',
        },
        {
          type: 'pdf',
          name: 'PDF',
        },
        {
          type: 'ppt,pptx',
          name: 'PPT',
        },
      ],
      loadmoreOptions: {
        disabled: false,
        callback: this.loadmore,
      },
      currentWikiId: '', // 当前选中的wikiId
      currentInitData: '', // 当前选中的初始化数据

      typeOptions: [],
      currentTreeActive: {
        type: 0,
      },
      currentNode: {},

      newType: '',

      treeProps: {
        label: 'name',
        children: 'subTypes',
      },
      beMoveData: void 0,

      moveDialogVisible: false,
      moveTreeData: [],
      currTab: 0,
      currSite: '',
      siteList: [],

      outsideLink: '',
      outsideCount: 0,
      listLoading: true,
      pending: false,

      expandedTreeList: [],

      leftClosed: false,

      ifSearchInPublished: 0,

      keyword: '',
      finalSearch: '',
      // finalSearchBackUp: '',

      isTypesAppending: false,

      wikiListForShare: [],
      shareEnable: false, // 是否有分享权限
      views: {
        0: 'published',
        1: 'faultlibrary',
      },
      exportAllLoading: false,
      batchImportLoading: false,
      batchImportAction: knowledgeBaseImport,
      batchImportAttachmentAction: knowledgeBaseImportFile,
      batchUpdateAttachmentAction: knowledgeBaseUpdateFile,
      knowledgeBaseImportFileTemplate,
      knowledgeBaseImportTemplate: knowledgeBaseImportTemplate,
      tenantId: rootWindowInitData?.user?.tenantId,
      nodeLabelEllipsis: false,
      changeShowType: 0, // 选择显示类型
      cloneParamsView: {}, // 备份搜索参数
      cloneCurrentNodeKey: '', // 备份当前选中节点key
      isInit: false, // mounted 加载过程 从true 到 false 表示mounted完整过程记录
      downloadCenterKey: 0,
      screenWidth: getRootWindow(window)?.innerWidth,
    };
  },
  components: {
    recommendItem,
    [ListFooter.name]: ListFooter,
    [DocumentDetailView.name]: DocumentDetailView,
    [moveCatalog.name]: moveCatalog,
    [documentShare.name]: documentShare,
    [ImportModal.name]: ImportModal,
    [SearchModal.name]: SearchModal,
    [TitleSettingDialog.name]: TitleSettingDialog,
    DownloadCenter,
  },
  computed: {
    taskListForDownload() {
      return documentV2Store.downloadTaskList
    },
    closeDownload() {
      return documentV2Store.closeTaskList
    },
    allowCreate() {
      return this.authorities?.VIP_INFO_CREATE;
    },
    allowEdit() {
      return this.authorities?.INFO_COMPILER;
    },
    allowDelete() {
      return this.authorities?.INFO_DELETE
    },
    allowImport() {
      return this.allowCreate;
    },
    allowImportAttachment() {
      return this.allowEdit;
    },
    allowBatchImportButton() {
      return this.allowImport;
    },
    allowOutsideSearch() {
      return this.initData.wikiConfig.outsideSearch
    },
    // 当前选择的是否是故障库
    isFault() {
      return this.currentTreeActive.type === 1;
    },
    // 故障库灰度
    isFaultGrayscale() {
      const RootWindow = getRootWindow(window);
      return RootWindow?.grayAuth?.faultLibrary;
    },
    currentTreeActiveId() {
      return this.currentTreeActive?.id;
    },
    authorities() {
      return this.initData.userInfo.authorities;
    },

    currSiteObj() {
      return this.siteList.find(site => site.id === this.currSite) || {};
    },
    isDefaultSite() {
      return ['搜狗', '360搜索', '必应'].includes(this.currSiteObj.name);
    },

    currLink() {
      if (!this.currSiteObj) return '';
      let link = this.currSiteObj.link || '';
      let search = this.finalSearch;
      // this.params.label || this.params.keyword
      if (!search) return '';
      switch (this.currSiteObj.name) {
        case '必应':
          return `https://cn.bing.com/search?q=${search}`;
        case '搜狗':
          return `https://www.sogou.com/web?query=${search}`;
        case '360搜索':
          return `https://www.so.com/s?q=${search}`;
        default:
          return link;
      }
    },
    checkAllLength() {
      return this.listMsg?.list?.filter(item => item.checked)?.length;
    },
    selection() {
      return this.listMsg?.list?.filter(item => item.checked) || [];
    },

    // 当前为published或者子分类的情况
    isPublishedType() {
      return !['draft', 'collection', 'record', 'unpublished'].includes(this.currentTreeActiveId);
    },
  },
  watch: {
    screenWidth(newWidth) {
      // 判断页面宽度小于1600以下时，自动把页面左侧导航收起，大于1600则不处理
      if (newWidth < 1600) {
        window.parent.postMessage('collapseLeftNav')
      }
    },
  },
  methods: {
    getStorageWidth() {
      const leftWidth = localStorage.getItem(leftWidthStorageKey)
      if (leftWidth && Number(leftWidth) > this.leftInitialWidth) {
        this.leftInitialWidth = Number(leftWidth)
      }
      const centerWidth = localStorage.getItem(centerWidthStorageKey)
      if (centerWidth && Number(centerWidth) > this.centerInitialWidth) {
        this.centerInitialWidth = Number(centerWidth)
      }
      this.$nextTick(() => {
        if (this.screenWidth < 1600) {
          window.parent.postMessage('collapseLeftNav')
        }
      })
    },
    setLeftWidth(width) {
      if (width <= this.minLeftWidth) {
        this.leftClosed = true
        this.$refs.baseResizable.leftWidth = 0
        return
      } else {
        this.leftClosed = false
      }
      this.leftInitialWidth = width
      localStorage.setItem(leftWidthStorageKey, width)
    },
    setCenterWidth(width) {
      this.centerInitialWidth = width
      localStorage.setItem(centerWidthStorageKey, width)
    },
    // 更新上传
    onFileAdd(file) {
      let taskList = this.taskListForDownload
      documentV2Store.closeTaskList = false

      file.downloadType = FileDownloadStatusEnum.Downloading
      file.progress = 0
      taskList.push(file)
      documentV2Store.setDownloadTaskList(taskList)
    },
    // 更新上传失败
    onFileAddError(file) {
      let taskList = this.taskListForDownload
      documentV2Store.closeTaskList = false

      taskList.forEach((item, index) => {
        if (item.id === file.id) {
          item.downloadType = FileDownloadStatusEnum.Error
          item.message = file.message
          taskList.splice(index, 1, item)
        }
      })
      this.downloadCenterKey++
      documentV2Store.setDownloadTaskList(taskList)
    },
    // 更新上传成功
    onFileAddSuccess(file) {
      let taskList = this.taskListForDownload
      documentV2Store.closeTaskList = false

      taskList.forEach((item, index) => {
        if (item.id === file.id) {
          item.message = ''
          item.progress = 100
          taskList.splice(index, 1, item)
        }
      })
      this.downloadCenterKey++
      documentV2Store.setDownloadTaskList(taskList)
    },
    setupResizeObserver() {
      const targetElement = this.$refs.listViewRight;
      const resizeObserver = new ResizeObserver(entries => {
        for (let entry of entries) {
          this.rightWidth = entry.contentRect.width;
        }
      });

      resizeObserver.observe(targetElement);

      // 在组件销毁时取消观察
      this.$once('hook:beforeDestroy', () => {
        resizeObserver.unobserve(targetElement);
      });
    },
    /**
     * @desc 切换tab逻辑
     */
    changeShowTypeHandler(val) {
      this.changeShowType = val;
      this.changeIntelligentTagsFilterPanelShow(val == 1);
      // 当切换到按标签搜索时，知识库搜索参数的wikiTypeId置空
      if (val == 1) {
        // 备份一份选中的节点信息
        this.cloneCurrentNodeKey = (this.$refs?.typeTree && this.$refs?.typeTree?.getCurrentKey()) || '';
        this.cloneParamsView = { ...this.params };
        this.params.wikiTypeId = '';
        this.params.view = '';
        this.resetIntelligentTagsSearchParams();
        this.search();
        return;
      }
      this.$nextTick(() => {
        // 如果是从标签使用统计进来，如果没有从按知识库切进来的，给个默认参数
        this.$refs?.typeTree && this.$refs?.typeTree?.setCurrentKey(this.cloneCurrentNodeKey ? this.cloneCurrentNodeKey : 'published');
        this.params.view = this.cloneParamsView?.view || 'published';
        this.search();
      });
    },
    // 导出知识库
    exportAll(isBatchOperateStatus) {
      this.exportAllLoading = true;
      let para = { ...this.params, ...this.builderIntelligentTagsSearchParams() };

      para.flag = false;
      if (para.label === '') {
        delete para.label;
      }
      // 导出全部数据
      para.pageSize = this.listTotal;
      // 批量导出
      if (isBatchOperateStatus) {
        if (!this.selection.length) {
          this.$platform.notification({
            title: this.$t('wiki.list.manyShareTitle'),
            type: 'error',
          });
          return;
        }
        para.ids = this.selection.map(v => v.id)
      }
      RepositoryApi.exportAll(para)
        .then(res => {
          if (res.succ) {
            exportAlert(res.message);
          }
        })
        .finally(() => {
          this.exportAllLoading = false;
        });
    },
    async getWikiConfig() {
      try {
        let res = await RepositoryApi.getWikiConfig();
        return res?.result?.permitShare || false;
      } catch (e) {
        console.error(e);
        return false;
      }
    },
    // 批量分享
    async manyShare() {
      if (!this.selection.length) {
        this.$platform.notification({
          title: this.$t('wiki.list.manyShareTitle'),
          type: 'error',
        });
        return;
      }
      // 点击批量分享判断是否有分享权限
      let share = await this.getWikiConfig();
      if (!share) {
        this.$message.error(this.$t('wiki.list.noShareTips'));
        return;
      }
      this.wikiListForShare = this.selection;
      let show = this.wikiListForShare.every(v => v.allowShare === 1);
      (this.$refs.documentShare || {}).shareDocument(show);
    },
    // 批量编辑权限设置
    openTitleSettingDialog() {
      if (!this.selection.length) {
        this.$platform.notification({
          title: this.$t('wiki.list.manyShareTitle'),
          type: 'error',
        });
        return;
      }
      this.$refs.titleSettingDialog.openDialog({
        allStaff: true,
        userList: [],
        allowExternalShare: false,
        allowExternalPeopleViewComment: true,
        commentPermission: 1,
        allowDownload: true,
      })
    },
    async wikiShare(wiki) {
      // 点击分享判断是否有分享权限
      let share = await this.getWikiConfig();
      if (!share) {
        this.$message.error(this.$t('wiki.list.noShareTips'));
        return;
      }
      this.wikiListForShare = [wiki];
      let show = this.wikiListForShare.every(v => v.allowShare === 1);
      (this.$refs.documentShare || {}).shareDocument(show);
    },
    findExpandedNode(list = [], data = this.treeData) {
      // 寻找所有展开的节点id
      data.forEach(data => {
        if (this.$refs.typeTree && this.$refs.typeTree.getNode(data)?.expanded === true) {
          list.push(data);
          if (data.subTypes?.length) {
            this.findExpandedNode(list, data.subTypes);
          }
        }
      });
      return list;
    },
    // 初始化数量
    async initCount() {
      try {
        let res = await RepositoryApi.getDocumentViewCount();

        if (res.success) {
          this.treeData.forEach(item => {
            if (item.id == 'published') this.$set(item, 'wikiCount', res.result?.published || 0);
            if (item.id == 'unpublished') this.$set(item, 'wikiCount', res.result?.unpublished || 0);
            if (item.id == 'faultlibrary') this.$set(item, 'wikiCount', res.result?.faultLibrary || 0);
            if (item.id == 'draft') this.$set(item, 'wikiCount', res.result?.draft || 0);
          });
        }
      } catch (err) {
        console.error(err);
      }
    },
    currTitle(index) {
      if (index === 0) {
        return this.$t('wiki.list.searchResTips.tips1', { data1: this.listTotal || 0 });
        // return `内部（${this.listMsg?.list?.length || 0}）`
      } else if (['搜狗', '360搜索', '必应'].includes(this.currSiteObj.name)) {
        return this.$t('wiki.list.searchResTips.tips2', { data1: this.outsideCount });
        // return `外部（${this.outsideCount}）`
      } else {
        // return '外部'
        return this.$t('wiki.list.searchResTips.tips3');
      }
    },
    async getAllWikiOutsideSearch() {
      try {
        const result = await getAllWikiOutsideSearch();
        if (result.succ) {
          this.siteList = (result.data || []).filter(v => v.state);
          this.currSite = this.siteList[0]?.id;
        }
      } catch (error) {
        console.error('get dispatch rules error', error);
      }
    },

    // 分类菜单处理
    handleCommand(type, node, data) {
      if (type === 'new') {
        this.appendNode(node, data, this.currentTreeActive.type);
      } else if (type === 'rename') {
        this.editNode(node, data);
      } else if (type === 'delete') {
        this.$prompt(`<p>${this.$t('wiki.list.deleteType.tips1')}</p><p>${this.$t('wiki.list.deleteType.tips3')}</p>`, this.$t('common.base.toast'), {
          dangerouslyUseHTMLString: true,
          confirmButtonText: this.$t('common.base.confirm'),
          cancelButtonText: this.$t('common.base.cancel'),
          inputValidator: (value) => {
            if (!value) return false
          },
          inputErrorMessage: this.$t('wiki.list.deleteType.tips5')
        }).then(({ value }) => {
          if (value != data.name) {
            this.$message.warning(this.$t('wiki.list.deleteType.tips4'))
            return
          }
          this.deleteType(data.id)
        })
      } else if (type === 'move') {
        this.handleMoveCatalog(data);
      } else if (type === 'export') {
        this.exportAll();
      }

      if (type == 'sync') {
        this.syncWiki(data);
      }
    },
    handleMoveCatalog(data) {
      // 批量移动时校验
      if (this.isBatchOperateStatus && !this.selection.length) {
        this.$platform.notification({
          title: this.$t('wiki.list.manyShareTitle'),
          type: 'error',
        });
        return;
      }
      this.beMoveData = data;
      this.moveDialogVisible = true;
      this.moveTreeData = this.currentTreeActive.type === 0 ? _.cloneDeep(this.types) :  _.cloneDeep(this.faultTypes)
    },
    syncWiki() {
      const params = {
        tenantId: rootWindowInitData?.user?.tenantId,
        wikiTypeId: this.params.wikiTypeId,
      };

      syncWiki(params).then(result => {
        const isSuccess = result?.success;

        if (isSuccess) {
          this.$platform.notification({
            title: '同步中, 请稍等片刻',
            type: 'success',
          });
        } else {
          this.$platform.notification({
            title: result?.message,
            type: 'error',
          });
        }
      });
    },
    deleteLLMWiki(ids) {
      const params = {
        ids,
        tenantId: rootWindowInitData?.user?.tenantId,
      };

      deleteWiki(params).then(result => {});
    },
    // 删除文章
    async deleteWiki() {
      if (!this.selection.length) {
        this.$platform.notification({
          title: this.$t('wiki.list.manyShareTitle'),
          type: 'error',
        });
        return;
      }
      try {
        if (!(await this.$platform.confirm(this.$t('wiki.list.deleteWikiTips')))) return;
        this.deleteCanClick = false;

        let params = {
          wikiIds: this.selection.map(v => v.id),
        };

        this.pending = true;
        let res = await RepositoryApi.deleteDocument(params);
        this.pending = false;

        if (res.success) {
          localStorage.removeItem(`document_article_${this.initData.userInfo.userId}`);

          this.$platform.notification({
            title: this.$t('wiki.list.deleteWikiSuccessTips'),
            type: 'success',
          });
          this.deleteCanClick = true;
          this.search();
          this.initCount();
          if (this.selection.some(v => v.id == this.currentWikiId)) {
            this.currentWikiId = '';
          }
          // 搜索弹窗数据重新搜索
          if (this.$refs.searchModal?.list?.length) {
            this.$refs.searchModal.inputSearch()
          }
          this.isBatchOperateStatus = false;

          this.deleteLLMWiki(params.wikiIds);

          if (this.changeShowType == 1) {
            this.deleteTagFetch();
          }

          // this.openFrame();
        } else {
          this.deleteCanClick = true;
          this.$platform.notification({
            title: res.message,
            type: 'error',
          });
        }
      } catch (e) {
        console.error(e);
      }
    },
    operateSuccessAndResetSelection() {
      this.operateSuccess()
      if (this.selection.some(v => v.id == this.currentWikiId)) {
        this.currentWikiId = '';
      }
    },
    // 导入成功，加个延迟
    importSuccess() {
      setTimeout(() => {
        this.operateSuccess()
      }, 300)
    },
    operateSuccess(params) {
      this.isBatchOperateStatus = false;
      this.$refs.documentDetail && this.$refs.documentDetail.getDetailTypes();
      this.getTypes();
      this.getFaultTypes();
      this.search({}, params);
      this.initCount();
      // 搜索弹窗数据重新搜索
      if (this.$refs.searchModal?.list?.length) {
        this.$refs.searchModal.inputSearch()
      }
    },
    // 标题修改成功
    titleSubmitSuccess(params) {
      let needRefresh = false
      this.listMsg.list.forEach(item => {
        if (item.id == params.id) {
          item.title = params.title
          item.desc = params.desc
          if (Number(item.typeId) !== params.typeId) {
            needRefresh = true
          }
        }
      })
      if (needRefresh) {
        this.operateSuccess()
      }
    },
    // 详情移动，加个延迟
    moveSuccess() {
      setTimeout(() => {
        this.operateSuccess()
      }, 300)
    },
    // 详情收藏
    addCollectionSuccess(id) {
      // 已收藏列表直接刷新
      if (this.currentTreeActiveId == 'collection') return this.operateSuccess({type: 'collection'})
      this.listMsg.list.forEach(item => {
        if (item.id == id) {
          item.collection = true
          item.collectionTimes += 1
        }
      })
    },
    // 详情取消收藏
    cancelCollectionSuccess(id) {
      // 已收藏列表直接刷新
      if (this.currentTreeActiveId == 'collection') return this.operateSuccess({type: 'collection'})
      this.listMsg.list.forEach(item => {
        if (item.id == id) {
          item.collection = false
          item.collectionTimes -= 1
        }
      })
    },
    // 详情点赞
    likeSuccess(id) {
      this.listMsg.list.forEach(item => {
        if (item.id == id) {
          item.like = true
          item.likeTimes += 1
        }
      })
    },
    // 详情取消点赞
    cancelLikeSuccess(id) {
      this.listMsg.list.forEach(item => {
        if (item.id == id) {
          item.like = false
          item.likeTimes -= 1
        }
      })
    },
    // 改变排序（默认false为降序）
    changeShow(init = false) {
      this.sortUp = init ? false : !this.sortUp;
    },
    // 重置
    resetParams(ifSearch = true) {
      this.searchIncludeMoreConditions = false;
      (this.params = {
        label: '', // 标签
        keyword: '', // 搜索的关键词
        pageNum: 1,
        pageSize: this.params.pageSize,
        orderDetail: this.params.orderDetail,
        view: this.params.view,
        fasterFindId: '',
        fileTypeList: '',
        wikiTypeId: this.params.wikiTypeId,
      }),
        (this.keyword = '');
      this.finalSearch = '';
      // this.finalSearchBackUp = ''
      this.outsideLink = '';

      ifSearch && this.search();
    },
    openImportModal() {
      this.$refs.importModal.open();
    },
    openSearchModal() {
      this.$refs.searchModal.open();
    },
    toCreate() {
      // 如果当前列表是故障库及其子类
      let data = {
        isFromList: true
      };
      const types = {
        published: 0,
        faultlibrary: 1,
      };
      data.wikiType = 'type' in this.currentTreeActive ? this.currentTreeActive.type : types[this.currentTreeActive.id];
      // 无数据时默认知识空间
      if (data.wikiType === undefined) {
        data.wikiType = 0
      }

      this.trackEventHandler('create');
      openTabForWikiCreate({ ...data, wikiTypeId: this.params.wikiTypeId }); // 新建知识库传递选中的分类
    },
    sortSearch(type) {
      if (this.sortWay !== type) {
        this.changeShow(true);
      }
      this.sortWay = type; // 备份，作比较

      this.params.orderDetail.sequence = this.sortUp ? 'asc' : 'desc';
      this.params.pageNum = 1;
      if (['collection', 'record'].includes(this.currentTreeActiveId)) {
        this.search({}, { type: this.currentTreeActiveId });
      } else {
        this.search();
      }
      // this.$emit('search', this.params);
    },
    // TalkingData事件埋点
    trackEventHandler(type) {
      if (type === 'create') {
        window.TDAPP.onEvent('pc：知识库列表-新建事件');
        return;
      }
      if (type === 'search') {
        window.TDAPP.onEvent('pc：知识库列表-搜索事件');
        return;
      }
      if (type === 'reset') {
        window.TDAPP.onEvent('pc：知识库列表-重置事件');
        return;
      }
    },
    // 重置排序
    initSort() {
      this.params.orderDetail.column = 'createtime';
      this.changeShow(true);
    },
    // 树形结构切换
    currentChange(data, node, isFromSearchResult) {
      // const isTagGroup = Reflect.has(data, 'tagType') && data.tagType === 'intelligentTagsGroup'
      // // 如果是标签组就return
      // if (isTagGroup) return
      if (node.key === 'appendNew') return; // 新建或编辑类目的时候

      // const isIntelligentList = data?.tagType && data.tagType === 'intelligentTagsList'
      // const isIntelligentTags =  data?.tagType && data.tagType === 'intelligentTagItem'
      // const isIntelligentTagsGroup = isTagGroup
      // setTimeout(() => {
      // this.$nextTick(() => {
      //   this.$refs.typeTree.setChecked(node.key, true, false)
      // })
      // }, 300)
      if (this.currentTreeActiveId === node.key) {
        if (isFromSearchResult) {
          this.scrollToSearchResult();
        }
        return;
      }
      this.currentTreeActive = data;
      this.currentNode = this.$refs?.['typeTree']?.getNode(this.currentTreeActive) || {};
      if (isFromSearchResult) {
        this.expandToNode(node);
        this.scrollToNode(data.id);
      }

      this.isBatchOperateStatus = false;

      if (['collection', 'record'].includes(node.key)) {
        this.params.view = 'published';
        this.params.wikiTypeId = '';
        this.resetParams(false);
        this.ifSearchInPublished = 0;
        this.search({ pageNum: 1 }, { type: node.key });
        return;
      }
      if (['published', 'draft', 'unpublished', 'faultlibrary'].includes(node.key)) {
        if (['draft', 'unpublished'].includes(node.key) && ['readTimes', 'likeTimes'].includes(this.params.orderDetail.column)) {
          this.initSort();
        }
        this.params.view = node.key;
        this.params.wikiTypeId = '';
      } else {
        this.params.view = this.views[data.type];
        this.params.wikiTypeId = node.key;
        // // 如果点击是标签或标签大分组
        // this.params.view = isIntelligentList || isIntelligentTags ? '' : (this.views[data.type] || '')
        // if(!(isIntelligentTags || isIntelligentList)) {
        //   this.params.wikiTypeId = node.key
        // } else {
        //     this.params.wikiTypeId = ''
        // }
      }

      this.params.pageNum = 1;

      // if(isIntelligentTags) {
      //   return this.filterTagsPanelBindOn.filterItemClick(data)
      // }

      // this.filterTagsPanelBindOn.filterItemClick({ id: 'all-tags'})
      // this.listLoading = !this.listLoading;
      this.search({}, { isFromSearchResult });
    },
    expandToNode(node) {
      // 展开到目标节点
      let parent = node.parent;
      while (parent) {
        parent.expand(); // 使用 expand 方法展开
        parent = parent.parent;
      }
    },
    scrollToNode(targetNodeId) {
      this.$nextTick(() => {
        // 获取 el-tree 的滚动容器
        const treeContainer = this.$refs.typeTree.$el;
        // 获取目标节点的 DOM 元素
        const nodeEl = treeContainer.querySelector(`[data-id="${targetNodeId}"]`);

        // 确保 DOM 元素存在
        if (nodeEl) {
          // 直接获取nodeEl.offsetTop一直是0，只能先这样
          let offsetTop = 0;
          treeContainer.querySelectorAll('[data-id]').forEach((el, ind) => {
            if (el.dataset?.id == targetNodeId) {
              offsetTop = (ind + 5) * 40;
            }
          });
          treeContainer.scrollTop = offsetTop - treeContainer.offsetHeight;
        } else {
          console.error('未找到目标节点的 DOM 元素');
        }
      });
    },
    // 回到上一级
    goToParent() {
      if (!this.currentNode?.parent?.id) return;
      // 获取父节点并设置为当前选中节点
      const parentNode = this.currentNode.parent;
      this.$refs.typeTree.setCurrentKey(null); // 清除之前的高亮状态
      this.$nextTick(() => {
        this.$refs.typeTree.setCurrentKey(parentNode.data.id); // 设置新的当前节点
        this.currentChange(parentNode.data, parentNode);
      });
    },
    // 移除树节点
    removeNode(node) {
      this.$refs.typeTree.remove(node);
    },
    // 普通类目下新增类目(输入框)
    appendNode(node, data, type = 0) {
      if (this.isTypesAppending) return;

      this.isTypesAppending = true;
      this.newType = this.$t('wiki.list.newType');
      this.newData = {
        id: 'appendNew',
        name: '',
        subTypes: [],
        parentId: data.id,
        type: type,
      };
      data.subTypes.unshift(this.newData);
      node.expanded = true;
      // this.$refs.typeTree.store.nodesMap[data.id].expanded = true;
      this.$nextTick(() => {
        setTimeout(() => {
          let input = this.$refs.newType?.$el?.querySelector('input');
          input.focus();
          input.select();
        }, 300);
      });
    },
    handleEnter() {
       // 在回车事件中，手动触发input的blur事件
      let input = this.$refs.newType?.$el?.querySelector('input');
      input.blur();
    },
    // 已发布下新增类目(输入框)
    addNew() {
      if (!this.newType || this.pending) {
        this.cancelAppend();
        return;
      }
      this.sumbitType(!!this.newData.idBackUp);
    },
    // 编辑类目名称（输入框）
    editNode(node, data) {
      this.newType = data.name;
      data.idBackUp = data.id;
      data.id = 'appendNew';
      this.newData = data;
      this.$nextTick(() => {
        setTimeout(() => {
          let input = this.$refs.newType?.$el?.querySelector('input');
          input.focus();
          input.select();
        }, 300);
        // this.$refs.newType && this.$refs.newType.$el.querySelector('input').focus();
      });
    },

    // 取消新增/编辑框
    cancelAppend() {
      if (this.newData.idBackUp) {
        this.newData.id = this.newData.idBackUp;
        delete this.newData.idBackUp;
      } else {
        this.removeNode(this.newData);
      }
      this.newData = void 0;
      this.newType = '';
      this.$nextTick(() => {
        this.isTypesAppending = false;
      });
    },
    // 提交编辑或添加的分类
    async sumbitType(isEdit) {
      try {
        let res;
        let parentId = this.newData.parentId;
        let params = {
          name: this.newType,
          id: isEdit ? this.newData.idBackUp : '',
          parentId: ['published', 'faultlibrary'].includes(parentId) ? '' : parentId,
          type: this.newData.type,
        };
        this.pending = true
        if (isEdit) {
          res = await RepositoryApi.updateDocumentType(params);
        } else {
          res = await RepositoryApi.addDocumentType(params);
        }
        this.pending = false

        if (res.success) {
          let msg = isEdit ? this.$t('wiki.list.sumbitType.msg1') : this.$t('wiki.list.sumbitType.msg2');
          this.$platform.notification({
            title: msg,
            type: 'success',
          });
          this.$refs.documentDetail && this.$refs.documentDetail.getDetailTypes();
          await this.getTypes();
          await this.getFaultTypes();
          // this.$nextTick(() => {
          //   this.$refs.typeTree.setChecked('published', true, false)
          // })
          // await this.getTypesCount();
          // this.search();
        } else {
          this.$platform.notification({
            title: res.message,
            type: 'error',
          });
          this.cancelAppend();
        }
        // isEdit = false;
      } catch (err) {
        console.error(err);
        this.$platform.notification({
          title: isEdit ? this.$t('wiki.list.sumbitType.msg3') : this.$t('wiki.list.sumbitType.msg4'),
          type: 'error',
        });
        this.cancelAppend();
      }
      this.$nextTick(() => {
        this.isTypesAppending = false;
      });
    },
    // 删除分类
    async deleteType(id) {
      try {
        let params = {
          typeId: id,
        };
        let res = await RepositoryApi.deleteDocumentType(params);

        if (res.success) {
          this.$platform.notification({
            title: this.$t('wiki.list.deleteType.tips2'),
            type: 'success',
          });
          this.goToParent()
          this.$refs.documentDetail && this.$refs.documentDetail.getDetailTypes();
          await this.getTypes();
          await this.getFaultTypes();
          // this.search();
        } else {
          this.$platform.notification({
            title: res.message,
            type: 'error',
          });
        }
      } catch (e) {
        console.error(e);
      }
    },
    // 获取知识空间分类二级树状结构
    async getTypes(nendSaveExpand = true) {
      return new Promise(async (resolve, reject) => {
        try {
          let res = await RepositoryApi.getDocumentTypes();
          this.types = res.result || []
          if (res.success) {
            let expandedTreeList = this.findExpandedNode();

            let parent = this.treeData.find(v => v.id === 'published');
            parent && (parent.subTypes = res.result);

            this.$nextTick(() => {
              nendSaveExpand && (this.expandedTreeList = expandedTreeList);
              this.$refs.typeTree && this.$refs.typeTree.setCurrentKey(this.currentTreeActiveId, true, false);
            });
            this.initCount();

            resolve();
          } else {
            this.$platform.notification({
              title: res.message,
              type: 'error',
            });
            reject();
          }
        } catch (err) {
          console.error(err);
        }
      });
    },
    // 获取故障库分类二级树状结构
    async getFaultTypes(nendSaveExpand = true) {
      return new Promise(async (resolve, reject) => {
        try {
          let res = await RepositoryApi.getDocumentTypes({
            type: 1,
          });
          this.faultTypes = res.result || []
          if (res.success) {
            let expandedTreeList = this.findExpandedNode();

            let parent = this.treeData.find(v => v.id === 'faultlibrary');
            parent && (parent.subTypes = res.result);

            // this.$nextTick(() => {
            //   nendSaveExpand && (this.expandedTreeList = expandedTreeList)
            //   this.$refs.typeTree.setCurrentKey(this.currentTreeActiveId, true, false)
            // })

            resolve();
          } else {
            this.$platform.notification({
              title: res.message,
              type: 'error',
            });
            reject();
          }
        } catch (err) {
          console.error(err);
        }
      });
    },
    // 加载更多
    loadmore() {
      this.loadmoreOptions.disabled = true;
      this.params.pageNum += 1;
      this.search(this.params, { isLoadMore: true });
    },
    // 获取文档库列表，将ListSearch、ListFooter组件传递的参数合并
    async search(params, { flag = false, type, searchType, isLoadMore, isFromSearchResult = false } = {}) {
      if (params) Object.assign(this.params, params);
      if (flag) {
        localStorage.setItem('wiki_pageSize', this.params.pageSize);
      }
      try {
        let fn = RepositoryApi.getDocumentList;
        if (type === 'collection') {
          fn = RepositoryApi.getAllMyCollection;
        } else if (type === 'record') {
          fn = RepositoryApi.getBrowseRecord;
        }
        let para = this.changeShowType == 1 ? { ...this.params, ...this.builderIntelligentTagsSearchParams() } : { ...this.params };
        // 如果只是在mounted中初始化，则执行该逻辑
        if (this.isInit) {
          para = {
            ...para,
            view: '',
          };
        }

        para.flag = false;
        if (para.label === '') {
          delete para.label;
        }
        if (para.fileTypeList == '') {
          delete para.fileTypeList
        } else {
          para.fileTypeList = para.fileTypeList.split(',')
        }
        this.listLoading = true;
        let res = await fn(para);

        if (res.success) {
          if (['collection', 'record'].includes(type)) {
            let list = res.result.wiki || res.result.wikiList || [];
            list.forEach(v => {
              if (v.content && v.content.includes('<')) {
                v.content = this.transHtmlToText(v.content);
              }
            });
            res.result.pageInfo.list = res.result.wiki || res.result.wikiList || [];
            res.result = res.result.pageInfo;
          }
          this.dealData(res, isLoadMore);
          // 选中的搜索结果跳转到详情
          if (isFromSearchResult) {
            this.toDetail({ id: this.currentWikiId });

            this.scrollToSearchResult();
          } else {
            !isLoadMore && this.toDetail(res.result.list[0]);
          }
        } else {
          this.toDetail();
          this.$platform.notification({
            title: res.message,
            type: 'error',
          });
          this.dealData(
            {
              result:{
                list: [],
                total: 0,
              }
            },
            isLoadMore
          );
        }
      } catch (err) {
        console.error(err);
      }
      this.listLoading = false;
    },
    scrollToSearchResult() {
      this.$nextTick(() => {
        const elements = document.querySelectorAll('[data-id]');
        const nodeEl = Array.from(elements).find(el => el.dataset?.id === this.currentWikiId);
        const lisEl = this.$refs.listWrap
        if (nodeEl) {
          const scrollOffset = nodeEl.getBoundingClientRect().top - lisEl.getBoundingClientRect().top;
          lisEl.scrollTop += scrollOffset;
        }
      });
    },
    transHtmlToText(htmlText) {
      let div = document.createElement('div');
      div.innerHTML = htmlText;
      return div.innerText;
    },
    dealData(res, isLoadMore) {
      if (this.$refs.list) this.$refs.list.resetScrollTop();
      this.listTotal = res.result.total;
      this.loadmoreOptions.disabled = !res.result.hasNextPage;
      if (res.result.list.length == 1 && res.result.nextPage == 0) {
        res.result.list[0].isLast = true;
      }

      res.result.list.forEach(item => {
        item.checked = false;
        if (typeof item.label === 'string') {
          item.label = item.label ? JSON.parse(item.label) : [];
        }
        // 标题匹配关键字
        if (this.ifSearchInPublished === 0 && item.title.indexOf('<em>') != -1) {
          let replaceReg = new RegExp('<em>', 'g');
          item.handleTitle = item.title.replace(replaceReg, '<span style="color: #FF7B00">');
          let reg = new RegExp('</em>', 'g');
          item.handleTitle = item.handleTitle.replace(reg, '</span>');
        } else if (this.ifSearchInPublished === 0 && this.keyword && item.handleTitle?.indexOf(this.keyword) != -1) {
          // 标题匹配关键字,keyword直接匹配
          let replaceReg = new RegExp(this.keyword, 'g');
          item.handleTitle = item.title?.replaceAll(replaceReg, `<span style="color: #FF7B00">${this.keyword}</span>`);
        } else {
          item.handleTitle = item.title;
        }
        // 清除富文本自带样式
        item.handleContent = item.content
          .replace(/<(style|script|iframe)[^>]*?>[\s\S]+?<\/\1\s*>/gi, '')
          .replace(/<[^>]+?>/g, '')
          .replace(/\f\n\r\t\v+/g, '')
          .replace(/>/g, '');
        // 正文匹配关键字
        if (item.handleContent.indexOf('<em>') != -1) {
          let replaceReg = new RegExp('<em>', 'g');
          item.handleContent = item.handleContent.replace(replaceReg, '<span style="color: #FF7B00">');
          let reg = new RegExp('</em>', 'g');
          item.handleContent = item.handleContent.replace(reg, '</span>');
        } else if (this.ifSearchInPublished === 1 && this.keyword && item.handleContent.indexOf(this.keyword) != -1) {
          // 正文匹配关键字,keyword直接匹配
          let replaceReg = new RegExp(this.keyword, 'g');
          item.handleContent = item.handleContent.replaceAll(replaceReg, `<span style="color: #FF7B00">${this.keyword}</span>`);
        }
        // 导入的附件文档内容为空
        if (item.importFileType) {
          item.handleContent = ''
          item.content = ''
        }
      });
      if (isLoadMore) {
        this.listMsg = {
          ...res.result,
          list: (this.listMsg.list || []).concat(res.result.list),
        };
        this.handleIndeterminate();
      } else {
        this.checkAll = false;
        this.isIndeterminate = false;
        this.listMsg = res.result;
      }
    },

    // 给子组件传过来的tag加上show属性
    setTag(tag) {
      this.tag.name = tag;
      this.tag.show = true;

      this.$refs.consoleError.setTag();
    },
    // 搜索选中数据
    selectSearchResult(item) {
      this.currentWikiId = item.id;
      const currentNode = this.$refs?.['typeTree']?.getNode(item.typeId) || {};
      const parent = currentNode.parent;
      this.params.view = parent.data.id;
      this.$refs.typeTree.setCurrentKey(null);
      this.$nextTick(() => {
        this.$refs.typeTree.setCurrentKey(currentNode.data.id);
        this.currentChange(currentNode.data, currentNode, true);
      });
    },
    async toDetail(item) {
      if (!item) {
        this.currentWikiId = '';
        return;
      }
      if (item.id === this.currentWikiId) return;
      const result = await getAsyncDocumentDetailInitData({ id: item.id });
      this.currentInitData = (result && result.data && result.data.initJson) || {};
      this.currentWikiId = item.id;
    },
    changeBatchStatus() {
      this.isBatchOperateStatus = !this.isBatchOperateStatus;
      // 切换批量操作状态时已选数据重置
      this.checkAll = false;
      this.handleCheckAllChange(false);
    },
    handleCheckAllChange(val) {
      this.isIndeterminate = false;
      this.listMsg.list.forEach(item => {
        item.checked = val;
      });
    },
    handleChecked(item, value) {
      this.listMsg.list.forEach(v => {
        if (v.id == item.id) {
          v.checked = value;
        }
      });
      this.handleIndeterminate();
    },
    handleIndeterminate() {
      const list = this.listMsg.list;
      let checkedCount = list.filter(v => v.checked)?.length;
      this.checkAll = checkedCount === list.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < list.length;
    },

    resetPageNum() {
      this.$refs.listFooter.resetPageNum();
    },
    releaseCircle(e) {
      if (e.id) {
        let data = this.listMsg;
        data.list = this.listMsg.list.map(item => {
          if (item.id == e.id) {
            item.circleState = e.circleState;
          }
          return item;
        });
        this.$set(this, 'listMsg', data);
      }
    },

    /** 选中一个目录 */
    initTreeItem(item) {
      this.selectedTreeItem = item;
    },
    nodeRender(h, node) {
      return <span>{node.tagName}</span>;
    },

    getOutsideSearchCount() {
      if (!this.isDefaultSite) return;
      let keyword = this.params.label || this.params.keyword;
      if (!keyword) {
        this.outsideCount = 0;
        return;
      }
      let type = '';
      switch (this.currSiteObj.name) {
        case '必应':
          type = 'bing';
          break;
        case '搜狗':
          type = 'sogou';
          break;
        case '360搜索':
          type = '360';
          break;
        default:
          break;
      }
      if (!type) {
        this.outsideCount = 0;
      }
      let params = {
        keyword: this.params.label || this.params.keyword,
        type,
      };
      RepositoryApi.getOutsideSearchCount(params).then(res => {
        res.code === 0 && (this.outsideCount = res.data);
      });
    },
    onShowNodeLabelTipsHandler(e) {
      if (e.target.offsetWidth < 200) {
        this.nodeLabelEllipsis = false;
      } else {
        this.nodeLabelEllipsis = true;
      }
    },
    getCurrentTreeActive() {
      let currentTreeActive = this.treeData.find(tree => tree.id === 'published');
      //监听发起审批返回到未发布状态
      if (storageGet('isCreateApprove') == 'true') {
        currentTreeActive = this.treeData.find(tree => tree.id === 'unpublished');
        this.params.view = 'unpublished';
        storageSet('isCreateApprove', '');
      }
      return currentTreeActive;
    },
    // 编辑成功
    documentEditSuccess(id) {
      this.currentWikiId = id
      this.search({}, {isFromSearchResult: true})
      this.$refs.documentDetail && this.$refs.documentDetail.getDocumentDetail()
    },
    // 更新屏幕宽度
    handleResize() {
      this.screenWidth = getRootWindow(window).innerWidth; 
    }
  },
  created() {
    this.initIntelligentTagsParams('WIKI');
  },
  async mounted() {
    this.isInit = true;
    this.currentTreeActive = this.getCurrentTreeActive();
    this.currentNode = this.$refs?.['typeTree']?.getNode(this.currentTreeActive) || {};
    this.shareEnable = await this.getWikiConfig();
    this.getTypes(false);
    this.getFaultTypes(false);
    this.getAllWikiOutsideSearch();
    this.search();

    this.tenantId = rootWindowInitData?.user?.tenantId;

    // 故障库灰度
    if (!this.isFaultGrayscale) {
      let index = this.treeData.findIndex(item => item.id === 'faultlibrary');
      this.treeData.splice(index, 1);
    }
    this.getStorageWidth()
    // this.fetchIntelligentGroupTagsList()
    // window.__exports__refresh = () => {}
    window.__exports__refresh = async () => {
      this.initCount();
      this.getTypes();
      this.getFaultTypes();
      this.search();
      this.$refs.documentDetail && this.$refs.documentDetail.getDocumentDetail()
    };
    this.isInit = false;
    this.$nextTick(() => {
      this.setupResizeObserver();
    })

    getRootWindow(window)?.addEventListener('message', (event) => {
      const { action, wikiId } = event.data;
      if (action == 'documentEditSuccess') {
        this.documentEditSuccess(wikiId)
      }
    });
    // 监听窗口大小变化事件
    getRootWindow(window)?.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    // 移除监听器
    window.removeEventListener('resize', this.handleResize);
  },
};
</script>

<style lang="scss">
.fault-tip-wrap {
  padding: 0px 12px 8px;
  .fault-tip {
    width: 100%;
    background: #ecf5ff;
    padding: 4px 8px;
    color: #595959;
    border-radius: 4px;
    font-size: 12px;
  }
}
.document-list-view {
  .list-search {
    position: relative;
    z-index: 99;
    height: 56px;
    background: #f8f8f8;
    border-bottom: 1px solid #e8eff0;
  }

  .document-list-bottom {
    display: flex;
    // height: calc(100vh - 76px);
    border-radius: 4px;
    height: 100%;

    .list {
      background: #fff;
      flex: 1;
      padding-top: 14px;
    }

    .empty {
      text-align: center;
      padding-top: 100px;

      .empty-img {
        width: 160px;
        height: 160px;
      }

      .empty-msg {
        display: block;
        padding-top: 8px;
        font-size: $font-size-base;
        color: $text-color-regular;
      }
    }

    .list-footer {
      margin-top: 3px;
      padding: 12px 16px 12px;
      text-align: right;
    }
  }
  .document-list-bottom-nav + .document-list-bottom {
    height: calc(100% - 52px);
  }
}

.search-cascader-panel {
  .el-cascader-menu__item,
  .is-active {
    line-height: 18px;
    display: flex;
    justify-content: space-between;

    & > span > .icon-qingkongshanchu {
      opacity: 0;
    }

    &:hover > span > .icon-qingkongshanchu {
      opacity: 1;
    }

    & > span > .icon-bianji {
      opacity: 0;
    }

    &:hover > span > .icon-bianji {
      opacity: 1;
    }

    .icon-bianji {
      margin-right: 5px;
      &:hover {
        color: #38a6a6;
      }
    }

    .icon-qingkongshanchu {
      margin-right: 15px;
      &:hover {
        color: #38a6a6;
      }
    }
  }
}
</style>
<style lang="scss" scoped>
.search-input-container .search-type ::v-deep .el-input__inner:hover,
.search-input-container .search-type ::v-deep .el-input__inner:focus,
.search-input-container .search-type ::v-deep .el-input__inner {
  border-color: gainsboro;
}

.document-list-view {
  padding: 10px;
  height: 100vh;
  display: flex;
  flex-direction: row;
  overflow: hidden;
  .document-list-view-left {
    min-width: 240px;
    height: 100%;
    position: relative;
    background: #fff;
    margin-right: 1px;
    overflow: hidden;
    &.is-closed {
      width: 0;
      min-width: auto;
      ::v-deep .typeTree {
        padding: 0;
      }
    }
    .left-top-wrap {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;
      padding: 12px 12px 0;
      margin-bottom: 12px;
      .el-button + .el-button {
        margin-left: 0;
      }
      &-right {
        flex: 1;
        display: flex;
        gap: 12px;
        .el-button {
          flex: 1;
        }
      }
    }
  }
  .document-list-view-center {
    min-width: 300px;
    height: 100%;
    padding: 12px 0;
    background: #fff;
    margin-right: 1px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .search-wrap {
      padding: 0 12px;
      cursor: pointer;
      .el-input {
        width: 100%;
      }
    }
    .filter-wrap {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      height: 40px;
      &.isBatch {
        .filter-wrap-left {
          max-width: 40%;
          span {
            font-weight: normal;
          }
        }
        .filter-wrap-right {
          .icon-item {
            width: 24px;
            height: 24px;
            margin-left: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
          }
          .icon-item:hover {
            border-radius: 4px;
            background: #f5f8fa;
          }
        }
      }
      &-left {
        max-width: 35%;
        display: flex;
        align-items: center;
        .iconfont {
          font-size: 16px;
          color: #595959;
          cursor: pointer;
        }
        span {
          font-weight: 500;
          line-height: 22px;
          @include text-ellipsis();
        }
      }
      &-right {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        ::v-deep .el-input--small .el-input__inner {
          width: 80px;
          text-align: right;
          border: none;
          padding: 0 27px 0 0;
        }
        ::v-deep .el-input__suffix {
          i {
            color: #595959;
          }
        }
      }
    }
    .list-wrap {
      padding: 8px 0;
      overflow-x: hidden;
      overflow-y: scroll;
      flex: 1;
    }
    .list-wrap::-webkit-scrollbar {
      height: 5px;
    }
    .list-wrap.list-no-data::-webkit-scrollbar {
      display: none;
    }
    .list-wrap::-webkit-scrollbar-track {
      background: #fff;
      border-radius: 5px;
    }
    .list-wrap::-webkit-scrollbar-thumb {
      background: #e2e6ed;
      border-radius: 5px;
    }
    .list-empty {
      height: 100%;
      color: #BFBFBF;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .document-list-view-right {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    flex: 1;
    background: #fff;
  }
  .document-list-bottom-box {
    height: calc(100vh - 96px);
    margin-top: 12px;
    display: flex;
    flex-direction: column;
    background: #fff;
    border-radius: 4px;
    overflow: hidden;
  }
  .document-list-bottom {
    width: 100%;
    flex-direction: column;
    overflow: hidden;
    .document-list-bottom-operation {
      display: flex;
      justify-content: space-between;
      padding: 12px 16px 0;
      background: #fff;
      .document-list-bottom-right {
        flex-shrink: 0;
      }
    }
  }
}
.document-list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fff;
  padding: 16px;
  border-radius: 4px;
  .search-input-container {
    font-size: 0;
    white-space: nowrap;
    ::v-deep .el-input {
      // width: 297px;
      vertical-align: middle;
      // input {
      //   border-radius: 4px 0 0 4px;
      // }
    }
    ::v-deep .el-button {
      margin-left: 0;
      vertical-align: middle;
      border-radius: 0 4px 4px 0;
    }
  }
}
.document-list-footer {
  height: auto;
}

.addNew {
  display: inline-block;
  ::v-deep .el-input__inner {
    height: 26px;
    line-height: 26px;
    font-size: 14px;
  }
}
.document-list-bottom-nav {
  display: flex;
  background: #fff;
  height: 52px;
  padding: 0 16px;
  &-tab {
    font-size: 14px;
    font-weight: 400;
    line-height: 52px;
    text-align: center;
    position: relative;
    color: #111f2c;
    cursor: pointer;
    white-space: nowrap;
    margin-right: 40px;
    &.active {
      color: $color-primary-light-6;
      &:before {
        content: '';
        width: 100%;
        height: 2px;
        background: $color-primary-light-6;
        position: absolute;
        bottom: -1px;
        left: 0;
      }
    }
  }
}
.outside-box {
  flex: 1;
  position: relative;
  .site-btns {
    padding: 12px 0 12px 16px;
    background: #fff;
    border-bottom: 1px solid #e8e8e8;
  }
  .outsite-show {
    width: 100%;
    height: 100%;
    background: #fff;
  }
}

.advanced-search-visible-btn {
  font-size: 14px;
  line-height: 32px;
  @include fontColor();
  border-color: $color-primary;
  color: $color-primary;
  background: #fff;
  padding: 0 13px;
  white-space: nowrap;
  i {
    font-size: 16px;
  }
  &:hover {
    cursor: pointer;
  }
}

::v-deep .el-tree {
  .el-tree-node__content {
    height: 40px;
    line-height: 40px;
    &:hover {
      background: $color-primary-light-1;
    }
  }
  .el-tree-node:focus > .el-tree-node__content {
    background: #fff;
  }
  .el-tree-node.is-current > .el-tree-node__content {
    background: $color-primary-light-1;
  }
}
::v-deep .typeTree {
  padding: 0 12px;
  .el-tree-node__content {
    padding-right : 8px !important;
  }
  .el-tree-node__content > .el-tree-node__expand-icon {
    font-size: 16px;
  }
  .el-tree-node__content > .el-tree-node__expand-icon:not(.is-leaf) {
    color: #595959;
  }
  .custom-tree-node {
    display: flex;
    align-items: center;
    flex: 1;
    overflow: hidden;
    .node-class {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      color: #262626;
      font-size: 0;
      height: 40px;
      line-height: 40px;
      span {
        margin-left: 4px;
        font-size: 14px;
      }
    }
  }
  .tree-node-expand-btn {
    display: block;
    height: 100%;
    .iconfont:before {
      font-size: 18px;
    }
    .icon-add2 {
      color: #595959;
    }
  }

  .el-tree-node > .el-tree-node__children {
    overflow: visible;
  }
}
.newTag {
  width: 36px;
  height: 18px;
  background: rgba(255, 146, 0, 0.16);
  border-radius: 11px;
  line-height: 18px;
  font-size: 12px;
  color: #ff9200;
  text-align: center;
  & > span {
    transform: scale(0.8);
    display: block;
  }
}

.closeLeft {
  position: absolute;
  z-index: 0;
  left: 243px;
  top: 50%;
  transform: translate(0px, -21px);
  width: 14px;
  height: 42px;
  background: #ffffff;
  border: 0.5px solid $color-primary;
  display: flex;
  align-items: center;
  justify-content: center;
  color: $color-primary;
  cursor: pointer;
  border-radius: 10px;
  z-index: 999;
  &.is-closed {
    left: 5px !important;
  }

  &:hover {
    background-color: $color-primary-light-1;
    border-color: $color-primary-light-2;
    .iconfont {
      color: $color-primary-light-6;
    }
  }
}

::v-deep .el-tree-node__content {
  .tree-item-more {
    display: none;
    .iconfont {
      font-size: 18px;
    }
  }
  .tree-item-add {
    display: none;
  }
  &:hover {
    .tree-item-more {
      display: block;
    }
    .tree-item-add {
      display: block;
    }
  }
}
.tree-item-more-menu {
  margin: 0 !important;
}

.add-btn {
  &.isFault {
    flex: 1;
  }
}

.nav-bottom-hr {
  margin: 0 16px;
  border-top: 0;
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
}

.search-sort {
  position: relative;
  .iconfont {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    font-size: 14px;
    color: #595959;
  }
}

::v-deep .search-input-container {
  .search-input {
    width: 257px;
    input {
      border-radius: 0;
    }
  }
  .search-type {
    vertical-align: middle;
    .el-input {
      width: 170px;
      height: 32px;
      input {
        border-right: 0;
        border-radius: 4px 0 0 4px;
      }
    }
    &.is-hidden {
      & + .search-input {
        width: 297px;
        input {
          border-radius: 4px 0 0 4px;
        }
      }
    }
  }
}
.document-list-bottom-left {
  display: flex;
  align-items: center;
  .countTips {
    font-size: 14px;
    font-weight: 400;
    color: #595959;
    line-height: 20px;
    margin-right: 24px;
  }
}

.main-title {
  font-size: 0;
  line-height: 40px;
  margin-top: 8px;
  margin-bottom: 0;
  padding-left: 12px;
  .iconfont {
    font-size: 18px;
    color: $color-primary-light-6;
    vertical-align: middle;
    margin-right: 8px;
  }
  span {
    vertical-align: middle;
    font-size: 16px;
    font-weight: 400;
    color: #262626;
    line-height: 22px;
  }
}

.batch-import-tip {
  padding: 10px;
}

.batch-import-attachment-tip {
  padding-bottom: 10px;
  a {
    color: $color-primary;
  }
}

.batch-update-attachment-tip {
  padding-bottom: 10px;
  a {
    color: $color-primary;
  }
}
.document-list-bottom-right {
  display: inline-flex;
  gap: 14px;
  align-items: center;
}
.in-tag-c + .tree-node-expand {
  display: none;
}
</style>

<style lang="scss" scoped>
.document-list-view-left {
  display: flex;
  flex-direction: column;
  .doc-button {
    padding: 16px 16px 0;

    ::v-deep .el-radio-group {
      display: flex;
      width: 100%;
    }
    ::v-deep .el-radio-button {
      flex: 1;
    }
    ::v-deep .el-radio-button__inner {
      width: 100%;
    }
  }

  .doc-knowledge  {
    height: calc(100% - 48px);
    overflow: auto;
    z-index: 2;
  }
  .doc-knowledge::-webkit-scrollbar {
    height: 5px;
  }
  .doc-knowledge::-webkit-scrollbar-track {
    background: #fff;
    border-radius: 5px;
  }
  .doc-knowledge::-webkit-scrollbar-thumb {
    background: #e2e6ed;
    border-radius: 5px;
  }
  &.is-closed {
    ::v-deep .biz-intelligent-tags__filter-panel {
      display: none;
    }
  }
  // 标签
  ::v-deep .biz-intelligent-tags__filter-panel {
    width: 100%;
    height: 100%;
    padding: 12px 16px 0;
    transition: transform 0s !important;
    transform: none;
  }
  ::v-deep .biz-intelligent-tags__filter-panel-title {
    display: none;
  }
}
.search-sort-option {
  width: 170px;
  display: flex;
  justify-content: space-between;
}
</style>
