<template>
  <base-modal width="800px" :show.sync="show" :mask-closeable="true" :show-header="false" class="search-modal">
    <div class="search-modal-content" v-loading="loading">
      <div class="search-wrap mar-b-12">
        <el-input ref="searchInput" class="search-input" :placeholder="$t('common.placeholder.inputKeyWordToSearch')" v-model.trim="params.keyword" @input="inputSearch(true)">
          <i slot="prefix" class="el-input__icon el-icon-search"></i>
        </el-input>
      </div>
      <div class="search-history-wrap mar-b-12" v-if="historyList.length && showHistoryList">
        <span class="history-label">{{ $t('common.base.lastSearch') }}</span>
        <div class="history-list">
          <span class="history-list-item" v-for="(item, index) in historyList" :key="index" @click="selectHistoryItem(item)">{{ item }}</span>
          <span class="just-cur-point" @click="clearSearchHistory()">{{ $t('common.base.clear') }}</span>
        </div>
      </div>
      <div class="list-wrap" v-loadmore="loadmoreOptions">
        <no-data-view-new class="mar-b-12" v-if="list.length == 0 && isSearched" :notice-msg="$t('wiki.list.searchResTips.tip4')"></no-data-view-new>
        <template v-else>
          <div class="list-item" v-for="item in list" :key="item.id" @click="selectSearchResult(item)">
            <div class="list-item-left">
              <!-- 文档类型 -->
              <div class="document-type mar-r-10"></div>
              <div class="document-content">
                <div class="document-title">{{ item.title }}</div>
                <div class="document-menu">{{ item.type }}</div>
              </div>
            </div>
            <div class="list-item-right">{{ item.createTime | fmt_datehour }}</div>
          </div>
        </template>
      </div>
      <div class="site-wrap" v-if="allowOutsideSearch">
        <div class="site-label">{{ $t('wiki.list.goToOutsideSearch') }}</div>
        <div class="site-item mar-l-8" v-for="item in siteList" :key="item.id" @click="openSite(item.link)">{{ item.name }}</div>
      </div>
    </div>
  </base-modal>
</template>

<script>
import NoDataViewNew from '@src/component/common/NoDataViewNew';
import * as RepositoryApi from '@src/api/Repository';
import _ from 'lodash'

export default {
  name: 'search-modal',
  props: {
    siteList: {
      type: Array,
      default: () => [],
    },
    allowOutsideSearch: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      showHistoryList: true,
      searchHistoryList: [],
      isSearched: false,
      show: false,
      loading: false,
      list: [],
      params: {
        keyword: '', // 搜索的关键词
        pageNum: 1,
        pageSize: 50,
      },
      loadmoreOptions: {
        disabled: false,
        callback: this.loadmore,
      },
    };
  },
  components: {
    NoDataViewNew,
  },
  computed: {
    // 前十条搜索历史记录
    historyList() {
      return this.searchHistoryList.slice(0, 10);
    }
  },
  watch: {
    'params.keyword': {
      handler(newValue) {
        // 清空关键词时显示
        if (!newValue) {
          this.showHistoryList = true
        }
      }
    }
  },
  mounted() {
    try {
      this.searchHistoryList = JSON.parse(localStorage.getItem('document_article_search_history') || '[]') || []
      // 去重
      this.searchHistoryList = [...new Set(this.searchHistoryList)]
    } catch (error) {
      console.error('document_article_search_history error:', error)
    }
  },
  methods: {
    selectSearchResult(item) {
      this.$emit('selectSearchResult', item);
      this.show = false;
    },
    open() {
      this.show = true;
      this.isSearched = false
      this.params.keyword = ''
      this.list = []
      this.params.pageNum = 1
      this.$nextTick(() => {
        this.$refs.searchInput.focus()
      })
    },
    inputSearch: _.debounce(function(isInput) {
      try {
        if (isInput) {
          this.params.keyword && this.searchHistoryList.unshift(this.params.keyword)
          // 去重
          this.searchHistoryList = [...new Set(this.searchHistoryList)]
          localStorage.setItem('document_article_search_history', JSON.stringify(this.searchHistoryList))
        }
      } catch (error) {
        console.log('document_article_search_history error:', error)
      }
      if (!this.params.keyword) {
        this.list = []
        this.isSearched = false
        return;
      }
      this.params.pageNum = 1;
      this.search();
    }, 500),
    selectHistoryItem(item) {
      this.params.keyword = item
      this.inputSearch(false)
    },
    clearSearchHistory() {
      this.searchHistoryList = []
      localStorage.removeItem('document_article_search_history')
    },
    search: _.debounce(function(isLoadMore) {
      this.loading = true;
      RepositoryApi.getDocumentList(this.params).then(res => {
        this.showHistoryList = false
        this.isSearched = true
        this.loading = false;
        this.loadmoreOptions.disabled = !res?.result?.hasNextPage;
        if (isLoadMore) {
          this.list = (this.list || []).concat(res?.result?.list || []);
        } else {
          this.list = res?.result?.list;
        }
      });
    }, 300),
    // 加载更多
    loadmore() {
      this.loadmoreOptions.disabled = true;
      this.params.pageNum += 1;
      this.search(true);
    },
    openSite(link) {
      if (!link) return;
      if (link.indexOf('http') == -1) {
        link = 'https://' + link;
      }
      window.open(link, '_blank');
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .base-modal {
  max-height: 600px;
}
.search-modal {
  ::v-deep .base-modal-body {
    display: flex;
    overflow: hidden;
  }
  .search-modal-content {
    position: relative;
    flex: 1;
    display: flex;
    flex-direction: column;
    ::v-deep .app-nodata-view .app-nodata-img-container {
      padding-top: 0;
    }
    .search-history-wrap {
      display: flex;
      align-items: center;
      gap: 8px;
      .history-label {
        color: #595959;
      }
      .history-list {
        flex: 1;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 8px;
        font-size: 12px;
        color: #595959;
        &-item {
          max-width: 150px;
          line-height: 20px;
          padding: 2px 10px;
          border-radius: 4px;
          background: #F0F2F5;
          cursor: pointer;
          @include text-ellipsis();
        }
      }
    }
    .list-wrap {
      flex: 1;
      padding-bottom: 46px;
      overflow-y: scroll;
    }
    .list-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px;
      cursor: pointer;
      &:hover {
        border-radius: 4px;
        background: #f5f8fa;
      }
      &-left {
        flex: 1;
        .document-title {
          line-height: 20px;
          font-weight: 500;
        }
        .document-menu {
          font-size: 12px;
          line-height: 16px;
          color: #8c8c8c;
        }
      }
      &-right {
        margin-left: 16px;
        color: #595959;
        font-size: 12px;
      }
    }
    .site-wrap {
      width: 100%;
      padding-top: 20px;
      display: flex;
      align-items: center;
      position: absolute;
      left: 0;
      bottom: 0;
      background: #fff;
      .site-label {
        line-height: 22px;
        color: #595959;
      }
      .site-item {
        padding: 0 10px;
        line-height: 24px;
        border-radius: 4px;
        border: 1px solid #e6fffb;
        background: #ecf5ff;
        color: $color-primary;
        font-size: 12px;
        cursor: pointer;
      }
    }
  }
}
</style>
