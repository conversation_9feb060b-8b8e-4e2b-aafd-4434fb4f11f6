<template>
  <div>
    <base-modal :title="$t('common.base.import')" width="600px" :show.sync="show" class="import-modal-body">
      <div class="import-modal-content">
        <div class="menu mar-b-8">{{ $t('common.base.Menu') }}：{{ typeName }}</div>
        <div class="upload-wrap">
          <div class="upload-title mar-b-12">{{ $t('common.base.uploadFile') }}</div>
          <base-upload :value="attachments" @input="upload" @remove="remove" formEditingMode="create" :isDrag="true" :useOriginFileName="true"></base-upload>
        </div>
      </div>

      <div class="dialog-footer" slot="footer">
        <el-button :disabled="pending" @click="show = false">{{ $t('common.base.cancel') }}</el-button>
        <el-button type="primary" :disabled="pending" @click="submit">{{ $t('common.base.import') }}</el-button>
      </div>
    </base-modal>
  </div>
</template>

<script>
import { theFileIsImportedAsAKnowledgeBase } from '@src/api/Repository';

export default {
  name: 'import-modal',
  props: {
    currentTreeActive: {
      type: Object,
      default: () => ({}),
    },
    treeData: {
      type: Array,
      default: () => ([])
    }
  },
  data() {
    return {
      typeName: '',
      show: false,
      pending: false,
      attachments: [],
    };
  },
  methods: {
    open() {
      this.show = true;
      let typeName = this.findLevelPath(this.treeData, this.currentTreeActive.id)
      if (typeName) {
        this.typeName = typeName
      }
    },
    // 上传
    upload(queue) {
      this.attachments = (queue || []).map(item => {
        // 使用文件原名称
        if (item.originFileName) {
          item.filename = item.originFileName
        }
        return item
      });
    },

    // 移除上传
    async remove(file) {
      if (!(await this.$platform.confirm(this.$t('common.form.preview.file.deleteFileTips')))) return;

      let index = -1;
      for (let i = 0; i < this.attachments.length; i++) {
        let f = this.attachments[i];
        if (f.url == file.url) {
          index = i;
          break;
        }
      }
      if (index >= 0) this.attachments.splice(index, 1);
    },
    submit() {
      if (!this.attachments.length) {
        this.$platform.notification({
          title: this.$t('common.placeholder.uploadSth', {data: this.$t('common.base.file')}),
          type: 'error',
        });
        return;
      }
      this.pending = true
      let currentTreeActive = this.currentTreeActive
      // 没有type时默认知识空间
      if (!currentTreeActive.hasOwnProperty('type')) {
        currentTreeActive = this.treeData.find(item => item.id == 'published')
      }
      let typeId = currentTreeActive.id
      // 选中的是知识空间时，取第一条数据
      let data = currentTreeActive.subTypes?.[0] || {}
      if (currentTreeActive.id == 'published' && data.id) {
        typeId = data.id
      }
      let typeName = this.findLevelPath(this.treeData, typeId) ?? data.name
      
      theFileIsImportedAsAKnowledgeBase({
        type: typeName,
        typeId,
        attachment: this.attachments,
      }).then(res => {
        this.pending = false
        if (res.success) {
          this.$platform.notification({
            title: this.$t('common.base.importSuccess'),
            type: 'success',
          });
          this.show = false;
          this.attachments = []
          this.$emit('importSuccess');
        } else {
          this.$platform.notification({
            title: res.message,
            type: 'error',
          });
        }
      })
    },
    // 递归函数，用于查找目标 id 的层级路径
    findLevelPath(treeData, targetId, currentPath = []) {
      for (const tree of treeData) {
        // 如果当前节点是目标节点
        if (tree.id === targetId) {
          // 如果当前路径为空，说明是第一级
          if (currentPath.length === 0) {
            return tree.name;
          } else {
            // 如果不是第一级，返回完整的父子层级路径
            return currentPath.join('/') + '/' + tree.name;
          }
        }

        // 遍历子节点
        if (tree.subTypes && tree.subTypes.length > 0) {
          // 递归调用，将当前节点名称加入路径
          const result = this.findLevelPath(tree.subTypes, targetId, [...currentPath, tree.name]);
          if (result) {
            return result; // 如果找到目标节点，返回路径
          }
        }
      }

      // 如果当前节点及其子节点都没有找到目标节点，返回 null
      return null;
    },
  },
};
</script>

<style lang="scss" scoped>
.import-modal-body {
  .import-modal-content {
    .menu {
      line-height: 46px;
      padding: 0 12px;
      color: #595959;
      background: #f4f5f5;
    }
    .upload-wrap {
      padding: 12px;
      background: #f4f5f5;
      .upload-title {
        line-height: 22px;
        color: #595959;
      }
      ::v-deep .base-file-del {
        display: inline-block;
      }
    }
  }
}
</style>
