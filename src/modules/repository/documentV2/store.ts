import { reactive } from 'vue';
export const documentV2Store = reactive({
  downloadTaskList: [],
  closeTaskList: true,
  setDownloadTaskList(val: any) {
    if (this.closeTaskList) return;
    this.downloadTaskList = val;
    console.log(val, '8 line vvv')
  },
  deleteDownloadTaskList() {
    this.closeTaskList = true;
    this.downloadTaskList = [];
  },
  getDownloadTaskList() {
    return this.downloadTaskList;
  },
  waitDownloadList: [],
  setWaitDownloadList(val: any) {
    this.downloadTaskList = val;
  },
});
