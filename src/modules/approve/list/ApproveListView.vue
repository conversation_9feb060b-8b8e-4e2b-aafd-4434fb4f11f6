<template>
  <div class="approve-list-view" v-loading.fullscreen.lock="ui.loadingListData" >
    <!-- 搜索 view -->
    <div ref="tableHeaderContainer" class="approve-search-view">
      <!-- 基础搜索 -->
      <form class="base-search" @submit.prevent="btnSearchHandler()">
        <div class="search-group">
          <el-input v-model="paramsBackup.keyword" :placeholder="$t('common.base.searchPlaceholder')">
            <i slot="prefix" class="el-input__icon el-icon-search"></i>
          </el-input>
          <el-button type="primary" native-type="submit">
            {{$t('common.base.search')}}
          </el-button>
          <el-button @click="resetParams">
            {{$t('common.base.reset')}}
          </el-button>
        </div>
        <span class="advanced-search-visible-btn" @click.self.stop="switchAdvanceSearch">
          <i class="iconfont icon-filter"></i>
          {{$t('common.base.advancedSearch')}}
        </span>
      </form>
      <!--高级搜索-->
      <base-panel :show.sync="ui.advanceSearchPanel" :width="panelWidth">
        <h3 slot="title">
          <span>{{$t('common.base.advancedSearch')}}</span>
          <el-dropdown class="pull-right" trigger="click" @command="setAdvanceSearchColumn">
            <i class="iconfont icon-xitongguanli approve-panel-btn" style="float: none;"></i>
            <el-dropdown-menu slot="dropdown" class="approve-advance-setting">
              <el-dropdown-item command="1">{{$t('common.base.table.oneCl')}}</el-dropdown-item>
              <el-dropdown-item command="2">{{$t('common.base.table.twoCl')}}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </h3>

        <el-form class="advanced-search-form" novalidate @submit.native.prevent="btnSearchHandler()">
          <div class="form-item-container" :class="{'two-columns': columnNum === 2, }">
            <!-- 发起人 -->
            <el-form-item :label="$t('common.base.promoter')">
              <base-select v-model="paramsForSelector.proposer" :remote-method="inputSearchInitiator" :placeholder="$t('common.base.pleaseSelect')" clearable>
                <template slot="option" slot-scope="{option}">
                  <div class="initiator-option-row">
                    <img :src="getInitiatorAvatar(option.head)" class="initiator-avatar"/>
                    <template v-if="isOpenData">
                      <open-data type='userName' :openid="option.staffId"></open-data>
                    </template>
                    <template v-else>
                      <span class="initiator-display-name">{{option.label}}</span>
                    </template>
                  </div>
                </template>
              </base-select>
            </el-form-item>
            <!-- 发起时间 -->
            <el-form-item :label="$t('common.label.launchTime')">
              <el-date-picker
                v-model="params.createTime"
                type="daterange"
                align="right"
                unlink-panels
                value-format="timestamp"
                range-separator="-"
                :default-time="['00:00:00', '23:59:59']"
                :start-placeholder="$t('common.base.plaeseStartTime')"
                :end-placeholder="$t('common.base.pleaseEndTime')"
                :picker-options="approveTimePickerOptions"
              ></el-date-picker>
            </el-form-item>
            <!-- 审批时间 -->
            <el-form-item :label="$t('common.base.approvalTime')">
              <el-date-picker
                v-model="params.completeTime"
                type="daterange"
                align="right"
                unlink-panels
                value-format="timestamp"
                range-separator="-"
                :default-time="['00:00:00', '23:59:59']"
                :start-placeholder="$t('common.base.plaeseStartTime')"
                :end-placeholder="$t('common.base.pleaseEndTime')"
                :picker-options="approveTimePickerOptions"
              ></el-date-picker>
            </el-form-item>
            <!-- 来源 -->
            <el-form-item :label="$t('common.base.source')">
              <el-select v-model="params.source" clearable :placeholder="$t('common.base.pleaseSelect')" @input="sourceChangeHandler">
                <el-option :value="item.value" :label="item.name" v-for="(item, idx) in newSources" :key="idx"></el-option>
              </el-select>
            </el-form-item>
            <!-- 来源为事件Event时 -->
            <template v-if="params.source === 'event'">
              <!-- 类型 for Event-->
              <el-form-item :label="$t('event.eventType')">
                <base-select v-model="paramsForSelector.eventType" :remote-method="searchEventType" clearable></base-select>
              </el-form-item>
              <!-- 流程节点 for Event -->
              <el-form-item :label="$t('task.record.processNode')">
                <el-select v-model="params.action" clearable :placeholder="$t('common.base.pleaseSelect')">
                  <el-option :value="item.value" :label="item.name" v-for="(item, idx) in processNodeForEvent" :key="idx"></el-option>
                </el-select>
              </el-form-item>
            </template>
            <!-- 来源为工单时 -->
            <template v-if="params.source === 'task'">
              <!-- 类型 for Task -->
              <el-form-item :label="$t('common.task.taskType')">
                <base-select v-model="paramsForSelector.taskType" :remote-method="searchTaskType" clearable></base-select>
              </el-form-item>
              <!-- 流程节点 for Task -->
              <el-form-item :label="$t('task.record.processNode')">
                <el-select v-model="params.action" clearable :placeholder="$t('common.base.pleaseSelect')">
                  <el-option :value="item.value" :label="item.name" v-for="(item, idx) in processNodeForOrder" :key="idx"></el-option>
                </el-select>
              </el-form-item>
            </template>
            <!-- 审批人 -->
            <el-form-item :label="$t('common.base.approveUser')">
              <base-select v-model="paramsForSelector.approvers" :remote-method="inputSearchInitiator" :placeholder="$t('common.base.pleaseSelect')" clearable>
                <template slot="option" slot-scope="{option}">
                  <div class="initiator-option-row">
                    <img :src="getInitiatorAvatar(option.head)" class="initiator-avatar"/>
                    <template v-if="isOpenData">
                      <open-data type='userName' :openid="option.staffId"></open-data>
                    </template>
                    <template v-else>
                      <span class="initiator-display-name">{{option.label}}</span>
                    </template>
                  </div>
                </template>
              </base-select>
            </el-form-item>

            <!-- start 转交人 -->
            <el-form-item :label="$t('common.base.personCharge')">
              <base-select v-model="paramsForSelector.operators" :remote-method="inputSearchInitiator" :placeholder="$t('common.base.pleaseSelect')" clearable>
                <template slot="option" slot-scope="{option}">
                  <div class="initiator-option-row">
                    <img :src="getInitiatorAvatar(option.head)" class="initiator-avatar"/>
                    <template v-if="isOpenData">
                      <open-data type='userName' :openid="option.staffId"></open-data>
                    </template>
                    <template v-else>
                      <span class="initiator-display-name">{{option.label}}</span>
                    </template>
                  </div>
                </template>
              </base-select>
            </el-form-item>
            <!-- end 转交人 -->

            <!-- start 转交时间 -->
            <el-form-item :label="$t('common.base.timeOfTransfer')">
              <el-date-picker
                v-model="params.handoverTime"
                type="daterange"
                align="right"
                unlink-panels
                range-separator="-"
                value-format="timestamp"
                :default-time="['00:00:00', '23:59:59']"
                :start-placeholder="$t('common.base.plaeseStartTime')"
                :end-placeholder="$t('common.base.pleaseEndTime')"
                :picker-options="approveTimePickerOptions"
              ></el-date-picker>
            </el-form-item>
            <!-- end 转交时间 -->

            <!-- start 原审批人 -->
            <el-form-item :label="$t('common.base.originalApprover')">
              <base-select v-model="paramsForSelector.oldHandovers" :remote-method="inputSearchInitiator" :placeholder="$t('common.base.pleaseSelect')" clearable>
                <template slot="option" slot-scope="{option}">
                  <div class="initiator-option-row">
                    <img :src="getInitiatorAvatar(option.head)" class="initiator-avatar"/>
                    <template v-if="isOpenData">
                      <open-data type='userName' :openid="option.staffId"></open-data>
                    </template>
                    <template v-else>
                      <span class="initiator-display-name">{{option.label}}</span>
                    </template>
                  </div>
                </template>
              </base-select>
            </el-form-item>
            <!-- end 原审批人 -->
          </div>
          <div class="advanced-search-btn-group">
            <el-button @click="resetParams">{{$t('common.base.reset')}}</el-button>
            <el-button type="primary" native-type="submit">{{$t('common.base.search')}}</el-button>
          </div>
        </el-form>
      </base-panel>
    </div>

    <!-- 列表 view -->
    <div class="list-group-view">
      <div ref="tableDoContainer">
        <el-form class="operation-bar-container">
          <div class="left-btn-group">

            <el-form-item :label="$t('common.base.byTheState')" class="state-btn-group mar-r-24">
              <el-radio-group :value="paramsForSelector.state" @input="stateChangeHandler" size="medium">
                <el-radio-button :label="$t('common.base.usualStatus.toBeApproved')"></el-radio-button>
                <el-radio-button :label="$t('common.base.usualStatus.passed')"></el-radio-button>
                <el-radio-button :label="$t('common.base.usualStatus.rejected')"></el-radio-button>
                <el-radio-button :label="$t('common.form.preview.stock.status.revoked')"></el-radio-button>
              </el-radio-group>
            </el-form-item>

            <el-form-item :label="$t('common.option.customerRole.label')" class="state-btn-group">
              <el-select @change="roleChangeHandler" v-model="params.mySearch" :placeholder="$t('common.base.pleaseSelect')">
                <el-option :value="item.value" :label="item.name" v-for="(item, idx) in role" :key="idx"></el-option>
              </el-select>
            </el-form-item>
          </div>

          <!-- <div class="right-btn-group"> -->
          <div class="action-button-group flex-x bg-w">
            <!-- start 转交 -->
            <el-button
              type="primary"
              @click="openTransferDialog"
              v-if="params.mySearch == 'approve'"
            >{{$t('common.base.transfer')}}</el-button>
            <!-- end 转交 -->

            <el-dropdown v-if="exportPermission" trigger="click">
              <!-- <el-dropdown trigger="click"> -->
              <!-- <span class="el-dropdown-link el-dropdown-btn" v-if="isButtonDisplayed">
                更多操作
                <i class="iconfont icon-nav-down"></i>
              </span> -->
              <div
                class="task-ai task-flex task-font14 task-c6 cur-point bg-w"
                v-if="isButtonDisplayed"
              >
                <span class="task-mr4 task-ml4">{{$t('common.base.moreOperator')}}</span>
                <i class="iconfont icon-fdn-select"></i>
              </div>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-if="isButtonDisplayed">
                  <div @click="exportApprove(false)">{{$t('common.base.export')}}</div>
                </el-dropdown-item>
                <el-dropdown-item v-else></el-dropdown-item>
                <el-dropdown-item v-if="isButtonDisplayed">
                  <div @click="exportApprove(true)">{{$t('common.base.exportAll')}}</div>
                </el-dropdown-item>
                <el-dropdown-item v-else></el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>

            <!-- 选择列 start-->
            <div class="guide-box">
              <div
                :class="['task-ai', 'task-flex', 'task-font14', 'task-c6', 'task-pointer', ' cur-point']"
                id="v-task-step-1"
                @click="showAdvancedSetting"
              >
                <span class="task-mr4">{{$t('common.base.choiceCol')}}</span>
                <i class="iconfont icon-fdn-select"></i>
              </div>
            </div>
            <!-- 选择列 end-->

            <!-- <el-dropdown :hide-on-click="false" :show-timeout="150" trigger="click">
              <span class="el-dropdown-link el-dropdown-btn">
                选择列
                <i class="iconfont icon-nav-down"></i>
              </span>
              <el-dropdown-menu slot="dropdown" class="customer-columns-dropdown-menu">
                <el-dropdown-item v-for="(item, index) in columns" :key="`${item.field}_${index}`">
                  <el-checkbox :value="item.show" @input="modifyColumnsShow($event, item)" :label="item.label"></el-checkbox>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown> -->
            <!-- <span class="el-dropdown-link el-dropdown-btn" @click="showColumnSetting">选择列<i class="iconfont icon-nav-down"></i></span> -->
          </div>
        </el-form>
      </div>
      <div class="el-table-box">
      <el-table
        v-table-style
        :data="approveDataList"
        stripe
        :border="true"
        @select="handleSelection"
        @select-all="handleSelection"
        @sort-change="sortChange"
        :highlight-current-row="false"
        :height="tableContainerHeight"
        header-row-class-name="approve-table-header"
        ref="multipleTable" class="approve-table bbx-normal-list-box"
      >
        <template slot="empty">
          <BaseListForNoData v-show="!ui.loadingListData" :notice-msg="$t('common.base.tip.noData')"></BaseListForNoData>
        </template>
        <el-table-column type="selection" align="center" class-name="select-column"></el-table-column>
        <template v-for="(column, index) in columns">
          <el-table-column
            v-if="column.show"
            :key="`${column.field}_${index}`"
            :label="column.label"
            :prop="column.field"
            :width="column.width"
            :min-width="column.minWidth || '120px'"
            :class-name="''"
            :sortable="column.sortable"
            :show-overflow-tooltip="column.field !== 'approverName'"
            :align="column.align"
            :fixed="column.fixLeft || false"
          >
            <template slot-scope="scope">
              <template v-if="column.field === 'objNo'">
                <a href="" class="view-detail-btn" @click.stop.prevent="showTaskOrEventDetail(scope.row, scope.row.source)">{{ scope.row[column.field] }}</a>
              </template>
              <template v-else-if="column.field === 'source'">
                {{ scope.row[column.field] | getEventName }}
              </template>
              <!-- 发起人 -->
              <template v-else-if="column.field === 'proposerName'">
                <template v-if="isOpenData">
                  <open-data type='userName' :openid="scope.row.proposerStaffId"></open-data>
                </template>
                <template v-else>
                  <a
                    @click="scope.row.proposerStaffId && toUserDetail(scope.row.proposer)"
                    :class="scope.row.proposerStaffId ? 'view-detail-btn view-user-detail-btn' : ''">
                    {{ scope.row[column.field] }}
                  </a>
                </template>
              </template>
              <template v-else-if="column.field === 'createTime'">
                {{ scope.row[column.field] | getFormatDate }}
              </template>
              <template v-else-if="column.field === 'completeTime'">
                {{ scope.row[column.field] | getFormatDate }}
              </template>
              <!-- 审批人 -->
              <template v-else-if="column.field === 'approverName'">
                <template v-if="isOpenData">
                  <open-data v-for="item in scope.row.approvers" :key="item.userId" type='userName' :openid="item.staffId"></open-data>
                </template>
                <template v-else>
                  {{ getApproversNameList(scope.row.approvers) }}
                </template>
              </template>
              <template v-else-if="column.field === 'approvalTime'">
                {{ scope.row[column.field] | getFormatApproveTime }}
              </template>
              <template v-else-if="column.field === 'handoverTime'">
                {{ scope.row[column.field] | getFormatDate }}
              </template>
              <!-- 转交人 -->
              <template v-else-if="column.field === 'operatorName'">
                <template v-if="isOpenData">
                  <open-data type='userName' :openid="scope.row.operatorStaffId"></open-data>
                </template>
                <template v-else>
                  {{ scope.row[column.field] }}
                </template>
              </template>
              <!-- 原审批人 -->
              <template v-else-if="column.field === 'oldHandoverName'">
                <template v-if="isOpenData">
                  <open-data type='userName' :openid="scope.row.oldHandoverStaffId"></open-data>
                </template>
                <template v-else>
                  {{ scope.row[column.field] }}
                </template>
              </template>
              <template v-else>
                {{ scope.row[column.field] }}
              </template>
            </template>
          </el-table-column>
        </template>

        <el-table-column :label="$t('common.base.table.col.operator')" width="120px" min-width="120px" v-if="params.state === 'unapproved'" fixed="right">
          <template slot-scope="scope" v-if="canApproveThis(scope.row)">
            <el-button type="text" @click="approveHandler(scope.row)">{{ $t('common.base.approve')}}</el-button>
          </template>
        </el-table-column>
      </el-table>
      </div>
      <div ref="tableFooterContainer" class="table-footer bbx-normal-table-footer-10">
        <div class="list-info">
          <!-- To Do -->
          共<span class="level-padding">{{ pageInfo.totalItems }}</span>记录
          <template v-if="multipleSelection&&multipleSelection.length>0">
            ，已选中<span class="approve-selected-count" @click="ui.multipleSelectionPanelShow = true">{{ multipleSelection.length }}</span>条
            <span class="approve-selected-count" @click="clearSelection">{{$t('common.base.clear')}}</span>
          </template>
        </div>
        <el-pagination
          class="approve-table-pagination"
          background
          @current-change="pageChangeHandler"
          @size-change="pageSizeChangeHandler"
          :page-sizes="defaultTableData.defaultPageSizes"
          :page-size="pageInfo.pageSize"
          :current-page="pageInfo.pageNum"
          layout="prev, pager, next, sizes, jumper"
          :total="pageInfo.totalItems"
        ></el-pagination>
      </div>
    </div>
    <!-- dialog views -->
    <!-- 列设置 -->
    <!-- <base-table-advanced-setting ref="columnSetting" @save="modifyColumnSetting" /> -->
    <!-- 导出数据 -->
    <approve-export
      ref="exportPanel"
      :columns="getExportColumns()"
      :build-params="buildExportParams"
      :validate="validateExport"
      method="post"
      action="/approve/export"
    />
    <!-- 已选中数据面板 -->
    <base-panel :show.sync="ui.multipleSelectionPanelShow" width="420px" class="base-hah">
      <h3 slot="title">
        <span>{{ $t('common.base.exportModal.selectedData', { count:multipleSelection.length})}}</span>
        <i v-if="multipleSelection.length > 0"
           class="iconfont icon-qingkongshanchu approve-panel-btn"
           @click="toggleSelection()"
           :title="$t('common.base.tip.clearChoseDataTip')" data-placement="right" v-tooltip
        ></i>
      </h3>
      <div class="approve-selected-panel">
        <div v-if="multipleSelection.length <= 0" class="approve-selected-tip">
          <img :src="nodataImage">
          <p>{{$t('common.base.tip.noSelectedDataFromList')}}</p>
        </div>
        <template v-else>
          <div class="approve-selected-list">
            <div class="approve-selected-row approve-selected-head">
              <span class="approve-selected-sn">{{$t('common.base.serialNumber')}}</span>
            </div>
            <div class="approve-selected-row" v-for="c in multipleSelection" :key="c.id">
              <span class="approve-selected-sn">{{c.objNo}}</span>
              <el-button type="primary" class="approve-selected-delete" @click="cancelSelectApprove(c)">
                <i class="iconfont icon-fe-close"></i>
              </el-button>
            </div>
          </div>
        </template>
      </div>
    </base-panel>


    <!-- start 转交弹窗 -->
    <base-modal title="转交办理" :show.sync="transferDialog.visible" width="600px" class="transfer-approve-dialog">
      <div class="base-modal-content">
        <div class="tips">{{$t('common.base.approvalAndTransfer')}}</div>
        <form-user
          :field="{ displayName: $t('common.base.personCharge') }"
          :placeholder="$t('common.base.pleaseSelectNewApprover')"
          v-model="transferDialog.approver"
          :see-all-org="true"
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="transferDialog.visible = false">{{$t('superQrcode.configurationItem.cancel')}}</el-button>
        <el-button type="primary" @click="transferApprove" :disabled="transferDialog.pending">{{$t('superQrcode.configurationItem.confirm')}}</el-button>
      </div>
    </base-modal>
    <!-- end 转交弹窗 -->

    <!-- start 选择列设置 -->
    <biz-select-column ref="advanced" @save="saveColumnStatus" />
    <!-- end 选择列设置 -->
  </div>
</template>

<script>
import i18n, { t } from '@src/locales'
import { isOpenData } from '@src/util/platform'
import _ from 'lodash';
import { formatDate, useDatePicker } from 'pub-bbx-utils';
import { checkButtonDisplayed } from '@src/util/dom';
import * as ApproveApi from '@src/api/ApproveApi';
import { safeNewDate } from '@src/util/time';
import { getOssUrl } from '@src/util/assets'
import { defaultTableData } from '@src/util/table'
import { haveWikiV2Gray } from '@src/util/grayInfo'
const nodataImage = getOssUrl('/no_data.png')
import StorageUtil from '@src/util/storage.ts';

/* enum */
import StorageModuleEnum from '@model/enum/StorageModuleEnum';
import StorageKeyEnum from '@model/enum/StorageKeyEnum';


const DEFAULT_INITIATOR_AVATAR  = getOssUrl('/avatar.png')
import ApproveExport from '../components/approveExport'
import platform from '../../../platform';

import { parse } from '@src/util/querystring';
import { isEmpty, isNotUndefined } from '@src/util/type';
import { getRootWindow } from '@src/util/dom';

import {
  mergeFieldsWithProperty,
  getFieldName
} from '@service/FieldService.ts';

/* mixin */
import ThemeMixin from '@src/mixins/themeMixin/index.ts'
import { isBasicEditionHideEvent, isBasicEditionHideknowledge, isBasicEditionHideOperationsAnalysis } from '@shb-lib/version'
import {
  VersionControlTaskMixin,
  VersionControlReportMixin,
  VersionControlProductMixin,
  VersionControlServiceProviderMixin,
  VersionControlEventMixin,
  VersionControlWikiMixin
} from '@src/mixins/versionControlMixin'

import { openTabForUserView } from '@src/util/business/openTab'

const KEY_MAP = {
  APPROVE_LIST_ADVANCE_SEARCH_COLUMN_NUMBER: 'approve_list_advance_search_column_number',
  APPROVE_LIST_VIEW: 'approve_list_view', // 本页vue中逻辑使用
  APPROVE_LIST_DATA: 'approve_list_data', // 兼容jsp中逻辑(用于点击审批按钮后跳转)
}

const sources = [
  { name: i18n.t('common.base.all'), value: '' },
  { name: i18n.t('common.base.event'), value: 'event' },
  { name: i18n.t('common.base.systemKeyword.task'), value: 'task' },
  { name: i18n.t('common.base.performanceReporting'), value: '绩效报告' },
  { name: i18n.t('common.notification.module.label7'), value: 'wiki' },
  { name: i18n.t('common.base.productRegister'), value: 'register' },
  { name: i18n.t('task.detail.components.settlement'), value: 'settle' },
  {name: '门户商城', value: 'doorShop' },
  { name: i18n.t('projectManage.setting.projectName'), value: 'project' },
  { name: i18n.t('common.base.sparePart'), value: 'out_inv' },
  { name: 'PaaS', value: 'PaaS' },
  { name: i18n.t('common.order.orderName'), value: 'indent' },
]


export default {
  name: 'approve-list-view',
  mixins: [
    ThemeMixin,
    VersionControlTaskMixin,
    VersionControlReportMixin,
    VersionControlProductMixin,
    VersionControlServiceProviderMixin,
    VersionControlEventMixin,
    VersionControlWikiMixin
  ],
  data () {
    const { nearDate} = useDatePicker()
    return {
      defaultTableData,
      nodataImage,
      isOpenData,
      approveDataList: [],
      columns: [],
      pageInfo: {
        pageSize: 10,
        pageNum: 1,
        totalItems: 0,
        totalPages: 0
      },
      ui: {
        loadingListData: false,
        advanceSearchPanel: false,
        multipleSelectionPanelShow: false,
        // approveConfirmPanelShow: false, // 审批弹框
        loadingApproveDetail: false, // 点击列表条目审批按钮时 获取详情
      },
      params: {
        keyword: '',
        proposerId: '', // 发起人Id
        createTime: [],
        completeTime: [],
        source: '',
        state: 'unapproved',
        mySearch: 'approve',
        // mySearch: '', // 联调时数据
        typeId: '',
        action: '',
        pageNum: 1,
        pageSize: 10,
        approversId: '',
        operatorId: '', // 转交人id
        handoverTime: [], // 转交时间
        oldHandover: '' // 原审批人id
      },
      paramsBackup: {
        keyword: '',
      },
      paramsForSelector: {
        proposer: [],
        eventType: [],
        taskType: [],
        state: i18n.t('common.base.usualStatus.toBeApproved'),
        approvers: [],
        operators: [],
        oldHandovers: []
      },
      proposer: [],
      sources,
      role: [
        { name: i18n.t('common.base.approvedByMe'), value: 'approve' },
        { name: i18n.t('common.base.initiatedByMe'), value: 'propose' },
        // { name: '全部审批', value: 'all' }
      ],
      processNodeForEvent: [
        { value: '', name: i18n.t('common.base.all') },
        { value: '分配', name: i18n.t('common.base.distribution') },
        { value: '开始', name: i18n.t('common.base.start') },
        { value: '完成', name: i18n.t('common.base.finish') },
        { value: '转为工单', name: i18n.t('common.event.actionStatus.convert2Task') }
      ],
      processNodeForOrder: [
        { value: '', name: i18n.t('common.base.all') },
        { value: '开始', name: i18n.t('common.base.start') },
        { value: '完成', name: i18n.t('common.base.finish') }
      ],
      columnNum: 1,
      userId: '',
      approveTimePickerOptions: {
        shortcuts: [
          nearDate(i18n.t('common.base.aboutTime.nearlyWeek'), 1, 'week'),
          nearDate(i18n.t('common.base.aboutTime.nearlyMonth'), 1, 'month'),
          nearDate(i18n.t('common.base.aboutTime.lastThreeMonths'), 3, 'month'),
        ],
      },
      multipleSelection: [],
      auth: {},
      selectedLimit: 200,
      transferDialog: { // 审批转交弹窗
        visible: false,
        pending: false,
        approver: {}
      },
      isButtonDisplayed: checkButtonDisplayed(),
      tableContainerHeight:'440px',
    }
  },
  methods: {
    toUserDetail(userId) {
      openTabForUserView(userId)
    },
    // 构建列
    async buildColumns() {
      const localStorageData = await this.getIndexedDbData();
      let columnStatus = localStorageData.columnStatus || [];

      let localColumns = columnStatus
        .map(i => (typeof i == 'string' ? { field: i, show: true } : i))
        .reduce((acc, col, currentIndex) => {
          acc[col.field] = {
            field: col,
            index: currentIndex,
          };
          return acc;
        }, {});

      let fields = this.getFixedColumns();

      if (Array.isArray(columnStatus) && columnStatus.length > 0) {
        fields = this.buildSortFields(fields, localColumns);
      }

      let columns = fields
        .map(field => {
          // let sortable = false;
          let minWidth = 120;
          return {
            ...field,
            label: field.label,
            field: field.field,
            formType: field.label,
            displayName: field.label,
            minWidth: typeof minWidth == 'number' ? minWidth : `${minWidth}px`,
            // sortable,
            isSystem: field.isSystem,
          };
        })
        .map(col => {
          let show = col.show === true;
          let { width } = col;
          let localField = localColumns[col.field]?.field || null;
          let fixLeft = localField?.fixLeft || null;
          if (null != localField) {
            if (localField.width) {
              width = typeof localField.width == 'number' ? `${localField.width}px` : localField.width;
            }
            show = localField.show !== false;
          } else {
            show = true;
          }
          col.show = show;
          col.width = width;
          col.type = 'column';
          col['fixLeft'] = fixLeft && 'left'
          return col;
        });

      this.columns = [];
      this.$nextTick(() => {
        this.$set(this, 'columns', columns.slice());
        // 并本地缓存列数据至当前实例的列数据
        this.mergeLocalStorageColumnsToColumns();
      });
    },
    /**
     * @description 合并本地缓存列数据至当前实例的列数据
     */
    async mergeLocalStorageColumnsToColumns() {
      const { columnStatus } = await this.getIndexedDbData();
      if (isEmpty(columnStatus)) return;

      mergeFieldsWithProperty(
        this.columns,
        columnStatus,
        (column, localStorageDataColumnItem) => {
          // 列名不匹配则返回
          if (getFieldName(column) !== getFieldName(localStorageDataColumnItem))
            return;
          // 覆盖列显示状态
          column.show = Boolean(localStorageDataColumnItem.show);
          // 覆盖宽度数据
          if (localStorageDataColumnItem?.width) {
            column.width = localStorageDataColumnItem.width;
          }
        }
      );
    },
    buildSortFields(originFields = [], fieldsMap = {}) {
      let fields = [];
      let unsortedFields = [];

      originFields.forEach(originField => {
        let { fieldName } = originField;
        let field = fieldsMap[fieldName];

        if (field) {
          let { index } = field;
          fields[index] = originField;
        } else {
          unsortedFields.push(originField);
        }
      });

      return fields.concat(unsortedFields);
    },
    /**
     * @description 选择列
     */
    showAdvancedSetting() {
      this.$refs.advanced.open(this.columns, this.currentTaskType);
    },
    /**
     * @description 修改选择列设置
     * @param {Object} event 事件对象
     */
    saveColumnStatus(event) {
      let columns = event.data || [];

      this.columns = [];
      this.$nextTick(() => {
        this.$set(this, 'columns', columns.slice());
        this.saveColumnStatusToStorage();
      });
      this.$message.success(this.$t('common.base.saveSuccess'));
    },
    async saveColumnStatusToStorage() {
      const localStorageData = await this.getIndexedDbData();
      let columnsStatus = null;

      // 判断是否存储选择列
      const columnsList = this.columns.map(c => ({
        field: c.fieldName,
        show: c.show,
        width: c.width,
        fixLeft: c.fixLeft
      }));

      if (localStorageData.columnStatus) {
        localStorageData.columnStatus = columnsList;
        columnsStatus = localStorageData.columnStatus;
      } else {
        columnsStatus = columnsList;
      }

      this.saveDataToIndexedDb('columnStatus', columnsStatus);
    },
    // 获取缓存数据
    async getIndexedDbData() {
      let data = {};

      try {
        data = await StorageUtil.storageGet(
          StorageKeyEnum.ApproveListColumns || 'all',
          {},
          StorageModuleEnum.Approve
        );
      } catch (error) {
        data = {};
        console.error('Caused ~ ArchiveList ~ getIndexedDbData ~ error', error);
      }
      return data;
    },
    /**
     * @description 保存数据到本地indexedDB
     */
    async saveDataToIndexedDb(key, value) {
      const data = await this.getIndexedDbData();
      data[key] = value;
      StorageUtil.storageSet(
        StorageKeyEnum.ApproveListColumns || 'all',
        data,
        StorageModuleEnum.Approve
      );
    },
    getDefaultParams () {
      return {
        keyword: '',
        proposerId: '',
        createTime: '',
        completeTime: '',
        source: '', // 来源搜索
        state: 'unapproved', // 审批状态搜索
        mySearch: 'approve', // 没有审批数据时可以传空字符串调试
        eventType: '',
        action: '',
        pageNum: 1,
        approversId: '',
        operatorId: '', // 转交人
        handoverTime: '', // 转交时间
        oldHandover: '' // 原审批人id
      }
    },
    buildParams () {
      // 两个时间拆开
      const { pageInfo, paramsBackup, params } = this
      const [createTimeStart, createTimeEnd] = params.createTime || [];
      const [completeTimeStart, completeTimeEnd] = params.completeTime || [];
      const [handoverTimeStart, handoverTimeEnd] = params.handoverTime || [];
      const { keyword = '' } = paramsBackup
      const { pageNum = 1, pageSize = 10 } = pageInfo

      this.params = {
        ...this.params,
        createTimeStart,
        createTimeEnd,
        completeTimeStart,
        completeTimeEnd,
        handoverTimeStart,
        handoverTimeEnd,
        keyword,
        pageNum,
        pageSize,
      }

      // 获取发起人参数Id
      let proposers = this.paramsForSelector.proposer;
      this.params.proposerId = (proposers && proposers.length > 0) ? proposers[0].userId : '';

      // 获取事件类型参数id
      if (this.params.source === 'task') {
        let types = this.paramsForSelector.taskType;
        this.params.typeId = (types && types.length > 0) ? types[0].id : '';
      } else if (this.params.source === 'event') {
        let types = this.paramsForSelector.eventType;
        this.params.typeId = (types && types.length > 0) ? types[0].id : '';
      } else {
        this.params.typeId = '';
      }

      // 审批人
      let approvers = this.paramsForSelector.approvers;
      this.params.approversId = (approvers && approvers.length) ? approvers[0].userId : '';

      // 转交人
      let operators = this.paramsForSelector.operators;
      this.params.operatorId = (operators && operators.length) ? operators[0].userId : '';

      // 原审批人
      let oldHandovers = this.paramsForSelector.oldHandovers;
      this.params.oldHandover = (oldHandovers && oldHandovers.length) ? oldHandovers[0].userId : '';

    },
    btnSearchHandler () {
      this.pageInfo.pageNum = 1;
      //
      this.buildParams();
      this.doSearch();
    },
    doSearch (params = this.params) {
      this.ui.loadingListData = true;
      // 字段转换 // todo 抽出方法
      params.page = params.pageNum;
      params.myId = this.userId;

      // 返回Promise供 '其他' 调用暴露方法时使用
      return ApproveApi.getApproveList(params).then((res) => {
        this.ui.loadingListData = false;
        if (!res || !res.data || !res.data.list) {
          this.approveDataList = [];
          this.pageInfo.totalItems = 0;
          this.pageInfo.totalPages = 0;
          this.pageInfo.pageNum = 1;

          return res && res.message && platform.alert(res.message);
        }
        const {data: {pages, total, pageNum}} = res;

        this.approveDataList = res.data.list;
        this.processingData(); // 将数据国际化
        this.pageInfo.totalItems = total;
        this.pageInfo.totalPages = pages;
        this.pageInfo.pageNum = Number(pageNum);

        this.matchSelected();
      }).catch((e) => {
        this.ui.loadingListData = false;
        console.info('get approve list error', e);
      })
    },
    resetParams () {
      this.params = Object.assign({pageSize: this.pageInfo.pageSize}, this.getDefaultParams())
      // 重置带有缓存字段
      this.paramsForSelector = {
        initiator: [],
        eventType: [],
        taskType: [],
        state: i18n.t('common.base.usualStatus.toBeApproved')
      }
      this.pageInfo.pageNum = 1;
      this.paramsBackup.keyword = '';
      this.doSearch();
    },
    switchAdvanceSearch () {
      this.ui.advanceSearchPanel = !this.ui.advanceSearchPanel;
    },
    setAdvanceSearchColumn (command) {
      this.columnNum = Number(command);
      localStorage.setItem(KEY_MAP.APPROVE_LIST_ADVANCE_SEARCH_COLUMN_NUMBER, command);
    },
    exportApprove (exportAll) {
      let ids = [];
      // To Do
      let fileName = `${formatDate(new Date(), 'YYYY-MM-DD')}审批中心数据.xlsx`;
      if (!exportAll) {
        if (!this.multipleSelection.length) return this.$platform.alert(i18n.t('common.base.tip.exportNoChoice'));
        ids = this.multipleSelection;
      }

      let isExportDownloadNow = true;
      this.$refs.exportPanel.open(ids, fileName, isExportDownloadNow);
    },
    showColumnSetting () {
      this.$refs.columnSetting.open(this.columns);
    },

    modifyColumnsShow (value, column) {
      this.columns = this.columns
        .map(c => {
          if (c.field === column.field) {
            c.show = value;
          }
          return c;
        });
      // let columnIsShow = this.columns.filter(c => c.show);
      // let columnIsShowIds = columnIsShow.map(field => field.field)
      this.saveDataToStorage('columnStatus', this.columns);
      // this.saveDataToStorage('columnStatusIds', columnIsShowIds);
    },
    /**
     * 获取审批页缓存数据
     * @param {String} key - 全部数据.key (approve_list_view.key)
     * @return {Any} value
     */
    getLocalStorageData (key) {
      const approveDataStr = localStorage.getItem(KEY_MAP.APPROVE_LIST_VIEW) || '{}';
      const approveData = JSON.parse(approveDataStr);
      return approveData[key];
    },
    /**
     * 存数据到当页储存中
     */
    saveDataToStorage (key, value) {
      const configDataStr = localStorage.getItem(KEY_MAP.APPROVE_LIST_VIEW) || '{}';
      const configData = JSON.parse(configDataStr);

      configData[key] = value;
      localStorage.setItem(KEY_MAP.APPROVE_LIST_VIEW, JSON.stringify(configData));
    },
    handleSelection (selection) {
      let tv = this.getComputeSelection(selection);
      let original = this.multipleSelection
        .filter(ms => this.approveDataList.some(cs => cs.objId === ms.objId));
      let unSelected = this.approveDataList
        .filter(c => original.every(oc => oc.objId !== c.objId));
      if (tv.length > this.selectedLimit) {
        unSelected.forEach(row => {
          this.$refs.multipleTable.toggleRowSelection(row, false);
        });
        // To Do
        return this.$platform.alert(`最多只能选择${this.selectedLimit}条数据`);
      }
      this.multipleSelection = tv;
    },
    getComputeSelection(selection) {
      let tv = [];

      tv = this.multipleSelection
        .filter(ms => this.approveDataList.every(c => c.objId !== ms.objId));
      tv = _.uniqWith([...tv, ...selection], _.isEqual);

      return tv;
    },
    toggleSelection(rows) {
      if (rows) {
        rows.forEach(row => {
          this.$refs.multipleTable.toggleRowSelection(row);
        });
      } else {
        this.$refs.multipleTable.clearSelection();
        this.multipleSelection = [];
      }
    },
    /**
     * 已选中面板点击取消按钮 事件处理
     */
    cancelSelectApprove (approve) {
      if (!approve || !approve.objId) return;
      this.multipleSelection = this.multipleSelection.filter(ms => ms.objId !== approve.objId);
      this.toggleSelection([approve]);
    },
    matchSelected() {
      if (!this.multipleSelection.length) return;
      // 如果后台没返回id 会出异常
      const selected = this.approveDataList
        .filter(c => {
          if (this.multipleSelection.some(sc => sc.objId === c.objId)) {
            this.multipleSelection = this.multipleSelection.filter(sc => sc.objId !== c.objId);
            this.multipleSelection.push(c);
            return c;
          }
        }) || [];
      this.$nextTick(() => {
        this.toggleSelection(selected);
      });
    },
    sortChange (option) {
      const {prop, order} = option;
      if (order) {
        this.params.sorts = [{ property: prop, direction: order === 'ascending' ? 'ASC' : 'DESC' }]
        this.doSearch();
      }
    },
    clearSelection () {
      this.toggleSelection();
    },
    pageChangeHandler (page) {
      this.params.pageNum = page;
      this.doSearch();
    },
    pageSizeChangeHandler (pageSize) {
      this.saveDataToStorage('pageSize', pageSize);
      this.params.pageNum = 1;
      this.params.pageSize = pageSize;
      this.pageInfo.pageSize = pageSize;
      this.doSearch();
    },
    sourceChangeHandler (source) {
      // 流程节点重置
      this.params.action = '';
      // 事件类型重置
      this.paramsForSelector.taskType = [];
      this.paramsForSelector.eventType = [];
    },
    stateChangeHandler (state) {
      this.paramsForSelector.state = state;
      switch (state) {
      case i18n.t('common.base.usualStatus.toBeApproved'):
        this.params.state = 'unapproved';
        break;
      case i18n.t('common.base.usualStatus.passed'):
        this.params.state = 'success';
        break;
      case i18n.t('common.base.usualStatus.rejected'):
        this.params.state = 'fail';
        break;
      case i18n.t('common.form.preview.stock.status.revoked'):
        this.params.state = 'offed';
        break;
      default:
        break;
      }

      this.pageInfo.pageNum = 1;
      this.params.pageNum = 1;
      this.doSearch();
    },
    inputSearchInitiator (e) {
      return ApproveApi.getInitiatorList({keyword: e.keyword, pageNum: e.pageNum, pageSize: e.pageSize})
        .then(res => {
          if (!res || !res.list) return;
          res.list = res.list.map(item => Object.freeze({
            label: item.displayName,
            value: item.userId,
            ...item
          }))
          return res;
        })
        .catch(err => console.info('searchInitiator function catch err', err))
    },
    searchEventType (e = {}) {
      return ApproveApi.getEventTypeList({keyword: e.keyword, pageNum: e.pageNum, pageSize: e.pageSize})
        .then(res => {
          if (!res || !res.list) return;
          if (res.list) {
            res.list = res.list.map(eventType => Object.freeze({
              label: eventType.name,
              value: eventType.id,
              ...eventType
            }))
          }
          return res;
        })
        .catch(err => console.info('searchEventType function catch err', err))
    },
    searchTaskType (e = {}) {
      return ApproveApi.getTaskTypeList({keyword: e.keyword, pageNum: e.pageNum, pageSize: e.pageSize})
        .then(res => {
          if (!res || !res.list) return;
          if (res.list) {
            res.list = res.list.map(eventType => Object.freeze({
              label: eventType.name,
              value: eventType.id,
              ...eventType
            }))
          }
          return res;
        })
        .catch(err => console.info('searchEventType function catch err', err))
    },
    /**
     * 角色变更时处理方法
     */
    roleChangeHandler (event) {
      this.pageInfo.pageNum = 1;
      this.params.mySearch = event;
      // this.params.mySearch = ''; // todo  不要提交
      this.doSearch();
    },
    /**
     * 导出validate
     */
    validateExport (ids, max) {
      let exportAll = !ids || ids.length === 0;
      return exportAll && this.pageInfo.totalItems > max ? i18n.t('common.base.tip.exportMostTip') : null;
    },

    getFixedColumns () {

      let fixedColumns =
       [
        { label: i18n.t('common.base.serialNumber'), field: 'objNo', fieldName: 'objNo', show: true, fixLeft: true, export: true, sortable: 'custom', isSystem: 1 },
        { label: i18n.t('common.base.source'), field: 'source', fieldName: 'source', show: true, fixLeft: true, export: true, sortable: 'custom', isSystem: 1 },
        { label: i18n.t('common.base.type'), field: 'typeName', fieldName: 'typeName', show: true, fixLeft: true, export: true, sortable: 'custom', isSystem: 1 },
        { label: i18n.t('task.record.processNode'), field: 'action', fieldName: 'action', show: true, fixLeft: true, export: true, sortable: 'custom', isSystem: 1 },
        { label: i18n.t('common.base.promoter'), field: 'proposerName', fieldName: 'proposerName', show: true, fixLeft: true, export: true, sortable: 'custom', isSystem: 1 },
        { label: i18n.t('wiki.detail.approveDialog.label7'), field: 'createTime', fieldName: 'createTime', show: true, fixLeft: true, export: true, sortable: 'custom', isSystem: 1 },
        { label: i18n.t('task.promoteRemark'), field: 'applyRemark', fieldName: 'applyRemark', show: true, fixLeft: true, export: true, sortable: 'custom', isSystem: 1 },
        // 注意导出时使用的字段是 approveName, 显示的时候列表item显示approvers(Array)的名称集合
        { label: i18n.t('common.base.approveUser'), field: 'approverName', fieldName: 'approverName', show: true, fixLeft: true, export: true, sortable: 'custom', isSystem: 1 },
        { label: i18n.t('common.base.approvalTime'), field: 'completeTime', fieldName: 'completeTime', show: true, fixLeft: true, export: true, sortable: 'custom', isSystem: 1 },
        { label: i18n.t('wiki.detail.approveDialog.label10'), field: 'approveRemark', fieldName: 'approveRemark', show: true, fixLeft: true, export: true, sortable: 'custom', isSystem: 1 },
        { label: i18n.t('common.base.timeOfApproval'), field: 'approvalTime', fieldName: 'approvalTime', show: true, fixLeft: true, export: true, sortable: 'custom', isSystem: 1 },
        { label: i18n.t('common.base.personCharge'), field: 'operatorName', fieldName: 'operatorName', show: true, fixLeft: true, export: true, sortable: 'custom', isSystem: 1 },
        { label: i18n.t('common.base.timeOfTransfer'), field: 'handoverTime', fieldName: 'handoverTime', show: true, fixLeft: true, export: true, sortable: 'custom', isSystem: 1 },
        { label: i18n.t('common.base.originalApprover'), field: 'oldHandoverName', fieldName: 'oldHandoverName', show: true, fixLeft: true, export: true, sortable: 'custom', isSystem: 1 }
      ]

      if (this.isShowVipApprove) {
        // 灰度情况下添加
        // fixedColumns.splice(4, 0, {
        //   label: i18n.t('common.zhongqi.title2'), field: 'approverName', fieldName: 'approverName', show: true, fixLeft: true, export: true, sortable: 'custom', isSystem: 1
        // })
      }

      return fixedColumns
    },
    /**
     * 保存approve(item)Id，用户点击列表中审批跳转后续操作
     */
    saveApproveId (id) {
      let storageKey = KEY_MAP.APPROVE_LIST_DATA;
      try {
        let storageData = localStorage.getItem(storageKey);

        if (!storageData) {
          localStorage.setItem(storageKey, JSON.stringify({}));
          storageData = localStorage.getItem(storageKey) || '{}';
        }

        storageData = JSON.parse(storageData);
        storageData[id] = id;

        localStorage.setItem(storageKey, JSON.stringify(storageData));
      } catch (e) {
        console.error('saveApproveId error', e);
      }
    },
    addTabs (id, url, title) {
      if (!window.frameElement) return;

      let fromId = window.frameElement.getAttribute('id');
      platform.openTab({
        id,
        title,
        close: true,
        url,
        fromId
      });
    },
    // todo clear
    approveHandlerTest () {
      let row = {
        objId: '3681',
        source: '绩效报告'
      }
      this.approveHandler(row);
    },
    approveHandler (row) {
      let type = row.source;
      let id = row.objId;

      let taskId = '';
      let taskUrl = '';
      let eventId = '';
      let eventUrl = '';
      let performanceId = '';
      let performanceUrl = '';
      let performanceQueryStr = '';
      let targetId = '';
      let targetUrl = '';
      let settleId = '';
      let settleUrl = '';
      let doorShopUrl = ''
      let doorShopId = ''
      let projectId = '';
      let projectUrl = '';
      
      // 两个工单事件jsp页面
      switch (type) {
      case 'task':
        taskId = `taskView_${id}`;
        taskUrl = `/task/view/${id}`;
        this.saveApproveId(id);
        this.addTabs(taskId, taskUrl, `${i18n.t('common.base.systemKeyword.task')}${row.objNo}`);
        break;
      case 'event':
        eventId = `eventView_${id}`;
        eventUrl = `/event/view/${id}`;
        this.saveApproveId(id);
        this.addTabs(eventId, eventUrl, `${i18n.t('common.base.event')}${row.objNo}`);
        break;
      case '绩效报告':
        console.info('#绩效报告条目数据#', row); // todo clear
        performanceQueryStr = 'from=approveList&action=approve';
        performanceId = `frame_tab_performanceReport${id}`;
        performanceUrl = `/performance/v2/report/desc/${id}?${performanceQueryStr}`;
          this.addTabs(performanceId, performanceUrl, i18n.t('frame.backgroundTask.text16'));
        break;
      case 'wiki':
        taskId = `document_detail_${id}`;
        taskUrl = `${haveWikiV2Gray() ? '/wikiV2' : '/wiki'}/detail/page?objId=${id}&action=approve`;
        this.saveApproveId(id);
          this.addTabs(taskId, taskUrl, i18n.t('wiki.detail.wikiDetail'));
        break;
      case 'register':
        targetId = `product_register_detail_${id}`;
        targetUrl = `/product/register/detail/${id}?from="approvelist"`;
        this.saveApproveId(id);
        this.addTabs(targetId, targetUrl, i18n.t('common.base.productRegDetail'));
        break;
      case 'settle':
        settleId = `settle_view_${id}`;
        settleUrl = `/pcResourceManage/settleDetailView?id=${id}&from="approvelist"`;
        this.saveApproveId(id);
          this.addTabs(settleId, settleUrl, i18n.t('common.base.statementDetail'));
        break;
      case 'doorShop':
        doorShopId = `door_shop_view_${id}`;
        doorShopUrl = `/shb/home/<USER>/order/detailV2?id=${id}`;
        this.saveApproveId(id);
        this.addTabs(doorShopId, doorShopUrl, `订单${row.objNo}`);
        break;
      case 'project':
        projectId = `project_type_detail_${id}`;
        projectUrl = `/project/type/view?id=${id}&templateId=${row.typeId}&from="approvelist"`;
        this.saveApproveId(id);
        this.addTabs(projectId, projectUrl, i18n.t('common.projectManage.detail.projectDetails'));
        break;
      case 'out_inv':
       targetId = `spare_part_apply_${id}`;
       targetUrl = `/partV2/repertory/apply?approveNo=${id}&action=applyDetail`;
        this.saveApproveId(id);
        this.addTabs(targetId, targetUrl, i18n.t('common.pageTitle.pageSparepartApply'));
        break;
      case 'PaaS':
        targetId = `paas_template_detail_${id}`;
        targetUrl = `/paas/#/template/detail?formContentId=${id}`;
        this.saveApproveId(id);
        this.addTabs(targetId, targetUrl, '');
        break;
      case 'indent':
        targetId = `purchaaseOrderManage_view_${id}`;
        targetUrl = `/warehouse/#/purchaaseOrderManage/view?id=${id}`;
        this.saveApproveId(id);
        this.addTabs(targetId, targetUrl, '');
        break;
      default: 
        break;
      }
    },
    /**
     * 点击列表中的编号字段，判断进入任务还是事件详情页
     */
    showTaskOrEventDetail (row, source) {
      // 编号进入 结算单详情
      let tabOptions;
      let fromId = window.frameElement.getAttribute('id');
      let objId = row.objId
      let targetPageId = ''
      let targetPageUrl = ''
      if (source === 'event') {
        tabOptions = {
          id: `eventView_${objId}`,
          title: `${i18n.t('common.base.event')}${row.objNo}`,
          close: true,
          url: `/event/view/${objId}`,
          fromId
        }
      }else if (source === 'settle') {
        // 编号进入,结算单详情
        tabOptions = {
          id: `settle_view_${objId}`,
          title: i18n.t('common.base.statementDetail'),
          close: true,
          url: `/pcResourceManage/settleDetailView?id=${objId}`,
          fromId
        }
      } else if (source === 'task') {
        tabOptions = {
          id: `taskView_${objId}`,
          title: `${i18n.t('common.base.systemKeyword.task')}${row.objNo}`,
          close: true,
          url: `/task/view/${objId}`,
          fromId
        }
      } else if (source === '绩效报告') {
        tabOptions = {
          id: `performanceReport${objId}`,
          title: i18n.t('frame.backgroundTask.text16'),
          close: true,
          url: `/performance/v2/report/desc/${objId}`,
          fromId
        }
      } else if (source == 'wiki') {
        tabOptions = {
          id: `document_detail_${objId}`,
          title: i18n.t('wiki.detail.wikiDetail'),
          close: true,
          url: `${haveWikiV2Gray() ? '/wikiV2' : '/wiki'}/detail/page?objId=${objId}`,
          fromId
        }
      } else if (source == 'register') {
        targetPageId = `product_register_detail_${objId}`;
        targetPageUrl = `/product/register/detail/${objId}?from=approvelist`;
      }else if(source == 'doorShop') {
        tabOptions = {
          id: `door_shop_view_${objId}`,
          title: `订单${row.objNo}`,
          close: true,
          url: `linkc/order/detailV2?id=${objId}`,
          fromId
        }
      } else if (source == 'project') {
        tabOptions = {
          id: `project_type_detail_${objId}`,
          title: i18n.t('common.projectManage.detail.projectDetails'),
          close: true,
          url: `/project/type/view?id=${row.objId}&templateId=${row.typeId}`,
          fromId
        }
      }else if (source == 'out_inv') {
        tabOptions = {
          id: `spare_part_apply_${objId}`,
          title: i18n.t('common.pageTitle.pageSparepartApply'),
          close: true,
          url: `/partV2/repertory/apply?approveNo=${objId}&action=applyDetail`,
          fromId
        }
      }
      else if (source == 'PaaS') {
        tabOptions = {
          id: `paas_template_detail_${objId}`,
          title: '',
          close: true,
          url: `/paas/#/template/detail?formContentId=${objId}`,
          fromId
        }
      }else if (source == 'indent') {
        tabOptions = {
          id: `purchaaseOrderManage_view_${objId}`,
          title: '',
          close: true,
          url: `/warehouse/#/purchaaseOrderManage/view?id=${objId}`,
          fromId
        }
      }

      tabOptions = tabOptions || {
        id: targetPageId,
        title: i18n.t('common.base.productRegDetail'),
        close: true,
        url: targetPageUrl,
        fromId
      }

      // /performance/v2/report/desc/3679
      platform.openTab(tabOptions)
    },

    /**
     * 构建导出参数
     * @params {Array} checkedArr
     * @params ids itemList(table)
     * */
    buildExportParams (checkedArr, ids) {
      let exportAll = !ids || ids.length === 0;
      return {
        approveChecked: checkedArr,
        selectedIds: exportAll ? '' : ids.map((elm) => elm.uuId).join(','),
        exportSearchModel: exportAll ? this.getExportParams() : ''
      }
    },
    /**
     * 字段名不同于列表字段名，且某些字段转换特殊，独立出来
     * For 导出全部使用
     */
    getExportParams () {
      let sortBy = {};

      let dynamicData = {};

      const { params } = this
      const [createTimeStart, createTimeEnd] = params.createTime || [];
      const [completeTimeStart, completeTimeEnd] = params.completeTime || [];
      // TODO bug排查 原先没有这个字段 是否属于bug
      // const [handoverTimeStart, handoverTimeEnd] = params.handoverTime || [];

      if (this.params.sorts && this.params.sorts.length) {
        sortBy[this.params.sorts[0].property] = this.params.sorts[0].direction === 'ASC';
      }
      // 动态字段设置
      if (this.params.source === 'task') {
        dynamicData.taskAction = this.params.action;
        dynamicData.taskTypeId = this.params.typeId;
        dynamicData.typeId = this.params.typeId;
      }
      if (this.params.source === 'event') {
        dynamicData.eventAction = this.params.action;
        dynamicData.eventTypeId = this.params.typeId;
        dynamicData.typeId = this.params.typeId;
      }

      // eventTypeId
      let fixedData = {
        completeTimeStart,
        completeTimeEnd,
        createTimeStart,
        createTimeEnd,
        // handoverTimeStart, // 原先没有这个字段
        // handoverTimeEnd, // 原先没有这个字段
        sortBy,
        pageSize: this.params.pageSize,
        pageNum: this.params.pageNum,
        keyword: this.params.keyword,
        proposerId: this.params.proposerId,
        source: this.params.source,
        myId: this.userId,
        state: this.params.state,
        mySearch: this.params.mySearch,
        action: this.params.action,
        // 下面三个字段目前不需要了
        isAdvanced: 0,
        ids: [],
        orderDetail: {}
      }

      return Object.assign(fixedData, dynamicData);
    },
    /**
     * 获取导出列数据
     */
    getExportColumns () {
      return this.getFixedColumns();
    },

    /**
     * 尝试从本地存储中恢复数据
     * - 列表列显隐藏配置
     * - 分页大小
     */
    recoverConfigByStorage () {
      try {
        const columns = this.getLocalStorageData('columnStatus');
        const pageSize = this.getLocalStorageData('pageSize');
        const advanceColumnNum = localStorage.getItem(KEY_MAP.APPROVE_LIST_ADVANCE_SEARCH_COLUMN_NUMBER);

        this.columns = (columns && columns.length) ? columns : this.getFixedColumns();
        this.pageInfo.pageSize = pageSize || 10;
        this.params.pageSize = pageSize || 10;
        this.columnNum = Number(advanceColumnNum || 1);
      } catch (e) {
        console.error('approveListRecoverStorageConfig error', e);
        this.columns = this.getFixedColumns();
      }
    },
    /**
     * 获取发起人头像信息
     */
    getInitiatorAvatar (avatar) {
      return avatar || DEFAULT_INITIATOR_AVATAR;
    },
    /**
     * 获取审批人名字列表
     * 原字段为直接显示approveName，现在显示approvers中的displayName 集合
     * @param {Array} value - 审批人列表集合 approveList.item.approvers
     * @return {String} - '张三,李四'
     */
    getApproversNameList (value) {
      if (!value || !value.length) return '';

      let arr = [];
      try {
        value.forEach(item => {
          let displayName = item.displayName;
          displayName && arr.push(displayName)
        })
      } catch (e) {
        return '';
      }

      return arr.join(',');
    },
    exportsRefreshUI (loading) {
      let title = i18n.t('common.base.approvalCenter');
      platform.setTabLoadingStatus({
        id: 'M_APPROVE_LIST',
        title,
        loading
      });
      // alert(`handleExport${loading}`)
    },

    /**
     * 设置权限
     */
    setPermission () {
      if (this.hasVIPApprovePermission) {
        this.role.push({ name: i18n.t('common.base.allApproved'), value: 'all' });
      }
    },

    /**
     * 是否可以审批此数据
     * - 控制审批按钮的显示隐藏
     * 必须是审批人才可以审批
     */
    canApproveThis (data) {
      if (!data) return false;
      if (data.state !== 'unapproved') return false;

      let approvers = data.approvers || [];
      let isApprover = approvers.some(approve => approve.userId === this.userId);

      return isApprover;
    },
    /**
    * @description 打开审批转交弹窗
    */
    openTransferDialog() {
      // 未选择数据
      if (!this.multipleSelection.length) return this.$platform.alert(i18n.t('common.base.pleaseSelectTheDataToBeTransferred'));

      if(this.multipleSelection.some(item=> item.source === 'PaaS')) {
        return this.$message.warning(i18n.t('common.base.pleaseSelectTheDataToBeTransferredExistPaasCheck'))
      }

      this.transferDialog.approver = {};
      this.transferDialog.visible = true;
    },
    /**
    * @description 审批转交
    */
    transferApprove() {
      let { approver } = this.transferDialog;

      // 未选择新审批人
      if (!approver.userId) return this.$platform.alert(i18n.t('common.base.pleaseSelectNewApprover'));

      this.transferDialog.pending = true;

      let approveIds = [];
      this.multipleSelection.forEach(item => {
        approveIds.push(item.uuId);
      })

      const params = {
        userId: approver.userId,
        approveIds
      }

      ApproveApi.approversDeliver(params).then(res => {
        if (res.succ) {
          this.$platform.notification({
            message: i18n.t('common.base.transferSuccess'),
            type: 'success',


          })

          this.doSearch();
          this.multipleSelection = [];
          this.transferDialog.visible = false;
        } else {
          this.$platform.alert(res.message);
        }

        this.transferDialog.pending = false;
      }).catch(err => {
        this.transferDialog.pending = false;
      })
    },

    /**
     * @des 获取列表当前的可滚动高度
     */
    knowTableContainerHeight(){
      let min = 440;
      try {
        let window_ = window.innerHeight;
        let header = this.$refs.tableHeaderContainer?.offsetHeight || 0;
        let do_ = this.$refs.tableDoContainer?.offsetHeight || 0;
        let footer = this.$refs.tableFooterContainer?.offsetHeight || 0;
        let selection = this.$refs.BaseSelectionBarComponent?.offsetHeight || 0;
        selection = selection ? selection + 8 : selection;
        min = window_ - header * 1 - do_ * 1 - footer * 1 - selection * 1 - 20 - 10;
        console.log(window_, header, do_, footer, selection, 'window_, header, do_, footer, selection');
        console.log(min, 'min')
        min = min > 440 ? min : 440;
      } catch (error) {
        console.warn(error, 'error try catch');
      }
      this.$set(this, 'tableContainerHeight', `${min}px`)
    },
    // 国际化列表数据
    processingData() {
      this.approveDataList.forEach((value, item) => {
        if (value.source == 'doorShop') {
          if (value.typeName == '门户商城退款') {
            this.approveDataList[item].typeName = i18n.t('approve.doorShop.typeName.portalMallRefund');
          }
          if (value.action == '退款申请') {
            this.approveDataList[item].action = i18n.t('approve.doorShop.action.refundApplication');
          }
        } else if (value.source == 'event') {
          // 流程节点：开始、分配、暂停、完成、转为工单、转派、取消
          switch (value.action) {
            case '开始' : this.approveDataList[item].action = i18n.t('common.base.start');break;
            case '分配' : this.approveDataList[item].action = i18n.t('common.base.distribution');break;
            case '暂停' : this.approveDataList[item].action = i18n.t('common.base.usualStatus.paused');break;
            case '完成' : this.approveDataList[item].action = i18n.t('common.base.finish');break;
            case '转为工单' : this.approveDataList[item].action = i18n.t('common.event.actionStatus.convert2Task');break;
            case '转派' : this.approveDataList[item].action = i18n.t('common.task.exceptionStatus.allot');break;
            case '取消' : this.approveDataList[item].action = i18n.t('common.base.cancel');break;
          }
        } else if (value.source == 'project') {
         
        } else if (value.source == 'register') {
          if (value.typeName == '产品注册') {
            this.approveDataList[item].typeName = i18n.t('common.base.productRegistration');
          }
          if (value.action == '新建') {
            this.approveDataList[item].action = i18n.t('common.base.create');
          }
        } else if (value.source == 'settle') {
          if (value.typeName == '服务商结算单') {
            this.approveDataList[item].typeName = i18n.t('approve.settle.typeName.vendorStatement');
          }
          if (value.action == '结算') {
            this.approveDataList[item].action = i18n.t('common.base.settleAccounts');
          }
        } else if (value.source == 'task') {
         // 流程节点：结算、完成、指派、取消、关闭、开始、回访、暂停、转派、质保审批、新建
         switch (value.action) {
            case '结算' : this.approveDataList[item].action = i18n.t('common.base.settleAccounts');break;
            case '完成' : this.approveDataList[item].action = i18n.t('common.base.finish');break;
            case '指派' : this.approveDataList[item].action = i18n.t('common.task.button.allot');break;
            case '取消' : this.approveDataList[item].action = i18n.t('common.base.cancel');break;
            case '关闭' : this.approveDataList[item].action = i18n.t('common.base.close');break;
            case '开始' : this.approveDataList[item].action = i18n.t('common.base.start');break;
            case '回访' : this.approveDataList[item].action = i18n.t('common.base.review');break;
            case '暂停' : this.approveDataList[item].action = i18n.t('common.base.usualStatus.paused');break;
            case '转派' : this.approveDataList[item].action = i18n.t('common.event.actionStatus.redeploy');break;
            case '质保审批' : this.approveDataList[item].action = i18n.t('task.edit.qualityApprove');break;
            case '新建' : this.approveDataList[item].action = i18n.t('common.base.create');break;
          }
        } else if (value.source == 'wiki') {
         // 流程节点：新建、编辑
         switch (value.action) {
            case '新建' : this.approveDataList[item].action = i18n.t('common.base.create');break;
            case '编辑' : this.approveDataList[item].action = i18n.t('common.base.edit');break;
          }
        } else if (value.source == '绩效报告') {
          // 类型：奖金制、计分制
          // 流程节点：发布
          switch (value.typeName) {
            case '奖金制' : this.approveDataList[item].typeName = i18n.t('performance.label.prizeRule');
            case '计分制' : this.approveDataList[item].typeName = i18n.t('performance.label.scoreRule');
          };
          switch (value.action) {
            case '发布' : this.approveDataList[item].action = i18n.t('common.base.release');break;
          }
        }
      })
    },
  },
  computed: {
    isShowVipApprove() {
      const RootWindow = getRootWindow(window)
      return RootWindow.grayAuth?.taskVipApprove || false
    },
    // 产品注册灰度
    isShowProductRegister() {
      const RootWindow = getRootWindow(window)
      return RootWindow.grayAuth?.PRODUCT_REGISTER || false
    },
    isShowPaaS() {
      const RootWindow = getRootWindow(window);
      return RootWindow?.grayAuth?.shbPaas || false
    },
    // 结算单灰度
    isShowSettle() {
      const RootWindow = getRootWindow(window)
      return RootWindow.grayAuth?.settle || false
    },
    panelWidth () {
      return `${420 * this.columnNum}px`;
    },
    exportPermission () {
      return true
      // return this.auth.EXPORT_IN;
    },
    hasVIPApprovePermission () {
      return this.auth.VIP_APPROVE === 3;
    },
    // 基础版功能是否隐藏事件
    isBasicEditionHideEvent() {
      return isBasicEditionHideEvent()
    },
    // 基础版功能是否隐藏绩效报告
    isBasicEditionHideOperationsAnalysis() {
      return isBasicEditionHideOperationsAnalysis()
    },
    // 基础版功能是否隐藏知识库
    isBasicEditionHideknowledge() {
      return isBasicEditionHideknowledge()
    },
    newSources() {

      let sources = this.sources.slice();

      if (this.isBasicEditionHideEvent) {
        sources = sources.filter(item => item.name !== i18n.t('common.base.event'))
      }

      if (this.isBasicEditionHideOperationsAnalysis) {
        sources = sources.filter(item => item.name !== i18n.t('common.base.performanceReporting'))
      }

      if (this.isBasicEditionHideknowledge) {
        sources = sources.filter(item => item.name !== i18n.t('notification.module.label7'))
      }

      if (!this.isShowPaaS) {
        sources = sources.filter(item => item.name !== 'PaaS')
      }

      const typeByVersionShowMap = {
        task: this._isShowTaskModule,
        '绩效报告': this._isShowReportPerformance,
        register: this._isShowProductRegister,
        settle: this._isShowServiceProviderSettle,
        event: this._isShowEventModule,
        wiki: this._isShowWikiModule,
      }
      const needJudgeByVersionShowVales = ['task', '绩效报告', 'register', 'settle', 'event', 'wiki']

      sources = sources.filter(item => {

        if (needJudgeByVersionShowVales.includes(item.value)) {
          const isShow = typeByVersionShowMap[item.value]
          return isNotUndefined(isShow) ? isShow : false
        }

        return true

      })

      return sources;
    },
  },
  mounted () {
    // this.recoverConfigByStorage();
    this.buildColumns();

    try { // 联调时 _init字段被声明
      let initData = JSON.parse(window._init || {});
      this.auth = initData.auth || {};
      this.userId = (initData.loginUser || {}).userId;
    } catch (e) {
      this.auth = {};
      this.userId = '';
    }

    let query = parse(window.location.search) || {};

    // 转交时间降序
    if (query.sort == 'handoverTime') {
      this.params.sorts = [{ property: 'handoverTime', direction: 'DESC' }];
    }

    // 根据auth设置不同权限
    this.setPermission();

    this.doSearch();
    let that_ = this;
    // 监听切换后需要重新计算列表高度
    window.addEventListener('message', (event)=> {
      const {action, data} = event.data;
      if (action == 'shb.frame.activatedPage'){
        that_.$nextTick(()=> {
          that_.knowTableContainerHeight();
        })
      }
    });
    this.$nextTick(()=> {
      this.knowTableContainerHeight()
      window.onresize = _.debounce(()=>{
        that_.knowTableContainerHeight()
      }, 500)
    })
    // 注册暴露方法 - es没那么快，需要延迟一点时间才能获取正确的数据
    window.__exports__refresh = async () => {
      setTimeout(() => {
        this.doSearch()
      }, 1000)
    }
    window.__exports__refresh_ui = this.exportsRefreshUI;
  },
  filters: {
    getEventName: value => {
      if (!value) return '';
      return sources.find(source => source.value === value)?.name || value
    },
    getFormatDate (value) {
      if (!value) return '';
      try {
        // YYYY-MM-DD HH:mm:ss 这里方法的格式不同于普通格式化
        return formatDate(safeNewDate(value), 'YYYY-MM-DD HH:mm'); // 这里的format fullYear是YYYY
      } catch (e) {
        return '';
      }
    },
    /**
     * 格式化的审批用时
     */
    getFormatApproveTime (value) {
      if (!value) return '';

      let day = Math.floor(value / (24 * 3600 * 1000));

      let l1 = value % (24 * 3600 * 1000);
      let hours = Math.floor(l1 / (3600 * 1000));

      let l2 = l1 % (3600 * 1000);
      let minute = Math.floor(l2 / (60 * 1000));

      let l3 = l2 % (60 * 1000);
      let second = Math.floor(l3 / 1000);


      day = day ? (`${day}${t('common.time.day')}`) : '';
      hours = hours ? (`${hours}${t('common.time.hour')}`) : '';
      minute = minute ? (`${minute}${t('common.time.minute')}`) : '';
      second = second ? (`${second}${t('common.time.second')}`) : '';

      return day + hours + minute + second;
    }
  },
  components: {
    ApproveExport
  }
}
</script>

<style lang="scss">
  
  html, body {
    height: 100%;
  }

  .level-padding {
    padding: 0 5px;
  }

  .approve-list-view {
    height: 100%;
    overflow: auto;
    padding: 10px;

    .panel-title {
      font-size: 16px;
      line-height: 60px;
      padding: 0 25px;
      color: rgb(132, 138, 147);
      border-bottom: 1px solid rgb(242, 248, 247);
      font-weight: normal;
      display: flex;
      justify-content: space-between;
      .iconfont:hover {
        cursor: pointer;
      }
    }

    // search
    .approve-search-view {
      height: 64px;
      .advanced-search-function, .base-search {
        background: #fff;
        border-radius: 3px;
      }

      .base-search {
        font-size: 14px;
        display: flex;
        justify-content: space-between;
        padding: 16px;
        .search-group {
          display: flex;
          width: 440px;
          justify-content: space-between;
          .el-input {
            width: 300px;
            input {
              height: 34px;
              line-height: 34px;
              width: 300px;
            }
          }
          a {
            line-height: 33px;
          }
        }

        .advanced-search-visible-btn {
          font-size: 14px;
          font-weight: lighter;
          line-height: 32px;
          @include fontColor();
          border-color: $color-primary;
          color: $color-primary;
          background: #fff;
          padding: 0 13px;
          white-space: nowrap;
          i{
            font-size: 14px;
          }
          &:hover {
            cursor: pointer;
          }
        }
      }

      .advanced-search-form {
        overflow: auto;
        // padding: 10px 0 63px 0;
        padding: 10px 15px 63px;
        height: calc(100% - 51px);
        .two-columns {
          display: flex;
          flex-wrap: wrap;
          .el-form-item {
            width: 50%;
          }
        }

        .el-form-item {
          .el-form-item__content,
          .el-select,
          .base-dist-picker,
          .el-cascader,
          .el-date-editor {
            width: 290px;
          }
        }

        .advanced-search-btn-group {
          display: flex;
          justify-content: flex-end;
          width: 100%;
          position: absolute;
          bottom: 0px;
          background: #fff;
          padding: 15px 20px;
          .base-button {
            margin: 0 10px;
          }
        }
      }

      .advanced-search-function {
        margin-top: 10px;
        padding-bottom: 10px;
        h4 {
          border-bottom: 1px solid #f4f4f4;
          padding: 10px;
        }
        .el-row {
          padding: 5px 0;
        }
        .input-label {
          text-align: right;
          line-height: 32px;
          padding-right: 0;
        }
      }
    }
    // end of approve-search-view

    // list-group-view
    .list-group-view {
      padding-top: 10px;
      /*min-height: calc(100% - 100px);*/

      .operation-bar-container {
        background: #fff;
        border-radius: 3px 3px 0 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px;
        border-bottom: 1px solid #f2f2f2;

        .left-btn-group {
          display: flex;
          flex-wrap: nowrap;
          align-items: center;
          .el-form-item, .el-form-item--small, .el-radio-button, .el-radio-button--medium {
            margin-bottom: 0;
          }
        }

        .top-btn-group .base-button {
          margin-right: 5px;
        }

        .action-button-group {
          .base-button {
            margin-left: 5px;
          }
        }
        .el-dropdown-btn {
          padding: 0 15px;
          line-height: 32px;
          display: inline-block;
          background: $color-primary-light-9;
          color: $text-color-primary;
          outline: none;
          margin-left: 5px;
          .iconfont {
            margin-left: 5px;
            font-size: 12px;
          }
          &:hover {
            cursor: pointer;
            color: #fff;
            background: $color-primary;
          }
        }
      }
      .state-btn-group {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
      }

      .el-table-box{
        padding:10px;
        background: #fff;
      }

      .approve-table {
        // padding: 10px;
        &:before {
          height: 0;
        }
        .approve-table-header th {
          background: #F5F5F5;
          color: $text-color-primary;
          font-weight: normal;
        }
        th {
          color: #606266;
          font-size: 14px;
        }
        td {
          color: #909399;
          font-size: 13px;
        }
        .view-detail-btn {
          @include fontColor();
        }
        .select-column .el-checkbox {
          position: relative;
          top: 3px;
        }
        .view-detail-btn {
          @include fontColor();
        }
      }

      .table-footer {
        display: flex;
        justify-content: space-between;
        padding: 0px 10px 10px 10px;
        background: #fff;
        border-radius: 0 0 3px 3px;

        .list-info {
          font-size: 13px;
          line-height: 32px;
          margin: 0;
          color: #767e89;
          .iconfont {
            position: relative;
            top: 1px;
          }
          .approve-selected-count {
            @include fontColor();
            padding: 0 3px;
            width: 15px;
            text-align: center;
            cursor: pointer;
            font-size: 13px;
          }
        }

        .el-pagination__jump {
          margin-left: 0;
        }
      }
    }
    // end of list-gruop-view
  }
  // end of (page)approve-list-view

  // 同page层级 开始
  .approve-panel-btn {
    float: right;
    cursor: pointer;
    font-size: 14px;
    margin-right: 5px;
    &:hover {
      @include fontColor();
    };
  }

  .approve-selected-panel {
    font-size: 14px;
    height: calc(100% - 51px);
    .approve-selected-tip {
      padding-top: 80px;
      img {
        display: block;
        width: 160px;
        margin: 0 auto;
      }
      p {
        text-align: center;
        color: $text-color-regular;
        margin: 8px 0 0 0;
        line-height: 20px;
      }
    }

    .approve-selected-list {
      height: 100%;
      padding: 10px;
      overflow-y: auto;

      .approve-selected-row {
        display: flex;
        flex-flow: row nowrap;
        line-height: 36px;
        border-bottom: 1px solid #ebeef5;
        font-size: 13px;

        &:hover {
          background-color: #f5f7fa;
          .approve-selected-delete {
            visibility: visible;
          }
        }
      }

      .approve-selected-head {
        background-color: #F0F5F5;
        color: #333;
        font-size: 14px;
      }

      .approve-selected-sn {
        padding-left: 10px;
        width: 150px;
        @include text-ellipsis;
      }

      .approve-selected-name {
        padding-left: 10px;
        flex: 1;
        @include text-ellipsis;
      }

      .approve-selected-delete {
        width: 36px;
      }

      .approve-selected-row button.approve-selected-delete {
        padding: 0;
        width: 36px;
        height: 36px;
        border: none;
        background-color: transparent;
        outline: none;
        color: #646B78;
        visibility: hidden;
        i {
          font-size: 14px;
        }
        &:hover {
          color: #e84040;
        }
      }
    }
  }

  .approve-advance-setting .el-dropdown-menu__item {
    width: 80px;
    text-align: center;
  }

  .approve-panel-btn {
    float: right;
    cursor: pointer;
    font-size: 14px;
    margin-right: 5px;
    &:hover {
      @include fontColor();
    }
  }

  // 审批确认弹框
  .apply-approve-modal-content {
    padding: 27px 30px;
    .apply-modal-row {
      display: flex;
      flex-wrap: nowrap;
      .approve-modal-key {
        width: 80px;
        text-align: right;
        margin: 0 20px 16px 0;
      }
      ._textarea {
        flex: 1;
      }
    }
  }
  .apply-modal-footer {
    position: relative;
    display: flex;
    justify-content: flex-end;
    .apply-remark {
      position: absolute;
      left: 10px;
      top: 3px;
    }
  }
  // 同page层级 结束

// 审批转交
.transfer-approve-dialog {
  .base-modal-body {
    padding: 20px;

    .tips {
      margin-bottom: 12px;
      font-size: 12px;
      color: #666;
    }
  }
  .base-modal-footer {
    text-align: right;
  }
}
</style>
<style lang="scss" scoped>
  .initiator-option-row {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    .initiator-avatar {
      width: 27px;
      height: 27px;
      margin-right: 10px;
    }
  }
</style>
