<template>
    <section>
        <div class="board-contain">
            <el-input
                prefix-icon="iconfont icon-Search"
                class="search"
                :placeholder="$t('common.placeholder.inputKeyWordToSearch')"
                v-model="searchVal"
                clearable
                @input="searchInputHandle">
            </el-input>

            <el-checkbox-group 
                v-model="checkIndicatorList"
                class="board-list"
                :max="6"
                @change="checkboxHandle">

                <div 
                    v-for="(item,index) in filterData"
                    :key="`${item.name}${index}`"
                >
                    <template v-if="item.indicatorList && item.indicatorList.length">
                        <h6 @click="showHandle(index)">

                            <b 
                                :class="[
                                    'iconfont','icon-caret-up',
                                    !indicatorIsshowArr[index] ? 'rotate-sty':''
                                ]"
                            ></b>

                            <span>{{item.name}}</span>
                        </h6>

                        <div v-show="indicatorIsshowArr[index]" style="padding-left:27px;">
                            <div
                                v-for="indicator in item.indicatorList"
                                :key="indicator.indType"
                                class="indicator-contain">

                                <el-checkbox 
                                    :label="indicator.indType">
                                    {{indicator.name}}
                                </el-checkbox>

                                <!-- <div class="indicator-name"></div> -->

                                <el-tooltip
                                    v-if="indicator.meaning"
                                    effect="dark" 
                                    :content="indicator.meaning" 
                                    placement="top">
                                    <span class="iconfont icon-jieshishuoming1"></span>
                                </el-tooltip>
                            </div>
                        </div>
                    </template>

                    
                </div>

                <div v-if="filterData && !filterData.length" class="no-data-dom">
                    <div>
                        <img :src="noDataNewPic" alt="">
                        <p>{{ $t('common.base.tip.noData') }}</p>
                    </div>
                </div>
                
            </el-checkbox-group>
        </div>
    </section>
</template>


<script>
// 暂无数据图片
import { getOssUrl } from '@src/util/assets'
const noDataNewPic = getOssUrl('/no_data.png')

import * as HomeApi from "@src/api/HomeApi";

export default {
  props:{
    userId:[String, Number]
  },
    data(){
        return {
            // 搜索框的value
            searchVal:'',

            // 接口获取到的看板数据，记录下来，搜索时需要通过这个字段来过滤
            dataList:[],

            // 过滤的看板数据
            filterData:[],

            // 选中的看板数据
            checkIndicatorList:[],

            // 看板是否展开
            indicatorIsshowArr:[],

            // 暂无数据图片
            noDataNewPic
        }
    },

    mounted(){
        this.initData()
    },

    methods:{
        initData(){
            this.$http
            .post('/api/workbench/outside/indicator/get/list/person', { type: 'statistics' }, false)
            .then(res => {
                const { status, data = [] } = res;

                if(status) return;

                this.dataList = [...data];

                this.filterData = [...data];

                let len = this.dataList.length;
                
                this.indicatorIsshowArr = new Array(len).fill(true);
            })
            .catch(err => {
                console.error('toggleStatus catch err', err);
            });
        },

        setCheckIndicator(val){
            this.checkIndicatorList = val
        },

        // checkbox取消/选中触发父组件该模块的展示
        checkboxHandle(){
            this.updataPersonCard();
            this.$emit('updata',this.checkIndicatorList)
        },

        // 更新卡片
        updataPersonCard(){
            HomeApi.addUserFieldForPersonCard({
                fields:this.checkIndicatorList,
                userId: localStorage.getItem('userId') || ''
            })
        },

        // 展开/隐藏
        showHandle(index){
            let flag = this.indicatorIsshowArr[index];
            this.$set(this.indicatorIsshowArr,index,!flag)
        },

        // 搜索事件
        searchInputHandle(){
            let searchData = [];

            this.dataList.map(item => {
                const { indicatorList = [] } = item;

                let itemData = [];

                itemData = indicatorList.filter(indicator => {
                    const { name } = indicator
                    if(name.includes(this.searchVal)){
                        return indicator
                    }
                })

                if(itemData.length){
                    searchData.push({
                        ...item,
                        indicatorList:itemData
                    })
                }
                
            })

            this.filterData = [ ...searchData ];
            
            const len = this.filterData.length;

            this.indicatorIsshowArr = new Array(len).fill(true);
        }
    }
}
</script>

<style lang="scss" scoped>
.board-contain{
    .search{
        position:relative;
        ::v-deep{
            .el-input__prefix{
                padding-left:10px;
            }
            .el-input__inner{
                padding-left:39px;
            }
        }
    }
    .board-list{
        height:196px;
        overflow-y:auto;
        .no-data-dom{
            height:150px;
            display: flex;
            align-items: center;
            justify-content: center;
            img{
                height:80px;
            }
            p{
                padding-top: 7px;
                text-align: center;
                margin:0;
                font-size: 12px;
                color:#595959;
            }
        }
    }
    h6{
        height: 20px;
        font-size: 14px;
        font-weight: 600;
        color: #262626;
        line-height: 20px;
        margin:10px 0 8px;
        display: flex;
        align-items: center;
        cursor: pointer;
        b{
            transition: transform .3s ease;
            margin-right: 12px;
        }
        &:hover{
            b{
                color:$color-primary-light-6;
            }
        }
        .rotate-sty{
            transform:rotate(180deg)
        }
    }
    ::v-deep{
        .indicator-contain{
            display: flex;
            align-items: center;
            padding:6px 0;

            .el-checkbox__inner{
                
            }
            
            .indicator-name{
                height: 20px;
                font-size: 14px;
                font-weight: 400;
                color: #595959;
                line-height: 20px;
                margin-left:12px;
            }
            .icon-jieshishuoming1{
                font-size: 12px;
                margin-left: 4px;
                color: #8c8c8c;
                cursor: pointer;
                &:hover{
                    color:$color-primary-light-6;
                }
            }
        }
    }
}
</style>