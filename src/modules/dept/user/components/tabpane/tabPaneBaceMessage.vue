<template>
    <section>
        <template v-if="_isShowUserDashBoard">
        <HeaderTitle 
            :title="$t('personal.tablePenal.baseMessage.title')">
            <template slot="right">

                <el-popover
                    placement="bottom"
                    width="273"
                    trigger="click"
                    v-model="showBoard"
                    :append-to-body="false">

                    <CustomBoard 
                        @updata="updataBoard" 
                        ref="customBoardRef"
                        :userId="userId"
                    />

                    <span slot="reference">
                        {{ $t('personal.tablePenal.baseMessage.customDashboard') }}( {{workData.length}}/6 )
                        <i :class="['iconfont','icon-fdn-select',showBoard ? 'rotate-sty' :'']"></i>
                    </span>
                </el-popover>
            </template>
        </HeaderTitle>

        <div class="work-content" v-if="workData.length">
            <template v-for="(item,index) of workData">

                <!-- 最多展示6个 -->
                <Card 
                    v-if="index < 6"
                    :key="index"
                    :content="item" 
                    :temp="indicatorTempMap[item.indType]"
                />
                
            </template>
        </div>
        <div v-else class="tips">{{$t('department.user.definedBoard')}}</div>
        </template>

        <HeaderTitle :title="$t('common.base.dynamicInfo')" :style="{marginTop:workData.length ? '24px' :'0'}"></HeaderTitle>



        <dept-records 
            :detail-id="userId" 
            :display-name="user.displayName" 
            :is-from-user-view="true" 
            :is-show-track="showButton.isShowTrack"
            :showExportBtn="true">
        </dept-records>
        <!-- <v-slot></v-slot> -->
    </section>
</template>

<script>
// 标题组件
import HeaderTitle from '../HeaderTitle.vue';
// 自定义看板
import CustomBoard from '../CustomBoard.vue';

// 卡片
import Card from '../Card.vue';

// 看板本地模版数据
import { indicatorTempMap } from '../../modal/statistics';

import DeptRecords from '../../../deptHome/components/DeptRecords.vue';

import * as HomeApi from "@src/api/HomeApi";
/* mixin */
import { VersionControlUserMixin } from '@src/mixins/versionControlMixin';

export default {
    mixins: [VersionControlUserMixin],
    components: {
        HeaderTitle,
        CustomBoard,
        Card,
        DeptRecords
    },

    data(){
        return {
            // 本地模版-看版
            indicatorTempMap,

            // 看板内容
            workData:[],

            // 所有看板数据
            allData:[],

            // 是否展开自定义看板
            showBoard:false,

            loginUserId:localStorage.getItem('userId')
            
        }
    },

    props:{
        user:{
            type:Object,
            required:true
        },
        showButton:{
            type:Object,
        },
        userId:[String,Number]
    },

    async mounted(){
        const params = {userId:this.loginUserId}
        Promise.all([
            await HomeApi.getUserFieldForPersonCard(params),
            await HomeApi.fill({ userId:this.userId })
        ]).then(([resultA,resultB]) => {

            if(resultB && !resultB.status){
                this.allData = resultB.data.indicatorMap
            }

            if(resultA && !resultA.status){
                this.$refs.customBoardRef && this.$refs.customBoardRef.setCheckIndicator(resultA.data)
                this.updataBoard(resultA.data || [])
            }
        })
        
    },

    methods:{

        // 展示工作统计更新后的内容
        updataBoard(checkIndicatorList){
            let workData = [];

            checkIndicatorList.map(key => {
                workData.push(this.allData[key])
            })

            this.workData = [ ...workData ]
        }
    }
}
</script>

<style lang="scss" scoped>
::v-deep{
    .el-popover__reference{
        display: flex;
        align-items: center;
        cursor: pointer;
        .iconfont{
            margin-left:8px;
            transition: transform .3s ease;
        }
        .rotate-sty{
            transform:rotate(180deg)
        }
    }
    .progress-content{
        padding:5px 0 0 10px;
        .base-timeline{
            padding-left:7px;
        }
    }
}
.work-content{
    
    display: flex;
    flex-wrap:wrap;

    :last-child{
        &::after{
            width:0;
        }
    }
    :nth-child(4){
        &::after{
            width:0;
        }
    }
}
.tips{
    height: 22px;
    font-size: 14px;
    font-weight: 400;
    color: #BFBFBF;
    line-height: 22px;
    margin:0 0 24px 10px;
}
</style>
