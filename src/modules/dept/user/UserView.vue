<template>
  <section class="user-center" v-loading="loading">
    <!-- 头部 -->
    <UserHeader
      ref="UserHeader"
      :userId="userId"
      :user="user"
      :userInfo="userInfo" 
      @getUserInfo="getUserInfo" 
      :showButton="showButton">
    </UserHeader>

    <div class="user-view">
      <UserLeft
        :class="['user-view-left', isHaveSmartAgent ? 'user-view-left-cont' : '']"
        :user="user"
        :userId="userId"
        :userInfo="userInfo"
        @update="getUserInfo">
      </UserLeft>
 
      <div :class="['user-view-right', isHaveSmartAgent ? 'user-view-right-cont' : '']">
        <BaseBarV3
          class="user-view-tab__list"
          :bar-list="tabData"
          :nowItem="currentTabItem"
          @changeItem="handleChangeTab"
          @upDateBarList="tabBarUpdateList"
        />
        <div class="user-view-right__content">
             <component
                :is="currentComponent.componentsName"
                :user="user"
                :userId="userId"
                :showButton="showButton"
                :class="currentComponent.name"
                :style="{padding: '17px'}"
                :is-me="isMe"
                mode="USER"
              >
        </component>
        </div> 
      </div>
     
    </div>
  </section>
</template>

<script>
import {TenantType} from '@src/util/platform'
import platform from '@src/platform';
import BaseBarV3 from '@src/component/common/BaseTabBar/BaseTabBarV3.vue'
// tab展示数据
import tabData from './modal/tabData';

// 页面头部内容
import UserHeader from './components/UserHeader';
// 页面左边内容
import UserLeft from './components/UserLeft';

// tab-pane组件
// 基本信息
import tabPaneBaceMessage from './components/tabpane/tabPaneBaceMessage';
// 资质管理
import tabQualification from './components/tabpane/tabQualification';

// 会话
import tabPaneSession from './components/tabpane/tabPaneSession';
// 通话
import tabPaneCall from './components/tabpane/tabPaneCall';
// 事件
import tabPaneEvent from './components/tabpane/tabPaneEvent';
// 工单
import tabPaneWorkOrder from './components/tabpane/tabPaneWorkOrder';
// 客户
import tabPaneCustomer from './components/tabpane/tabPaneCustomer';
// 个人备件库
import tabPaneSpareParts from './components/tabpane/tabPaneSpareParts';
// 日程
import tabPaneSchedule from './components/tabpane/tabPaneSchedule';
    
import { getRootWindow } from '@src/util/dom';

/* enum */
import { TaskEventNameMappingEnum } from '@model/enum/EventNameMappingEnum.ts';
import StorageKeyEnum from '@model/enum/StorageKeyEnum'
import { BaseTabBarUsualEnum, StorageHttpParamsForTerminalType, StorageHttpParamsForModuleType } from '@src/component/common/BaseTabBar/enum'
/* mixin */
import { 
  VersionControlCalendarMixin,
  VersionControlTaskMixin,
  VersionControlSparePartMixin
} from '@src/mixins/versionControlMixin'
import { isNotUndefined } from '@src/util/type';
import { computedTabList } from '@src/util/tabBarUtils'

import { getStorageForDetailTabbar, setStorageForDetailTabbar } from '@src/api/SystemApi'

let win = getRootWindow(window);
let origin = window.location.origin;
export default {
  mixins: [
    VersionControlCalendarMixin,
    VersionControlTaskMixin,
    VersionControlSparePartMixin
  ],
  computed: {
    // 用户userId
    userId() {
      const pathname = window.location.pathname;
      if(pathname.indexOf('/mine/') > -1) return pathname.split('/mine/')[1];
      return pathname.split('/security/user/view/')[1];
    },
    isMe(){
      return this.loginUserId === this.userId
    },
    // 是否开启了智能派单灰度
    isHaveSmartAgent() {
      const RootWindow = getRootWindow(window)
      return RootWindow.grayAuth?.SMART_DISPATCH ?? false;
    },
    currentComponent() {
      return this.tabData.find(item=> item.name === this.currentTabItem)
    }
  },
	watch: {
		activeTab(val) {
			const current = this.tabData.filter(item => item.name === val);
			this.$track.clickStat(this.$track.formatParams('USER_DETAIL_TABS', current[0].label))
		}
	},
  // provide: {
  //   // 为了控制台不报错
  //   initData: Object.freeze({})
  // },
   provide(){
    return{
      initData:{
        tenantType:TenantType
      }
    }
  },
  data() {
    return {
      loading:false,

      // 当前选中的tab名
      activeTab: 'baseMessage',
      // 接口返回的所有数据
      userInfo: {},
      // 和用户相关的数据，取自userInfo.user
      user: {},

      // 按钮权限
      showButton: {},


      // 获取登陆的userId，匹配与当前页面的用户是否是同一个人
      loginUserId:localStorage.getItem('userId'),
      currentTabItem: 'baseMessage',
      tabData: this.getTabData()
    }
  },

  components: {
    UserHeader,
    UserLeft,
    tabPaneBaceMessage,
    tabQualification,
    tabPaneSession,
    tabPaneCall,
    tabPaneEvent,
    tabPaneWorkOrder,
    tabPaneCustomer,
    tabPaneSpareParts,
    tabPaneSchedule,
    BaseBarV3
  
  },

  mounted () {

    this.getUserInfo('mounted').then(()=>{
      this.initShowModifyChangePassWordDialog()
    });
    
    const frameElement = window.frameElement
    if (frameElement) {
      const currentTabId = frameElement.dataset.id;
      // TODO 国际化 标题是否需要修改
      // platform.setTabTitle({
      //   id: currentTabId,
      //   title:'用户详情'
      // })
    }

    this.getTabBarFromCache()
  },

  methods: {
    async getTabBarFromCache() {
        let { TabBarListItemKey:tabName, TabBarListItemShow:tabShow} = BaseTabBarUsualEnum;
        let params = {
          equipment:StorageHttpParamsForTerminalType.PC,
          bizType:StorageHttpParamsForModuleType.User,
          bizTypeId: `user_${this.loginUserId}`,
        }
        // 获取tabbar用户行为缓存/*  */
        let { status, data = [], message = '' } = await getStorageForDetailTabbar(params);
        if(status !== 0) {
          throw message
        }
        let storageList = data.map(item=>{
          const { cardId, checked} = item;
          return {
            [tabName]:cardId,
            [tabShow]:checked
          }
        })
        this.tabData = computedTabList(this.tabData, storageList)
    },
    tabBarUpdateList(list) {
      const { TabBarCardInfoType, TabBarListItemKey:tabName, TabBarListItemShow:tabShow } = BaseTabBarUsualEnum;
      let list_ = list.map(item=>{
        return {
          cardId: item.type == TabBarCardInfoType ? item.id : item[tabName],
          checked: item[tabShow]
        }
      })
      let params = {
        equipment:StorageHttpParamsForTerminalType.PC,
        bizType:StorageHttpParamsForModuleType.User,
        bizTypeId: `user_${this.loginUserId}`,
        cardList:list_
      }
      // 存储右栏tabbar
      setStorageForDetailTabbar(params)
    },
    getTabData() {
      return tabData.map(item=> {
        return {
          ...item,
          tabShow: true,
          tabLabel: item.label,
          tabName: item.name,
          disabled: item.name === 'baseMessage'
        }
      })
    },
    handleChangeTab(item) {
      this.currentTabItem = item.tabName
    },
    async getUserInfo(type){

      this.loading = true;

      try{ 
        const { status, message, data } = await this.$http.get(`/security/user/getDetail/${this.userId}`);
        this.loading = false;
        
        if(status != 0) return;

        this.userInfo = data || {};
        
        let { certificateList = []} = data.user

        // 解决下载时组件取的是id字段，但是接口返回的id不是fileId的问题
        certificateList.map(item=>{
          const { id, fileId } = item;
          fileId  && (item.id = fileId);
        })

        this.user = { ...data.user, certificateList, userFaceState:data?.userFaceState || false};
       
        
        this.showButton = data.showButton || {}; 

        this.loading = false;

        // 是否可以查看会话       isShowConversation
        // 是否可以查看通话       isShowCell
        // 是否可以查看事件      isShowCaseView
        // 是否可以查看工单      isShowTask
        // 是否可以查看客户      isShowCustomer
        // 是否显示个人备件库    isShowSparepart
        // 是否可以查看日程      isShowCalendar
        // 设置tab权限
        this.tabData.map((item,index) =>{
          if(item.permissionKey){
            let isShow = this.showButton[item.permissionKey] || false
            // 纯客服云版本隐藏工单、个人备件库、日程
            const judgePermissionArr = ['isShowTask', 'isShowSparepart', 'isShowCalendar']
            
            const keyByVersionShowMap = {
              isShowTask: this._isShowTaskModule,
              isShowSparepart: this._isShowSparePartPerson,
              isShowCalendar: this._isShowCalendarModule
            }
            
            if (judgePermissionArr.includes(item.permissionKey)) {
              const show = keyByVersionShowMap[item.permissionKey]
              isShow = isNotUndefined(show) ? show : isShow
            }
            
            item.isShow = isShow
          }
        })
        // 当前登陆的用户与当前页面是同一个人，需要更新右上角的用户信息
        if(this.loginUserId === this.userId){
            win.postMessage({ displayName:this.user.displayName }, origin);
        }

        // 更新日志,子组件触发
        type !== 'mounted' && (this.$eventBus.$emit(TaskEventNameMappingEnum.UpdateRecord));
        
      } catch(error){ 
        this.loading = false;
        console.log(error)
      } 
    },
    initShowModifyChangePassWordDialog() {
      try {
        
        // 是否显示修改密码弹窗
        const isShowModifyChangePassWordDialog = sessionStorage.getItem(StorageKeyEnum.PersonalCenterModifyPassword);
        if (!isShowModifyChangePassWordDialog) {
          return
        }
        
        // 清除 sessionStorage, 下次不再弹窗
        sessionStorage.removeItem(StorageKeyEnum.PersonalCenterModifyPassword);
        
        // 打开修改密码弹窗
        this.$refs.UserHeader.updatePwdBtnHandle()
        
      } catch (error) {
        console.error(error)
      }
    }
  },
}
</script>

<style lang="scss" scoped>
@import './common.scss';
section.user-center{
  padding:16px;
  width:100%;
  min-width:1200px;
  height:100%;
  position: relative;
  ::v-deep .el-dialog__headerbtn{
    top: 0;
  }
}

.user-view {
  display: flex;
  position: absolute;
  bottom: 16px;
  top: 116px;
  right: 16px;
  left: 16px;

  .user-view-left {
    width:400px;
    min-width:400px;
    background:#fff;
    border-radius: 4px;
    height:100%;
    overflow-y: hidden;
    .user-info {
      background: #fff;
      img {
        width: 60px;
        height: 60px;
        border-radius: 50%;
      }
      .info {
        display: flex;
        padding: 12px 12px 0 12px;
        .username {
          flex: 1;
          margin-left: 8px;
        }
        button {
          width: 80px;
          height: 36px;
          padding: 8px 10px;
          border-radius: 4px;
        }
      }
    }

  }
  .user-view-right {
    min-width:800px;
    height: 100%;
    overflow-y: auto; 
    background: #fff;
    margin-left: 12px;
    border-radius: 4px;
    flex:1;
    display: flex;
    flex-direction: column;
    .el-tab-pane{
      height:100%;
    }
  }

  .user-view-left-cont {
    width:480px !important;
    min-width:480px !important;
  }

  .user-view-right-cont {
    min-width:720px !important;
  }
  .user-view-tab__list{
    background: #fafafa;
    ::v-deep .bbx-base-tab-list{
      min-height: 48px;
      .bbx-base-tab-list-item{
        margin: 0;
        padding: 0 24px;
        height:48px;
        line-height: 48px;
      }
      .is-select{
        &::after{
          width: calc(100% - 48px);
          left: 50%;
          transform: translateX(-50%);
        }
      }
    }
  }
}
.user-view-right__content{
    flex: 1;
    overflow-y: auto;
  // padding: 17px;
}
</style>