/* components */
import InitiateSettlementDialog from '@src/modules/settlement/pendingSettlementPool/components/InitiateSettlementDialog.vue';
import GenerateDetailsDialog from '@src/modules/settlement/pendingSettlementPool/components/GenerateDetailsDialog.vue';
import ApplyAdjustmentDialog from '@src/modules/settlement/pendingSettlementPool/components/ApplyAdjustmentDialog.vue';
import BaseSearchDrawer from 'packages/BaseSearchDrawer';
import BaseSearchPanel from 'packages/BaseSearchPanel';
import ConditionsDialog from '@src/modules/smartSettlement/standardSetting/list/components/settlementStandard/components/conditionsDialog';
/* utils */
import { openAccurateTab } from '@src/util/platform';
import { PageRoutesTypeEnum } from 'pub-bbx-global/pageType/dist/enum/PageRoutesEnum';
import { formatDate } from 'pub-bbx-utils';
import { storageGet, storageSet } from "@src/util/storage.ts";
import _ from 'lodash';
import { getRootWindowInitData } from '@src/util/window';
import AuthUtil from '@src/util/auth';
/* models */
import { getDetailAdvancedFields, getDetailColumnFields, getBizAdvancedFields, getBizColumnFields, getBizSubColumnFields, SettlementStatus } from '@src/modules/settlement/pendingSettlementPool/model/fields.js';
/* api */
import * as settlementApi from '@src/api/Settle';
import { getSettlementStandardList } from '@src/modules/smartSettlement/api/index.js'

export default {
    name: 'SettlePoolList',
    data() {
        return {
            searchParams: {
                keywords: '',
                pageSize: 30,
                pageNum: 1,
                totalCount: 0,
            },
            visible: false, // 高级筛选弹窗
            columnNum: 1, // 高级筛选弹窗的列数
            packUp: true, // 收起/展开
            loading: false,
            settlementFeeItemList: [], // 结算项
            statusStatisticsList: [], // 结算状态
            moduleList: [], // 结算模块
            selectedSettlementItem: null, // 选中的结算项目
            selectedSettlementStatus: null, // 选中的结算状态
            module: null, // 结算模块
            displayMode: '1', // 显示模式 1：按结算项视图 2：按业务单据视图
            columns: [], // 列表字段
            tableData: [], // 列表数据
            selectedList: [], // 选中的列表
            selectedBizList: [], // 选中的列表，业务单据视图下，是个二维数组，因为是子表格
            isNeedApprove: false, // 是否需要审批
            standardList: [], // 结算标准列表
            allowedAdjustmentList: [], // 允许调整的结算项
            scrollLeft: 0,
        }
    },
    computed: {
        SettlementStatus() {
            return SettlementStatus;
        },
        formatDate() {
            return formatDate;
        },
        // 高级搜索字段
        getDetailAdvancedFields() {
            return this.displayMode == '1'? getDetailAdvancedFields(this) : getBizAdvancedFields(this);
        },
        // 业务单据列表子字段
        getBizSubColumnFields() {
            return getBizSubColumnFields(this);
        },
        // 获取模块名称
        getModuleName() {
            switch (this.module) {
                case 'task':
                    return this.$t('common.base.systemKeyword.task');
                case 'event':
                    return this.$t('common.base.systemKeyword.event');
                case 'paas':
                    return this.$t('trigger.passApplication');
                case -1:
                    return '';
                default:
                    return this.$t('common.smartSettlement.allModules');
            }
        },
        //选择列的key
        selectColumnKey() {
            return this.displayMode == '1' ? 'settle_list_detail' : 'settle_list_biz';
        },
        auth() {
            const rootWindowInitData = getRootWindowInitData()
            return rootWindowInitData?.user?.auth || {}
        },
        // 调整结算金额权限
        hasAdjustAuth() {
            return AuthUtil.hasAuth(this.auth, 'INTELLIGENT_SETTLE_ADJUST')
        },
        // 取消结算权限
        hasCancelAuth() {
            return AuthUtil.hasAuth(this.auth, 'INTELLIGENT_SETTLE_FLOW_CANCEL')
        },
        // 发起结算权限
        hasSettleAuth() {
            return AuthUtil.hasAuth(this.auth, 'INTELLIGENT_SETTLE_FLOW_START')
        },
    },
    watch: {
        displayMode: {
            handler(val) {
                this.buildColumns();
            },
        }
    },
    mounted() {
        // 获取配置信息
        this.getSettleConfig();
        // 获取列表字段
        this.buildColumns();
        // 获取列表信息
        this.search();
        // 获取结算标准列表
        this.getStandardList();
    },
    methods: {
        handleScroll(event) {
            this.scrollLeft = event.target.scrollLeft;
        },
        // 根据列表数据返回结算模块名称
        getModuleText(row) {
            if (row.module == 'paas') {
                return row.moduleTemplateName || this.$t('trigger.passApplication');
            } else if (row.module == 'task') {
                return this.$t('common.base.systemKeyword.task');
            } else if (row.module == 'event') {
                return this.$t('common.base.systemKeyword.event');
            }
        },
        buildColumns() {
            try {
                setTimeout(async() => {
                    let localColumns = await this.getLocalColumns();
                    if (localColumns && Object.keys(localColumns).length > 0) {
                        this.columns = this.buildSortFields(this.displayMode == '1'? getDetailColumnFields(this) : getBizColumnFields(this), localColumns);
                    } else if (this.displayMode == '1') {
                        this.columns = getDetailColumnFields(this);
                    } else {
                        this.columns = getBizColumnFields(this);
                    }
                });
            } catch (error) {
                console.error(error);
            }
        },
        getSettleConfig() {
            settlementApi.getSettleConfigList({moduleCode: 'intelligentSettle'}).then(res => {
                if (res.succ) {
                    res.data.forEach(item => {
                        if (item.configCode === 'changeApproval') {
                            this.isNeedApprove = item?.isOpen || false;
                        } else if (item.configCode === 'allowedAdjustment') {
                            this.allowedAdjustmentList = item?.isOpen ? JSON.parse(item?.configStrValue || '[]') : [];
                        }
                    });
                } else {
                    this.$message.error(res.message);
                }
            }).catch(err => {
                console.error(err);
            })
        },
        // 获取结算池统计信息
        async getStatistics() {
            try {
                const res = await settlementApi.getStatistics();
                this.settlementFeeItemList = res.data?.settlementFeeItemList || [];
                this.statusStatisticsList = res.data?.statusStatisticsList || [];
                this.moduleList = [
                  { id: '', name: this.$t('common.smartSettlement.allModules') },
                  { id: 'task', name: this.$t('common.base.systemKeyword.task') },
                  { id: 'event', name: this.$t('common.base.systemKeyword.event') },
                ].concat(res.data?.paasTemplateList?.map(i => {
                  return {
                    id: i.templateId,
                    name: i.templateName,
                  };
                }) || []);
                console.log(this.moduleList);
            } catch (error) {
                console.error(error);
            }
        },
        // 重置搜索参数
        resetParams() {
            this.visible = false;
            this.searchParams.keywords = '';
            this.searchParams.pageSize = 30;
            this.searchParams.pageNum = 1;
            this.searchParams.totalCount = 0;
            this.selectedSettlementItem = null;
            this.selectedSettlementStatus = null;
            this.module = null;
            this.selectedList = [];
            this.selectedBizList = [];
            if (this.$refs.searchPanel) {
                this.$nextTick(() => {
                    this.$refs.searchPanel.initFormVal();
                    this.search();
                });
            } else {
                this.search();
            }
        },
        // 收起/展开
        changePackUp() {
            this.packUp = !this.packUp;
        },
        // 查询列表数据
        search(flg) {
            // 更新结算池统计信息
            this.getStatistics();
            // 按结算项视图/按业务单据视图
            if (this.displayMode === '1') {
                this.getPageListByDetailView(flg);
            } else {
                this.getPageListByBizView(flg);
            }
        },
        // 获取结算池列表：按结算项视图
        async getPageListByDetailView(flg) {
            try {
                this.loading = true;
                const params = this.buildSearchParams(flg);
                const res = await settlementApi.getPageListByDetailView(params);
                this.tableData = res.data?.list || [];
                this.searchParams.totalCount = res.data?.total || 0;
                this.toggleSelection(this.selectedList);
            } catch (error) {
                console.error(error);
            } finally {
                this.loading = false;
            }
        },
        buildSearchParams(flg) {
            let advancedSearchParams = this.$refs.searchPanel ? this.$refs.searchPanel.buildParams() : {};
        
            // 简单字段映射规则
            const keyMapping = {
                module: 'moduleList',
                settlementItemName: 'settlementItemIdList',
                settlementStatus: 'settlementStatusList',
                moduleTemplateName: 'moduleTemplateId',
                standardNo: 'standardIdList',
                settlementSponsorName: 'settlementSponsorIdList',
                createName: 'createId',
            };
        
            // 转换简单字段
            Object.entries(keyMapping).forEach(([originalKey, newKey]) => {
                if (advancedSearchParams?.[originalKey]) {
                    advancedSearchParams[newKey] = advancedSearchParams[originalKey];
                    delete advancedSearchParams[originalKey];
                }
            });
        
            // 转换数组字段
            if (advancedSearchParams?.tags?.length) {
                advancedSearchParams.belongTagId = advancedSearchParams.tags[0]?.id;
                delete advancedSearchParams.tags;
            }

            // 结算对象转换
            if (advancedSearchParams?.settlementObjectName) {
                advancedSearchParams.settlementObjectIdList = advancedSearchParams.settlementObjectName?.settleObjectIdList?.length ? advancedSearchParams.settlementObjectName.settleObjectIdList.map(i => i.id || i.userId) : [];
                advancedSearchParams.settlementObjectType = advancedSearchParams.settlementObjectName.settleObjectType;
                delete advancedSearchParams.settlementObjectName;
            }
        
            // 转换日期范围字段
            const dateRangeMapping = {
                createTime: ['startTime', 'endTime'],
                settlementSponsorTime: ['sponsorSettlementStartTime', 'sponsorSettlementEndTime'],
                settlementFinishTime: ['finishSettlementTimeStart', 'finishSettlementTimeEnd'],
                adjustAmount: ['adjustAmountMin', 'adjustAmountMax'],
            };
        
            Object.entries(dateRangeMapping).forEach(([originalPrefix, [startKey, endKey]]) => {
                const startField = `${originalPrefix}Start`;
                const endField = `${originalPrefix}End`;
                if (advancedSearchParams?.[startField] && advancedSearchParams?.[endField]) {
                    advancedSearchParams[startKey] = advancedSearchParams[startField];
                    advancedSearchParams[endKey] = advancedSearchParams[endField];
                    delete advancedSearchParams[startField];
                    delete advancedSearchParams[endField];
                }
            });
        
            // 设置分页参数
            if (!flg) {
                this.searchParams.pageNum = 1;
            }
        
            // 返回最终的参数对象
            return {
                settlementItemIdList: this.selectedSettlementItem ? [this.selectedSettlementItem] : [],
                settlementStatusList: (this.selectedSettlementStatus !== null && this.selectedSettlementStatus !== -1)
                    ? [this.selectedSettlementStatus] : [],
                moduleList: this.module ? [this.module] : [],
                ...advancedSearchParams,
                ...this.searchParams,
            };
        },        
        // 获取结算池列表：按业务单据视图
        async getPageListByBizView(flg) {
            try {
                this.loading = true;
                const params = this.buildSearchParams(flg);
                const res = await settlementApi.getPageListByBizView(params);
                this.tableData = res.data?.list || [];
                this.tableData.forEach(item => {
                    item.hasChildren = item.settlementDetailsList && item.settlementDetailsList.length > 0;
                });  
                this.searchParams.totalCount = res.data?.total || 0;
                this.toggleSelection(this.selectedBizList);
            } catch (error) {
                console.error(error);
            } finally {
                this.loading = false;
                this.$nextTick(() => {
                    this.$refs.table.bodyWrapper.removeEventListener('scroll', this.handleScroll);
                    this.$refs.table.bodyWrapper.addEventListener('scroll', this.handleScroll);
                });
            }
        },
        // 表格项选中处理
        handleSelectionChange(val, index) {
            if (this.displayMode === '1') {
                if (val && index) {
                    // 单行选择处理
                    const id = index?.id;
                    const existingIndex = this.selectedList.findIndex(item => item.id === id);
                    
                    if (existingIndex === -1) {
                        this.selectedList.push(index);
                    } else {
                        this.selectedList.splice(existingIndex, 1);
                    }
                } else if (!index) {
                    // 全选/取消全选处理
                    const selectedIds = new Set(this.selectedList.map(item => item.id));
                    
                    if (val.length) {
                        // 全选：添加未选中的项
                        this.tableData.forEach(item => {
                            if (!selectedIds.has(item.id)) {
                                this.selectedList.push(item);
                            }
                        });
                    } else {
                        // 取消全选：移除当前页的选中项
                        this.selectedList = this.selectedList.filter(item => 
                            !this.tableData.some(row => row.id === item.id)
                        );
                    }
                }
            } else {
                // 业务单据视图处理
                this.selectedBizList[index] = val;
                this.selectedList = this.selectedBizList.flat();
            }
        },
        // 选择模块
        changeModule(val) {
            this.$refs.searchPanel && this.$refs.searchPanel.updateForm({
                key: 'module',
                value: null,
            })
            this.module = val;
            this.search();
        },
        // 修改顶部结算项目
        changeSettlementItem(id) {
            this.$refs.searchPanel && this.$refs.searchPanel.updateForm({
                key: 'settlementItemName',
                value: null,
            })
            this.selectedSettlementItem = id;
            this.search();
        },
        // 修改顶部结算状态
        changeSettlementStatus(id) {
            this.$refs.searchPanel && this.$refs.searchPanel.updateForm({
                key: 'settlementStatus',
                value: null,
            })
            this.selectedSettlementStatus = id;
            this.search();
        },
        // 修改显示模式 按结算项视图/按业务单据视图
        changeDisplayMode(id) {
            this.displayMode = id;
            this.selectedList = [];
            this.selectedBizList = [];
            this.tableData = [];
            this.resetParams();
        },
        // 高级筛选搜索
        handleAdvancedSearch(value) {
            this.visible = false;
            let advancedSearchParams = this.$refs.searchPanel ? this.$refs.searchPanel.buildParams() : {};
            if (advancedSearchParams?.module && advancedSearchParams?.module.length) {
                this.module = -1;
            }
            if (advancedSearchParams?.settlementStatus && advancedSearchParams?.settlementStatus.length) {
                this.selectedSettlementStatus = -1;
            }
            if (advancedSearchParams?.settlementItemName && advancedSearchParams?.settlementItemName.length) {
                this.selectedSettlementItem = -1;
            }
            this.search();
        },
        // 分页跳转到指定页
        jump(val) {
            this.searchParams.pageNum = val;
            this.search(true);
        },
        // 每页显示条数改变
        handleSizeChange(val) {
            this.searchParams.pageSize = val;
            this.search();
        },
        // 发起结算
        openInitiateSettlementDialog() {
            if (this.selectedList.length === 0) {
                this.$refs.InitiateSettlementDialogRef.open();
            } else {
                this.$refs.InitiateSettlementDialogRef.openNext(this.selectedList.map(i => i.id));
            }
        },
        // 高级筛选弹窗的列数
        setAdvanceSearchColumn(num) {
            this.columnNum = num;
        },
        // 高级筛选弹窗的显示隐藏
        panelSearchAdvancedToggle() {
            this.visible = !this.visible;
        },
        // 取消结算
        cancelSettlement(idList) {
            if (idList.length === 0) {
                this.$message.warning(this.$t('common.smartSettlement.placeholder2'));
                return;
            }
            this.$confirm(this.$t('common.smartSettlement.placeholder3'), this.$t('common.base.deleteTip'), {
                confirmButtonText: this.$t('common.base.makeSure'),
                cancelButtonText: this.$t('common.base.cancel'),
                type: 'warning',
            }).then(() => {
                settlementApi.cancelSettlement(idList).then(res => {
                    if (res.succ) {
                        this.$message.success(this.$t('common.base.deleteSuccess'));
                        this.search();
                    } else {
                        this.$message.error(res.message);
                    }
                })
            })
        },
        // 跳转到详情页
        goDetail(row, flg) {
            const fromId = window.frameElement.getAttribute('id');
            if (this.displayMode == '1' || flg) {
                this.$platform.openTab({
                    id: `settlement_pendingSettlementPool_detail_${fromId}`,
                    title: `待结算池详情`,
                    reload: true,
                    close: true,
                    url: `/shb/home/<USER>/settlement/pendingSettlementPool/detail?id=${row?.id}`,
                    fromId
                })
            } else {
                this.$platform.openTab({
                    id: `settlement_pendingSettlementPool_detail_${fromId}`,
                    title: `待结算池详情`,
                    reload: true,
                    close: true,
                    url: `/shb/home/<USER>/settlement/pendingSettlementPool/detail?module=${row?.module}&moduleSourceId=${row?.moduleSourceId}`,
                    fromId
                })
            }
        },
        getSettleStatus(i) {
            return SettlementStatus.find(item => item.id === i)?.name;
        },
        getSettleStatusBgc(i) {
            switch (i) {
                case 0: // 审批中
                    return '#e6a23c'; // 橙色
                case 1: // 待结算
                    return '#F56C6C'; // 红色
                case 2: // 结算审批中
                    return '#e6a23c'; // 橙色
                case 3: // 已结算
                    return '#67c23a'; // 绿色
                case 4: // 变更审批中
                    return '#e6a23c'; // 橙色
                case 5: // 取消结算
                    return '#909399'; // 灰色
                default:
                    return '';
            }
        },
        // 申请调整结算金额
        applyAdjustment(row, flg) {
            if (!row.some(i => i.settlementStatus === 1) || !row.some(i => this.isAllowedAdjustment(i))) {
                this.$message.warning(this.$t('common.smartSettlement.placeholder4'));
                return;
            }
            row = row.filter(i => this.isAllowedAdjustment(i));
            this.$refs.ApplyAdjustmentDialogRef.open(row, flg)
                .then(() => {
                    this.selectedList = [];
                    this.selectedBizList = [];
                    this.search();
                })
                .catch(err => {
                    console.error(err);
                });
        },        
        // 打开生成结算明细
        openGenerateDetailsDialog() {
            this.$refs.GenerateDetailsDialogRef.open();
        },
        // 拼接部门名称
        getTagtName(item) {
            return (item || []).map(i => i.belongTagName).join(',');
        },
        // 根据来源打开来源详情页
        openSourceDetail(row) {
            if (row.module === 'task') {
                this.openTaskTab(row.moduleSourceId, row.moduleSourceNo);
            } else if (row.module === 'event') {
                this.openEventTab(row.moduleSourceId, row.moduleSourceNo);
            } else if (row.module === 'paas') {
                this.openPaaSAppTab(row.moduleSourceId, row.bizNo);
            }
        },
        // 打开工单详情Tab
        openTaskTab(taskId, taskNo) {
            if (!taskId) return;
            
            let fromId = window.frameElement.getAttribute('id');
            openAccurateTab({
              type:PageRoutesTypeEnum.PageTaskView,
              key:taskId,
              titleKey:taskNo,
              params: `noHistory=1`,
              fromId
            })
        },
        // 打开事件详情Tab
        openEventTab(eventId, eventNo) {
            if (!eventId) return;

            let fromId = window.frameElement.getAttribute('id');
            openAccurateTab({
              type:PageRoutesTypeEnum.PageEventView,
              key:eventId,
              titleKey:eventNo,
              fromId
            })
        },
        // 打开PaaS应用详情Tab
        openPaaSAppTab(formContentId, bizNo) {
            if (!formContentId) return;
            let fromId = window.frameElement.getAttribute('id');
            openAccurateTab({
              type:PageRoutesTypeEnum.PagePaasTemplateDetail,
              key:bizNo,
              params: `formContentId=${formContentId}`,
              fromId
            })
        },
        // 清空选中数据
        clearSelection() {
            this.toggleSelection();
        },
        // 设置表格选中
        toggleSelection(rows) {
            this.$nextTick(() => {
                if (rows) {
                    rows.forEach(r => {
                      this.tableData.forEach(row => {
                        if (row.id === r.id) {
                          this.$refs.table.toggleRowSelection(row, true);
                        }
                      });
                    });
                  } else {
                    this.$refs.table.clearSelection();
                    this.selectedBizList = [];
                    this.selectedList = [];
                  }
            });
        },
        // 打开命中规则明细弹窗
        openStanderDialog(id) {
            this.$refs.conditionsDialogRef.open(id)
        },
        // 获取结算标准列表
        getStandardList() {
            getSettlementStandardList({
                pageSize: 1000,
                pageNum: 1,
            }).then(res => {
                if (res.succ) {
                    this.standardList = res.data?.list || [];
                } else {
                    this.$message.error(res.message);
                }
            }).catch(err => {
                console.error(err);
            })
        },
        // 是否允许调整
        isAllowedAdjustment(row) {
            return this.allowedAdjustmentList.some(i => i.value === row?.settlementItemId);
        },
        // 打开选择列弹窗
        showAdvancedSetting() {
            this.$refs.advanced.open(this.columns);
        },
        /*保存选择列配置*/
        saveColumnStatus(event) {
            try {
                let columns = _.cloneDeep(event.data || [])

                this.columns = []
                this.$nextTick(async () => {

                    this.$set(this, 'columns', columns.slice());
                    await this.saveColumnStatusToStorage()
                    this.$message.success(this.$t('common.base.saveSuccess'));
                })
            } catch (e) {
                console.error(e)
            }
        },
        /*保存选择列配置*/
        async saveColumnStatusToStorage() {
            try {
                const localStorageData = await this.getLocalStorageData()
                let columnsStatus = null


                const columnsList = this.columns.map(c => ({
                    field: c.field,
                    show: c.show,
                    width: c.width,
                }));

                if (localStorageData.columnStatus) {
                    localStorageData.columnStatus = columnsList;
                    columnsStatus = localStorageData.columnStatus;
                } else {
                    columnsStatus = columnsList;
                }
                
                await this.saveDataToStorage('columnStatus', columnsStatus);
            } catch (e) {
                console.error(e)
            }
        },

        /*保存数据*/
        async saveDataToStorage(key, value) {
            try {
                const data = await this.getLocalStorageData()
                data[key] = value
                await storageSet(this.selectColumnKey, JSON.stringify(data))
            } catch (e) {
                console.error(e)
            }
        },

        /*获取选择列配置*/
        async getLocalStorageData() {
            let data = {}
            try {
                data = await storageGet(this.selectColumnKey)
                data = JSON.parse(data)
            } catch (error) {
                data = {}
                console.error(error)
            }

            return data
        },

        /*获取选择列表单*/
        async getLocalColumns() {
            let columnStatus = []
            try {
                const localStorageData = await this.getLocalStorageData();
                columnStatus = localStorageData?.columnStatus ?? [];

                columnStatus= columnStatus
                    .map((i) => (typeof i == 'string' ? { field: i, show: true } : i))
                    .reduce((acc, col, currentIndex) => {
                        acc[col.field] = {
                            field: col,
                            index: currentIndex,
                        }
                        return acc
                    }, {});
            } catch (e) {
                console.error(e)
            }

            return columnStatus;
        },

        /*选择列排序*/
        buildSortFields(originFields = [], fieldsMap = {}) {
            let fields = [];
            let unsortedFields = [];
      
            originFields.forEach(originField => {
              let { fieldName } = originField;
              let field = fieldsMap[fieldName];
      
              if (field) {
                let { index } = field;
                fields[index] = originField;
              } else {
                unsortedFields.push(originField);
              }
            });
      
            fields = fields.concat(unsortedFields)

            // 选择列配置 是否勾选（显示）&宽度
            fields = fields.map((col) => {
                let show = col.show;
                let width = col.width;
                let localField = fieldsMap[col.field]?.field || null;

                if (null != localField) {
                    if (localField.width) {
                        width = typeof localField.width == 'number'
                            ? `${localField.width}px`
                            : localField.width;
                    }
                    show = localField.show !== false;
                }

                col.show = show;
                col.width = width;
                col.minWidth = col.width || 150;
                col.type = 'column';
                return col;
            });

            return fields
        },
        /*拖拽表头*/
        headerDragend (newWidth, oldWidth, column, event) {
            let data = this.columns
                .map((item) => {
                    if (item.fieldName === column.property) {
                        item.width = column.width;
                    }
                    return item;
                })
                .map((item) => {
                    return {
                        field: item.field,
                        show: item.show,
                        width: item.width,
                    };
                });
            this.modifyColumnStatus({ type: 'column', data });
        },
        /*修改选择列设置*/
        modifyColumnStatus (event) {
            let columns = event.data || [],
                colMap = columns.reduce(
                    (acc, col) => (acc[col.field] = col) && acc,
                    {}
                );
            this.columns.forEach((col) => {
                let newCol = colMap[col.field];
                if (null != newCol) {
                    this.$set(col, 'show', newCol.show);
                    this.$set(col, 'width', newCol.width);
                }
            });
            this.saveColumnStatusToStorage();
        },
    },
    components: {
        InitiateSettlementDialog,
        GenerateDetailsDialog,
        ApplyAdjustmentDialog,
        BaseSearchDrawer,
        BaseSearchPanel,
        ConditionsDialog,
    },
}