<template>
  <el-dialog :title="title" :visible.sync="visible" :close-on-click-modal="false" width="500px">
    <!-- 主体内容 -->
    <el-form :model="model" :rules="rules" ref="formRef" label-width="115px">
      <el-form-item v-if="isMultiLanguage" class="multiLanguage">
        <span slot="label">
          <span>{{ $t('portal.multilingual') }}</span>
          <el-tooltip
            effect="dark"
            :content="$t('portal.portalTip12')"
            placement="top"
          >
            <i class="iconfont icon-fdn-info"></i>
          </el-tooltip>
        </span>
         <el-checkbox-group v-model="checkLists">
            <el-checkbox
              @change="checkboxChange(i,$event)"
              v-for="(v, i) in language"
              :key="v.code"
              :label="v.code"
              :disabled="v.isMainLanguage"
            >
              {{v.remark}}
            </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <template v-for="v in templateNameList">
        <el-form-item
          :label="`${v.label}`"
          :key="v.key"
          :prop="v.key"
          v-if="v.show"
        >
          <el-input
            :placeholder="$t('common.placeholder.input2')"
            v-model="model[v.key]"
          ></el-input>
        </el-form-item>
      </template>
    </el-form>

    <div slot="footer">
      <el-button type="default" @click="visible = false">{{ $t('common.base.cancel') }}</el-button>
      <el-button type="primary" @click="submit">{{ $t('common.base.confirm') }}</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { reactive, toRefs, ref, computed, inject, onMounted } from 'vue';
import { cloneDeep } from 'lodash'; 
export default {
  name: 'edit-template-name-dialog',
  props: {
    title: {
      type: String,
      default: '',
    },
    doorType: {
      type: Object,
      default: () => ({}),
    },
    callBack: {
      type: Function,
    },
    systemLanguage: {
      type: Array,
      default: () => [],
    },
    checkList: {
      type: Array,
      default: () => [],
    },
    language:{
      type: Array,
      default: () => [],
    },
  },
  setup(props, { emit }) {
    const formRef = ref(null);

    const isMultiLanguage = inject('isMultiLanguage')

    const languagesList = inject('languagesList')

    const checkLists = ref([])

    const state = reactive({
      visible: false,
      templateNameList: [],
      model: {},
      rules: {},
      checkLists: [],
    });
    // 模板语言列表
    const languages = computed(() =>{
      let temp = (props?.doorType?.setting?.language ?? []).map(v=>v.code)
      if(isMultiLanguage.value){
        return temp
      }
      // 筛选掉没开灰度的语言
      return [temp[0]]
    });
      // 模板多语言名称列表
    const doorNameLanguage = computed(
      () => props.doorType?.setting?.doorNameLanguage ?? {}
    );
    const checkboxChange = () => {
      const model = {}
      const templateNameList = checkLists.value.map((v) => {
        model[v] = ''
        const item = props.systemLanguage.find(v2 => v2.code == v)
        return {
          key: item.code,
          label: item.languageValue,
          show:true
        }
      })
      state.templateNameList = templateNameList
      state.model = model
      Object.keys(doorNameLanguage.value).map(key => {
        state.model[key] = doorNameLanguage.value[key];
      });
    }

    const openDialog = () => {
      state.visible = true;
      const model = {}
      checkLists.value = cloneDeep(props.checkList)
      const templateNameList = checkLists.value.map((v) => {
        model[v] = ''
        const item = props.systemLanguage.find(v2 => v2.code == v)
        return {
          key: item.code,
          label: item.languageValue,
          show:true
        }
      })
      state.templateNameList = templateNameList
      state.model = model
      // 初始化表单验证规则
      state.templateNameList.map(v => {
        state.rules[v.key] = [
          { required: false, message: `请输入${v.label}`, trigger: 'change' },
          { max: 50, message: '最多50个字符', trigger: 'change' },
        ];
      });
      state.rules['zh'] = [
        { required: true, message: '请输入中文名称', trigger: 'change' },
        { max: 50, message: '最多50个字符', trigger: 'change' },
      ];

      Object.keys(doorNameLanguage.value).map(key => {
        state.model[key] = doorNameLanguage.value[key];
      });
    };
    const submit = () => {
      formRef.value.validate(valid => {
        if (valid) {
          state.visible = false;
          const doorNameLanguage = {};
          languages.value.map(key => {
            doorNameLanguage[key] = state.model[key];
          });

          emit('callBack', {checkList:checkLists.value,doorNameLanguage});
        } else {
          console.info('表单校验不通过');
        }
      });
    };
    return {
      ...toRefs(state),
      formRef,
      languages,
      isMultiLanguage,
      openDialog,
      submit,
      languagesList,
      checkboxChange,
      checkLists
    };
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-form-item__label {
  text-align: left;
  margin-left: 15px;
}
  .el-form-item{
  display: flex;
}
  ::v-deep .el-checkbox{
    margin-right: 16px;
  }
  ::v-deep .el-input{
    width: 280px;
  }
  ::v-deep .el-form-item__content {
    margin-left: 20px !important;
  }
  ::v-deep .is-required:before{
    margin-right: 0;
  }
  ::v-deep .el-dialog__body {
    padding-right: 30px;
  }
</style>