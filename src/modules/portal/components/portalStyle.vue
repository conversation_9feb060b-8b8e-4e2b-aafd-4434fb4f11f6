<template>
  <div class="main-container">
    <div class="main-container-left">
      <h2>{{ $t('portal.chooseAColorScheme') }}</h2>
      <div class="config-style-blendent">
        <div
          class="config-style-blendent-group"
          v-for="item in colorList"
          :key="item.label"
        >
          <div
            class="config-style-blendent-group-item"
            :class="{
              'checked-color': checkedColor === item.color,
            }"
            @click="handleChangeColor(item.color)"
          >
            <div></div>
            <div :style="{ background: item.color.split(',')[0] }"></div>
            <div :style="{ background: item.color.split(',')[1] }"></div>
          </div>
          <span
            :class="{
              'custom-color': item.value === 4,
            }"
            >{{ item.label }}</span
          >
          <el-color-picker
            v-if="item.value === 4"
            v-model="customColor"
            @change="handleUpdateColorList"
          ></el-color-picker>
        </div>
      </div>
    </div>
    <div class="main-container-right">
      <h2>{{ $t('portal.previewEffect') }}</h2>
      <div class="config-style-preview">
        <div class="config-style-preview-item" :style="previewImage1">
          <div class="config-style-preview-item-text">
            <i :style="{ background: styleColor }"></i>
            <span :style="{ color: styleColor }">[xxx]{{$t('task.record.addRemark')}}</span>
          </div>
          <div
            class="config-style-preview-item-button"
            :style="{ background: styleColor }"
          >
            {{ $t('task.detail.components.serviceEvaluate') }}
          </div>
        </div>
        <div class="config-style-preview-item" :style="previewImage2">
          <div class="config-style-preview-item-tabbar">
            <div
              v-for="(item, index) in tabBarFormatList"
              :key="item.title"
              :style="{ color: index === 2 ? styleColor : '#000' }"
            >
              <i class="iconfont" :class="item.icon"></i>
              <span>{{ item.title }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed, onMounted, getCurrentInstance } from 'vue';
import { useConfigStyle } from '@hooks/portal/useConfig.ts';
import { getLocalesOssUrl } from '@src/util/assets'
/* version control mixin */
import { VersionControlLinkCMixin } from '@src/mixins/versionControlMixin'

const previewImage1 = `background-image: url(${getLocalesOssUrl('/portalPreviewImage1.png')});`

export default {
  mixins: [VersionControlLinkCMixin],
  props: {
    typeId: {
      type: String,
      default: '',
    },
    themeColour: {
      type: String,
      default: '',
    },
    tabBarList: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, _) {
    
    const instance = getCurrentInstance();
    const {
      colorList,
      checkedColor,
      customColor,
      styleColor,
      initColor,
      handleCheckSave,
      handleChangeColor,
      handleUpdateColorList,
      handleBack,
      handleSave,
    } = useConfigStyle();
    
    const tabBarFormatList = computed(() => {
      return (
        Object.keys(props.tabBarList)
          .map(v => {
            return props.tabBarList[v];
          })
          .sort((a, b) => {
            return a.order - b.order;
          }).filter(v => {
            
            // 纯客服云版本不显示商城
            if (v.value == '商城') {
              return instance?.proxy?._isShowLinkCShop
            }
            
            return v
            
          }) || []
      );
    });
    
    const save = (isRelease) => {
      return handleSave(props.typeId, isRelease);
    };
    
    const previewImage2 = computed(() => {
      return (
        instance?.proxy?._isShowLinkCShop
        ? `background-image: url(${getLocalesOssUrl('/portalPreviewImage2.png')});`
        : `background-image: url(${getLocalesOssUrl('/portalPreviewImage3.png')});`
      )
    })
    
    onMounted(() => {
      initColor(props.themeColour);
    });
    
    return {
      colorList,
      checkedColor,
      customColor,
      styleColor,
      handleCheckSave,
      handleChangeColor,
      handleUpdateColorList,
      save,
      handleBack,
      tabBarFormatList,
      previewImage1,
      previewImage2
    };
  },
};
</script>

<style lang="scss" scoped>
@import '../index';
$configMainColor: $color-primary-light-6;

.main-container {
  width: 100%;
  height: calc(100vh - 50px);
  display: flex;

  &-left {
    width: 280px;
    border-right: 1px solid #e8e8e8;
    padding: 24px;

    h2 {
      @include font-setting(16px, 400, #262626, 22px);
    }

    .config-style-blendent {
      margin-top: 24px;

      &-group {
        margin: 8px 0;
        @include flex-setting(flex-start, center);

        &-item {
          @include box-setting(70px, 40px, #ffffff);
          @include flex-setting();
          border: 1px solid transparent;
          position: relative;
          cursor: pointer;

          div {
            width: 24px;
            height: 24px;
          }

          div:first-child {
            width: 0;
            height: 0;
            border: 5px solid transparent;
            border-top-color: $configMainColor;
            border-right-color: $configMainColor;
            position: absolute;
            top: 0;
            right: 0;
            opacity: 0;
          }

          div:last-child {
            box-sizing: border-box;
            border: 1px solid #d9d9d9;
            border-left: none;
          }
        }

        span {
          @include font-setting(14px, 400, #595959, 20px);
          margin: 0 16px;
        }

        .custom-color {
          color: $configMainColor;
          cursor: pointer;
        }
      }

      .checked-color {
        border: 1px solid $configMainColor;

        .div:first-child {
          opacity: 1;
        }
      }
    }
  }

  &-right {
    padding: 24px 40px;

    h2 {
      @include font-setting(16px, 400, #262626, 22px);
    }

    .config-style-preview {
      // margin-top: 36px;
      display: flex;

      &-item {
        width: 320px;
        height: 512px;
        background-size: 100%;
        position: relative;

        &-text {
          position: absolute;
          top: 138px;
          left: 64px;
          font-size: 12px;
          display: flex;
          align-items: center;

          i {
            display: block;
            width: 5px;
            height: 5px;
            border-radius: 3px;
            margin-right: 5px;
          }
        }

        &-button {
          @include box-setting(70%, 36px, #999999, 5px);
          @include font-setting(12px, 400, #ffffff, 36px);
          position: absolute;
          bottom: 58px;
          left: 50%;
          transform: translateX(-50%);
          text-align: center;
        }

        &-tabbar {
          @include box-setting(70%, 36px, transparent);
          @include font-setting(12px, 400, #000000, 20px);
          position: absolute;
          bottom: 58px;
          left: 50%;
          transform: translateX(-50%);
          display: flex;
          align-items: center;

          div {
            flex: 1;
            text-align: center;
            display: flex;
            flex-direction: column;
          }
        }
      }

      &-item:last-child {
        background-size: 100%;
      }
    }
  }
}
</style>
