<template>
  <div class="portalTemplate" id="_portalTemplate">
    <div class="btn-box" v-if="isHavePortalPageSetAuth()">
      <el-button class="add-btn" type="primary" @click="showAddDialog"
      >{{ $t('portal.createPortal') }}</el-button
      >
    </div>

    <!-- 模板列表 -->
    <ul class="template-list">
      <li
        class="template-item"
        v-for="(v, i) in doorList"
        :key="v.id"
        :class="{ active: v.id == doorTypeId }"
        @click="switchTemplate(v, i)"
      >
        <div class="template-item-status">
          <span
            class="circle"
            :style="{ backgroundColor: statusMap[v.inUse].color }"
          ></span>
          <span
            class="text ml_8"
            :style="{ color: statusMap[v.inUse].color }"
          >{{ statusMap[v.inUse].text }}</span
          >
        </div>
        <div class="template-item-name">
          {{ v.zh_CN_NAME }}
        </div>
        <div class="template-item-language mt_4">
          <span class="text">
            {{ v.isMultilingual ? $t('portal.multilingualPortal') : $t('portal.chinesePortal') }}
          </span>
          <el-dropdown>
            <i class="iconfont icon-MoreOutlined"></i>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="handleClickCopyTemplate(v)" v-if="isHavePortalPageSetAuth()">
                {{ $t('portal.copyPortal') }}
              </el-dropdown-item>
              <el-dropdown-item
                @click.native="handleClickCopyShareUrl(v.setting.protalUrl)"
              >
              {{ $t('portal.copyShareLink') }}
              </el-dropdown-item>
              <el-dropdown-item
                v-if="v.defaultType === 1 && isHavePortalPageSetAuth()"
                @click.native="handleClickDelTemplate(v)"
              >
              {{ $t('portal.deletePortal') }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </li>
    </ul>

    <!-- 创建门户模板弹窗 -->
    <AddTemplateDialog ref="addDialog" :title="$t('portal.newPortal')" />
  </div>
</template>

<script>
/** vue */
import { reactive, toRefs, ref, computed, inject,onMounted } from 'vue';
import AddTemplateDialog from '@src/modules/portal/components/AddTemplateDialog.vue';

/* api */
import { getInfos } from '@src/api/LinkcApi'
/** util */
import { isHavePortalPageSetAuth } from '@service/AuthService'
/* hooks */
import { useCopy } from '@hooks/useElement';
/** model */
import { COPY_SHARE_ERROR, COPY_SUCCESS } from '@src/model/const/Alert';
import { MessageBox, Message } from 'element-ui';
import i18n from '@src/locales';

export default {
  name: 'portal-template-list',
  components: {
    AddTemplateDialog,
  },
  props: {
    doorList: {
      type: Array,
      default: () => [],
    },
    doorType: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    const editDoorType = inject('editDoorType');

    const doorTypeId = computed(() => props.doorType.id);

    const addDialog = ref(null);

    const state = reactive({
      visible: true,
      maxDoor: 10,
    });

    const statusMap = {
      1: {
        text: i18n.t('common.customer.questionnaire.deactivated'),
        color: 'rgba(0, 0, 0, 0.65)',
      },
      0: {
        text: i18n.t('common.base.enable'),
        color: '#52C41A',
      },
    };

    // 打开模板创建弹窗
    const showAddDialog = () => {
      if (props.doorList.length >= state.maxDoor) {
        return Message.warning(i18n.t('portal.portalTip5', { num: state.maxDoor }));
      }
      addDialog.value.openDialog();
    };

    // 复制门户
    const handleClickCopyTemplate = v => {
      if (props.doorList.length >= state.maxDoor) {
        return Message.warning(i18n.t('portal.portalTip5', { num: state.maxDoor }));
      }

      MessageBox.confirm(
        i18n.t('portal.portalTip1'),
        i18n.t('portal.portalTip2'),
        {
          confirmButtonText: i18n.t('common.base.confirm'),
          cancelButtonText: i18n.t('common.base.cancel'),
          type: 'warning',
        }
      )
        .then(() => {
          const params = {
            id: v.id,
          };
          editDoorType({ editType: 'copy', params });
        })
        .catch(err => {
          console.log(err);
        });
    };

    // 点击复制分享链接
    const handleClickCopyShareUrl = shareLink => {
      useCopy(shareLink, COPY_SUCCESS, COPY_SHARE_ERROR);
    };

    // 删除门户
    const handleClickDelTemplate = v => {
      MessageBox.confirm(
        i18n.t('portal.portalTip3'),
        i18n.t('portal.portalTip4'),
        {
          confirmButtonText: i18n.t('common.base.confirm'),
          cancelButtonText: i18n.t('common.base.cancel'),
          type: 'warning',
        }
      )
        .then(() => {
          const params = {
            id: v.id,
          };
          editDoorType({ editType: 'delete', params });
        })
        .catch(err => {
          console.log(err);
        });
    };

    const switchTemplate = (v, i) => {
      if (v.id != state.doorTypeId) {
        emit('changeDoor', i);
      }
    };

    const scrollBottom = () => {
      let ele = document.getElementById('portalTemplateList');
      ele.scrollTop = ele.scrollHeight;
    };

    const scrollTop = () => {
      let ele = document.getElementById('portalTemplateList');
      ele.scrollTop = 0;
    };

    const queryInfos = async () =>{
      const {status,data} = await getInfos()
      if(status==200){
        let oldMax = state.maxDoor
        state.maxDoor = data?.baseInfo?.doorTypeCountLimit ?? oldMax
      }
    }

    onMounted(()=>{
      queryInfos()
    })

    return {
      ...toRefs(state),
      doorTypeId,
      statusMap,
      addDialog,
      showAddDialog,
      handleClickCopyTemplate,
      handleClickCopyShareUrl,
      handleClickDelTemplate,
      switchTemplate,
      scrollBottom,
      scrollTop,
      queryInfos,
      isHavePortalPageSetAuth,
    };
  },
};
</script>

<style scoped lang="scss">
.portalTemplate {
  width: 208px;
  height: 100vh;
  background: #ffffff;
  box-sizing: border-box;
  overflow-y: auto;
  padding: 16px 24px;
  padding-top: 0;
  scroll-behavior: smooth;

  .btn-box{
    position: sticky;
    left: 0;
    top: 0;
    z-index: 99;
    padding: 16px 0;
    background-color: #ffffff;
    .add-btn {
    width: 100%;
  }
  }

  

  .template-list {
    width: 100%;

    .template-item {
      width: 100%;
      box-sizing: border-box;
      border-radius: 4px;
      border: 2px solid rgba(0, 0, 0, 0.05);
      margin-bottom: 13px;
      background: url('@src/assets/img/template-bg.png') no-repeat top center;
      background-size: 100% 108px;
      padding: 4px 12px 12px 8px;
      cursor: pointer;
      &:last-child {
        margin-bottom: 0;
      }

      &.active {
        border: 2px solid $color-primary-light-6;
      }

      &-status {
        width: 100%;
        display: flex;
        align-items: center;

        .circle {
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background-color: red;
        }

        .text {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.45);
        }
      }

      &-name {
        padding-top: 95px;
        font-weight: bold;
        color: rgba(0, 0, 0, 0.85);
        font-size: 14px;
        @include text-ellipsis;
      }

      &-language {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
        display: flex;
        justify-content: space-between;
        .icon-ellipsis {
          //  transform: rotate(45deg);
        }
      }
    }
  }
}
</style>
