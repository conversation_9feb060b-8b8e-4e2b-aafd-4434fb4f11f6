import { AIAgentLogSourceEnum, AIAgentTypeEnum, AiModelEnum } from "@src/modules/ai/model/enum"
/* type */
import { SummaryField } from "@src/modules/ai/components/summary/type"
import { AIAgentAppAbilityBizDataItem, AIAgentAppType } from "@src/modules/ai/types/agent"

type AIAgentListParamType = {
  keyword?: string
  type?: number
}

type AIAgentDetailParamType = {
  id: number
  verifyPermissions?: boolean
}

type AIAgentAppListParamType = {
  id: number
}

type AddDingtalkAgentParamType = {
  authCode: string
}

type AIAgentAppDetailParamType = {
  id: number
}

type AIAgentAppDetailUpdateParamType = AIAgentAppType

type AIAgentAppResetPromptParamType = {
  id: number
}

type AIAgentEditNameParamType = {
  id: number
  name: string
}

type AIAgentEditModelParamType = {
  id: number
  model: AiModelEnum
}

type AIAgentAppTemplateListParamType = {
  type: AIAgentType<PERSON><PERSON>
}

type AIAgentAppAddTemplateParamType = {
  id: number
  templateIds: number[]
}

type AIAgentAppDeleteParamType = {
  id: number
}

type AIAgentCreateParamType = {
  name: string
  type: AIAgentTypeEnum
}

type AIAgentAppAbilityListParamType = {
  agentAppId: number
  keyword?: string
}

type AIAgentAppAbilityAddParamType = {
  agentAppId: number
  bizDataList: AIAgentAppAbilityBizDataItem[]
}

type AIAgentAppAbilityRelearnParamType = {
  knowledgeId: number
}

type AIAgentAppAbilityRemoveParamType = {
  agentAppId?: number
  knowledgeId: number
}

type AIAgentAppAuthListParamType = {
  id: number
}

type AIVocAgentSummaryParamType = {
  fields: SummaryField[];
  require: string;
  bizIds: string[];
}

type AIImQaAgentParamType = {
  question: string;
  tenantId?: string;
  userId?: string;
  conversationId?: string;
}

type AIVocAgentSummaryAbstractParamType = {
  template: string;
  taskTemplateId?: string;
  content: string;
  bizIds: string[];
  fields: string[];
  exportParam: Record<string, any>;
}

type AIVocAgentDetailSummaryParamType = {
  tag: string;
  recordId: number;
  url: string;
}

type AIImAgentDetailSummaryParamType = {
  
}

type AIAgentWorkflowShowParamType = {
  agentTemplate?: string
  template: string
}

type AIAgentStatusParamType = {
  id: number
  status: number
}

type AIAgentDeleteParamType = {
  id: number
}

type AIAgentLogParamType = {
  agentId?: number
  agentAppId?: number
  startTime?: number
  endTime?: number
  pageNum?: number
  pageSize?: number
  sortBy?: {
    createTime: boolean
  }
  source?: AIAgentLogSourceEnum
}

type AiChatAgentConversationParamType = {
  id: number
  agentId: number
  question: string
  model?: AiModelEnum
  tenantId?: string
  userId?: string
  disabledAgentEnabledValidate?: boolean
  source?: AIAgentLogSourceEnum
  doorId?: string
  conversationId?: string
}

type AiAgentChartParamType = {
  startTime?: number
  endTime?: number
  agentId?: number | undefined
  agentAppId?: number | undefined
}

type AiGetTicketParameterExtractionParamType = {
  question: string
  taskTypeId: string
}

type GetAbstractTemplateListParamType = {
  type: 'task' | 'im' | 'voc' | string
}

type DeleteNotOpenWikiParamType = {
  id: string
}

type SendShareAccessMessageParamType = {
  agentId: number
  ranges: Record<string, any>
}

type GetAbstractFieldsParamType = {
  bizType: 'TASK' | 'CALLCENTER' | 'IM'
  bizTypeId: string
}

export {
  AIAgentListParamType,
  AIAgentDetailParamType,
  AIAgentEditNameParamType,
  AIAgentAppTemplateListParamType,
  AIAgentAppAddTemplateParamType,
  AIAgentCreateParamType,
  AIAgentAppDetailParamType,
  AIAgentAppAbilityListParamType,
  AIAgentAppAbilityAddParamType,
  AIAgentAppAbilityRemoveParamType,
  AIAgentAppAuthListParamType,
  AIVocAgentSummaryParamType,
  AIVocAgentDetailSummaryParamType,
  AIAgentStatusParamType,
  AddDingtalkAgentParamType,
  AIAgentAppListParamType,
  AIAgentAppDeleteParamType,
  AIAgentAppDetailUpdateParamType,
  AIAgentEditModelParamType,
  AIAgentDeleteParamType,
  AIAgentAppResetPromptParamType,
  AIAgentWorkflowShowParamType,
  AIAgentLogParamType,
  AiChatAgentConversationParamType,
  AiAgentChartParamType,
  AIVocAgentSummaryAbstractParamType,
  AIImAgentDetailSummaryParamType,
  AIImQaAgentParamType,
  AiGetTicketParameterExtractionParamType,
  DeleteNotOpenWikiParamType,
  AIAgentAppAbilityRelearnParamType,
  SendShareAccessMessageParamType,
  GetAbstractTemplateListParamType,
  GetAbstractFieldsParamType
}
