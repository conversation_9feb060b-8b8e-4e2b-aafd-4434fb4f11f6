import { getLocalesOssUrl } from "@src/util/assets"

/**
 * Agent机器人类型枚举
 */
export enum AIAgentTypeEnum {
  /** 聊天助手 */
  Chat = 0,
  /** 工作流 */
  Workflow = 1
}

export enum AIAgentTemplateEnum {
  VOC = 'voc',
  Dingtalk = 'dingtalk',
  Task = 'task',
  IM = 'im',
  QA = 'qa',
  IM_QA = 'im_qa',
  System = 'system',
  TaskFormChat = 'task_form_chat',
  TaskForm = 'task_form',
}

/**
 * Agent应用设置页面的标签页枚举
 */
export enum AIAgentAppSettingTabEnum {
  /** 应用设置 */
  APP = 'app',
  /** 能力设置 */
  ABILITY = 'ability',
  /** 授权管理 */
  AUTH = 'auth'
}

// 数据类型枚举
export enum AIAgentAppAbilityTypeEnum {
  APPLICATION = 1,
  KNOWLEDGE_BASE = 2,
  LOCAL_FILE = 3,
  EXTERNAL_LINK = 4
}

// 学习状态枚举
export enum AIAgentAppAbilityLearningStatusEnum {
  NOT_STARTED = 0,
  IN_PROGRESS = 1,
  COMPLETED = 2,
  FAILED = 3
}

// 删除标记枚举
export enum AIAgentAppAbilityDeleteTagEnum {
  NOT_DELETED = 0,
  DELETED = 1
}

export enum AIAgentViewTabEnum {
  List = 'list',
  Chart = 'chart',
  Log = 'log'
}

export enum AIAgentLogSourceEnum {
  // 自助门户
  DOOR = 'DOOR',
  // 外部分享
  OUTSIDE = 'OUTSIDE',
  // 内部访问
  INSIDE = 'INSIDE',
  // 系统
  SYSTEM = 'SYSTEM'
}

export const AIAgentLogSourceCNNameEnum = {
  [AIAgentLogSourceEnum.DOOR]: '自助门户',
  [AIAgentLogSourceEnum.OUTSIDE]: '外部分享',
  [AIAgentLogSourceEnum.INSIDE]: '内部访问',
  [AIAgentLogSourceEnum.SYSTEM]: '系统'
}

export const AIAgentViewTabCNNameEnum = {
  [AIAgentViewTabEnum.List]: '智能体管理',
  [AIAgentViewTabEnum.Log]: '智能体日志',
  [AIAgentViewTabEnum.Chart]: '使用分析'
}

export enum AIAgentAppAuthTypeEnum {
  ALL = '1',
  PART = '0',
  HIDDEN = '2'
}

export const AIAgentAppAuthTypeCNNameEnum = {
  [AIAgentAppAuthTypeEnum.ALL]: '全部员工',
  [AIAgentAppAuthTypeEnum.PART]: '部分员工',
  [AIAgentAppAuthTypeEnum.HIDDEN]: '不可见'
}

export enum AiModelEnum {
  // OpenAI
  OPENAI = 0,
  // 通义大模型
  TONG_YI = 1,
  // 文心大模型
  WEN_XIN = 2,
  // 豆包大模型
  DOU_BAO = 3,
  // 月之暗面大模型
  MOON_SHOT = 4,
  // 讯飞大模型
  XUN_FEI = 5,
  // 钉钉大模型
  DING_DING = 6,
  // 其他
  OTHER = 7,
  // 深度求索
  DEEPSEEK = 8,
  // 售后宝
  SHB = 9
}

export const AiModelCNNameEnum = {
  [AiModelEnum.OPENAI]: 'OpenAI',
  [AiModelEnum.DEEPSEEK]: 'DeepSeek大模型',
  [AiModelEnum.TONG_YI]: '通义大模型',
  [AiModelEnum.WEN_XIN]: '文心大模型',
  [AiModelEnum.DOU_BAO]: '豆包大模型',
  [AiModelEnum.MOON_SHOT]: '月之暗面大模型',
  [AiModelEnum.XUN_FEI]: '讯飞大模型',
  [AiModelEnum.DING_DING]: '钉钉大模型',
  [AiModelEnum.OTHER]: '其他',
  [AiModelEnum.SHB]: '售后宝专属模型'
}

export const AiModelIconEnum = {
  [AiModelEnum.OPENAI]: getLocalesOssUrl('/ai/model_openai.png'),
  [AiModelEnum.DEEPSEEK]: getLocalesOssUrl('/ai/model_deepseek.png'),
  [AiModelEnum.TONG_YI]: getLocalesOssUrl('/ai/model_tongyi.png'),
  [AiModelEnum.WEN_XIN]: getLocalesOssUrl('/ai/model_wenxin.png'),
  [AiModelEnum.DOU_BAO]: getLocalesOssUrl('/ai/model_doubao.png'),
  [AiModelEnum.MOON_SHOT]: getLocalesOssUrl('/ai/model_moon.png'),
  [AiModelEnum.XUN_FEI]: getLocalesOssUrl('/ai/model_xunfei.png'),
  [AiModelEnum.DING_DING]: getLocalesOssUrl('/ai/model_dingding.png'),
  [AiModelEnum.OTHER]: getLocalesOssUrl('/ai/model_other.png'),
  [AiModelEnum.SHB]: getLocalesOssUrl('/ai/model_shb.png')
}

export enum AIAgentAppAuthRangeTypeEnum {
  // 部门
  DEPARTMENT = 0,
  // 角色
  ROLE = 1,
  // 服务商
  SERVICE_PROVIDER = 2,
  // 人员
  USER = 3
}

export const AIAgentAppAuthRangeTypeCNNameEnum = {
  [AIAgentAppAuthRangeTypeEnum.DEPARTMENT]: '部门',
  [AIAgentAppAuthRangeTypeEnum.ROLE]: '角色',
  [AIAgentAppAuthRangeTypeEnum.SERVICE_PROVIDER]: '服务商',
  [AIAgentAppAuthRangeTypeEnum.USER]: '人员'
}

export enum AiKnowledgeTypeEnum {
  TASK = 'TASK',
  EVENT = 'EVENT',
  REPORT = 'REPORT',
  CALL_CENTER = 'CALL_CENTER',
  CUSTOMER = 'CUSTOMER',
  PRODUCT = 'PRODUCT',
  FAULT_LIBRARY = 'FAULT_LIBRARY',
  PAAS = 'PAAS',
  WIKI = 'SYSTEM_KNOWLEDGE',
  IM = 'IM',
  LOCAL_UPLOAD = 'LOCAL_UPLOAD'
}


export enum AgentChatTimeTypeEnum {
  LAST7 = 1,
  LAST30 = 2,
  CURRENT_MONTH = 3,
  CURRENT_YEAR = 4,
  CUSTOM = 5,
  ALL = 0
}


export const AgentChatTimeTypeCNNameEnum = {
  [AgentChatTimeTypeEnum.LAST7]: '最近7日',
  [AgentChatTimeTypeEnum.LAST30]: '最近30日',
  [AgentChatTimeTypeEnum.CURRENT_MONTH]: '本月',
  [AgentChatTimeTypeEnum.CURRENT_YEAR]: '今年',
  [AgentChatTimeTypeEnum.CUSTOM]: '自定义',
  [AgentChatTimeTypeEnum.ALL]: '全部'
}

export enum AIAgentTrailTypeEnum {
  // 智能体
  AI_AGENT = 25,
  // 智能体-小宝
  AI_AGENT_SYSTEM = 26,
  // 智能体-VOC摘要
  AI_AGENT_VOC_SUMMARY = 27,
  // 智能体-智能客服
  AI_AGENT_IM = 28,
  // 智能体-智能问答
  AI_AGENT_QA = 29,
  // 智能体-工单摘要
  AI_AGENT_TASK_SUMMARY = 30
}

export const AIAgentTrailTypeTemplateEnum = {
  [AIAgentTemplateEnum.System]: AIAgentTrailTypeEnum.AI_AGENT_SYSTEM,
  [AIAgentTemplateEnum.VOC]: AIAgentTrailTypeEnum.AI_AGENT_VOC_SUMMARY,
  [AIAgentTemplateEnum.IM]: AIAgentTrailTypeEnum.AI_AGENT_IM,
  [AIAgentTemplateEnum.QA]: AIAgentTrailTypeEnum.AI_AGENT_QA,
  [AIAgentTemplateEnum.Task]: AIAgentTrailTypeEnum.AI_AGENT_TASK_SUMMARY,
}
