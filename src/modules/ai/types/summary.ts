
type AiSummaryTemplateType = {
  id: number
  templateName: string
  bizType: string
  bizTypeId: string
  tenantId: string | null
  content: string
  fields: AiSummaryTemplateField[]
}

type AiSummaryTemplateField = {
  enName: string
  cnName: string
  formType: string
}

type AiSummaryFieldsType = {
  fieldName: string
  displayName: string
  formType: string
  isSystem: boolean
}

export {
  AiSummaryTemplateType,
  AiSummaryTemplateField,
  AiSummaryFieldsType
}