import LoginUser from '@model/entity/LoginUser/LoginUser'
import { 
  BaseSelectUserSelectedItemType, 
  BaseSelectUserTypeIdEnum 
} from '@src/component/common/BaseSelectUser'
import { 
  AIAgentAppAbilityLearningStatusEnum, 
  AIAgentAppAbilityTypeEnum, 
  AIAgentAppAbilityDeleteTagEnum, 
  AIAgentAppAuthTypeEnum,
  AIAgentAppAuthRangeTypeEnum,
  AiKnowledgeTypeEnum,
  AIAgentLogSourceEnum
} from '@src/modules/ai/model/enum'
/* type */
import { SummaryField } from '@src/modules/ai/components/summary/type'

type AIAgentAppRangeForm =  {
  users: BaseSelectUserSelectedItemType[]; 
  tags: BaseSelectUserSelectedItemType[];
  providers: BaseSelectUserSelectedItemType[];
  roles: BaseSelectUserSelectedItemType[];
}

type AIAgentDataSummaryType = {
  // 启用 Agent 数量
  numberOfAgentsEnabled: number
  // 平均活跃用户数
  activeUsersToday: number
  // 平均活跃用户数 - 昨日
  yesterdaySActiveUsers: number
  // 用户使用数
  theNumberOfUsersUsedToday: number
  // 用户使用数 - 昨日
  theNumberOfUserUsesYesterday: number
}

/**
 * Agent应用信息
 * @description Agent应用基础信息，包含应用标识、名称、描述等信息
 */
type AIAgentAppType = {
  /** 自增主键ID */
  id: number;
  /** 
   * Agent应用模板标识
   * @description 用于关联外部Agent
   */
  template: string;
  /** 
   * 租户ID
   * @description 用于多租户系统隔离
   */
  tenantId: string;
  /** 
   * 是否删除
   * @default 0
   * @remarks 0-未删除 1-已删除
   */
  deleteTag: number;
  /** 创建用户ID */
  createUser: string;
  /** 
   * 创建时间
   * @remarks 时间戳，单位：毫秒
   */
  createTime: number;
  /** 更新用户ID */
  updateUser: string;
  /**
   * 更新时间
   * @remarks 时间戳，单位：毫秒
   */
  updateTime: number;
  /** Agent机器人ID */
  agentId: number;
  /** AI应用名称 */
  name: string;
  /** AI应用介绍 */
  description: string;
  // 提示词
  prompt?: string;
  // 欢迎语 
  welcomeMessage?: string;
  // 兜底默认回复
  defaultAnswer?: string;
  // 开场默认问题列表
  defaultQuestion?: string[];
  // 授权范围是否为全部员工：0-否 1-是
  allRange?: number;
  /** 授权范围 */
  ranges?: AIAgentAppAuthItem[]
  /** 是否系统预置 */
  /** 设置 */
  setting?: {
    /** 是否允许修改AI模型 */
    allowModifyAIModel?: boolean
    /** 是否开启公开访问 */
    publicAccess?: boolean
    /**
     *  允许知识库自动更新智能体训练数据
     *  true允许，false不允许
     */
    automaticUpdates?: boolean
  },
  variable: AIvariable[],
  // 是否自动学习
  isAutoLearn?: boolean
  /** 是否公开访问 */
  publicAccess?: boolean
  /** 是否开启公开访问 */
  open?: boolean
  /** 关联的智能体ID */
  agentIds?: number[] | string[]
}

type AIAgentAppComponentType = {
  disabled?: boolean
} & AIAgentAppType

/**
 * Agent机器人基础信息接口
 */
type AIAgentBaseType = {
  /** 主键ID */
  id: string | number;
  /** 租户ID */
  tenantId: string;
  /** Agent机器人名称 */
  name: string;
  /** Agent机器人描述 */
  description: string;
  /** 
   * 是否系统预置
   * @default 0
   * @remarks 0-否 1-是
   */
  system: boolean;
  /** Agent机器人图标URL */
  icon: string;
  /** 
   * 是否启用
   * @default 1
   * @remarks 1-启用 0-禁用
   */
  enable: number;
  /** 
   * 是否删除
   * @default 0
   * @remarks 0-未删除 1-已删除
   */
  deleteTag: number;
  /** 创建人ID */
  createUser: LoginUser;
  /** 
   * 创建时间
   * @remarks 时间戳，单位：毫秒
   */
  createTime: number;
  /** 最后更新人ID */
  updateUser: LoginUser;
  /** 
   * 最后更新时间
   * @remarks 时间戳，单位：毫秒
   */
  updateTime: number;
  /** 分享链接 */
  shareUrl: string;
  /** 
   * 机器人类型
   * @default 0
   * @remarks 0-聊天助手 1-工作流
   */
  type: number;
  /** 使用的模型名称 */
  model?: number;
  template?: string
  /** 是否需要联系客服 */
  needIm?: boolean
}

type AIAgentType = AIAgentBaseType & {
  updateUserEntity?: LoginUser;
  agentAppId?: number;
  aiAppList?: string[];
  agentId: number | string;
  /**
   * 智能体app详情
  */
  aiAgentAppDetail: AIAgentAppType;
  /**
   * 多智能体关联的智能体
  */
  unionAiAgent: AIAgentBaseType[];
}

type AIAgentDetailType = AIAgentBaseType & {
  aiAppList?: AIAgentAppType[];
}

type DingtalkAssistantInstallInfo = {
  /**
   * 自增主键
   */
  id: number;

  /**
   * 租户ID
   */
  tenantId: string;

  /**
   * 助理ID
   */
  assistantId: string;

  /**
   * 创建时间
   */
  createTime: Date;

  /**
   * 创建人
   */
  createUser: string;

  /**
   * 知识库文档链接
   */
  docUrl?: string;
}

type AIAgentAppAbilityItem = {
  // 主键ID
  id: number;
  // 数据名称
  name: string;
  // 数据类型：1-应用 2-知识库 3-本地文件 4-外部链接
  type: AIAgentAppAbilityTypeEnum;
  // 学习状态：0-未学习 1-学习中 2-已学习
  learningStatus: AIAgentAppAbilityLearningStatusEnum;
  // 文档链接
  url: string;
  // 创建用户ID
  createUser: LoginUser;
  // 创建时间
  createTime: number;
  // 更新时间
  updateTime: number;
  // 更新用户ID
  updateUser: LoginUser;
  // 租户ID
  tenantId: string;
  // 业务类型
  bizType: string;
  // 业务ID
  bizId: string;
  // Agent机器人应用ID
  agentAppId: number;
  // 是否删除：0-未删除 1-已删除
  deleteTag: AIAgentAppAbilityDeleteTagEnum;
  // 是否重新学习
  relearn: boolean;
}

type AIAgentAppAbilityBizAppItem = {
  id?: string;
  bizId?: string | null;
  bizType?: AiKnowledgeTypeEnum;
  name: string;
}

type AIAgentAppAuthItem = {
  // 自增主键
  id?: number;
  // 范围id
  rangeId: string;
  // 名称
  rangeName: string;
  // 范围类型(0:部门 1:角色 2：服务商 3:人员）
  rangeType: AIAgentAppAuthRangeTypeEnum
}

type AIvariable = {
  key: string;
  name: string;
  required: boolean; 
}

type AIAgentAppAuthAddParamType = {
  agentAppId: number;
  authList: AIAgentAppAuthItem[];
}

type AIAgentAppAuthRemoveParamType = {
  agentAppId: number;
  id: string;
}

type AIAgentAppAbilityBizDataItem = {
  bizType?: AiKnowledgeTypeEnum;
  bizId?: string | null;
  name: string;
  type: AIAgentAppAbilityTypeEnum;
}

type AiAgentLog = {
  /**
   * 自增主键
   */
  id: number;
  /**
   * 创建人
   */
  createUser: string;
  createUserVO: LoginUser;
  /**
   * 更新人用户
   */
  updateUser: string;
  updateUserVO: LoginUser;
  /**
   * 模型名称
   */
  modelName: string;
  /**
   * 调用模型输入的 tokens 数量
   */
  inputTokens: number;
  /**
   * 调用模型输出的 tokens 数量
   */
  outputTokens: number;
  /**
   * 调用模型总 tokens 数量
   */
  totalTokens: number;
  /**
   * 创建时间
   */
  createTime: Date;
  /**
   * 更新时间
   */
  updateTime: Date;
  /**
   * Agent 机器人 id
   */
  agentId: number;
  /**
   * Agent 机器人应用 id
   */
  agentAppId: number;
  /**
   * 用户的输入
   */
  question: string;
  /**
   * AI 的回复
   */
  answer: string;
  /**
   * 是否删除
   */
  deleteTag: boolean;
  /**
   * 是否失败:0-否 1-是
   */
  isError: boolean;
  /**
   * 错误信息
   */
  errorMessage: string;
  aiAgent: AIAgentType;
  aiAgentApp: AIAgentAppType;
  /**
   * 来源
   */
  source: AIAgentLogSourceEnum;
  newAnswer: string;
}

type AiAgentChatDataItem = {
  createTime: string
  count: number
}

type AiAgentChatData = {
  /**
    * 根据时间范围统计每日用户使用数量
   */
  countTheNumberOfUsersWhoUseTheData: AiAgentChatDataItem[]
  /**
    * 根据时间范围统计每日用户活跃数量
   */
  countDailyActiveUsers: AiAgentChatDataItem[]
}

type AiAgentAppVarItem = {
  key: string;
  name: string;
  required: boolean;
}

export { 
  AIAgentDataSummaryType,
  AIAgentAppType,
  AIAgentType,
  AIAgentDetailType,
  AIAgentAppComponentType,
  AIAgentAppRangeForm,
  AIAgentAppAbilityItem,
  AIAgentAppAbilityBizAppItem,
  AIAgentAppAuthItem,
  AIvariable,
  AIAgentAppAuthAddParamType,
  AIAgentAppAuthRemoveParamType,
  DingtalkAssistantInstallInfo,
  AIAgentAppAbilityBizDataItem,
  AiAgentLog,
  AiAgentChatData,
  AiAgentChatDataItem,
  AIAgentBaseType,
  AiAgentAppVarItem
}
