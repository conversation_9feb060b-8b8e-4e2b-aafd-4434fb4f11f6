/* vue */
import { defineComponent } from "vue";
/* components */
import SettingGPTViewDialog from "@gpt/components/dialog"
import WikiTreeDialogTree from "@src/modules/ai/components/wiki-tree-dialog/tree"
/* scss */
import "@src/modules/ai/components/wiki-tree-dialog/index.scss"

type SettingGPTViewDialogComponent = InstanceType<typeof SettingGPTViewDialog>
type WikiTreeDialogTreeComponent = InstanceType<typeof WikiTreeDialogTree>

enum WikiTreeDialogEventEnum {
  Confirm = 'confirm'
}

export default defineComponent({
  name: "WikiTreeDialog",
  props: {
    loading: {
      type: Boolean,
      default: false
    },
    onConfirm: {
      type: Function
    }
  },
  data() {
    return {
      value: [] as string[]
    }
  },
  computed: {
    wikiTreeDialog(): SettingGPTViewDialogComponent {
      return this.$refs.WikiTreeDialog as SettingGPTViewDialogComponent;
    },
    wikiTreeDialogTree(): WikiTreeDialogTreeComponent {
      return this.$refs.WikiTreeDialogTree as WikiTreeDialogTreeComponent;
    },
    confirmText() {
      return '确定应用'
    },
    attrs(): Record<string, any> {
      return {
        directives: [
          {
            name: 'loading',
            value: this.loading
          }
        ],
      }
    },
  },
  methods: {
    open() {
      this.wikiTreeDialogTree.clearDefaultCheckedKeys()
      this.wikiTreeDialog.open();
    },
    close() {
      this.wikiTreeDialog.close();
    },
    setValue(value: string[]) {
      this.value = value;
    },
    onConfirmHandler() {
      const checkedKeys = this.wikiTreeDialogTree?.getCheckedKeys()
      const checkedNodes = this.wikiTreeDialogTree?.getCheckedNodes(checkedKeys)
      this.$emit(WikiTreeDialogEventEnum.Confirm, checkedNodes)
    }
  },
  render() {
    return (
      <SettingGPTViewDialog
        width="700px"
        title="知识库设置"
        ref="WikiTreeDialog"
        className="wiki-tree-dialog"
        confirmText={this.confirmText}
        onConfirm={this.onConfirmHandler}
        {...this.attrs}
      >
        <div class="wiki-tree-dialog-content">
          <div class="wiki-tree-dialog-tip">
            请选择知识库范围
          </div>
          <div class="wiki-tree-dialog-tree-main">
            <WikiTreeDialogTree 
              ref="WikiTreeDialogTree"
              value={this.value}
            />
          </div>
        </div>
      </SettingGPTViewDialog>
    )
  }
})