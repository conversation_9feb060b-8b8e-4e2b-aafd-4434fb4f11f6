/* api */
import * as RepositoryApi from '@src/api/Repository'
/* vue */
import { defineComponent, CreateElement } from "vue"
/* util */
import { isFalsy } from "@src/util/type"
import { asyncErrorCaptured } from "@src/util/onerror"
import platform from "@src/platform"
import { t } from "@src/locales"
import { getRootWindow } from "pub-bbx-utils"

enum WikiNodeKeyEnum {
  DRAFT = 'draft',
  PUBLISHED = 'wiki',
  COLLECTION = 'collection',
  RECORD = 'record',
  UNPUBLISHED = 'unpublished',
  FAULTLIBRARY = 'faultLibrary'
}

export type TreeItem = { id: string, name: string, type: number, count?: number, subTypes?: TreeItem[] }
export type TreeData = TreeItem[]

export default defineComponent({
  name: "WikiTreeDialogTree",
  props: {
    value: {
      type: Array as () => string[],
      default: () => []
    }
  },
  data() {
    return {
      expandedTreeList: [] as string[],
      nodeLabelEllipsis: true,
      treeData: [] as TreeData,
      defaultCheckedKeys: [] as string[],
      loading: false
    }
  },
  computed: {
    treeProps() {
      return {
        label: 'name',
        children: 'subTypes'
      }
    },
    // 故障库灰度
    isFaultGrayscale(): boolean {
      const RootWindow = getRootWindow(window)
      return Boolean(RootWindow?.grayAuth?.faultLibrary)
    },
  },
  watch: {
    value: {
      handler(value: string[]) {
        this.defaultCheckedKeys = value
      },
      immediate: true
    }
  },
  mounted() {
    this.initialize()
  },
  methods: {
    getCheckedKeys() {
      const treeComponent = this.$refs.tree as Record<string, any>
      const keys = treeComponent?.getCheckedKeys() || []
      return this.findTopLevelIds(this.treeData, keys)
    },
    getCheckedNodes(checkedKeys: string[]) {
      return this.findNodesByKeys(this.treeData, checkedKeys)
    },
    getCheckedNodesNew() {
      const treeComponent = this.$refs.tree as Record<string, any>
      const nodes = treeComponent?.getCheckedNodes() || []
      return nodes
    },
    /**
     * 查找多个节点
     * @param tree 树结构数据
     * @param keys 要查找的 keys 数组
     * @returns 找到的节点数组
     */
    findNodesByKeys(tree: TreeItem[], keys: string[]): TreeItem[] {
      // 用 Set 提高查找效率
      const keySet = new Set(keys);
      const result: TreeItem[] = [];
      
      // 广度优先搜索
      const queue: TreeItem[] = [...tree];
      
      while (queue.length > 0) {
        const node = queue.shift()!;
        
        if (keySet.has(node.id)) {
          result.push(node);
          // 如果找齐了所有节点，就提前结束
          if (result.length === keys.length) {
            break;
          }
        }
        
        if (node.subTypes?.length) {
          queue.push(...node.subTypes);
        }
      }
      
      return result;
    },
    /**
     * @description 初始化
    */
    async initialize() {
      try {
        
        this.loading = true
        
        await this.fetchWikiTypes()
        await this.fetchCount()
        
        // 设置默认展开的节点
        this.setDefaultExpandedKeys()
        
      } catch (error) {
        console.error('initialize error:', error)
      } finally {
        this.loading = false
      }
    },
    /**
     * 从平铺的 ids 中找出最上级的 ids
     * @param tree 树结构数据
     * @param ids 平铺的 id 列表
     * @returns 最上级的 id 列表
     */
    findTopLevelIds(tree: TreeItem[], ids: string[]): string[] {
      // 构建一个 map 存储所有节点的父子关系
      const parentChildMap = new Map<string, Set<string>>();
      
      // 递归遍历树，构建父子关系映射
      function buildParentChildMap(nodes: TreeItem[], parentId?: string) {
        nodes.forEach(node => {
          if (parentId) {
            // 如果当前节点有父节点
            if (!parentChildMap.has(parentId)) {
              parentChildMap.set(parentId, new Set());
            }
            parentChildMap.get(parentId)!.add(node.id);
          }
          
          // 递归处理子节点
          if (node.subTypes && node.subTypes.length > 0) {
            buildParentChildMap(node.subTypes, node.id);
          }
        });
      }

      // 检查一个节点是否是另一个节点的子孙节点
      function isDescendant(nodeId: string, potentialAncestorId: string): boolean {
        const children = parentChildMap.get(potentialAncestorId);
        if (!children) return false;
        
        if (children.has(nodeId)) return true;
        
        // 递归检查更深层级
        return Array.from(children).some(childId => isDescendant(nodeId, childId));
      }

      // 构建父子关系映射
      buildParentChildMap(tree);

      // 过滤出最上级的 ids
      return ids.filter(id => {
        // 检查当前 id 是否是其他任何 id 的子孙节点
        return !ids.some(otherId => {
          if (otherId === id) return false;
          return isDescendant(id, otherId);
        });
      });
    },
    /**
     * @description 获取知识空间分类二级树状结构
     */
    async fetchWikiTypes() {
      
      let [error, result] = await asyncErrorCaptured(RepositoryApi.getDocumentTypes)
      
      if (error) {
        console.error('fetchWikiTypes error:', error)
        return
      }
      
      const isSuccess = Boolean(result?.success)
      const message = result?.message || '获取知识空间分类失败'
      if (isFalsy(isSuccess)) {
        platform.notificationError({ 
          title: message 
        })
        return
      }
      
      const data = result?.result || []
      this.treeData = data
      
    },
    async fetchFaultTypes() {
      
      if (isFalsy(this.isFaultGrayscale)) {
        console.log('故障库灰度未开启')
        return
      }
      
      const [error, result] = await asyncErrorCaptured(RepositoryApi.getDocumentTypes, [{
        type: 1
      }])
      
      if (error) {
        console.error('fetchFaultTypes error:', error)
        return
      }
      
      const isSuccess = Boolean(result?.success)
      if (isFalsy(isSuccess)) {
        return
      }
      
      const data = result?.result || []
      this.treeData.push({
        id: WikiNodeKeyEnum.FAULTLIBRARY,
        name: t('wiki.list.treeData.label4'),
        subTypes: data,
        type: 1
      })
      
    },
    /**
     * @description 获取知识空间分类数量
     */
    async fetchCount() {
      
      let [error, result] = await asyncErrorCaptured(RepositoryApi.getDocumentViewCount)
      
      if (error) {
        console.error('fetchCount error:', error)
        return
      }
      
      const isSuccess = Boolean(result?.success)
      if (isFalsy(isSuccess)) {
        return
      }
      
      const data = result?.result || {} as Record<string, number>
      
      this.treeData.forEach(item => {
        
        if (item.id == WikiNodeKeyEnum.PUBLISHED) {
          this.$set(item, 'count', data.published || 0)
        }
        
        if (item.id == WikiNodeKeyEnum.FAULTLIBRARY) {
          this.$set(item, 'count', data.faultLibrary || 0)
        }
        
      })
      
    },
    clearDefaultCheckedKeys() {
      this.defaultCheckedKeys = []
      // @ts-ignore
      this.$refs.tree.setCheckedKeys([]);
    },
    setDefaultExpandedKeys() {
      this.expandedTreeList = this.treeData.map(item => item.id)
    },
    onCurrentChangeHandler() {
    },
    onShowNodeLabelTipsHandler(event: Event) {
      const target = event.target as HTMLElement
      this.nodeLabelEllipsis = target.offsetWidth > 500
    },
    renderNodeIcon(node: { key: string }) {
      
      const { key } = node
      
      if (key === WikiNodeKeyEnum.DRAFT) {
        return <i class="iconfont icon-caogaoxiang" />
      }
      if (key === WikiNodeKeyEnum.PUBLISHED) {
        return <i class="iconfont icon-yifabu" />
      }
      if (key === WikiNodeKeyEnum.COLLECTION) {
        return <i class="iconfont icon-shoucang" />
      }
      if (key === WikiNodeKeyEnum.RECORD) {
        return <i class="iconfont icon-zuijinfangwen" />
      }
      if (key === WikiNodeKeyEnum.UNPUBLISHED) {
        return <i class="iconfont icon-weifabu" />
      }
      
      return <i class="iconfont icon-fenlei1" />
      
    },
    renderNodeLabel(node: { key: string, label: string }, data: { count: number }) {
      
      const count = (data.count || data.count === 0) ? `（${data.count}）` : ''
      const content = `${node.label}${count}`
      const isDisabled = isFalsy(this.nodeLabelEllipsis)
      
      return (
        <el-tooltip 
          class="item" 
          effect="dark" 
          placement="top-start" 
          disabled={isDisabled}
          content={content} 
        >
          <span 
            class="wiki-tree-node-label" 
            onMouseover={this.onShowNodeLabelTipsHandler}
          >
            { content }
          </span>
        </el-tooltip>
      )
      
    },
    renderTreeContent({ node, data }: Record<string, any>) {
      return (
        <span class="wiki-tree-node">
          <span class="wiki-tree-node-block">
            { this.renderNodeIcon(node) }
            { this.renderNodeLabel(node, data) }
          </span>
        </span>
      )
    }
  },
  render(h: CreateElement) {
    
    const attrs = {
      props: {
        props: this.treeProps,
      },
      directives: [
        {
          name: 'loading',
          value: this.loading
        }
      ]
    }
  
    return (
      <el-tree
        {...attrs}
        ref="tree"
        nodeKey="id"
        show-checkbox
        highlightCurrent
        checkOnClickNode
        data={this.treeData}
        defaultExpandedKeys={this.expandedTreeList}
        defaultCheckedKeys={this.defaultCheckedKeys}
        onCurrentChange={this.onCurrentChangeHandler}
        expandOnClickNode={false}
        scopedSlots={{
          default: this.renderTreeContent
        }}
      >
      </el-tree>
    )
  }
})
