
/* vue */
import { defineComponent, PropType } from "vue"
/* types */
import { AIAgentTypeEnum, AiModelCNNameEnum, AiModelEnum, AiModelIconEnum } from "@src/modules/ai/model/enum"
/* scss */
import '@src/modules/ai/components/type-tag.scss'
/* util */
import { isNull, isUndefined } from "pub-bbx-utils"
/* enum */
import ComponentNameEnum from "@model/enum/ComponentNameEnum"
/* scss */
import '@src/modules/ai/components/model-item.scss'

export default defineComponent({
  name: ComponentNameEnum.AiAgentModelItem,
  props: {
    value: {
      type: Number as PropType<AiModelEnum | undefined>,
      required: true
    },
    nowModel: {
      type: Number as PropType<AiModelEnum | undefined>
    },
  },
  computed: {
    modelCNName(): string {
      return AiModelCNNameEnum[this.value as AiModelEnum] || ''
    },
    modelIcon(): string {
      return AiModelIconEnum[this.value as AiModelEnum] || ''
    },
    isDeepSeek(): boolean {
      return this.value === AiModelEnum.DEEPSEEK
    },
    iconClass() {
      return {
        'ai-agent-model-item-model-icon': true,
        'ai-agent-model-item-model-icon-deepseek': this.isDeepSeek
      }
    }
  },
  methods: {
    renderModelWrapper() {
      if (isUndefined(this.value)) {
        return null
      }
      return this.renderModel()
    },
    renderModel() {
      return (
        <div class="ai-agent-model-item-model">
          <img
            class={this.iconClass}
            src={this.modelIcon} 
            alt={this.modelCNName} 
          />
          <span class={["ai-agent-model-item-model-name", this.nowModel == this.value ? 'color-primary' : '']}>
            {this.modelCNName}{this.nowModel == this.value ? <i class="iconfont icon-check color-primary mar-l-8"></i> : ''}
          </span>
        </div>
      )
    }
  },
  render() {
    return (
      <div class="ai-agent-model-item">
        {this.renderModelWrapper()}
      </div>
    )
  }
})