import MarkdownIt from 'markdown-it';

export const MarkdownClassName = 'markdown-body';

/**
 * 渲染带有边框表格的 Markdown 文本
 * @param markdownText 要渲染的 Markdown 文本
 * @returns 渲染后的 HTML 字符串
 */
export function renderMarkdown(markdownText: string) {
  
  const md = new MarkdownIt({
    html: true,
  });

  md.renderer.rules.think_open = function (tokens, idx, options, env, self) {
    console.log('think_open', tokens, idx, options, env, self)
    return '<div class="think-open">';
  };

  md.renderer.rules.think_close = function (tokens, idx, options, env, self) {
    console.log('think_close', tokens, idx, options, env, self)
    return '</div>';
  };
  
  // 修改表格渲染规则，添加边框样式和文字居中样式
  md.renderer.rules.table_open = function (tokens, idx, options, env, self) {
    return '<table style="border-collapse: collapse;">';
  };
  
  md.renderer.rules.table_close = function (tokens, idx, options, env, self) {
    return '</table>';
  };
  
  md.renderer.rules.td_open = function (tokens, idx, options, env, self) {
    return '<td>';
  };
  
  md.renderer.rules.th_open = function (tokens, idx, options, env, self) {
    return '<th>';
  };
  
  // 修改引用块的渲染规则
  md.renderer.rules.blockquote_open = function () {
    return '<blockquote style="color: #666; border-left: 4px solid #ddd; padding-left: 1em; margin: 1em 0;">';
  };

  md.renderer.rules.blockquote_close = function () {
    return '</blockquote>';
  };

  return md.render(markdownText);

}