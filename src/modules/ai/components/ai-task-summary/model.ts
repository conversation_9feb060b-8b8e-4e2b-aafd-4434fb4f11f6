// 导入需要的类型
type GetChatSummaryFieldsModel = {
  formType: string;
  fieldName: string;
  displayName: string;
}

// 聊天摘要模型
export type ChatSummaryModel = {
  fields: string[];
  summaryOptions?: string[];
  tasks: Array<{[key: string]: string}>;
}

// 定义API返回结果类型
type GenerateSummaryResult = {
  text: string;
}

enum TaskSummaryTabEnum {
  SUMMARY = 'summary',
  ORIGINAL = 'original'
}

enum AITaskSummaryEventEnum {
  SUMMARY_RESULT = 'summaryResult'
}

// 字段标签接口
export interface FieldTag {
  label: string; // 显示名称
  value: string; // 字段值
  selected: boolean; // 是否选中
}

// 摘要选项接口
export interface SummaryOption {
  label: string; // 显示名称
  value: string; // 选项值
}

export {
  TaskSummaryTabEnum,
  AITaskSummaryEventEnum,
  GetChatSummaryFieldsModel,
  GenerateSummaryResult
}