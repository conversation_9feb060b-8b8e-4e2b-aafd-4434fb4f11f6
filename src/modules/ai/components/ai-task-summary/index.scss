// 颜色变量定义
$ai-primary-color: #6F47FF; // 主要紫色
$ai-primary-light-color: rgba(111, 71, 255, 0.08); // 浅紫色背景
$ai-primary-lighter-color: rgba(111, 71, 255, 0.08); // 更浅紫色（标签选中背景）
$ai-primary-border-color: rgba(111, 71, 255, 0.08); // 紫色边框
$ai-background-color: #f5f5f5; // 背景色
$ai-text-color: #666; // 文本颜色
$ai-text-dark-color: #262626; // 深色文本
$ai-text-light-color: #999; // 浅色文本
$ai-border-color: #f0f0f0; // 边框颜色
$ai-white: #fff; // 白色

.ai-task-summary {
  width: 100%;
  height: 100%;
  padding: 24px;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  background-color: $ai-background-color;
  border-radius: 8px;
}

.ai-task-summary-main {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.ai-task-summary-header {
  background-color: $ai-primary-light-color;
  padding: 12px 16px;
  border-radius: 4px;
}

.ai-task-summary-progress-tip {
  font-size: 14px;
  color: $ai-text-color;
}

.ai-task-summary-progress-tip,
.ai-task-summary-progress-count {
  color: $ai-primary-color;
}

.ai-task-summary-content {
  display: flex;
  flex: 1;
  gap: 24px;
  overflow: hidden;
}

.ai-task-summary-left-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.ai-task-summary-right-panel {
  flex: 1;
  background-color: $ai-white;
  border-radius: 24px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid $ai-border-color;
}

.ai-task-summary-result-title {
  padding: 12px 16px;
  font-size: 16px;
  font-weight: bold;
}

.ai-task-summary-result-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  padding-top: 0;
}

.ai-task-summary-requirement,
.ai-task-summary-field-select,
.ai-task-summary-options {
  border-radius: 8px;
  padding: 16px 16px 16px 0;
}

.ai-task-summary-requirement-title,
.ai-task-summary-field-title,
.ai-task-summary-options-title {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 12px;
  color: $ai-text-dark-color;
}

.ai-task-summary-requirement {
  textarea {
    max-height: 90px;
    &:hover {
      border-color: $ai-primary-color;
    }
    &:focus {
      border-color: $ai-primary-color;
    }
  }
}

.ai-task-summary-field-tags,
.ai-task-summary-options-tags {
  display: flex;
  flex-wrap: wrap;
}

.ai-task-summary-field-tags {
  max-height: 180px;
  overflow-y: auto;
  padding-top: 6px;
}

.ai-task-summary-field-select-add-tag {
  cursor: pointer;
}

.ai-task-summary-field-select-add-button {
  display: flex;
}

.ai-task-summary-options-tags,
.ai-task-summary-field-select {
  .el-tag {
    height: 28px;
    border-color: transparent;
    border-radius: 16px;
    overflow: unset;
    line-height: normal;
    padding-left: 8px;
    padding-right: 8px;
  }
}

.ai-task-summary-generate {
  display: flex;
  .el-button {
    height: 40px;
    padding: 0 20px;
    background-color: $ai-primary-color !important;
    border-color: $ai-primary-color !important;
    &:hover, &:focus {
      background-color: lighten($ai-primary-color, 5%) !important;
      border-color: lighten($ai-primary-color, 5%) !important;
    }
    &:active {
      background-color: darken($ai-primary-color, 5%) !important;
      border-color: darken($ai-primary-color, 5%) !important;
    }
    i {
      margin-left: 8px;
    }
  }
}

.ai-task-summary-generate-button {
  padding-left: 20px;
  padding-right: 20px;
  border-radius: 20px;
}

.ai-task-summary-empty-result {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 1px solid $ai-border-color;
  border-radius: 16px;
}

.ai-task-summary-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  color: $ai-text-light-color;
}

.ai-task-summary-brand {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  padding: 16px;
  width: 100%;
  height: 100%;
  
  img {
    width: 160px;
    height: 160px;
    opacity: 0.2;
  }
  
  div {
    color: $ai-text-light-color;
    font-size: 18px;
  }

  .ai-task-summary-brand-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    gap: 8px;
    width: 100%;
    height: 100%;
    flex-flow: column;
  }

  .ai-task-summary-brand-logo-text {
    font-size: 24px;
    font-weight: bold;
  }
  
  .ai-task-summary-brand-copyright {
    font-size: 12px;
    margin-top: 16px;
  }
}

.ai-task-summary-tab-wrapper {
  width: 100%;
  margin-bottom: 16px;
}

.ai-task-summary-transcript {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 24px;
  box-sizing: border-box;
}

.ai-task-summary-transcript-loading {
  font-size: 14px;
  color: $ai-text-color;
}

.ai-task-summary-ai-icon {
  width: 16px;
  height: 16px;
  vertical-align: text-bottom;
}

.ai-task-summary-original-data {
  width: 100%;
  padding: 8px;
  box-sizing: border-box;
}

/* Element UI 标签样式覆盖 */
.ai-tag-item {
  background-color: $ai-background-color;
  border-color: $ai-background-color;
  color: $ai-text-color;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 8px;
  margin-bottom: 8px;
  position: relative;
  
  &:hover {
    background-color: darken($ai-background-color, 3%);
    
    .el-tag__close {
      display: inline-block;
    }
  }
  
  &.el-tag--primary {
    background-color: $ai-primary-lighter-color !important;
    border-color: $ai-primary-border-color !important;
    color: $ai-primary-color !important;
    
    &:hover {
      background-color: darken($ai-primary-lighter-color, 3%) !important;
    }
    
    .el-tag__close {
      background-color: transparent;
      background-color: #595959;
      color: #fff;
    }
  }
  
  .el-tag__close {
    display: none;
    font-size: 12px;
    height: 16px;
    width: 16px;
    line-height: 16px;
    text-align: center;
    border-radius: 50%;
    top: -5px;
    right: -5px;
    position: absolute;
    background-color: #595959;
    color: #fff !important;
    &:hover {
      background-color: $ai-primary-color !important;
    }
  }
}

.ai-task-summary-options-tags {
  .el-tag__close {
    display: none !important;
  }
}

/* Element UI 按钮样式覆盖 */
.ai-task-summary {
  .el-button--primary {
    background-color: $ai-primary-color;
    border-color: $ai-primary-color;
    
    &:hover, &:focus {
      background-color: lighten($ai-primary-color, 5%);
      border-color: lighten($ai-primary-color, 5%);
    }
    
    &:active {
      background-color: darken($ai-primary-color, 5%);
      border-color: darken($ai-primary-color, 5%);
    }
  }
  
  .el-textarea__inner:focus {
    border-color: $ai-primary-color;
  }
  
  .el-input__inner:focus {
    border-color: $ai-primary-color;
  }
} 

.ai-task-summary-dialog {
  .base-modal-header {
    display: none;
  }
  .base-modal-body {
    padding: 0;
  }
}

.ai-task-summary-result-title {
  text-align: center;
}

.ai-task-summary-field-select-add-tag {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: $ai-primary-lighter-color;
  color: $ai-primary-color;
  i {
    margin-right: 6px;
  }
}

.ai-task-summary-field-list-cascader {

  border: none !important;

  i {
    color: $ai-primary-color;
  }

  .el-cascader-node.in-active-path,
  .el-cascader-node.is-active {
    .el-cascader-node__label {
      color: $ai-primary-color;
    }
  }

  .el-checkbox__inner:hover {
    border-color: $ai-primary-color;
  }

  .el-checkbox__input.is-indeterminate,
  .el-checkbox.is-checked {
    .el-checkbox__inner {
      border-color: $ai-primary-color !important;
      background-color: $ai-primary-color !important;
      &:hover {
        border-color: $ai-primary-color !important;
        background-color: $ai-primary-color !important;
      }
    }
  }

}

.ai-summary-content {
  overflow-y: auto;
}

.ai-task-summary-content-thinking {
  display: flex;
  align-items: center;
  gap: 6px;
  height: 20px;
  line-height: 20px;
  margin-bottom: 6px;
  color: #595959;
  img {
    width: 20px;
    height: 20px;
  }
}

.ai-summary-content {
  think {
    color: #8c8c8c;
    border-left: 4px solid #ddd;
    padding-left: 10px;
    height: 100%;
    display: block;
    margin-bottom: 12px;
    p {
      margin-top: 12px;
    }
  }
  img {
    width: 100%;
  }
  ol {
    padding-left: 26px;
    list-style-type: decimal !important;
  }
  ul {
    padding-left: 16px;
    list-style-type: decimal !important;
  }
  li::marker {
    color: #262626;
    font-weight: bold;
    content: "· "; /* 自定义标记内容 */
  }
  li li {
    &::marker {
      font-weight: bold;
      content: "· "; /* 自定义标记内容 */
    }
  }
  ol > li::marker {
    content: counter(list-item) ". ";
  }
}

.ai-summary-result-wrapper {
  border: 1px solid #f0f0f0;
  border-radius: 16px;
  height: 100%;
  overflow-y: auto;
  padding: 12px;
  background-color: #f9f9f9;
  max-height: 530px;
  /* 滚动条轨道 */
  &::-webkit-scrollbar-track {
    background-color: transparent;
  }
}

.ai-summary-result-wrapper-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 12px;
  background-color: #f9f9f9;
  gap: 12px;
  i {
    margin-right: 6px;
  }
  .el-button--text {
    color: #595959 !important;
    &:hover {
      color: $ai-primary-color !important;
    }
  }
}

.ai-task-summary-dialog-content-wrapper {
  min-height: 560px;
  max-height: 700px;
}

.ai-task-summary-dialog-content {
  height: 100%;
}

.ai-task-summary-empty-result,
.ai-summary-result-wrapper {
  background-color: #fafafa;
}

.ai-task-summary-field-select {
  max-height: 236px;
}

.ai-task-summary-options-sub-title {
  font-size: 12px;
  color: #8C8C8C;
  margin-bottom: 8px;
}

.ai-task-summary-requirement {
  padding-bottom: 0;
}

.ai-task-summary-requirement,
.ai-task-summary-field-select,
.ai-task-summary-options {
  padding-right: 0;
}

.ai-task-summary-right-panel {
  max-width: 400px;
}

.ai-task-summary-dialog-close-button {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 16px;
  width: 16px;
  cursor: pointer;
  i {
    font-size: 14px;
  }
}

.ai-task-summary-dialog-content-wrapper {
  display: flex;
  flex-flow: column;
  .ai-task-summary-dialog-content {
    flex: 1;
  }
}

.ai-task-summary-dialog-content {
  display: flex;
  flex-flow: column;
  .ai-task-summary-input-section,
  .ai-task-summary-main,
  .ai-task-summary {
    flex: 1;
  }
}

.ai-task-summary-input-section {
  display: flex;
  flex-flow: column;
  .ai-task-summary-field-select {
    flex: 1;
  }
}

.ai-task-summary-field-list-search-input {
  input {
    &:hover {
      border-color: $ai-primary-color !important;
    }
    &:focus {
      border-color: $ai-primary-color !important;
    }
    &:active {
      border-color: $ai-primary-color !important;
    }
  }
}

.ai-task-summary-field-list-cascader-checkbox-group {
  
  display: flex;
  flex-flow: column;
  height: 280px;
  overflow-y: auto;
  padding-top: 6px;

  .el-checkbox {
    height: 34px;
    min-height: 34px;
    display: flex;
    align-items: center;
  }

  .el-checkbox.is-checked {
    .el-checkbox__label {
      color: $ai-primary-color;
    }
  }
  
  .el-checkbox__inner:hover {
    border-color: $ai-primary-color;
  }

  .el-checkbox__input.is-indeterminate,
  .el-checkbox.is-checked {
    .el-checkbox__inner {
      border-color: $ai-primary-color !important;
      background-color: $ai-primary-color !important;
      &:hover {
        border-color: $ai-primary-color !important;
        background-color: $ai-primary-color !important;
      }
    }
  }

}