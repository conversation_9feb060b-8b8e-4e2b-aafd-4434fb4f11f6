/* components */
import SettingGPTViewDialog from '@gpt/components/dialog'
import AITaskSummaryContent from '@src/modules/ai/components/ai-task-summary/content'
/* vue */
import { defineComponent, PropType } from "vue"
/* scss */
import "@src/modules/ai/components/ai-task-summary/index.scss"
/* model */
import { GetChatSummaryFieldsModel } from "@src/modules/ai/components/ai-task-summary/model"
import Field from '@model/entity/Field'
/* hooks */
import { useDialog } from '@hooks/useDialog'
/* enum */
import { AIAgentTemplateEnum } from '@src/modules/ai/model/enum'

type SettingGPTViewDialogComponent = InstanceType<typeof SettingGPTViewDialog>

enum AiAgentUpgradeV2EventEnum {
  CLOSE = 'close',
  CONFIRM = 'confirm'
}

export default defineComponent({
  name: "AiTaskSummaryDialog",
  props: {
    taskTypesMap: {
      type: Object as PropType<Record<string, any>>,
      default: () => {}
    },
    taskList: {
      type: Array as PropType<Record<string, any>[]>,
      default: () => []
    },
    fields: {
      type: Array as PropType<Field[]>,
      default: () => []
    },
    template: {
      type: String as PropType<AIAgentTemplateEnum>,
      default: ''
    },
    onSummaryResult: {
      type: Function as PropType<(result: string) => void>,
      default: () => {}
    },
    onClose: {
      type: Function as PropType<() => void>
    },
    onConfirm: {
      type: Function as PropType<(text: string) => void>
    }
  },
  setup() {
    const { visible } = useDialog()
    return {
      visible
    }
  },
  data() {
    return {
      selectedFieldTags: [] as string[],
      selectedSummaryOptions: [] as string[]
    }
  },
  computed: {
    isTaskTemplate(): boolean {
      return this.template === AIAgentTemplateEnum.Task
    },
    dialogComponent(): SettingGPTViewDialogComponent {
      return this.$refs.SettingGPTViewDialog as SettingGPTViewDialogComponent
    }
  },
  methods: {
    openDialog() {
      this.visible = true
      this.dialogComponent.open()
    },
    closeDialog() {
      this.visible = false
      this.dialogComponent.close()
      this.handleClose()
    },
    handleClose() {
      this.visible = false
      this.$emit(AiAgentUpgradeV2EventEnum.CLOSE)
    },
    handleConfirm() {
      this.$emit(AiAgentUpgradeV2EventEnum.CONFIRM)
    },
    renderContentWrapper() {

      if (!this.visible) {
        return null
      }

      return (
        <div class="ai-task-summary-dialog-content-wrapper">
          {this.renderContent()}
        </div>
      )
    },
    renderContent() {
      return (
        <div class="ai-task-summary-dialog-content">
          <AITaskSummaryContent 
            ref="AITaskSummaryContent"
            template={this.template}
            taskList={this.taskList}
            fields={this.fields}
            taskTypesMap={this.taskTypesMap}
          />
        </div>
      )
    },
    // 点击字段标签切换选中状态
    handleFieldTagClick(tagValue: string) {
      if (this.selectedFieldTags.includes(tagValue)) {
        const index = this.selectedFieldTags.indexOf(tagValue)
        if (index !== -1) {
          this.selectedFieldTags.splice(index, 1)
        }
      } else {
        this.selectedFieldTags.push(tagValue)
      }
    },
    // 关闭字段标签
    handleFieldTagClose(tagValue: string) {
      const index = this.selectedFieldTags.indexOf(tagValue)
      if (index !== -1) {
        this.selectedFieldTags.splice(index, 1)
      }
    },
    // 关闭摘要选项标签
    handleSummaryOptionClose(optionValue: string) {
      const index = this.selectedSummaryOptions.indexOf(optionValue)
      if (index !== -1) {
        this.selectedSummaryOptions.splice(index, 1)
      }
    },
    renderCloseButton() {
      return (
        <div 
          class="ai-task-summary-dialog-close-button"
          onClick={this.closeDialog}
        >
          <i class="iconfont icon-fe-close"></i>
        </div>
      )
    }
  },
  render() {
    return (
      <SettingGPTViewDialog
        ref="SettingGPTViewDialog"
        width="990px"
        class="ai-task-summary-dialog"
        showFooter={false}
        onClose={this.handleClose}
      >
        {this.renderContentWrapper()}
        {this.renderCloseButton()}
      </SettingGPTViewDialog>
    )
  }
}) 