/* components */
// @ts-ignore
import VueMarkdown from 'vue-markdown'
import BizSelectColumn from '@src/component/business/BizSelectColumn/BizSelectColumn'
/* vue */
import Vue, { defineComponent, PropType } from "vue"
/* scss */
import "@src/modules/ai/components/ai-task-summary/index.scss"
import 'shb-ai-chat-md/index.scss'
/* utils */
import { formatDate, isEmpty, isFalsy, sleep } from "pub-bbx-utils"
import { getLocalesOssUrl } from "@src/util/assets"
/* model */
import MsgModel from "@model/MsgModel"
import {
  TaskSummaryTabEnum,
  FieldTag,
  SummaryOption
} from "@src/modules/ai/components/ai-task-summary/model"
import Field from '@model/entity/Field'
import { cloneDeep } from 'lodash'
import { AIAgentTemplateEnum } from '@src/modules/ai/model/enum'
import { AIVocAgentSummaryAbstractParamType, GetAbstractFieldsParamType, GetAbstractTemplateListParamType } from '@src/modules/ai/model/param'
import { AiSummaryFieldsType, AiSummaryTemplateType } from '@src/modules/ai/types/summary'
/* api */
import { getAllFields } from '@src/api/TaskApi'
/* image */
// @ts-ignore
import aiAnalyzeLogo from '@src/assets/img/ai_analyze_logo.gif'
/* util */
import { userCenterGetTokenInfo } from '@src/util/userCenter'
import { renderMarkdown } from '@src/modules/ai/components/ai-task-summary/util'
import { copyText } from '@src/util/dom'
import { isJSONObject, parse_with_default_value } from '@src/util/lang/object'
import { getAbstractFields, getAbstractTemplateList } from '@src/api/AIv2API'

export default defineComponent({
  name: 'AITaskSummaryContent',
  components: {
    VueMarkdown
  },
  props: {
    taskList: {
      type: Array as PropType<Record<string, any>[]>,
      default: () => []
    },
    template: {
      type: String as PropType<AIAgentTemplateEnum>,
      default: ''
    },
    fields: {
      type: Array as PropType<Field[]>,
      default: () => []
    },
    taskTypesMap: {
      type: Object as PropType<Record<string, any>>,
      default: () => {}
    }
  },
  data() {
    return {
      activeTab: TaskSummaryTabEnum.SUMMARY,
      loading: false,
      aiIconUrl: getLocalesOssUrl('/ai/ai_icon.png'),
      summary: '',
      originalData: [] as Record<string, any>[],
      // 用户选择的字段标签
      selectedFieldTags: [] as FieldTag[],
      // 用户输入的需求
      userRequirement: '',
      // 用户选择的摘要选项
      selectedSummaryOptions: [] as SummaryOption[],
      // 字段列表
      fieldList: [] as Field[],
      // 选择列实例
      bizSelectColumnComponentInstance: new Vue(BizSelectColumn as Record<string, any>),
      columnTree: {} as Record<string, any>,
      fieldOptions: [] as Record<string, any>[],
      taskTypeFieldsMap: {} as Record<string, Field[]>,
      // 摘要模板列表
      summaryTemplateList: [] as AiSummaryTemplateType[],
      fieldListSearchInputValue: ''
    }
  },
  computed: {
    completedCount(): number {
      return this.taskList.length
    },
    isEmptySummary(): boolean {
      return isEmpty(this.summary)
    },
    isEmptyOriginal(): boolean {
      return isEmpty(this.originalData)
    },
    isTaskTemplate(): boolean {
      return this.template === AIAgentTemplateEnum.Task
    },
    isImTemplate(): boolean {
      return this.template === AIAgentTemplateEnum.IM
    },
    isVocTemplate(): boolean {
      return this.template === AIAgentTemplateEnum.VOC
    },
    // 可用的字段标签
    availableFieldTags(): FieldTag[] {
      return this.selectedFieldTags
    },
    // 可用的摘要选项
    availableSummaryOptions(): SummaryOption[] {
      return this.summaryTemplateList.map(template => {
        return {
          label: template.templateName,
          value: String(template.id)
        }
      })
    },
    // 摘要模板id映射
    summaryTemplateIdMap(): Record<string, AiSummaryTemplateType> {
      return this.summaryTemplateList.reduce((acc, template) => {
        acc[template.id] = template
        return acc
      }, {} as Record<string, AiSummaryTemplateType>)
    },
    contentHTML(): string {
      try {
        return renderMarkdown(this.summary || '')
      } catch (error) {
        return this.summary || ''
      }
    },
    fieldOptionsValueFieldMap(): Record<string, any> {
      return this.fieldOptions.reduce((acc, option) => {

        const children = (option?.children || []) as Record<string, any>[]
        const fieldNameMap = children.reduce((childMap, child) => {
          childMap[child.value] = child
          return childMap
        }, {} as Record<string, any>)

        acc[option.value] = fieldNameMap

        return acc
      }, {} as Record<string, any>)
    },
    fieldFilterOptions() {
      return this.fieldOptions.filter(option => {
        return option.label.includes(this.fieldListSearchInputValue)
      })
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    handleFieldListSearchInputChange(value: string) {
      this.fieldListSearchInputValue = value
    },
    handleFieldSelectAddButtonClick() {
      this.fieldListSearchInputValue = ''
    },
    handlerMainClick(event: MouseEvent) {
      if (event.target instanceof HTMLElement) {
        const target = event.target
        if (target.closest('.ai-task-summary-field-select-add-tag')) {
          return
        }
        this.hideFieldSelectAddPopover()
      }
    },
    hideFieldSelectAddPopover() {
      const popover = this.$refs.FieldSelectAddPopover as Record<string, any>
      popover?.doClose()
    },
    async init() {

      await this.initSummaryFields()

      // 初始化摘要模板列表
      await this.initSummaryTemplateList()

      // 如果是工单，则初始化工单
      if (this.isTaskTemplate) {
        await this.initTask()
        return
      }
      
      this.initSelectedFieldTags()

      this.initSelectedDefaultSystemFields()

    },
    async initSummaryFields() {

      try {

        let bizType = ''
        let bizTypeId = ''

        if (this.isTaskTemplate) {
          
          bizType = 'TASK'
          
          // taskList 所有的 templateId 是否都相同
          const isSameTemplate = this.taskList.every(task => task.templateId == this.taskList?.[0]?.templateId)
          if (isSameTemplate) {
            bizTypeId = this.taskList[0].templateId
          }

        } 
        else if (this.isImTemplate) {
          bizType = 'IM'
          bizTypeId = 'IM'
        } 
        else if (this.isVocTemplate) {
          bizType = 'CALLCENTER'
          bizTypeId = 'CALLCENTER'
        }
        
        const params: GetAbstractFieldsParamType = {
          bizType: bizType as any,
          bizTypeId: bizTypeId as any
        }

        const result = await getAbstractFields(params)

        if (MsgModel.isFail(result)) {
          this.$message.error(result.message)
          return
        }

        const fields = MsgModel.getData<AiSummaryFieldsType[]>(result, [])

        const fieldOptions = fields.map(field => {
          return {
            label: field.displayName,
            value: field.fieldName
          }
        })

        this.fieldOptions = fieldOptions
        
        console.log('fields', fields)
        
      } catch (error) {
        console.error('获取摘要字段失败', error)
      }
      
    },
    initSelectedFieldTags() {
      // 默认选中第一个模板
      const defaultTemplate = this.summaryTemplateList[0]
      if (defaultTemplate) {
        this.handleSummaryOptionClick(String(defaultTemplate.id))
      }
    },
    initSelectedDefaultSystemFields() {
      // 工单摘要 - 初始化默认字段
      if (this.isTaskTemplate) {
        this.initSelectedTaskDefaultSystemFields()
      }
      // 会话摘要 - 初始化默认字段
      else if (this.isImTemplate) {
        this.initSelectedImDefaultSystemFields()
      }
      // 呼叫中心 - 初始化默认字段
      else if (this.isVocTemplate) {
        this.initSelectedVocDefaultSystemFields()
      }
    },
    initSelectedImDefaultSystemFields() {
      // 会话摘要/咨询分类/客户名称/客服名称/会话来源/渠道/解决状态/服务备注/会话响应时长
      const defaultSystemFieldNames = [
        'conversationSummary',
        'consultName',
        'customerName',
        'customerServiceName',
        'source',
        'putChannelName',
        'solveStatus',
        'serviceRemark',
        'responseDuration'
      ]
      
      this.initSelectedBaseDefaultSystemFields(defaultSystemFieldNames)
    },
    initSelectedVocDefaultSystemFields() {
      // 通话ID/通话摘要/通话时长/接待坐席/客户/服务备注/咨询分类/呼叫类型/解决状态
      const defaultSystemFieldNames = [
        'recordId',
        'callRecordContentSummary',
        'talkTime',
        'agentName',
        'customerName',
        'serviceRemark',
        'consultName',
        'callType',
        'solveStatus'
      ]
      
      this.initSelectedBaseDefaultSystemFields(defaultSystemFieldNames)
    },
    initSelectedTaskDefaultSystemFields() {

      // 客户/产品类型/产品/服务类型/服务内容/服务部门/计划开始时间/计划完成时间/完成时间/服务商/工单状态/
      const defaultSystemFieldNames = [
        'customer',
        'productType',
        'product',
        'serviceType',
        'serviceContent',
        'executorTags',
        'planTime',
        'planStartTime',
        'planEndTime',
        'completeTime',
        'serviceProviders',
        'state',
      ]
      
      this.initSelectedBaseDefaultSystemFields(defaultSystemFieldNames)

    },
    initSelectedBaseDefaultSystemFields(defaultSystemFieldNames: string[]) {

      const systemFields = (this.fieldOptions?.[0]?.children || []) as FieldTag[]

      const defaultSystemFields = systemFields.filter((field: FieldTag) => {
        return defaultSystemFieldNames.includes(field.value) && !this.selectedFieldTags.some(tag => tag.value == field.value)
      })

      const defaultSystemFieldsTags = defaultSystemFields.map(field => {
        return {
          label: field.label,
          value: field.value,
          selected: false
        }
      })
      
      this.selectedFieldTags = this.selectedFieldTags.concat(defaultSystemFieldsTags)

    },
    async initSummaryTemplateList() {

      try {

        const params: GetAbstractTemplateListParamType = {
          type: this.template as string
        }

        const result = await getAbstractTemplateList(params)
        
        if (MsgModel.isFail(result)) {
          this.$message.error(result.message)
          return
        }
        
        this.summaryTemplateList = MsgModel.getData<AiSummaryTemplateType[]>(result, [])

      } catch (error) {
        console.error('获取摘要模板列表失败', error)
      }
    },
    async initTask() {
      try {
        this.initSelectedFieldTags()
        this.initSelectedDefaultSystemFields()
      } catch (error) {
        console.error('初始化工单字段失败', error)
      }
    },
    /**
     * 去除字符串中的<think></think>标签及其中的内容
     * @param {string} text - 包含可能的think标签的文本
     * @return {string} - 去除think标签及其内容后的文本
     */
    removeThinkTags(text: string) {
      // 使用正则表达式匹配<think>开始标签到</think>结束标签之间的所有内容（包括标签本身）
      // [\s\S]*? 表示匹配任意字符（包括换行符），非贪婪模式
      const thinkTagRegex = /<think>[\s\S]*?<\/think>/g;
      // 将匹配到的内容替换为空字符串
      return text.replace(thinkTagRegex, '');
    },
    getModuleName() {
      if (this.template === AIAgentTemplateEnum.Task) {
        return '工单摘要'
      } 
      else if (this.template === AIAgentTemplateEnum.IM) {
        return '呼叫中心摘要'
      } 
      else if (this.template === AIAgentTemplateEnum.VOC) {
        return '会话摘要'
      }
      return ''
    },
    downloadMarkdown(content: string, filename?: string) {
      // 创建 Blob 对象
      const blob = new Blob([content], { type: 'text/markdown' });
      
      // 创建下载链接
      const url = URL.createObjectURL(blob);
      
      // 创建一个临时的 a 标签用于下载
      const a = document.createElement('a');
      const currentDate = formatDate(new Date(), 'YYYY-MM-DD')
      const moduleName = this.getModuleName()
      const defaultFilename = `${moduleName}_${currentDate}.md`
      
      a.href = url;
      a.download = filename || defaultFilename;
      
      // 模拟点击下载
      document.body.appendChild(a);
      a.click();
      
      // 清理
      document.body.removeChild(a);
      // 释放 URL 对象
      URL.revokeObjectURL(url);

    },
    handleSummaryDownloadMarkdown() {
      const content = this.removeThinkTags(this.summary)
      this.downloadMarkdown(content)
    },
    handleSummaryCopy() {

      const content = this.summary

      console.log('content', content)

      if (isEmpty(content)) {
        this.$message.warning('暂无摘要内容')
        return
      }
      
      // 去除 <think> 和 </think> 标签中间的内容
      const contentWithoutThink = this.removeThinkTags(content)
      
      copyText(contentWithoutThink).then(() => {
        this.$message.success('复制成功')
      })
      
    },
    async getAllTaskTypeAllFields() {

      this.taskTypeFieldsMap = {}
      
      // 获取所有工单类型id
      const taskTypeIds = this.taskList.map(task => task.templateId).filter(Boolean)
      // 去重
      const uniqueTaskTypeIds = [...new Set(taskTypeIds)]
      
      // 分批处理，每批 5 个
      const batchSize = 5
      
      // 将 uniqueTaskTypeIds 分成多个批次
      for (let i = 0; i < uniqueTaskTypeIds.length; i += batchSize) {
        // 获取当前批次的 taskTypeIds
        const batchTaskTypeIds = uniqueTaskTypeIds.slice(i, i + batchSize)
        
        console.log(`处理第${Math.floor(i/batchSize) + 1}批工单类型，数量: ${batchTaskTypeIds.length}`)
        
        // 获取当前批次的所有工单类型字段
        const batchTaskTypeFields = batchTaskTypeIds.map(taskTypeId => this.getTaskTypeAllFields(taskTypeId))
        const batchTaskTypeFieldsPromise = await Promise.all(batchTaskTypeFields)
        
        // 将字段按工单类型id分组
        batchTaskTypeFieldsPromise.forEach((fields, index) => {
          const taskTypeId = batchTaskTypeIds[index]
          this.taskTypeFieldsMap[taskTypeId] = fields
        })
      }
      
    },
    async getTaskTypeAllFields(taskTypeId: string) {
      
      const taskParams = {
        typeId: taskTypeId,
        tableName: 'task',
        isFromSetting: false
      }

      const taskReceiptParams = {
        typeId: taskTypeId,
        tableName: 'task_receipt',
        isFromSetting: false
      }

      const allPromiseList = [getAllFields(taskParams), getAllFields(taskReceiptParams)]
      const [taskFields, taskReceiptFields] = await Promise.all(allPromiseList)

      const allFields = [...taskFields, ...taskReceiptFields]

      const notSystemFields = allFields.filter(field => isFalsy(field?.isSystem))

      // 合并工单类型字段
      return notSystemFields
    },
    taskTypeFieldsListToElementOptions() {

      const fieldOptions = [] as Record<string, any>[]

      for (const taskTypeId in this.taskTypeFieldsMap) {

        const originTaskType = this.taskTypesMap[taskTypeId] || {}
        const taskTypeName = originTaskType?.name || ''

        const optionsChildren = [] as Record<string, any>[]
        const options = [
          {
            label: taskTypeName,
            value: taskTypeName,
            children: optionsChildren
          }
        ]

        const fields = this.taskTypeFieldsMap[taskTypeId]

        fields.forEach(field => {

          const taskTypeFieldLabel = field.displayName

          optionsChildren.push({
            label: taskTypeFieldLabel,
            value: field.fieldName
          })

        })

        fieldOptions.push(...options)
      }

      return fieldOptions
      
    },
    columnTreeToElementOptions(tree: Record<string, any>) {

      if (isEmpty(tree) || Object.keys(tree).length === 0) {
        return []
      }

      const treeName = tree?.name || ''
      const optionsChildren = [] as Record<string, any>[]
      const options = [
        {
          label: treeName,
          value: treeName,
          children: optionsChildren
        }
      ]
      const columns = (tree?.columns || []) as Field[]


      columns.forEach(column => {
        optionsChildren.push({
          label: column.displayName,
          value: column.fieldName
        })
      })
      
      return options

    },
    getColumnsTree() {
      const originColumns = cloneDeep(this.fields)
      // @ts-ignore
      const columnTree = this.bizSelectColumnComponentInstance?.columnsDataGrouped(originColumns)
      return columnTree || {}
    },
    getParamsFields() {
      const selectedFieldTags = this.selectedFieldTags.filter(tag => tag.selected)
      return selectedFieldTags.map(tag => tag.value)
    },
    getParamsBizIds() {
      return this.taskList.map(item => item.id)
    },
    getTaskSummaryParams() {
      
      const isSameTemplate = this.taskList.every(task => task.templateId == this.taskList?.[0]?.templateId)
      const taskTypeId = isSameTemplate ? this.taskList[0]?.templateId : null

      const params: AIVocAgentSummaryAbstractParamType = {
        fields: this.getParamsFields(),
        bizIds: this.getParamsBizIds(),
        content: this.userRequirement,
        template: this.template,
        exportParam: {
          taskIds: this.getParamsBizIds().join(','),
          data: this.getParamsBizIds().join(','),
          ids: this.getParamsBizIds().join(','),
          receiptChecked: this.getParamsFields().join(','),
          sysChecked: this.getParamsFields().join(','),
          checked: this.getParamsFields().join(','),
          dataTotal: this.taskList.length,
          taskTypeId: taskTypeId
        }
      }
      return params
    },
    getImSummaryParams() {
      const params: AIVocAgentSummaryAbstractParamType = {
        fields: this.getParamsFields(),
        bizIds: this.getParamsBizIds(),
        content: this.userRequirement,
        template: this.template,
        exportParam: {
          ids: this.getParamsBizIds().join(','),
          checked: this.getParamsFields().join(','),
          dataTotal: this.taskList.length,
          exportSearchModel: JSON.stringify({
            orderDetail: {}
          })
        }
      }
      return params
    },
    getVocSummaryParams() {
      const params: AIVocAgentSummaryAbstractParamType = {
        fields: this.getParamsFields(),
        bizIds: this.getParamsBizIds(),
        content: this.userRequirement,
        template: this.template,
        exportParam: {
          ids: this.getParamsBizIds().join(','),
          checked: this.getParamsFields().join(','),
          dataTotal: this.taskList.length,
          exportSearchModel: JSON.stringify({
            orderDetail: {}
          })
        }
      }
      return params
    },
    async fetchSummary() {
      
      if (isEmpty(this.taskList)) {
        this.$message.warning('请先选择需要摘要的数据')
        return
      }

      if (isEmpty(this.userRequirement)) {
        this.$message.warning('请输入摘要要求')
        return
      }
      
      const selectedFieldTags = this.selectedFieldTags.filter(tag => tag.selected)
      if (isEmpty(selectedFieldTags)) {
        this.$message.warning('请选择要摘要的字段')
        return
      }
      
      try {

        let params = {}

        if (this.template == AIAgentTemplateEnum.Task) {
          params = this.getTaskSummaryParams()
        } 
        else if (this.template == AIAgentTemplateEnum.IM) {
          params = this.getImSummaryParams()
        } 
        else if (this.template == AIAgentTemplateEnum.VOC) {
          params = this.getVocSummaryParams()
        }

        console.log('params', params)
        
        this.loading = true
        this.summary = ''
        
        const result = await this.sendMessageWithStream(params)

        console.log('result', result)

      } catch (error) {
        console.error('获取摘要失败', error)
      } finally {
        this.loading = false
      }
      
    },
    async sendMessageWithStream(params: Record<string, any>) {

      const controller = new AbortController();

      let eventCount = 0;
      // 添加缓冲区存储不完整的数据
      let buffer = '';
      let text = ''

      const url = '/api/voice/outside/xiaobao/workflow/handle/abstract/ListStream'

      try {
        const tokenInfo = userCenterGetTokenInfo() as Record<string, any>
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'token': tokenInfo?.token || '',
          },
          signal: controller.signal,
          body: JSON.stringify(params),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const reader = response.body?.getReader();
        if (!reader) {
          throw new Error('Response body is null');
        }

        const decoder = new TextDecoder();
        let isFirst = true
        const endThinkTagText = '</think>'

        while (true) {

          const { value, done } = await reader.read();

          if (done) {
            console.log(`流式响应结束，共收到 ${eventCount} 条消息`);
            break;
          }

          // 解码当前数据块并添加到buffer
          buffer += decoder.decode(value, { stream: true });
          
          try {
            if (isFirst) {
              const data = decoder.decode(value, { stream: true })
              const isErrorJSON = isJSONObject(data)
              if (isErrorJSON) {
                const error = parse_with_default_value(data, {} as Record<string, any>)
                const isFail = MsgModel.isFail(error)
                if (isFail) {
                  this.$message.error(error?.message || '网络异常，请稍后再试')
                  return
                }
              }
            }
          } catch (error) {
            console.warn(error)
          } finally {
            isFirst = false
          }
          
          // 按行分割处理数据
          const lines = buffer.split('\n');
          
          // 保留最后一行（可能不完整）
          buffer = lines.pop() || '';

          // 处理完整的行
          for (const line of lines) {
            // 跳过空行
            if (line.trim() === '') {
              continue;
            }

            if (line.startsWith('data:')) {
              eventCount++;
              const message = line.replace(/^data:\s*/, '').trim();

              console.log(`第 ${eventCount} 条消息:`, message);

              if (message) {
                // 更新助理消息
                let textValue = JSON.parse(message).data.text || ''

                // 如果  textValue 包含 </think>, 则在 </think> 前面加一个空的 div
                if (textValue.includes(endThinkTagText)) {
                  const endThinkTagIndex = textValue.indexOf(endThinkTagText)
                  const before = textValue.slice(0, endThinkTagIndex)
                  const after = textValue.slice(endThinkTagIndex)
                  textValue = before + `<div></div>` + after
                }

                text += textValue
                this.summary = text;
                // 滚动消息列表到底部
                this.scrollMessageListToBottom();
                setTimeout(() => {
                  this.scrollMessageListToBottom();
                }, 300)
              }

            }

          }
        }

      } catch (error) {
        console.error('sendMessageWithStream error:', error);
      }
      
      this.$nextTick(() => {
        this.scrollMessageListToBottom();
      })
      
      setTimeout(() => {
        this.scrollMessageListToBottom();
      }, 300)

      if (isFalsy(text)) {
        this.$message.error('网络异常，请稍后再试')
      }

      return text;

    },
    scrollMessageListToBottom() {
      const thinkContent = this.$refs.AiSummaryContentWrapper as HTMLElement
      if (thinkContent) {
        thinkContent.scrollTop = 100000
      }
    },
    unSelectedSummaryOptions() {
      this.selectedSummaryOptions = []
    },
    handleSummary() {
      this.fetchSummary()
    },
    // 选择摘要选项
    handleSummaryOptionSelect(options: SummaryOption[]) {
      this.selectedSummaryOptions = options
    },
    // 用户需求输入变化
    handleRequirementChange(value: string) {
      this.userRequirement = value
      this.unSelectedSummaryOptions()
    },
    // 关闭字段标签
    handleFieldTagClose(tagValue: string) {
      this.selectedFieldTags = this.selectedFieldTags.filter(tag => tag.value !== tagValue)
      this.unSelectedSummaryOptions()
      this.$nextTick(() => {
        this.updatePopper()
      })
    },
    handleFieldTagClick(tag: FieldTag) {
      // 切换选中状态
      tag.selected = !tag.selected
      this.unSelectedSummaryOptions()
      this.$nextTick(() => {
        this.updatePopper()
      })
    },
    // 点击摘要选项切换选中状态
    handleSummaryOptionClick(optionValue: string) {
      
      // 如果当前已选中，则取消选中
      if (this.selectedSummaryOptions.some(opt => opt.value === optionValue)) {
        this.selectedSummaryOptions = this.selectedSummaryOptions.filter(opt => opt.value !== optionValue)
        return
      }

      const option = this.availableSummaryOptions.find(opt => opt.value === optionValue)
      if (option) {
        this.selectedSummaryOptions = [option]
      }
      
      this.setSummaryTemplate(optionValue)

    },
    // 获取摘要模板
    setSummaryTemplate(templateId: string) {

      const template = this.summaryTemplateIdMap[templateId]
      if (!template) {
        console.warn(`摘要模板不存在: ${templateId}`)
        return null
      }

      // 摘要要求
      const content = template?.content || ''
      // 获取摘要模板中的字段列表
      const fields = template?.fields || []
      
      const newFieldTags = fields.map(field => ({
        label: field.cnName,
        value: field.enName,
        selected: true
      }))
      
      const currentNotSelectedFieldTags = this.selectedFieldTags
        .filter(tag => !newFieldTags.some(newTag => newTag.value === tag.value))
        .map(tag => {
          const newTag = {
            ...tag,
            selected: false
          }
          return newTag
        })
      
      // 将摘要模板中的字段设置为选中的字段
      this.selectedFieldTags = newFieldTags.concat(currentNotSelectedFieldTags)

      this.userRequirement = content
    },
    // 获取字段标签类型
    getFieldTagType(tagValue: string): string {
      const isSelected = this.selectedFieldTags.some(tag => tag.value === tagValue)
      return isSelected ? 'primary' : 'info'
    },
    // 获取摘要选项类型
    getSummaryOptionType(optionValue: string): string {
      const isSelected = this.selectedSummaryOptions.some(option => option.value === optionValue)
      return isSelected ? 'primary' : 'info'
    },
    getCascaderGroupValue(childTagValue: string) {
      for (const key in this.fieldOptionsValueFieldMap) {

        const childMap = this.fieldOptionsValueFieldMap[key]
        if (!childMap) {
          continue
        }

        const child = childMap[childTagValue]
        if (!child) {
          continue
        }

        return key
        
      }
    },
    getCascaderValue() {
      const selectedFieldTags = this.selectedFieldTags.filter(tag => tag.selected)
      const cascaderValue = selectedFieldTags.map(tag => {
        const groupValue = this.getCascaderGroupValue(tag.value)
        return [tag.value]
      })
      return cascaderValue
    },
    // 渲染加载中状态
    renderLoading() {
      return (
        <div class="ai-task-summary-loading">
          <el-loading></el-loading>
          <div>正在生成摘要...</div>
        </div>
      )
    },
    // 渲染品牌信息
    renderBrand() {
      const logoUrl = getLocalesOssUrl('/ai/deepservice_logo.png')
      return (
        <div class="ai-task-summary-brand">
          <div class="ai-task-summary-brand-logo">
            <img src={logoUrl} alt="DeepService" />
          </div>
          <div class="ai-task-summary-brand-copyright">由 DeepService + AI 生成</div>
        </div>
      )
    },
    // 渲染空结果区域
    renderEmptyResult() {
      let content = this.renderBrand();
      
      return (
        <div class="ai-task-summary-empty-result">
          {content}
        </div>
      )
    },
    renderSummaryContentThinking() {
      
      if (!this.loading) {
        return null
      }
      
      return (
        <div class="ai-task-summary-content-thinking">
          深度思考中...
          <img src={aiAnalyzeLogo} alt="深度思考" />
        </div>
      )
    },
    // 渲染摘要内容
    renderSummaryContent() {
      return (
        <div class="ai-summary-result-wrapper" ref="AiSummaryContentWrapper">
          {this.renderSummaryContentThinking()}
          <div
            ref="AiSummaryContent"
            class="ai-summary-content markdown-body"
            // @ts-ignore
            domPropsInnerHTML={this.contentHTML}
          >
          </div>
          <div class="ai-summary-result-wrapper-footer">
            {this.renderSummaryContentFooter()}
          </div>
        </div>
      )
    },
    renderSummaryContentFooter() {
      
      if (this.loading || this.isEmptySummary) {
        return null
      }

      return (
        <div class="ai-summary-result-wrapper-footer">
          <el-button
            type="text" 
            onClick={this.handleSummaryCopy}
          >
            <i class="iconfont icon-fuzhi"></i>
            复制
          </el-button>
        </div>
      )
    },
    // 渲染摘要结果或空状态
    renderResultContent() {

      if (this.loading) {
        return this.renderSummaryContent()
      }
      
      if (this.isEmptySummary) {
        return this.renderEmptyResult()
      }

      return this.renderSummaryContent()
    },
    // 渲染摘要生成按钮
    renderSummaryButton() {
      return (
        <el-button type="primary" onClick={this.handleSummary}>
          <img 
            src={this.aiIconUrl}
            class="ai-task-summary-ai-icon"
          />
          <span>
            &nbsp;
          </span>
          <span>
            生成任务摘要
          </span>
        </el-button>
      )
    },
    // 渲染摘要生成中状态
    renderSummaryLoading() {
      return (
        <span class="ai-task-summary-transcript-loading">
          正在生成摘要...
        </span>
      )
    },
    // 渲染原始数据加载按钮
    renderOriginalButton() {
      return (
        <el-button type="primary" onClick={this.handleSummary}>
          <img 
            src={this.aiIconUrl}
            class="ai-task-summary-ai-icon"
          />
          <span>
            &nbsp;
          </span>
          <span>
            显示原始数据
          </span>
        </el-button>
      )
    },
    // 渲染原始数据加载中状态
    renderOriginalLoading() {
      return (
        <span class="ai-task-summary-transcript-loading">
          正在加载数据...
        </span>
      )
    },
    renderHeader() {
      return (
        <div class="ai-task-summary-header">
          <div class="ai-task-summary-progress-tip">
            已选 
            &nbsp;
            <span class="ai-task-summary-progress-count">
              {this.completedCount}
            </span> 
            &nbsp;
            条数据，为你整理了可能需要的摘要，快来点击试试结果！
          </div>
        </div>
      )
    },
    renderRequirementInput() {
      return (
        <div class="ai-task-summary-requirement">
          <div class="ai-task-summary-requirement-title">
            描述您需要摘要内容的要求
          </div>
          <el-input
            rows={4}
            type="textarea"
            maxlength={500}
            placeholder="请输入摘要内容的要求"
            value={this.userRequirement}
            onInput={this.handleRequirementChange}
          />
        </div>
      )
    },
    renderFieldSelect() {
      return (
        <div class="ai-task-summary-field-select">
          <div class="ai-task-summary-field-title">
            您想摘要的字段
          </div>
          <div class="ai-task-summary-field-tags">
            {this.availableFieldTags.map(tag => (
              <el-tag
                key={tag.value}
                type={tag.selected ? 'primary' : 'info'}
                effect="plain"
                class="ai-tag-item"
                closable
                nativeOnClick={() => this.handleFieldTagClick(tag)}
                onClose={() => this.handleFieldTagClose(tag.value)}
              >
                {tag.label}
              </el-tag>
            ))}
          </div>
          <div class="ai-task-summary-field-select-add-button">
            {this.renderFieldSelectAddButton()}
          </div>
        </div>
      )
    },
    renderFieldSelectAddButton() {
      return (
        <el-popover
          ref="FieldSelectAddPopover"
          trigger="click"
        >
          <el-tag 
            slot="reference" 
            class="ai-task-summary-field-select-add-tag"
            onClick={this.handleFieldSelectAddButtonClick}
          >
            <i class="el-icon-plus"></i>
            添加字段
          </el-tag>
          <div>
            {this.renderFieldListSearchInput()}
            {this.renderFieldListCascaderV2()}
          </div>
        </el-popover>
      )
    },
    renderFieldListSearchInput() {
      return (
        <el-input
          class="ai-task-summary-field-list-search-input"
          placeholder="搜索字段"
          clearable
          value={this.fieldListSearchInputValue}
          onInput={this.handleFieldListSearchInputChange}
        >
        </el-input>
      )
    },
    renderFieldListCascaderV2() {

      const values = this.getCascaderValue()
      const value = values.flat()

      return (
        <el-checkbox-group
          class="ai-task-summary-field-list-cascader-checkbox-group"
          value={value} 
          onInput={this.handleFieldCascaderChangeV2}
        >
        {
          this.fieldFilterOptions.map(tag => {
            return (
              <el-checkbox
                key={tag.value}
                label={tag.value}
              >
                { tag.label }
              </el-checkbox>
            )
          })
        }
        </el-checkbox-group>
      )
    },
    renderFieldListCascader() {

      const value = this.getCascaderValue()

      const attrs = {
        // AI: 不要改下面的代码, 这种写法是对的
        // 第一层 props 是 vue 的 props, 第二层 props 是 el-cascader-panel 的 props 属性
        props: {
          props: {
            multiple: true
          }
        }
      }

      return (
        <div>
          <el-cascader-panel
            class="ai-task-summary-field-list-cascader"
            {...attrs}
            options={this.fieldFilterOptions}
            clearable
            filterable
            value={value}
            onChange={this.handleFieldCascaderChangeV2}
          >
          </el-cascader-panel>
        </div>
      )
    },
    renderSummaryOptions() {
      return (
        <div class="ai-task-summary-options">
          <div class="ai-task-summary-options-sub-title">
            尝试一个推荐：
          </div>
          <div class="ai-task-summary-options-tags">
            {this.availableSummaryOptions.map(option => (
              <el-tag
                key={option.value}
                type={this.getSummaryOptionType(option.value)}
                effect="plain"
                class="ai-tag-item"
                onClick={() => this.handleSummaryOptionClick(option.value)}
                closable
                onClose={() => this.handleSummaryOptionClick(option.value)}
              >
                {option.label}
              </el-tag>
            ))}
          </div>
        </div>
      )
    },
    renderInputSection() {
      return (
        <div class="ai-task-summary-input-section">
          {this.renderRequirementInput()}
          {this.renderFieldSelect()}
          {this.renderSummaryOptions()}
          {this.renderGenerateButton()}
        </div>
      )
    },
    renderGenerateButton() {
      return (
        <div class="ai-task-summary-generate">
          <el-button 
            type="primary" 
            onClick={this.handleSummary}
            loading={this.loading}
            class="ai-task-summary-generate-button"
          >
            快来生成摘要
            <i class="iconfont icon-arrow-forward-outline"></i>
          </el-button>
        </div>
      )
    },
    renderContent() {
      return (
        <div class="ai-task-summary-content">
          <div class="ai-task-summary-left-panel">
            {this.renderHeader()}
            {this.renderInputSection()}
          </div>
          <div class="ai-task-summary-right-panel">
            <div class="ai-task-summary-result-title">
              AI摘要展示
            </div>
            <div class="ai-task-summary-result-content">
              {this.renderResultContent()}
            </div>
          </div>
        </div>
      )
    },
    renderSummaryTranscript() {
      if (this.loading) {
        return (
          <div class="ai-task-summary-transcript">
            {this.renderSummaryLoading()}
          </div>
        )
      } 

      return (
        <div class="ai-task-summary-transcript">
          {this.renderSummaryButton()}
        </div>
      )
    },
    renderOriginal() {
      if (this.isEmptyOriginal) {
        return this.renderOriginalTranscript()
      }

      return (
        <div class="ai-task-summary-original-data">
          <el-table
            data={this.originalData}
            border
            style="width: 100%"
          >
            {this.fields.map(field => (
              <el-table-column
                prop={field.fieldName}
                label={field.displayName}
              />
            ))}
          </el-table>
        </div>
      )
    },
    renderOriginalTranscript() {
      if (this.loading) {
        return (
          <div class="ai-task-summary-transcript">
            {this.renderOriginalLoading()}
          </div>
        )
      }

      return (
        <div class="ai-task-summary-transcript">
          {this.renderOriginalButton()}
        </div>
      )
    },
    renderMain() {
      return (
        <div class="ai-task-summary-main">
          {this.renderContent()}
        </div>
      )
    },
    handleFieldCascaderChangeV2(selectedOptions: string[]) {

      console.log('级联选择器选择变化:', selectedOptions)

      this.unSelectedSummaryOptions()

      // 如果未选择任何字段，则不进行处理
      if (isEmpty(selectedOptions)) {
        console.warn('未选择任何字段')
        this.selectedFieldTags = []
        return
      }

      // 创建一个临时数组存储新字段标签
      const newFieldTags: FieldTag[] = []

      const fieldMap = this.fieldOptions.reduce((acc, option) => {
        acc[option.value] = option
        return acc
      }, {})
      
      // 遍历所有选中的选项
      selectedOptions.forEach(optionPath => {

        const fieldValue = optionPath
        const fieldOption = fieldMap[fieldValue]

        if (fieldOption) {
          const fieldTag: FieldTag = {
            label: fieldOption.label,
            value: fieldValue,
            selected: true
          }
          newFieldTags.push(fieldTag)
        }

      })

      const currentNotSelectedFieldTags = this.selectedFieldTags
        .filter(tag => !newFieldTags.some(newTag => newTag.value === tag.value))
        .map(tag => {
          const newTag = {
            ...tag,
            selected: false
          }
          return newTag
        })
      
      // 更新已选字段标签
      this.selectedFieldTags = newFieldTags.concat(currentNotSelectedFieldTags)
      
      console.log('更新后的字段标签列表:', this.selectedFieldTags)
      
      this.$nextTick(() => {
        this.updatePopper()
      })

    },
    updatePopper() {
      const times = [0, 100, 300, 500, 1000]
      times.forEach(time => {
        setTimeout(() => {
          this.updatePopperImpl()
        }, time)
      })
    },
    updatePopperImpl() {
      const popover = this.$refs.FieldSelectAddPopover as Record<string, any>
      popover?.updatePopper()
    }
  },
  render() {
    return (
      <div 
        class="ai-task-summary" 
        onClick={this.handlerMainClick}
      >
        {this.renderMain()}
      </div>
    )
  }
})
