/* components */
import ComponentNameEnum from "@model/enum/ComponentNameEnum"
import SettingGPTViewDialog from '@gpt/components/dialog'
/* vue */
import { defineComponent, PropType } from "vue"
/* scss */
import "@src/modules/ai/components/upgrade-v2/dialog.scss"
/* api */
import { saveValueAddedClue } from "@src/api/Clue"
/* utils */
import { isFalsy, postPage } from "pub-bbx-utils"
import { getRootWindow } from "@src/util/platform"
import { getLocalesOssUrl } from "@src/util/assets"

type SettingGPTViewDialogComponent = InstanceType<typeof SettingGPTViewDialog>

enum AiAgentUpgradeV2EventEnum {
  CLOSE = 'close',
  CONFIRM = 'confirm'
}

export default defineComponent({
  name: ComponentNameEnum.AiAgentUpgradeV2Dialog,
  props: {
    onClose: {
      type: Function as PropType<() => void>
    },
    onConfirm: {
      type: Function as PropType<(text: string) => void>
    }
  },
  data() {
    return {
      type: 24,
      upgradeImage: getLocalesOssUrl('/ai/upgrade_v2_large.png'),
      backgroundImage: getLocalesOssUrl('/ai/ai_upgrade_v2.png')
    }
  },
  computed: {
    dialogComponent(): SettingGPTViewDialogComponent {
      return this.$refs.SettingGPTViewDialog as SettingGPTViewDialogComponent
    },
    confirmText(): string {
      return '升级'
    },
    style(): Record<string, string> {
      return {
        backgroundImage: `url(${this.backgroundImage})`,
        backgroundSize: '100% 100%',
        backgroundRepeat: 'no-repeat',
        backgroundPosition: 'center'
      }
    }
  },
  methods: {
    openDialog() {
      this.dialogComponent.open()
    },
    closeDialog() {
      this.dialogComponent.close()
      this.handleClose()
    },
    handleClose() {
      this.$emit(AiAgentUpgradeV2EventEnum.CLOSE)
    },
    handleConfirm() {
      this.$emit(AiAgentUpgradeV2EventEnum.CONFIRM)
    },
    openChat() {
      
      saveValueAddedClue({ type: this.type })
      
      const rootWindow = getRootWindow(window)
      postPage({
        action: 'shb.system.openChat',
        data: {},
      })
      
    },
    renderContent() {
      return (
        <div class="ai-upgrade-v2-dialog-content" style={this.style}>
          {this.renderTitle()}
          {this.renderDescription()}
          {this.renderChatButton()}
          {this.renderCloseButton()}
        </div>
      )
    },
    renderTitle() {
      return (
        <div class="ai-upgrade-v2-dialog-title">
          全新升级 DeepService AI 智能体！
        </div>
      )
    },
    renderDescription() {
      return (
        <div class="ai-upgrade-v2-dialog-description">
          <p>
            “hello，我是可以深度学习企业内数据，创造专属于您企业的 AI 智能应用的平台，我已经拥有了多种成熟的智能体场景，例如 
            <span class="ai-upgrade-v2-dialog-description-highlight">
              帮助客服进行 7*24 小时不间断客户沟通的"智能客服”、实现企业内知识精准对答的"智能问答"
            </span>
            等等，我将是最懂您企业服务场景的 AI 伙伴，快来点击下方按钮试用吧。”
          </p>
        </div>
      )
    },
    renderChatButton() {
      return (
        <div class="ai-upgrade-v2-dialog-chat-button">
          <el-button
            type="primary"
            onClick={this.openChat}
          >
            立即咨询
          </el-button>
        </div>
      )
    },
    renderCloseButton() {
      return (
        <div 
          class="ai-upgrade-v2-dialog-close-button"
          onClick={this.closeDialog}
        >
          <i class="iconfont icon-fe-close"></i>
        </div>
      )
    }
  },
  render() {
    return (
      <SettingGPTViewDialog
        ref="SettingGPTViewDialog"
        title="全新升级"
        width="840px"
        class="ai-upgrade-v2-dialog"
        confirmText={this.confirmText}
        onClose={this.handleClose}
        onConfirm={this.handleConfirm}
      >
        {this.renderContent()}
      </SettingGPTViewDialog>
    )
  }
}) 