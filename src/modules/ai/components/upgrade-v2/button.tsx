/* components */
import ComponentNameEnum from "@model/enum/ComponentNameEnum"
/* vue */
import { defineComponent, PropType } from "vue"
/* scss */
import "@src/modules/ai/components/upgrade-v2/button.scss"
/* util */
import { getLocalesOssUrl } from "@src/util/assets"

enum AiAgentUpgradeV2EventEnum {
  CLICK = 'click'
}

export default defineComponent({
  name: ComponentNameEnum.AiAgentUpgradeV2Button,
  props: {
    value: {
      type: Boolean,
      default: false
    },
    onClick: {
      type: Function,
    }
  },
  data() {
    return {
      upgradeImage: getLocalesOssUrl('/ai/upgrade_v2_mini.png'),
    }
  },
  computed: {
  },
  methods: {
    onClickHandle() {
      this.$emit(AiAgentUpgradeV2EventEnum.CLICK)
    },
    renderButton() {
      return (
        <el-button
          class="ai-upgrade-v2-button-wrapper"
          onClick={this.onClickHandle}
        >
          <img class="ai-upgrade-v2-button-image" src={this.upgradeImage} />
          <span class="ai-upgrade-v2-button-text">全新升级</span>
        </el-button>
      )
    }
  },
  render() {
    return (
      <div class="ai-upgrade-v2-button">
        {this.renderButton()}
      </div>
    )
  }
}) 