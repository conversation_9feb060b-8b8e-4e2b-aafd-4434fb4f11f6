
.ai-agent-share-dialog-content-send-user-wrapper-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
}

.ai-agent-share-dialog-content-send-user-wrapper-title {
  margin-bottom: 6px;
}

.ai-agent-share-dialog-content-send-user-input-empty {
  flex: 1;
}

.ai-agent-share-dialog-content-send-user-wrapper-tip {
  margin-top: 6px;
  height: 20px;
  font-size: 12px;
  color: #8C8C8C;
}

.ai-agent-share-dialog-content-send-user-wrapper {
  margin-bottom: 16px;
}

.ai-agent-share-dialog-content-public-content {
  display: flex;
  align-items: center;
}

.ai-agent-share-dialog-content-public-content-left {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  background-color: $color-primary;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  i {
    font-size: 22px;
  }
}

.ai-agent-share-dialog-content-public-content-right-title {
  height: 22px;
  line-height: 22px;
  .ai-agent-share-dialog-content-public-content-right-title-description {
    color: #8C8C8C;
    font-size: 12px;
    margin-left: 4px;
  }
}

.ai-agent-share-dialog-content-public-content-right-description {
  color: #8C8C8C;
  font-size: 12px;
  margin-top: 4px;
}

.ai-agent-share-dialog-content-public-content-copy-button {
  margin-top: 16px;
  .el-input-group__append,
  .el-button {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
}

.ai-agent-share-dialog-content-send-user-input-not-empty-input,
.ai-agent-share-dialog-content-send-user-input-empty-input {
  border: 1px solid #D9D9D9;
  min-height: 32px;
  border-radius: 4px;
  padding: 2px 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  color: #8c8c8c;
}

.ai-agent-share-dialog-content-send-user-input-not-empty {
  width: 100%;
}

.ai-agent-share-dialog-content-send-user-input-not-empty-input {
  i {
    margin-left: 4px;
    &:hover {
      color: $color-danger;
    }
  }
}