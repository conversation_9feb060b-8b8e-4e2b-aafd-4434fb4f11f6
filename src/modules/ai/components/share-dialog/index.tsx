/* vue */
import { defineComponent } from "vue";
/* components */
import SettingGPTViewDialog from "@gpt/components/dialog"
import AiAgentShareDialogContent from "@src/modules/ai/components/share-dialog/content"
/* types */
import { AIAgentType } from "@src/modules/ai/types"
/* scss */
import "@src/modules/ai/components/share-dialog/index.scss"
import { useDialog } from "@hooks/useDialog";

type SettingGPTViewDialogComponent = InstanceType<typeof SettingGPTViewDialog>
type AiAgentShareDialogContentComponent = InstanceType<typeof AiAgentShareDialogContent>

export default defineComponent({
  name: "AiAgentShareDialog",
  setup() {
    const { visible } = useDialog()
    return {
      visible
    }
  },
  data() {
    return {
      item: {} as AIAgentType
    }
  },
  computed: {
    aiAgentShareDialog(): SettingGPTViewDialogComponent {
      return this.$refs.AiAgentShareDialog as SettingGPTViewDialogComponent;
    },
    aiAgentShareDialogContent(): AiAgentShareDialogContentComponent {
      return this.$refs.AiAgentShareDialogContent as AiAgentShareDialogContentComponent
    }
  },
  methods: {
    open(item: AIAgentType) {
      
      this.visible = true
      
      this.item = item

      this.$nextTick(() => {
        const aiAgentShareDialog = this.$refs.AiAgentShareDialog as SettingGPTViewDialogComponent
        aiAgentShareDialog.open();
      })
      
    },
    close() {
      this.visible = false
    },
    closeHandler() {
      this.aiAgentShareDialog.close()
    },
    renderContentWrapper() {
      if (this.visible) {
        return this.renderContent()
      }
      return null
    },
    renderContent() {
      return (
        <AiAgentShareDialogContent
          ref="AiAgentShareDialogContent"
          agent={this.item as AIAgentType}
          onClose={this.closeHandler}
        />
      )
    }
  },
  render() {
    return (
      <SettingGPTViewDialog
        width="600px"
        title="分享"
        ref="AiAgentShareDialog"
        className="ai-agent-share-dialog"
        showFooter={false}
        onClose={this.close}
      >
        {this.renderContentWrapper()}
      </SettingGPTViewDialog>
    )
  }
})