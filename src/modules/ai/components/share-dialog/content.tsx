/* vue */
import { defineComponent, PropType } from "vue";
/* components */
/* scss */
import "@src/modules/ai/components/share-dialog/index.scss"
/* types */
import { AIAgentAppType, AIAgentDetailType, AIAgentType } from "@src/modules/ai/types"
import 
  BaseSelectUser, 
  { 
    BaseSelectUserModeEnum, 
    BaseSelectUserMultiAllOptionsType,
    BaseSelectUserSelectedItemType
  } 
from "@src/component/common/BaseSelectUser"
/* utils */
import { isEmpty } from "pub-bbx-utils"
import { useTenantId } from "@hooks/useRootWindow"
import { setClipboardData } from "@src/util/dom"
import { message } from "@src/util/message"
import MsgModel from "@model/MsgModel";
import { AIAgentDetailParamType, SendShareAccessMessageParamType } from "@src/modules/ai/model/param";
import { getAIAgentDetail, sendShareAccessMessage } from "@src/api/AIv2API";
import { AIAgentAppAuthTypeEnum } from "@src/modules/ai/model/enum";

enum AiAgentShareDialogContentEventEnum {
  Close = 'close'
}

export default defineComponent({
  name: "AiAgentShareDialogContent",
  props: {
    agent: {
      type: Object as PropType<AIAgentType>,
      default: () => ({})
    },
    onClose: {
      type: Function
    }
  },
  setup() {
    const tenantIdRef = useTenantId()
    return {
      tenantIdRef
    }
  },
  data() {
    return {
      selectedList: [] as BaseSelectUserSelectedItemType[],
      agentAppDetail: {} as AIAgentAppType,
      loading: false
    }
  },
  computed: {
    agentId(): string {
      return this.agent?.id as string
    },
    isPublic(): boolean {
      return Boolean(this.agentAppDetail?.open)
    },
    allRange(): number | undefined {
      return this.agentAppDetail?.allRange
    },
    isAllRangeHidden(): boolean {
      const allRange = this.allRange || ''
      return String(allRange) === String(AIAgentAppAuthTypeEnum.HIDDEN)
    },
    shareUrl(): string {
      const locationPath = window.location.origin
      const agentId = this.agentId
      const tenantId = this.tenantIdRef
      return `${locationPath}/shb/home/<USER>/agent/chat?agentId=${agentId}&tenantId=${tenantId}`
    },
    attrs(): Record<string, any> {
      return {
        directives: [
          {
            name: 'loading',
            value: this.loading
          }
        ]
      }
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.initSelectedList()
      this.getAIAgentDetail()
    },
    initSelectedList() {
      this.selectedList = []
    },
    async getAIAgentDetail() {
      try {

        this.loading = true

        const params: AIAgentDetailParamType = {
          id: this.agent.id as number
        }
        const res = await getAIAgentDetail(params)
        
        const isFail = MsgModel.isFail(res)
        if (isFail) {
          const message = MsgModel.getMessage(res)
          this.$message.error(message)
          return
        }
        
        const aiAgent = MsgModel.getData<AIAgentType>(res, {})
        
        this.agentAppDetail = aiAgent?.aiAgentAppDetail || {}
        
      } catch (error) {
        console.error(error)
      } finally {
        this.loading = false
      }
    },
    handleSendUserInputClick() {
      this.openSelectUserDialog()
    },
    handleSendUserInputDeleteClick(event: Event) {
      event.stopPropagation()
      this.selectedList = []
    },
    handleCopyUrl() {
      setClipboardData(this.shareUrl, () => {
        message.success('复制成功')
      })
    },
    handleSendUserButtonClick() {

      const isEmptySelectedList = isEmpty(this.selectedList)
      if (isEmptySelectedList) {
        message.warning("请先选择发送对象")
        return
      }

      this.sendShareAccessMessage()

    },
    async sendShareAccessMessage() {

      const params: SendShareAccessMessageParamType = {
        agentId: this.agent.id as number,
        ranges: this.selectedList
      }

      const res = await sendShareAccessMessage(params)

      const isFail = MsgModel.isFail(res)
      if (isFail) {
        const message = MsgModel.getMessage(res)
        this.$message.error(message)
        return
      }

      message.success('发送成功')

      this.handleClose()
      
    },
    handleClose() {
      this.$emit(AiAgentShareDialogContentEventEnum.Close)
    },
    async openSelectUserDialog() {
      
      const options: BaseSelectUserMultiAllOptionsType = {
        title: '添加发送对象',
        max: 100,
        selectedAll: this.selectedList,
        mode: BaseSelectUserModeEnum.Filter
      }
      const result = await BaseSelectUser.props.multi.all(options as BaseSelectUserMultiAllOptionsType)
      const isFail = MsgModel.isFail(result)
      if (isFail) {
        return
      }
      
      const all = result?.data?.all || []
      this.selectedList = all

    },
    renderSendUserWrapper() {

      // 如果对内分享不可见，则不显示发送内部对象
      if (this.isAllRangeHidden) {
        return null
      }
      
      return (
        <div class="ai-agent-share-dialog-content-send-user-wrapper">
          <div class="ai-agent-share-dialog-content-send-user-wrapper-title">
            发送内部对象
          </div>
          <div class="ai-agent-share-dialog-content-send-user-wrapper-content">
            {this.renderSendUserInputWrapper()}
            {this.renderSendUserButtonWrapper()}
          </div>
        </div>
      )
    },
    renderSendUserInputWrapper() {
      const isEmptySelectedList = isEmpty(this.selectedList)
      if (isEmptySelectedList) {
        return this.renderSendUserInputEmpty()
      }
      return this.renderSendUserInput()
    },
    renderSendUserInputEmpty() {
      return (
        <div class="ai-agent-share-dialog-content-send-user-input-empty">
           <div 
            class="ai-agent-share-dialog-content-send-user-input-empty-input"
            onClick={this.handleSendUserInputClick}
          >
            <span>搜索用户发送到内部聊天</span>
            <i class="iconfont icon-more"></i>
          </div>
        </div>
      )
    },
    renderSendUserInput() {

      const names: string[] = this.selectedList.map(item => {
        // @ts-ignore
        return item.name || item.displayName
      })
      
      const name = names.join(', ')

      return (
        <div class="ai-agent-share-dialog-content-send-user-input-not-empty">
          <div 
            class="ai-agent-share-dialog-content-send-user-input-not-empty-input"
            onClick={this.handleSendUserInputClick}
          >
            <span>
              {name}
            </span>
            <i 
              class="iconfont icon-fe-close"
              onClick={this.handleSendUserInputDeleteClick}
            >
            </i>
          </div>
        </div>
      )
    },
    renderSendUserButtonWrapper() {
      return (
        <div class="ai-agent-share-dialog-content-send-user-button-wrapper">
          <el-button 
            type="primary"
            onClick={this.handleSendUserButtonClick}
          >
            发送
          </el-button>
        </div>
      )
    },
    renderMain() {
      return (
        <div class="ai-agent-share-dialog-content-main">
          {this.renderSendUserWrapper()}
          {this.renderPublicWrapper()}
        </div>
      )
    },
    renderPublicWrapper() {
      const isPublic = this.isPublic
      if (isPublic) {
        return (
          <div class="ai-agent-share-dialog-content-public-content-wrapper">
            {this.renderPublicContent()}
            {this.renderUnPublicContentCopyButton()}
          </div>
        )
      }
      return this.renderUnPublicContent()
    },
    renderPublicContent() {
      return (
        <div class="ai-agent-share-dialog-content-public-content">

          <div class="ai-agent-share-dialog-content-public-content-left">
            <i class="iconfont icon-team1"></i>
          </div>

          <div class="ai-agent-share-dialog-content-public-content-right">

            <div class="ai-agent-share-dialog-content-public-content-right-title">
              <span>
                公开访问已开启
              </span>
              <span class="ai-agent-share-dialog-content-public-content-right-title-description">
                (如需修改请前往编辑)
              </span>
            </div>

            <div class="ai-agent-share-dialog-content-public-content-right-description">
              互联网上获得链接的用户可访问使用该Agent
            </div>

          </div>

        </div>
      )
    },
    renderUnPublicContent() {
      return (
        <div class="ai-agent-share-dialog-content-public-content">

          <div class="ai-agent-share-dialog-content-public-content-left">
            <i class="iconfont icon-earth">
            </i>
          </div>

          <div class="ai-agent-share-dialog-content-public-content-right">

            <div class="ai-agent-share-dialog-content-public-content-right-title">
              <span>
                公开访问已关闭
              </span>
              <span class="ai-agent-share-dialog-content-public-content-right-title-description">
                (如需修改请前往编辑)
              </span>
            </div>

            <div class="ai-agent-share-dialog-content-public-content-right-description">
              仅内部被授权的员工可访问使用该Agent
            </div>

          </div>

        </div>
      )
    },
    renderUnPublicContentCopyButton() {
      return (
        <div class="ai-agent-share-dialog-content-public-content-copy-button">
          <el-input
            disabled
            value={this.shareUrl}
          >
            <el-button 
              slot="append" 
              type="primary" 
              onClick={this.handleCopyUrl}
            >
              复制链接
            </el-button>
          </el-input>
        </div>
      )
    }
  },
  render() {
    return (
      <div 
        class="ai-agent-share-dialog-content"
        {...this.attrs}
      >
        {this.renderMain()}
      </div>
    )
  }
})