// @ts-nocheck
/* components */
// @ts-ignore
import VueMarkdown from 'vue-markdown'
/* vue */
import { defineComponent, PropType } from "vue"
/* scss */
import "@src/modules/ai/components/audio-summary/index.scss"
/* api */ 
import { vocAgentDetailSummary } from "@src/api/AIv2API"
/* utils */
import { isEmpty, isFalsy } from "pub-bbx-utils"
import { parse, parse_with_default_value } from "@src/util/lang/object"
import { getLocalesOssUrl } from "@src/util/assets"
/* model */
import { AIAudioSummaryTagEnum } from "@src/modules/ai/components/audio-summary/model"
import { AIVocAgentDetailSummaryParamType } from "@src/modules/ai/model/param"
import MsgModel from "@model/MsgModel"

enum AIAudioSummaryEventEnum {
  CALL_RECORD = 'callRecord',
  LEAVE_MESSAGE = 'leaveMessage'
}

export default defineComponent({
  name: 'AIAudioSummary',
  components: {
    VueMarkdown
  },
  props: {
    callDetail: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({})
    },
    isCall: {
      type: Boolean as PropType<boolean>,
      default: true
    },
    onCallRecord: {
      type: Function as PropType<(event: AIAudioSummaryEventEnum) => void>,
      default: () => {}
    },
    onLeaveMessage: {
      type: Function as PropType<(event: AIAudioSummaryEventEnum) => void>,
      default: () => {}
    }
  },
  data() {
    return {
      loading: false,
      voiceIconUrl: getLocalesOssUrl('/ai/ai_voice.png'),
      showSummaryOverlay: false
    }
  },
  computed: {
    url(): string {
      if (this.isCall) {
        return this.callDetail?.recordFile || ''
      }
      return this.callDetail?.leaveMessageFileUrl || ''
    },
    callRecordContent(): Record<string, any> {
      return parse_with_default_value(this.callDetail?.callRecordContent, {})
    },
    leaveMessageContent(): Record<string, any> {
      return parse_with_default_value(this.callDetail?.leaveMessageContent, {})
    },
    recordFileSummary(): string {
      return this.callRecordContent?.summary || ''
    },
    leaveMessageSummary(): string {
      return this.leaveMessageContent?.summary || ''
    },
    summary(): string {
      return this.isCall ? this.recordFileSummary : this.leaveMessageSummary
    },
    recordFileText(): Record<string, any>[] {
      return parse_with_default_value(this.callRecordContent?.content, [])
    },
    leaveMessageText(): Record<string, any>[] {
      return parse_with_default_value(this.leaveMessageContent?.content, [])
    },
    originalTexts(): Record<string, any>[] {
      return this.isCall ? this.recordFileText : this.leaveMessageText
    },
    tag(): string {
      return this.isCall ? AIAudioSummaryTagEnum.CALL_RECORD_SUMMARY : AIAudioSummaryTagEnum.LEAVE_MESSAGE_SUMMARY
    }
  },
  methods: {
    fetchSummary() {
      
      const params: AIVocAgentDetailSummaryParamType = {
        tag: this.tag,
        recordId: this.callDetail.recordId,
        url: this.url
      }

      this.loading = true

      vocAgentDetailSummary(params)
        .then((res) => {

          const isFail = MsgModel.isFail(res)
          if (isFail) {
            const message = MsgModel.getMessage(res)
            this.$message.error(message)
            return
          }

          const data = MsgModel.getData(res, {})

          if (this.isCall) {
            this.$emit(AIAudioSummaryEventEnum.CALL_RECORD, data)
          } else {
            this.$emit(AIAudioSummaryEventEnum.LEAVE_MESSAGE, data)
          }

        })
        .finally(() => {
          this.loading = false
        })

    },
    handleSummary() {
      this.fetchSummary()
    },
    handleSummaryToggle() {
      this.showSummaryOverlay = !this.showSummaryOverlay
    },
    renderHeaderWrapper() {
      return (
        <div class="ai-audio-summary-header-wrapper">
          <div class="ai-audio-summary-title">
            通话录音：
          </div>
          {this.renderAudio()}
        </div>
      )
    },
    renderAudio() {
      return (
        <audio 
          src={this.url} 
          // @ts-ignore
          controls="controls" 
          // @ts-ignore
          preload 
          style="height:36px;outline: 0;"
        >
        </audio>
      )
    },
    renderSummaryButton() {
      const hasSummary = !isEmpty(this.summary)
      if (!hasSummary) {
        return null
      }

      return (
        <el-button
          type="text"
          class="ai-audio-summary-toggle-btn"
          onClick={this.handleSummaryToggle}
        >
          会话摘要
          <i class={this.showSummaryOverlay ? 'el-icon-arrow-up' : 'el-icon-arrow-down'}></i>
        </el-button>
      )
    },
    renderContent() {
      return (
        <div class="ai-audio-summary-content">
          {this.renderSummaryOverlay()}
          {this.renderOriginal()}
        </div>
      )
    },
    renderSummaryOverlay() {
      const hasSummary = !isEmpty(this.summary)
      if (!hasSummary || !this.showSummaryOverlay) {
        return null
      }

      return (
        <div class="ai-audio-summary-overlay">
          <div class="ai-audio-summary-overlay-content">
            <div class="ai-audio-summary-overlay-header">
              <span>沟通内容：</span>
              <div class="ai-audio-summary-overlay-actions">
                <el-button
                  type="text"
                  size="mini"
                  onClick={this.handleSummary}
                >
                  <i class="el-icon-refresh"></i>
                  重新生成
                </el-button>
                <el-button
                  type="text"
                  size="mini"
                >
                  <i class="el-icon-document-copy"></i>
                  复制
                </el-button>
              </div>
            </div>
            <div class="ai-md-content">
              <VueMarkdown>
                {this.summary}
              </VueMarkdown>
            </div>
            <div class="ai-audio-summary-overlay-footer">
              以上信息由"智能VOC"智能体生成
            </div>
          </div>
        </div>
      )
    },

    renderOriginal() {

      const isEmptyText = isEmpty(this.originalTexts)
      if (isEmptyText) {
        return this.renderOriginalTranscript()
      }

      return (
        <div>
          {this.originalTexts.map(this.renderOriginalItem)}
        </div>
      )
    },
    renderOriginalTranscript() {

      const button = (
        <el-button type="primary" onClick={this.handleSummary}>
          <img 
            src={this.voiceIconUrl}
            class="ai-audio-summary-voice-icon"
          />
          <span>
            &nbsp;
          </span>
          <span>
            点击语音转为文字
          </span>
        </el-button>
      )
      const loading = (
        <span class="ai-audio-summary-transcript-loading">
          正在转换中...
        </span>
      )

      const content = this.loading ? loading : button

      return (
        <div class="ai-audio-summary-transcript">
          {content}
        </div>
      )
    },
    renderOriginalItem(item: Record<string, any>) {

      const isServiceUser = item.speaker === '客服'
      const speaker = item.speaker === '客户'
      const className = isServiceUser ? 'ai-audio-summary-transcript-service-user' : 'ai-audio-summary-transcript-customer'

      return (
        <div class="ai-audio-summary-transcript-item">
          <span class={className}>
            {item.speaker} : &nbsp;
          </span>
          <span>
            {item.text}
          </span>
        </div>
      )
    },
    renderMain() {
      return (
        <div class="ai-audio-summary-main">
          {this.renderHeaderWrapper()}
          {this.renderContent()}
          {this.renderSummaryButton()}
        </div>
      )
    }
  },
  render() {
    return (
      <div class="ai-audio-summary">
        {this.renderMain()}
      </div>
    )
  }
})
