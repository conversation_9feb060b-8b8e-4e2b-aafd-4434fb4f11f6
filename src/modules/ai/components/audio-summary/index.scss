
// 原文容器
.ai-audio-original-container {
  margin-bottom: 12px;
}

.ai-audio-original-content {
  height: 156px;
  max-height: 240px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  padding: 12px;
  gap: 12px;
  border-radius: 4px;
  background: #F5F8FA;
  border: 1px solid #E4E7ED;
}

// 摘要容器
.ai-audio-summary-container {
  border: 1px solid #E4E7ED;
  border-radius: 4px;
  background: #FFF;
}

.ai-audio-summary-header {
  padding: 0;
  border-bottom: 1px solid #E4E7ED;
}

.ai-audio-summary-content-wrapper {
  padding: 12px;

  .ai-md-content {
    p {
      color: #262626 !important;
    }
  }
}

.ai-audio-summary-content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;

  span {
    font-size: 14px;
    color: #262626;
    font-weight: 500;
  }
}

.ai-audio-summary-actions {
  display: flex;
  gap: 8px;

  .el-button {
    padding: 4px 8px;
    font-size: 12px;

    i {
      margin-right: 4px;
    }
  }
}

.ai-audio-summary-footer {
  margin-top: 12px;
  font-size: 12px;
  color: #999;
  text-align: left;
}

.ai-audio-summary-header-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.ai-audio-summary-title {
  font-size: 14px;
  color: #262626;
  font-weight: 500;
}

.ai-audio-summary-toggle-btn {
  width: 100%;
  padding: 12px;
  text-align: center;
  color: #FF6B9D !important;
  border: none;
  background: transparent;

  &:hover {
    color: #FF6B9D !important;
    background: #FFF9FC;
  }

  &:focus {
    color: #FF6B9D !important;
  }

  i {
    margin-left: 4px;
  }
}

.ai-audio-summary {

  margin-top: 12px;



  .ai-audio-summary-transcript {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    .el-button {
      padding-right: 24px;
      border-radius: 32px;
    }
    .ai-audio-summary-transcript-loading {
      color: #9c9c9c;
      font-size: 12px;
    }
  }

}

.ai-audio-summary-transcript-item {
  margin-bottom: 6px;
}

.ai-audio-summary-transcript-service-user,
.ai-audio-summary-transcript-customer {
  font-weight: 500;
}

.ai-audio-summary-transcript-service-user {
  color: #262626;
}

.ai-audio-summary-transcript-customer {
  color: #08979C;
}

.ai-audio-summary-voice-icon {
  width: 16px;
  height: 16px;
  transform: scale(1.4);
  margin-bottom: 2px;
}