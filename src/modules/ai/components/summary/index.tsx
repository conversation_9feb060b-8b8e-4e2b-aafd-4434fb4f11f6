/* vue */
import { defineComponent, PropType } from 'vue'
/* enum */
import ComponentNameEnum from '@model/enum/ComponentNameEnum'
/* model */
import Field from '@model/entity/Field'
/* components */
import AiSummaryDialog from '@src/modules/ai/components/summary/dialog'
import AiSummaryButton from '@src/modules/ai/components/summary/button'
import AiTaskSummary from '@src/modules/ai/components/ai-task-summary/index'
/* scss */
import "@src/modules/ai/components/summary/index.scss"
/* util */
import { otherFieldsToSummaryFields } from '@src/modules/ai/components/summary/util'
/* type */
import { SummaryField } from '@src/modules/ai/components/summary/type'
import { AIAgentTemplateEnum } from '@src/modules/ai/model/enum'

type AiSummaryDialogComponent = InstanceType<typeof AiSummaryDialog>
type AiTaskSummaryComponent = InstanceType<typeof AiTaskSummary>

export default defineComponent({
  name: ComponentNameEnum.AiSummary,
  props: {
    fields: {
      type: Array as PropType<Field[]>,
      default: () => []
    },
    multipleSelection: {
      type: Array as PropType<Record<string, any>[]>,
      default: () => []
    },
    template: {
      type: String as PropType<AIAgentTemplateEnum>,
      default: ''
    },
    isNew: {
      type: Boolean,
      default: false
    },
    taskTypesMap: {
      type: Object as PropType<Record<string, any>>,
      default: () => {}
    }
  },
  computed: {
    aiSummaryDialog(): AiSummaryDialogComponent {
      return this.$refs.AiSummaryDialog as AiSummaryDialogComponent
    },
    aiTaskSummary(): AiTaskSummaryComponent {
      return this.$refs.AiTaskSummary as AiTaskSummaryComponent
    },
    summaryFields(): SummaryField[] {
      return otherFieldsToSummaryFields(this.fields)
    }
  },
  methods: {
    handleConfirm(summaryText: string) {
      this.handleClose()
    },
    handleClose() {
      this.aiSummaryDialog.closeDialog()
    },
    handleClick() {

      if (this.multipleSelection.length === 0) {
        this.$message.warning('请先选择需要摘要的数据')
        return
      }

      if (this.isNew) {
        this.aiTaskSummary.openDialog()
        return
      }
      
      this.aiSummaryDialog.openDialog()

    },
    renderDialog() {
      return (
        <AiSummaryDialog 
          ref="AiSummaryDialog"
          multipleSelection={this.multipleSelection}
          fields={this.summaryFields}
          template={this.template}
          onConfirm={this.handleConfirm}
        />
      )
    },
    renderNewDialogWrapper() {
      if (this.isNew) {
        return this.renderNewDialog()
      }
      return null
    },
    renderNewDialog() {
      return (
        <AiTaskSummary
          ref="AiTaskSummary"
          template={this.template}
          taskList={this.multipleSelection}
          fields={this.summaryFields}
          taskTypesMap={this.taskTypesMap}
        />
      )
    },
    renderButton() {
      return (
        <AiSummaryButton 
          onClick={this.handleClick}
        />
      )
    }
  },
  render() {
    return (
      <div class="ai-summary">
        {this.renderButton()}
        {this.renderDialog()}
        {this.renderNewDialogWrapper()}
      </div>
    )
  }
})
