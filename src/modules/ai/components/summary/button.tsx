/* components */
import ComponentNameEnum from "@model/enum/ComponentNameEnum"
import SettingGPTViewDialog from '@gpt/components/dialog'
/* vue */
import { defineComponent, PropType } from "vue"
/* assets */
// @ts-ignore
import AMagicFull from '@src/assets/img/icon-magic-full.gif'
/* scss */
import "@src/modules/ai/components/summary/button.scss"
/* util */
import { getLocalesOssUrl } from "@src/util/assets"

enum AISummaryEventEnum {
  CLICK = 'click'
}

export default defineComponent({
  name: ComponentNameEnum.AiSummaryButton,
  props: {
    value: {
      type: Boolean,
      default: false
    },
    onClick: {
      type: Function,
    }
  },
  data() {
    return {
    }
  },
  computed: {
  },
  methods: {
    onClickHandle() {
      this.$emit(AISummaryEventEnum.CLICK)
    },
    renderButton() {

      const iconUrl = AMagicFull

      return (
        <el-button
          onClick={this.onClickHandle}
        >
          <img 
            class="ai-summary-button-icon"
            src={iconUrl} 
            alt="AI 摘要" 
          />
          <span>
            &nbsp;
          </span>
          <span class="ai-summary-button-text">
            AI 摘要
          </span>
        </el-button>
      )
    }
  },
  render() {
    return (
      <div class="ai-summary-button">
        {this.renderButton()}
      </div>
    )
  }
}) 