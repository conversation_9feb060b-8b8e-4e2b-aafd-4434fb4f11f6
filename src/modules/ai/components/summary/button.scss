
.ai-summary-button-icon {
  width: 16px;
  height: 16px;
  margin-bottom: 2px;
}

.ai-summary-button {
  .el-button {
    height: 32px;
    background: linear-gradient(121deg, rgba(255, 118, 44, 0.1) -5%, rgba(206, 43, 243, 0.063) 30%, rgba(32, 116, 241, 0.1) 104%);
    box-sizing: border-box;
    border-radius: 4px;
    position: relative;
    padding: 0 12px;
    border: 1px solid #CBD6E2;
    display: flex;
    justify-content: center;
    align-items: center;
    &:focus,
    &:hover {
      background-color: unset !important;
      border-color: #CBD6E2 !important;
    }
    & > span {
      display: flex;
      align-items: center;
    }
  }
  .ai-summary-button-text {
    background: linear-gradient(128deg, #FF762C -9%, #CE2BF3 29%, #2074F1 102%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}