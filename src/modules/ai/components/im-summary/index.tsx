/* components */
// @ts-ignore
import VueMarkdown from 'vue-markdown'
import chattingRecords from '@src/modules/im/imChat/components/messageBox/chattingRecords/index.vue'
/* vue */
import { defineComponent, PropType } from "vue"
/* scss */
import "@src/modules/ai/components/im-summary/index.scss"
/* api */ 
import { imAgentDetailSummary, vocAgentDetailSummary } from "@src/api/AIv2API"
/* utils */
import { isEmpty, isFalsy } from "pub-bbx-utils"
import { parse, parse_with_default_value } from "@src/util/lang/object"
import { getLocalesOssUrl } from "@src/util/assets"
import { copyText } from '@src/util/dom'
/* model */
import { AIAudioSummaryTagEnum } from "@src/modules/ai/components/audio-summary/model"
import { AIImAgentDetailSummaryParamType, AIVocAgentDetailSummaryParamType } from "@src/modules/ai/model/param"
import MsgModel from "@model/MsgModel"
import { AIImAgentDetailSummaryResultType } from '@src/modules/ai/model/result'

enum ImSummaryTabEnum {
  SUMMARY = 'summary',
  ORIGINAL = 'original'
}

enum AIAudioSummaryEventEnum {
  CALL_RECORD = 'callRecord',
  LEAVE_MESSAGE = 'leaveMessage'
}

export default defineComponent({
  name: 'AIImSummary',
  components: {
    VueMarkdown,
    chattingRecords
  },
  props: {
    callDetail: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({})
    },
    isCall: {
      type: Boolean as PropType<boolean>,
      default: true
    },
    imMessageInfoList: {
      type: Array as PropType<Record<string, any>[]>,
      default: () => []
    },
    showMore: {
      type: Boolean as PropType<boolean>,
      default: false
    },
    userDetail: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({})
    },
    conversationNumber: {
      type: String as PropType<string>,
      default: ''
    },
    conversationId: {
      type: String as PropType<string>,
      default: ''
    },
    source: {
      type: String as PropType<string>,
      default: ''
    },
    imConversationInfo: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({})
    },
    onCallRecord: {
      type: Function as PropType<(event: AIAudioSummaryEventEnum) => void>,
      default: () => {}
    },
    onLeaveMessage: {
      type: Function as PropType<(event: AIAudioSummaryEventEnum) => void>,
      default: () => {}
    },
    rightActiveTab: {
      type: String as PropType<string>,
      default: ''
    }
  },
  data() {
    return {
      activeTab: ImSummaryTabEnum.SUMMARY,
      loading: false,
      voiceIconUrl: getLocalesOssUrl('/ai/ai_voice.png'),
      summary: '',
      toggleButtonPosition: 'top',
      isFirstShow: true
    }
  },
  watch: {
    imConversationInfo: {
      handler(newVal) {
        this.summary = newVal?.conversationSummary || ''
      },
      immediate: true
    },
    rightActiveTab: {
      handler(newVal) {
        if (newVal == 2 && this.isFirstShow && this.imMessageInfoList.length > 0 && !this.summary) {
          this.isFirstShow = false
          this.handleSummary()
        }
      },
      immediate: true
    }
  },
  methods: {
    fetchSummary() {
      
      const params: AIImAgentDetailSummaryParamType = {
        conversationId: this.conversationId
      }

      this.loading = true
      
      imAgentDetailSummary(params)
        .then((res) => {

          const isFail = MsgModel.isFail(res)
          if (isFail) {
            const message = MsgModel.getMessage(res)
            this.$message.error(message)
            return
          }

          const data = MsgModel.getData(res, {}) as AIImAgentDetailSummaryResultType
          this.summary = data?.summary || ""

        })
        .finally(() => {
          this.loading = false
        })

    },
    handleSummary() {
      this.summary = ''
      this.fetchSummary()
    },
    handleTabChange(tab: string) {
      this.activeTab = tab as ImSummaryTabEnum
    },
    onLoadMoreMsgs() {
      this.$emit('loadMoreMsgs')
    },
    handleAddRemark() {
      this.$emit('addRemark', this.summary)
    },
    renderTabWrapper() {
      return (
        <div class="ai-im-summary-tab-wrapper">
          {this.renderTab()}
        </div>
      )
    },
    renderTab() {
      return (
        <el-radio-group value={this.activeTab} onInput={this.handleTabChange}>
          <el-radio-button label={ImSummaryTabEnum.SUMMARY}>
            <span>
              会话摘要
            </span>
          </el-radio-button>
          <el-radio-button label={ImSummaryTabEnum.ORIGINAL}>
            <span>
              会话原文
            </span>
          </el-radio-button>
        </el-radio-group>
      )
    },
    renderContent() {
      const isTop = this.toggleButtonPosition === 'top'
      const isShow = this.imMessageInfoList.length > 0
      return (
        <div class="ai-im-summary-content">
          {isTop && isShow && this.renderToggleButton()}
          {this.renderOriginalWrapper()}
          {
            !isTop && 
            <div class="ai-im-summary-content-summary-wrapper">
              {this.renderSummaryWrapper()}
            </div>
          }
        </div>
      )
    },
    renderToggleButton() {
      const isTop = this.toggleButtonPosition === 'top'
      const icon = !isTop ? 'el-icon-arrow-up' : 'el-icon-arrow-down'
      return ( 
        <div class={`ai-im-summary-toggle-button-wrapper ai-im-summary-toggle-button-wrapper-${isTop ? 'top' : 'bottom'}`} onClick={this.handleToggleButton}>
          <div class={`ai-im-summary-toggle-button ai-im-summary-toggle-button-${isTop ? 'top' : 'bottom'}`}>
            <span>
              会话摘要
            </span>
            <i class={icon}></i>
          </div>
        </div>
      )
    },
    handleToggleButton() {
      this.toggleButtonPosition = this.toggleButtonPosition === 'top' ? 'bottom' : 'top'
    },
    renderAddRemark() {
      return (
        <div class="ai-im-summary-add-remark">
          以上信息由“智能VOC”智能体生成
        </div>
      )
    },
    renderSummaryWrapper() {
      return (
        <div>
          {this.renderSummary()}
          {this.toggleButtonPosition === 'bottom' && this.renderToggleButton()}
        </div>
      )
    },
    renderOriginalWrapper() {
      return this.renderOriginal()
    },
    renderSummary() {

      const isEmptySummary = isEmpty(this.summary)
      if (isEmptySummary) {
        return this.renderSummaryTranscript()
      }

      return (
        <div class="ai-md-content">
          <VueMarkdown>
            {this.summary}
          </VueMarkdown>
          {this.renderOperationButtons()}
          {this.renderAddRemark()}
        </div>
      )

    },
    renderOperationButtons() {
      return (
        <div class="ai-im-summary-operation-buttons">
          {/* 重新生成 */}
          <div class="ai-im-summary-operation-button" onClick={this.handleRegenerate}>
            <i class="icon el-icon-refresh"></i>
            重新生成
          </div>
          {/* 复制 */}
          <div class="ai-im-summary-operation-button" onClick={this.handleCopy}>
            <i class="icon el-icon-copy-document"></i>
            复制
          </div>
          {/* 将摘要填入服务备注 */}
          <div class="ai-im-summary-operation-button" onClick={this.handleAddRemark}>
            <i class="icon el-icon-edit-outline"></i>
            填入服务备注
          </div>
        </div>
      ) 
    },
    handleRegenerate() {
      this.handleSummary()
    },
    handleCopy() {
      copyText(this.summary).then(() => {
        this.$message.success('复制成功')
      })
    },
    renderSummaryTranscript() {

      const isLoading = this.loading
      const button = (
        <el-button type="primary" onClick={this.handleSummary}>
          <img 
            src={this.voiceIconUrl}
            class="ai-im-summary-voice-icon"
          />
          <span>
            &nbsp;
          </span>
          <span>
            请先摘要会话记录
          </span>
        </el-button>
      )
      const loading = (
        <span class="ai-im-summary-transcript-loading">
          正在提炼内容...
        </span>
      )

      const content = isLoading ? loading : button

      return (
        <div class="ai-im-summary-transcript">
          {content}
        </div>
      )
    },
    renderOriginal() {
      return (
        <chattingRecords
          msg-list={this.imMessageInfoList}
          is-show-more={this.showMore}
          source={this.source}
          onLoadMoreMsgs={this.onLoadMoreMsgs}
          ref="msgContent"
          user-detail={this.userDetail}
          conversation-number={this.conversationNumber}
        />
      )
    },
    renderOriginalTranscript() {

      const button = (
        <el-button type="primary" onClick={this.handleSummary}>
          <img 
            src={this.voiceIconUrl}
            class="ai-im-summary-voice-icon"
          />
          <span>
            &nbsp;
          </span>
          <span>
            点击语音转为文字
          </span>
        </el-button>
      )
      const loading = (
        <span class="ai-im-summary-transcript-loading">
          正在转换中...
        </span>
      )

      const content = this.loading ? loading : button

      return (
        <div class="ai-im-summary-transcript">
          {content}
        </div>
      )
    },
    renderOriginalItem(item: Record<string, any>) {

      const isServiceUser = item.speaker === '客服'
      const speaker = item.speaker === '客户'
      const className = isServiceUser ? 'ai-im-summary-transcript-service-user' : 'ai-im-summary-transcript-customer'

      return (
        <div class="ai-im-summary-transcript-item">
          <span class={className}>
            {item.speaker} : &nbsp;
          </span>
          <span>
            {item.text}
          </span>
        </div>
      )
    },
    renderMain() {
      return (
        <div class="ai-im-summary-main">
          {this.renderContent()}
        </div>
      )
    }
  },
  render() {
    return (
      <div class="ai-im-summary">
        {this.renderMain()}
      </div>
    )
  }
})
