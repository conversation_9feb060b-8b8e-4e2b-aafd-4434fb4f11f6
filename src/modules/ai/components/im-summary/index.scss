.ai-im-summary-content {
  height: 100%;
  gap: 12px;
  border-radius: 0px 0px 4px 4px;
  background: #F5F8FA;
  border-width: 1px;
  border-style: solid;
  border-color: #E4E7ED;
  overflow-y: auto;
  .messagebox-container {
    overflow-y: initial;
    .default {
      background: #F5F8FA !important;
    }
  }
}

.ai-im-summary-tab-wrapper {
  display: flex;
  justify-content: center;
  margin-bottom: 24px;
}

.ai-im-summary {
  position: relative;
  height: 100%;

  &-main {
    height: 100%;
    height: 100%;
  }
  .el-tabs {
    width: fit-content;
  }

  .el-tabs__header {
    border-bottom: none;
  }

  .el-tabs__content {
    display: none;
  }
  
  .el-tabs--border-card {
    box-shadow: none;
  }

  .el-tabs__item:nth-last-of-type(1) {
    border-right: none;
  }

  .ai-im-summary-transcript {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 60px;
    .el-button {
      padding-right: 24px;
      border-radius: 32px;
    }
    .ai-im-summary-transcript-loading {
      color: #9c9c9c;
      font-size: 12px;
    }
  }

}
.ai-im-summary-toggle-button-wrapper {
  position: sticky;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
  &-top {
    top: 0;
    padding-bottom: 12px;
  }
  &-bottom {
    bottom: 0;
    padding-top: 36px;
  }
}
.ai-im-summary-toggle-button {
  width: 108px;
  height: 34px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 6px 16px;
  gap: 4px;
  background: linear-gradient(116deg, rgba(255, 118, 44, 0.1) -4%, rgba(206, 43, 243, 0.063) 30%, rgba(32, 116, 241, 0.1) 103%);
  border-image: conic-gradient(from 180deg at 50% 50%, #00C1DF 0deg, #2B22DB 113deg, #CF65D9 234deg, #00C1DF 360deg) 0;
  box-sizing: border-box;
  border: 0px solid;
  z-index: 99;
  cursor: pointer;
  &-top {
    border-radius: 0px 0px 8px 8px;
  }
  &-bottom {
    border-radius: 8px 8px 0px 0px;
  }
}

.ai-im-summary-content-summary-wrapper {
  overflow-y: auto;
  position: absolute;
  width: calc(100% - 24px);
  max-height: 90%;
  left: 50%;
  transform: translateX(-50%);
  top: 0;
  background: #F5F8FA;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
  border-radius: 0px 0px 12px 12px;
  padding: 24px;
  padding-bottom: 0px;
  z-index: 99;
}

.ai-im-summary-operation-buttons {
  display: flex;
  .ai-im-summary-operation-button {
    color: #595959 !important;
    margin-right: 14px;
    cursor: pointer;
    .icon {
      margin-right: 4px;
    }
  }
}

.ai-im-summary-transcript-item {
  margin-bottom: 6px;
}

.ai-im-summary-transcript-service-user,
.ai-im-summary-transcript-customer {
  font-weight: 500;
}

.ai-im-summary-transcript-service-user {
  color: #262626;
}

.ai-im-summary-transcript-customer {
  color: #08979C;
}

.ai-im-summary-voice-icon {
  width: 16px;
  height: 16px;
  transform: scale(1.4);
  margin-bottom: 2px;
}

.ai-im-summary-add-remark {
  font-size: 12px;
  margin-top: 12px;
  color: #8c8c8c;
}