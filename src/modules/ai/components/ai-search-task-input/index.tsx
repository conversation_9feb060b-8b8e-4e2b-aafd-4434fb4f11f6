/* vue */
import { defineComponent, PropType } from 'vue'
/* enum */
import ComponentNameEnum from '@model/enum/ComponentNameEnum'
/* scss */
import "@src/modules/ai/components/ai-search-task-input/index.scss"
import 'shb-ai-chat-md/index.scss'
/* api */
import { aiTaskSearch } from '@src/api/TaskApi'
/* util */
import { formatDate, getRootWindow, isEmpty, isFalsy } from 'pub-bbx-utils'
import MsgModel from '@model/MsgModel'
import i18n from '@src/locales'
import { safeNewDate } from '@src/util/time'
import { renderMarkdown } from "shb-ai-chat-md"
/* image */
// @ts-ignore
import aiAnalyzeLogo from '@src/assets/img/ai_analyze_logo.gif'
import { userCenterGetTokenInfo } from '@src/util/userCenter'
import { isJSONObject, parse_with_default_value } from '@src/util/lang/object'
import Page from '@model/Page'
import { getLocalesOssUrl } from '@src/util/assets'

enum AiSearchInputEventEnum {
  BeforeSearch = 'beforeSearch',
  AfterSearch = 'afterSearch',
  SearchError = 'searchError',
  SearchCatch = 'searchCatch',
  SearchFinally = 'searchFinally',
  SearchSuccess = 'searchSuccess',
  Close = 'close',
}

export default defineComponent({
  name: ComponentNameEnum.AiSearchTaskInput,
  props: {
    isHaveNodeFlowAuth: {
      type: Boolean,
      default: false
    },
    planTimeType: {
      type: String,
      default: ''
    },
    planStartTimeType: {
      type: String,
      default: ''
    },
    planEndTimeType: {
      type: String,
      default: ''
    },
    currentPage: {
      type: Object as PropType<Page>,
      default: () => new Page()
    },
    onBeforeSearch: {
      type: Function
    },
    onAfterSearch: {
      type: Function
    },
    onSearchError: {
      type: Function
    },
    onSearchCatch: {
      type: Function
    },
    onSearchFinally: {
      type: Function
    },
    onSearchSuccess: {
      type: Function
    },
    onClose: {
      type: Function
    }
  },
  data() {
    return {
      isShow: false,
      aiInputText: '',
      messageText: '',
      loading: false,
      aisSearchLoading: false,
      aiSearchLoad: false,
      aiSearchSuccess: false,
      isShowAiSearchThink: false,
      content: '',
      searchResultList: [],
      searchTaskResult: {
        pageData: {},
        searchParam: {}
      } as {
        pageData: Page<Record<string, any>>
        searchParam: Record<string, any>
      },
      isShowSearchResultView: false,
      searchSuccessVideo: getLocalesOssUrl('/ai/ai_success.mp4'),
      timeouts: [] as number[]
    }
  },
  computed: {
    isLoading() {
      return this.loading || this.aisSearchLoading
    },
    iconClass() {
      return ['iconfont', 'icon-fasong', this.aiInputText.length === 0 || this.isLoading ? 'disabled' : null]
    },
    contentHTML(): string {
      try {
        return renderMarkdown(this.content || '')
      } catch (error) {
        return this.content || ''
      }
    },
    isShowPrefix() {
      return (
        this.aisSearchLoading 
        || this.aiSearchSuccess
      )
    },
    inputBoxClass() {
      return {
        'input-box': true,
        'input-box-prefix': this.isShowPrefix
      }
    }
  },
  mounted() {
    // document.addEventListener('click', this.handlerClickOutside)
  },
  beforeDestroy() {
    // document.removeEventListener('click', this.handlerClickOutside)
  },
  methods: {
    handlerClickOutside(event: MouseEvent) {
      const target = event.target as HTMLElement
       
      if (!this.aiSearchSuccess) {
        return
      }

      // 如果点击的元素不是 ai-search-task-think 的子元素，则关闭 ai-search-task-think
      // 这个元素 ai-search-task-input-box 及子元素除外
      if (!target.closest('.ai-search-task-think') && !target.closest('.ai-search-task-input-box')) {
        this.closeSearchResultView()
      }
      
    },
    outsideGetInputText() {
      return this.aiInputText
    },
    outsideGetSearchParam() {
      return this.searchTaskResult?.searchParam || {}
    },
    handlerClear() {
      this.aiInputText = ''
    },
    handlerInput(value: string) {

      this.aiInputText = value

      if (value.length <= 0) {
        this.handlerClear()
      }

      if (this.aiSearchSuccess) {
        this.aiSearchSuccess = false
        this.isShowSearchResultView = false
        this.isShowAiSearchThink = false
      }
      
    },
    handlerSearch() {
      this.fetchAiSearch()
    },
    handlerClose() {
      this.isShow = false
      this.emitClose()
    },
    handlerKeyup(event: KeyboardEvent) {
      if (event.key === 'Enter') {
        this.handlerSearch()
      }
    },
    async fetchAiSearch() {
      try {

        // 如果输入框为空，则不进行搜索
        if (isEmpty(this.aiInputText)) {
          console.log('aiInputText is empty')
          return
        }
        
        this.content = ''

        // 发送搜索前的回调
        this.emitBeforeSearch()

        const pageNum = this.currentPage?.pageNum || 1
        const pageSize = this.currentPage?.pageSize || 10

        const aiParams = {
          page: pageNum,
          pageSize: pageSize,
          input: this.aiInputText,
        }

        this.messageText = this.aiInputText
        this.aiSearchSuccess = false
        this.aisSearchLoading = true
        this.aiSearchLoad = true
        this.isShowAiSearchThink = true
        
        const result = await this.sendMessageWithStream(aiParams)

        console.log('result', result)

        const isFail = isEmpty(result)
        if (isFail) {
          const errorMessage = '网络异常，请稍后再试'
          this.$message.error(errorMessage)
          this.emitSearchError(errorMessage)
          return;
        }

        // 处理搜索结果
        this.postProcessResult()
        
        this.aiSearchSuccess = true

        const timeoutScrollMessageListToBottom = setTimeout(() => {
          this.scrollMessageListToBottom()
        })
        
        const timeoutCloseSearchResultView = setTimeout(() => {
          this.isShowSearchResultView = false
          this.isShowAiSearchThink = false
        }, 5000)

        this.timeouts.push(timeoutScrollMessageListToBottom)
        this.timeouts.push(timeoutCloseSearchResultView)

      } catch (error) {
        this.emitSearchCatch()
      } finally {
        this.aisSearchLoading = false
        this.emitSearchFinally()
      }
    },
    async sendMessageWithStream(params: Record<string, any>) {

      const controller = new AbortController();

      let eventCount = 0;
      // 添加缓冲区存储不完整的数据
      let buffer = '';
      let text = ''

      const url = '/api/elasticsearch/outside/ai/task/search_v2'
      
      try {
        const tokenInfo = userCenterGetTokenInfo() as Record<string, any>
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'token': tokenInfo?.token || '',
          },
          signal: controller.signal,
          body: JSON.stringify(params),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const reader = response.body?.getReader();
        if (!reader) {
          throw new Error('Response body is null');
        }

        const decoder = new TextDecoder();
        let isFirst = true
        const endThinkTagText = '</think>'

        while (true) {

          const { value, done } = await reader.read();

          if (done) {
            console.log(`流式响应结束，共收到 ${eventCount} 条消息`);
            break;
          }

          // 解码当前数据块并添加到buffer
          buffer += decoder.decode(value, { stream: true });

          try {
            if (isFirst) {
              const data = decoder.decode(value, { stream: true })
              const isErrorJSON = isJSONObject(data)
              if (isErrorJSON) {
                const error = parse_with_default_value(data, {} as Record<string, any>)
                const isFail = MsgModel.isFail(error)
                if (isFail) {
                  this.$message.error(error?.message || '网络异常，请稍后再试')
                  this.aisSearchLoading = false
                  this.aiSearchLoad = false
                  this.aiSearchSuccess = false
                  return
                }
              }
            }
          } catch (error) {
            console.warn(error)
          } finally {
            isFirst = false
          }

          // 按行分割处理数据
          const lines = buffer.split('\n');

          // 保留最后一行（可能不完整）
          buffer = lines.pop() || '';

          // 处理完整的行
          for (const line of lines) {
            
            // 跳过空行
            if (line.trim() === '') {
              continue;
            }

            if (!line.startsWith('data:')) {
              continue;
            }

            eventCount++;
            const message = line.replace(/^data:\s*/, '').trim();
            
            console.log(`第 ${eventCount} 条消息:`, message);

            if (!message) {
              continue;
            }

            const messageObj = parse_with_default_value(message, {} as Record<string, any>)
            const event = messageObj.event || ''
            
            if (!event || event == 'text_chunk') {
              // 更新助理消息
              let textValue = messageObj.data.text || ''

              // 如果  textValue 包含 </think>, 则在 </think> 前面加一个空的 div
              if (textValue.includes(endThinkTagText)) {
                const endThinkTagIndex = textValue.indexOf(endThinkTagText)
                const before = textValue.slice(0, endThinkTagIndex)
                const after = textValue.slice(endThinkTagIndex)
                textValue = before + `<div></div>` + after
              }

              text += textValue
              this.content = text;
              // 滚动消息列表到底部
              this.scrollMessageListToBottom();
              setTimeout(() => {
                this.scrollMessageListToBottom();
              })
            } 
            else if (event == 'search_result') {
              this.searchTaskResult = messageObj as any
              this.isShowSearchResultView = true
            }

          }
        }

      } catch (error) {
        console.error('sendMessageWithStream error:', error);
      }

      return text;
    },
    scrollMessageListToBottom() {
      const thinkContent = this.$refs.AiSearchThink as HTMLElement
      if (thinkContent) {
        thinkContent.scrollTop = 100000
      }
    },
    emitSearchError(error: string) {
      this.$emit(AiSearchInputEventEnum.SearchError, error)
    },
    emitBeforeSearch() {
      this.$emit(AiSearchInputEventEnum.BeforeSearch)
    },
    emitAfterSearch() {
      this.$emit(AiSearchInputEventEnum.AfterSearch)
    },
    emitSearchCatch() {
      this.$emit(AiSearchInputEventEnum.SearchCatch)
    },
    emitSearchFinally() {
      this.$emit(AiSearchInputEventEnum.SearchFinally)
    },
    emitSearchSuccess(data: Record<string, any>) {
      this.$emit(AiSearchInputEventEnum.SearchSuccess, data)
    },
    emitClose() {
      this.$emit(AiSearchInputEventEnum.Close)
    },
    postProcessResult() {

      console.log('postProcessResult', this.searchTaskResult)
      
      let data = this.searchTaskResult?.pageData || {};
      let { number, content = [], totalPages, totalElements, size } = data;

      data.total = totalPages || 1;
      data.pageNum = number || 1;
      data.pageSize = size

      const pageContent = content || [];
      data.list = pageContent.map((item: Record<string, any>) => {

        item.pending = false;
        item.acceptUsedTime = this.timestamp(item.acceptUsedTime);
        item.taskUsedTime = this.timestamp(item.taskUsedTime);
        item.workUsedTime = this.timestamp(item.workUsedTime);
        item.taskResponseTime = this.timestamp(item.taskResponseTime);
        item.createToCompleteUsedTime = this.timestamp(item.createToCompleteUsedTime);

        // 草稿状态处理
        if (item.isDelete == 5) {
          item.state = 'draft'
        }

        if (item.planTime && this.planTimeType === 'date') {
          item.planTime = formatDate(safeNewDate(item.planTime), 'YYYY-MM-DD');
        }

        if (item.planStartTime && this.planStartTimeType === 'date') {
          item.planStartTime = formatDate(safeNewDate(item.planStartTime), 'YYYY-MM-DD');
        }

        if (item.planEndTime && this.planEndTimeType === 'date') {
          item.planEndTime = formatDate(safeNewDate(item.planEndTime), 'YYYY-MM-DD');
        }

        return item;

      });

      // 合并节点负责人
      if (this.isHaveNodeFlowAuth) {
        pageContent.forEach((item: Record<string, any>) => {
          const { attribute: { nodeHandlingPerson = {} } = {} } = item
          Object.assign(item, nodeHandlingPerson)
        })
      }

      // 发送搜索成功回调
      this.emitSearchSuccess(data)

    },
    timestamp(value: any) {
      if (typeof value === 'number') {
        // @ts-ignore
        let h = value / 3600 < 0 ? 0 : parseInt((value as any) / 3600)
        let m;
        if (h > 0) {
          m = (value % 3600) ? Math.ceil((value % 3600) / 60) : value % 3600
        } else {
          m = Math.ceil(value / 60)
        }

        if (m > 59) {
          m = 0;
          h++;
        }
        return i18n.t('task.list.howManyHourAndMinute', { h, m });
      }
      return ''
    },
    handlerRecommend(recommend: string) {
      
      // 清除延迟
      this.timeouts.forEach((timeout) => {
        clearTimeout(timeout)
      })

      this.aiInputText = recommend
      
      this.handlerSearch()
      
    },
    openSearchResultView() {
      this.isShowAiSearchThink = true
    },
    closeSearchResultView() {
      this.isShowAiSearchThink = false
    },
    toggleShowThinkDetail() {
      this.isShowAiSearchThink = !this.isShowAiSearchThink
    },
    renderInputPrefix() {
      
      if (!this.isShowPrefix) {
        return null
      }

      return (
        <div class="ai-search-input-prefix">
          {this.renderAiAnalyzeLogo()}
          {this.renderAiSearchSuccess()}
        </div>
      )
    },
    renderAiAnalyzeLogo() {

      if (!this.aisSearchLoading) {
        return null
      }

      return (
        <img class="ai-analyze-logo" src={aiAnalyzeLogo} />
      )
    },
    renderAiSearchSuccess() {

      if (!this.aiSearchSuccess || !this.aiInputText.length) {
        return null
      }

      return (
        <span
          class="iconfont icon-chenggong"
          style={{ fontSize: '20px', color: '#67C23A' }}
        >
        </span>
      )
    },
    renderAiSearchThink() {
      
      if (!this.isShowAiSearchThink) {
        return null
      }

      return (
        <div 
          class="ai-search-task-think"
          ref="AiSearchThink"
        >
          {this.renderAiSearchThinkTitle()}
          {this.renderAiSearchThinkContent()}
          {this.renderAiSearchThinkResult()}
        </div>
      )
    },
    renderAiSearchThinkTitle() {

      if (!this.aisSearchLoading || this.content.length > 0) {
        return null
      }
      
      const text = '思考中...'

      return (
        <div class="ai-search-task-think-title">
          {text}
        </div>
      )
    },
    renderAiSearchThinkContent() {

      if (!this.content) {
        return null
      }

      return (
        <div
          ref="AiSearchThinkContent"
          class="ai-search-task-think-content markdown-body"
          // @ts-ignore
          domPropsInnerHTML={this.contentHTML}
        >
        </div>
      )
    },
    renderAiSearchThinkResult() {

      if (!this.aiSearchSuccess) {
        return null
      }

      const totalElements = this.searchTaskResult?.pageData?.totalElements || 0
      const searchResultNum = totalElements > 0 ? totalElements : 0

      return (
        <div class="ai-search-task-think-result">
          {this.renderAiSearchThinkResultNum(searchResultNum)}
          {this.renderAiSearchThinkResultRecommend(searchResultNum)}
        </div>
      )
    },
    renderAiSearchThinkResultNum(searchResultNum: number) {
      return (
        <div class="ai-search-task-think-result-text">
          <div class="ai-search-task-result-success">
            <span
              class="iconfont icon-chenggong"
              style={{ fontSize: '20px', color: '#67C23A' }}
            >
            </span>
          </div>
          <div class="ai-search-task-result-text">
            已成功为您找到 { searchResultNum } 条数据
          </div>
        </div>
      )
    },
    renderAiSearchThinkResultRecommend(searchResultNum: number) {

      const isShowRecommend = searchResultNum <= 0
      
      if (!isShowRecommend) {
        return null
      }

      const recommendList = [
        '最近 7 天我的工单',
        '质保状态是保内的工单'
      ]

      return (
        <div class="ai-search-task-think-result-recommend">
          <div class="ai-search-task-think-result-recommend-left">
            <span>
              如果没有找到，您可以试试：
            </span>
            {recommendList.map((item) => (
              <span 
                class="ai-search-task-think-result-recommend-tag"
                onClick={() => this.handlerRecommend(item)}
              >
                {item}
              </span>
            ))}
          </div>
        </div>
      )
    },
    renderAiSearchResultShowThinkDetailButton() {
      
      if (!this.aiSearchSuccess) {
        return null
      }
       
      return (
        <div 
          class="ai-search-task-think-result-show-think-detail-button"
          onClick={this.toggleShowThinkDetail}
        >
         <div class="ai-search-task-think-result-show-think-detail-button-content">
          <span>
              思考
            </span>
            <i class="iconfont icon-more"></i>
         </div>
        </div>
      )
    }
  },
  render() {
    return (
      <div class="aiSearchClass" ref="AiSearch">

        <div class="ai-search-task-input-box">

          <div class={this.inputBoxClass}>

            <el-input
              placeholder="输入您想问的，比如“请筛选最近7天我完成的工单”"
              value={this.aiInputText}
              disabled={this.isLoading}
              onClear={this.handlerClear}
              onInput={this.handlerInput}
              nativeOnKeyup={this.handlerKeyup}
              clearable
            >
              <div slot="prefix">
                {this.renderInputPrefix()}
              </div>
            </el-input>

            {this.renderAiSearchResultShowThinkDetailButton()}

            <div class="suffix-icon" onClick={this.handlerSearch}>
              <i class={this.iconClass}></i>
            </div>

          </div>

          {this.renderAiSearchThink()}

        </div>

        <div class="close-btn" onClick={this.handlerClose}>
          <i class="el-icon-close"></i>
        </div>

      </div>
    )
  }
})
