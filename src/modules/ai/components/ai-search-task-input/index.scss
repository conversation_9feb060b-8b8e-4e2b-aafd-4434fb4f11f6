.aiSearchClass {
  background-color: #fff;
  min-height: 64px;
  position: relative;
  padding: 0 16px;
  margin-bottom: 12px;
  display: flex;
  justify-content: flex-end;
  align-items: center;

  .input-box{
    width: 500px;
    height: 40px;
    background: conic-gradient(from 180deg at 50% 50%, #00C1DF 0deg, #2B22DB 113deg, #CF65D9 234deg, #00C1DF 360deg);
    padding: 2px;
    border-radius: 6px;
    overflow: hidden;
    display: flex;
    align-items: center;

    .el-input__inner{
      height: 36px;
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
      border: none;
    }
    .el-input__prefix{
      line-height: 36px;
      input {
        &:disabled{
          background: #fff;
        }
      }
      .ai-analyze-logo{
        width: 20px;
        height: 20px;
        margin-top: -4px;
      }
    }
    .is-disabled{
      .el-input__inner{
        background-color: #fff !important;
        border-color: transparent !important;
      } 
    }
    .suffix-icon{
      height: 100%;
      background: #fff;
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
      line-height: 36px;
      padding-right: 12px;
      .icon-fasong{
        font-size: 20px;
        color: #6F47FF;
        cursor: pointer;
        &.disabled{
          opacity: 0.6;
          cursor: not-allowed;
        }
      }
    }
   
  }
  .close-btn{
    width: 32px;
    height: 32px;
    background: #F5F8FA;
    color: #262626;
    text-align: center;
    line-height: 32px;
    font-size: 16px;
    margin-left: 12px;
    cursor: pointer;
  }

}

.ai-search-task-input-box {
  position: relative;
}

.ai-search-task-result,
.ai-search-task-think {
  max-height: 150px;
  position: absolute;
  border-radius: 8px;
  background: #FFFFFF;
  box-sizing: border-box;
  border: 1px solid #E4E7ED;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
  width: 100%;
  padding: 0 12px;
  margin-top: 4px;
  z-index: 9;
  overflow-y: auto;
}

.ai-search-task-think-title {
  height: 22px;
  color: #595959;
  margin-top: 12px;
  margin-bottom: 8px;
}


.ai-search-task-think-content {
  think {
    color: #8c8c8c;
    border-left: 4px solid #ddd;
    padding-left: 10px;
    height: 100%;
    display: block;
    margin-bottom: 12px;
    p {
      margin-top: 12px;
    }
  }
  img {
    width: 100%;
  }
  ol {
    padding-left: 26px;
    list-style-type: decimal !important;
  }
  ul {
    padding-left: 16px;
    list-style-type: decimal !important;
  }
  li::marker {
    color: #262626;
    font-weight: bold;
    content: "· "; /* 自定义标记内容 */
  }
  li li {
    &::marker {
      font-weight: bold;
      content: "· "; /* 自定义标记内容 */
    }
  }
  ol > li::marker {
    content: counter(list-item) ". ";
  }
}

.ai-search-task-think-content {
  margin-top: 12px;
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 18px;
  color: #8c8c8c;

  .markdown-body h1, 
  .markdown-body h2, 
  .markdown-body h3, 
  .markdown-body h4, 
  .markdown-body h5, 
  .markdown-body h6 {
    font-size: 14px;
  }
}

.ai-search-task-result {
  height: 64px;
  border-radius: 8px;
  background: #FFFFFF;
  box-sizing: border-box;
  border: 1px solid #E4E7ED;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 8px;
  font-size: 14px;
  color: #595959;
}

.aiSearchClass {
  .input-box {
    .el-input__inner {
      padding-left: 12px;
    }
  }
  .input-box-prefix {
    .el-input__inner {
      padding-left: 30px;
    }
  }
}

.ai-search-task-result {
  padding-right: 48px;
  .ai-search-task-result-text {
    margin-left: -6px;
  }
}

.ai-search-task-result-video {
  width: 60px;
  height: 60px;
  video {
    width: 100%;
    height: 100%;
  }
}

.ai-search-task-think-result-text  {
  display: flex;
  align-items: center;
  height: 24px;
  margin: 0 0 8px 0;
  color: #262626;
  .ai-search-task-result-success {
    margin-right: 8px;
  }
  .ai-search-task-result-text {
    line-height: 100%;
  }
}

.ai-search-task-think-result-recommend {
  height: 32px;
  display: flex;
  align-items: center;
  border-width: 1px 0px 0px 0px;
  border-style: solid;
  border-color: #F0F2F5;
  padding-top: 12px;
  font-size: 12px;
  justify-content: space-between;
}

.ai-search-task-think-result-recommend-left {
  display: flex;
  align-items: center;
}

.ai-search-task-think-result-recommend-tag {
  height: 20px;
  display: flex;
  align-items: center;
  padding: 2px 8px;
  margin-left: 4px;
  border-radius: 4px;
  background: #F0F2F5;
  cursor: pointer;
}

.ai-search-task-think-result {
  margin-bottom: 12px;
}

.ai-search-task-think-result-recommend-icon {
  cursor: pointer;
}

.ai-search-task-think-result-show-think-detail-button {
  width: 80px;
  height: 100%;
  background: #fff;
  display: flex;
  align-items: center;
  padding-right: 10px;
  .ai-search-task-think-result-show-think-detail-button-content  {
    height: 22px;
    border-radius: 4px;
    background-color: rgb(240, 242, 245);
    font-size: 12px;
    padding: 0 8px;
    display: flex;
    align-items: center;
    cursor: pointer;
    &:hover {
      background-color: rgba(240, 242, 245, 0.8);
    }
    i {
      font-size: 12px;
      margin-left: 4px;
    }
  }
}