/* enum */
import ComponentNameEnum from "@model/enum/ComponentNameEnum"
/* vue */
import { defineComponent } from "vue";
/* scss */
import "@src/modules/ai/components/header.scss"
/* util */
import { getLocalesOssUrl } from '@src/util/assets'

const GptFullBackgroundImage = getLocalesOssUrl('/ai/ai_home_bg.png')
const GptBackgroundImage = getLocalesOssUrl('/ai/ai_home_bg_right.png')

export default defineComponent({
  name: ComponentNameEnum.AiAgentHeader,
  props: {
    title: {
      type: String,
      default: "小宝AI"
    },
    subTitle: {
      type: String,
      default: ""
    },
    fullBackground: {
      type: String,
      default: GptFullBackgroundImage
    },
    rightBackground: {
      type: String,
      default: GptBackgroundImage
    }
  },
  computed: {
    attrs(): Record<string, any> {
      return {
        style: {
          backgroundImage: `url(${this.fullBackground})`
        }
      }
    }
  },
  render() {
    return (
      <div class="ai-agent-view-header" {...this.attrs}>
        
        <div class="ai-agent-view-header-left">
          
          <div class="ai-agent-view-header-title">
            {this.title}
          </div>
          
          <div class="ai-agent-view-header-sub-title">
            {/* @ts-ignore */}
            <span domPropsInnerHTML={this.subTitle}>
            </span>
          </div>
          
          <div class="ai-agent-view-header-button">
            { this.$slots.button }
          </div>
          
        </div>
        
        <div class="ai-agent-view-header-right">
          <img src={this.rightBackground} />
        </div>
        
      </div>
    )
  }
});