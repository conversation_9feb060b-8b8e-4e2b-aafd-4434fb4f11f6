<template>
  <div class="knowledge-set" v-loading="loading">
    <div class="group-box" v-show="!isNull">
      <KnowledgeItem 
        v-for="(item, index) in dataList" 
        :key="index" 
        :info="item" 
        :agent-app="agentApp"
        mode="knowledge" 
        @deleteRefresh="onKnowledgeItemDeleteRefreshHandler"
        @loading="onKnowledgeItemLoadingHandler"
      />
    </div>
    <div class="second-text pad-12" v-show="isMounted && isNull">{{ configItem.placeholder }}</div>
    <base-modal appendToBody :show.sync="modalShow" :title="configItem.label" width="800px">
      <AbilitySetting 
        mode="v2" 
        type="knowledge" 
        :agent-app-detail="info" 
        @updateAbilityList="updateAbilityList" 
        ref="abilitySettingRef" 
        :refashKey="refashKey" 
        @initRefreshKey="refashKey=''"
        @updateAutoLearn="updateAutoLearn"
      >
      </AbilitySetting>
      <template slot="footer">
        <!-- <el-button type="plain-third" @click="modalShow = false">{{ $t('common.base.cancel') }}</el-button> -->
        <el-button type="primary" :loading="loading" @click="modalShow = false">
          {{ $t('common.base.close') }}
        </el-button>
      </template>
    </base-modal>
  </div>
</template>
<script>
import { defineComponent, ref, onMounted, computed } from 'vue';
import AbilitySetting from '@src/modules/ai/views/app/components/ability-setting/index.tsx';
import KnowledgeItem from './knowledgeItem.vue';
import { knowledgeKey, getConifgItemIsNotNull } from '../mock'
import MsgModel from '@model/MsgModel'
import { deleteNotOpenWiki } from "@src/api/AIv2API"

export default defineComponent({
  name: 'KnowledgeSet',
  components: {
    AbilitySetting,
    KnowledgeItem,
  },
  props: {
    info: {
      type: Object,
      default: () => ({}),
    },
    configItem: {
      type: Object,
      default: () => ({}),
    },
    refashKey: {
      type: String,
      default: '',
    },
    agentApp: {
      type: Object,
      default: () => ({}),
    }
  },
  setup(props, {emit}) {
    const modalShow = ref(false);
    const loading = ref(false);

    const dataList = ref([]);
    const isMounted = ref(false);
    const isNull = computed(()=>{
      return !getConifgItemIsNotNull(knowledgeKey, dataList.value)
    })

    const abilitySettingRef = ref(null);

    async function refreshAbilitySetting() {
      if(abilitySettingRef.value) {
        loading.value = true
        let res = null
        try {
          res = await abilitySettingRef.value.getAbilityList()
        } catch (error) {
          console.error(error)
        } finally {
          loading.value = false
        }
        return res
      }
      return Promise.resolve();
    }

    function open() {
      modalShow.value = true;
    }

    function updateAbilityList(e) {
      let arr_ = e.filter(i=>(i.type == 2 || i.type == 3))
      dataList.value = arr_;
      emit('update', arr_)
    }

    onMounted(()=>{
      isMounted.value = true
    })
    return {
      modalShow,
      loading,
      updateAbilityList,
      dataList,
      open,
      isMounted,
      isNull,
      refreshAbilitySetting,
      abilitySettingRef,
    };
  },
  methods: {
    refreshAbilitySettingHandler() {
      this.refreshAbilitySetting().then((res) => {

        const isFail = MsgModel.isFail(res)
        if (isFail) {
          return
        }

        this.$message.success('已刷新至最新状态')

      })
    },
    async onKnowledgeItemDeleteRefreshHandler() {

      const res = await this.refreshAbilitySetting()

      const isFail = MsgModel.isFail(res)
      if (isFail) {
        return
      }

      this.$message.success('移除成功')

    },
    internalKnowledgeList() {
      return this.dataList.filter(i => !(i.isPublic))
    },
    isHaveInternalKnowledge() {
      const internalKnowledgeList = this.internalKnowledgeList()
      return internalKnowledgeList.length > 0
    },
    async removeInternalKnowledge() {
      
      const isRemoved = await this.handleRemoveAbility()

      if (isRemoved) {
        this.$message.success('移除成功')
        this.refreshAbilitySetting()
      }

    },
    async handleRemoveAbility() {
      try {
        
        this.loading = true

        const agentAppId = this.agentApp.id
        const params = {
          agentAppId
        }
        const res = await deleteNotOpenWiki(params)
        
        const isFail = MsgModel.isFail(res)
        if (isFail) {
          const message = MsgModel.getMessage(res)
          this.$message.error(message)
          return false
        }
        
        return true

      } catch (error) {

        console.error(error)

        return false

      } finally {
        this.loading = false
      }
    },
    onKnowledgeItemLoadingHandler(value) {
      this.loading = value
    },
    updateAutoLearn(value) {
      this.$emit('updateAutoLearn', value)
    }
  }
});
</script>
<style lang="scss" scoped>
@import '../index.scss';

</style>
