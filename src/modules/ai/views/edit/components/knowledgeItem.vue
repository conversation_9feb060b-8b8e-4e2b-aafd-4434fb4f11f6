<template>
  <div class="knowledge-item-box flex-x">
    <div class="knowledge-label flex-1 flex-x">
      <img class="icon-box mar-r-8" v-if="isAgent" :src="info.icon" />
      <template v-if="isKnowledge" >
        <img class="icon-box-icon mar-r-8" :src="findFileIconUrl(info.name)" v-if="info.type === 3" />
        <div class="mar-r-8"><i class="iconfont icon-zhishiku1" v-if="info.type === 2"></i></div>
      </template>
      
      <div class="overHideCon-1" :class="{'overHideCon-9':Localfiles}" @click="handlePreviewFile(info)">{{ info.name }}</div>
    </div>
    <div class="knowledge-status">

      <div 
        v-if="isShowWikiWarning"
        class="knowledge-status-wiki-warning"
        @click="handleWikiWarning"
      >
        <el-tooltip content="风险提示：存在内部知识被外部访问">
          <i class="el-icon-warning knowledge-status-wiki-warning-icon">
          </i>
        </el-tooltip>
      </div>

      <template v-if="isAgent">
        <div class="flex-x">
          <div class="mar-r-8" @click="toAgentEdit"><i class="iconfont icon-edit-square1 cur-point"></i></div>
          <div @click="deleteItem(index)"><i class="iconfont icon-delete hover-danger-icon"></i></div>
        </div>
      </template>
      <template v-else><el-tag :type="statusTag">{{ statusTagDes }}</el-tag></template>

    </div>
  </div>
</template>
<script>
import { defineComponent, ref, computed,getCurrentInstance  } from 'vue';
import { openTabAIAgentDetail } from "@src/util/business/openTab"
import { findFileIconUrl } from '../mock'
import { confirm } from '@src/util/message'

import { removeAIAgentAppAbility } from "@src/api/AIv2API"
import MsgModel from '@model/MsgModel'
import { isFalsy } from 'pub-bbx-utils'

import {  getBigFileCheckCanDownLoad } from 'pub-bbx-utils';
export default defineComponent({
  name: 'KnowledgeItem',
  props: {
    info: {
      type: Object,
      default: () => ({}),
    },
    agentApp: {
      type: Object,
      default: () => ({}),
    },
    mode: {
      type: String,
      default: '',
    },
  },
  setup(props, { emit }) {
    
    const isAgent = computed(() => {
      return props.mode === 'agent';
    });
    const isKnowledge = computed(()=>{
      return props.mode === 'knowledge';
    })
    const statusTag = computed(() => {
      return props.info?.learningStatus === 2 ? 'success' : props.info?.learningStatus === 1 ? 'warning' : 'danger';
    });
    const Localfiles = computed(()=>{
      return props.info?.type == 3 && props.info?.learningStatus === 2  || false
    })

    const statusTagDes = computed(() => {
      return props.info?.learningStatus === 2 ? '已学习' : props.info?.learningStatus === 1 ? '学习中' : '学习失败';
    });
    function deleteItem(index) {
      emit('deleteItem', index);
    }
    function toAgentEdit(){
      let fromId = window.frameElement?.getAttribute('fromid');
      openTabAIAgentDetail(props.info, fromId)
    }
    function icon(file){
      let icon = '';
      const name = file.fieldName || file.url;
      if (/\.(png|bmp|gif|jpg|jpeg|tiff|image|webp)$/i.test(name)) {
        icon = 'img';
      } else if (/\.(ppt|pptx)$/i.test(name)) {
        icon = 'ppt-file-icon';
      } else if (/\.(mp3)$/i.test(name)) {
        icon = 'voice-file-icon';
      } else if (/\.(mp4)$/i.test(name)) {
        icon = 'video-file-icon';
      } else if (/\.(zip)$/i.test(name)) {
        icon = 'zip-file-icon';
      } else if (/\.(pdf)$/i.test(name)) {
        icon = 'pdf-file-icon';
      } else if (/\.(xls|xlsx)$/i.test(name)) {
        icon = 'xls-file-icon';
      } else if (/\.(doc|docx)$/i.test(name)) {
        icon = 'doc-file-icon';
      } else if (/\.(txt)$/i.test(name)) {
        icon = 'txt-file-icon';
      } else if (/\.(csv)$/i.test(name)) {
        icon = 'csv-file-icon';
      } else {
        icon = 'other-file-icon';
      }     
      return icon;
    }
    function downloadBigFile(file) {
      // 创建a标签
      const link = document.createElement('a');
      // files/auth/bigFile/get? 传入对应的文件id
      link.href = file.url;
      link.setAttribute('rel', 'noopener noreferrer');
      link.download = file.name;
      
      // 添加到DOM
      document.body.appendChild(link);
      
      // 触发点击
      link.click();
      
      // 清理：移除元素和释放 blob URL
      document.body.removeChild(link);
      
      // 延迟释放 blob URL，确保下载已开始
      setTimeout(() => {
        window.URL.revokeObjectURL(link.href);
      }, 100);
    }
    function genDownloadUrl(file){
      const {fileId} = file
      return `/files/download?fileId=${fileId}`;
    }
    const { proxy } = getCurrentInstance();
    const previewFile = ref({})
    function handlePreviewFile(file) {
      console.log(file)
      if(file.type != 3) return
      if(file?.learningStatus != 2) return
      if(!file?.url) return proxy.$message.warning('文件地址不存在');
      if(file?.isBigFile && file?.url) {
        return getBigFileCheckCanDownLoad({ fileId: file.fileId }, true).then(res=> {
          if(!res) {
            downloadBigFile(file);
          } else {
            proxy.$message.warning(res);
          }
        }).catch(err=> {
          proxy.$message.warning(err?.message || '');
        });
      }
      const { fileSize, fieldName, id, url } = file
      const downloadUrl = genDownloadUrl(file)
      previewFile.value = {
        id,
        url,
        fileSize,
        fileName: fieldName,
        name: fieldName,
        downloadUrl,
        fileType: '',
      }
      const iconType = icon(file)
      switch (iconType) {
        case 'img':
        case 'small-img':
          previewFile.value.fileType = 'image'
          break;
        case 'pdf-file-icon':
        case 'small-pdf-file-icon':
          previewFile.value.fileType = 'pdf'
          break;
        case 'voice-file-icon':
        case 'small-voice-file-icon':
          previewFile.value.fileType = 'audio'
          break;
        case 'video-file-icon':
        case 'small-video-file-icon':
          previewFile.value.fileType = 'video'
          break;
        case 'doc-file-icon':
        case 'small-doc-file-icon':
          previewFile.value.fileType = 'word'
          break;
        case 'xls-file-icon':
        case 'small-xls-file-icon':
          previewFile.value.fileType = 'excel'
          break;
        case 'txt-file-icon':
        case 'small-txt-file-icon':
          previewFile.value.fileType = 'txt'
          break;
        case 'csv-file-icon':
        case 'small-csv-file-icon':
          previewFile.value.fileType = 'csv'
          break;
        case 'ppt-file-icon':
        case 'small-ppt-file-icon':
          previewFile.value.fileType = 'ppt'
          break;
        default:
          previewFile.value.fileType = ''
          break;
      }
      switch (previewFile.value.fileType) {
        //视频查看
        case "video": {
          proxy.$previewVideo(previewFile.value.url, previewFile.value);
          break;
        }

        //图片查看
        case "image": {
          proxy.$previewElementImg(previewFile.value.url, [], previewFile.value);
          break;
        }

        // PDF查看
        case "pdf": {
          proxy.$previewPDF(previewFile.value.url, previewFile.value);
          break;
        }

        // csv,txt, word, excel查看
        case "csv":
        case "txt":
        case "word":
        case "excel":
        case "ppt":
          const suffix = fieldName.substring(fieldName.lastIndexOf(".")+1)
          proxy.$previewFile({...previewFile.value, suffix});
          break;
      }
    }
    
    return {
      statusTag,
      statusTagDes,
      isAgent,
      deleteItem,
      toAgentEdit,
      isKnowledge,
      findFileIconUrl,
      Localfiles,
      icon,
      handlePreviewFile
    };
  },
  data() {
    return {
      loading: false,
    }
  },
  computed: {
    agentAppId() {
      return this.agentApp?.id
    },
    publicAccess() {
      return Boolean(this.agentApp?.publicAccess)
    },
    /**
     * @description 是否显示风险提示：存在内部知识被外部访问
     * 1. 当对外公开访问时
     * 2. 且知识库存在内部知识时
     */
    isShowWikiWarning() {
      return this.publicAccess && !(this.info?.isPublic)
    }
  },
  methods: {
    async handleWikiWarning() {

      const confirmed = await confirm(
        '知识库内存在仅内部可查看的知识，为避免内部知识在外部流传，建议移除知识库。', 
        '温馨提示',
        {
          confirmButtonText: '移除',
          cancelButtonText: '不移除',
        }
      )

      if (isFalsy(confirmed)) {
        return
      }

      const id = this.info.id
      if (isFalsy(id)) {
        console.error('id is falsy')
        return
      }

      this.handleRemoveAbility(id)

    },
     async handleRemoveAbility(id) {
      try {
        
        this.loading = true
        this.$emit('loading', true)

        const params = {
          agentAppId: this.agentAppId,
          knowledgeId: id
        }
        const res = await removeAIAgentAppAbility(params)
        
        const isFail = MsgModel.isFail(res)
        if (isFail) {
          const message = MsgModel.getMessage(res)
          this.$message.error(message)
          return
        }

        this.$emit('deleteRefresh')

      } catch (error) {
        console.error(error)
      } finally {
        this.loading = false
        this.$emit('loading', false)
      }
    },
  }
});
</script>
<style lang="scss" scoped>
.knowledge-item-box {
  height: 40px;
  background-color: #fff;
  border-radius: 4px;
  padding: 0 12px;
  .icon-box {
    width: 24px;
    height: 24px;
    border-radius: 6;
  }
  .icon-box-icon {
    width: 14px;
    height: 16px;
  }
}
.knowledge-status {
  display: flex;
  gap: 8px;
  align-items: center;
}
.knowledge-status-wiki-warning {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.knowledge-status-wiki-warning-icon {
  color: #F56C6C;
  font-size: 18px;
}
.overHideCon-9:hover{
  cursor: pointer;
  text-decoration: underline;
  color: rgb(19, 194, 194);
}
</style>
