<template>
  <div class="auth-set pad-12">
    <div class="second-text">{{ configItem.placeholder }}</div>
    <AuthSetting mode="v2" :agent-app-detail="info" @update="update" @publicAccessChange="handlePublicAccessChange"></AuthSetting>
  </div>
</template>
<script>
import { defineComponent, ref, onMounted } from 'vue';
import AuthSetting from '@src/modules/ai/views/app/components/auth-setting/index.tsx';
import { agent<PERSON>ey } from '../mock.ts';
export default defineComponent({
  name: 'AuthSet',
  props: {
    info: {
      type: Object,
      default: () => ({}),
    },
    configItem: {
      type: Object,
      default: () => ({}),
    },
  },
  components: {
    AuthSetting,
  },
  setup(props, { emit }) {

    function handleSave() {}

    function handlePublicAccessChange(value) {
      emit('publicAccessChange', value)
    }

    function update(e) {
      emit('update', e);
    }
    
    return {
      agentKey,
      handleSave,
      update,
      handlePublicAccessChange,
    };
  },
});
</script>
<style lang="scss" scoped>
@import '../index.scss';
</style>
