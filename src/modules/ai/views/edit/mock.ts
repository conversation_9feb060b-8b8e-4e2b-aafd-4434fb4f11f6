import { ref, computed } from 'vue';
import { cloneDeep } from 'lodash';
import { getLocalesCommonOssUrl } from '@src/util/assets'

// 定义各种配置项的键名常量
export const tipsContentKey = 'prompt'; // 提示词配置键
export const variableKey = 'variable'; // 变量配置键
export const outVariable = 'outVariable';
export const knowledgeKey = 'knowledge'; // 知识库配置键
export const businessKey = 'bussiness'; // 业务数据配置键
export const dialogueExperiencekey = 'welcomeMessage'; // 对话体验配置键
export const startQuestionKey = 'defaultQuestion'; // 默认问题配置键
export const backupTalkKey = 'defaultAnswer'; // 默认回答配置键
export const agentKey = 'agentIds'; // 智能体ID配置键
export const authKey = 'allRange'; // 授权范围配置键
export const authGroupKey = 'ranges'; // 授权组配置键
export const userVisionKey = 'userVisionKey'; // 用户可见配置键
export const publicAccessKey = 'publicAccess'; // 公开访问配置键

// 默认智能体图标URL
export const defaultAgentIcon = 'https://shb-multi.oss-cn-hangzhou.aliyuncs.com/images/logo/agent/XB_AI.png';

/**
 * 检查配置项是否为空
 * @param key 配置项键名
 * @param value 配置项值
 * @returns 配置项是否不为空
 */
export function getConifgItemIsNotNull(key: any, value: any) {
  if (key === variableKey) {
    return value?.length;
  } else if (key === knowledgeKey) {
    return value?.length;
  } else if (key === businessKey) {
    return value?.length;
  } else if (key === agentKey) {
    return value?.length;
  }else if (key === outVariable) {
    return value?.length;
  }
  return true;
}

/**
 * 变量设置状态钩子
 * 用于管理变量设置相关的状态和操作
 */
export const useStateVarSet = () => {
  const itemKey = 'key'; // 变量键名属性
  const itemLabel = 'name'; // 变量名称属性
  const itemIsRequire = 'required'; // 变量是否必填属性

  /**
   * 创建一个新的变量项
   * @returns 新的变量项对象
   */
  function createItem() {
    return {
      [itemKey]: '',
      [itemLabel]: '',
      [itemIsRequire]: false,
    };
  }

  return {
    itemKey,
    itemLabel,
    itemIsRequire,
    createItem,
  };
};

export const useStateVarSetOut = () => {
  const itemKey = 'key';
  const itemLabel = 'name';

  function createItem() {
    return {
      [itemKey]: '',
      [itemLabel]: '',
    };
  }

  return {
    itemKey,
    itemLabel,
    createItem,
  };
}

export function getconfigNameByKey(key: any) {
  const enmObj: any = {
    [variableKey]: '输入变量',
    [outVariable]:'输出变量',
    [knowledgeKey]: '知识',
    [businessKey]: '业务数据',
    [dialogueExperiencekey]: '对话体验',
    [agentKey]: '智能体',
    [authKey]: '授权范围',
    [userVisionKey]: '用户可见配置',
    [publicAccessKey]: '对外公开访问',
  };

  return cloneDeep(enmObj[key]) || '';
}

/**
 * 根据配置键获取配置的占位符说明文本
 * @param key 配置键
 * @returns 配置的占位符说明文本
 */
function getconfigPlaceholderByKey(key: any) {
  const enmObj: any = {
    [variableKey]: '变量是智能体运行中用于存储、传递和更新数据的元素。合理使用变量可提升智能体对用户输入的理解与处理能力，进而生成更准确、个性化的回答。',
    [outVariable]:'用于规范Agent输出变量内容',
    [knowledgeKey]: '智能体通过学习知识内容，为AI结果输出提供支持。典型场景包括学习知识库内容以实现智能问答。',
    [businessKey]: '智能体通过获取相应模块的数据，实现高效查询与精准问答。',
    [dialogueExperiencekey]: '聊天类智能体，可以设置初次对话时的欢迎语，帮助用户快速了解Agent助理的功能。',
    [agentKey]: '在一个智能体中设置多个Agent，可以有效处理复杂的逻辑和流程。',
    [authKey]: '设置智能体的可见范围，能够为特定用户在特定场景中提供精准服务。',
    [userVisionKey]: '通过设置用户可见内容，让用户切实感受到智能化场景的强大魅力。',
    [publicAccessKey]: '仅内部被授权的员工可访问使用该Agent',
  };

  return cloneDeep(enmObj[key]) || '';
}

/**
 * AI编辑页面状态管理钩子
 * 用于管理AI编辑页面的状态和操作
 */
export const useStateAiEdit = () => {
  
  const formValue: any = ref({});

  /**
   * 初始化页面数据
   * @param info 初始化数据对象
   */
  function initPageValue(info: any = {}) {
    const obj_ = {
      ...formValue.value,
      id: info.aiAgentAppDetail.id,
      name: info.name,
      type: info.type,
      model: info.model,
      icon: info.icon,
      description: info.description,
      [tipsContentKey]: info[tipsContentKey] || '',
      [variableKey]: info[variableKey] || [],
      [outVariable]: info[outVariable] || [],
      [knowledgeKey]: info[knowledgeKey] || [],
      [businessKey]: info[businessKey] || [],
      [dialogueExperiencekey]: info[dialogueExperiencekey] || '',
      [startQuestionKey]: info[startQuestionKey] || [''],
      [backupTalkKey]: info[backupTalkKey] || '',
      [agentKey]: info?.unionAiAgent?.map(i => i.id) || [],
      [authKey]: info[authKey] ?? true,
      [authGroupKey]: info[authGroupKey] || [],
      [userVisionKey]: info?.setting?.['allowModifyAIModel'] ?? true,
      [publicAccessKey]: Boolean(info?.open),
      isAutoLearn: Boolean(info?.setting?.automaticUpdates)
    };
    formValue.value = obj_;
  }

  // 配置组数据
  const configurationGroup = ref([]);

  /**
   * 初始化配置组项
   * @param key 配置键
   * @returns 配置组项对象
   */
  function initConfigurationGroupItem(key: any) {
    let headerBtn = false;
    if ([variableKey,outVariable, knowledgeKey, businessKey, dialogueExperiencekey, agentKey].includes(key)) {
      headerBtn = true;
    }
    return {
      label: getconfigNameByKey(key),
      key,
      isCollege: false,
      headerBtn,
      placeholder: getconfigPlaceholderByKey(key),
    };
  }

  /**
   * 更新表单值
   * @param key 键名
   * @param value 值
   */
  function updateFormValue(key, value) {
    formValue.value = {
      ...formValue.value,
      [key]: value,
    };
  }

  /**
   * 合并表单值
   * @param value 要合并的值对象
   */
  function mergeFormValue(value) {
    formValue.value = {
      ...formValue.value,
      ...value,
    };
  }

  /**
   * 初始化配置组
   * @param isWorkflow 是否为工作流类型
   */
  function initConfigurationGroup(isWorkflow) {
    configurationGroup.value = [
      initConfigurationGroupItem(variableKey), 
      initConfigurationGroupItem(outVariable),
      initConfigurationGroupItem(knowledgeKey), 
      initConfigurationGroupItem(businessKey), 
      initConfigurationGroupItem(dialogueExperiencekey), 
      initConfigurationGroupItem(agentKey), 
      initConfigurationGroupItem(authKey), 
      initConfigurationGroupItem(userVisionKey),
    ];
    if (isWorkflow) {
      configurationGroup.value = configurationGroup.value.filter(item => item.key != agentKey);
    }
  }

  /**
   * 将本地数据转换为HTTP请求数据
   * @param currentFormValue 当前表单值
   * @returns 转换后的HTTP请求数据
   */
  function transferLocalForHttp(currentFormValue: Record<string, any>) {
    let val = cloneDeep(formValue.value);
    delete val[knowledgeKey];
    delete val[businessKey];
    val['setting'] = {
      allowModifyAIModel: val[userVisionKey],
      automaticUpdates: Boolean(currentFormValue?.isAutoLearn)
    };
    val['open'] = Number(val[publicAccessKey]);
    delete val[userVisionKey];
    delete val[publicAccessKey];
    return val;
  }

  // 能力列表计算属性
  const abilityList: any = computed(() => {
    const arr1 = formValue.value[knowledgeKey] || [];
    const arr2 = formValue.value[businessKey] || [];
    return [...arr1, ...arr2];
  });

  return {
    tipsContentKey,
    variableKey,
    outVariable,
    knowledgeKey,
    businessKey,
    dialogueExperiencekey,
    startQuestionKey,
    backupTalkKey,
    agentKey,
    authKey,
    userVisionKey,
    publicAccessKey,
    configurationGroup,
    abilityList,
    formValue,
    updateFormValue,
    initPageValue,
    mergeFormValue,
    transferLocalForHttp,
    initConfigurationGroup,
  };
};

/**
 * 根据文件名获取文件图标URL
 * @param fileName 文件名
 * @returns 文件图标URL
 */
export const findFileIconUrl = (fileName: string) => {
  // 如果文件名为空，返回默认图标
  if (!fileName) {
    return getLocalesCommonOssUrl('/trainingManagement/fileIcon/other.png');
  }

  // 文件类型对应的图标映射
  const fileTypeIcon:any = {
    'jpg':'/trainingManagement/fileIcon/image.png',
    'png':'/trainingManagement/fileIcon/image.png',
    'jpeg':'/trainingManagement/fileIcon/image.png',
    'webp': '/trainingManagement/fileIcon/image.png',
    'bmp': '/trainingManagement/fileIcon/image.png',
    'mp3':'/trainingManagement/fileIcon/music.png',
    'mp4':'/trainingManagement/fileIcon/movie.png',
    'pdf':'/trainingManagement/fileIcon/PDF.png',
    'ppt':'/trainingManagement/fileIcon/PPT.png',
    'pptx':'/trainingManagement/fileIcon/PPT.png',
    'svg':'/trainingManagement/fileIcon/SVG.png',
    'text': '/trainingManagement/fileIcon/PDF.png',
    'xlsx': '/trainingManagement/fileIcon/XLS.png',
    'xls': '/trainingManagement/fileIcon/XLS.png',
    'docx': '/trainingManagement/fileIcon/word.png',
    'doc': '/trainingManagement/fileIcon/word.png',
    'psd': '/trainingManagement/fileIcon/PSD.png',
    'txt': '/trainingManagement/fileIcon/TXT.png',
    'gif': '/trainingManagement/fileIcon/GIF.png',
  }
  
  // 特殊文件名处理
  if (fileName === 'examination') {
    return getLocalesCommonOssUrl('/trainingManagement/fileIcon/examination.png');
  }
  if (fileName == 'wiki') return getLocalesCommonOssUrl('/trainingManagement/fileIcon/exam.png');
  let fileKey = fileName;
  if (fileName.includes('.')) {
    const arr_ = fileName.split('.');
    fileKey = arr_[arr_.length - 1];
  }
  fileKey = fileKey.toLowerCase();
  
  // 返回对应的图标URL，如果没有匹配则返回默认图标
  return fileTypeIcon[fileKey] ? getLocalesCommonOssUrl(fileTypeIcon[fileKey]) : getLocalesCommonOssUrl('/trainingManagement/fileIcon/other.png');
};
