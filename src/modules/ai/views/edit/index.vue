<template>
  <div class="ai-edit-page">
    <!-- 页面头部区域 -->
    <div class="ai-edit-header">
      <PageHeader mode="v2" :agent-detail="currentFormValue" @changeModel="onModelChangeHandler" @edit="openEdit" @save="savePageInfo" @cancel="backPrePage"></PageHeader>
    </div>
    <!-- 页面内容区域，分为三列布局 -->
    <div class="ai-edit-content flex-x">
      <!-- 第一列：提示词编辑区域 -->
      <div class="w-3-p">
        <Prompt 
          ref="promptDom"
          :key="promptKey" 
          :agent-app-detail="currentFormValue" 
          :ability-list="abilityList" 
          @input="handleUpdatePrompt" 
          @reset="handleResetPrompt"
        >
        </Prompt>
      </div>

      <!-- 第二列：能力设置区域 -->
      <div class="w-3-p pad-l-18 pad-r-18 scroll-y">
        <div class="config-set-box">
          <div class="config-set-header">
            能力设置
          </div>
          <!-- 配置项列表 -->
          <div class="config-set-item" v-for="(item, index) in configurationGroup" :key="index">
            <!-- 配置项标题栏 -->
            <div class="config-set-item-header pad-l-8 pad-r-8 flex-x" @click="item.isCollege = !item.isCollege">
              <div class="college-box just-cur-point" :class="item.isCollege ? 'arrow-up' : ''"><i class="iconfont icon-down"></i></div>
              <div class="flex-1 overHideCon-1">{{ item.label }}</div>
              <!-- 配置项操作按钮 -->
              <div v-if="item.headerBtn">
                <!-- 变量配置操作按钮 -->
                <template v-if="item.key === variableKey">
                  <el-button type="plain-third" size="mini" @click.stop="addVarItem">添加</el-button>
                </template>
                <template v-if="item.key === outVariable && showOutButton" >
                  <el-button type="plain-third" size="mini" @click.stop="addOutVarItem">添加</el-button>
                </template>
                <template v-if="item.key === knowledgeKey">
                  <i class="iconfont icon-sync sync-icon" @click.stop="refrash"></i>
                  <el-button type="plain-third" size="mini" @click.stop="addKnowledegeItem">管理</el-button>
                </template>
                <!-- 业务数据配置操作按钮 -->
                <template v-if="item.key === businessKey">
                  <el-button type="plain-third" size="mini" @click.stop="addBusinessItem">管理</el-button>
                </template>
                <!-- 智能体配置操作按钮 -->
                <template v-if="item.key === agentKey">
                  <el-button type="plain-third" size="mini" @click.stop="addAgentItem">添加</el-button>
                </template>
              </div>
            </div>
            <!-- 配置项内容区域，使用折叠过渡效果 -->
            <el-collapse-transition>
              <div v-show="!item.isCollege">
                <!-- 变量设置组件 -->
                <template v-if="item.key === variableKey">
                  <VarSet ref="varSetDom" :config-item="item" :info="currentFormValue" @deleteItem="deleteVar" @addItem="addVar" :varFormHeardShow="varFormHeardShow"></VarSet>
                </template>
                <template v-if="item.key === outVariable">
                  <OutSet ref="OutSetDom" :config-item="item" :info="currentFormValue" @deleteItem="deleteOutVar" @addOutItem="addOut" @changeFomart="changeFomart" :initFomart="showOutButton" :outFormHeardShow="outFormHeardShow"></OutSet>
                </template>
                <!-- 知识库设置组件 -->
                <template v-if="item.key === knowledgeKey">
                  <KnowledgeSet 
                    ref="knowledgeSetDom" 
                    :config-item="item" 
                    :info="currentFormValue" 
                    :agent-app="currentFormValue"
                    @update="updateKonwledge" 
                    :refashKey="refashKey"
                    @updateAutoLearn="updateAutoLearn"
                  >
                  </KnowledgeSet>
                </template>
                <!-- 业务数据设置组件 -->
                <template v-if="item.key === businessKey">
                  <BusinessSet ref="businessSetDom" :config-item="item" :info="currentFormValue" @update="updateBusiness"></BusinessSet>
                </template>
                <!-- 对话体验设置组件 -->
                <template v-if="item.key === dialogueExperiencekey">
                  <DialogueExperienceSet ref="dialogueExperienceSetDom" :config-item="item" :info="currentFormValue" @deleteItem="deleteDialogue" @addItem="addDialogue" @update="updateDialogue"> </DialogueExperienceSet>
                </template>
                <!-- 智能体设置组件 -->
                <template v-if="item.key === agentKey">
                  <AgentSet 
                    ref="agentSetDom" 
                    :config-item="item" 
                    :info="currentFormValue" 
                    @update="updateAgent" 
                    @deleteItem="deleteAgent" 
                    @getAgentList="getAgentList"
                  >
                  </AgentSet>
                </template>
                <!-- 授权设置组件 -->
                <template v-if="item.key === authKey">
                  <AuthSet 
                    ref="authSetDom" 
                    :info="currentFormValue" 
                    :config-item="item" 
                    @update="updateAuthSet"
                    @publicAccessChange="handlePublicAccessChange"
                  >
                  </AuthSet>
                </template>

                <!-- 用户可见配置 - 切换模型设置 -->
                <template v-if="item.key === userVisionKey">
                  <div class="pad-8">
                    <div class="second-text mar-b-8">{{ item.placeholder }}</div>
                    允许切换AI模型：
                    <el-radio-group :value="currentFormValue[userVisionKey]" @input="updateUserVision">
                      <el-radio :label="true">允许</el-radio>
                      <el-radio :label="false">禁用</el-radio>
                    </el-radio-group>
                  </div>
                </template>

              </div>
            </el-collapse-transition>
          </div>
        </div>
      </div>

      <!-- 第三列：预览与调试区域 -->
      <div class="w-3-p ai-edit-preview">
        <Preview :showHeaderNew="true" :showHeader="false" :key="previewKey" mode="setPreview" :agent-app-detail="currentFormValue" :itemAgentList="AgentList" :agent-detail="agentDetail" @changeAgent="changeAgent"> </Preview>
      </div>
    </div>

    <!-- 创建对话框组件 -->
    <CreateDialog ref="createDialogDom" :value="currentFormValue" @add="createDialogAdd"></CreateDialog>
  </div>
</template>
<script>
import { defineComponent, ref, computed, watch } from 'vue';

// 导入组件
import Prompt from '@src/modules/ai/views/app/components/base-setting/prompt';
import PageHeader from 'src/modules/ai/views/detail/components/header.tsx';
import Preview from '@src/modules/ai/views/app/components/base-setting/preview';
import CreateDialog from '@src/modules/ai/views/home/<USER>/create-dialog.tsx';

// 导入设置相关组件
import VarSet from './components/varSet.vue';
import OutSet from './components/outSet.vue';
import KnowledgeSet from './components/knowledgeSet.vue';
import BusinessSet from './components/businessSet.vue';
import DialogueExperienceSet from './components/dialogueExperienceSet.vue';
import AgentSet from './components/agentSet.vue';
import AuthSet from './components/authSet.vue';
import { useStateAiEdit, getConifgItemIsNotNull, useStateVarSet, useStateVarSetOut } from './mock';
import { cloneDeep } from 'lodash';
import Router from '@src/router/index';
import { updateAIAgentAppDetail, getAIAgentDetail, addAIAgent, resetAIAgentAppPrompt } from '@src/api/AIv2API.ts';
import { backToFromAccurateTab } from '@src/util/platform';
import Platform from '@src/platform';
import MsgModel from '@model/MsgModel';
import { consoleStyle, isFalsy } from 'pub-bbx-utils';
import { AIAgentTypeEnum, AiModelEnum } from '@src/modules/ai/model/enum';
import { safeCleanupInvalidPrompt } from '@src/modules/ai/utils';
import { AIAgentAppAuthTypeEnum } from '@src/modules/ai/model/enum';
import { confirm } from '@src/util/message'
import { message as MessageUtil } from '@src/util/message';

export default defineComponent({
  name: 'AiEdit',
  components: {
    Prompt,
    PageHeader,
    VarSet,
    OutSet,
    KnowledgeSet,
    BusinessSet,
    DialogueExperienceSet,
    AgentSet,
    AuthSet,
    Preview,
    CreateDialog,
  },
  setup() {
    // 预览和提示词组件的刷新键
    const previewKey = ref(0);
    const promptKey = ref(0);
    // 组件引用
    const promptDom = ref(null);
    const refashKey = ref('')
    
    const outFormHeardShow = ref(false)
    const varFormHeardShow = ref(false)

    const pageId = computed(() => {
      return Router?.history?.current?.query?.id;
    });
    const agentId = computed(() => {
      return Router?.history?.current?.query?.agentId;
    });

    // 获取AI编辑状态管理
    const useStateAiEditData = useStateAiEdit();

    // 当前表单值，合并状态管理中的表单值和路由参数
    const currentFormValue = computed(() => {
      return { ...useStateAiEditData.formValue.value, agentId: agentId.value, id: pageId.value };
    });

    // 组件引用
    const varSetDom = ref(null);
    const knowledgeSetDom = ref(null);
    const businessSetDom = ref(null);
    const agentSetDom = ref(null);
    const OutSetDom = ref(null);
    const agentDetail = ref({});
    const showOutButton = ref(false)

    /**
     * 获取变量设置组件的数据列表
     * @returns 变量数据列表
     */
    function getVarSetDomDataList() {
      return varSetDom.value?.[0]?.dataList || []
    }

    /**
     * 获取知识库设置组件的数据列表
     * @returns 知识库数据列表
     */
    function getKnowledgeSetDomDataList() {
      return knowledgeSetDom.value?.[0]?.dataList || []
    }

    /**
     * 获取业务数据设置组件的数据列表
     * @returns 业务数据列表
     */
    function getBusinessSetDomDataList() {
      return businessSetDom.value?.[0]?.dataList || []
    }

    /**
     * 清理无效的提示词
     * @param prompt 提示词
     * @returns 清理后的提示词
     */
    function cleanupInvalidPrompt(prompt) {
      const vars = getVarSetDomDataList()
      const knowledgeList = getKnowledgeSetDomDataList()
      const businessDataList = getBusinessSetDomDataList()
      return safeCleanupInvalidPrompt(prompt, vars, knowledgeList, businessDataList)
    }

    /**
     * 处理提示词更新事件
     * @param e 更新的提示词内容
     */
    function handleUpdatePrompt(e) {
      useStateAiEditData.updateFormValue(useStateAiEditData.tipsContentKey, e);
    }

    /**
     * 重置智能体应用提示词
     */
    async function fetchResetAgentAppPrompt() {
      try {
        const params = {
          id: pageId.value,
        };
        useStateAiEditData.updateFormValue(useStateAiEditData.tipsContentKey, '');
        const res = await resetAIAgentAppPrompt(params);

        const isFail = MsgModel.isFail(res);
        if (isFail) {
          const messageString = MsgModel.getMessage(res);
          MessageUtil.error(messageString);
          return;
        }
        let data_ = MsgModel.getData(res)
        MessageUtil.success('重置成功')

        currentFormValue.value[useStateAiEditData.tipsContentKey] = data_
        

      } catch (error) {
        console.error(error);
      }
    }
    
    /**
     * 处理重置提示词事件
     */
    async function handleResetPrompt() {
      const isConfirm = await Platform.confirm('确定要重置提示词吗？');
      if (isConfirm) {
        fetchResetAgentAppPrompt().then(() => {
          promptKey.value++
        })
      }
    }
    
    // 智能体列表
    const AgentList = ref([])
    
    /**
     * 获取智能体列表
     * @param e 智能体列表数据
     */
    function getAgentList(e){
      AgentList.value = e
    }
    
    /**
     * 处理模型变更事件
     * @param e 新的模型值
     */
    function onModelChangeHandler(e) {
      useStateAiEditData.updateFormValue('model', e);
      agentDetail.value.model = e;
    }
    
    /**
     * 切换智能体
     * @param e 新的模型值
     */
    function changeAgent(e){
      agentDetail.value.model = e;
    }
    
    /**
     * 保存智能体详情信息
     */
    async function saveDeatilInfo() {
      const { name, description, icon, model } = useStateAiEditData.formValue.value;
      const newParams = {
        name,
        description,
        icon,
        model,
        id: agentId.value,
      };

      const result = await addAIAgent(newParams);

      const isFail = MsgModel.isFail(result);
      if (isFail) {
        const messageString = MsgModel.getMessage(result);
        MessageUtil.error(messageString);
        return;
      }

      const agentId_ = MsgModel.getData(result, null);
      if (isFalsy(agentId_)) {
        MessageUtil.error('更新失败');
        return;
      }
      // Platform.notificationSuccess('保存成功');
    }
    
    /**
     * 添加变量项
     */
    function addVarItem() {
      varSetDom.value?.[0]?.addItem();
    }
    
    /**
     * 刷新知识库
     */
    function refrash() {
     refashKey.value = 'sync-icon'
     knowledgeSetDom.value?.[0]?.refreshAbilitySettingHandler()
    }
    function addOutVarItem() {
      OutSetDom.value?.[0]?.addOutItem()
    }
    
    /**
     * 添加知识库项
     */
    function addKnowledegeItem() {
      knowledgeSetDom.value?.[0]?.open();
    }
    
    /**
     * 添加业务数据项
     */
    function addBusinessItem() {
      businessSetDom.value?.[0]?.open();
    }
    
    /**
     * 添加智能体项
     */
    function addAgentItem() {
      agentSetDom.value?.[0]?.open(useStateAiEditData.formValue.value[useStateAiEditData.agentKey]);
    }

    /**
     * 删除对话项
     * @param index 要删除的项索引
     */
    function deleteDialogue(index) {
      let value_ = cloneDeep(currentFormValue.value[useStateAiEditData.startQuestionKey]);
      value_.splice(index, 1);
      useStateAiEditData.updateFormValue(useStateAiEditData.startQuestionKey, value_);
      previewKey.value++;
    }
    
    /**
     * 添加对话项
     */
    function addDialogue() {
      let value_ = cloneDeep(currentFormValue.value[useStateAiEditData.startQuestionKey]);
      value_.push('');
      useStateAiEditData.updateFormValue(useStateAiEditData.startQuestionKey, value_);
    }

    /**
     * 更新授权设置
     * @param e 新的授权设置值
     */
    function updateAuthSet(e) {
      useStateAiEditData.mergeFormValue(e);
    }

    /**
     * 更新知识库
     * @param e 新的知识库值
     */
    function updateKonwledge(e) {
      useStateAiEditData.updateFormValue(useStateAiEditData.knowledgeKey, e);
    }
    
    /**
     * 更新业务数据
     * @param e 新的业务数据值
     */
    function updateBusiness(e) {
      useStateAiEditData.updateFormValue(useStateAiEditData.businessKey, e);
    }

    /**
     * 更新用户可见配置
     * @param e 新的用户可见配置值
     */
    function updateUserVision(e) {
      useStateAiEditData.updateFormValue(useStateAiEditData.userVisionKey, e);
    }

    /**
     * 更新智能体
     * @param e 新的智能体值
     */
    function updateAgent(e) {
      useStateAiEditData.updateFormValue(useStateAiEditData.agentKey, e);
    }

    /**
     * 删除智能体
     * @param index 要删除的智能体索引
     */
    function deleteAgent(index) {
      const id = index.id || ''
      if(!id) return
      // useStateAiEditData.formValue.value[useStateAiEditData.agentKey]?.splice(index, 1);
      useStateAiEditData.formValue.value[useStateAiEditData.agentKey] = useStateAiEditData.formValue.value[useStateAiEditData.agentKey]?.filter(item => item !=id)
    }

    /**
     * 删除变量
     * @param index 要删除的变量索引
     */
    function deleteVar(index) {
      useStateAiEditData.formValue.value[useStateAiEditData.variableKey]?.splice(index, 1);
      if(useStateAiEditData.formValue.value[useStateAiEditData.variableKey].length === 0){
        varFormHeardShow.value = false;
      }
    }

    function deleteOutVar(index) {
      useStateAiEditData.formValue.value[useStateAiEditData.outVariable]?.splice(index, 1);
      if(useStateAiEditData.formValue.value[useStateAiEditData.outVariable].length === 0){
        outFormHeardShow.value = false;
      }
    }

    // 获取变量设置状态管理中的创建项方法
    const { createItem: createVarItem } = useStateVarSet();
    const { createItem: createOutItem } = useStateVarSetOut();

    async function addVar() {
      let value_ = cloneDeep(currentFormValue.value[useStateAiEditData.variableKey]);
      value_.push(createVarItem());
      useStateAiEditData.updateFormValue(useStateAiEditData.variableKey, value_);
      varFormHeardShow.value = true;
    }

    async function addOut() {
      let value_ = cloneDeep(currentFormValue.value[useStateAiEditData.outVariable]);
      value_.push(createOutItem());
      useStateAiEditData.updateFormValue(useStateAiEditData.outVariable, value_)
      outFormHeardShow.value = true;
    }

    function changeFomart(val){
      if(!val){
        showOutButton.value = false
        useStateAiEditData.updateFormValue(useStateAiEditData.outVariable, [])
      }else{
        showOutButton.value = true
        outFormHeardShow.value = false
      }
    }

    /**
     * 更新对话
     * @param key 配置键
     * @param value 新值
     * @param index 索引，默认为-1
     */
    function updateDialogue(key, value, index = -1) {
      if (key === useStateAiEditData.startQuestionKey) {
        let val = useStateAiEditData.formValue.value[useStateAiEditData.startQuestionKey];
        val[index] = value;
        useStateAiEditData.updateFormValue(useStateAiEditData.startQuestionKey, val);
      } else {
        useStateAiEditData.updateFormValue(key, value);
      }

      previewKey.value++;
    }

    /**
     * 检查配置项值是否为空
     * @param item 配置项
     * @returns 配置项值是否为空
     */
    function getValueIsNull(item) {
      return !getConifgItemIsNotNull(item.key, currentFormValue.value[item.key]);
    }

    /**
     * 保存页面信息
     */
    async function savePageInfo() {
      let params = useStateAiEditData.transferLocalForHttp(currentFormValue.value);
      const prompt = promptDom.value.getPromptInputValue()
      const cleanedPrompt = cleanupInvalidPrompt(prompt)
      params[useStateAiEditData.tipsContentKey] = cleanedPrompt

      // 验证提示词是否为空
      if (!prompt || prompt == '<br>') {
        MessageUtil.error('请填写提示词');
        return;
      }

      //校验逻辑
      const isValid = await varSetDom.value?.[0]?.validateForm();
      const isOutValid = await OutSetDom.value?.[0]?.validateForm();
      const isAllowSave = isValid.every(item => item === true) && isOutValid.every(item => item === true);
      if (!isAllowSave) return
      try {
        await saveDeatilInfo();
        let detailRes = await updateAIAgentAppDetail(params);
        const isFail = MsgModel.isFail(detailRes);
        if (isFail) {
          const messageString = MsgModel.getMessage(detailRes);
          MessageUtil.error(messageString);
          return;
        }

        MessageUtil.success('保存成功');

        try {
          setTimeout(() => {
            let fromId = window?.frameElement?.getAttribute('fromid');
            Platform.refreshTab(fromId);
            window.location.reload(true)
          }, 1500);
        } catch (error) {
          console.error(error);
        }
        
      } catch (err) {
        console.error(err);
      };
    }

    /**
     * 初始化页面值
     */
    async function initPageValue() {
      let detailData = {};
      // 获取智能体详情
      let { status, data, message } = await getAIAgentDetail({
        id: agentId.value,
      });
      if (status === 0) {
        // 获取智能体应用详情
        let aiAgentAppDetail = data?.aiAgentAppDetail || {}
        // 合并数据
        detailData = { ...data, ...aiAgentAppDetail };
        detailData.outVariable = detailData.outVariable || []
        if(detailData.outVariable.length === 0){
          showOutButton.value = false
        }else{
          showOutButton.value = true
        }

        const isWorkFlow = detailData?.type == AIAgentTypeEnum.Workflow;
        agentDetail.value = data
        // 初始化配置组
        useStateAiEditData.initConfigurationGroup(isWorkFlow);
      }else{
        MessageUtil.error(message);
      }

      useStateAiEditData.initPageValue(detailData);
    }

    /**
     * 返回上一页
     */
    function backPrePage() {
      let nowId = window?.frameElement?.dataset?.id;
      let fromId = window?.frameElement?.getAttribute('fromid');

      Platform.refreshTab(fromId);
      Platform.closeTab(nowId);
    }

    /**
     * 创建对话框添加事件处理
     * @param params 添加的参数
     */
    async function createDialogAdd(params) {
      useStateAiEditData.updateFormValue('name', params.name);
      useStateAiEditData.updateFormValue('icon', params.icon);
      useStateAiEditData.updateFormValue('description', params.description);
      createDialogDom.value.closeDialog();
    }

    // 创建对话框组件引用
    const createDialogDom = ref(null);
    
    /**
     * 打开编辑对话框
     */
    function openEdit() {
      createDialogDom.value.clearValidate();
      createDialogDom.value.openDialog(useStateAiEditData.formValue.value);
    }

    /**
     * 为提示词中的变量添加span标签
     * @param prompt 提示词
     * @returns 处理后的提示词
     */
    function promptVarAddSpanTag(prompt) {
      if (isFalsy(prompt)) {
        return '';
      }

      const variableRegex = /\{([^{}]*)\}/g;

      return prompt.replace(variableRegex, (match, variableName) => {
        const matchValue = match.replace('{', '').replace('}', '');
        return `{<span style="color: #13c2c2;">${matchValue}</span>}`;
      });
    }

    /**
     * 将换行符转换为段落标签
     * @param text 文本
     * @returns 处理后的文本
     */
    function convertNewlinesToParagraphs(text) {
      if (isFalsy(text)) {
        return '';
      }

      // 先将文本按换行符分割成数组
      const lines = text.split('\n');

      // 过滤掉空行，并将每行文本包裹在 <p> 标签中
      const paragraphs = lines
        .map(line => line.trim())
        .filter(line => line.length > 0)
        .map(line => `<p>${line}</p>`);

      // 将所有段落连接起来
      return paragraphs.join('');
    }

    /**
     * 初始化提示词
     * @param prompt 提示词
     * @returns 初始化后的提示词
     */
    function initializePrompt(prompt) {
      if (isFalsy(prompt)) {
        return '';
      }

      let newPrompt = convertNewlinesToParagraphs(prompt);
      newPrompt = promptVarAddSpanTag(newPrompt);
      return newPrompt;
    }

    // 初始化页面值
    initPageValue();
    watch(() => currentFormValue.value[useStateAiEditData.variableKey], (newValue)=>{
       if(newValue){
         varFormHeardShow.value = true;
       }
    })

    watch(() => currentFormValue.value[useStateAiEditData.outVariable], (newValue)=>{
       if(newValue){
         outFormHeardShow.value = true;
       }
    })

    return {
      previewKey,
      promptDom,
      refashKey,
      promptKey,
      agentDetail,
      ...useStateAiEditData,
      showOutButton,
      handleUpdatePrompt,
      handleResetPrompt,
      onModelChangeHandler,
      varSetDom,
      OutSetDom,
      knowledgeSetDom,
      businessSetDom,
      agentSetDom,
      addVarItem,
      addOutVarItem,
      addKnowledegeItem,
      addBusinessItem,
      getValueIsNull,
      deleteDialogue,
      addDialogue,
      pageId,
      agentId,
      currentFormValue,
      addAgentItem,
      updateAuthSet,
      updateDialogue,
      updateKonwledge,
      updateBusiness,
      updateAgent,
      savePageInfo,
      deleteAgent,
      deleteVar,
      deleteOutVar,
      addVar,
      addOut,
      backPrePage,
      createDialogAdd,
      createDialogDom,
      openEdit,
      updateUserVision,
      changeAgent,
      AgentList,
      getAgentList,
      refrash,
      changeFomart,
      outFormHeardShow,
      varFormHeardShow,
    };
  },
  methods: {
    /**
     * 更新公开访问状态
     * @param value 新的公开访问状态
     */
    async handlePublicAccessChange(value) {
      // 授权范围
      const allRange = String(this.currentFormValue[this.authKey])
      // 授权范围是否为不可见
      const isAllRangeHidden = allRange === AIAgentAppAuthTypeEnum.HIDDEN
      // 权限范围为内部不可见、又选择外部不开放，出现此情况时，提示用户"请合理配置Agent权限"；
      if (isAllRangeHidden && !value) {
        this.$message.error('请合理配置 Agent 权限')
        return
      }

      const knowledgeSetDomElement = this.knowledgeSetDom?.[0] || {}
      const isHaveInternalKnowledge = knowledgeSetDomElement?.isHaveInternalKnowledge()

      // 开放时，若已学知识库有仅内部可见的知识，知识处出现预警，点击出现温馨提示弹窗，租户可选择移除知识库、也可不移除；
      if (value && isHaveInternalKnowledge) {

        let confirmed = false

        try {
          confirmed = await confirm(
            '知识库内存在仅内部可查看的知识，为避免内部知识在外部流传，建议移除知识库。', 
            '温馨提示',
            {
              confirmButtonText: '移除',
              cancelButtonText: '不移除',
            }
          )
        } catch (error) {
          confirmed = false
          console.error(error)
        }

        // 移除知识库
        if (confirmed) {
          knowledgeSetDomElement.removeInternalKnowledge().then(() => {
            this.updatePublicAccessHandler(value);
          })
        } else {
          this.updatePublicAccessHandler(value);
        }

        return
      }

      this.updatePublicAccessHandler(value);
    },
    /**
     * 处理公开访问状态更新
     * @param value 新的公开访问状态
     */
    updatePublicAccessHandler(value) {
      this.updateFormValue(this.publicAccessKey, value);
    },
    updateAutoLearn(value) {
      this.currentFormValue.isAutoLearn = value;
    }
  }
});
</script>
<style lang="scss" scoped>
@import './index.scss';
.ai-edit-page {
  display: flex;
  height: 100vh;
  flex-direction: column;
  overflow: hidden;
  .ai-edit-content {
    flex: 1;
    overflow: hidden;
    .ai-agent-app-setting-prompt {
      background-color: #fff;
      height:100%;
      max-height:100%;
    }
  }
  .ai-edit-header {
    .ai-agent-detail-header {
      border-bottom: 1px solid #f0f2f5;
      border-radius: 0;
    }
  }
}
.w-3-p {
  width: calc(100% / 3);
  height: 100%;
  background-color: #fff;
}

.config-set-box {
  .config-set-header{
    height:48px;
    display:flex;
    align-items: center;
    border-bottom: 1px solid #cbd6e2;
    font-weight:600;
    font-size: 16px;
    color: #262626;
    line-height: 24px;
    }
  .config-set-item {
    border-bottom: 1px solid #cbd6e2;
    .config-set-item-header {
      height: 48px;
      display: flex;
      &:hover{
        background-color: #f5f8fa;
      }
    }
    .college-box {
      transition: all 0.5s;
      width: 25px;
      height: 25px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .arrow-up {
      transform: rotateZ(-90deg);
    }
  }
}
</style>

<style lang="scss">
.ai-edit-preview {
  .chat-ai-view-footer-app-tag {
    margin-top: 0;
  }
  .chat-ai-view-model-dropdown {
    display: none;
  }
}
.ai-agent-app-setting-prompt {
  background-color: #fff;
  max-height: calc(100vh - 200px);
  height: calc(100vh - 200px);
}
.sync-icon{
  margin-right: 8px;
  font-size:14px;
  cursor: pointer;
}
.ai-edit-public-access-header {
  margin-bottom: 8px;
  .ai-edit-public-access-title {
    display: inline-block;
    margin-right: 8px;
  }
}
.ai-edit-page {
  min-width: 1060px;
}
</style>
