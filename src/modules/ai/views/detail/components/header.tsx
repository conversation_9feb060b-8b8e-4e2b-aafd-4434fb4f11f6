/* components */
import ComponentNameEnum from '@model/enum/ComponentNameEnum';
import TypeTag from '@src/modules/ai/components/type-tag';
import ModelItem from '@src/modules/ai/components/model-item';
/* enum */
import { AIAgentTypeEnum, AiModelEnum } from '@src/modules/ai/model/enum';
/* types */
import { AIAgentDetailType } from '@src/modules/ai/types';
/* vue */
import { defineComponent, PropType } from 'vue';
/* scss */
import '@src/modules/ai/views/detail/components/header.scss';

enum AiAgentDetailHeaderEventEnum {
  EditName = 'editName',
  ChangeModel = 'changeModel',
}

export default defineComponent({
  name: ComponentNameEnum.AiAgentDetailHeader,
  props: {
    agentDetail: {
      type: Object as PropType<AIAgentDetailType>,
      required: true,
    },
    onEditName: {
      type: Function as PropType<(name: string) => void>,
    },
    onChangeModel: {
      type: Function as PropType<(model: AiModelEnum) => void>,
    },
    mode: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      editName: '',
      isEdit: false,
    };
  },
  computed: {
    name(): string {
      return this.agentDetail.name || '';
    },
    type(): AIAgentTypeEnum {
      return this.agentDetail.type as AIAgentTypeEnum;
    },
    model(): AiModelEnum {
      return this.agentDetail.model as AiModelEnum;
    },
    icon(): string{
      return this.agentDetail.icon || '';
    }
  },
  methods: {
    onEditHandler() {
      if(this.mode === 'v2'){
        return this.$emit('edit')
      }
      this.editName = this.name;
      this.isEdit = true;
    },
    onEditNameInput(value: string) {
      this.editName = value;
    },
    onEditNameBlur() {
      this.isEdit = false;
      this.$emit(AiAgentDetailHeaderEventEnum.EditName, this.editName);
    },
    onModelDropdownCommand(model: AiModelEnum) {
      this.$emit(AiAgentDetailHeaderEventEnum.ChangeModel, model);
    },
    renderName() {
      return <div class="ai-agent-detail-header-name">{this.isEdit ? this.renderNameEdit() : this.renderNameView()}</div>;
    },
    renderNameView() {
      return (
        <div class="ai-agent-detail-header-name-view flex-x" onClick={this.onEditHandler}>
          {(this.mode === 'v2' && this.icon) ? <img class="icon-box mar-r-8" src={this.icon}></img> : ''}
          {this.name}
          <i class="iconfont icon-edit-square1"></i>
        </div>
      );
    },
    renderNameEdit() {
      return (
        <div class="ai-agent-detail-header-name-edit">
          <el-input value={this.editName} maxlength={20} onInput={this.onEditNameInput} onBlur={this.onEditNameBlur} />
        </div>
      );
    },
    renderTypeTag() {
      return (
        <div class="ai-agent-detail-header-type-tag">
          <TypeTag agentType={this.type} />
        </div>
      );
    },
    renderLeft() {
      return (
        <div class="ai-agent-detail-header-left">
          {this.mode === 'v2' ? <div class="cur-point mar-r-8" onClick={()=>this.$emit('cancel')}><i class="iconfont icon-left1"></i></div> : ''}
          {this.renderName()}
          {this.renderTypeTag()}
        </div>
      );
    },
    renderRight() {
      return <div class="ai-agent-detail-header-right">{this.renderModel()}</div>;
    },
    renderModel() {
      return (
        <div class="ai-agent-detail-header-model">
          <span class="ai-agent-detail-header-model-label">模型</span>
          {this.renderModelDropdown()}
          {this.mode === 'v2' ? <div class="mar-l-8"><el-button type="plain-third"  onClick={()=>this.$emit('cancel')}>取消</el-button><el-button type="primary" onClick={()=>this.$emit('save')}>保存</el-button></div> : ''}
        </div>
      );
    },
    renderModelDropdown() {
      return (
        <el-dropdown trigger="click" onCommand={this.onModelDropdownCommand}>
          <div class="ai-agent-detail-header-model-dropdown">
            <ModelItem value={this.model} />
            <i class="iconfont icon-more"></i>
          </div>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command={AiModelEnum.DEEPSEEK}>
              <ModelItem value={AiModelEnum.DEEPSEEK} />
            </el-dropdown-item>
            <el-dropdown-item command={AiModelEnum.TONG_YI}>
              <ModelItem value={AiModelEnum.TONG_YI} />
            </el-dropdown-item>
            <el-dropdown-item command={AiModelEnum.DOU_BAO}>
              <ModelItem value={AiModelEnum.DOU_BAO} />
            </el-dropdown-item>
            <el-dropdown-item command={AiModelEnum.MOON_SHOT}>
              <ModelItem value={AiModelEnum.MOON_SHOT} />
            </el-dropdown-item>
            <el-dropdown-item command={AiModelEnum.DING_DING}>
              <ModelItem value={AiModelEnum.DING_DING} />
            </el-dropdown-item>
            {/* <el-dropdown-item command={AiModelEnum.SHB}>
              <ModelItem value={AiModelEnum.SHB} />
            </el-dropdown-item> */}
          </el-dropdown-menu>
        </el-dropdown>
      );
    },
  },
  render() {
    return (
      <div class="ai-agent-detail-header">
        {this.renderLeft()}
        {this.renderRight()}
      </div>
    );
  },
});
