/* components */
import AiAgentSelect from "@src/modules/ai/views/home/<USER>/agent-select"
/* model */
import ComponentNameEnum from "@model/enum/ComponentNameEnum"
/* vue */
import { defineComponent, PropType } from "vue"
/* types */
import { AIAgentAppType, AIAgentType } from "@src/modules/ai/types/agent"
/* scss */
import "@src/modules/ai/views/home/<USER>/list/header.scss"
import { AIAgentLogSourceCNNameEnum, AIAgentLogSourceEnum } from "@src/modules/ai/model/enum"

enum AiAgentLogHeaderEventEnum {
  CurrentAgentValueChange = 'currentAgentValueChange',
  CurrentAgentAppValueChange = 'currentAgentAppValueChange',
  SearchTimeChange = 'searchTimeChange',
  SourceChange = 'sourceChange'
}

export default defineComponent({
  name: ComponentNameEnum.AiAgentLogHeader,
  props: {
    agents: {
      type: Array as PropType<AIAgentType[]>,
      default: () => []
    },
    agentApps: {
      type: Array as PropType<AIAgentAppType[]>,
      default: () => []
    },
    currentAgentValue: {
      type: String,
      default: ''
    },
    currentAgentAppValue: {
      type: String,
      default: ''
    },
    sourceValue: {
      type: String,
      default: ''
    },
    searchTimes: {
      type: Array as PropType<Date[]>,
      default: () => []
    },
    onCurrentAgentValueChange: {
      type: Function
    },
    onCurrentAgentAppValueChange: {
      type: Function
    },
    onSearchTimeChange: {
      type: Function
    },
    onSourceChange: {
      type: Function
    }
  },
  data() {
    return {
      sourceOptions: [
        {
          label: AIAgentLogSourceCNNameEnum[AIAgentLogSourceEnum.DOOR],
          value: AIAgentLogSourceEnum.DOOR
        },
        {
          label: AIAgentLogSourceCNNameEnum[AIAgentLogSourceEnum.OUTSIDE],
          value: AIAgentLogSourceEnum.OUTSIDE
        },
        {
          label: AIAgentLogSourceCNNameEnum[AIAgentLogSourceEnum.INSIDE],
          value: AIAgentLogSourceEnum.INSIDE
        }
      ]
    }
  },
  computed: {
    timeRangeValue(): Date[] {
      return this.searchTimes
    }
  },
  methods: {
    onCurrentAgentValueChangeHandler(value: string) {
      this.$emit(AiAgentLogHeaderEventEnum.CurrentAgentValueChange, value)
    },
    onCurrentAgentAppValueChangeHandler(value: string) {
      this.$emit(AiAgentLogHeaderEventEnum.CurrentAgentAppValueChange, value)
    },
    onSearchTimeRangeChangeHandler(value: Date[] = []) {
      this.$emit(AiAgentLogHeaderEventEnum.SearchTimeChange, value)
    },
    onSourceChangeHandler(value: string) {
      this.$emit(AiAgentLogHeaderEventEnum.SourceChange, value)
    },
    renderAgentSelect() {
      return (
        <el-select 
          clearable
          placeholder="智能体"
          value={this.currentAgentValue} 
          onChange={this.onCurrentAgentValueChangeHandler}
        >
          {this.agents.map(agent => (
            <el-option
              value={agent.id}
              label={agent.name}
            />
          ))}
        </el-select>
      )
    },
    renderAgentAppSelect() {
      return (
        <el-select 
          clearable
          placeholder="AI应用"
          value={this.currentAgentAppValue} 
          onChange={this.onCurrentAgentAppValueChangeHandler}
        >
          {this.agentApps.map(agentApp => (
            <el-option 
              value={agentApp.id} 
              label={agentApp.name} 
            />
          ))}
        </el-select>
      )
    },
    renderSourceSelect() {
      return (
        <el-select
          clearable
          placeholder="来源"
          value={this.sourceValue}
          onChange={this.onSourceChangeHandler}
        >
          {this.sourceOptions.map(option => (
            <el-option
              value={option.value}
              label={option.label}
            />
          ))}
        </el-select>
      )
    },
    renderSearchTimeRange() {
      return (
        <el-date-picker
          clearable
          value={this.timeRangeValue}
          onInput={this.onSearchTimeRangeChangeHandler}
           type="daterange"
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        >
        </el-date-picker>
      )
    },
    renderContent() {
      return (
        <div class="ai-agent-log-header-content">
          {this.renderAgentSelect()}
          {this.renderSourceSelect()}
          {this.renderSearchTimeRange()}
        </div>
      )
    }
  },
  render() {
    return (
      <div class="ai-agent-view-list-header">
        {this.renderContent()}
      </div>
    )
  }
})