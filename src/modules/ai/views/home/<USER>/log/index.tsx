/* component */
import AiAgentLogHeader from "@src/modules/ai/views/home/<USER>/log/header"
import AiAgentLogTable from "@src/modules/ai/views/home/<USER>/log/table"
/* model */
import ComponentNameEnum from "@model/enum/ComponentNameEnum"
/* vue */
import { defineComponent, PropType } from "vue"
/* scss */
import "@src/modules/ai/views/home/<USER>/log/index.scss"
/* type */
import { AIAgentAppType, AIAgentType, AiAgentLog } from "@src/modules/ai/types"
/* api */
import { getAIAgentAppList, getAIAgentLog } from "@src/api/AIv2API"
/* param */
import { AIAgentAppListParamType, AIAgentLogParamType } from "@src/modules/ai/model/param"
import MsgModel from "@model/MsgModel"
import Page from "@model/Page"
import { formatDate, FormatTemplate, isEmpty, isFalsy, isNotEmpty, isString } from "pub-bbx-utils"
import { getRootWindowInitDataUser } from '@src/util/window';
import { isJSONObject } from "@src/util/lang/object"
import { AIAgentLogSourceEnum } from "@src/modules/ai/model/enum"

export default defineComponent({
  name: ComponentNameEnum.AiAgentLog,
  props: {
    agents: {
      type: Array as PropType<AIAgentType[]>,
      default: () => []
    }
  },
  data() {
    return {
      currentAgentValue: undefined as string | number | undefined,
      currentAgentAppValue: undefined as string | number | undefined,
      searchTimes: [] as Date[],
      agentLogsPage: new Page<AiAgentLog>({pageSize:30}),
      agentLogs: [] as AiAgentLog[],
      loading: false,
      aiAppList: [] as AIAgentAppType[],
      sourceValue: undefined as AIAgentLogSourceEnum | undefined
    }
  },
  computed: {
    attrs(): Record<string, any> {
      return {
        directives: [
          {
            name: 'loading',
            value: this.loading
          }
        ]
      }
    }
  },
  mounted() {
    this.getLocal('trigger_Log_List')
    this.initialize()
  },
  methods: {
    getLocal(key: string) {
      const loginUser = getRootWindowInitDataUser()
      const userId = loginUser?.userId
      const storage = window.localStorage
      const _key = `${userId}-${key}`
      this.agentLogsPage.pageSize = storage.getItem(_key)
    },
    setLocal(key: string, value: ang) {
      const loginUser = getRootWindowInitDataUser()
      const userId = loginUser?.userId
      const storage = window.localStorage
      const _key = `${userId}-${key}` 
      storage.setItem(_key, value)
    },
    initialize() {
      this.handleSearch()
    },
    handleCurrentAgentValueChange(value: string) {
      
      this.currentAgentValue = value

      // 如果当前 agent 为空，则清空应用列表
      this.currentAgentAppValue = undefined
      this.aiAppList = []
      this.agentLogsPage.pageNum = 1
      
      this.fetchAIAgentAppList()

      this.handleSearch()
      
    },
    handleCurrentAgentAppValueChange(value: string) {
      this.currentAgentAppValue = value
      this.agentLogsPage.pageNum = 1
      this.handleSearch()
    },
    handleSearchTimeChange(value: Date[]) {
      this.searchTimes = value
      this.agentLogsPage.pageNum = 1
      this.handleSearch()
    },
    handleSourceChange(value: string) {
      this.sourceValue = value as AIAgentLogSourceEnum
      this.agentLogsPage.pageNum = 1
      this.handleSearch()
    },
    handlePageNumChange(pageNum: number) {
      this.agentLogsPage.pageNum = pageNum
      this.fetchAgentLog()
    },
    handlePageSizeChange(pageSize: number) {
      this.agentLogsPage.pageSize = pageSize
      this.setLocal('trigger_Log_List',pageSize)
      this.fetchAgentLog()
    },
    handleSearch() {
      this.fetchAgentLog()
    },
    async fetchAgentLog() {
      try {
        
        this.loading = true

        const pageNum = this.agentLogsPage.pageNum || 1
        const pageSize = this.agentLogsPage.pageSize || 10
        const [startTime, endTime] = this.searchTimes || []
        
        let startTimeTimestamp = null
        let endTimeTimestamp = null
        if (startTime) {
          startTimeTimestamp = startTime!.getTime()
        }
        if (endTime) {
          endTimeTimestamp = endTime!.getTime() + 24 * 60 *60 *1000
        }

        const params: AIAgentLogParamType = {
          agentId: this.currentAgentValue as number,
          agentAppId: this.currentAgentAppValue as number,
          startTime: startTimeTimestamp as number,
          endTime: endTimeTimestamp as number,
          pageNum,
          pageSize,
          sortBy: {
            createTime: false
          },
          source: this.sourceValue
        }
        const res = await getAIAgentLog(params)
        
        const isFail = MsgModel.isFail(res)
        if (isFail) {
          const message = MsgModel.getMessage(res)
          this.$message.error(message)
          return
        }
        
        const page = MsgModel.getData<Page<AiAgentLog>>(res, {
          list: [],
          total: 0
        })
        const list = page?.list || []

        this.agentLogsPage = page as Page<AiAgentLog>
        let logList = list as AiAgentLog[]

        logList = logList.map(log => {
          let newLog = {
            ...log,
            newAnswer:''
          }
          try {
            const answer = log.answer
            if (answer && isJSONObject(answer)) {
              const answerObj = JSON.parse(answer)
              newLog.newAnswer = answerObj.text
              newLog.answer = answerObj.text.replaceAll('\n', ' ').replaceAll('<think>', ' ').replaceAll('</think>', ' ')
            }
            else if (answer && isString(answer) && answer == 'null') {
              newLog.answer = ''
              newLog.newAnswer = answer
            }else {
              newLog.newAnswer = answer
            }
          } catch (error) {
            console.error(error)
          }
          return newLog
        })

        this.agentLogs = logList
        
      } catch (error) {
        console.error(error)
      } finally {
        this.loading = false
      }
    },
    async fetchAIAgentAppList() {
      try {
        
        const params: AIAgentAppListParamType = {
          id: this.currentAgentValue as number
        }
        const res = await getAIAgentAppList(params)

        const isFail = MsgModel.isFail(res)
        if (isFail) {
          const message = MsgModel.getMessage(res)
          this.$message.error(message)
          return
        }
        
        // 获取应用列表
        const appList = MsgModel.getData<AIAgentAppType[]>(res, [])
        // 更新应用列表
        this.aiAppList = appList

      } catch (error) {
        console.error(error)
      }
    },
    renderHeader() {
      return (
        <AiAgentLogHeader 
          agents={this.agents}
          sourceValue={this.sourceValue}
          agentApps={this.aiAppList}
          currentAgentValue={this.currentAgentValue as string}
          currentAgentAppValue={this.currentAgentAppValue as string}
          searchTimes={this.searchTimes as Date[]}
          onCurrentAgentValueChange={this.handleCurrentAgentValueChange}
          onCurrentAgentAppValueChange={this.handleCurrentAgentAppValueChange}
          onSearchTimeChange={this.handleSearchTimeChange}
          onSourceChange={this.handleSourceChange}
        />
      )
    },
    renderList() {
      return (
        <AiAgentLogTable 
          loading={this.loading}
          logs={this.agentLogs as AiAgentLog[]}
          page={this.agentLogsPage as unknown as Page}
          onPageNumChange={this.handlePageNumChange}
          onPageSizeChange={this.handlePageSizeChange}
        />
      )
    },
    renderContent() {
      return (
        <div class="ai-agent-log-content">
          {this.renderHeader()}
          {this.renderList()}
        </div>
      )
    }
  },
  render() {
    return (
      <div class="ai-agent-log" {...this.attrs}>
        {this.renderContent()}
      </div>
    )
  }
})