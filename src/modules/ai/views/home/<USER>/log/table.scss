.ai-agent-log-table-main {
  .el-table {
    th.el-table__cell {
      background: #f4f4f5;
    }
  }
}

.ai-agent-log-table-content {
  .el-table__body-wrapper {
    height: 410px;
    overflow-y: auto;
  }
}
.ai-agent-log-table-id{
  cursor: pointer;
  @include fontColor();
}

.ai-agent-log-drawer-header {
  display: flex;
  align-items: center;
  .ai-agent-log-drawer-header-img{
    width: 32px;
    height: 32px;
    border-radius: 8px;
    margin-right: 12px;
  }
  .header-item {
    display: flex;
    align-items: center;
    font-size: 14px;
    line-height: 22px;
    &:last-child {
      margin-bottom: 0;
    }
    .Id{
      color: #262626;
    }
    .label {
      font-weight: 500;
      color: #606266;
    }
    
    .value {
      margin-right: 15px;
    }
    
    .ml20 {
      margin-left: 20px;
    }
    
    .tag-wrapper {
      margin-left: 10px;
    }
  }
  .header-item-a {
    display: flex;
    align-items: center;
    font-size: 12px;
    line-height: 20px;
    &:last-child {
      margin-bottom: 0;
    }
    
    .label {
      font-weight: 500;
      color: #606266;
    }
    
    .value {
      margin-right: 15px;
    }
    
    .ml20 {
      margin-left: 20px;
    }
    
    .tag-wrapper {
      margin-left: 10px;
    }
  }
}
.ai-agent-log-drawer-content{
  min-height: calc(100% - 24px);
  border-radius: 8px;
  margin:12px;
  padding: 24px;
  border-radius: 16px;
  border: 6px solid #F5F8FA;
 
}
.ai-agent-log-drawer-content-answer{
  padding-left: 12px;
  img{
    border-style: none;
    max-width: 100%;
    box-sizing: content-box;
  }
}
.ai-agent-log-drawer-content-answer-wrapper{
  margin-top: 16px;
  font-size: 12px;
  font-weight: normal;
  line-height: 20px;
  color: #8C8C8C;
}
.ai-agent-log-drawer-content-Question{
  border-radius: 16px 0px 16px 16px;
  /* light/fill/--el-fill-color-light */
  background: #F5F8FA;
  padding: 4px 16px 4px 16px;
  width: fit-content;
  max-width: 450px;
}
.ai-agent-log-drawer-content-questions{
  display: flex;
  justify-content: flex-end;
}
.el-drawer__body{
  overflow-y: auto;
}
