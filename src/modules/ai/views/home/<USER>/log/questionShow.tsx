import { defineComponent,computed } from 'vue';
import './questionShow.scss';
import { getOssUrl } from '@src/util/assets'
import { renderMarkdown } from "shb-ai-chat-md"
import 'shb-ai-chat-md/index.scss'
interface Props {
  linkString: string;
}

export default defineComponent({
  name: 'QuestionShow',
  props: {
    linkString: {
      type: String,
      required: true
    }
  },
  setup(props: Props) {
    const links = computed(() => props.linkString);
    // const links = props.linkString.split(' ');
    const strLink = (str) => {
      try {
        return renderMarkdown(str || '')
      } catch (error) {
        return str || ''
      }
    }
    // 处理图片文件
    const renderImage = (link: string, index: number) => {
      return <img key={index} src={link} alt="Media" class="question-show-image" />;
    };

    // 处理音频文件
    const renderAudio = (link: string, index: number) => {
      return <audio key={index} controls src={link} class="question-show-audio" />;
    };
  const getFileClass = (name: string) =>{
    let icon = ''
      if (/\.(png|bmp|gif|jpg|jpeg|tiff|image)$/i.test(name)) {
          icon = 'img';
      } else if (/\.(ppt|pptx)$/i.test(name)) {
          icon = 'ppt-file-icon';
      } else if (/\.(mp3)$/i.test(name)) {
          icon = 'voice-file-icon';
      } else if (/\.(mp4)$/i.test(name)) {
          icon = 'video-file-icon';
      } else if (/\.(zip)$/i.test(name)) {
          icon = 'zip-file-icon';
      } else if (/\.(pdf)$/i.test(name)) {
          icon = 'pdf-file-icon';
      } else if (/\.(xls|xlsx)$/i.test(name)) {
          icon = 'xls-file-icon';
      } else if (/\.(doc|docx)$/i.test(name)) {
          icon = 'doc-file-icon';
      } else if (/\.(txt)$/i.test(name)) {
          icon = 'txt-file-icon';
      } else if (/\.(csv)$/i.test(name)) {
          icon = 'csv-file-icon';
      } else {
          icon = 'other-file-icon';
      }

      let clazz = ['base-file-preview'];

      if (icon !== 'img') {
          clazz = clazz.concat(['base-file-icon', icon]);
      }

      return clazz;
  }
  const  styl = (link: string) =>{
      let styl = {
          backgroundImage: `url(${getOssUrl('/file-icon.png')})`
      };
      
      if(/\.(png|bmp|gif|jpg|jpeg|tiff|image)$/i.test(link)) {
          
          let suffix = link.indexOf('http') == 0 ? 'x-oss-process=image/resize,m_lfit,h_88,w_88' : 'isCmp=true';
          
          let url = `${link}${link.indexOf('?') >= 0 ? '&' : '?'}${suffix}`;
          
          styl.backgroundImage = `url(${url})`;
          styl.cursor = 'pointer';
      }
      
      return styl;
  }

    // 处理文档文件
    const renderDocument = (link: string, index: number) => {
      const fileNameMatch = link.match(/\/([^\/]+)$/);
      const fileName = fileNameMatch ? fileNameMatch[1] : link;
      
      const extension = fileName.split('.').pop()?.toUpperCase() || '';
      
      return (
        <div key={index} class="question-show-document">
          <a href={link} target="_blank" rel="noopener noreferrer" class="document-link">
            <div class="document-icon">
            <div class={getFileClass(link)} style={styl(link)}></div>
            </div>
            <div class="document-info">
              <div class="document-name">{fileName}</div>
            </div>
          </a>
        </div>
      );
    };



    // 根据链接类型选择渲染方式
    const renderContent = (link: string, index: number) => {
      if (/\.(jpg|jpeg|png|gif)$/i.test(link)) {
        return renderImage(link, index);
      } else if (/\.(mp3|wav|ogg)$/i.test(link)) {
        return renderAudio(link, index);
      } else if (/\.(pdf|doc|docx|ppt|pptx|xls|xlsx)$/i.test(link)) {
        return renderDocument(link, index);
      } else {
        return (
          <div
            domPropsInnerHTML={strLink(link)}
          >
        </div>
        )
      }
    };

    return () => (
      <div class="question-show markdown-body" 
        domPropsInnerHTML={strLink(links.value)}
      >
      </div>
    );
  }
});