
/* component */
import SettingListPagination from "@src/modules/setting/gpt/components/pagination"
import NoDataViewNew from '@src/component/common/NoDataViewNew'
import TypeTag from '@src/modules/ai/components/type-tag';
import QuestionShow from '@src/modules/ai/views/home/<USER>/log/questionShow'
/* model */
import ComponentNameEnum from "@model/enum/ComponentNameEnum"
/* vue */
import { defineComponent, PropType } from "vue"
/* scss */
import "@src/modules/ai/views/home/<USER>/log/table.scss"
import '@highlightjs/cdn-assets/styles/github-dark.css';
/* type */
import { AiAgentLog } from "@src/modules/ai/types"
/* param */
import Page from "@model/Page"
/* utils */
import { formatDate, FormatTemplate } from "pub-bbx-utils"
import { AIAgentLogSourceCNNameEnum, AIAgentLogSourceEnum } from "@src/modules/ai/model/enum"

import { renderMarkdown } from "shb-ai-chat-md"
import 'shb-ai-chat-md/index.scss'
enum AiAgentLogTableEventEnum {
  PageNumChange = 'pageNumChange',
  PageSizeChange = 'pageSizeChange'
}

export default defineComponent({
  name: ComponentNameEnum.AiAgentLogTable,
  props: {
    logs: {
      type: Array as PropType<AiAgentLog[]>,
      default: () => []
    },
    page: {
      type: Object as PropType<Page>,
      default: () => ({})
    },
    loading: {
      type: Boolean,
      default: false
    },
    onPageNumChange: {
      type: Function
    },
    onPageSizeChange: {
      type: Function
    }
  },
  computed: {
    pageNum(): number {
      return this.page?.pageNum || 1
    },
    pageSize(): number {
      return this.page?.pageSize || 10
    },
    total(): number {
      return this.page?.total || 0
    },
    QuestionContentHTML(): string {
      try {
        return this.drawerData.question || ''
      } catch (error) {
        return this.drawerData.question || ''
      }
    },
    AnswerContentHTML(): string {
      try {
        return renderMarkdown(this.drawerData.newAnswer || '')
      } catch (error) {
        return this.drawerData.newAnswer || ''
      }
    }
  },
  data(){
    return {
      drawerShow : false,
      drawerData : {} 
    }
  },
  methods: {
    onChangePageNum(pageNum: number) {
      this.$emit(AiAgentLogTableEventEnum.PageNumChange, pageNum)
    },
    onChangePageSize(pageSize: number) {
      this.$emit(AiAgentLogTableEventEnum.PageSizeChange, pageSize)
    },
    handleClickSessionId(row){
      this.drawerShow = true
      this.drawerData = row
      console.log('row', row)
    },
    handleClose(){
      this.drawerShow = false
      this.drawerData = {}
    },
    renderTable() {
      return (
        <div class="ai-agent-log-table-main">
          <el-table
            data={this.logs}
            stripe
            border
          >

            <el-table-column 
              label="智能体"
              scopedSlots={{
                default: this.renderAgentColumn
              }}
            />
            <el-table-column
              label="会话Id"
              scopedSlots={{
                default: this.renderId
              }}
            >

            </el-table-column>
            {/* <el-table-column 
              label="AI应用"
              scopedSlots={{
                default: this.renderAiAppColumn
              }}
            /> */}
            <el-table-column 
              label="交互时间" 
              scopedSlots={{
                default: this.renderInteractionTimeColumn
              }}
            />
            <el-table-column 
              label="发起人" 
              scopedSlots={{
                default: this.renderCreatorColumn
              }}
            />
            <el-table-column 
              label="来源" 
              scopedSlots={{
                default: this.renderSourceColumn
              }}
            />
            <el-table-column 
              label="用户输入" 
              scopedSlots={{
                default: this.renderUserInputColumn
              }}
            />
            <el-table-column 
              label="结果输出" 
              show-overflow-tooltip={true}
              scopedSlots={{
                default: this.renderResultOutputColumn
              }}
            />
            <el-table-column 
              label="执行结果"
              scopedSlots={{
                default: this.renderExecutionResultColumn
              }}
            />

            <div slot="empty">
              {this.renderEmpty()}
            </div>

          </el-table>
        </div>
      )
    },
    renderAgentColumn({ row }: { row: AiAgentLog }) {
      const agentName = row.aiAgent?.name || ''
      return (
        <div>
          {agentName}
        </div>
      )
    },
    renderId({ row }: { row: AiAgentLog }) {
      const Id = row?.id || ''
      return (
        <div class='ai-agent-log-table-id' onClick={() => this.handleClickSessionId(row)}>
          {Id}
        </div>
      )
    },
    renderRightContent(){
      return (
        <div>
          <el-drawer
            visible={this.drawerShow}
            size='40%'
            onClose={this.handleClose}
            scopedSlots={{
              title: this.renderDrawerHeader,
              default: this.renderDrawerContent
            }}
          >
          </el-drawer>
        </div>
      )
    },
    renderDrawerHeader(){
      return (
        <div class="ai-agent-log-drawer-header">
          <div >
            <img class='ai-agent-log-drawer-header-img' src={this.drawerData.aiAgent?.icon} alt="" />
          </div>
          <div >
            <div class="header-item">
              <span class="label Id">会话ID: </span>
              <span class="value Id">{this.drawerData.id ||  ''}</span>
              <span>
                <TypeTag agentType={this.drawerData.aiAgent?.type} />
              </span>
              <span class="tag-wrapper">
                {this.drawerData.isError ? 
                  <el-tag type="danger" size="mini">失败</el-tag> : 
                  <el-tag type="success" size="mini">成功</el-tag>
                }
              </span>
            </div>
            <div class="header-item-a">
              <span class="label">发起人: </span>
              <span class="value">{this.drawerData.createUserVO?.displayName || ''}</span>
              {/* <span class="label ml20">来源: </span>
              <span class="value">门户名称</span> */}
              <span class="label ml20">交互时间: </span>
              <span class="value">{formatDate(this.drawerData.createTime, FormatTemplate.datetime)}</span>
            </div>
          </div>
         
        </div>
      )
    },
    renderDrawerContent(){
        return (
          <div class='ai-agent-log-drawer-content'>
            <div class='ai-agent-log-drawer-content-questions'>
              {this.renderDrawerContentQuestion()}
            </div>
            <div>
              {this.renderDrawerContentAnswer()}
            </div>
          </div>
        )
    },
    renderDrawerContentQuestion(){
      return(
        <QuestionShow 
          class='ai-agent-log-drawer-content-Question'
          linkString={this.QuestionContentHTML}
        >
        </QuestionShow>
      )
    },
    renderDrawerContentAnswer(){
      return(
       <div>
         <div class='ai-agent-log-drawer-content-answer-wrapper'>
          小宝AI
        </div>
        <div 
          class='ai-agent-log-drawer-content-answer'
        >
          <div
            class="vue-markdown markdown-body"
            domPropsInnerHTML={this.AnswerContentHTML}
          ></div>
        </div>
       </div>
      )
    },
    renderAiAppColumn({ row }: { row: AiAgentLog }) {
      const aiAppName = row.aiAgentApp?.name || ''
      return (
        <div>
          {aiAppName}
        </div>
      )
    },
    renderInteractionTimeColumn({ row }: { row: AiAgentLog }) {
      const createTime = formatDate(row.createTime, FormatTemplate.datetime)
      return (
        <div>
          {createTime}
        </div>
      )
    },
    renderCreatorColumn({ row }: { row: AiAgentLog }) {
      const creatorName = row.createUserVO?.displayName || ''
      return (
        <div>
          {creatorName}
        </div>
      )
    },
    renderSourceColumn({ row }: { row: AiAgentLog }) {
      const source = (row.source || '') as string
      return (
        <div>
          {source}
        </div>
      )
    },
    renderUserInputColumn({ row }: { row: AiAgentLog }) {
      const question = row.question || ''
      return (
        <div>
          {question}
        </div>
      )
    },
    renderResultOutputColumn({ row }: { row: AiAgentLog }) {
      const answer = row.answer || ''
      return (
        <div>
          {answer}
        </div>
      )
    },
    renderExecutionResultColumn({ row }: { row: AiAgentLog }) {
      const isError = row.isError || false

      if (isError) {
        return (
          <div>
            <el-tag type="danger">
              失败
            </el-tag>
          </div>
        )
      }

      return (
        <div>
          <el-tag type="success">
            成功
          </el-tag>
        </div>
      )
    },
    renderEmpty() {

      if (this.loading) {
        return null
      }
      
      return (
        <NoDataViewNew />
      )
      
    },
    renderPagination() {
      return (
        <SettingListPagination
          pageNum={this.pageNum}
          pageSize={this.pageSize}
          total={this.total}
          onChangePageNum={this.onChangePageNum}
          onChangePageSize={this.onChangePageSize}
        />
      )
    },
    renderContent() {
      return (
        <div class="ai-agent-log-table-content">
          {this.renderTable()}
          {this.renderPagination()}
        </div>
      )
    }
  },
  //渲染右侧弹窗
 
  render() {
    return (
      <div class="ai-agent-log-table">
        {this.renderContent()}
        {this.renderRightContent()}
      </div>
    )
  }
})