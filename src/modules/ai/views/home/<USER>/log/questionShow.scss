.question-show {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;
  p{
    margin-bottom: 0;
  }
  &-image {
    max-width: 200px;
    border-radius: 6px;
    border: 1px solid #eaedf1;
  }
  
  &-audio {
    width: 100%;
    max-width: 300px;
  }
  
  
  &-document {
    border-radius: 6px;
    overflow: hidden;
    
    .document-link {
      display: flex;
      align-items: center;
      padding: 12px;
      background-color: #f5f7fa;
      border-radius: 6px;
      gap: 12px;
      cursor: pointer;
      text-decoration: none;
      color: inherit;
      
      &:hover {
        background-color: #edf1f6;
      }
    }
    
    .document-icon {
      width: 40px;
      height: 50px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      position: relative;
      
      .iconfont {
        font-size: 36px;
      }
      
      .icon-file-pdf {
        color: #F56C6C;
      }
      
      .icon-file-word {
        color: #409EFF;
      }
      
      .icon-file-excel {
        color: #67C23A;
      }
      
      .icon-file-ppt {
        color: #E6A23C;
      }
      
      .extension {
        position: absolute;
        bottom: 0;
        font-size: 10px;
        color: #fff;
        background-color: rgba(0, 0, 0, 0.7);
        padding: 1px 3px;
        border-radius: 2px;
      }
    }
    
    .document-info {
      flex: 1;
      
      .document-name {
        font-size: 14px;
        color: #333;
        margin-bottom: 4px;
        word-break: break-word;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      .document-size {
        font-size: 12px;
        color: #909399;
      }
    }
  }
  
  &-analysis {
    width: 100%;
    
    .analysis-bg {
      background-color: #f5f7fa;
      border-radius: 6px;
      padding: 12px 16px;
      font-size: 14px;
      color: #333;
      line-height: 1.6;
    }
  }
  
  &-quality {
    .quality-card {
      display: flex;
      flex-direction: column;
      width: 150px;
      border-radius: 6px;
      overflow: hidden;
      cursor: pointer;
      transition: all 0.3s;
      border: 1px solid #eaedf1;
      
      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
      
      .quality-image {
        width: 100%;
        height: 100px;
        overflow: hidden;
        background-color: #f5f7fa;
        display: flex;
        align-items: center;
        justify-content: center;
        
        svg {
          max-width: 90%;
          max-height: 90%;
        }
      }
      
      .quality-text {
        padding: 10px;
        text-align: center;
        font-size: 14px;
        color: #333;
        background-color: #fff;
      }
    }
  }
} 