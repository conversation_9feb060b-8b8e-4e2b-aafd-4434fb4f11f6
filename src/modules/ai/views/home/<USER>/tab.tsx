
/* model */
import ComponentNameEnum from "@model/enum/ComponentNameEnum"
/* enum */
import { AIAgentViewTabCNNameEnum, AIAgentViewTabEnum } from "@src/modules/ai/model/enum"
import { AIAgentDataSummaryType, AIAgentType } from "@src/modules/ai/types"
/* component */
import AIAgentViewList from "@src/modules/ai/views/home/<USER>/list"
import AIAgentLog from "@src/modules/ai/views/home/<USER>/log"
import AIAgentChart from "@src/modules/ai/views/home/<USER>/chart"
/* vue */
import { defineComponent, PropType } from "vue"
/* scss */
import "@src/modules/ai/views/home/<USER>/tab.scss"
import { isFalsy } from "pub-bbx-utils"

type AiAgentChartComponentType = InstanceType<typeof AIAgentChart>

enum AiAgentViewTabEventEnum {
  Change = 'change',
  Edit = 'edit',
  Switch = 'switch',
  Create = 'create',
  ItemClick = 'itemClick',
  Search = 'search',
  AgentSelect = 'agentSelect',
  Delete = 'delete',
  Share = 'share'
}

export default defineComponent({
  name: ComponentNameEnum.AiAgentViewTab,
  props: {
    loading: {
      type: Boolean,
      default: false
    },
    value: {
      type: String as PropType<AIAgentViewTabEnum>,
      default: AIAgentViewTabEnum.List
    },
    agents: {
      type: Array as PropType<AIAgentType[]>,
      default: () => []
    },
    searchAgentType: {
      type: String
    },
    searchKeyword: {
      type: String,
      default: ''
    },
    aiAgentDataSummary: {
      type: Object as PropType<AIAgentDataSummaryType>,
      default: () => ({})
    },
    onChange: {
      type: Function as PropType<(tab: AIAgentViewTabEnum) => void>,
      default: () => {}
    },
    onEdit: {
      type: Function
    },
    onSwitch: {
      type: Function
    },
    onCreate: {
      type: Function
    },
    onItemClick: {
      type: Function
    },
    onSearch: {
      type: Function
    },
    onAgentSelect: {
      type: Function
    },
    onDelete: {
      type: Function
    },
    onShare: {
      type: Function
    }
  },
  computed: {
    aiAgentChartComponent(): AiAgentChartComponentType {
      return this.$refs.AIAgentChart as AiAgentChartComponentType
    },
    isChart(): boolean {
      return this.value === AIAgentViewTabEnum.Chart
    },
    classNames(): Record<string, boolean> {
      return {
        'ai-agent-view-tab': true,
        'ai-agent-view-tab-log': this.value === AIAgentViewTabEnum.Log,
        'ai-agent-view-tab-chart': this.value === AIAgentViewTabEnum.Chart,
        'ai-agent-view-tab-list': this.value === AIAgentViewTabEnum.List
      }
    }
  },
  watch: {
    value: {
      handler(newValue: AIAgentViewTabEnum) {
        this.onValueChange(newValue)
      }
    }
  },
  methods: {
    onValueChange(newValue: AIAgentViewTabEnum) {
      
    },
    handleTabChange(value: string) {
      this.$emit(AiAgentViewTabEventEnum.Change, value)
    },
    handleEdit(item: AIAgentType) {
      this.$emit(AiAgentViewTabEventEnum.Edit, item)
    },
    handleSwitch(value: boolean, agentId: number) {
      this.$emit(AiAgentViewTabEventEnum.Switch, value, agentId)
    },
    handleItemClick(item: AIAgentType) {
      this.$emit(AiAgentViewTabEventEnum.ItemClick, item)
    },
    handleCreate() {
      this.$emit(AiAgentViewTabEventEnum.Create)
    },
    handleSearch(value: string) {
      this.$emit(AiAgentViewTabEventEnum.Search, value)
    },
    handleAgentSelect(value: string) {
      this.$emit(AiAgentViewTabEventEnum.AgentSelect, value)
    },
    handleDelete(item: AIAgentType) {
      this.$emit(AiAgentViewTabEventEnum.Delete, item)
    },
    handleShare(item: AIAgentType) {
      this.$emit(AiAgentViewTabEventEnum.Share, item)
    },
    /** 
     * @description 渲染 Tabs
    */
    renderTabs() {
      return (
        <el-tabs 
          activeName={this.value} 
          onInput={this.handleTabChange}
        >
          {this.renderListTab()}
          {this.renderLogTab()}
          {this.renderChartTab()}
        </el-tabs>
      )
    },
    /** 
     * @description 渲染 Agent 管理 Tab
    */
    renderListTab() {
      const key = AIAgentViewTabEnum.List
      const label = AIAgentViewTabCNNameEnum[key]
      const name = key
      return (
        <el-tab-pane 
          key={key} 
          label={label} 
          name={name}
        >
          <AIAgentViewList 
            loading={this.loading}
            searchAgentType={this.searchAgentType}
            searchKeyword={this.searchKeyword}
            agents={this.agents}
            onCreate={this.handleCreate}
            onEdit={this.handleEdit}
            onItemClick={this.handleEdit}
            onEnableChange={this.handleSwitch}
            onSearch={this.handleSearch}
            onAgentSelect={this.handleAgentSelect}
            onDelete={this.handleDelete}
            onShare={this.handleShare}
          />
        </el-tab-pane>
      )
    },
    /** 
     * @description 渲染 Agent 日志 Tab
    */
    renderLogTab() {
      const key = AIAgentViewTabEnum.Log
      const label = AIAgentViewTabCNNameEnum[key]
      const name = key
      return (
        <el-tab-pane key={key} label={label} name={name}>
          <AIAgentLog
            agents={this.agents}
          />
        </el-tab-pane>
      )
    },
    /** 
     * @description 渲染 Agent 使用分析 Tab
    */
    renderChartTab() {
      const key = AIAgentViewTabEnum.Chart
      const label = AIAgentViewTabCNNameEnum[key]
      const name = key
      return (
        <el-tab-pane key={key} label={label} name={name}>
          {this.renderChartTabMain()}
        </el-tab-pane>
      )
    },
    renderChartTabMain() {
      if (isFalsy(this.isChart)) {
        return null
      }
      return (
        <AIAgentChart
          ref="AIAgentChart"
          agents={this.agents}
          aiAgentDataSummary={this.aiAgentDataSummary}
        />
      )
    }
  },
  render() {
    return (
      <div class={this.classNames}>
        {this.renderTabs()}
      </div>
    )
  }
})