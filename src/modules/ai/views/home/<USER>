/* api */
import { 
  addAIAgent, 
  addDingtalkAgent, 
  deleteAIAgent,
  getAIAgentDataStatistics, 
  getAIAgentList, 
  getDingtalkAgentList, 
  updateAgentStatus 
} from "@src/api/AIv2API"
/* components */
import { AIAgentViewHeader } from "@src/modules/ai/components"
import AIAgentViewData from "@src/modules/ai/views/home/<USER>/data"
import AIAgentViewTab from "@src/modules/ai/views/home/<USER>/tab"
import AIAgentCreateDialog from "@src/modules/ai/views/home/<USER>/create-dialog"
import AIAgentShareDialog from "@src/modules/ai/components/share-dialog"
/* enum */
import ComponentNameEnum from "@model/enum/ComponentNameEnum"
import { AIAgentTemplateEnum, AIAgentViewTabEnum } from "@src/modules/ai/model/enum"
/* model */
import MsgModel from "@model/MsgModel"
import { 
  AddDingtalkAgentParamType, 
  AIAgentCreateParamType, 
  AIAgentDeleteParamType, 
  AIAgentListParamType, 
  AIAgentStatusParamType 
} from "@src/modules/ai/model/param"
/* vue */
import { defineComponent } from "vue"
/* types */
import { AIAgentDataSummaryType, AIAgentType, DingtalkAssistantInstallInfo } from "@src/modules/ai/types"
/* scss */
import "@src/modules/ai/views/home/<USER>"
/* util */
import platform from "@src/platform"
import { openTabAIAgentDetail } from "@src/util/business/openTab"
import { DingtalkOpenAuthBaseParams, DingtalkOpenAuthTypeEnum, isEmpty, isFalsy, isNotEmpty } from "pub-bbx-utils"
import { openDingtalkAuth } from "@src/util/dd/auth"
import { DingtalkAuthTypeItem } from "@model/dingtalk"
import { confirm } from "@src/platform/message"
import Platform from '@src/util/platform'
import { isNotEqual } from "@src/util/type"
import { getRootWindowInitData, getRootWindowInitDataUserAuth } from '@src/util/window'
/* hooks */
import { useBanner } from "@src/modules/ai/views/home/<USER>/useBanner"

type AIAgentCreateDialogType = InstanceType<typeof AIAgentCreateDialog>
type AIAgentShareDialogType = InstanceType<typeof AIAgentShareDialog>

export default defineComponent({
  name: ComponentNameEnum.SettingGPTHomeView,
  setup() {
    const { showClose, showExpand, closeBanner, expandBanner }  = useBanner()
    return {
      showClose,
      showExpand,
      closeBanner,
      expandBanner
    }
  },
  data() {
    return {
      activeTab: AIAgentViewTabEnum.List,
      loading: false,
      agents: [] as AIAgentType[],
      aiAgentDataSummary: {} as AIAgentDataSummaryType,
      searchKeyword: '',
      searchAgentType: undefined as string | undefined,
    }
  },
  computed: {
    title(): string {
      return '智能体（AI Agent）管理'
    },
    subTitle(): string {
      return `
        深度学习企业知识与业务数据，深度集成客服、工单、产品、知识库等模块，支持多模态文件处理（图片、语音、视频），
        可个性化配置智能体提示词及交互方式，适配豆包、通义、DeepSeek等主流平台模型，
        具备组织级安全权限控制，助力企业打造专属AI应用，提升业务流程效率。
      `
    },
    attrs(): Record<string, any> {
      return {
        directives: [
          {
            name: 'loading',
            value: this.loading
          }
        ],
      }
    },
    createDialog(): AIAgentCreateDialogType {
      return this.$refs.AIAgentCreateDialog as AIAgentCreateDialogType
    },
    shareDialog(): AIAgentShareDialogType {
      return this.$refs.AIAgentShareDialog as AIAgentShareDialogType
    },
    editAuth(){
      const auth = getRootWindowInitDataUserAuth()
      return Boolean(auth?.AI_V2_create)
    },
    classNames(): Record<string, boolean> {
      return {
        'ai-agent-view': true,
        'ai-agent-view-close': this.showClose,
        'ai-agent-view-expand': this.showExpand
      }
    }
  },
  watch: {
    
  },
  mounted() {
    this.initialize()
  },
  methods: {
    async initialize() {
      try {
        // 获取数据统计
        this.fetchAIAgentDataStatistics()
        // 获取列表
        this.fetchAIAgentList()
      } catch (error) {
        console.error(error)
      }
    },
    handleEdit(item: AIAgentType) {
     
      const isUnEnabled = Boolean(item?.needIm)
      if (isUnEnabled) {
        console.log('该智能体未初始化，无法编辑')
        return
      }
      
      this.openTabAIAgentDetail(item)

    },
    handleSwitch(value: boolean, agentId: number) {

      const agent = this.agents.find(agent => agent.id === agentId)
      const isDingtalkAssistant = agent?.template == 'dingtalk'
      
      if (isDingtalkAssistant && value) {
        this.fetchUpdateDingtalkAgentStatus(value, agentId)
      } else {
        this.fetchUpdateAgentStatus(value, agentId)
      }

    },
    handleItemClick(item: AIAgentType) {
      this.openTabAIAgentDetail(item)
    },
    handleSearch(value: string) {
      this.searchKeyword = value
      this.fetchAIAgentList()
    },
    async handleAgentSelect(value: string) {
      this.loading = true
      this.searchAgentType = value
      this.agents = []
      this.fetchAIAgentList()
    },
    async handleDelete(item: AIAgentType) {

      const isConfirm = await confirm('您确定删除该 Agent 吗？')
      if (isFalsy(isConfirm)) {
        return
      }

      this.fetchDeleteAIAgent(item)
      
    },
    handleTabChange(value: AIAgentViewTabEnum) {
      this.activeTab = value
    },
    handleShare(item: AIAgentType) {
      this.shareDialog.open(item)
    },
    handleCreate() {
      this.createDialog.clearValidate()
      this.createDialog.openDialog()
    },
    handleAdd(value: AIAgentCreateParamType) {
      this.fetchAddAIAgent(value)
    },
    openTabAIAgentDetail(agent: AIAgentType) {
      
      if (!this.editAuth) {
        console.log('没有编辑权限')
        return
      }

      openTabAIAgentDetail(agent)

    },
    closeCreateDialog() {
      this.createDialog.closeDialog()
    },
    async fetchUpdateAgentStatus(value: boolean, agentId: number) {
      try {

        const params: AIAgentStatusParamType = {
          id: agentId,
          status: Number(value)
        }
        const result = await updateAgentStatus(params)
        const isFail = MsgModel.isFail(result)
        if (isFail) {
          const message = MsgModel.getMessage(result)
          this.$message.error(message)
          return
        }
        
        this.fetchAIAgentList()
        this.fetchAIAgentDataStatistics()

      } catch (error) {
        console.error(error)
      }
    },
    async fetchUpdateDingtalkAgentStatus(value: boolean, agentId: number) {
      try {

        // 获取钉钉 AI助理 列表
        const dingtalkAgentResult = await getDingtalkAgentList()
        const isDingtalkAgentResultFail = MsgModel.isFail(dingtalkAgentResult)
        if (isDingtalkAgentResultFail) {
          const message = MsgModel.getMessage(dingtalkAgentResult)
          this.$message.error(message)
          return
        }
        
        const dingtalkAgentList = MsgModel.getData<DingtalkAssistantInstallInfo[]>(dingtalkAgentResult, [])
        if (isNotEmpty(dingtalkAgentList)) {
          this.fetchUpdateDingtalkAgentStatus(value, agentId)
          return;
        }

        console.log('openDingtalkAuth')
      
        const authParams: DingtalkOpenAuthBaseParams = {
          rpcScope: 'Assistant.Management.Write',
          type: DingtalkOpenAuthTypeEnum.Personal
        }        
        const authCode = await openDingtalkAuth(authParams as DingtalkAuthTypeItem)

        console.log('authCode', authCode)

        const addParams: AddDingtalkAgentParamType = {
          authCode: authCode as unknown as string
        }
        const addResult = await addDingtalkAgent(addParams)
        const isAddFail = MsgModel.isFail(addResult)
        if (isAddFail) {
          const message = MsgModel.getMessage(addResult)
          this.$message.error(message)
          return
        }
        
        this.fetchUpdateAgentStatus(value, agentId)
        
      } catch (error) {
        console.error(error)
      }
    },
    async fetchAIAgentListNoLoading() {
      const isShowLoading = false
      return this.fetchAIAgentList(isShowLoading)
    },
    async fetchAIAgentList(isShowLoading?: boolean) {
      try {

        if (isShowLoading) {
          this.loading = true
        }
        
        const type = isEmpty(this.searchAgentType) ? undefined : Number(this.searchAgentType)
        const params: AIAgentListParamType = {
          keyword: this.searchKeyword,
          type
        }
        const result = await getAIAgentList(params)
        
        const isFail = MsgModel.isFail(result)
        if (isFail) {
          const message = MsgModel.getMessage(result)
          this.$message.error(message)
          return
        }
        
        let agents = MsgModel.getData<AIAgentType[]>(result, [])
        // 如果当前不是钉钉桌面版，则过滤掉钉钉 AI助理
        const isDingtalkDesktop = Platform.isDingDingDesktop()
        if (isFalsy(isDingtalkDesktop)) {
          agents = agents.filter(agent => isNotEqual(agent.template, AIAgentTemplateEnum.Dingtalk))
        }

        this.agents = agents

      } catch (error) {
        console.error(error)
      } finally {
        this.loading = false
      }
    },
    async fetchDeleteAIAgent(item: AIAgentType) {
      try {

        this.loading = true

        const agentId = Number(item.id)
        const params: AIAgentDeleteParamType = {
          id: agentId
        }
        const result = await deleteAIAgent(params)

        const isFail = MsgModel.isFail(result)
        if (isFail) {
          const message = MsgModel.getMessage(result)
          this.$message.error(message)
          return
        }
        
        this.$message.success('删除成功')

        this.fetchAIAgentList()
        this.fetchAIAgentDataStatistics()

      } catch (error) {
        console.error(error)
      } finally {
        this.loading = false
      }
    },
    async fetchAIAgentDataStatistics() {
      try {

        const result = await getAIAgentDataStatistics()

        const isFail = MsgModel.isFail(result)
        if (isFail) {
          const message = MsgModel.getMessage(result)
          this.$message.error(message)
          return
        }
  
        this.aiAgentDataSummary = MsgModel.getData<AIAgentDataSummaryType>(result, {})

      } catch (error) {
        console.error(error)
      }
    },
    async fetchAddAIAgent(params: AIAgentCreateParamType) {
      try {

        this.loading = true
        
        const newParams = {
          ...params,
          template: 'custom'
        }
        const result = await addAIAgent(newParams)

        const isFail = MsgModel.isFail(result)
        if (isFail) {
          const message = MsgModel.getMessage(result)
          this.$message.error(message)
          return
        }

        const data = MsgModel.getData<{
          agentId: number,
          agentAppId: number
        }>(result, {})

        const agentId = data?.agentId
        const agentAppId = data?.agentAppId

        if (isFalsy(agentId) || isFalsy(agentAppId)) {
          this.$message.error('创建失败')
          return
        }
         
        this.openTabAIAgentDetail({
          id: agentId as number,
          agentAppId,
          name: params.name
        } as AIAgentType)

        this.initialize()
        this.closeCreateDialog()

      } catch (error) {
        console.error(error)
      } finally {
        this.loading = false
      }
    },
    descriptionButtonClickHandler() {
      platform.openLink('https://doc.shb.ltd/shb_xoqazk/ia4qctp2ly559d1m/eka8tf9wv8x43arx/lvh0cf5o94rc04pp.html')
    },
    /**
     * @description 渲染使用说明按钮
     */
    renderDescriptionButton() {
      return (
        <el-button
          v-el-button-blur
          type="plain-third"
          onClick={this.descriptionButtonClickHandler}
        >
          使用说明
        </el-button>
      )
    },
    renderHeader() {
      return (
        <transition name="el-fade-in-linear">
          <div v-show={this.showClose}>
            <AIAgentViewHeader
              title={this.title}
              subTitle={this.subTitle}
            >
              <div slot="button">
                {this.renderDescriptionButton()}
              </div>
            </AIAgentViewHeader>
            {this.renderHeaderBanner()}
          </div>
        </transition>
        
      )
    },
    renderHeaderBanner() {
      if (!this.showClose) {
        return null
      }
      return (
        <div class="close-header-box" onClick={this.closeBanner}>
          <i class="iconfont icon-close"></i>
        </div>
      )
    },
    renderTab() {
      return (
        <div class="p-rel" {...this.attrs}>
          <AIAgentViewTab
            loading={this.loading}
            value={this.activeTab}
            agents={this.agents as AIAgentType[]}
            searchAgentType={this.searchAgentType}
            searchKeyword={this.searchKeyword}
            aiAgentDataSummary={this.aiAgentDataSummary}
            onCreate={this.handleCreate}
            onEdit={this.handleEdit}
            onSwitch={this.handleSwitch}
            onSearch={this.handleSearch}
            onAgentSelect={this.handleAgentSelect}
            onDelete={this.handleDelete}
            onChange={this.handleTabChange}
            onShare={this.handleShare}
          >
            
          </AIAgentViewTab>
          {this.renderCollegeHeader()}
        </div>
      )
    },
    renderCollegeHeader() {
      if (!this.showExpand) {
        return null
      }
      return (
        <el-tooltip class="item" effect="dark" content="功能介绍" placement="bottom">
          <div class="college-header-box" onClick={this.expandBanner}>
            <i class="icon-zhankai1 iconfont"></i>
          </div>
        </el-tooltip>
      )
    },
    renderCreateDialog() {
      return (
        <AIAgentCreateDialog 
          ref="AIAgentCreateDialog"
          onAdd={this.handleAdd}
        />
      )
    },
    renderShareDialog() {
      return (
        <AIAgentShareDialog
          ref="AIAgentShareDialog"

        />
      )
    }
  },
  render() {
    return (
      <div class={this.classNames}>
        {this.renderHeader()}
        {this.renderTab()}
        {this.renderCreateDialog()}
        {this.renderShareDialog()}
      </div>
    )
  }
})