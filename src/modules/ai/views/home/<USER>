
.ai-agent-view {

  padding: 12px;
  min-width: 800px;

  .ai-agent-view-data {
    margin-top: 12px;
  }

  .ai-agent-view-tab {
    margin-top: 12px;
  }

}

.base-tip-el {
  max-width: calc(100vw - 200px);
}

.college-header-box{
  width: 20px;
  height: 20px;
  position: absolute;
  right: 24px;
  top: 14px;
  z-index: 99;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.close-header-box{
  width: 20px;
  height: 20px;
  position: absolute;
  right: 24px;
  top: 24px;
  z-index: 99;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.p-rel{
  position: relative;
}

// 屏幕小于 1000px 时，隐藏 header 的右侧内容
@media (max-width: 1000px) {
  .ai-agent-view-header-right {
    display: none;
  }
}

.ai-agent-view-expand {
  .ai-agent-log-table-content .el-table__body-wrapper {
    min-height: calc(100vh - 270px);
  }
  .ai-agent-view-tab-log {
    .el-tab-pane {
      padding-bottom: 0;
    }
  }
}