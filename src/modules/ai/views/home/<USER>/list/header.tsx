/* components */
import AiAgentSelect from "@src/modules/ai/views/home/<USER>/agent-select"
/* model */
import ComponentNameEnum from "@model/enum/ComponentNameEnum"
/* vue */
import { defineComponent, PropType } from "vue"
/* types */
import { AIAgentType } from "@src/modules/ai/types/agent"
/* scss */
import "@src/modules/ai/views/home/<USER>/list/header.scss"

import { getRootWindowInitData, getRootWindowInitDataUserAuth } from '@src/util/window'

enum AiAgentViewListHeaderEventEnum {
  Create = 'create',
  Search = 'search',
  AgentSelect = 'agentSelect'
}

export default defineComponent({
  name: ComponentNameEnum.AiAgentViewListHeader,
  props: {
    value: {
      type: String,
      default: ''
    },
    agents: {
      type: Array as PropType<AIAgentType[]>,
      default: () => []
    },
    searchAgentType: {
      type: String
    },
    searchKeyword: {
      type: String
    },
    onCreate: {
      type: Function,
      default: () => {}
    },
    onSearch: {
      type: Function,
      default: () => {}
    },
    onAgentSelect: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      insideSearchKeyword: ''
    }
  },
  watch: {
    searchKeyword(value: string) {
      this.insideSearchKeyword = value
    }
  },
  computed:{
    editAuth(){
      const auth = getRootWindowInitDataUserAuth()
      return Boolean(auth?.AI_V2_create)
    },
    createAuth(){
      const auth = getRootWindowInitDataUserAuth()
      return Boolean(auth?.AI_V2_BUILT)
    }
  },
  methods: {
    onCreateHandler() {
      this.$emit(AiAgentViewListHeaderEventEnum.Create)
    },
    onKeywordInputHandler(value: string) {
      this.insideSearchKeyword = value
    },
    onKeywordChangeHandler(value: string) {
      this.insideSearchKeyword = value
      this.onSearchHandler()
    },
    onClearHandler() {
      this.onKeywordInputHandler('')
      this.onSearchHandler()
    },
    onSearchHandler() {
      this.$emit(AiAgentViewListHeaderEventEnum.Search, this.insideSearchKeyword)
    },
    onAgentSelectHandler(value: string) {
      this.$emit(AiAgentViewListHeaderEventEnum.AgentSelect, value)
    },
    renderCreateButtonWrapper() {
      if (!this.createAuth) {
        return null
      }
      return this.renderCreateButton()
    },
    renderCreateButton() {
      return (
        <div class="ai-agent-view-list-header-create-button">
          <el-button
            type="primary"
            onClick={this.onCreateHandler}
          >
            新建智能体
          </el-button>
        </div>
      )
    },
    renderContent() {
      return (
        <div class="ai-agent-view-list-header-content">
          {this.renderAgentSelectWrapper()}
          {this.renderSearch()}
        </div>
      )
    },
    renderAgentSelectWrapper() {
      const isNotCreateAuth = !this.createAuth
      const firstAgent = this.agents[0] || {}
      const isSameAgentsTypes = this.agents.every(agent => agent.type === firstAgent?.type)
      // 如果没有创建权限，且所有智能体类型相同，则不显示智能体类型切换
      if (isNotCreateAuth && isSameAgentsTypes) {
        return null
      }
      return this.renderAgentSelect()
    },
    renderAgentSelect() {
      return (
        <AiAgentSelect 
          value={this.searchAgentType}
          onChange={this.onAgentSelectHandler}
        />
      )
    },
    renderSearch() {
      return (
        <div class="ai-agent-view-list-header-search">
          <el-input
            placeholder="请输入名称搜索"
            clearable
            value={this.insideSearchKeyword}
            onInput={this.onKeywordInputHandler}
            onClear={this.onClearHandler}
            onChange={this.onKeywordChangeHandler}
          >
            <el-button 
              slot="append"
              type="primary"
              onClick={this.onSearchHandler}
            >
              搜索
            </el-button>
          </el-input>
        </div>
      )
    }
  },
  render() {
    return (
      <div class="ai-agent-view-list-header">
        {this.renderCreateButtonWrapper()}
        {this.renderContent()}
      </div>
    )
  }
})