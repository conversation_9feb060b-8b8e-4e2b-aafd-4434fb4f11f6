/* component */
import AIAgentViewListHeader from "@src/modules/ai/views/home/<USER>/list/header"
import AIAgentViewListItem from "@src/modules/ai/views/home/<USER>/list/item"
/* component */
import NoDataViewNew from '@src/component/common/NoDataViewNew'
/* model */
import ComponentNameEnum from "@model/enum/ComponentNameEnum"
/* vue */
import { defineComponent, PropType } from "vue"
/* scss */
import "@src/modules/ai/views/home/<USER>/list/index.scss"
/* type */
import { AIAgentType } from "@src/modules/ai/types"
import { isEmpty } from "pub-bbx-utils"

enum AiAgentViewListEventEnum {
  Edit = 'edit',
  EnableChange = 'enableChange',
  Create = 'create',
  ItemClick = 'itemClick',
  Delete = 'delete',
  Search = 'search',
  AgentSelect = 'agentSelect',
  Share = 'share'
}

export default defineComponent({
  name: ComponentNameEnum.AiAgentViewList,
  props: {
    agents: {
      type: Array as PropType<AIAgentType[]>,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    searchAgentType: {
      type: String
    },
    searchKeyword: {
      type: String
    },
    onCreate: {
      type: Function as PropType<() => void>,
      default: () => {}
    },
    onEdit: {
      type: Function
    },
    onEnableChange: {
      type: Function
    },
    onItemClick: {
      type: Function
    },
    onSearch: {
      type: Function
    },
    onAgentSelect: {
      type: Function
    },
    onDelete: {
      type: Function
    },
    onShare: {
      type: Function
    }
  },
  data() {
    return {
    }
  },
  methods: {
    handleListItemEdit(item: AIAgentType) {
      this.$emit(AiAgentViewListEventEnum.Edit, item)
    },
    handleListItemEnableChange(value: boolean, item: AIAgentType) {
      this.$emit(AiAgentViewListEventEnum.EnableChange, value, item.id)
    },
    handleCreate() {
      this.$emit(AiAgentViewListEventEnum.Create)
    },
    handleSearch(value: string) {
      this.$emit(AiAgentViewListEventEnum.Search, value)
    },
    handleAgentSelect(value: string) {
      this.$emit(AiAgentViewListEventEnum.AgentSelect, value)
    },
    handleListItemClick(item: AIAgentType) {
      this.$emit(AiAgentViewListEventEnum.ItemClick, item)
    },
    handleListItemDelete(item: AIAgentType) {
      this.$emit(AiAgentViewListEventEnum.Delete, item)
    },
    handleListItemShare(item: AIAgentType) {
      this.$emit(AiAgentViewListEventEnum.Share, item)
    },
    renderHeader() {
      return (
        <AIAgentViewListHeader 
          agents={this.agents}
          searchAgentType={this.searchAgentType}
          searchKeyword={this.searchKeyword}
          onCreate={this.handleCreate}
          onSearch={this.handleSearch}
          onAgentSelect={this.handleAgentSelect}
        />
      )
    },
    renderList() {

      if (isEmpty(this.agents) && !this.loading) {
        return (
          <NoDataViewNew />
        )
      }

      return (
        <div class="ai-agent-view-list-container">
          {this.agents.map(this.renderListItem)}
        </div>
      )
    },
    renderListItem(agent: AIAgentType) {
      const handleListItemEditWrapper = (item: AIAgentType) => {
        this.handleListItemEdit(item)
      }
      const handleListItemClickWrapper = (item: AIAgentType) => {
        this.handleListItemClick(item)
      }
      const handleListItemDeleteWrapper = (item: AIAgentType) => {
        this.handleListItemDelete(item)
      }
      const handleListItemShareWrapper = (item: AIAgentType) => {
        this.handleListItemShare(item)
      }
      return (
        <AIAgentViewListItem 
          item={agent}
          onEdit={handleListItemEditWrapper}
          onEnableChange={this.handleListItemEnableChange}
          onClick={handleListItemClickWrapper}
          onDelete={handleListItemDeleteWrapper}
          onShare={this.handleListItemShare}
        />
      )
    },
    renderContent() {
      return (
        <div class="ai-agent-view-list-content">
          {this.renderHeader()}
          {this.renderList()}
        </div>
      )
    }
  },
  render() {
    return (
      <div class="ai-agent-view-list">
        {this.renderContent()}
      </div>
    )
  }
})