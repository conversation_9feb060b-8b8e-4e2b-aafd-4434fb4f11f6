
/* model */
import ComponentNameEnum from "@model/enum/ComponentNameEnum"
import { AIAgentTrailTypeEnum, AIAgentTrailTypeTemplateEnum, AIAgentTypeEnum, AiModelEnum } from "@src/modules/ai/model/enum"
import { AIAgentAppType, AIAgentType } from "@src/modules/ai/types"
/* components */
import TypeTag from "@src/modules/ai/components/type-tag"
import ModelItem from "@src/modules/ai/components/model-item"
/* util */
import { setClipboardData } from "@src/util/dom"
import { message } from "@src/util/message"
import { formatDate, FormatTemplate, isFalsy, postPage } from "pub-bbx-utils"
import { useTenantId, useRootUser } from '@hooks/useRootWindow'
/* vue */
import { defineComponent, PropType } from "vue"
/* scss */
import "@src/modules/ai/views/home/<USER>/list/item.scss"
import { defaultAgentIcon as xiaoBaoIcon } from '@src/modules/ai/views/edit/mock'
import { getRootWindowInitData, getRootWindowInitDataUserAuth } from '@src/util/window'
import { saveValueAddedClue } from "@src/api/Clue"

enum AiAgentViewListItemEventEnum {
  EnableChange = 'enableChange',
  Edit = 'edit',
  Delete = 'delete',
  Click = 'click',
  Share = 'share'
}

export default defineComponent({
  name: ComponentNameEnum.AiAgentViewListItem,
  props: {
    item: {
      type: Object as PropType<AIAgentType>,
      default: () => ({}),
      required: true
    },
    onEnableChange: {
      type: Function
    },
    onEdit: {
      type: Function
    },
    onDelete: {
      type: Function
    },
    onClick: {
      type: Function
    },
    onShare: {
      type: Function
    }
  },
  computed: {
    // 名称
    name(): string {
      return this.item.name || ''
    },
    // 描述
    description(): string {
      return this.item.description || ''
    },
    // 模型
    model(): AiModelEnum {
      return this.item.model || AiModelEnum.DOU_BAO
    },
    // 类型
    type(): number {
      return this.item.type || AIAgentTypeEnum.Chat
    },
    // 是否启用
    enable(): boolean {
      return Boolean(this.item.enable)
    },
    // 是否是系统 Agent
    isSystem(): boolean {
      return Boolean(this.item.system)
    },
    // 更新时间
    updateTime(): string {
      return formatDate(this.item.updateTime, FormatTemplate.dateMinute) as string
    },
    // 创建用户名称
    createUserName(): string {
      return this.item.createUser?.displayName || ''
    },
    //修改名称
    updateUserEntity(): string {
      return this.item?.updateUserEntity?.displayName || ''
    },
    // 应用列表
    appList(): string[] {
      return this.item.aiAppList || []
    },
    // 是否是聊天助手
    isChatAgent(): boolean {
      return this.type === AIAgentTypeEnum.Chat
    },
    // 是否是工作流
    isWorkflowAgent(): boolean {
      return this.type === AIAgentTypeEnum.Workflow
    },
    url(): string {
      return this.item.shareUrl || ''
    },
    editAuth(){
      const auth = getRootWindowInitDataUserAuth()
      return Boolean(auth?.AI_V2_create)
    },
    unEnabled(): boolean {
      return Boolean(this.item?.needIm)
    },
    classNames(): Record<string, boolean> {
      return {
        'ai-agent-view-list-item-container': true,
        'ai-agent-view-list-item-container-un-enabled': this.unEnabled
      }
    }
  },
  methods: {
    onContactCustomerHandler() {

      const template = this.item.template || ''
      const type = AIAgentTrailTypeTemplateEnum[template as keyof typeof AIAgentTrailTypeTemplateEnum] || AIAgentTrailTypeEnum.AI_AGENT
      
      saveValueAddedClue({ type })
      
      postPage({
        action: 'shb.system.openChat',
        data: {},
      })
      
    },
    onEnableChangeHandler(value: boolean) {
      this.$emit(AiAgentViewListItemEventEnum.EnableChange, value, this.item)
    },
    nativeOnSwitchClickHandler(event: Event) {
      event.stopPropagation()
    },
    onDropdownClickHandler(event: Event) {
      event.stopPropagation()
    },
    onCopyUrlHandler() {

      const tenantIdRef = useTenantId()
      const locationPath = window.location.origin
      const shareUrl = `${locationPath}/shb/home/<USER>/agent/chat?agentId=${this.item.id}&tenantId=${tenantIdRef.value}`

      setClipboardData(shareUrl, () => {
        message.success('复制成功')
      })

    },
    onShareHandler() {
      this.$emit(AiAgentViewListItemEventEnum.Share, this.item)
    },
    onDeleteHandler() {
      this.$emit(AiAgentViewListItemEventEnum.Delete, this.item)
    },
    onEditHandler() {
      this.$emit(AiAgentViewListItemEventEnum.Edit, this.item)
    },
    onItemClickHandler() {
      this.$emit(AiAgentViewListItemEventEnum.Click, this.item)
    },
    renderItem() {
      return (
        <div 
          class="ai-agent-view-list-item"
          onClick={this.onItemClickHandler}
        >
          <div class="ai-agent-view-list-item-content">
            <div class="ai-agent-view-list-item-content-header">
              <div class="ai-agent-view-list-item-content-header-left">
                {this.renderLogo()}
              </div>
              <div class="ai-agent-view-list-item-content-header-right">
                {this.renderHeader()}
                {this.renderSubHeader()}
              </div>
            </div>
            {this.renderAgentDescription()}
          </div>
          {this.renderFooter()}
        </div>
      )
    },
    renderLogo() {
      
      const logo = this.item.icon || xiaoBaoIcon
      
      return (
        <div class="ai-agent-view-list-item-content-header-left-logo">
          <img src={logo} />
        </div>
      )
    },
    renderHeader() {
      return (
        <div class="ai-agent-view-list-item-header">
          <div class="ai-agent-view-list-item-header-left">
            {this.renderAgentName()}
          </div>
          <div class="ai-agent-view-list-item-header-right">
            {this.renderAgentStatusSwitchWrapper()}
          </div>
        </div>
      )
    },
    renderAgentName() {
      return (
        <div class="ai-agent-view-list-item-header-left-name">
          {this.name}
        </div>
      )
    },
    renderAgentType() {
      return (
        <div>
          <TypeTag agentType={this.type} />
        </div>
      )
    },
    renderAgentStatusSwitchWrapper() {
      if (!this.editAuth) {
        return null
      }
      return this.renderAgentStatusSwitch()
    },
    renderAgentStatusSwitch() {
      return (
        <div class="ai-agent-view-list-item-header-left-status-switch">
          <el-switch
            value={this.enable}
            onInput={this.onEnableChangeHandler}
            nativeOnClick={this.nativeOnSwitchClickHandler}
          />
        </div>
      )
    },
    renderSubHeader() {
      return (
        <div class="ai-agent-view-list-item-sub-header">
          {this.renderSystemLabel()}
          {this.renderAgentModel()}
          {this.renderAgentType()}
        </div>
      )
    },
    renderSystemLabel() {

      if (isFalsy(this.isSystem)) {
        return null
      }

      return (
        <div class="ai-agent-view-list-item-label-tag">
          系统
        </div>
      )
    },
    renderAgentModel() {
      return (
        <div class="ai-agent-view-list-item-sub-header-model">
          <ModelItem value={this.model} />
        </div>
      )
    },
    renderAgentDescription() {
      return (
        <div class="ai-agent-view-list-item-sub-header-description">
          {this.description}
        </div>
      )
    },
    renderAppList() {
      const appListStr = this.appList.join('、')
      return (
        <div class="ai-agent-view-list-item-sub-header-app-list">
          <span>
            智能体:
          </span>
          &nbsp;
          <span>
            {appListStr}
          </span>
        </div>
      )
    },
    renderLastUpdateTime() {
      return (
        <div class="ai-agent-view-list-item-sub-header-last-update-time">
          <span>
            最近更新:
          </span>
          &nbsp;
          <span class="ai-agent-view-list-item-sub-header-last-update-time-user-name">
            {this.updateUserEntity}
          </span>
          &nbsp;
          <span>
            {this.updateTime}
          </span>
        </div>
      )
    },
    renderFooter() {
      return (
        <div class="ai-agent-view-list-item-footer">
          {this.renderFooterLeft()}
          {this.renderFooterRight()}
        </div>
      )
    },
    renderFooterLeft() {
      return (
        <div class="ai-agent-view-list-item-footer-left">
          {this.renderLastUpdateTime()}
        </div>
      )
    },
    renderFooterRight() {
      // 未初始化 或者 没有编辑权限，不显示
      if (this.unEnabled || !this.editAuth) {
        return null
      }

      return (
        <div class="ai-agent-view-list-item-footer-right">
          <el-dropdown>
            <span 
              class="ai-agent-view-list-item-footer-right-el-dropdown"
              onClick={this.onDropdownClickHandler}
            >
              <i class="el-icon-more el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              {this.renderEditDropdownWrapper()}
              <el-dropdown-item>
                <span
                  class="ai-agent-view-list-item-footer-more-button-item"
                  onClick={this.onShareHandler}
                >
                  分享
                </span>
              </el-dropdown-item>
              {this.renderDeleteDropdownWrapper()}
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      )
    },
    renderEditDropdownWrapper() {
      if (!this.editAuth) {
        return null
      }
      return this.renderEditDropdown()
    },
    renderEditDropdown() {
      return (
        <el-dropdown-item>
          <div
            onClick={this.onEditHandler}
          >
            编辑
          </div>
        </el-dropdown-item>
      )
    },
    renderDeleteDropdownWrapper() {
      if (this.isSystem) {
        return null
      }
      if (!this.editAuth) {
        return null
      }
      return this.renderDeleteDropdown()
    },
    renderDeleteDropdown() {
      return (
        <el-dropdown-item>
          <div
            onClick={this.onDeleteHandler}
          >
            删除
          </div>
      </el-dropdown-item>
      )
    },
    renderLeftButton() {
      return this.isSystem ? this.renderLeftButtonWithSystem() : this.renderLeftButtonWithNotSystem()
    },
    renderLeftButtonWithSystem() {
      return null
    },
    renderLeftButtonWithNotSystem() {
      return this.renderDeleteButton()
    },
    renderDeleteButton() {
      return (
        <div
          class="ai-agent-view-list-item-footer-delete-button"
          onClick={this.onDeleteHandler}
        >
          <i class="iconfont icon-delete"></i>
          删除
        </div>
      )
    },
    renderEditButton() {
      return (
        <div 
          class="ai-agent-view-list-item-footer-edit-button"
          onClick={this.onEditHandler}
        >
          <i class="iconfont icon-edit-square"></i>
          编辑
        </div>
      )
    },
    renderUnEnabledContainer() {
      return (
        <div class="ai-agent-view-list-item-un-enabled-container">
          <el-button 
            type="primary"
            onClick={this.onContactCustomerHandler}
          >
            联系客服试用
          </el-button>
        </div>
      )
    }
  },
  render() {
    return (
      <div class={this.classNames}>
        {this.renderItem()}
        {this.renderUnEnabledContainer()}
      </div>
    )
  }
})