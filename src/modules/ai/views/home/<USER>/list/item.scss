
.ai-agent-view-list-item-container {
  border: 1px solid #e1e3ea;
  border-radius: 8px;
  padding-top: 16px;
  transition: all .5s;
  &:hover{
    cursor: pointer;
    transform: translateY(-4px);
    box-shadow: 0px 10px 10px rgba(0, 0, 0, 0.3);
  }
}

.ai-agent-view-list-item-content {
  padding: 0px 16px;
  padding-bottom: 16px;
  min-height: 110px;
}

.ai-agent-view-list-item-header {
  height: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.ai-agent-view-list-item-header-left {
  display: flex;
  align-items: center;
}

.ai-agent-view-list-item-header-left-name {
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  color: #262626;
  margin-right: 8px;
  @include text-ellipsis;
}

.ai-agent-view-list-item-sub-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.ai-agent-view-list-item-label-tag {
  width: 44px;
  height: 21px;
  display: flex;
  flex-direction: column;
  padding: 0px 8px;
  border-radius: 3px;
  background: #F0F2F5;
  font-size: 12px;
  line-height: 20px;
  margin-right: 6px;
}

.ai-agent-view-list-item-sub-header-last-update-time,
.ai-agent-view-list-item-sub-header-app-list {
  color: #595959;
  margin-bottom: 4px;
  font-size: 12px;
  @include text-ellipsis;
}

.ai-agent-view-list-item-footer {
  height: 38px;
  display: flex;
  padding: 2px 0px;
  box-sizing: border-box;
  border-width: 1px 0px 0px 0px;
  border-style: solid;
  border-color: #E4E7ED;
  color: #595959;
  display: flex;
  align-items: center;
  font-size: 12px;
  padding-left: 16px;
  padding-right: 16px;
  .ai-agent-view-list-item-footer-left {
    flex: 1;
    max-width: calc(100% - 30px);
  }
  i {
    margin-right: 4px;
  }
}

.ai-agent-view-list-item-footer-right-el-dropdown {
  cursor: pointer;
  height: 28px;
  display: flex;
  width: 28px;
  border-radius: 4px;
  align-items: center;
  justify-content: center;
  &:hover {
    background: #F0F2F5;
  }
}

.ai-agent-view-list-item-footer-more-button,
.ai-agent-view-list-item-footer-copy-url-button,
.ai-agent-view-list-item-footer-edit-button {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.ai-agent-view-list-item-sub-header-model {
  border-radius: 3px;
  background: #F0F2F5;
  font-size: 12px;
  padding: 2px 6px;
  margin-right: 6px;
}

.ai-agent-view-list-item-footer-more-button-item {
  cursor: pointer;
  width: 100%;
}

.ai-agent-view-list-item-content-header {
  display: flex;
  justify-content: center;
}

.ai-agent-view-list-item-content-header-left {
  margin-right: 10px;
  img {
    height: 48px;
    width: 48px;
    border-radius: 4px;
  }
}

.ai-agent-view-list-item-content-header-left-logo {
  display: flex;
  align-items: center;
  justify-content: center;
}

.ai-agent-view-list-item-content-header-right {
  flex: 1;
}

.ai-agent-view-list-item-header-right {
  cursor: pointer;
}

.ai-agent-view-list-item-container {
  position: relative;
}

.ai-agent-view-list-item-un-enabled-container {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  background-color: rgba(255, 255, 255, 0.8);
  justify-content: center;
  align-items: center;
  .el-button {
    height: 40px;
  }
}
.ai-agent-view-list-item-container-un-enabled {
  &:hover {
    .ai-agent-view-list-item-un-enabled-container {
      display: flex;
      transition: all 1s;
    }
  }
}

.ai-agent-view-list-item-sub-header-last-update-time {
  display: flex;
  align-items: center;
  .ai-agent-view-list-item-sub-header-last-update-time-user-name {
    max-width: calc(100% - 160px);
    @include text-ellipsis;
  }
}