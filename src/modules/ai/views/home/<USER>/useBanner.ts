import { ref, computed } from 'vue'
/* http */
import { creatServerCachApi, getServerCachApi } from '@src/api/GuideApi.js'
import { getRootWindowInitDataUser } from '@src/util/window'

const useStorage = () => {
    
    const loginUser = getRootWindowInitDataUser()
    const userId = loginUser?.userId
    const storage = window.localStorage
    
    const getLocal = (key: string) => {
        const _key = `${userId}-${key}`
        return storage.getItem(_key)
    }
    const setLocal = (key: string, value: string) => {
        const _key = `${userId}-${key}` 
        storage.setItem(_key, value)
    }
    return {
        getLocal,
        setLocal
    }
}
/**
 * @description use banner close hooks
 */
const useBanner = () => {
    const _def_banner = ref('initAIAgentBanner')
    const showClose = ref(true)
    const showExpand = ref(false)
    // 获取本地存储hooks
    const { getLocal, setLocal } = useStorage()

    const closeBanner = () => {
        showClose.value = false
        showExpand.value = true
        // 存储本地
        setLocal(_def_banner.value, JSON.stringify({ showClose: false, showExpand: true }))
        setIntBanner({ showClose: false, showExpand: true })
    }

    const expandBanner = () => {
        showExpand.value = false
        showClose.value = true
        // 存储本地
        setLocal(_def_banner.value, JSON.stringify({ showClose: true, showExpand: false }))
        setIntBanner({ showClose: true, showExpand: false })
    }

    // 设置网络缓存
    const setIntBanner = async (params: {showClose: boolean, showExpand: boolean}) => {
        try {
            await creatServerCachApi({
                isComplete: 1,
                step: 1,
                type: _def_banner.value,
                userConfig: JSON.stringify(params) ?? JSON.stringify({})
            })

        } catch (error) {
            console.error(error)
        }
    }

    // 获取网络的缓存
    const getIntBanner = async () => {
        try {
            const res = await getServerCachApi(_def_banner.value)
            if (res?.data && Array.isArray(res?.data) && res?.data.length > 0) {
                const { showClose, showExpand } = JSON.parse(res?.data?.[0].userConfig)
                return {
                    close: showClose,
                    expand: showExpand
                }
            }
        } catch (error) {
            console.log(error)
        }
    }

    /**
     * @desc 初始化
     */
    const init = async () => {
        // 如果本地存在就走本地
        if (getLocal(_def_banner.value)) {
            const res = JSON.parse(getLocal(_def_banner.value) as unknown as any)
            showClose.value = res.showClose
            showExpand.value = res.showExpand
            return
        }
        try {
            const { close, expand } = await getIntBanner() || { close: true, expand: false };
            showClose.value = close
            showExpand.value = expand
            setLocal(_def_banner.value, JSON.stringify({ showClose: close, showExpand: expand }))
        } catch (error) {
            console.error(error)
        }
    }

    init()

    return {
        showClose,
        showExpand,
        closeBanner,
        expandBanner
    }

}



export {
    useBanner
}