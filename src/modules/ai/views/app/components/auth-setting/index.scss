.ai-agent-app-auth-setting {
  background-color: #fff;
  border-radius: 8px;
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;

  &-sub-title {
    background: #fff;
    height: 38px;
    display: flex;
    align-items: center;
    padding: 8px 0px;
    color: #595959;
    font-size: 14px;
    line-height: 22px;
  }

  &-header-operation {
    margin-top: 16px;
    display: flex;
    flex-flow: column;
  }

  &-search {
    display: flex;
    gap: 12px;
    align-items: center;
    margin-bottom: 12px;

    .el-input {
      width: 260px;
    }
  }

  &-table {
    flex: 1;
    .el-table {
      .el-button {
        padding: 0;

        &.danger {
          color: #F56C6C;
        }
      }
    }
  }
} 

.ai-agent-app-auth-setting-title {
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  color: #262626;
}

.ai-agent-app-auth-setting-table {
  .el-table th.el-table__cell {
    background: #F4F4F5;
  }
}

.pad-none{
  padding: 0 !important;
}

.ai-agent-app-auth-setting-header-operation {
  margin-bottom: 16px;
}

.ai-agent-app-auth-setting-header-operation-title {
  font-size: 14px;
  line-height: 24px;
  color: #262626;
  margin-bottom: 8px;
}

.ai-edit-public-access-tip {
  font-size: 12px;
  line-height: 18px;
  color: #8C8C8C;
}

.ai-agent-app-auth-setting-content {
  margin-bottom: 12px;
}