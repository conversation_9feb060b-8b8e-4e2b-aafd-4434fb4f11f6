/* api */
import { getAIAgentApp<PERSON><PERSON><PERSON>ist, removeAIAgentAppAuth } from "@src/api/AIv2API"
/* components */
import NoDataViewNew from '@src/component/common/NoDataViewNew'
/* enum */
import ComponentNameEnum from "@model/enum/ComponentNameEnum"
/* vue */
import { defineComponent, PropType } from "vue"
/* types */
import { AIAgentAppAuthAddParamType, AIAgentAppAuthItem, AIAgentAppAuthRemoveParamType, AIAgentAppType } from "@src/modules/ai/types"
import MsgModel from "@model/MsgModel"
/* scss */
import "@src/modules/ai/views/app/components/auth-setting/index.scss"
/* model */
import { AIAgentAppAuthListParamType } from "@src/modules/ai/model/param"
import 
  BaseSelectUser, 
  { 
    BaseSelectUserModeEnum, 
    BaseSelectUserMultiAllOptionsType, 
    BaseSelectUserResult,
    BaseSelectUserSelectedItemType, 
    BaseSelectUserTypeIdEnum, 
    SelectUserComponentUser 
  } 
  from "@src/component/common/BaseSelectUser"
import { 
  AIAgentAppAuthRangeTypeCNNameEnum, 
  AIAgentAppAuthRangeTypeEnum, 
  AIAgentAppAuthTypeCNNameEnum, 
  AIAgentAppAuthTypeEnum 
} from "@src/modules/ai/model/enum"
import { cloneDeep, isEmpty, isFalsy } from "pub-bbx-utils"

enum AiAgentAppAuthSettingEventEnum {
  AuthTypeChange = 'authTypeChange',
  Update = 'update',
  PublicAccessChange = 'publicAccessChange'
}

export default defineComponent({
  name: ComponentNameEnum.AiAgentAppAuthSetting,
  props: {
    agentAppDetail: {
      type: Object as PropType<AIAgentAppType>,
      required: true
    },
    onUpdate: {
      type: Function,
    },
    mode:{
      type:String,
      default:'',
    },
    onPublicAccessChange: {
      type: Function,
    },
  },
  data() {
    return {
      loading: false,
      showCreateDialog: false,
      keyword: '',
      authList: [] as AIAgentAppAuthItem[],
      backupAuthList: [] as AIAgentAppAuthItem[],
      allAuthList: [] as AIAgentAppAuthItem[],
    }
  },
  computed: {
    agentAppId(): number {
      return this.agentAppDetail.id as number
    },
    attrs(): Record<string, any> {
      return {
        directives: [
          {
            name: 'loading',
            value: this.loading
          }
        ],
      }
    },
    isAllRange(): boolean {
      return Number(this.agentAppDetail.allRange) === Number(AIAgentAppAuthTypeEnum.ALL)
    },
    isPartRange(): boolean {
      return Number(this.agentAppDetail.allRange) === Number(AIAgentAppAuthTypeEnum.PART)
    },
    isHiddenRange(): boolean {
      return Number(this.agentAppDetail.allRange) === Number(AIAgentAppAuthTypeEnum.HIDDEN)
    },
    authType(): AIAgentAppAuthTypeEnum {
      const allRange = this.agentAppDetail?.allRange
      if (JSON.stringify(allRange) === JSON.stringify(true)) {
        return AIAgentAppAuthTypeEnum.ALL
      }
      return String(this.agentAppDetail.allRange) as unknown as AIAgentAppAuthTypeEnum
    },
    /**
     * 公开访问状态
     * @returns 是否开启公开访问
     */
    publicAccess() {
      return Boolean(this.agentAppDetail.publicAccess)
    },
    /**
     * 公开访问开关文本
     * @returns 公开访问开关文本
     */
    publicAccessSwitchText() {
      return '对外公开访问'
    },
    /**
     * 公开访问开关提示文本
     * @returns 公开访问开关提示文本
     */
    publicAccessSwitchTextPlaceholder() {
      return this.publicAccess ? '互联网上获得链接的用户可使用该Agent' : '仅内部被授权的员工可访问使用该Agent'
    }
  },
  mounted() {
    this.initialize()
  },
  methods: {
    async initialize() {
      await this.getAuthList()
    },
    onUpdateAgentAppDetail(value: AIAgentAppType) {
      this.$emit(AiAgentAppAuthSettingEventEnum.Update, value)
    },
    async getAuthList() {
      try {

        this.loading = true

        const params: AIAgentAppAuthListParamType = {
          id: this.agentAppId,
        }

        const res = await getAIAgentAppAuthList(params)
        
        const isFail = MsgModel.isFail(res)
        if (isFail) {
          const message = MsgModel.getMessage(res)
          this.$message.error(message)
          return
        }

        this.authList = MsgModel.getData<AIAgentAppAuthItem[]>(res, [])
        this.backupAuthList = cloneDeep(this.authList)
        this.allAuthList = cloneDeep(this.authList)
        this.handleAuthListChange()

      } catch (error) {
        console.error(error)
      } finally {
        this.loading = false
      }
    },
    async getLocalAuthList() {

      if (isEmpty(this.keyword)) {
        this.authList = this.backupAuthList
        return
      }
      
      const keyword = this.keyword
      const authList = this.authList
      const filteredAuthList = authList.filter(item => item.rangeName.includes(keyword))

      this.authList = filteredAuthList
    },
    async handleRemoveAuthHandler(rangeId: string) {
      
      const confirmed = await this.$confirm('确定要移除该授权对象吗？')
      if (isFalsy(confirmed)) {
        return
      }

      // 移除授权对象
      const authItemIndex = this.authList.findIndex(item => item.rangeId === rangeId)
      if (authItemIndex === -1) {
        return
      }
      
      // 移除授权对象
      this.authList.splice(authItemIndex, 1)

      // 更新应用授权范围
      this.handleAuthListChange()

    },
    handleAuthListChange() {
      
      const authList = this.isAllRange ? [] : this.authList
      
      const agentAppDetail = {
        ...this.agentAppDetail,
        ranges: authList
      }
      
      this.onUpdateAgentAppDetail(agentAppDetail)
    },
    /**
     * @deprecated
     * @description 移除授权对象
     */
    async handleRemoveAuth(id: string) {
      try {

        this.loading = true

        const params: AIAgentAppAuthRemoveParamType = {
          agentAppId: this.agentAppId,
          id
        }

        const res = await removeAIAgentAppAuth(params)
        
        const isFail = MsgModel.isFail(res)
        if (isFail) {
          const message = MsgModel.getMessage(res)
          this.$message.error(message)
          return
        }

        this.$message.success('移除成功')
        await this.getAuthList()

      } catch (error) {
        console.error(error)
      } finally {
        this.loading = false
      }
    },
    async handleShowCreateDialog() {

      const typeIdMap = {
        [AIAgentAppAuthRangeTypeEnum.DEPARTMENT]: BaseSelectUserTypeIdEnum.Department,
        [AIAgentAppAuthRangeTypeEnum.ROLE]: BaseSelectUserTypeIdEnum.Role,
        [AIAgentAppAuthRangeTypeEnum.USER]: BaseSelectUserTypeIdEnum.User,
        [AIAgentAppAuthRangeTypeEnum.SERVICE_PROVIDER]: BaseSelectUserTypeIdEnum.ServiceProvider
      }

      const selected = this.allAuthList.map(item => {
        
        const rangeType = item.rangeType
        const typeId = typeIdMap[rangeType]
        const isUser = rangeType === AIAgentAppAuthRangeTypeEnum.USER
        
        let selectItem: BaseSelectUserSelectedItemType = {
          id: item.rangeId,
          name: item.rangeName,
          typeId: typeId,
        } as unknown as BaseSelectUserSelectedItemType

        if (isUser) {
          (selectItem as SelectUserComponentUser).displayName = item.rangeName;
          (selectItem as SelectUserComponentUser).userId = item.rangeId;
        }
        
        return selectItem

      })

      const options: BaseSelectUserMultiAllOptionsType = {
        title: '添加授权对象',
        max: 100,
        selectedAll: selected,
        mode: BaseSelectUserModeEnum.Filter
      }

      const result = await BaseSelectUser.props.multi.all(options as BaseSelectUserMultiAllOptionsType)
      const data = result?.data || {} as BaseSelectUserResult
      const depts = data.depts || []
      const roles = data.roles || []
      const users = data.users || []
      const serviceProviders = data.serviceProviders || []

      const deptAuthList = depts.map(dept => {
        return {
          rangeId: dept.id,
          rangeName: dept.name,
          rangeType: AIAgentAppAuthRangeTypeEnum.DEPARTMENT
        }
      })

      const roleAuthList = roles.map(role => {
        return {
          rangeId: role.id,
          rangeName: role.name,
          rangeType: AIAgentAppAuthRangeTypeEnum.ROLE
        }
      })

      const userAuthList = users.map(user => {
        return {
          rangeId: user.userId,
          rangeName: user.displayName,
          rangeType: AIAgentAppAuthRangeTypeEnum.USER
        }
      })

      const serviceProviderAuthList = serviceProviders.map(serviceProvider => {
        return {
          rangeId: serviceProvider.id,
          rangeName: serviceProvider.name,
          rangeType: AIAgentAppAuthRangeTypeEnum.SERVICE_PROVIDER
        }
      })
      
      const authList = [
        ...deptAuthList,
        ...roleAuthList,
        ...userAuthList,
        ...serviceProviderAuthList
      ]
      
      this.authList = authList
      this.allAuthList = authList

      this.handleAuthListChange()

    },
    handleKeywordChange(value: string) {
      console.log('hbc ~ handleKeywordChange ~ value:', value)
      this.keyword = value
      this.getLocalAuthList()
    },
    handleAuthTypeChange(value: AIAgentAppAuthTypeEnum) {
      const agentAppDetail = {
        ...this.agentAppDetail,
        allRange: Number(value)
      }
      this.onUpdateAgentAppDetail(agentAppDetail)
    },
    handleAuthTypeChangeWrapper(value: AIAgentAppAuthTypeEnum) {

      // 授权范围是否为不可见
      const isAllRangeHidden = String(value) === String(AIAgentAppAuthTypeEnum.HIDDEN)
      // 外部分享
      const isPublicAccess = this.agentAppDetail.publicAccess

      // 权限范围为内部不可见、又选择外部不开放，出现此情况时，提示用户“请合理配置Agent权限”；
      if (isAllRangeHidden && !isPublicAccess) {
        this.$message.error('请合理配置 Agent 权限')
        return
      }
      
      this.handleAuthTypeChange(value)

      this.$nextTick(() => {
        this.handleAuthListChange()
      })
      
    },
    closeCreateDialog() {
      this.showCreateDialog = false
    },
    openCreateDialog() {
      this.showCreateDialog = true
    },
    handlePublicAccessChange(value: boolean) {
      this.$emit(AiAgentAppAuthSettingEventEnum.PublicAccessChange, value)
    },
    renderHeader() {
      return (
        <div class="ai-agent-app-auth-setting-header">
          {this.renderHeaderOperation()}
        </div>
      )
    },
    renderHeaderOperation() {
      return (
        <div class="ai-agent-app-auth-setting-header-operation">
          <div class="ai-agent-app-auth-setting-header-operation-title">
            内部可见范围
          </div>
          <div class="ai-agent-app-auth-setting-radio-group">
            <el-radio-group 
              value={this.authType}
              onInput={this.handleAuthTypeChangeWrapper}
            >
              <el-radio
                label={AIAgentAppAuthTypeEnum.ALL}
              >
                {AIAgentAppAuthTypeCNNameEnum[AIAgentAppAuthTypeEnum.ALL]}
              </el-radio>
              <el-radio 
                label={AIAgentAppAuthTypeEnum.PART}
              >
                {AIAgentAppAuthTypeCNNameEnum[AIAgentAppAuthTypeEnum.PART]}
              </el-radio>
              <el-radio 
                label={AIAgentAppAuthTypeEnum.HIDDEN}
              >
                {AIAgentAppAuthTypeCNNameEnum[AIAgentAppAuthTypeEnum.HIDDEN]}
              </el-radio>
            </el-radio-group>
          </div>
        </div>
      )
    },
    renderOutsiderAuthSetting() {
      return (
        <div class="ai-agent-app-auth-setting-outsider-auth-setting">
          <div class="flex-x ai-edit-public-access-header">
            <span class="ai-edit-public-access-title">
              {this.publicAccessSwitchText}
            </span>
            <el-switch 
              class="ai-edit-public-access-switch"
              value={this.publicAccess} 
              onInput={this.handlePublicAccessChange}
            >
            </el-switch>
          </div>
          <div class="second-text mar-b-8 ai-edit-public-access-tip">
            {this.publicAccessSwitchTextPlaceholder}
          </div>
        </div>
      )
    },
    renderAddAuthOperation() {
      return (
        <div class="ai-agent-app-auth-setting-search">
          <el-button 
            type="primary"
            onClick={this.handleShowCreateDialog}
          >
            添加授权对象
          </el-button>
        </div>
      )
    },
    renderTable() {
      return (
        <div class="ai-agent-app-auth-setting-table">
          <el-table
            data={this.authList}
            stripe
            border
            {...this.attrs}
          >

            <el-table-column
              label="授权对象"
              scopedSlots={{
                default: this.renderNameColumn
              }}
            />

            <el-table-column
              label="操作"
              width="120"
              scopedSlots={{
                default: this.renderOperationColumn
              }}
            />

            <div slot="empty">
              {this.renderEmpty()}
            </div>

          </el-table>
        </div>
      )
    },
    renderNameColumn({ row }: { row: AIAgentAppAuthItem }) {

      const rangeTypeCNName = AIAgentAppAuthRangeTypeCNNameEnum[row.rangeType] || ''

      return (
        <div>
          <span>{row.rangeName}</span>
          <span>
            &nbsp;
          </span>
          <span>
            ({rangeTypeCNName})
          </span>
        </div>
      )
    },
    renderOperationColumn({ row }: { row: AIAgentAppAuthItem }) {

      const onClickHandler = () => {
        this.handleRemoveAuthHandler(row.rangeId)
      }

      return (
        <el-button
          type="text"
          class="danger"
          onClick={onClickHandler}
        >
          移除
        </el-button>
      )
    },
    renderEmpty() {

      if (this.loading) {
        return null
      }

      return (
        <NoDataViewNew />
      )

    },
    renderContentWrapper() {
      if (isFalsy(this.isPartRange)) {
        return
      }
      return this.renderContent()
    },
    renderContent() {
      return (
        <div class="ai-agent-app-auth-setting-content">
          {this.renderAddAuthOperation()}
          {this.renderTable()}
        </div>
      )
    }
  },
  render() {
    return (
      <div class={['ai-agent-app-auth-setting', this.mode === 'v2' ? 'pad-none' : '']}>
        {this.renderHeader()}
        {this.renderContentWrapper()}
        {this.renderOutsiderAuthSetting()}
      </div>
    )
  }
}) 
