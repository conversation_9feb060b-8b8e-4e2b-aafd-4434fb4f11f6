/* api */
import { 
  getAIAgentAppAbilityList, 
  addAIAgentAppAbility, 
  removeAIAgentAppAbility, 
  getAIAgentAppAbilityBizAppList, 
  aiAgentAppAbilityRelearn
} from "@src/api/AIv2API"
/* components */
import ComponentNameEnum from "@model/enum/ComponentNameEnum"
import CreateDialog from "@src/modules/ai/views/app/components/ability-setting/create-dialog"
import NoDataViewNew from '@src/component/common/NoDataViewNew'
import WikiTreeDialog from "@src/modules/ai/components/wiki-tree-dialog"
/* vue */
import { defineComponent, PropType } from "vue"
/* types */
import { AIAgentAppAbilityBizAppItem, AIAgentAppAbilityItem, AIAgentAppType } from "@src/modules/ai/types"
import MsgModel from "@model/MsgModel"
/* scss */
import "@src/modules/ai/views/app/components/ability-setting/index.scss"
/* model */
import { 
  AIAgentAppAbilityAddParamType, 
  AIAgentAppAbilityListParamType, 
  AIAgentAppAbilityRelearnParamType, 
  AIAgentAppAbilityRemoveParamType 
} from "@src/modules/ai/model/param"
/* enum */
import { 
  AIAgentAppAbilityTypeEnum,
  AIAgentAppAbilityLearningStatusEnum, 
  AiKnowledgeTypeEnum
} from "@src/modules/ai/model/enum"
/* util */
import { formatDate, FormatTemplate, isNotEmpty } from "pub-bbx-utils"
import { confirm, message } from '@src/util/message'
import http from "@src/util/http"
import { TreeItem } from "@src/modules/ai/components/wiki-tree-dialog/tree"
import {  getBigFileCheckCanDownLoad } from 'pub-bbx-utils';

enum AiAgentAppAbilitySettingEventEnum {
  UPDATE_ABILITY_LIST = 'updateAbilityList',
  UPDATE_AUTO_LEARN = 'updateAutoLearn'
}

type WikiTreeDialogComponent = InstanceType<typeof WikiTreeDialog>

export default defineComponent({
  name: ComponentNameEnum.AiAgentAppAbilitySetting,
  components: {
    CreateDialog
  },
  props: {
    agentAppDetail: {
      type: Object as PropType<AIAgentAppType>,
      required: true
    },
    onUpdateAbilityList: {
      type: Function
    },
    mode:{
      type:String,
    },
    type:{
      type:String,
    },
    onUpdateAutoLearn: {
      type: Function
    }
  },
  data() {
    return {
      loading: false,
      showCreateDialog: false,
      keyword: '',
      abilityList: [] as AIAgentAppAbilityItem[],
      bizAppList: [] as AIAgentAppAbilityBizAppItem[],
      // 每个文件最大大小 200MB
      fileMaxMb: 200,
      isAutoLearn: false,
      // 每个文件最大大小 15MB
      // fileMaxMb: 15,
      previewFile:{},
    }
  },
  computed: {
    agentAppId(): number {
      return this.agentAppDetail.id as number
    },
    attrs(): Record<string, any> {
      return {
        directives: [
          {
            name: 'loading',
            value: this.loading
          }
        ],
      }
    },
    fileInputElement(): Element {
      return this.$refs.FileInput as Element
    },
    // "PDF", "DOC", "DOCX", "XLSX", "PPT", "TXT", "PPTX"
    fileAccept(): string {
      return "application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-powerpoint,text/plain,application/vnd.openxmlformats-officedocument.presentationml.presentation"
    },
    wikiTreeDialog(): WikiTreeDialogComponent {
      return this.$refs.WikiTreeDialog as WikiTreeDialogComponent
    },
    fileMaxSize(): number {
      return this.fileMaxMb * 1024 * 1024
    }
  },
  watch: {
    agentAppDetail: {
      handler(value) {
        if (isNotEmpty(value)) {
          this.initIsAutoLearn()
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.initialize()
  },
  methods: {
    async initialize() {
      await this.getAbilityList()
      await this.getBizAppList()
      this.initIsAutoLearn()
    },
    initIsAutoLearn() {
      this.isAutoLearn = this.agentAppDetail?.isAutoLearn as boolean
    },
    async getAbilityList() {
      try {

        this.loading = true

        const params: AIAgentAppAbilityListParamType = {
          agentAppId: this.agentAppId,
          keyword: this.keyword
        }
        const res = await getAIAgentAppAbilityList(params)
        
        const isFail = MsgModel.isFail(res)
        if (isFail) {
          const message = MsgModel.getMessage(res)
          this.$message.error(message)
          return
        }

        let abilityList = MsgModel.getData<AIAgentAppAbilityItem[]>(res, []) as AIAgentAppAbilityItem[]
        if(this.mode === 'v2'){
          if(this.type === 'business'){
            abilityList = abilityList.filter(i=>i.type == 1);
          }else if(this.type === 'knowledge'){
            abilityList = abilityList.filter(i=>(i.type == 2 || i.type == 3));
          }
        }
        this.abilityList = abilityList.map(item => {
          let name = item?.name || ''
          // if (item.bizType === AiKnowledgeTypeEnum.LOCAL_UPLOAD) {
          //   // "1740302950185_Typescript介绍 、Typescript安装、Typescript开发工具.pdf"
          //   // 去除前面的数字和下划线
          //   name = name.split('_')[1]
          // }
          let fieldName = ''
          if (item.bizType === AiKnowledgeTypeEnum.LOCAL_UPLOAD) {
            // "1740302950185_Typescript介绍 、Typescript安装、Typescript开发工具.pdf"
            // 去除前面的数字和下划线
            fieldName = name
            // name = name.split('.')[0]
          }
          return {
            ...item,
            name,
            fieldName
          }
        })
        
        this.$emit(AiAgentAppAbilitySettingEventEnum.UPDATE_ABILITY_LIST, this.abilityList)

        return res;

      } catch (error) {
        console.error(error)
      } finally {
        this.loading = false
      }
    },
    async getBizAppList() {
      try {

        const res = await getAIAgentAppAbilityBizAppList()

        const isFail = MsgModel.isFail(res)
        if (isFail) {
          const message = MsgModel.getMessage(res)
          this.$message.error(message)
          return
        }

        this.bizAppList = MsgModel.getData<AIAgentAppAbilityBizAppItem[]>(res, [])

      } catch (error) {
        console.error(error)
      }
    },
    onCheckboxIsAutoLearnInputHandler(value: boolean) {
      this.isAutoLearn = value
      this.$emit(AiAgentAppAbilitySettingEventEnum.UPDATE_AUTO_LEARN, value)
    },
    onWikiTreeDialogConfirm(treeItems: TreeItem[]) {
      const bizAppList = treeItems.map(item => {
        return {
          name: item.name,
          bizId: item.id,
          bizType: AiKnowledgeTypeEnum.WIKI,
          type: AIAgentAppAbilityTypeEnum.KNOWLEDGE_BASE
        }
      })
      this.handleAddWiki(bizAppList)
    },
    async handleAddWiki(bizAppList: AIAgentAppAbilityBizAppItem[]) {
      try {

        this.loading = true

        const bizDataList = bizAppList.map(item => {
          return {
            type: AIAgentAppAbilityTypeEnum.KNOWLEDGE_BASE,
            name: item.name,
            bizType: item.bizType,
            bizId: item.bizId as string
          }
        })
        const params: AIAgentAppAbilityAddParamType = {
          agentAppId: this.agentAppId,
          bizDataList
        }
        const res = await addAIAgentAppAbility(params)
        
        const isFail = MsgModel.isFail(res)
        if (isFail) {
          const message = MsgModel.getMessage(res)
          this.$message.error(message)
          return
        }

        this.$message.success('添加成功')

        this.getAbilityList()
        this.closeWikiTreeDialog()

      } catch (error) {
        console.error(error)
      } finally {
        this.loading = false
      }
    },
    async handleAddAbility(bizAppList: AIAgentAppAbilityBizAppItem[]) {
      try {

        this.loading = true

        const bizDataList = bizAppList.map(item => {
          return {
            type: AIAgentAppAbilityTypeEnum.APPLICATION,
            name: item.name,
            bizType: item.bizType,
            bizId: item.bizId as string
          }
        })
        const params: AIAgentAppAbilityAddParamType = {
          agentAppId: this.agentAppId,
          bizDataList
        }
        const res = await addAIAgentAppAbility(params)
        
        const isFail = MsgModel.isFail(res)
        if (isFail) {
          const message = MsgModel.getMessage(res)
          this.$message.error(message)
          return
        }

        this.$message.success('添加成功')

        this.getAbilityList()
        this.closeCreateDialog()

      } catch (error) {
        console.error(error)
      } finally {
        this.loading = false
      }
    },
    handleAddShbKnowledge() {
      this.openWikiTreeDialog()
    },
    handleAddLocalFile() {
      // @ts-ignore
      this.fileInputElement.click()
    },
    openWikiTreeDialog() {
      this.wikiTreeDialog.open()
    },
    closeWikiTreeDialog() {
      this.wikiTreeDialog.close()
    },
    onFileInputChange(event: Event) {
      // @ts-ignore
      const files = Array.from(event.target.files) as File[]
      // 检查每个文件的大小
      const oversizedFiles = files.filter(file => file.size > this.fileMaxSize);
      
      if (oversizedFiles.length > 0) {
        const fileNames = oversizedFiles.map(f => f.name).join('\n')
        message.warning(`以下文件超过${this.fileMaxMb}MB限制:\n${fileNames}`)
        // @ts-ignore
        event.target.value = ''
        return;
      }
      
      this.uploadFiles(files)
    },
    async uploadFiles(files: File[]) {
      
      const formData = new FormData();
      
      // 添加 agentAppId
      formData.append('agentAppId', this.agentAppId.toString());
      
      // 添加多个文件
      files.forEach(file => {
        formData.append('files', file);
      });

      try {
        
        const response = await http.post(
          '/api/voice/outside/xiaobao/agent/app/knowledge/application/addByUpload', 
          formData, 
          false,
          {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          }
        )
        
        const isFail = MsgModel.isFail(response)
        if (isFail) {
          const message = MsgModel.getMessage(response)
          this.$message.error(message)
          this.getAbilityList()
          return
        }

        this.$message.success('上传成功')
        this.getAbilityList()

      } catch (error) {
        console.error(error)
      }
    },
    async handleRemoveAbilityWrapper(id: number) {
      const confirmed = await confirm.warning('确定要移除吗？')
      if (confirmed) {
        await this.handleRemoveAbility(id)
      }
    },
    async handleReLearnWikiWrapper(row: AIAgentAppAbilityItem) {
      const name = row.name
      const confirmed = await confirm.warning(`是否立刻重新学习{${name}}`, '重新学习')
      if (confirmed) {
        await this.handleReLearnWiki(row)
      }
    },
    async handleReLearnWiki(row: AIAgentAppAbilityItem) {

      try {

        this.loading = true

        const params: AIAgentAppAbilityRelearnParamType = {
          knowledgeId: row.id
        }
        const res = await aiAgentAppAbilityRelearn(params)
        
        const isFail = MsgModel.isFail(res)
        if (isFail) {
          const message = MsgModel.getMessage(res)
          this.$message.error(message)
          return
        }

        this.$message.success('重新学习成功')
        
        this.getAbilityList()

      } catch (error) {
        console.error(error)
      } finally {
        this.loading = false
      }
    }, 
    async handlePreview(row){
      if(row.type !==3) return
      if(!row?.url) return this.$message.warning('文件地址不存在');
     this.handlePreviewFile(row)
    },
    async handleRemoveAbility(id: number) {
      try {
        
        this.loading = true

        const params: AIAgentAppAbilityRemoveParamType = {
          agentAppId: this.agentAppId,
          knowledgeId: id
        }
        const res = await removeAIAgentAppAbility(params)
        
        const isFail = MsgModel.isFail(res)
        if (isFail) {
          const message = MsgModel.getMessage(res)
          this.$message.error(message)
          return
        }

        this.$message.success('移除成功')

        this.getAbilityList()

      } catch (error) {
        console.error(error)
      } finally {
        this.loading = false
      }
    },
    handleShowCreateDialog() {
      this.openCreateDialog()
    },
    handleKeywordChange(value: string) {
      this.keyword = value
      this.getAbilityList()
    },
    closeCreateDialog() {
      this.showCreateDialog = false
    },
    openCreateDialog() {
      this.showCreateDialog = true
    },
    icon(file){
      let icon = '';
      const name = file.url;
      
      if (/\.(png|bmp|gif|jpg|jpeg|tiff|image|webp)$/i.test(name)) {
        icon = 'img';
      } else if (/\.(ppt|pptx)$/i.test(name)) {
        icon = 'ppt-file-icon';
      } else if (/\.(mp3)$/i.test(name)) {
        icon = 'voice-file-icon';
      } else if (/\.(mp4)$/i.test(name)) {
        icon = 'video-file-icon';
      } else if (/\.(zip)$/i.test(name)) {
        icon = 'zip-file-icon';
      } else if (/\.(pdf)$/i.test(name)) {
        icon = 'pdf-file-icon';
      } else if (/\.(xls|xlsx)$/i.test(name)) {
        icon = 'xls-file-icon';
      } else if (/\.(doc|docx)$/i.test(name)) {
        icon = 'doc-file-icon';
      } else if (/\.(txt)$/i.test(name)) {
        icon = 'txt-file-icon';
      } else if (/\.(csv)$/i.test(name)) {
        icon = 'csv-file-icon';
      } else {
        icon = 'other-file-icon';
      }
     
      return icon;
    },
    handlePreviewFile(file) {
      if(file?.isBigFile && file?.url) {
        return getBigFileCheckCanDownLoad({ fileId: file.fileId }, true).then(res=> {
          if(!res) {
            this.downloadBigFile(file);
          } else {
            this.$message.warning(res);
          }
        }).catch(err=> {
            this.$message.warning(err?.message || '');
        });
      }
      const { name,fieldName, fileId, url } = file
      const downloadUrl = this.genDownloadUrl(file)
      this.previewFile = {
        id:fileId,
        url,
        fileName: fieldName,
        name: name,
        downloadUrl,
        fileType: '',
      }
      const icon = this.icon(file)
      switch (icon) {
        case 'img':
        case 'small-img':
          this.previewFile.fileType = 'image'
          break;
        case 'pdf-file-icon':
        case 'small-pdf-file-icon':
          this.previewFile.fileType = 'pdf'
          break;
        case 'voice-file-icon':
        case 'small-voice-file-icon':
          this.previewFile.fileType = 'audio'
          break;
        case 'video-file-icon':
        case 'small-video-file-icon':
          this.previewFile.fileType = 'video'
          break;
        case 'doc-file-icon':
        case 'small-doc-file-icon':
          this.previewFile.fileType = 'word'
          break;
        case 'xls-file-icon':
        case 'small-xls-file-icon':
          this.previewFile.fileType = 'excel'
          break;
        case 'txt-file-icon':
        case 'small-txt-file-icon':
          this.previewFile.fileType = 'txt'
          break;
        case 'csv-file-icon':
        case 'small-csv-file-icon':
          this.previewFile.fileType = 'csv'
          break;
        case 'ppt-file-icon':
        case 'small-ppt-file-icon':
          this.previewFile.fileType = 'ppt'
          break;
        default:
          this.previewFile.fileType = ''
          break;
      }
      switch (this.previewFile.fileType) {
        //视频查看
        case "video": {
          this.$previewVideo(this.previewFile.url, this.previewFile);
          break;
        }

        //图片查看
        case "image": {
          this.$previewElementImg(this.previewFile.url, [], this.previewFile);
          break;
        }

        // PDF查看
        case "pdf": {
          this.$previewPDF(this.previewFile.url, this.previewFile);
          break;
        }

        // csv,txt, word, excel查看
        case "csv":
        case "txt":
        case "word":
        case "excel":
        case "ppt":
          console.log(fieldName,111)
          const suffix = fieldName.substring(fieldName.lastIndexOf(".")+1)
          this.$previewFile({...this.previewFile, suffix});
          break;
      }
    },
    downloadBigFile(file) {
      // 创建a标签
      const link = document.createElement('a');
      // files/auth/bigFile/get? 传入对应的文件id
      link.href = file.url;
      link.setAttribute('rel', 'noopener noreferrer');
      link.download = file.fieldName;
      
      // 添加到DOM
      document.body.appendChild(link);
      
      // 触发点击
      link.click();
      
      // 清理：移除元素和释放 blob URL
      document.body.removeChild(link);
      
      // 延迟释放 blob URL，确保下载已开始
      setTimeout(() => {
        window.URL.revokeObjectURL(link.href);
      }, 100);
    },
    genDownloadUrl(file){
      const {fileId} = file
      return `/files/download?fileId=${fileId}`;
    },
    renderHeader() {
      return (
        <div class="ai-agent-app-ability-setting-header">
          {this.mode != 'v2' ? <div class="ai-agent-app-ability-setting-title">
            AI 智能体会学习业务数据及知识内容，用于 AI 结果输出。典型场景：学习知识库内容进行智能问答。
          </div> : ''}
          {this.renderHeaderOperation()}
        </div>
      )
    },
    renderHeaderOperation() {
      return (
        <div class={["ai-agent-app-ability-setting-header-operation", this.mode === 'v2' ? 'mar-t-0-i' : '']}>

          <div class="ai-agent-app-ability-setting-header-operation-buttons">
            {this.mode != 'v2' ? <el-button 
              type="primary"
              onClick={this.handleShowCreateDialog}
            >
              添加业务数据
            </el-button> : this.mode === 'v2' && this.type === 'business' ?  <el-button 
              type="primary"
              onClick={this.handleShowCreateDialog}
            >
              添加业务数据
            </el-button> : ''}
            {this.renderAddKnowledgeButton()}
          </div>

          {this.mode != 'v2' ? <el-input
            value={this.keyword}
            placeholder="请输入关键字"
            onInput={this.handleKeywordChange}
          /> : ''}

        </div>
      )
    },
    renderAddKnowledgeButton() {      
      if(this.mode === 'v2'){
        if(this.type === 'business'){
          return null
        }
        return (
          <div class="ai-agent-app-ability-setting-add-knowledge-button">
            <div class="ai-agent-app-ability-setting-add-knowledge-button-left">
              <el-button 
                type="primary" 
                onClick={this.handleAddShbKnowledge}
              >
                添加知识库
              </el-button>
              <el-button 
                type="plain-third" 
                onClick={this.handleAddLocalFile}
              >
                添加本地文件
              </el-button>
            </div>
            <div class="ai-agent-app-ability-setting-add-knowledge-button-right">
              <el-checkbox
                value={this.isAutoLearn}
                onInput={this.onCheckboxIsAutoLearnInputHandler}
              >
                知识库发生变更自动学习
              </el-checkbox>
            </div>
          </div>
        )
      }

      return (
        <el-dropdown>
          <el-button>
            添加知识
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <div onClick={this.handleAddShbKnowledge}>
                添加知识库
              </div>
            </el-dropdown-item>
            <el-dropdown-item>
              <div onClick={this.handleAddLocalFile}>
                添加本地文件
              </div>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      )
    },
    renderTable() {
      return (
        <div class="ai-agent-app-ability-setting-table">
          <el-table
            data={this.abilityList}
            stripe
            border
            {...this.attrs}
          >
            <el-table-column
              label="应用/知识库"
              scopedSlots={{
                default: this.renderNameColumn
              }}
            />
            <el-table-column
              label="类型"
              scopedSlots={{
                default: this.renderTypeColumn
              }}
            />
            <el-table-column
              label="学习状态"
              scopedSlots={{
                default: this.renderStatusColumn
              }}
            />
            <el-table-column
              label="添加人"
              scopedSlots={{
                default: this.renderCreatorColumn
              }}
            />
            <el-table-column
              label="添加时间"
              scopedSlots={{
                default: this.renderCreateTimeColumn
              }}
            />
            <el-table-column
              label="操作"
              width="160"
              scopedSlots={{
                default: this.renderOperationColumn
              }}
            />

            <div slot="empty">
              {this.renderEmpty()}
            </div>

          </el-table>
        </div>
      )
    },
    renderNameColumn({ row }: { row: AIAgentAppAbilityItem }) {
      return (
        <span>
          {row.name}
        </span>
      )
    },
    renderTypeColumn({ row }: { row: AIAgentAppAbilityItem }) {
      const typeMap = {
        [AIAgentAppAbilityTypeEnum.APPLICATION]: '应用',
        [AIAgentAppAbilityTypeEnum.KNOWLEDGE_BASE]: '知识库',
        [AIAgentAppAbilityTypeEnum.LOCAL_FILE]: '本地文件',
        [AIAgentAppAbilityTypeEnum.EXTERNAL_LINK]: '外部链接'
      }
      return <span>{typeMap[row.type]}</span>
    },
    renderStatusColumn({ row }: { row: AIAgentAppAbilityItem }) {
      const statusMap = {
        [AIAgentAppAbilityLearningStatusEnum.NOT_STARTED]: '未学习',
        [AIAgentAppAbilityLearningStatusEnum.IN_PROGRESS]: '学习中',
        [AIAgentAppAbilityLearningStatusEnum.COMPLETED]: '已学习',
        [AIAgentAppAbilityLearningStatusEnum.FAILED]: '学习失败'
      }
      const statusClassMap = {
        [AIAgentAppAbilityLearningStatusEnum.NOT_STARTED]: 'status-not-started',
        [AIAgentAppAbilityLearningStatusEnum.IN_PROGRESS]: 'status-in-progress',
        [AIAgentAppAbilityLearningStatusEnum.COMPLETED]: 'status-completed',
        [AIAgentAppAbilityLearningStatusEnum.FAILED]: 'status-failed'
      }
      return (
        <span class={`ai-agent-app-ability-setting-status ${statusClassMap[row.learningStatus]}`}>
          {statusMap[row.learningStatus]}
        </span>
      )
    },
    renderCreatorColumn({ row }: { row: AIAgentAppAbilityItem }) {
      const displayName = row.createUser?.displayName || ''
      return <span>{displayName}</span>
    },
    renderCreateTimeColumn({ row }: { row: AIAgentAppAbilityItem }) {
      return (
        <span>
          {formatDate(row.createTime, FormatTemplate.datetime)}
        </span>
      )
    },

    renderOperationColumn({ row }: { row: AIAgentAppAbilityItem }) {

      const isReLearn = Boolean(row?.relearn)
      const isWiki = row?.type === AIAgentAppAbilityTypeEnum.KNOWLEDGE_BASE
      const isShowRelearnButton = Boolean(isWiki && isReLearn)
      const showPreview = row.type == 3 &&  row.learningStatus === 2
      const reLearnButton = (
        <el-button 
          type="text" 
          onClick={() => this.handleReLearnWikiWrapper(row)}
        >
          重新学习
        </el-button>
      )
      const reLearnButtonWrapper = isShowRelearnButton ? reLearnButton : null

      return (
        <div>
          {reLearnButtonWrapper}
          {showPreview && (<el-button
          type="text"
          onClick={() => this.handlePreview(row)}
        >
          预览
        </el-button>
        )}
          <el-button
            type="text"
            onClick={() => this.handleRemoveAbilityWrapper(row.id)}
          >
            移除
          </el-button>
        </div>
      )
    },
    renderCreateDialog() {
      return (
        <CreateDialog
          value={this.showCreateDialog}
          abilityList={this.abilityList as AIAgentAppAbilityItem[]}
          bizAppList={this.bizAppList}
          onConfirm={this.handleAddAbility}
          onClose={this.closeCreateDialog}
        />
      )
    },
    renderEmpty() {

      if (this.loading) {
        return null
      }

      return (
        <NoDataViewNew />
      )

    },
    renderFileInput() {
      return (
        <input 
          class="ai-agent-app-ability-setting-file-input"
          type="file" 
          ref="FileInput" 
          multiple
          accept={this.fileAccept}
          onChange={this.onFileInputChange}
        >
        </input>
      )
    },
    renderWikiTreeDialog() {
      return (
        <WikiTreeDialog
          ref="WikiTreeDialog"
          loading={this.loading}
          onConfirm={this.onWikiTreeDialogConfirm}
        />
      )
    }
  },
  render() {
    return (
      <div class="ai-agent-app-ability-setting">
        {this.renderHeader()}
        {this.renderTable()}
        {this.renderCreateDialog()}
        {this.renderFileInput()}
        {this.renderWikiTreeDialog()}
      </div>
    )
  }
}) 