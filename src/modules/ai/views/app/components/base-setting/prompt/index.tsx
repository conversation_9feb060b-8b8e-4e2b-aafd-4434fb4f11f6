/* components */
import ComponentNameEnum from "@model/enum/ComponentNameEnum"
import FormRichText from "@src/component/form/components/FormRichText/index.vue"
/* vue */
import { defineComponent, PropType } from "vue"
/* types */
import { AIAgentAppAbilityItem, AIAgentAppType } from "@src/modules/ai/types"
/* scss */
import "@src/modules/ai/views/app/components/base-setting/prompt/index.scss"
/* utils */
import { isEmpty, isFalsy, isNotEmpty } from "pub-bbx-utils"

enum AiAgentPromptEventEnum {
  Input = 'input',
  Reset = 'reset'
}

export default defineComponent({
  name: ComponentNameEnum.AiAgentPrompt,
  props: {
    agentAppDetail: {
      type: Object as PropType<AIAgentAppType>,
      required: true
    },
    abilityList: {
      type: Array as PropType<AIAgentAppAbilityItem[]>,
      default: () => []
    },
    onInput: {
      type: Function
    },
    onReset: {
      type: Function
    }
  },
  components: {
    FormRichText
  },
  data() {
    return {
      selectedVariable: '',
      rangeIndex: null as number | null,
      isFirstSelectionIndexChange: true
    }
  },
  computed: {
    prompt(): string {
      return this.agentAppDetail.prompt || ''
    },
    baseEditor(): Record<string, any> {
      return this.$refs.BaseEditor as Record<string, any> || {}
    },
    supportAbilityList(): AIAgentAppAbilityItem[] {
      return this.abilityList.filter(item => {
        const isAdded = this.addedAbilityNameFormatList.includes(item.name)
        return isFalsy(isAdded)
      })
    },
    addedAbilityNameFormatList(): string[] {
      return (
        this.addedAbilityNameList
        .map(name => name.replace('{', '').replace('}', ''))
        .map(name => {
          const regex = /<span[^>]*>(.*?)<\/span>/;
          const match = name.match(regex);
          return match ? match[1] : name
        })
      )
    },
    addedAbilityNameList(): string[] {
      // 获取所有插入的变量，匹配{xxx}，或者 <span style="xxx">xxx</span>
      const curlyBracesRegex = /\{([^{}]*)\}/g;
      // 匹配 <span style="xxx">xxx</span> 格式的变量
      const spanRegex = /<span\s+style=["']([^"']*)["']>([^<]*)<\/span>/g;
      // 合并匹配结果
      const matches = this.prompt.match(curlyBracesRegex) || [];
      const spanMatches = this.prompt.match(spanRegex) || [];
      return [...matches, ...spanMatches].filter((match, index, self) => self.indexOf(match) === index)
    },
    promptHtml(): string {
      return this.renderPromptHtml()
    },
    isPromptEmpty(): boolean {
      return isEmpty(this.prompt) && isNotEmpty(this.agentAppDetail)
    }
  },
  mounted() {
  },
  methods: {
    onResetPrompt() {
      this.$emit(AiAgentPromptEventEnum.Reset)
    },
    onInsertVariable() {

      if (isEmpty(this.selectedVariable)) {
        this.$message.error('请选择插入的字段')
        return
      }
      
      // 插入字段文本
      const variable = `{<span style="color: #13c2c2;">${this.selectedVariable}</span>}`
      // 获取光标位置, 如果光标位置不存在, 则插入到最后
      let rangeIndex = this.rangeIndex || this.prompt.length
      
      // 获取插入后的文本
      let beforeText = this.prompt.slice(0, rangeIndex)
      let afterText = this.prompt.slice(rangeIndex)

      // 如果 beforeText 里面有 标签，比如 <p>, 则需要加上标签的长度
      const beforeTextRegex = /<[^>]*>/
      if (beforeTextRegex.test(beforeText)) {
        beforeText = beforeText.replace(beforeTextRegex, '')
      }
      
      let newText = beforeText + variable + afterText

      const isPromptHaveRootPTag = newText.startsWith('<p>') && this.prompt.endsWith('</p>')
      if (!isPromptHaveRootPTag) {
        newText = newText.replaceAll('<p>', '').replaceAll('</p>', '')
      }

      // 更新数据
      this.onPromptInput(newText)
      // 清空选中变量
      this.selectedVariable = ''
      // 清空光标位置
      this.rangeIndex = null
      
    },
    onInsertVariableNew() {
      
      if (isEmpty(this.selectedVariable)) {
        this.$message.error('请选择插入的字段')
        return
      }

      let span = document.createElement('span')
			span.setAttribute('contenteditable', 'false')
			span.className = 'fill-span-con'
      span.innerHTML = `{${this.selectedVariable}}`

      let emptyTextSpan = document.createElement('span')
      emptyTextSpan.setAttribute('contenteditable', 'false')
      emptyTextSpan.className = 'fill-span-con-empty'
      emptyTextSpan.innerText = ' '

      const htmlFillSetDom = this.$refs.htmlFillSetDom as HTMLElement
			const focus = this.getCursorPosition(htmlFillSetDom)

      if (!focus) {
        this.$message.error('请先点击文本框选择插入位置')
        return
      }

			if (focus) {
				focus.insertNode(span)
        htmlFillSetDom.appendChild(emptyTextSpan)
			} else {
				htmlFillSetDom.appendChild(span)
			}

    },
    // 获取光标位置相关信息
		getCursorPosition(ele: HTMLElement) {
      // @ts-ignore
			const doc = ele.ownerDocument || ele.document
      // @ts-ignore
			const win = doc.defaultView || doc.parentWindow
      // @ts-ignore
			const sel = win.getSelection()
			let range
			if (sel.rangeCount > 0) {
				range = sel.getRangeAt(0) // 获取到当前光标所在的元素区域对象

				// 光标不在富文本框内，则将 range 改为 undefined
				if (!ele.contains(range.startContainer)) {
					range = undefined
				}
			}
      
      return range
    },
    getTagsLengthPrecise(htmlString: string) {

      if (!htmlString) return 0;
      
      let totalLength = 0;
      let inTag = false;
      let currentTagLength = 0;

      let tagsNumber = 0
      
      // 逐字符遍历字符串
      for (let i = 0; i < htmlString.length; i++) {
        const char = htmlString[i];
        
        if (char === '<') {
          inTag = true;
          currentTagLength = 1; // 开始计算当前标签长度，包含 '<' 字符
          tagsNumber++
        } else if (char === '>' && inTag) {
          currentTagLength++; // 加上 '>' 字符
          totalLength += currentTagLength; // 将当前标签长度加到总长度
          inTag = false;
          tagsNumber++
        } else if (inTag) {
          currentTagLength++; // 在标签内部，继续累加长度
        }
      }
      
      return totalLength
    },    
    onPromptInput(value: string) {
      this.$emit(AiAgentPromptEventEnum.Input, value)
    },
    onPromptInputNew(event: Event) {
      const value = (event.target as HTMLElement).innerHTML
      if (!value || value == '<br>') {
        this.onPromptInput('')
        return
      }
    },
    getPromptInputValue() {
      const htmlFillSetDom = this.$refs.htmlFillSetDom as HTMLElement
      const value = htmlFillSetDom.innerHTML
      return value
    },
    mapFillSpan(spanKey?: string) {

    },
    deleteFillSpanByInput() {

    },
    onEditorClick(editor: Record<string, any>) {
      // 获取当前选择对象
      const selection = editor.selection;
      const node = selection.getNode();
      let nodeOutHtml = node.outerHTML.replaceAll(` data-mce-style=\"color: #13c2c2;\"`, '')
      const nodeTextContent = node.textContent;
      // 获取当前光标位置的索引
      let cursorIndex = selection.getRng().startOffset;

      let prompt = this.prompt

      const isPromptHaveRootPTag = this.prompt.startsWith('<p>') && this.prompt.endsWith('</p>')
      if (!isPromptHaveRootPTag) {
        cursorIndex = this.getAbsoluteIndex(editor, selection)
        nodeOutHtml = nodeOutHtml.replaceAll('<p>', '').replaceAll('</p>', '')
        prompt = prompt.replaceAll('<p>', '').replaceAll('</p>', '')
      }

      // 从 this.prompt 中获取 nodeTextContent 位置的索引
      let nodeTextHtmlIndex = prompt.indexOf(nodeOutHtml);

      const cursorIndexBefore = nodeTextContent.slice(0, cursorIndex)
      const newCursorIndex = nodeOutHtml.indexOf(cursorIndexBefore) + cursorIndexBefore.length

      let currentNodeCursorHtmlIndex = nodeTextHtmlIndex + newCursorIndex;

      if (!isPromptHaveRootPTag) {
        if (cursorIndex == nodeTextContent.length) {
          currentNodeCursorHtmlIndex = node.outerHTML.length
        }
      }

      console.log('onEditorClick ~ cursorIndex:', cursorIndex)
      console.log('onEditorClick ~ newCursorIndex:', newCursorIndex)
      console.log('onEditorClick ~ nodeTextIndex:', nodeTextHtmlIndex)
      console.log('onEditorClick ~ currentNodeCursorHtmlIndex:', currentNodeCursorHtmlIndex)

      const realCursorIndex = currentNodeCursorHtmlIndex;

      // 设置光标位置
      this.rangeIndex = realCursorIndex
    },
    // 计算绝对索引位置的函数
    getAbsoluteIndex(editor: Record<string, any>, selection: Record<string, any>) {
      // 获取当前范围
      const rng = selection.getRng();
      
      // 创建一个范围从文档开始到当前光标位置
      const absoluteRange = editor.dom.createRng();
      absoluteRange.setStart(editor.getBody(), 0);
      absoluteRange.setEnd(rng.startContainer, rng.startOffset);
      
      // 获取范围内的文本内容，其长度即为绝对索引
      const content = absoluteRange.toString();
      return content.length;
    },  
    onRangeIndex(value: number) {
      if (this.isFirstSelectionIndexChange) {
        value += 1
      }
      this.rangeIndex = value
      this.isFirstSelectionIndexChange = false
    },
    onPromptBlur() {
      this.rangeIndex = null
    },
    onVariableSelect(value: string) {
      this.selectedVariable = value
    },
    // 后端为了防止 XSS 攻击将 < 转义为 &lt;，导致页面展示的是 HTML 编码而非预期的内容
    safeHtml(questionTitle: string) {
      // 创建一个临时 DOM 元素，用于解码
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = questionTitle;
      return tempDiv.textContent || tempDiv.innerText || '';
    },
    renderTitle() {
      return (
        <div class="ai-agent-app-setting-prompt-title">
          <div class="ai-agent-app-setting-prompt-title-text">
            提示词
          </div>
          <div 
            class="ai-agent-app-setting-prompt-title-reset"
            onClick={this.onResetPrompt}
          >
            重置
          </div>
        </div>
      )
    },
    renderVariableTip() {
      return (
        <div class="ai-agent-app-setting-prompt-variable-tip">
          能力设置内学习的数据源支持作为动态字段插入提示词
        </div>
      )
    },
    renderVariableSelect() {
      return (
        <div class="ai-agent-app-setting-prompt-variable">
          <el-select 
            value={this.selectedVariable}
            onInput={this.onVariableSelect}
            placeholder="请选择"
            class="ai-agent-app-setting-prompt-variable-select"
          >
            {this.supportAbilityList.map((ability) => (
              <el-option label={ability.name} value={ability.name} />
            ))}
          </el-select>
          <el-button 
            type="text"
            size="small"
            onClick={this.onInsertVariableNew}
          >
            {'</>'}
            插入字段
          </el-button>
        </div>
      )
    },
    renderPromptInput() {
      
      const classNames = {
        'ai-agent-app-setting-prompt-input-wrapper': true,
        'ai-agent-app-setting-prompt-input-wrapper-empty': this.isPromptEmpty,
        'flex-1':true,
      }
      
      return (
        <div class={classNames}>

          <div
            ref="htmlFillSetDom"
            class="htmlFillSetDom-box"
            contenteditable="true"
            onInput={this.onPromptInputNew}
            // @ts-ignore
            domPropsInnerHTML={this.prompt}
          >
          </div>
          
          {this.isPromptEmpty && (
            <div class="ai-agent-app-setting-prompt-input-wrapper-empty-text">
              请输入提示词
            </div>
          )}
          
        </div>
      )
    },
    renderPromptHtml() {      
      return this.prompt
    }
  },
  render() {
    return (
      <div class="ai-agent-app-setting-prompt flex-y h-100-p">
        {this.renderTitle()}
      <div class="ai-agent-app-setting-prompt-box flex-y h-100-p">
        {this.renderPromptInput()}
        {this.renderVariableTip()}
        {this.renderVariableSelect()}
      </div>
      </div>
    )
  }
})
