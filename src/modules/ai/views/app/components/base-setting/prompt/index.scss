.ai-agent-app-setting-prompt {
  background-color: #f0f2fd;
  border-radius: 8px;
  padding: 12px;

  &-title {
    margin-bottom: 8px;
  }

  &-variable {
    display: flex;
    align-items: center;
    gap: 12px;

    &-tip {
      font-size: 14px;
      color: #86909c;
      margin-top: 8px;
      margin-bottom: 8px;
    }

    &-select {
      width: 200px;
    }
  }

  &-input {
    .el-textarea__inner {
      padding: 8px 12px;
      font-size: 14px;
      color: #1f2329;
      
      &::placeholder {
        color: #86909c;
      }
    }
  }
} 

.ai-agent-app-setting-prompt-title {
  display: flex;
  justify-content: space-between;
}

.ai-agent-app-setting-prompt-title-text {
  font-size: 16px;
  color: #262626;
  font-weight: 600;
  line-height: 24px;
}
.ai-agent-app-setting-prompt-box{
  border-radius: 8px;
  padding: 6px;
  border-radius: 16px;
  border: 6px solid #F5F8FA;
}
.ai-agent-app-setting-prompt-title-reset {
  color: #8c8c8c;
  cursor: pointer;
}

.ai-agent-app-setting-prompt-input {
  height: 400px;
  .el-textarea__inner {
    height: 400px;
    min-height: 400px;
    max-height: 400px;
  }
}

.ai-agent-app-setting-prompt-input-wrapper {
  overflow-y: auto;
  .ql-toolbar {
    display: none;
  }
  .ql-container {
    border: none;
  }
  .ql-editor {
    border-radius: 4px;
    border: 1px solid #E4E7ED;
    min-height: 400px;
  }
  .tox-statusbar,
  .tox-editor-header {
    display: none !important;
  }
  .tox-tinymce,
  .form-richtext {
    height: calc(100vh - 200px) !important;
    max-height: calc(100vh - 200px);
  }
}

.ai-agent-app-setting-prompt-input-wrapper-empty {
  .base-editor-container {
    border: 1px solid #F56C6C;
    border-radius: 2px;
  }
}

.ai-agent-app-setting-prompt-input-wrapper-empty-text {
  color: #F56C6C;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
}

.htmlFillSetDom-box {
  overflow: auto;
  height: 100%;
	position: relative;
	display: block;
	resize: vertical;
	padding: 5px 15px;
	line-height: 1.5;
	box-sizing: border-box;
	font-size: inherit;
	font-family: inherit;
	color: var(--el-input-text-color, var(--el-text-color-regular));
	background-color: var(--el-input-bg-color, var(--el-fill-color-blank));
	background-image: none;
	-webkit-appearance: none;
	box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color)) inset;
	border-radius: var(--el-input-border-radius, var(--el-border-radius-base));
	transition: var(--el-transition-box-shadow);
	border: none;
	width: 100%;
	min-width: 200px;
	min-height: 150px;
	word-break: break-all;
  font-size: 14px;

  border: none;
  border-radius: 4px;

	&:focus-visible {
		outline: 0;
		box-shadow: 0 0 0 1px var(--el-input-focus-border-color) inset;
		border: none;
	}

  .fill-span-con {
    color: #13c2c2;
  }
}
