.ai-agent-app-setting-preview {

  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);

  &-title {
    font-size: 16px;
    font-weight: 500;
    color: #1f2329;
    margin-bottom: 8px;
  }

  &-container {
    border-radius: 8px;
    overflow: hidden;
  }

  &-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background-color: #f5f7fa;

    &-title {
      display: flex;
      align-items: center;
      gap: 8px;

      .avatar {
        width: 24px;
        height: 24px;
        border-radius: 50%;
      }

      span {
        font-size: 14px;
        font-weight: 500;
        color: #1f2329;
      }
    }

    &-icon {
      cursor: pointer;
      color: #86909c;
      
      &:hover {
        color: #4e5969;
      }
    }
  }

  &-content {
    height: 500px;
    display: flex;
    flex-direction: column;

    &-top {
      flex: 1;
      padding: 16px;
      overflow-y: auto;
    }

    &-bottom {
      padding: 16px;
    }

    &-app-list {
      margin-bottom: 12px;
    }

    &-question-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-bottom: 16px;
    }
  }

  &-welcome {
    color: #1f2329;
    font-size: 14px;
    line-height: 22px;
    margin-bottom: 16px;
  }

  &-questions {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  &-question-item {
    background: #fff;
    padding: 12px 16px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #1f2329;
    font-size: 14px;
    transition: background-color 0.2s ease;

    &:hover {
      background: #f0f2f5;
    }

    i {
      color: #86909c;
      font-size: 12px;
    }
  }
} 

.ai-agent-app-setting-preview-header {
  background-color: #fff;
}

.ai-agent-app-setting-preview-content-bottom,
.ai-agent-app-setting-preview-content-top {
  background-color: #f0f2fd; 
}

.ai-agent-app-setting-preview-content-bottom {
  .chat-ai-input-bar {
    padding: 0;
  }
}

.ai-agent-app-setting-preview-content-top-message {
  background-color: #fff;
  border-top-right-radius: 16px;
  border-bottom-left-radius: 16px;
  border-bottom-right-radius: 16px;
  padding: 16px;
  margin-top: 6px;
}

.ai-agent-app-setting-preview-question-item {
  width: fit-content;
  line-height: 22px;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 4px 12px;
  gap: 8px;
  border-radius: 12px;
  background: #F0F2F5;
}

.ai-agent-app-setting-preview-content {
  min-height: 560px;
  height: calc(100% - 50px);
}

.ai-agent-app-setting-preview,
.ai-agent-app-setting-preview-container {
  height: 100%;
}
.ai-agent-app-setting-preview-container{
  border-radius: 8px;
  padding: 6px;
  border-radius: 16px;
  border: 6px solid #F5F8FA;
}
.ai-agent-app-setting-preview-header-title-new{
  font-size: 16px;
  color: #262626;
  font-weight: 600;
  line-height: 24px;
  height:48px;
  padding-left: 12px;
  display:flex;
  align-items: center;
}