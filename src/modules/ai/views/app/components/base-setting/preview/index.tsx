/* components */
import { 
  ChatAIInputBar,
  ChatAIAppTag,
  ChatAIPromptTag
} from '@src/component/business/BizChatPanel/chat/components'
import Chat from '@src/component/business/BizChatPanelNew/chat'
import app from '@src/component/business/BizChatPanelNew/app'
/* enum */
import ComponentNameEnum from "@model/enum/ComponentNameEnum"
/* vue */
import { defineComponent, PropType } from "vue"
/* types */
import { AIAgentAppComponentType, AIAgentAppType, AIAgentType } from "@src/modules/ai/types"
/* scss */
import "@src/modules/ai/views/app/components/base-setting/preview/index.scss"
/* util */
import { getLocalesOssUrl } from '@src/util/assets'
import { AiModelEnum } from '@src/modules/ai/model/enum'
import MsgModel from '@model/MsgModel'
import { AIAgentTemplateEnum } from '@src/modules/ai/model/enum'
import { getAIAgentDetail } from '@src/api/AIv2API'
import { AIAgentDetailParamType } from '@src/modules/ai/model/param'
type ChatAIInputBarComponent = InstanceType<typeof ChatAIInputBar>
type ChatAIViewComponent = InstanceType<typeof Chat>

const xiaoBaoIcon = getLocalesOssUrl('/xiao-bao-icon.png')

export default defineComponent({
  name: ComponentNameEnum.AiAgentPreview,
  props: {
    agentAppDetail: {
      type: Object as PropType<AIAgentAppType>,
      required: true
    },
    agentDetail: {
      type: Object as PropType<AIAgentType>,
      default: () => ({})
    },
    itemAgentList:{
      type: Array as PropType<AIAgentType[]>,
      default: () => ([])
    },
    showHeader: {
      type: Boolean,
      default: true
    },
    showHeaderNew: {
      type: Boolean,
      default: false
    },
  },
  computed: {
    chatAIInputBarComponent(): ChatAIInputBarComponent {
      return this.$refs.ChatAIInputBar as ChatAIInputBarComponent
    },
    defaultQuestion(): string[] {
      return this.agentAppDetail.defaultQuestion || []
    },
    welcomeMessage(): string {
      return this.agentAppDetail.welcomeMessage || ''
    },
    agentId(): string {
      return this.agentDetail.id as string
    },
    tenantId(): string {
      return this.agentDetail.tenantId
    },
    chatViewComponent(): ChatAIViewComponent {
      return this.$refs.ChatView as ChatAIViewComponent
    },
    agentIds(): number[] | string[] {
      return this.agentAppDetail.agentIds || []
    },
    agentList(): Array<any> {
      // @ts-ignore
      return this.itemAgentList.filter(item => this.agentIds.includes(item.id)).concat([this.agentDetail]) || []
    }
  },
  watch: {
    welcomeMessage: {
      handler(welcomeMessage: string) {
        this.handlerChangeWelcomeMessage(welcomeMessage)
      },
      immediate: true
    },
    agentDetail: {
  
      handler(agentDetail: AIAgentType) {
        this.changeModelHandler(agentDetail.model as AiModelEnum)
      },
      immediate: true
    },
    agentAppDetail:{
      handler(agentDetail: AIAgentType) {
        // @ts-ignore
       this.tagAgentAppDetail = {
        ...agentDetail,
        id: agentDetail.agentId as number
       }
      },
      immediate: true
    }
  },
  data() {
    return {
      isShowAppService:false,
      tagAgentAppDetail: {} as AIAgentAppComponentType
    }
  },
  components:{
    app
  },
  methods: {
    handleQuestionClick(question: string) {
      this.chatAIInputBarComponent.onInputHandler(question)
    },
    handlerChangeWelcomeMessage(welcomeMessage: string) {
      const chatViewComponent = this.$refs.ChatView as ChatAIViewComponent
      chatViewComponent?.handlerChangeWelcomeMessage(welcomeMessage)
    },
    changeModelHandler(model: AiModelEnum) {
      const chatViewComponent = this.$refs.ChatView as ChatAIViewComponent
      chatViewComponent?.changeModelHandler(model)
    },
    onAppTagClickHandler() {
      this.isShowAppService = true
    },
    onBackHandler() {
      this.isShowAppService = false
    },
    onCancelHandler() {
      this.onBackHandler()
      // @ts-ignore
      this.$refs.AppServiceView?.resetValue()
    },
    async fetchAIAgentWithUnionAiAgent(id: number | string) {
      try {
        
        const params: AIAgentDetailParamType = {
          id: id as number,
          verifyPermissions: false
        }
        const res = await getAIAgentDetail(params)
        
        const isFail = MsgModel.isFail(res)
        if (isFail) {
          const message = MsgModel.getMessage(res)
          this.$message.error(message)
          return
        }
        
        // 获取 Agent 详情
        const agent = MsgModel.getData<AIAgentType>(res, {})
        const app = agent?.aiAgentAppDetail || {}

        this.tagAgentAppDetail = {
          ...app,
          id: app.agentId as number
        }
        
      } catch (error) {
        console.error(error)
      }
    },
    renderHeader() {
      return (
        <div class="ai-agent-app-setting-preview-header">
          <div class="ai-agent-app-setting-preview-header-title">
            <el-image
              class="avatar"
              src={xiaoBaoIcon}
            />
            <span>预览与调试</span>
          </div>
        </div>
      )
    },
    renderHeaderNew() {
      return (
        <div class='ai-agent-app-setting-preview-header-title-new'>
          预览与调试
        </div>
      )
    },
    async onConfirmHandler(id: number | string) {
      
      this.onBackHandler()
      
      const agentDetail = this.agentList.find(item => {
        return item.id == id
      })

      await this.fetchAIAgentWithUnionAiAgent(id)
      
      this.$emit('changeAgent', this.agentDetail)

    },
    renderContent() {      
      return (
        <div class="ai-agent-app-setting-preview-content">
          <app
            ref="AppServiceView"
            v-show={this.isShowAppService}
            currentAgent={this.agentDetail}
            currentApp={this.agentAppDetail}
            list={this.agentList}
            onCancel={this.onCancelHandler}
            onConfirm={this.onConfirmHandler}
          />
          <Chat
            ref="ChatView" 
            v-show={!this.isShowAppService}
            stream
            tenantId={this.tenantId} 
            agentId={this.tagAgentAppDetail.id}
            currentAgent={this.agentDetail}
            currentApp={this.tagAgentAppDetail}
            disabledAgentEnabledValidate={true}
            isSendMessageToServer={false}
            onAppTag={this.onAppTagClickHandler}
            mode="setPreview"
            isViews={true}
          />
        </div>
      )
    },
    renderAIUser() {
      return (
        <div class="ai-agent-app-setting-preview-ai-user">
          小宝 AI
        </div>
      )
    },
    renderWelcomeMessage() {
      return (
        <div class="ai-agent-app-setting-preview-welcome">
          {this.welcomeMessage}
        </div>
      )
    },
    renderQuestionList() {
      return (
        <div class="ai-agent-app-setting-preview-questions">
          {this.defaultQuestion.map((question, index) => (
            <div 
              class="ai-agent-app-setting-preview-question-item"
              onClick={() => this.handleQuestionClick(question)}
            >
              {question}
              <i class="el-icon-arrow-right" />
            </div>
          ))}
        </div>
      )
    },
    renderAppList() {
      return (
        <div class="ai-agent-app-setting-preview-content-app-list">
          <ChatAIAppTag
            value={this.agentAppDetail.name}
          />
        </div>
      )
    },
    renderInputBar() {
      return (
        <ChatAIInputBar 
          ref="ChatAIInputBar"
        />
      )
    }
  },
  render() {
    return (
      <div class="ai-agent-app-setting-preview">
         { this.showHeaderNew && this.renderHeaderNew()}
        <div class="ai-agent-app-setting-preview-container">
        { this.showHeader && this.renderHeader()}
          {this.renderContent()}
        </div>
      </div>
    )
  }
}) 