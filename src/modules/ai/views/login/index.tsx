/* components */
import { defineComponent } from 'vue'
/* enum */
import ComponentNameEnum from "@model/enum/ComponentNameEnum"
/* scss */
import "@src/modules/ai/views/login/index.scss"
/* util */
import { getLocalesOssUrl } from '@src/util/assets'
import { isFalsy } from 'pub-bbx-utils'
import { message } from '@src/util/message'
/* api */
import { getAIAgentDetail, login, sendLoginVerifyCode } from '@src/api/AIv2API'
/* model */
import MsgModel from '@model/MsgModel'
import { AIAgentDetailParamType } from '../../model/param'
import { AIAgentDetailType } from '../../types'

interface SlideVerifyInstance {
  reset: () => void;
}

export default defineComponent({
  name: 'AIAgentLoginView',
  data() {
    return {
      loading: false,
      form: {
        phone: '',
        code: '',
      },
      countdown: 0,
      timer: null as ReturnType<typeof setInterval> | null,
      aiFullBackgroundImage: getLocalesOssUrl('/gpt-full-bg.png'),
      showSlideVerify: false,
      slideVerifyRef: null as SlideVerifyInstance | null,
      images: [
        getLocalesOssUrl('/slide-verify/1.jpeg'),
        getLocalesOssUrl('/slide-verify/2.jpeg'),
        getLocalesOssUrl('/slide-verify/3.jpeg'),
        getLocalesOssUrl('/slide-verify/4.jpeg'),
        getLocalesOssUrl('/slide-verify/5.jpeg'),
        getLocalesOssUrl('/slide-verify/6.jpeg'),
        getLocalesOssUrl('/slide-verify/7.jpeg'),
        getLocalesOssUrl('/slide-verify/8.jpeg'),
        getLocalesOssUrl('/slide-verify/9.jpeg'),
        getLocalesOssUrl('/slide-verify/10.jpeg'),
        getLocalesOssUrl('/slide-verify/11.jpeg'),
        getLocalesOssUrl('/slide-verify/12.jpeg'),
        getLocalesOssUrl('/slide-verify/13.jpeg'),
        getLocalesOssUrl('/slide-verify/14.jpeg'),
        getLocalesOssUrl('/slide-verify/15.jpeg'),
        getLocalesOssUrl('/slide-verify/16.jpeg'),
        getLocalesOssUrl('/slide-verify/17.jpeg'),
        getLocalesOssUrl('/slide-verify/18.jpeg'),
        getLocalesOssUrl('/slide-verify/19.jpeg'),
        getLocalesOssUrl('/slide-verify/20.jpeg')
      ],
      defaultAgentIcon: 'https://shb-multi.oss-cn-hangzhou.aliyuncs.com/images/logo/agent/XB_AI.png',
      loginBackgroundVideo: getLocalesOssUrl('/ai/login_background.mp4'),
      agentDetail: {} as AIAgentDetailType
    }
  },
  computed: {
    attrs(): Record<string, any> {
      return {
        directives: [
          {
            name: 'loading',
            value: this.loading
          }
        ]
      }
    },
    canSendCode(): boolean {
      return this.countdown === 0 && !isFalsy(this.form.phone)
    },
    sendCodeText(): string {
      return this.countdown > 0 ? `${this.countdown}s后重新获取` : '获取验证码'
    },
    queryParams(): Record<string, any> {
      return this.$route.query || {}
    },
    agentId(): string {
      return this.queryParams.agentId || this.queryParams.id || ''
    },
    agentName(): string {
      return this.agentDetail.name || ''
    },
    agentIcon(): string {
      return this.agentDetail.icon || this.defaultAgentIcon
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.getAIAgentDetail()
    },
    async getAIAgentDetail() {
      try {

        if (isFalsy(this.agentId)) {
          message.error('Agent ID 不存在')
          return
        }

        this.loading = true

        const params: AIAgentDetailParamType = {
          id: Number(this.agentId)
        }
        const res = await getAIAgentDetail(params)
        
        const isFail = MsgModel.isFail(res)
        if (isFail) {
          const message = MsgModel.getMessage(res)
          this.$message.error(message)
          return
        }
        
        // Agent 详情
        this.agentDetail = MsgModel.getData<AIAgentDetailType>(res, {})
        
      } catch (error) {
        console.error(error)
      } finally {
        this.loading = false
      }
    },
    handlePhoneInput(value: string) {
      this.form.phone = value
    },
    handleCodeInput(value: string) {
      this.form.code = value
    },
    async handleSendCode() {
      if (!this.canSendCode) {
        return
      }

      // 显示滑动验证
      this.showSlideVerify = true
    },
    async handleVerifySuccess() {
      this.showSlideVerify = false
      
      try {
        this.loading = true
        const result = await sendLoginVerifyCode({
          phone: this.form.phone
        })
        
        const isFail = MsgModel.isFail(result)
        if (isFail) {
          const msg = MsgModel.getMessage(result)
          message.error(msg)
          return
        }

        this.startCountdown()
        message.success('验证码已发送')
      } catch (error) {
        console.error(error)
        message.error('发送验证码失败')
      } finally {
        this.loading = false
      }
    },
    handleVerifyClose() {
      this.showSlideVerify = false
      if (this.slideVerifyRef) {
        this.slideVerifyRef.reset()
      }
    },
    async handleSubmit() {
      if (isFalsy(this.form.phone) || isFalsy(this.form.code)) {
        message.warning('请输入手机号和验证码')
        return
      }

      try {
        this.loading = true
        const result = await login({
          phone: this.form.phone,
          code: this.form.code
        })
        
        const isFail = MsgModel.isFail(result)
        if (isFail) {
          const msg = MsgModel.getMessage(result)
          message.error(msg)
          return
        }

        message.success('登录成功')
        // 登录成功后跳转到 chat 页面
        throw new Error('跳转页面')

      } catch (error) {
        console.error(error)
        message.error('登录失败')
      } finally {
        this.loading = false
      }
    },
    startCountdown() {
      this.countdown = 60
      this.timer = setInterval(() => {
        if (this.countdown > 0) {
          this.countdown--
        } else {
          this.clearTimer()
        }
      }, 1000)
    },
    clearTimer() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
    },
    renderLogo() {

      if (this.loading) {
        return null
      }

      return (
        <div class="ai-agent-login-logo">
          <img src={this.agentIcon} alt={this.agentName} />
        </div>
      )
    },
    renderTitle() {
      return (
        <div class="ai-agent-login-title">
          欢迎来到{this.agentName}
        </div>
      )
    },
    renderHeader() {
      return (
        <div class="ai-agent-login-header">
          {this.renderLogo()}
          {this.renderTitle()}
        </div>
      )
    },
    renderPhoneInput() {
      return (
        <div class="ai-agent-login-form-item">
          <el-input
            value={this.form.phone}
            placeholder="请输入手机号"
            onInput={this.handlePhoneInput}
          />
        </div>
      )
    },
    renderVerifyCodeInput() {
      return (
        <div class="ai-agent-login-form-item ai-agent-login-form-code">
          <el-input
            class="ai-agent-login-form-code-input"
            value={this.form.code}
            placeholder="请输入短信验证码"
            onInput={this.handleCodeInput}
          >
          </el-input>
          <el-button
            class="ai-agent-login-form-code-button"
            type="text"
            disabled={!this.canSendCode}
            onClick={this.handleSendCode}
          >
            {this.sendCodeText}
          </el-button>
        </div>
      )
    },
    renderSubmitButton() {
      return (
        <div class="ai-agent-login-form-item">
          <el-button
            type="primary"
            class="ai-agent-login-submit"
            onClick={this.handleSubmit}
          >
            注册登录
          </el-button>
        </div>
      )
    },
    renderAgreement() {
      return (
        <div class="ai-agent-login-form-tips">
          注册登录即代表已阅读并同意我们的
          <span class="link">
            《用户协议》
          </span>
          和
          <span class="link">
            《隐私政策》
          </span>
          ，未注册号将自动创建账号
        </div>
      )
    },
    renderForm() {
      return (
        <div class="ai-agent-login-form">
          {this.renderPhoneInput()}
          {this.renderVerifyCodeInput()}
          {this.renderAgreement()}
          {this.renderSubmitButton()}
        </div>
      )
    },
    renderFooter() {
      return (
        <div class="ai-agent-login-footer">
          <div>Copyright © 众联成业科技（杭州）有限公司 保留所有权利</div>
          <div>备案号：浙ICP备2020031187号</div>
        </div>
      )
    },
    renderSlideVerify() {
      return (
        <el-dialog
          visible={this.showSlideVerify}
          title="请完成安全验证"
          width="480px"
          onClose={this.handleVerifyClose}
          closeOnClickModal={false}
          showClose={true}
          customClass="ai-agent-login-verify-dialog"
        >
          <div class="ai-agent-login-verify">
            <slide-verify
              ref="slideVerifyRef"
              w={440}
              imgs={this.images}
              sliderText="向右滑动完成验证"
              onSuccess={this.handleVerifySuccess}
              onFail={this.handleVerifyClose}
              onClose={this.handleVerifyClose}
            >
            </slide-verify>
          </div>
        </el-dialog>
      )
    },
    renderBackground() {
      return (
        <div class="ai-agent-login-background">
          <video
            src={this.loginBackgroundVideo}
            autoplay={true}
            loop={true}
            muted={true}
            playsinline={true}
          />
        </div>
      )
    }
  },
  beforeDestroy() {
    this.clearTimer()
  },
  render() {
    return (
      <div class="ai-agent-login" {...this.attrs}>
        {this.renderBackground()}
        <div class="ai-agent-login-container">
          {this.renderHeader()}
          {this.renderForm()}
        </div>
        {this.renderFooter()}
        {this.renderSlideVerify()}
      </div>
    )
  }
})
