.ai-agent-login {
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;

  &-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;

    video {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.3);
    }
  }

  &-container {
    width: 410px;
    padding: 48px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &-header {
    text-align: center;
    margin-bottom: 32px;
  }

  &-logo {
    .iconfont {
      font-size: 48px;
      color: $color-primary;
    }
  }

  &-title {
    font-size: 24px;
    font-weight: 500;
    color: #262626;
  }

  &-form {
    &-item {
      margin-bottom: 24px;

      .el-input {
        width: 100%;
      }
    }

    &-code {
      display: flex;
      gap: 12px;

      .el-input {
        flex: 1;
      }

      .el-button {
        width: 120px;
      }
    }
  }

  &-submit {
    width: 100%;
    height: 40px;
    border-radius: 8px;
  }

  &-form-tips {
    font-size: 14px;
    color: #8c8c8c;

    .link {
      color: $color-primary;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  &-footer {
    position: absolute;
    bottom: 20px;
    left: 0;
    right: 0;
    text-align: center;
    color: #fff;
    font-size: 14px;
    line-height: 1.8;
    opacity: 0.8;
  }

  &-verify {
    padding: 20px 0;
    
    &-dialog {
      .el-dialog__header {
        padding: 16px 20px;
        border-bottom: 1px solid #f0f0f0;
      }
      
      .el-dialog__body {
        padding: 0;
      }

      .el-dialog__title {
        font-size: 16px;
        font-weight: 500;
        color: #262626;
      }
    }
  }
} 

.ai-agent-login-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.ai-agent-login-logo {
  img {
    width: 40px;
    height: 40px;
    border-radius: 12px;
  }
}

.ai-agent-login-form-tips {
  margin-bottom: 32px;
}

.ai-agent-login-form-item:nth-last-of-type(1) {
  margin-bottom: 0;
}

.ai-agent-login-verify {
  display: flex;
  align-items: center;
  justify-content: center;
}

.slide-verify-slider-mask {
  border-color: $color-primary !important;
}

.slide-verify-slider-mask-item {
  &:hover {
    border-color: $color-primary !important;
    background: $color-primary !important;
    background-color: $color-primary !important;
  }
}