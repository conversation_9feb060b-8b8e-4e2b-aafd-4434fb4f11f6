/* components */
import { Loading } from 'element-ui'
import Chat from '@src/component/business/BizChatPanelNew/chat'
import app from '@src/component/business/BizChatPanelNew/app'
/* enum */
import ComponentNameEnum from "@model/enum/ComponentNameEnum"
import StorageKeyEnum from '@model/enum/StorageKeyEnum'
/* vue */
import { defineComponent, ref } from "vue"
/* model */
import { SettingGPTServiceTypeEnum } from '@gpt/model'
/* util */
import { isFalsy } from "@src/util/type"
import { uuid } from '@src/util/lang/string'
/* scss */
import "@src/modules/ai/views/chat/index.scss"
/* api */
import { getRobot, getRobotShare } from '@src/api/AIApi'
/* model */
import MsgModel from '@model/MsgModel'
/* util */
import { parse_with_default_value } from '@src/util/lang/object'
import { isEmpty, isNotEmpty, isNotUndefined, isNull } from 'pub-bbx-utils'
/* types */
import { AIChatRobotItemType } from '@src/component/business/BizChatPanel/chat/types'
import { AIAgentAppComponentType, AIAgentBaseType, AIAgentType } from '@src/modules/ai/types/agent'
import { getAIAgentAppListShare, getAIAgentDetailShare } from '@src/api/AIv2API'
import { AIAgentLogSourceEnum, AIAgentTemplateEnum, AiModelEnum } from '@src/modules/ai/model/enum'
import { message } from '@src/util/message'
import { getDoorTypeListToC } from '@src/api/PortalApi'
import { GetDoorTypeListModel } from '@model/param/in/Portal'
import DoorType from '@model/entity/DoorType'
import EventBus from '@src/util/eventBus'

type ChatAIViewComponent = InstanceType<typeof Chat>

export default defineComponent({
  name: ComponentNameEnum.SettingGPTChatView,
  components: {
    Chat,
    app
  },
  data() {
    return {
      isShare: true,
      userId: '',
      stream: undefined as any,
      loadingInstance: null as any,
      loading: true,
      currentApp: undefined as unknown as AIAgentAppComponentType | undefined,
      currentAgent: undefined as unknown as AIAgentBaseType | undefined,
      isShowAppService: false,
      isLoadFinish: false,
      isOpenShare: true,
      isRobotFail: false,
      isRobotAppFail: false,
      isRobotEnable: true,
      isRobotDelete: false,
      isRobotShare: false,
      robotName: '',
      aiAgentAppList: [] as AIAgentAppComponentType[],
      agentList: [] as AIAgentBaseType[],
      sspLogin: {
        isError: false,
        errorMessage: ''
      }
    }
  },
  computed: {
    disabled(): boolean {
      return this.loading || this.isRobotFail || this.isRobotAppFail
    },
    query(): Record<string, any> {
      return this.$route.query || {}
    },
    agentId(): string {
      return this.query.agentId
    },
    from(): string {
      return this.query.from
    },
    isFromLinkc(): boolean {
      return this.from === 'linkc'
    },
    source(): string {
      return this.isFromLinkc ? AIAgentLogSourceEnum.DOOR : AIAgentLogSourceEnum.OUTSIDE
    },
    doorId(): string {
      return this.getSspDoorId() as string
    },
    tenantId(): string {
      return this.query.tenantId
    },
    isAICustomerService(): boolean {
      return this.query.isAICustomerService
    },
    isShowChat(): boolean {
      return isNotUndefined(this.stream)
    },
    notShowAppTypes(): string[] {
      return [
        SettingGPTServiceTypeEnum.Intelligent,
        SettingGPTServiceTypeEnum.BI
      ]
    },
    insideGptRobotAppList(): AIAgentAppComponentType[] {
      return this.aiAgentAppList
    },
    noPermissionText(): string {
      if (this.sspLogin.isError) {
        return this.sspLogin.errorMessage
      }
      if (isFalsy(this.isRobotEnable)) {
        return 'Agent 已被禁用，请先开启再使用。'
      }
      if (this.isRobotDelete) {
        return 'Agent 已被删除，无法使用。'
      }
      if (isFalsy(this.isRobotShare)) {
        return 'Agent 未开启公开访问，无法使用。'
      }
      return ''
    },
    isHaveAuth(): boolean {

      if (this.loading) {
        return true
      }

      if (this.sspLogin.isError) {
        return false
      }

      return (
        this.isRobotEnable 
        && isFalsy(this.isRobotDelete)
        && this.isRobotShare
      )
    },
    chatSystemMessage(): string {
      
      if (isFalsy(this.isAICustomerService)) {
        return ''
      }
      
      return `
        🌟 欢迎来到AI客户服务 🌟 </br>
        您好！我是“AI客户服务”，您的数字化客户专家。我擅长在多种行业领域提供智能客户服务解决方案。在客户服务领域拥有超过10年的行业经验，处理过超过50,000个客户服务案例，构建了一个包含各种解决方案的庞大知识库。</br>
        🚀 “AI客户服务 —— 智慧客户服务，服务不打烊！ 🚀 </br>
      `
      
    },
    classNames(): Record<string, boolean> {
      return {
        [ComponentNameEnum.SettingGPTChatView]: true,
        'ai-agent-chat-view': true,
      }
    }
  },
  watch: {
    loading: {
      immediate: true,
      handler(loading) {
        if (loading) {
          this.loadingInstance = Loading.service({
            target: this.$el as HTMLElement,
            fullscreen: true,
            lock: true,
            text: '正在启用您的智能 AI 助手...'
          })
        } else {
          this.loadingInstance?.close()
        }
      }
    }
  },
  async created() {

    try {
      
      // await this.initLogin()
    
      this.userId = this.getUserId()
      
      await this.getAgent()
      
      await this.fetchAIAgentAppList()

    } catch (error) {
      console.error(error)
    } finally {
      this.$nextTick(() => {
        this.initHiddenLoading()
      })
    }
    
    this.isLoadFinish = true

    this.initModel()
    
  },
  mounted() {
    EventBus.$on('sspLoginError', this.onSspLoginErrorHandler)
  },
  beforeUnmount() {
    EventBus.$off('sspLoginError', this.onSspLoginErrorHandler)
  },
  methods: {
    initHiddenLoading() {

      const sspAccessToken = this.getSspAccessToken()

      if (sspAccessToken) {
        this.loading = false
      }

    },
    async onSspLoginErrorHandler() {
      try {
        this.loading = true
        await this.initLogin()
      } catch (error) {
        console.error(error)
      } finally {
        setTimeout(() => {
          this.loading = false
        }, 1500)
      }
    },
    getSspEmailKey() {
      return localStorage.getItem('ssp_email_key')
    },
    getSspPhoneKey() {
      return localStorage.getItem('ssp_phone_key')
    },
    getSspUserName() {
      return localStorage.getItem('ssp_user_name')
    },
    getSspDoorId() {
      return localStorage.getItem('ssp_door_type_id')
    },
    getSspAccessToken() {
      return localStorage.getItem('accessToken')
    },
    isSspLogin() {
      const emailKey = this.getSspEmailKey()
      const phoneKey = this.getSspPhoneKey()
      return Boolean(emailKey || phoneKey)
    },
    async initLogin() {

      // 获取自助门户数据
      const doorType = await this.getDoorType()
      // 自助门户数据为空，报错
      if (isFalsy(doorType)) {
        this.sspLogin.isError = true
        this.sspLogin.errorMessage = '自助门户未启用'
        return
      }
      
      // @ts-ignore
      //  portalUrl 示例： "https://pubapp.shb.ltd/p/102378#/mall/index"
      let portalUrl = doorType?.setting?.protalUrl || ''
      if (isEmpty(portalUrl)) {
        this.sspLogin.isError = true
        this.sspLogin.errorMessage = '自助门户链接地址为空'
        return
      }

      // 如果 portalUrl 前面的域和当前的域名不一样，替换成当前域名
      const locationOrigin = window.location.origin
      // 解析原始URL的各个部分
      const urlObj = new URL(portalUrl);
      const portalUrlOrigin = urlObj.origin;
      
      // 如果域名不同，则替换域名
      if (locationOrigin !== portalUrlOrigin) {
        portalUrl = portalUrl.replace(portalUrlOrigin, locationOrigin);
      }

      // 将门户地址替换为登录地址
      const loginUrl = portalUrl.replace('/mall/index', '/login')
      // 登录参数
      const params = {
        from: 'ai_agent_share',
        redirect: window.location.href
      }

      // 如果 loginUrl 存在 ? 则使用 &， 否则使用 ?
      const searchParams = new URLSearchParams(params).toString()
      const operator = loginUrl.includes('?') ? '&' : '?'
      const loginUrlWithParams = `${loginUrl}${operator}${searchParams}`

      // 跳转登录
      window.location.replace(loginUrlWithParams)
      
    },
    async getDoorType() {
      
      const params: GetDoorTypeListModel = {
        tenantId: this.tenantId
      }

      const res = await getDoorTypeListToC(params)
      const isFail = MsgModel.isFail(res)
      if (isFail) {
        message.error('获取门户数据失败')
        return
      }
      
      const data = MsgModel.getData<DoorType[]>(res, [])

      function isAvailable(item: DoorType | undefined) {
        return item?.inUse == 0 && item?.isDelete == 0
      }

      // 默认系统模板是否可用
      const defaultType = data.find(item => item.defaultType == 0)
      const defaultTypeAvailable = isAvailable(defaultType)
      if (defaultTypeAvailable) {
        return defaultType
      }

      // 自定义模板是否可用
      const customTypeList = data.filter(item => item.defaultType == 1)
      const customTypeAvailable = customTypeList.find(isAvailable)
      if (customTypeAvailable) {
        return customTypeAvailable
      }
      
      return null
    },
    init() {
      // @ts-ignore
      this.$refs.ChatView.init()
    },
    async fetchAIAgentAppList() {
      try {
        
        const params = {
          id: this.agentId as unknown as number,
          tenantId: this.tenantId
        }
        const res = await getAIAgentAppListShare(params)
        
        const isFail = MsgModel.isFail(res)
        if (isFail) {
          const message = MsgModel.getMessage(res)
          this.$message.error(message)
          return
        }
        
        // 获取应用列表
        const appList = MsgModel.getData<AIAgentAppComponentType[]>(res, [])
        // 更新应用列表
        this.aiAgentAppList = appList
        // 获取 Agent 详情
        await this.fetchAIAgentWithUnionAiAgent()
        
      } catch (error) {
        console.error(error)
      }
    },
    initChat() {
      
    },
    getUserId() {

      const sspEmailKey = this.getSspEmailKey() as string
      const sspPhoneKey = this.getSspPhoneKey() as string
      
      if (sspEmailKey || sspPhoneKey) {
        return sspEmailKey || sspPhoneKey
      }
      
      const storageUserId = localStorage.getItem(StorageKeyEnum.ChatShareUserId)
      
      if (storageUserId) {
        return storageUserId
      }
      
      const userId = uuid()
      
      localStorage.setItem(StorageKeyEnum.ChatShareUserId, userId)
      
      return userId
    },
    getAgent() {
      
      const params = {
        id: this.agentId as unknown as number,
        tenantId: this.tenantId
      }
      
      return getAIAgentDetailShare(params).then(result => {

        const code = result?.code
        if (code == 900003) {
          this.isRobotDelete = true
          throw new Error('Agent 已被删除')
        }
        
        if (MsgModel.isFail(result)) {
          this.isRobotFail = true
          throw new Error(MsgModel.getMessage(result))
        }
        
        const data = MsgModel.getData<AIAgentType>(result, {} as AIAgentType)
        const isRobotDeleted = isNull(data?.id)
        
        if (isRobotDeleted) {
          this.isRobotDelete = true
          throw new Error('Agent 已被删除')
        }

        const aiAgentAppDetail = data?.aiAgentAppDetail || {}
        const publicAccess = Boolean(aiAgentAppDetail?.open)
         
        this.isRobotEnable = Boolean(data.enable)
        this.isRobotDelete = Boolean(data.deleteTag)
        this.robotName = data.name
        this.stream = true
        this.currentAgent = data
        this.isRobotShare = publicAccess

        document.title = this.robotName
        
        // 获取 Agent 详情
        const agent = MsgModel.getData<AIAgentType>(result, {})
        const agentList = agent?.unionAiAgent || []
        // 更新应用列表
        this.agentList = [
          agent,
          ...agentList
        ].filter(item => {
          return item.template != AIAgentTemplateEnum.TaskFormChat && item.template != AIAgentTemplateEnum.TaskForm
        })
        
      })
    },
    onAppTagClickHandler() {
      this.isShowAppService = true
    },
    onBackHandler() {
      this.isShowAppService = false
    },
    onCancelHandler() {
      this.onBackHandler()
    },
    async onConfirmHandler(id: number) {
      
      this.onBackHandler()
      
      this.currentAgent = this.agentList.find(item => {
        return item.id == id
      })

      await this.fetchAIAgentWithUnionAiAgent()
      
      this.init()
      this.initChat()
      
    },
    async fetchAIAgentWithUnionAiAgent() {
      try {
        
        const params = {
          id: this.currentAgent?.id as unknown as number,
          tenantId: this.tenantId,
          verifyPermissions: true
        }
        const res = await getAIAgentDetailShare(params)
        
        const isFail = MsgModel.isFail(res)
        if (isFail) {
          const message = MsgModel.getMessage(res)
          this.$message.error(message)
          return
        }
        
        // 获取 Agent 详情
        const agent = MsgModel.getData<AIAgentType>(res, {})
        const app = agent?.aiAgentAppDetail || {}
        this.currentApp = app
        
      } catch (error) {
        console.error(error)
      }
    },
    initModel() {
      this.$nextTick(() => {
        const chatView = this.$refs.ChatView as ChatAIViewComponent
        chatView?.changeModelHandler(this.currentAgent?.model as AiModelEnum)
      })
    }
  },
  render() {
    return (
      <permission auth={this.isHaveAuth} text={this.noPermissionText}>
        <div class={this.classNames}>
          
          {this.isLoadFinish && (
            <Chat
              v-show={!this.isShowAppService}
              ref="ChatView"
              disabled={this.disabled}
              currentAgent={this.currentAgent as AIAgentBaseType}
              currentApp={this.currentApp}
              isShare={this.isShare}
              agentId={this.agentId}
              tenantId={this.tenantId}
              userId={this.userId}
              stream={this.stream}
              chatSystemMessage={this.chatSystemMessage}
              onAppTag={this.onAppTagClickHandler}
              source={this.source as AIAgentLogSourceEnum}
              doorId={this.doorId}
            />
          )}
          
          <app
            ref="AppServiceView"
            v-show={this.isShowAppService}
            currentApp={this.currentApp}
            currentAgent={this.currentAgent}
            list={this.agentList}
            onCancel={this.onCancelHandler}
            onConfirm={this.onConfirmHandler}
          />
          
        </div>
      </permission>
    )
  }
});