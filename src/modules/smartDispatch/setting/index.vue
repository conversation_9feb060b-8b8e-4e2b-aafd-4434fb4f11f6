<template>
  <div class="system-setting">
    <el-tabs class="system-setting-tab" v-model="activeName">
      <EventRule ref="EventRuleRef" v-if="activeName === 'EVENT'"/>
      <TaskRule ref="TaskRuleRef" v-if="activeName === 'TASK'"/>
    </el-tabs>
  </div>
</template>

<script>
/* components*/
import TaskRule from './components/TaskRule.vue'
import EventRule from './components/EventRule.vue'
/* utils */
import { parse } from "@src/util/querystring";

export default {
  name: "index",
  components: {
    TaskRule,
    EventRule,
  },
  data() {
    return {
      activeName: 'EVENT',
    }
  },
  mounted() {
    const query = parse(window.location.search) || {};
    this.activeName = query?.type ?? 'EVENT';
  },
}
</script>

<style lang="scss">
@import "./index.scss";

</style>
<style lang="scss" scoped>
.system-setting-tab {
  padding: 4px 16px;

  ::v-deep .el-tabs__content {
    height: 100%;

    .el-tab-pane {
      height: 100%;
    }
  }

  .setting-public-view {
    padding-top: 0 !important;
    padding-left: 0 !important;
    overflow-y: auto;
    height: calc(100% - 60px);
  }

  .setting-page {
    overflow-y: auto;
    height: calc(100% - 80px);
  }
}
</style>