<!--
 * @Author: <PERSON><PERSON>uan
 * @Date: 2021-03-11 09:14:21
 * @LastEditTime: 2025-06-09 17:59:55
 * @LastEditors: 铃兰 <EMAIL>
 * @Description: 
 * You build it, You run it.
-->
<template>
  <div class="event-detail-view" v-loading.fullscreen.lock="loading">
    <div class="top-box bg-w">
      <!-- <div class="event-no font-16">事件 {{workEvent.eventNo}}</div> -->
      <!-- 按钮组 -->
      <event-button-group
        v-if="isInit"
        ref="eventButtonGroupRef"
        :init-data="initData"
        :work-event="workEvent"
        :allFields="allFields"
        :auth="auth"
        :show-assign-button="showAssignButton"
        :vipApproveSetting="vipApproveSetting"
        @loadingPage="loadingPage"
        @changeRightActiveTab="changeRightActiveTab"
        @updataRemarks="updataRemarks"
        @toReview="modifyEvaluate"
      >
      <template v-slot:intelligentTagShow>
        <IntelligentTagsTaggingView v-bind="tagsSingleComponentAttrs" showType="text" />
      </template>
    </event-button-group>
    </div>

    <BaseTileLayoutTabBar
      v-if="taskLayout === 1"
      :bar-list="taskLayoutTabBarList"
      :now-item="leftActiveTab"
      @changeItem="tabBarChangeItem"
      @openLayoutModal="openBaseLayoutModal"
    ></BaseTileLayoutTabBar>

    <base-collapse 
      class="task-detail-main-content detail-main-content" 
      :show-collapse="showCollapse" 
      :direction.sync="collapseDirection"
      :hide-part-collapse="hidePartCollapse">
      <template slot="left">
        <!-- 事件信息 -->
        <div class="event-info bg-w" v-show="collapseDirection !== 'left'">
          <BaseTileLayoutTabBar
            v-if="taskLayout === 2"
            :bar-list="taskLeftBarList"
            :now-item="leftActiveTab"
            :structure="2"
            @openLayoutModal="openBaseLayoutModal"
          ></BaseTileLayoutTabBar>
          <event-info
            v-if="showEventInfo"
            :class="formCellCount > 1 ? 'bbx-normal-form-view-cell' : ''"
            :fields="allFields"
            :door-gray="doorGray"
            :event="workEvent"
            :auto="JSON.parse(initData.auth)"
            :can-see-customer="canSeeCustomer"
            :cus-match-map="cusMatchMap"
            :show-assign-button="showAssignButton"
            :has-cus-create="hasCusCreate"
            @eventInfoSaveCusInfo="eventInfoSaveCusInfo"
            @eventInfoSaveLinkman="eventInfoSaveLinkman"
            @updateRecord="updateRecord"
            :form-cell-count="formCellCount"
          ></event-info>
        </div>
        <div class="collapse-left" v-show="collapseDirection === 'left'">
          {{$t('event.eventOrderList.text10')}}
        </div>
      </template>
      <template slot="right">
        <!-- 关联数据 -->
        <div class="contact-info bg-w" v-loading="rightTabBarLoading" v-show="collapseDirection !== 'right'">
          <BaseBarV3
            v-if="taskLayout == 2"
            :bar-list="tabBarList"
            :now-item="rightActiveTab"
            @changeItem="tabBarChangeItem"
            @upDateBarList="tabBarUpdateList">
            <template slot="tooltip-relationTask" slot-scope="{tabs}">
              <el-tooltip placement="top" :disabled="!relationTaskList.length && !archiveCount">
                <span class="overHideCon-1">{{ tabs[tabNameKey] }}</span>
                <div slot="content" v-html="$t('task.detail.components.unfinishedAndAllTask', {unfinished: unfinishedRelatedTask, all: relationTaskList.length+archiveCount})"></div>
                <span>({{ `${unfinishedRelatedTask}/${relationTaskList.length+archiveCount}` }})</span>
              </el-tooltip>
            </template>
          </BaseBarV3>
          <div class="right-con-box">
            <event-progress-rate v-if="rightActiveTab == 'eventProgressRate'" ref="eventProgressRate" :event="workEvent" :init-data="initData"></event-progress-rate>
            <relation-task v-if="rightActiveTab == 'relationTask'" :relation-task-list="relationTaskList" :event-id='eventId' :archive-count='archiveCount' :is-archive-view='isArchiveView'></relation-task>
            <AssociatedSession v-if="rightActiveTab == 'associatedSession'" ref="associatedSession" :associated-session-list="associatedSessionList" :event-id='eventId'></AssociatedSession>
            <order-info v-if="rightActiveTab == 'orderInfo'" :order-sheet="orderSheet"></order-info>
            <event-fields-form v-if="rightActiveTab == 'receiptInfo'" :work-event="workEvent" :fields="receiptFields" type="receiptInfo"></event-fields-form>
            <event-fields-form v-if="rightActiveTab == 'approveReceiptInfo'" :work-event="workEvent" :work-draft-event="workDraftEvent" :fields="receiptFields" type="approveReceiptInfo"></event-fields-form>
            <AmountSettlementTab v-if="rightActiveTab == 'amountSettlement'" module="event" :moduleSourceId="workEvent.id"></AmountSettlementTab>
            <event-fields-form
                v-if="rightActiveTab == 'finishInfo'"
                :work-event="workEvent"
                :work-draft-event="workDraftEvent"
                :fields="receiptFields"
                :is-edit="true"
                @reloadTab="reloadTab"
                type="finishInfo"
                @needApprove="needApprove"
                @needVipApprove="needVipApprove"
            ></event-fields-form>
            <customer-evaluate
                v-if="rightActiveTab == 'evaluateInfo'"
                :work-event="workEvent"
                :evaluate-data="evaluateData"
                :evaluate-config="evaluateConfig"
                :edit-all="editAll"
                @modifyEvaluate="modifyEvaluate"
            ></customer-evaluate>
            <template v-for="item in tabBarList">
              <card-info v-if="item[tabTypeKey] == tabIsCardType &&rightActiveTab == item[tabNameKey]" :work-event="workEvent" :card-info="item" @updateCard="updateCardData" :form-cell-count="formCellCount"></card-info>
            </template>
          </div>
        </div>
        <div class="collapse-right" v-show="collapseDirection === 'right' && this.tabBarList.length">
          {{this.tabBarList.length && this.tabBarList[0].tabLabel}}
        </div>
      </template>
    </base-collapse>

    <biz-layout-modal
      ref="bizLayoutModal"
      :biz-layout-type="baseLayout"
      :columns="formCellCount"
      @changeLayout="changeTaskDetailLayout">
    </biz-layout-modal>

    <!-- 修改评价弹窗 -->
    <modify-evaluate-dialog
      ref="modifyEvaluteRef"
      :work-event="workEvent"
      :evaluate-data="evaluateData"
      :evaluate-config="evaluateConfig"
    ></modify-evaluate-dialog>

    <event-add-remarks-dialog ref="eventAddRemarksDialog" :work-event="workEvent" :remark-list="initData.remarkList" :message-config="initData.messageConfig" ></event-add-remarks-dialog>

  </div>
</template>

<script>
import AuthUtil from '@src/util/auth';
import * as Utils from '@src/component/form/util';
import * as EventApi from '@src/api/EventApi.js';
import * as IMApi from '@src/api/ImApi.js';
import {getCount} from '@src/api/ArchiveApi';
import EventInfo from './components/EventInfo'
import EventButtonGroup from './components/EventButtonGroup.vue';

import EventProgressRate from './components/ContactInfo/EventProgressRate.vue';
import RelationTask from './components/ContactInfo/RelationTask'
import OrderInfo from './components/ContactInfo/OrderInfo'
import CustomerEvaluate from './components/ContactInfo/CustomerEvaluate'
import ModifyEvaluateDialog from './components/Dialog/ModifyEvaluateDialog'

import { useComplementRelatedFieldDateType } from '@hooks/useComplementRelatedFieldDateType'
import EventFieldsForm from './components/ContactInfo/EventFieldsForm.vue';
import CardInfo from './components/ContactInfo/CardInfo/index.vue';
import AssociatedSession from './components/ContactInfo/AssociatedSession'
import BaseBarV3 from '@src/component/common/BaseTabBar/BaseTabBarV3.vue'
import BaseTileLayoutTabBar from '@src/component/common/BaseTabBar/BaseTileLayoutTabBar.vue'
import AmountSettlementTab from '@src/modules/smartSettlement/components/amountSettlementList.vue'
import { BaseTabBarUsualEnum, StorageHttpParamsForTerminalType, StorageHttpParamsForModuleType } from '@src/component/common/BaseTabBar/enum'
import { computedTabList } from '@src/util/tabBarUtils';
import { getStorageForDetailTabbar, setStorageForDetailTabbar } from '@src/api/SystemApi'
/* service */
import { isConnectorCard } from '@service/CardService'
/* mixin */
import { VersionControlTaskMixin } from '@src/mixins/versionControlMixin'
import { useStateSystemViewLayout } from 'pub-bbx-utils'
import { intelligentTagsDetailMixin } from '@src/modules/intelligentTags/mixins/index'
import * as FormUtil from '@src/component/form/util'

import { cloneDeep } from '@src/util/type';
import EventAddRemarksDialog from './components/Dialog/EventAddRemarksDialog'
import { getSettlementPoolByModuleCheck } from '@src/api/SettingApi'


import i18n from '@src/locales';
export default {
  mixins: [VersionControlTaskMixin, intelligentTagsDetailMixin],
  components:{ 
    EventInfo,
    EventButtonGroup,
    EventProgressRate,
    RelationTask,
    OrderInfo,
    CustomerEvaluate,
    ModifyEvaluateDialog,
    EventFieldsForm,
    CardInfo,
    AssociatedSession,
    BaseBarV3,
    BaseTileLayoutTabBar,
    AmountSettlementTab,
    EventAddRemarksDialog,
  },
  data() {
    return {
      initData: {},
      taskLeftBarList: [
        {
          tabName: 'eventInfo',
          disabled: true,
          tabLabel: i18n.t('event.eventOrderList.text10'),
          tabShow: true,
          position: 'left',
        }
      ],
      tabPosition: '',
      baseLayout: 2,
      taskLayoutTabBarList: [],
      leftActiveTab: 'eventInfo',
      rightActiveTab: '',
      allFields: [],
      customerCount: {},
      productCount: [],
      loading: false,
      relationTaskList: [], // 关联工单
      unfinishedRelatedTask: 0, // 未完成关联工单数量
      archiveCount:0, // 归档工单数量
      isArchiveView:false, // 归档工单查看权限
      associatedSessionList:[], // 关联会话
      im_isOpen:0, // 是否开通在线客服
      tabBarList:[],
      tabLabelKey:BaseTabBarUsualEnum.TabBarListItemLabel,
      tabNameKey:BaseTabBarUsualEnum.TabBarListItemKey,
      tabTypeKey:BaseTabBarUsualEnum.TabBarListItemType,
      tabIsCardType:BaseTabBarUsualEnum.TabBarCardInfoType,
      rightTabBarLoading:false,
      showCollapse: true,
      collapseDirection: '',
      formCellCount:1,
      vipApproveSetting: {},
    }
  },
  computed: {
    taskLayout() {
      return this.baseLayout
    },
    hidePartCollapse() {
      if(this.taskLayout === 1) return this.tabPosition === 'left' ? 'right' : 'left';
      return '';
    },
    workEvent() {
      return this.initData.workEvent || {};
    },
    auth() {
      return this.initData.auth ? JSON.parse(this.initData.auth) : {}
    },
    evaluateData() {
      return this.initData.evaluateDataJson ? JSON.parse(this.initData.evaluateDataJson) : {};
    },
    evaluateConfig() {
      return this.initData.evaluateConfig || {};
    },
    workDraftEvent() {
      return this.initData.receiptDraft ? this.initData.receiptDraft.event : {};
    },
    // 事件未完成
    isAwaiting(){
      return ['allocated', 'processing', 'created'].indexOf(this.workEvent.state) >= 0;
    },
    canEdit() {
      return this.initData.canEdit;
    },
    /* 是否拥有事件分配权限 */
    caseAllot() {
      return this.initData.caseAllot;
    },
    /**
     * @description: 是否显示分配按钮 
     * 是否拥有事件分配权限
     * workEvent 取值是来自initData里面的 workEvent
     * 事件未完成 isAwaiting
     * 事件不在审批中 inApprove != 1 
     * 事件不是暂停状态 isPaused != 1 
     * 是否有编辑权限 this.canEdit
     * 该事件不存在负责人 this.workEvent.executorId
     * 此处改动是为了修复 bug 17739 和移动端对齐
     * @return {Boolean} 
    */
    showAssignButton(){
      return this.caseAllot && this.isAwaiting && this.workEvent.inApprove != 1 && this.workEvent.isPaused != 1 && !this.workEvent.executorId
    },
    /* 是否可以查看客户详情 */
    canSeeCustomer() {
      return this.initData.canSeeCus || false;
    },
    cusMatchMap() {
      return this.initData.cusMatchMap || {};
    },
    canPause() {
      return this.initData.canPause || false;
    },
    showMyBtn() {
      return this.initData.showMyBtn || false;
    },
    loginUser() {
      return this.initData.loginUser || {};
    },
    /* 是否拥有事件编辑权限 */
    edit() {
      return this.initData.edit || false;
    },
    orderSheet() {
      return this.initData.orderSheet;
    },
    receiptFields() {
      const receiptFieldsList = this.initData.receiptFields || [];

      receiptFieldsList.forEach(field=>{
        field.displayName = field?.displayNameLanguage?.[i18n.locale] || field.displayName;
      })
      
      return receiptFieldsList;
    },
    unFinishedAppr() {
      return this.initData.unFinishedAppr;
    },
    editAll() {
      return this.initData.editAll || false;
    },
    eventType() {
      return this.initData.eventType;
    },
    canApprove() {
      return this.initData.canApprove;
    },
    cardInfo() {
      // 多选格式调整
      this.initData.cardInfo.forEach(card => {
        card.fields = Utils.migration(card.fields || []);
      })
      
      // 过滤没有权限的附加组件
      return this.initData.cardInfo.filter(card => {
        
        let { canWrite, canRead, canCreate, canDelete } = card;
        
        const isHaveAuth = (canWrite || canRead || canCreate || canDelete)
        const isHaveFields = card.fields.length
        
        // 是否为连接器附加组件，连接器附加组件里面不存字段列表，所以只判断权限就可以
        if (isConnectorCard(card)) {
          return isHaveAuth
        }
        
        return isHaveAuth && isHaveFields
        
      })
    },
    /* 该登录账户是否是事件负责人 */
    isExecutor() {
      let executorId = this.workEvent.executorId || '';
      return executorId == this.loginUser.userId;
    },
    /* 是否可以看见完整的回执信息 */
    canLookCompleteReceipt() {
      return this.initData.canEdit || this.auth.VIP_APPROVE == 3;
    },
    isInit() {
      return Object.keys(this.initData).length > 0
    },
    showEventInfo() {
      return this.isInit && this.leftActiveTab === 'eventInfo'
    },
    hasCusEdit() {
      return AuthUtil.hasAuth(this.auth, 'CUSTOMER_EDIT')
    },
    hasCusCreate() {
      return AuthUtil.hasAuth(this.auth, 'CUSTOMER_CREATE')
    },
    // 待结算池查看权限
    hasSettlePoolAuth() {
      return AuthUtil.hasAuth(this.auth, 'INTELLIGENT_SETTLE_POOL_VIEW')
    },
    // 是否开启灰度
    doorGray() {
      return this.initData.doorGray;
    },
    /*是否显示评价或者回访的附加组件
    * 1.isEvaluate是否评价过
    * 2.reviewerState是否回访过
    * */
    showReviewTab() {
      return this.workEvent.isEvaluate == 1 || this.workEvent.reviewerState == 1
    },
  },
  created(){
    this.eventId = window.location.pathname.split('/event/view/')[1]
    this.im_isOpen = localStorage.getItem('im_isOpen') || 0
    this.init()
    this.updateIntelligentTagsModule('EVENT')
  },
  methods: {
    openBaseLayoutModal() {
      this.$refs.bizLayoutModal.open()
    },
    changeTaskDetailLayout(type, columns) {
      this.leftActiveTab = 'eventInfo' 
      this.baseLayout = type
      this.tabPosition = 'left'
      if(type === 2) {
        this.rightActiveTab = 'eventProgressRate' 
      }
      this.formCellCount = columns * 1
    },
    async initFormBuilderCellCount(){
      const { getSystemViewLayout } = useStateSystemViewLayout()
      const count = await getSystemViewLayout()
      this.baseLayout = count.baseLayout || 2
      this.formCellCount = count.formCellCount || 1
    },
    async init() {
      await this.initFormBuilderCellCount()
      await this.queryInitData()
      await this.getEventFields()
      await this.initApproveBtn()
      this.getEventRelatedTask()
      this.getCount()
      this.getListByBizInfo()
      this.initRightTabBar()
    },

    // 初始化获取高级审批按钮展示
    async initApproveBtn() {
      try {
        // 如果是高级审批模式
        if(this.unFinishedAppr?.processorInstanceId) {
          const { success, result } = await EventApi.getApproveLoadForEvent({
            approveId: this.unFinishedAppr?.id,
          })
          if (success) {
            this.vipApproveSetting = result.approveInfo
          }
        }
      } catch (e) {
        console.error(e)
      }
    },

    // 获取initData
    async queryInitData() {
      const params = {
        eventId: this.eventId
      }
      this.loading = true
      await EventApi.queryEventViewInitData(params)
        .then(res => {
          if(res.status === 0) {
            this.initData = res.data
            const currentTabId = window.frameElement?.dataset.id;
            let { eventNo } = this.workEvent
            let title = `${this.$t('common.base.event')}${eventNo}`
            this.$platform.setTabTitle({
              id: currentTabId,
              title
            })

            this.$nextTick(() => {
              const toReview = this.$getUrlObj(window)?.toReview ?? false;
              if(toReview && this.initData?.reviewEventEdit) {
                this.$refs.modifyEvaluteRef.openDialog({ newReview: true })
              }
            })
          }
        })
        .finally(()=> {this.loading = false})
    },
    // 更新卡片数据

    async updateCardData(){
      const params = {
        eventId: this.eventId
      }
      await EventApi.queryEventViewInitData(params)
        .then(res => {
          if(res.status === 0) {
            this.initData = res.data
          }
        })
        .finally(()=> {})
    },
    // 获取归档工单数量
    async getCount(){
      const params = {
        eventId:this.eventId
      }
      const {code, message, result} = await getCount(params);
      if(code === 0){
        this.archiveCount = result.archiveCount;
        this.isArchiveView = result.isArchiveView;
      }else{
        this.$notify({
          title: this.$t('common.base.fail'),
          message,
          type: 'error'
        });
      }
    },

    // 获取事件的字段
    async getEventFields() {
      const params = {
        tableName: 'event',
        templateId: this.workEvent.templateId || '',
        isFromSetting: false
      }
      const extraFields = [
        {
          displayName: this.$t('common.fields.executorUser.displayName'),
          fieldName: 'executorName',
          isSystem: 1,
        },
        {
          displayName: this.$t('common.fields.synergies.displayName'),
          fieldName: 'synergies',
          isSystem: 1,
        },
        {
          displayName: this.$t('event.eventState'),
          fieldName: 'state'
        },
        {
          displayName: this.$t('common.base.column.createPerson'),
          fieldName: 'createUser',
          formType: 'user',
          isSystem: 1,
        },
        {
          displayName: this.$t('common.base.column.createTime'),
          fieldName: 'createTime',
          formType: 'timestamp',
          isSystem: 1,
        }, 
        {
          displayName: this.$t('common.base.createSource'),
          fieldName: 'source',
          isSystem: 1,
        }, 
      ]

      const res = await EventApi.getEventTemplateFields(params)
      this.allFields = [...res, ...extraFields]

      const {
        complementCustomerRelationFieldsDateType,
        complementProductRelationFieldsDateType,
      } = useComplementRelatedFieldDateType()
      // 检查是否有关联客户字段、补全时间格式信息
      await complementCustomerRelationFieldsDateType(this.allFields, this.workEvent.cusId)
      // 检查是否有关联产品字段、补全时间格式信息
      await complementProductRelationFieldsDateType(this.allFields)

      // 处理富文本
      this.initData.workEvent = await FormUtil.initRichTextContent(this.allFields, this.initData.workEvent)

    },

    // 获取关联工单
    getEventRelatedTask() {
      const params = { eventId: this.workEvent.id }
      
      EventApi.getEventRelatedTask(params)
        .then(res=> {
          const unfinishedState = ['created', 'allocated', 'taskPool', 'accepted', 'refused', 'processing']
          this.relationTaskList = res.data || []
          this.unfinishedRelatedTask = this.relationTaskList.filter(item => unfinishedState.indexOf(item.state) > -1).length
        })
    },

    // 手动切换tab
    changeRightActiveTab(activeTab) {
      // 通栏模式点击回执进入回执编辑tab处理
      if (activeTab === 'finishInfo' && this.taskLayout === 1) {
        const finishInfoTabBar = this.tabBarList.find(item => item.tabName === 'finishInfo');
        if (finishInfoTabBar) {
          this.tabBarChangeItem(finishInfoTabBar);
          return;
        }
      }

      this.rightActiveTab = activeTab;
    },
    updataRemarks() {
      this.$refs.eventAddRemarksDialog.openDialog()
    },

    // 重新加载
    reloadTab() {
      this.$refs.eventButtonGroupRef.reloadTab()
    },

    // 打开需要审批弹窗
    needApprove(data) {
      this.$refs.eventButtonGroupRef.needApprove(data)
    },

    // 打开高级审批批弹窗
    needVipApprove(data) {
      this.$refs.eventButtonGroupRef?.toNeedVipApprove(data)
    },
    // 打开修改客户评价弹窗
    modifyEvaluate(val = {}) {
      this.$refs.modifyEvaluteRef.openDialog(val)
    },

    // 事件信息模块保存客户信息
    eventInfoSaveCusInfo() {
      this.$refs.eventButtonGroupRef.onlySaveCusInfo()
    },

    // 事件信息模块保存联系人
    eventInfoSaveLinkman() {
      this.$refs.eventButtonGroupRef.onlySaveLinkman()
    },

    // 设置页面loading状态
    loadingPage(loading) {
      this.loading = loading
    },

    // 更新事件进度
    updateRecord() {
      this.$refs.eventProgressRate.getEventRecord()
    },

    // 根据事件查询关联会话列表
    async getListByBizInfo(){
      const params = {
        bizType:'event',
        bizId:this.eventId,
        pageSize:9999
      }
      const {code, data} = await IMApi.getListByBizInfo(params)
      if(code == 0){
        this.associatedSessionList = data.list
      }
    
    },
    async initRightTabBar(){
      if (this.taskLayout === 1) {
        this.tabPosition = 'left';
      }
      let { TabBarListItemKey:tabName, TabBarListItemLabel:tabLabel, TabBarListItemShow:tabShow, TabBarCardInfoType:tabIsCardType, TabCardInfoSingle:tabIsCardSingle, TabBarListItemType:tabType } = BaseTabBarUsualEnum;
      let cardInfoList = [];
      this.rightTabBarLoading = true;
      let settlementPoolByModuleCheckShow = false;
      try {
        // 获取附加组件列表
        cardInfoList = this.cardInfo.map(item=>{
          const { cardId:id, cardName, cardNameLanguage } = item;
          const name = cardNameLanguage && cardNameLanguage[i18n.locale] ? cardNameLanguage[i18n.locale] : cardName
          return {
            ...item,
            [tabName]: `${tabIsCardSingle}${id}`, 
            [tabLabel]: name, 
            [tabShow]:true, 
            [tabType]:tabIsCardType
          }
        })
        let getSettlementPoolByModuleCheckRes = await getSettlementPoolByModuleCheck({
          module:'event',
          moduleSourceId:this.workEvent.id
        })
        if(getSettlementPoolByModuleCheckRes.status === 0){
          settlementPoolByModuleCheckShow = getSettlementPoolByModuleCheckRes.data
        }
      } catch (error) {
        console.warn(error, 'error try catch getEnabledCardInfo');
      }
      let barArr = [
        {[tabName]:"eventProgressRate", disabled:true, [tabLabel]:this.$t('event.detail.text1'), [tabShow]:true},
        // 纯客服云版本不显示关联工单
        ...(this._isShowTaskModule ? [{[tabName]:"relationTask", [tabLabel]:this.$t('common.fields.relationTask.displayName'), [tabShow]:true }] : []),
        ...(this.im_isOpen == 1 ? [{[tabName]:"associatedSession", [tabLabel]:this.$t('event.detail.text2'), [tabShow]:true }] : []),
        ...(this.orderSheet && this.orderSheet !== '' ? [{[tabName]:"orderInfo", [tabLabel]:this.$t('event.detail.text3'), [tabShow]:false }] : []),
        ...((this.workEvent.state == 'finished' || (this.workEvent.state == 'closed' && this.workEvent.completeTime)) && this.receiptFields.length > 0 ? [{[tabName]:"receiptInfo", [tabLabel]:this.$t('common.task.TaskExportGroupTextEnum.taskReceipt'), [tabShow]:true }] : []),
        ...(this.hasSettlePoolAuth && settlementPoolByModuleCheckShow ? [{[tabName]:'amountSettlement', [tabLabel]:this.$t('common.smartSettlement.amountSettle'), [tabShow]:true}] : []),
        ...(this.unFinishedAppr != null 
              && this.unFinishedAppr.action == '完成' // TODO: 中文判断
              && this.workEvent.inApprove == 1 
              && this.receiptFields.length > 0 
            && (this.showMyBtn || this.isExecutor || this.canApprove || this.canLookCompleteReceipt) ? [{[tabName]:"approveReceiptInfo", [tabLabel]:this.$t('event.detail.text4'), [tabShow]:true }] : []),
        ...(this.showMyBtn
              && this.workEvent.inApprove != 1 
              && this.workEvent.isPaused != 1 
              && this.receiptFields.length > 0 
              && (
              (this.workEvent.state == 'allocated' && !this.eventType.flowSetting.start.state) 
              || (this.workEvent.state == 'processing' && this.eventType.flowSetting.start.state) 
              || (this.workEvent.state == 'processing' && !this.eventType.flowSetting.start.state)
            ) ? [{[tabName]:"finishInfo", [tabLabel]:this.$t('common.task.TaskExportGroupTextEnum.taskReceipt'), [tabShow]:true}] : []),
            ...(this.showReviewTab ? [{[tabName]:"evaluateInfo", [tabLabel]:this.$t('customer.detail.customerTaskTable.table.label.suggestion'), [tabShow]:true }] : []),
            ...cardInfoList
      ]
      barArr.forEach(tab => {
        tab.needTrack = true
        tab.position = 'right'
      })
      let parasm_ = {
        equipment:StorageHttpParamsForTerminalType.PC,
        bizType:StorageHttpParamsForModuleType.Event,
        bizTypeId:this.eventType.id
      }
      try {
        // 获取tabbar用户行为缓存
        let storageList = await getStorageForDetailTabbar(parasm_);
        if(storageList.status !== 0) {
          throw storageList.message
        }
        let storageList_ = storageList.data.map(item=>{
          const { cardId, checked} = item;
          return {
            [tabName]:cardId,
            [tabShow]:checked
          }
        })
        barArr = computedTabList(barArr, storageList_)
      } catch (error) {
        console.warn(error, 'error try catch getStorageForDetailTabbar');
      }
      this.tabBarList = barArr;
      this.taskLayoutTabBarList = cloneDeep(barArr.filter(item => item.tabShow));
      if(this.taskLayoutTabBarList.findIndex(item => item.tabName === 'eventInfo') === -1) {
        this.taskLayoutTabBarList.unshift(...this.taskLeftBarList)
      }
      this.$nextTick(()=>{
        let firstItem = barArr && barArr.find(item=>item[tabShow])
        this.rightActiveTab = firstItem?.[tabName];
        this.rightTabBarLoading = false;
        this.leftActiveTab = 'eventInfo';
      })
    },
    tabBarChangeItem(item){
      let tabName = BaseTabBarUsualEnum.TabBarListItemKey;
      
      this.tabPosition = item.position;
      if(this.taskLayout === 1 || item.position === 'left') {
        this.leftActiveTab = item[tabName];
      } 
      if(item.position === 'right') {
        this.rightActiveTab = item[tabName];
      } 
    },
    /**
     * @des tabbar数据发生变更钩子函数
     */
    tabBarUpdateList(list){
      const { TabBarCardInfoType, TabBarListItemKey:tabName, TabBarListItemShow:tabShow } = BaseTabBarUsualEnum;
      let list_ = list.map(item=>{
        return {
          cardId: item.type == TabBarCardInfoType ? item.id : item[tabName],
          checked: item[tabShow]
        }
      })
      let parasm_ = {
        equipment:StorageHttpParamsForTerminalType.PC,
        bizType:StorageHttpParamsForModuleType.Event,
        bizTypeId:this.eventType.id,
        cardList:list_
      }
      setStorageForDetailTabbar(parasm_)
      this.taskLayoutTabBarList = cloneDeep(this.tabBarList.filter(item => item.tabShow));
    },
    
    getBtnsTrackData(id, data) {
      return this.$track.formatParams(id, data, 'DETAIL_BTNS_GROUP');
    },
  }
}
</script>

<style lang="scss" scoped>
.event-detail-view {
  width: 100%;
  min-width: 1100px;
  padding: 10px;
  overflow: hidden;
  .bbx-base-tab-bar-box{
    background-color: $bg-color-l2;
  }
  .top-box {
    display: flex;
    justify-content: space-between;
    padding: 12px 16px;
    margin-bottom: 10px;
    align-items: center;
  }
  .detail-main-content {
    min-height: 100%;
    >div{
      padding-bottom: 10px;
    }

    .right-con-box{
      padding: 16px;
      overflow-x: hidden;
    }
    .collapse-left,
    .collapse-right {
      height: 40px;
      line-height: 40px;
      padding-left: 12px;
      color: $text-color-regular;
      border-bottom: 2px solid $color-border-l2;
    }
  }
  ::v-deep .base-collapse {
    min-height: calc(100vh - 88px);
    .base-collapse-right, .base-collapse-left, .resize-bar {
      height: auto
    }
  }
}
.bg-w {
  background: #fff;
  border-radius: 4px;
}
</style>
<style lang="scss">
  .detail-main-content {
    .task-tab-container {
      padding: 16px;
    }
    .form-view-row {
      // padding: 6px 0;
    }
    .el-button {
      // min-width: 60px;
      font-weight: normal;
      border-radius: 4px;
    }
  }
  
  .event-info{
    // tabs标签页
  .el-tabs {
    height: 100%;
    display: flex;
    flex-direction: column;

    &__header {
      margin: 0;
      background: $bg-color-l2;
      position: sticky;
      top: 0;
      z-index: 9;

      .el-tabs__item {
        padding: 0 24px !important;
        color: $text-color-regular;
        font-weight: normal;
  
        &.is-active {
          @include fontColor();
        }
      }
      .el-tabs__nav{
        display: flex;
        align-items: center;
      }
    }

    &__content {
      flex: 1;
      // overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }
  }
</style>
