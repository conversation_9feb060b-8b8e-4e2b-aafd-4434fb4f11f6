<!--
 * @Author: <PERSON><PERSON>uan
 * @Date: 2021-03-30 09:48:30
 * @LastEditTime: 2025-05-20 14:06:32
 * @LastEditors: 铃兰 <EMAIL>
 * @Description: 事件进度
 * You build it, You run it.
-->
<template>
  <div class="event-progress-rate">
    <div v-if="!agendaEvent" class="progress-info">
      <div class="title"><i class="iconfont icon-liucheng1"></i>{{$t('event.detail.components.eventProgressRate.text1')}}</div>
      <div class="progress-content">
        <biz-process :value="eventState" :data="event" :flow-setting="initData.eventType.flowSetting" mode="event" @change="changeEventProcessState"></biz-process>
        <div v-if="event.completeTime != null" class="cost-time">
          <el-tooltip placement="bottom" :content="$t('event.detail.components.eventProgressRate.text2')">
            <span>{{`${$t('task.detail.components.respondTime')}：${formatProgressTime(event.acceptUsedTimeStr)}`}}</span>
          </el-tooltip>
          <el-tooltip placement="bottom" :content="$t('event.detail.components.eventProgressRate.text3')">
            <span>{{`${$t('task.detail.components.workTime')}：${formatProgressTime(event.workUsedTimeStr)}`}}</span>
          </el-tooltip>
          <el-tooltip placement="bottom" :content="$t('event.detail.components.eventProgressRate.text4')">
            <span>{{`${$t('event.list.text8')}：${formatProgressTime(event.finishUsedTimeStr)}`}}</span>
          </el-tooltip>
        </div>
      </div>
    </div>
    
    <div class="update-info">
      <template v-if="!agendaEvent">
        <div class="title"><i class="iconfont icon-gengxin"></i>{{$t('common.base.lastUpdate')}}</div>
      </template>
      <div class="progress-content">
        <!-- start 时间轴 -->
        <div class="task-timeline" ref="timeline">
          <base-timeline 
            :data="recordList" 
            :record-render="renderRecord" 
            :loading="recordLoading"
          />
        </div>
        <!-- end 时间轴 -->
      </div>
    </div>
  </div>
</template>

<script>
import { isOpenData } from '@src/util/platform';
import * as EventApi from '@src/api/EventApi.js';
import addressMixin from '@src/mixins/addressMixin';
import platform from '@src/platform'
import _ from 'lodash';
import Page from '@model/Page';
import { trimAll, formatDate } from 'pub-bbx-utils';
import { openTabForEventView, openTabForTaskView } from '@src/util/business/openTab';
import { translateAction } from '@src/util/record'
import { t } from '@src/locales'
import { getRootWindow } from "@src/util/dom";
/* mixin */
import { ConnectorModuleConnectorRecordMixin, ConnectorModuleTriggerRecordMixin } from '@src/modules/connector'
/* enum */
import { ApproveActionLabelEnum } from '@model/enum/LabelEnum.ts'

function createAttachmentDom(h, attachments){
  return attachments && attachments.length > 0 
    ? <div class="base-timeline-attachment base-file__preview">
      {attachments.map(item => <base-file-item Source={attachments} file={item} key={item.id} readonly size="small"/>)}
    </div> 
    : ''
}

export default {
  name: 'event-progress-rate',
  mixins: [ConnectorModuleConnectorRecordMixin, ConnectorModuleTriggerRecordMixin,addressMixin],
  props: {
    event: {
      type: Object,
      default: ()=> {}
    },
    initData: {
      type: Object,
      default: ()=> {}
    },
    agendaEvent: {
      type: Boolean,
      default: false
    },
  },
  computed: {
    loginUser(){
      return this.initData?.loginUser || {};
    },
    editAll() {
      return this.initData.editAll || false;
    },
    eventState() {
      return this.event.state == 'closed' ? 'finished' :this.event.state
    },
    // 亚马芬灰度
    isHasAmerPrint() {
      const RootWindow = getRootWindow(window);
      return RootWindow?.grayAuth?.AMER_PRINT ?? false;
    },
  },
  watch: {
    event: {
      handler(newValue) {
        if(newValue) {
          this.getEventRecord()
        }
      }
    }
  },
  data() {
    return {
      recordLoading: false,
      recordList: [],
    }
  },
  mounted(){
    this.getEventRecord()
  },
  methods: {
    // 获取action的多语言label
    getActionLabel(key) {
      return ApproveActionLabelEnum[key] || key
    },
    /** 
     * 同时满足以下条件允许删除该记录
     * 1. 该记录没有被删除
     * 2. 工单编辑权限（CUSTOMER_EDIT）值为3 或者 是创建人
     * 3. 该工单没有被删除
     */
    allowDeleteRecord(item){
      let isDelete = item.content.isDelete == 'true'
      let user = this.loginUser
      let isCreator = item.userId == user.userId

      return !isDelete && (isCreator || this.editAll);
    },
    // 获取事件记录（最近更新）
    getEventRecord() {
      this.recordLoading = true;
      const recordParams = {
        eventId: this.event.id,
        pageNum: 1,
        pageSize: 99999,
      }
      EventApi.getEventRecord(recordParams)
        .then(res=> {
          const [first] = this.recordList = res || [];
          if(first){
            // 创建跳转过来的 
            // 因为直接修改的href 
            // tabid 还是创建时的createEvent； 
            // EventList.vue  toCreateEvent()  中 定义的tabid
            // 修改frame 标题
            platform.setTabTitle({
              id: 'createEvent', 
              title: this.$t('common.base.event') + first.eventNo
            })
          }
        })
        .finally(()=> {this.recordLoading = true;})
    },

    /** 根据记录的action渲染对应的内容，支持jsx和render函数 */
    renderRecord(h, record) {
      let { action, userName, content, attachments, eventNo, staffId, userId } = record;
      
      if (action == '开始' || action == '完成') return this.renderStartOrFinishDom(record)
      if (action == '添加') return this.renderActionRemarkDom(h, record)
      if (action == '留言') return this.renderLeaveMessageDom(h, record)
      if (action == '提交') return this.renderSubmitDom(record)
      if (action === '修改') return this.renderEventEditDom(record)
      if (action == '新建') return this.renderEventCreatetDom(record)
      if (action === 'API新建') return this.renderApiNewCreateDom(record)
      if (action === '转为工单') return this.renderConvertTaskDom(record)
      if (action === '转为工单并完成') return this.renderConvertTaskDoneDom(record)
      if (action === '工单完成事件同步完成') return this.renderConvertTaskDoneAutoEventDoneDom(record)
      if (action === '评价') return content.degree ? this.renderEvaluteDom(record) : this.renderModifyEvaluteDom(record)
      if (action === '自动分配') return this.renderEventAutoAllotDom(record)
      if (action === '修改协同人') return this.renderEventEditSynergysDom(record)
      if (action == '分配' || action == '转派') return this.renderAllotDom(record)
      if (action === '审批') return this.renderEventApproveDom(record)
      if (action === '卡片') return this.renderEventCardDom(record)
      if (action === '超时') return this.renderEventTimeoutDom(record)
      if (action === '已超时') return this.renderEventTimeoutdDom(record)
      if (action === '转交') return this.renderEventTransferDom(record)
      if (action === '暂停') return this.renderEventPausedDom(record)
      if (action === '取消') return this.renderEventOffDom(record)
      if (action == '电话日志') return this.renderPhoneLogDom(record)
      if (action == 'API创建') return this.renderApiCreateDom(record)
      if (action == '审核转交') return this.renderEventApproveTransferDom(record)
      if (action === '修改评价') return this.renderModifyEvaluteDom(record)
      if (action === '附件') return this.renderAttachment(record)
      if (action === '连接器to') return this.renderConnectorTo(record)
      if (action === '连接器from') return this.renderConnectorFrom(record)
      if (action === '触发器to') return this.renderTriggerTo(record)
      if (action === '触发器from') return this.renderTriggerFrom(record)
      if (action === '进行了新增打标') return this.renderIntelligentTagsInfoNew(record)
      if (action === '进行了覆盖打标') return this.renderIntelligentTagsInfoTwo(record)
      if (action === '取消了标签') return this.renderIntelligentTagsInfoThree(record)
      
      const { isGoBack, synergy, updateType, updateContent } = content;
      const isShowUpdateContent = updateType === 'eRecord' || updateType === 'tRecord';
      // 根据后端返回action做翻译枚举
      const actionDes = action === '回访' ? this.$t('task.detail.components.finishReview') : translateAction(action)
      return [
        <i18n path="common.record.event.normal" tag="h5">
          <span place="operator">{this.renderUserName(userName, staffId, userId)}</span>
          <span place="action">{actionDes}</span>
          <span place="eventNo">{eventNo}</span>
        </i18n>,
        synergy && <div>{t('common.label.synergiesUser')}：{ this.renderSynergyName(synergy, content.synergyStaffId) }</div>,
        isShowUpdateContent && <div>{ updateContent }</div>,
        isGoBack !== undefined && <div> {isGoBack !== null ? isGoBack == '1' ? t('common.record.event.partBackUp.back') : t('common.record.event.partBackUp.notBack') : ''} </div>,
        createAttachmentDom(h, attachments)
      ];
    },
    renderUserName(userName, staffId, userId){
      return (
        <recordUserName userName={userName} staffId={staffId} userId={userId} />
      )
    },
    renderSynergyName(synergy, synergyStaffId){
      if(isOpenData && synergy != '自动审批' && synergyStaffId) {
        const sids = synergyStaffId.split(',')
        if(sids.length) return sids.map(item=><open-data key={item} type="userName" openid={item}></open-data>) 
      }
      return synergy
    },
    changeEventProcessState(state) {
      // this.taskState = state;
    },
    renderIntelligentTagsInfoNew(record = {}) {
      let {action, userName, showInOwn, content, attachments, primaryName, staffId, userId} = record;
      return (
          <h5>{this.renderUserName(userName, staffId, userId)}
            {/* TODO 为后面国际化做处理 */}
            { `进行了新增打标${content?.labelId ? content?.labelName : ''}` }
          </h5>
        )
    },
    renderIntelligentTagsInfoTwo(record = {}) {
      let {action, userName, showInOwn, content, attachments, primaryName, staffId, userId} = record;
      return (
          <h5>{this.renderUserName(userName, staffId, userId)}
            {/* TODO 为后面国际化做处理 */}
            { `进行了覆盖打标${content?.labelId ? content?.labelName : ''}` }
          </h5>
        )
    },
    renderIntelligentTagsInfoThree(record = {}) {
      let {action, userName, showInOwn, content, attachments, primaryName, staffId, userId} = record;
      return (
          <h5>{this.renderUserName(userName, staffId, userId)}
            {/* TODO 为后面国际化做处理 */}
            { `取消了标签${content?.labelId ? content?.labelName : ''}` }
          </h5>
        )
    },


    /** 
     * @description 渲染地址dom
     * @param {Object} record 记录数据
     * @param {Boolean} isFinished 是否是完成状态 特殊处理
    */
    renderAddressRecordDom(record = {}, isFinished = false) {
      let address = trimAll(record.address || '');
      let { latitude, longitude } = record;
      let isShowAddress = address && latitude && longitude;
      let openMapFunc = isFinished ? this.openMapForFinished : this.openMap
      let icon = isShowAddress ? <i class="iconfont icon-address"></i> : '';
      let addressDom = <span onClick={e => {openMapFunc(record)}}>{icon}{address}</span>;
      return { address, icon, isShowAddress, addressDom }
    },

    /* 渲染修改事件协同人 */
    renderEventEditSynergysDom(record = {}) {
      let { userName, content, eventNo, staffId, userId } = record;
      return[
        <i18n path="common.record.event.eventEditSynergys" tag="div">
          <span place="operator">{this.renderUserName(userName, staffId, userId)}</span>
          <span place="eventNo">{eventNo}</span>
        </i18n>,
        content.synergy != undefined ? <div>{t('common.label.synergiesUser')}：{this.renderSynergyName(content.synergy, content.synergyStaffId)}</div> : ''
      ]
    },

    /* 渲染指派转派 */
    renderAllotDom(record = {}) {
      let { action, userName, content, eventNo, executorName, staffId, executorStaffId, userId, executorId } = record;
      const { agentName = '' } = content
      return (
        <div class="task-record-allot">
          {
            agentName ? <i18n  path="common.record.event.eventSmartAutoAllot" tag="h5">
              <span place="eventNo">{eventNo}</span>
              <span place="agentName">{ agentName }</span>
              <span place="operator">{this.renderUserName(executorName, executorStaffId, executorId)}</span>
            </i18n> :
                <i18n path={`common.record.event.allot['${action}']`} tag="h5">
            <span place="operator">{this.renderUserName(userName, staffId, userId)}</span>
            <span place="eventNo">{eventNo}</span>
            <span place="recipient">{this.renderUserName(executorName, executorStaffId, executorId)}</span>
          </i18n>}
          {(content && Object.keys(content).length > 0) && (
            <div>
              {content.synergy && <div>{t('common.label.synergiesUser')}：{this.renderSynergyName(content.synergy, content.synergyStaffId)}</div>}
              {content.updateType == 'tRecord' && (
                (action == '转派' && content.updateContent) ? <div>{t('event.transferDesc')}：<biz-comment-html html={content.updateContent}></biz-comment-html></div> : <div><biz-comment-html html={content.updateContent}></biz-comment-html></div>
              )}
              {content.reAllotCustomReason && <div>{t('task.list.displayName.transferReason')}：{ content.reAllotCustomReason } </div>}
              {content.reAllotContent && <div>{t('task.detail.components.transferDesc')} ：{ content.reAllotContent } </div>}
            </div>
          )}
        </div>
      )
    },
    /* 渲染添加备注 */
    renderActionRemarkDom(h, record = {}) {
      let { userName, showInOwn, toCustomer, cusNotice, content, attachments, eventNo, staffId, userId } = record;
      //是否有updateUser属性
      const hasUpdateUser = content.hasOwnProperty('updateUser') && this.isHasAmerPrint
      return [
        <h5 class="main-info">
          <i18n path="common.record.task.somebodyAddRemark" tag="span">
            <span place="operator">{this.renderUserName(userName, staffId, userId)}</span>
          </i18n>
          #{eventNo}
          {!!showInOwn && (
            <span class="private"> <i class="iconfont icon-account1"></i> {t('task.record.onlyVisibleToYourself')} </span>
          )}。
          {!!toCustomer && <span class=""> ({t('task.record.visibleToCustomer')}) </span> }
          {!!cusNotice && <span class=""> ({t('task.record.notifyCustomerBySms')}) </span> }
          {
            this.allowDeleteRecord(record) 
            && (
              <button type='button' class="btn-text base-timeline-delete" onClick={e => this.deleteRemark(record)}>
                <i class="iconfont icon-shanchu"></i>{t('common.base.delete')}
              </button>
            )
          }
        </h5>,
        content.isDelete == 'true'
          ? <i18n path="common.record.task.somebodyDeleteRemark" tag="p" class="text-danger">
              <span place="operator">{this.renderUserName(content.deleteUserName, content.deleteUserStaffId)}</span>
              <span place="time">{formatDate(content.deleteTime)}</span>
            </i18n>
          // <p class="text-danger">{ this.renderUserName(content.deleteUserName, content.deleteUserStaffId) }于{content.deleteTime}删除了该备注。</p> 
          : [
              hasUpdateUser && <p class="pre-line secondary-info"><biz-comment-html html={content.updateUser}></biz-comment-html></p>,
              <p className="pre-line secondary-info">{hasUpdateUser && content.updateContent ?
                  <form-rich-text-view value={content.updateContent}></form-rich-text-view> :
                  <biz-comment-html html={content.updateContent}></biz-comment-html>}</p>  ,
              createAttachmentDom(h, attachments)]
      ]
    },
    /* 渲染api新建 */
    renderApiNewCreateDom(record = {}) {
      let { userName, content, eventNo, executorName, staffId, executorStaffId, userId } = record;
      return [
        <h5>{this.renderUserName(userName, staffId, userId)}{t('common.record.event.text5', {clientName:content.clientName, eventNo})}</h5>,
        executorName != undefined ? <div>{`${t('common.record.event.text6')}：${this.renderUserName(executorName, executorStaffId)}`}</div> : ''
      ]
    },

    /* 渲染api创建 */
    renderApiCreateDom(record = {}) {
      let { userName, eventNo, staffId, userId } = record;
      return [
        <i18n path="common.record.event.addByApi" tag="h5">
          <span place="operator">{this.renderUserName(userName, staffId, userId)}</span>
          <span place="apiName"></span>
          <span place="eventNo">{eventNo}</span>
        </i18n>
        // <h5>应用{this.renderUserName(userName, staffId, userId)}{` 创建了事件 # ${eventNo}`}</h5>
      ]
    },

    /* 转为工单 */
    renderConvertTaskDom(record = {}) {
      let { userName, content, eventNo, staffId, userId } = record;
      return [
        <i18n path="common.record.event.text8" tag="h5">
          <span place="userName">{this.renderUserName(userName, staffId, userId)}</span>
          <span place="eventNo">{eventNo}</span>
          <span place="taskNo" class="link-text" onClick={task => openTabForTaskView(content.taskId)}>#{content.taskNo} </span>
        </i18n>
      ]
    },
    /* 转为工单完成 */
    renderConvertTaskDoneDom(record = {}) {
      let { userName, content, eventNo, staffId, userId } = record;
      return [
        <i18n path="common.record.event.text9" tag="h5">
          <span place="userName">{this.renderUserName(userName, staffId, userId)}</span>
          <span place="eventNo">{eventNo}</span>
          <span place="taskNo" class="link-text" onClick={task => openTabForTaskView(content.taskId)}>#{content.taskNo} </span>
        </i18n>
      ]
    },
    /* 工单完成事件同步完成 */
    renderConvertTaskDoneAutoEventDoneDom(record = {}) {
      let { userName, content, eventNo, staffId, userId } = record;
      return [
        <i18n path="common.record.event.text13" tag="h5">
          <span place="userName">{this.renderUserName(userName, staffId, userId)}</span>
          <span place="eventNo">{eventNo}</span>
          <span place="taskNo" class="link-text mr_0" onClick={task => openTabForTaskView(content.taskId)}>#{content.taskNo} </span>
        </i18n>
      ]
    },

    /* 评价 */
    renderEvaluteDom(record = {}) {
      let { content } = record;
      return [
        <div> { this.renderBaseEventAction(record) } </div>,
        <div>{ content.degree != undefined ? `${t('common.label.evaluation')}：${content.degree}` : '' }</div>,
        <div>{ content.suggestion != undefined ? `${t('common.base.content')}：${content.suggestion}` : '' }</div>
      ]
    },

    /* 修改评价 */
    renderModifyEvaluteDom(record = {}) {
      let { userName, content, eventNo, staffId, action, customerName = '', userId} = record;

      let data = {};

      for(let name in content){
        let value = content[name] || '';
        let arr = value && value.split('[ + + ]');

        if(name == '满意度') {
          data.degree = {
            name,
            oldValue: arr[0],
            newValue: arr[1]
          }
        }else if(name == '服务标签'){
          data.tag = {
            name,
            oldValue: arr[0],
            newValue: arr[1]
          }
        }else{
          if(!data.star) data.star = [];
          if(arr && arr.length) {
            data.star.push({
              name,
              oldValue: arr[0],
              newValue: arr[1]
            })
          }

        }
      }

      let tempArr = [];
      if(data.degree) tempArr.push(data.degree)
      if(data.star) tempArr = tempArr.concat(data.star)
      if(data.tag) tempArr.push(data.tag)

      const history = tempArr.map(function(o){
        return <i18n path="common.record.task.taskReviewModifield" tag="p">
            <strong place="data1">{ o.name }</strong>
            <strong place="value1">{ o.oldValue }</strong>
            <strong place="value2">{ o.newValue }</strong>
          </i18n>
      });

      return [
        <div> { action == '评价' ? customerName : this.renderUserName(userName, staffId, userId)} {t('common.record.event.text11')} # {eventNo} </div>,
        <div>{ history }</div>
      ]
    },

    /* 渲染事件自动分配dom */
    renderEventAutoAllotDom(record = {}) {
      let { executorName = '', content, eventNo, executorStaffId, executorId } = record;
      return <i18n path="common.record.event.text12" tag="div">
        <span place="eventNo">{ eventNo }</span>
        <span place="ruleName">{content.ruleName}</span>
        <span place="executorName">{ this.renderUserName(executorName, executorStaffId, executorId) }</span>
      </i18n>
    },
    
    /* 渲染基础的事件动作 */
    renderBaseEventAction({ action, userName, eventNo, staffId, userId, customerName }) {
      // 根据后端返回action做翻译枚举
      const actionDes = translateAction(action)
      return <i18n path="common.record.event.normal" tag="h5">
        <strong place="operator">{userId ? this.renderUserName(userName, staffId, userId) : customerName}</strong>
        <span place="action">{actionDes}</span>
        <span place="eventNo">{eventNo}</span>
      </i18n>
    },
    /* 渲染留言 */
    renderLeaveMessageDom(h, record = {}) {
      let { userId, userName, content, attachments, staffId, customerName } = record;
      return [
        <i18n path="common.record.task.somebodyAddRemark2" tag="h5" class="main-info">
          <strong place="operator">{userId ? this.renderUserName(userName, staffId, userId) : customerName}</strong>
        </i18n>,
        content.isDelete == 'true'
          ? <i18n path="common.record.task.somebodyDeleteRemark2" tag="p" class="text-danger">
              <span place="operator">{this.renderUserName(content.deleteUserName, content.deleteUserStaffId)}</span>
              <span place="time">{ formatDate(content.deleteTime) }</span>
            </i18n>
          : [<p class="pre-line secondary-info"><biz-comment-html html={content.updateContent}></biz-comment-html></p>, createAttachmentDom(h, attachments)]
      ]
    },

    /* 渲染电话日志 */
    renderPhoneLogDom(record = {}) {
      let { userName, content, staffId } = record;
      return <i18n path="common.record.task.phoneLog" tag="h5">
        <span place="operator">{this.renderUserName(userName, staffId)}</span>
        <strong place="targetName">{ content.targetName }</strong>
      </i18n>
    },
    /* 渲染开始和完成 */
    renderStartOrFinishDom(record = {}) {
      let { content, address, action } = record;
      let isFinished = action == '完成'
      let addressData = this.renderAddressRecordDom(record, isFinished);
      let isPositionException = content.isPositionException == 'true';
      let className = isPositionException ? 'task-position-exception' : '';
      let startOrFinshRecord = [
        // 基础事件操作日志
        <div> { this.renderBaseEventAction(record) } </div>,
        // 地址操作
        <div>
          { address === 'PC端' ? <span>{t('task.record.operateFromPcEnd')}</span> : <span class="form-view-row-content-address">{ addressData.addressDom }</span> }
        </div>,
        // 是否地理位置异常
        <div>
          { isPositionException && <span>{t('task.detail.components.distanceToCustomer')}：{ content.distance ? `${content.distance} km` : t('task.record.positionException') }</span> }
        </div>
      ];
      
      return (
        <div class={className}>
          { startOrFinshRecord }
        </div>
      )
    },
    getApprNamesDom(content){
      if(isOpenData && content.apprNames && content.apprNames != '自动审批' && content.apprStaffIds && content.apprStaffIds.length) {
        let apprStaffIds = content.apprStaffIds.split(',') || []
        return <div>审批人：{ apprStaffIds.map(item=><open-data type="userName" openid={item}></open-data>) }</div> 
      }  
      return <div>审批人：{ content.apprNames }</div>
    },
    /* 渲染事件审批dom */
    renderEventApproveDom(record = {}) {
      let { content, eventNo, address, attachments, userName, longitude, latitude, staffId, userId } = record;
      let { state, level } = content;
      // 事件审批状态
      let taskState = {
        unApproved: state == 'unapproved',
        success: state == 'success',
        fail: state == 'fail',
        offed: state == 'offed',
      }
      // 工单审批等级
      let levelName = level >= 1 ? `${[t('common.base.level0'), t('common.base.level1'),t('common.base.level2'),t('common.base.level3'),t('common.base.level4'),t('common.base.level5'),t('common.base.level6'),t('common.base.level7'),t('common.base.level8'), t('common.base.level9')][level] }` : '';;

      if (content?.vipApprove == 1 && content?.approveNode) {
        levelName = content.approveNode || ''
      }

      // 是否显示地址
      let isShowAddress = address && address != 'PC端';
      // 地址数据
      let addressData = this.renderAddressRecordDom(record);
      // 地址dom
      let addressDom = isShowAddress 
        ? longitude && latitude 
          ? <div class="form-view-row-content-address">{ addressData.addressDom }</div> 
          : <div>{ address }</div>
        : '';
      // 未审批dom
      let unApprovedDom = [
        <i18n tag="div" path={ address == 'PC端' ? 'common.record.event.eventApproveByPc' : 'common.record.event.eventApprove' }>
          <span place="operator">{this.renderUserName(userName, staffId, userId)}</span>
          <span place="taskNo">{eventNo}</span>
          <span place="action">{content.action}</span>
        </i18n>,
        content.remark && <div>{t('common.base.remark')}：<biz-comment-html html={content.remark}></biz-comment-html></div>,
        content.apprNames && <div>{content.apprNames_2 ? t('common.base.approveUserLevel1') : t('common.base.approveUser')}：{ this.renderSynergyName(content.apprNames, content.apprStaffIds) }</div>,
        content.apprNames_2 && <div>{t('common.base.approveUserLevel2')}：{ this.renderSynergyName(content.apprNames_2, content.apprStaffIds_2) }</div>,
        addressDom
      ];

      // 是否展示审批签名
      const sign = content?.sign ? [content.sign] : [];
      const addApproveSignatures = <div class="form-approve-signatures">
        <span>{t('common.base.approveSign')}:</span>
        <el-image style="width: 100px; height: 100px" src={content?.sign} previewSrcList={sign}/>
      </div>
      
      // 审批成功dom
      let isAutoApprove = userName == '自动审批';
      let autoApproveDom = <h5>{t('common.record.event.autoApprove', {eventNo, action:content.action, approveUser:t('common.base.approveUserByLevel', {data1:levelName})})}</h5>;
      let notAutoApproveDom = [
        <i18n path="common.record.event.paasApproveBysomebody" tag="div">
          <span place="operator">{this.renderUserName(userName, staffId, userId)}</span>
          <span place="eventNo">{eventNo}</span>
          <span place="action">{ content.action }</span>
          <span place="approve">{t('common.base.approveByLevel',{data1:levelName})}</span>
        </i18n>,
        content.remark && <div>{t('common.base.remark')}：<biz-comment-html html={ content.remark }></biz-comment-html></div>, createAttachmentDom(h, attachments),
        content.sign && addApproveSignatures
      ]
      let successDom = isAutoApprove ? autoApproveDom : notAutoApproveDom;
      // 审批失败dom
      let failDom = [
        <i18n path="common.record.event.refuseApproveBysomebody" tag="div">
          <span place="operator">{this.renderUserName(userName, staffId, userId)}</span>
          <span place="eventNo">{eventNo}</span>
          <span place="action">{ content.action }</span>
          <span place="approve">{t('common.base.approveByLevel',{data1:levelName})}</span>
        </i18n>,
        content.remark && <div>{t('common.base.remark')}：<biz-comment-html html={ content.remark }></biz-comment-html></div>, createAttachmentDom(h, attachments),
        content.sign && addApproveSignatures
      ];
      // 审批撤回dom
      let offedDom = [
        <i18n path="common.record.event.withdrawApproveBysomebody" tag="div">
          <span place="operator">{this.renderUserName(userName, staffId, userId)}</span>
          <span place="eventNo">{eventNo}</span>
          <span place="action">{ content.action }</span>
          <span place="approve">{t('common.base.approveByLevel',{data1:levelName})}</span>
        </i18n>
      ];
      
      return [
        <div>
          { taskState.unApproved ? unApprovedDom : '' }
          { taskState.success ? successDom : '' }
          { taskState.fail ? failDom : '' }
          { taskState.offed ? offedDom : '' }
        </div>
      ]
      
    },
    
    /* 渲染事件附加组件卡片 */
    renderEventCardDom(record = {}) {
      let { userName, content, staffId, userId } = record;
      return <i18n path={`common.record.task.taskCard['${content.action}']`} tag="div">
        <span place="operator">{this.renderUserName(userName, staffId, userId)}</span>
        <span place="cardName">{ content.cardName }</span>
      </i18n>
    },
    /* 渲染事件创建dom */
    renderEventCreatetDom(record = {}) {
      let { executorName, executorStaffId, executorId } = record
      return [
        this.renderBaseEventAction(record),
        executorName && <div>{t('common.record.event.text6')}：{this.renderUserName(executorName, executorStaffId, executorId)}</div>
      ]
    },
    /* 渲染事件修改dom */
    renderEventEditDom(record = {}) {
      let { content } = record;
      return [
        this.renderBaseEventAction(record),
        content.updateFields && <div>{t('common.base.modifyField')}：{ content.updateFields } </div>,
        content.planTime && <div>{t('common.base.planTime')}：{ formatDate(content.planTime) } </div>
      ]
    },

    /* 渲染事件提交dom */
    renderSubmitDom(record = {}) {
      let { customerName, eventNo, action } = record;
      return <i18n path="common.record.event.submit" tag="h5">
        <strong place="operator">{customerName}</strong>
        <span place="eventNo">{eventNo}</span>
      </i18n>
    },

    /* 渲染事件取消dom */
    renderEventOffDom(record = {}) {
      let { content } = record;
      return [
        this.renderBaseEventAction(record),
        <div><biz-comment-html html={content.updateContent}></biz-comment-html></div>
      ]
    },

    /* 渲染事件暂停dom */
    renderEventPausedDom(record = {}) {
      let { action, content, eventNo, userName, staffId, userId } = record;
      let reasonDom = content.reason && <div>{t('common.base.pauseReason')}{ content.reason }</div>
      return (
        <div class="task-record-fail">
          <i18n tag="div" path="common.record.event['暂停']">
            <span place="operator">{this.renderUserName(userName, staffId, userId)}</span>
            <span place="eventNo">{eventNo}</span>
          </i18n>
          { reasonDom }
        </div>
      )
    },
    
    /* 渲染事件超时dom */
    renderEventTimeoutDom(record = {}) {
      let { content } = record;
      return [
        <i18n path="common.record.task.taskTimeOut" tag="div">
          <span place="operator">{ this.renderSynergyName(content.receivers, content.receiversStaffId) }</span>
        </i18n>,
        content.flow && <div>{t('task.record.processNode')}： { this.getActionLabel(content.flow) }</div>,
        <div>{t('task.record.processTimeout')}：{ formatDate(content.overTime) }</div>
      ]
    },
    /* 渲染事件已超时dom */
    renderEventTimeoutdDom(record = {}) {
      let { content, eventNo } = record;
      return <div class="task-record-fail">{t('common.record.event.hadTimeOut', {eventNo, flow: this.getActionLabel(content.flow)})}</div>
    },
    /* 渲染事件转交dom */
    renderEventTransferDom(record = {}) {
      let { executorName, userName, executorId, content, eventNo, staffId, executorStaffId, userId } = record;
      return [
        <i18n path="common.record.event.transfer" tag="h5">
          <span place="operator">{this.renderUserName(userName, staffId, userId)}</span>
          <span place="eventNo">{eventNo}</span>
        </i18n>,
        executorId && <div>{t('task.record.modifyExecutorTo')}：{this.renderUserName(executorName, executorStaffId, executorId)}</div>,
        content.synergy && <div>{t('task.record.modifySynergiesTo')}：{ this.renderSynergyName(content.synergy, content.synergyStaffId) }</div>
      ]
    },
    /* 渲染事件审批转交dom */
    renderEventApproveTransferDom(record = {}) {
      let { operatorName, handoverName, operatorStaffId, handoverStaffId } = record?.content;
      return <i18n path="common.record.event.transferApprove" tag="h5">
          <strong place="operator">{ this.renderUserName(operatorName, operatorStaffId) }</strong>
          <strong place="recipient">{ this.renderUserName(handoverName, handoverStaffId) }</strong>
        </i18n>
    },

    /**
     * 附件记录
     */
    renderAttachment(record = {}){
      let { userName, eventNo } = record;
      return (<i18n path="common.record.event.attachment" tag="div"><strong place="operator">{userName}</strong><span place="eventNo">{eventNo}</span></i18n>)
    },

    /** 
     * @description 删除备注
    */
    async deleteRemark(record) {
      try {
        if (!await this.$platform.confirm(this.$t('common.base.tip.deleteRemarkTip'))) return;
        const result = await EventApi.eventDeleteRecord({ id: record.id });
        
        if (result.status == 0) {
          this.initializeRecord(); 
        } else {
          this.$notify({
            title: this.$t('common.base.error'),
            message: result.message,
            type: 'error'
          });
        }
        
      } catch (e) {
        console.warn('task deleteRemark -> error', e);
      }
    },


    /** 
     * @description 初始化信息动态
    */
    async initializeRecord() {
      this.getEventRecord()
    },

    //  处理流程信息 响应用时&工作用时&事件用时
    formatProgressTime(timeStr) {
      if(timeStr && timeStr !== null) {
        return timeStr
      } 
      return '--'
      
    }
  }
}
</script>

<style lang="scss" scoped>
.form-view-row-content-address{
  cursor: pointer;
}
div.form-view-row-content-address>span{
  display: block;
}
.form-view-row-content-address:hover{
    color:$color-primary-light-6
 }
.title {
  font-weight: 600;
}
.progress-info {
  margin-bottom: 10px;
  border-bottom: 1px solid rgb(244, 244, 244);
}
.progress-content {
  padding: 15px 10px;
  .cost-time {
    padding-left: 12px;
    margin-top: 15px;
    font-size: 12px;
    >span {
      margin-right: 10px;
    }
  }
}
.form-approve-signatures {
  display:grid;
  grid-template-columns: 35px 1fr;
}
</style>