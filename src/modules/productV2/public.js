import i18n from '@src/locales';
const $t = i18n.t.bind(i18n);
import { getTimestamp } from 'pub-bbx-utils';

const catalogFieldFix = [
  {
    displayName: $t('common.base.productMenu'),
    fieldName: 'pathName',
    formType: 'related_catalog',
    export: true,
    isSearch: true,
    show: true,
    orderId: -1,
    isSystem: 1,
    isDiy: true,
    tableName: 'catalog',
  },
  // {
  //   displayName: '视频',
  //   fieldName: 'productVideo',
  //   formType: 'attachment',
  //   export: false,
  //   show: true,
  //   orderId: -0.9,
  //   isSystem: 1,
  //   tableName:'catalog',
  // },
  // {
  //   displayName: '图片',
  //   fieldName: 'productPic',
  //   formType: 'attachment',
  //   export: false,
  //   show: true,
  //   orderId: -0.8,
  //   isSystem: 1,
  //   tableName:'catalog',
  // },
  // {
  //   displayName: '缩略图',
  //   fieldName: 'thumbnail',
  //   formType: 'attachment',
  //   export: false,
  //   show: true,
  //   orderId: -0.7,
  //   isSystem: 1,
  //   tableName:'catalog',
  // },
  {
    displayName: $t('product.relatedProductCount'),
    fieldName: 'productNum',
    formType: 'number',
    export: true,
    show: true,
    isSystem: 1,
    tableName: 'catalog',
  },
  {
    displayName: $t('common.base.column.createPerson'),
    fieldName: 'createUser',
    formType: 'user',
    returnData: 'name', // 参数保留等待表单优化支持模糊查询
    noClearable: false,
    isNull: 1,
    isSystem: 1,
    orderId: -3.5,
    placeHolder: $t('product.placeholder.createUser'),
    export: true,
    show: true,
    tableName: 'catalog',
  },
  {
    displayName: $t('common.base.column.createTime'),
    fieldName: 'createTime',
    formType: 'datetime',
    export: true,
    show: true,
    isSystem: 1,
    tableName: 'catalog',
  },
];

// 没用到，不做多语提取
const catalogFieldFixForProduct = [
  {
    displayName: '产品视频',
    fieldName: 'productVideo',
    formType: 'attachment',
    export: false,
    show: true,
    orderId: -0.9,
    isSystem: 1,
    tableName: 'catalog',
  },
  {
    displayName: '产品图片',
    fieldName: 'productPic',
    formType: 'attachment',
    export: false,
    show: true,
    orderId: -0.8,
    isSystem: 1,
    tableName: 'catalog',
  },
  {
    displayName: '产品数量',
    fieldName: 'productNum',
    formType: 'text',
    export: true,
    show: true,
    isSystem: 1,
    tableName: 'catalog',
  },
];

const productFieldFix = [
  // {
  //   displayName: "产品类型",
  //   fieldName: "catalogId",
  //   formType: "related_catalog",
  //   export: 1,
  //   isSystem: 1,
  //   tableName:"product",
  // },
  {
    displayName: $t('common.base.lastUpdate'),
    fieldName: 'updateTime',
    formType: 'datetime',
    export: false,
    isSystem: 1,
    tableName: 'product',
  },
  {
    displayName: $t('common.base.productTemplate'),
    fieldName: 'productTemplate',
    formType: 'text',
    export: false,
    isSystem: 1,
    tableName: 'product',
  },
  {
    displayName: $t('common.base.serviceDepartment'),
    fieldName: 'tags',
    export: true,
    isSystem: 1,
    exportAlias: 'customerTags',
    tableName: 'product',
  },
  {
    displayName: $t('product.type.remindCount'),
    fieldName: 'remindCount',
    export: false,
    isSystem: 0,
    tableName: 'product',
  },
  {
    displayName: $t('common.base.column.createTime'),
    fieldName: 'createTime',
    formType: 'datetime',
    export: true,
    isSystem: 1,
    tableName: 'product',
  },
  {
    displayName: i18n.t('common.base.productStatus'),
    fieldName: 'productStatus',
    formType: 'select',
    export: true,
    isSystem: 1,
    operator: 'between',
    tableName: 'product',
    setting: {
      isMulti: true,
      dataSource: [
        {
          text: i18n.t('common.base.enableState[0]'),
          value: true,
        },
        {
          text: i18n.t('common.base.enableState[1]'),
          value: false,
        },
      ],
    },
  },
  {
    displayName: $t('common.base.column.createPerson'),
    fieldName: 'createUser',
    formType: 'user',
    returnData: 'name', // 参数保留等待表单优化支持模糊查询
    noClearable: false,
    isNull: 1,
    isSystem: 1,
    orderId: -3.5,
    placeHolder: $t('product.placeholder.createUser'),
    export: true,
    show: true,
    tableName: 'product',
  },
];

export const useStateExtendedStatus = () => {
  const extendedStatusDes = {
    in: '延保中',
    out: '非延保',
  };
  const getExtendedStatusDes = (info)=>{
    let startTime = info.extendedStartTime;
    let endTime = info.extendedEndTime;
    if(!startTime || !endTime){
      return extendedStatusDes.out
    }
    let now = new Date().getTime();
    if(typeof startTime === 'string'){
      startTime = getTimestamp(startTime)
    }
    if(typeof endTime === 'string'){
      endTime = getTimestamp(endTime)
    }
    if(now > startTime && now < endTime){
      return extendedStatusDes.in
    }
    if(!startTime || !endTime){
      return ''
    }
    return extendedStatusDes.out
  }
  return {
    extendedStatusDes,
    getExtendedStatusDes,
  };
};

export { catalogFieldFix, productFieldFix, catalogFieldFixForProduct };
