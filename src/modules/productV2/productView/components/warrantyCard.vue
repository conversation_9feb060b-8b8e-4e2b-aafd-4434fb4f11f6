<template>
  <div class="customer-remind-table-container">
    <JustTable :columns="columns" :fetch-data="fetchData">
      <template #fieldName_activationUser="{ data }">
        <BaseUserTag v-if="data.activationUserType === 'SYSTEM_USER'" :props-data="{ id: data.activationUserInfo.staffId, name: data.activationUserInfo.displayName }" />
        <div v-if="data.activationUserType === 'DOOR_USER'">{{ data.activationUser }}</div>
      </template>
    </JustTable>
  </div>
</template>

<script>
import { formatDate, useFormTimezone } from 'pub-bbx-utils';
import { getWarrantyCardInfo } from '@src/api/ProductV2Api';
import JustTable from '@src/component/compomentV2/JustTable/index.vue';
import BaseUserTag from '@src/component/compomentV2/baseUserTag/index.vue';
export default {
  name: 'warranty-card',
  props: {
    shareData: {
      type: Object,
      default: () => ({}),
    },
    extraAttrs: {
      type: Object,
      default: () => ({}),
    },
  },
  components: {
    JustTable,
    BaseUserTag,
  },
  data() {
    return {
      ...useFormTimezone(),
      pending: {},
      remindList: [],
      columns: this.buildColumns(),
      loading: false,
      searchModel: {
        pageSize: 10,
        pageNum: 1,
        totalItems: 0,
        orderDetail: {},
      },
    };
  },
  computed: {
    productId() {
      return this.shareData.product.id;
    },
  },
  mounted() {},
  beforeDestroy() {},
  methods: {
    fetchData(searchParams) {
      const params = {
        productId: this.productId,
        pageSize: searchParams.pageSize,
        pageNum: searchParams.pageNum,
      };

      return getWarrantyCardInfo(params)
        .then(res => {
          if (res.code) {
            return {
              code: 0,
              data: res.data,
            };
          }
        })
        .catch(e => {
          console.error('e', e);
        });
    },
    buildColumns() {
      return [
        {
          displayName: '延保卡号',
          fieldName: 'cardNumber',
          formType: 'text',
          show: true,
          width: '240px',
          tooltip: true,
        },
        {
          displayName: '订单号',
          fieldName: 'bizNo',
          formType: 'text',
          show: true,
          width: '240px',
          tooltip: true,
        },
        {
          displayName: '延保开始日期',
          fieldName: 'extendedStartTime',
          formType: 'date',
          show: true,
          tooltip: true,
          width: '240px',
          setting:{dateType:'YYYY-MM-DD'},
        },
        {
          displayName: '延保结束日期',
          fieldName: 'extendedEndTime',
          formType: 'date',
          show: true,
          tooltip: false,
          width: '240px',
          setting:{dateType:'YYYY-MM-DD'},
        },
        {
          displayName: '原质保结束日期',
          fieldName: 'oldQualityEndTime',
          formType: 'date',
          show: true,
          tooltip: false,
          width: '240px',
          setting:{dateType:'YYYY-MM-DD'},
        },
        {
          displayName: '状态',
          fieldName: 'extendedStatus',
          formType: 'text',
          show: true,
          tooltip: false,
          width: '240px',
        },
        {
          displayName: '操作人',
          fieldName: 'activationUser',
          formType: 'user',
          show: true,
          tooltip: false,
          width: '240px',
        },
        {
          displayName: '操作时间',
          fieldName: 'activationTime',
          formType: 'date',
          show: true,
          tooltip: false,
          width: '240px',
        },
      ];
    },
  },
};
</script>

<style lang="scss">
.customer-remind-table-container {
  padding: 16px;
  .edit-btn {
    @include fontColor();
  }

  .delete-remind-btn {
    color: $color-danger;
  }

  .delete-remind-btn.is-disabled,
  .delete-remind-btn.is-disabled:hover,
  .el-button.delete-remind-btn:focus {
    color: #c0c4cc;
    cursor: not-allowed;
    background-image: none;
  }
}
</style>
