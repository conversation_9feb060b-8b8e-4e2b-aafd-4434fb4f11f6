<template>
  <base-modal
    :title="title"
    :show.sync="visible"
    width="500px"
  >
    <el-form label-position="top" class="link-dialog-form">
      <el-form-item :label="`${$t('common.placeholder.select')}：`">
        <el-select
          v-if="dataType==='part'"
          v-model="value"
          multiple
          clearable
          collapse-tags
          :placeholder="$t('common.placeholder.select')"
          remote
          filterable
          @focus="focus"
          :remote-method="search"
          :loading="selectLoading"
          v-el-select-loadmore="loadMoreOptions"
          class="select-list"
        >
          <el-option v-for="item in list" :key="item.id" :value="item.id" :label='item.name'>
            <ul class="part-list">
              <li>{{ $t('common.base.name') }}：{{item.name}}</li>
              <li class="part-list-child">{{ $t('common.base.serialNumber') }}：{{item.serialNumber}}</li>
              <li class="part-list-child">{{ $t('product.component.miniTable.partType.standard') }}：{{item.standard}}</li>
            </ul>
          </el-option>
        </el-select>

        <el-select
          v-if="dataType==='wiki'"
          v-model="value"
          multiple
          clearable
          collapse-tags
          :placeholder="$t('common.placeholder.select')"
          remote
          filterable
          @focus="focus"
          :remote-method="search"
          :loading="selectLoading"
          v-el-select-loadmore="loadMoreFunc"
          class="select-list"
        >
          <el-option v-for="item in list" :key="item.id" :value="item.id" :label='item.title'>
            <div class="wiki-item">
              <h4>{{item.title}}</h4>
              <template v-if="isOpenData && item.createUserStaffId">
                <p><open-data type='userName' :openid="item.createUserStaffId"></open-data> {{ $t('product.productType.text.text2') }} {{item.createTime | fmt_datetime}} {{item.type}}</p>
              </template>
              <template v-else>
                <p>{{item.createUserName}} {{ $t('product.productType.text.text2') }} {{item.createTime | fmt_datetime}} {{item.type}}</p>
              </template>
              <p class="wiki-content">{{item.content}}</p>
              <p v-if="item.label && item.label.length">
                <i class="iconfont icon-tag"></i>
                <span v-for="label in item.label" :key="label">{{label}}</span>
              </p>
            </div>
          </el-option>
        </el-select>

        <!-- 复制产品类型 -->
        <biz-remote-select
          v-if="dataType === 'copy'"
          v-model="value"
          :remote-method="searchProductType"
          cleared
          :placeholder="$t('common.placeholder.select')"
        >
        </biz-remote-select>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">{{$t('common.base.cancel')}}</el-button>
      <el-button type="primary" @click="submit">{{$t('common.base.makeSure')}}</el-button>
    </span>
  </base-modal>
</template>

<script>
import { isOpenData } from '@src/util/platform'
import _ from 'lodash';
import {
  getPagePart,
  getPageWiki,
  getCatalogList
} from "@src/api/ProductV2Api";
import {Cascader} from 'element-ui';

export default {
  name:'link-dialog',
  props:{
    dataType:{
      type:String
    },
    partList:{
      type:Array,
      default:()=>[]
    },
    wikiList:{
      type:Array,
      default:()=>[]
    }
  },
  data(){
    return{
      isOpenData,
      visible:false,
      value:[],
      list:[],
      selectList:[],
      selectLoading:false,
      params:{
        keyWord:'',
        pageSize:50,
        pageNum:1
      },
      hasNextPage: false,
      loadMoreOptions: { // 分页配置
        disabled: false,
        callback: this.loadMoreFunc,
        distance: 10
      },
    }
  },
  computed:{
    title(){
      const dataType=this.dataType;
      if(dataType==='part'){
        return this.$t('product.productPart')
      }else if(dataType==='wiki'){
        return this.$t('product.productWiki')
      }else if(dataType==='copy'){
        return this.$t('product.productType.title.title6')
      }
    }
  },
  watch:{
    value(val){
      if(this.dataType!=='copy'){
        val.forEach(id=>{
          const exist=this.selectList.find(item=>item.id===id);
          if(!exist){
            const item=this.list.find(l=>l.id===id);
            this.selectList.push(item);
          }
        });
        const delList=this.selectList.filter(item=>!val.find(id=>item.id===id)).map(item=>item.id);
        delList.forEach(id=>{
          const index=this.selectList.findIndex(item=>item.id===id);
          this.selectList.splice(index,1);
        });
      }
    }
  },
  methods:{
    open(){
      this.value=[];
      this.list=[];
      this.selectList=[];
      this.echo();
      this.visible=true;
    },
    // 回显
    echo(){
      this.$nextTick(()=>{
        if(this.dataType==='part'){
          if(this.partList.length){
            this.value=this.partList.map(item=>item.id);
            this.list=[...this.list, ...this.partList];
          }
        }else if(this.dataType==='wiki'){
          if(this.wikiList.length){
            this.value=this.wikiList.map(item=>item.id);
            this.list=[...this.list, ...this.wikiList];
          }
        }
      })
    },
    submit(){
      if(!this.value.length) return this.$message.warning(this.$t('product.tips.selectDataTip'));
      this.visible=false;
      let currentSelectValue = this.value;

      if (this.dataType == 'copy') {
        currentSelectValue = this.value?.map(item => {
          return item.catalogId
        })
      }
      this.$emit('submit',this.selectList, currentSelectValue);
    },
    focus(){
      if(!this.list.length) this.search();
    },
    search:_.debounce(function(keyWord, loadMore) {
      const httpObj = this.dataType === 'part' || this.dataType === 'copy' ? getPagePart : getPageWiki;
      this.params.keyWord = keyWord || '';

      this.selectLoading = true;
      httpObj(this.params).then(res=>{
        if(!res) return
        const list = res.result.list || [];
        this.list = loadMore ? this.list.concat(list) : list;
        this.hasNextPage = res.result.hasNextPage;
      }).catch(err=>{
        console.error(err);
      }).finally(()=>{
        this.selectLoading = false;
      });
    }, 500),

    loadMoreFunc() {
      if (this.hasNextPage) {
        this.params.pageNum++;
        this.search(this.params.keyWord, true);
      } 
    },
    // 获取产品类型下拉数据
    searchProductType(params) {
      try {
        params.keyWord = params.keyword;
        return getCatalogList(params)
          .then(res => {
            if(!res.success){
              this.$message.error(res.message);
              return;
            }
            
            res.result.list = res.result?.list?.map(item =>
              Object.freeze({
                label: item?.catalogName,
                value: item?.catalogId,
                ...item
              })
            );
            return res.result;
          })
          .catch(e => console.error(e));
      }catch(e){
        console.error(e)
      }
    }
  },
  components:{
    [Cascader.name]:Cascader
  }
}
</script>
<style lang="scss" scoped>
.link-dialog-form {
  padding: 30px 20px;
}
.cascader2{
  z-index:4000 !important;
}
.el-select-dropdown__item{
  height: auto;
  width: 360px;
}

.select-list{
  width: calc(100% - 100px);
}
.part-list{
  padding-left: 0;
  // border-bottom: 1px solid $color-border-l3;
  padding: 5px 0;
  overflow: auto;
  li{
    height: 20px;
    line-height: 20px;
  }
  .part-list-child{
    color: #666666;
  }
}
.el-select-dropdown.is-multiple .el-select-dropdown__item.selected{
  p{
    // color:$color-primary;
    color: #666666;
  }
}

.wiki-item{
  padding: 5px 0;
  // border-bottom: 1px solid $color-border-l3;
  overflow: auto;
  h4{
    margin-bottom: 0;
    height: 20px;
    line-height: 20px;
    @include text-ellipsis();
  }
  p{
    margin-bottom: 0;
    height: 20px;
    line-height: 20px;
    color: $text-color-secondary;
    font-size: 12px;
    @include text-ellipsis();

    span{
      padding: 2px 5px;
      margin-right: 6px;
      background-color: $bg-color-l3;
    }
  }
}
</style>
