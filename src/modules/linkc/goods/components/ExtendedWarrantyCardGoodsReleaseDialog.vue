<template>
  <base-modal
    :show="show"
    :title="$t('order.extendedWarrantyCard.warrantyCardRelease')"
    width="800px"
    @cancel="cancel"
    class=""
  >
    <el-checkbox
      class="mb_12"
      v-model="duplicateCommodity"
      @change="fetchTableData"
    >{{$t('goods.component.filterProducts')}}</el-checkbox>

    <el-table
      ref="multipleTableRef"
      v-loading="listLoading"
      :data="dataList"
      header-row-class-name="common-list-table-header__v2"
      class='bbx-normal-list-box mar-b-16'
      stripe
      border
      row-key="id"
      max-height="500"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        width="55"
        :reserve-selection="true"
        :selectable="canBeSelect"
      >
      </el-table-column>

      <template slot="empty">
        <BaseListForNoData
          v-show="!listLoading"
          :notice-msg="$t('common.base.tip.noData')"
        ></BaseListForNoData>
      </template>
      <el-table-column
        v-for="column in columns"
        :key="column.fieldName"
        :prop="column.fieldName"
        :label="column.displayName"
        min-width="108"
        show-overflow-tooltip
      >
        <template slot-scope="{ row }">
          <template v-if="column.fieldName === 'timeExpand'">
            {{ row.timeExpand }}{{ renderTimeExpand(column, row) }}
          </template>
          <template v-else>
            {{ $formatFormField(column, row) }}
          </template>
        </template>
      </el-table-column>
    </el-table>

    <BaseAddOnPagination
      :pagination-info="paginationInfo"
      @pageJump="jump"
      @sizeChange="handleSizeChange"
    />

    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        type="plain-third"
        @click="cancel"
      >
        {{ $t('common.base.cancel') }}
      </el-button>
      <el-button
        :disabled="pending"
        type="primary"
        @click="handleReleased"
      >
        {{$t('common.base.release')}}
      </el-button>
    </div>
  </base-modal>
</template>
<script>
import { computed, defineComponent, ref, reactive, toRefs, nextTick } from 'vue';

import { formatDate } from 'pub-bbx-utils';
import { toast } from '@src/util/platform';
import { getRootWindow } from '@src/util/dom';
import { openAccurateTab } from '@src/util/platform';
import { PageRoutesTypeEnum } from '@model/enum/PageRoutesEnum';
import { extendedWarrantyCardField, renderTimeExpand } from '@src/modules/setting/extendedWarrantyCardType/field';
import i18n from '@src/locales';

/** api */
import { getExtendedWarrantyCardList } from '@src/api/ExtendedWarrantyCard';
import { releaseGoods } from '@src/api/PortalApi.ts';

export default defineComponent({
  name: 'ExtendedWarrantyCardGoodsReleaseDialog',
  emits: ['reloadPage'],
  setup(props, { emit }) {
    const show = ref(false);
    const listLoading = ref(false);
    const multipleSelection = ref([]);
    const multipleTableRef = ref();

    const duplicateCommodity = ref(true)

    let state = reactive({
      paginationInfo: {
        pageNum: 1,
        pageSize: 10,
        totalItems: 0,
      },
      dataList: [],
    });

    const pending = ref(false);

    const columns = computed(() => {
      return extendedWarrantyCardField?.filter(item => ['name', 'desc', 'timeExpand'].includes(item.fieldName));
    });

    // 发布商品灰度开启
    const commodityGray = computed(() => {
      const RootWindow = getRootWindow(window);
      return RootWindow?.grayAuth?.COMMIDITY_OPTIMIZATION ?? false;
    })

    // 获取延保卡列表数据
    const fetchTableData = async () => {
      listLoading.value = true;
      try {
        const res = await getExtendedWarrantyCardList({
          pageSize: state.paginationInfo.pageSize,
          pageNum: state.paginationInfo.pageNum,
          duplicateCommodity: !duplicateCommodity.value
        });
        const { success, msg, data } = res || {};
        if (!success) {
          return toast(msg, 'error');
        }

        state.dataList = data?.list || [];

        state.paginationInfo = {
          pageNum: data?.pageNum,
          pageSize: data?.pageSize,
          totalItems: data?.total,
        };
      } catch (err) {
        console.log('err', err);
      } finally {
        listLoading.value = false;
      }
    };

    const openDialog = () => {
      show.value = true;
      fetchTableData();
    };

    const cancel = () => {
      show.value = false;
      pending.value = false;
      multipleSelection.value = [];
      nextTick(() => {
        multipleTableRef.value.clearSelection();
      });
      state.paginationInfo.pageNum = 1;
      state.paginationInfo.pageSize = 10;
      state.paginationInfo.totalItems = 0;
      state.dataList = [];
    };

    const handleReleased = async () => {
      try {
        if (commodityGray.value) {
          if (multipleSelection.value.length === 0) {
            return toast(i18n.t('goods.placeHolder.selectProduct'), 'warning');
          }
          // 最多选择的商品数量
          const productLimit = 20 
          if (multipleSelection.value.length > productLimit) {
            return toast(i18n.t('goods.placeHolder.selectProductLimit', {limit: productLimit}), 'warning');
          }

          const selection = multipleSelection.value.map(item => {
            return {
              id: item.id,
              name: item.name,
            }
          })
          // 缓存选中的商品key
          const SELECTED_RELEASE_GOODS_INFO = "selected_release_goods_info";
          localStorage.setItem(SELECTED_RELEASE_GOODS_INFO, JSON.stringify(selection));

          let fromId = window.frameElement.getAttribute('id');

          openAccurateTab({
            type: PageRoutesTypeEnum.PageGoodsEdit,
            params: `isRelease=1&originType=4`,
            fromId,
          });
          return;
        }
        if (multipleSelection.value.length <= 0) {
          return toast(i18n.t('goods.placeHolder.selectProduct'), 'warning');
        }
        const ids = multipleSelection.value.map(item => item.id) || [];

        pending.value = true;
        const { code } = await releaseGoods({
          commodityIdList: ids,
          originType: 4, // 4 是延保卡
        });

        if (code !== '200') {
          return toast(i18n.t('goods.tips.releaseFailed'), 'error');
        }

        toast(i18n.t('goods.tips.releaseSuccess'), 'success');

        cancel();
        setTimeout(() => {
          emit('reloadPage');
        }, 1000);
      } catch (error) {
        console.error('handleReleased error', error);
      } finally {
        pending.value = false;
      }
    };

    const jump = pN => {
      state.paginationInfo.pageNum = pN;
      fetchTableData();
    };

    const handleSizeChange = pageSize => {
      state.paginationInfo.pageSize = pageSize;
      state.paginationInfo.pageNum = 1;
      fetchTableData();
    };

    const handleSelectionChange = selection => {
      multipleSelection.value = selection;
    };

    // 设置当前行是否禁用
    const canBeSelect = (row) => {
      return !row?.alreadyPublishedCommodity;
    }

    return {
      show,
      ...toRefs(state),
      listLoading,
      multipleTableRef,
      duplicateCommodity,
      pending,
      columns,

      openDialog,
      cancel,
      handleReleased,
      formatDate,
      jump,
      handleSizeChange,
      renderTimeExpand,
      handleSelectionChange,
      fetchTableData,
      canBeSelect
    };
  },
});
</script>

