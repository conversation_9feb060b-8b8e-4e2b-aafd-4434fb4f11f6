<template>
  <div class="goods-container" v-loading.fullscreen.lock="loading">
    <div class="normal-title-1">
      <div class="header-title flex-1">
        <span class="title mar-r-10" v-if="isRelease">{{ $t('goods.component.releaseProduct') }}</span>
        <span class="tip" v-if="isRelease">{{ $t('goods.component.releaseProductTip') }}</span>
      </div>
      <div>
        <el-button v-if="isRelease" type="plain-third" @click="closeTab">
          {{$t('common.base.back')}}
        </el-button>
        <el-button v-else-if="goodsId" type="plain-third" @click="handleBack">
          {{$t('common.base.back')}}
        </el-button>
        <el-button type="primary" @click="handleSave" :loading="pending">
          {{ isRelease ? $t('common.base.release') : $t('common.base.save') }}
        </el-button>
      </div>
    </div>

    <div class="base-form base-form-ss goods-form-wrap" v-if="init">
      <div class="goods-form-container" v-if="isRelease">
        <div class="goods-menu" v-if="multipleSelection.length > 1">
          <div
            class="goods-menu-item"
            :class="currentActiveIndex == index ? 'active' : ''"
            v-for="(item, index) in multipleSelection"
            :key="item.id"
            @click="handleSelectionChange(index)"
          >
            <span>{{ item.name }}</span>
            <i :ref="`warningIcon_${index}`" class="iconfont icon-warning-circle-fill mar-l-4"></i>
          </div>
        </div>
        <div class="goods-form-list flex-1">
          <GoodsEditForm
            v-for="(item, index) in forms"
            :key="index"
            v-show="currentActiveIndex == index"
            :ref="`goodsEditForm_${index}`"
            :fields="_goodsFields"
            :goods-id="goodsId"
            v-model="forms[index]"
            :pending="pending"
            :is-edit="isEdit"
            @addNeedServerDeleFiles="addNeedServerDeleFiles($event, index)"
            :isAllowOverbooking="form.isAllowOverbooking === 0 ? false : true"
            @updateOverBooking="updateOverBooking"
            @input="goodsEditFormInput"
          />
        </div>
      </div>
      <GoodsEditForm
        v-else
        ref="goodsEditForm"
        :fields="_goodsFields"
        :goods-id="goodsId"
        v-model="form"
        :pending="pending"
        :is-edit="isEdit"
        @addNeedServerDeleFiles="addNeedServerDeleFiles"
        :isAllowOverbooking="form.isAllowOverbooking === 0 ? false : true"
        @updateOverBooking="updateOverBooking"
      />
    </div>
  </div>
</template>

<script>
import _ from 'lodash';
import qs from '@src/util/querystring';
import * as FormUtil from '@src/component/form/util';
import { goodsFields, formatFields } from '@src/modules/linkc/field.js';
import GoodsEditForm from '@src/modules/linkc/goods/components/GoodsEditForm.vue';
import { editGoodsInfo, initSaveListInfo, releaseGoodsV2, queryGoodsDetail } from '@src/api/PortalApi.ts';
import { openAccurateTab } from '@src/util/platform';
import { PageRoutesTypeEnum } from '@model/enum/PageRoutesEnum';
import { querySpecsList } from '@src/api/LinkcApi';
import { getGoodsRichTextContent } from '@src/api/SystemApi.ts';

// 缓存选中的商品key
const SELECTED_RELEASE_GOODS_INFO = "selected_release_goods_info";

export default {
  components: {
    GoodsEditForm,
  },
  data() {
    return {
      loading: false,
      init: true,
      pending: false,
      form: {},
      forms: [], // 批量发布商品的表单数据
      multipleSelection: [], // 选中的商品
      goodsFields: [],
      standArr: [],
      needServerDeleFiles: [],
      needServerDeleFilesList: [],
      currentActiveIndex: 0,
    };
  },
  computed: {
    // 是否是发布
    isRelease() {
      return this.queryObject.isRelease == 1
    },
    // 商品类型，1 备件, 2 服务项目, 3 物料(相当于3.0版本的备件)，4 延保卡
    originType() {
      return Number(this.queryObject.originType)
    },
    queryObject() {
      return qs.parse(window.location.search)
    },
    goodsId() {
      return this.queryObject.id ?? '';
    },
    action() {
      return this.goodsId ? 'edit' : 'create';
    },
    isEdit() {
      return this.action === 'edit';
    },
    _goodsFields() {
      this.goodsFields = _.cloneDeep(goodsFields);

      this.goodsFields.forEach(v => {
        if (v.fieldName === 'serviceType' && (this.form?.originType === 2 || this.originType == 2)) {
          v.setting.dataSource = [{ text: this.$t('task.setting.taskTypeSetting.manage.industryNames.name7'), value: 2 }];
          v.disable = 1;
        }
        if (['originType', 'originCode', 'originName', 'originCategory', 'specsPrice'].includes(v.fieldName)) {
          v.disabled = true
          v.isNull = 1
        }

        if (v.fieldName === 'originPrice') {
          if (this.form?.originType === 1 || this.originType == 1) {
            v.placeHolder = this.$t('goods.edit.modifyPrice1')
          } else if (this.form?.originType === 2 || this.originType == 2) {
            v.placeHolder = this.$t('goods.edit.modifyPrice2')
          } else {
            v.placeHolder = this.$t('goods.edit.modifyPrice3')
          }
        }
        if(v.fieldName === 'information') {
          v.maxlength = 1000
        }
        // 发布商品隐藏【商品编号】【物料编号】【类别】字段
        if (this.isRelease) {
          if (['commodityCode', 'originCode', 'originCategory'].includes(v.fieldName)) {
            v.isHidden = 1
          }
        }
      });
      return this.goodsFields.filter(field => field.isEdit);
    },
  },
  async mounted() {
    await this.getSpecsList()
    if (this.isRelease) {
      this.initForm()
      return;
    }
    this.handleGetGoodsInfo();
  },
  methods: {
    async initForm() {
      try {
        const currentTabId = window.frameElement?.dataset?.id;
        this.$platform.setTabTitle({
          id: currentTabId,
          title: this.$t('goods.component.releaseProduct'),
        });
        this.forms = []
        this.multipleSelection = JSON.parse(localStorage.getItem(SELECTED_RELEASE_GOODS_INFO) || '[]')
        initSaveListInfo({
          commodityIdList: this.multipleSelection.map(item => item.id),
          originType: this.originType
        }).then(res => {
          this.forms = (res.data || []).map(form => {
            if (form.serviceType === 1) {
              form.serviceType = [this.$t('goods.component.expressDelivery')];
            } else if (this.form.serviceType === 2) {
              form.serviceType = [this.$t('task.setting.taskTypeSetting.manage.industryNames.name7')];
            } else {
              form.serviceType = [this.$t('goods.component.expressDelivery'), this.$t('task.setting.taskTypeSetting.manage.industryNames.name7')];
            }
            form.priceType = form.priceType + ''
            // 延保卡默认手动填写
            if (form.originType == 4) {
              form.priceType = '2'
            }
            form.price = form.price ?? '0'
            form.originPrice = form.originPrice ?? '0'
            form.expressCost = '0'
            form.standardDesc = {
              hasSpecs: 0, 
              specifications: [], 
              specsList: [], 
              standard: ''
            }
            return form
          })
        })
       
        this.multipleSelection.forEach((item => {
          this.needServerDeleFilesList.push([])
        }))
      } catch (error) {
        console.error('fetch multipleSelection error', error);
      }
    },
    goodsEditFormInput() {
      if (this.multipleSelection.length > 1) {
        this.$nextTick(async() => {
          const arr = this.forms.map((item, index) => index)
          for(const index of arr) {
            this.$nextTick(async() => {
              // 校验表单是否有必填项，显示隐藏icon
              const valid = await this.$refs[`goodsEditForm_${index}`]?.[0]?.validate()
              this.$refs[`warningIcon_${index}`][0].style.display = valid ? 'none' : 'block'
            })
          }
        })
      } 
    },
    handleSelectionChange(index) {
      this.currentActiveIndex = index;
    },
    updateOverBooking(val){
    this.form.isAllowOverbooking = val ? 1 : 0; 
    },
    getSpecsList() {
      querySpecsList().then(res => {
          if(res.code != 200) return this.$message.error(res.msg);
          this.standArr = (res.data || []).map(item => {
            return {
              name: item.specsName,
              fieldName: item.fieldName,
            }
          })
      }).catch(err => {
          this.$message.error(res.msg);
      })
    },
    async handleGetGoodsInfo() {
      try {
        this.loading = true;

        const { code, data } = await queryGoodsDetail({ id: this.goodsId });

        if (code !== '200') {
          this.loading = false;
          return this.$message.error(this.$t('goods.tips.obtainInfoFailed'));
        }

        const richtextId = data?.informationId || ''
        if (richtextId) {
          const res = await getGoodsRichTextContent({
            id: richtextId,
          })
          if (res.code == 0 || res.status == 0) {
            data.information = res.result?.content || ''
          }
        }

        this.handleConvertData(data);
        this.loading = false;
      } catch (error) {
        this.loading = false;
        console.error('fetch goods Detail error', error);
      }
    },
    // 转换数据
    handleConvertData(data) {
      this.form = data;

      this.form.catalogIds = this.form?.productTypeList?.map(v => v.id) || [];
      this.form.priceType = this.form.priceType + '';

      if (this.form.inventoryNumber >= 0)
        this.form.inventoryNumber = this.form.inventoryNumber + '';

      if (this.form.expressCost >= 0)
        this.form.expressCost = this.form.expressCost + '';

      if (this.form.serviceType === 1) {
        this.form.serviceType = [this.$t('goods.component.expressDelivery')];
      } else if (this.form.serviceType === 2) {
        this.form.serviceType = [this.$t('task.setting.taskTypeSetting.manage.industryNames.name7')];
      } else {
        this.form.serviceType = [this.$t('goods.component.expressDelivery'), this.$t('task.setting.taskTypeSetting.manage.industryNames.name7')];
      }
      let { hasSpecs=0, specifications=[], specsList=[], standard=''} = this.form
      this.$set(this.form, 'standardDesc', {})
      if(specsList?.length) {
        specsList = specsList.map(items => {
          let obj = {}
          items.specsInfo.forEach(v => {
            obj[v.specsGroupName] = v.specsInfoValue
          })
          return {
            ...items,
            ...obj
          }
        })
      }
      this.form.standardDesc = {
        hasSpecs, 
        specifications, 
        specsList, 
        standard
      }
    },
    closeTab() {
      let id = window.frameElement.dataset.id;
      this.$platform.closeTab(id);
    },
    handleBack() {
      let id = window.frameElement.dataset.id;
      this.$platform.closeTab(id);

      openAccurateTab({
        type: PageRoutesTypeEnum.PageGoodsDetail,
        key: this.goodsId,
        titleKey: ` - ${this.form.commodityName}`,
        params: `id=${this.goodsId}`,
      });
    },
    async handleSave() {
      if (this.isRelease) {
        const arr = this.forms.map((item, index) => index)
        let isValid = false
        // 遍历校验表单
        for(const index of arr) {
          const valid = await this.$refs[`goodsEditForm_${index}`]?.[0]?.validate()
          if (this.multipleSelection.length > 1) {
            this.$refs[`warningIcon_${index}`][0].style.display = valid ? 'none' : 'block'
          }
          if (!valid) {
            isValid = valid
            if (!isValid) return this.$message.error(this.$t('goods.component.submitTip1'))
            if(this.forms?.[index]?.standardDesc?.hasSpecs) {
              let specsData = this.forms?.[index]?.standardDesc?.specifications || []
              let specsList = this.forms?.[index]?.standardDesc?.specsList || []
              if(specsData.length ==0) return this.$message.error(this.$t('goods.tips.SpecsDescTip'));
              if(specsList.length) {
                let flag = false
                flag = specsList.some(item => ((item.inventory!=0 && !item.inventory) || !item.salePrice ))
                if(flag) return this.$message.error(this.$t('goods.tips.SpecsDescTip'));
                let serialNumberArr = specsList.filter(item => item?.serialNumber).map(v=> v?.serialNumber)
                flag = this.isRepeat(serialNumberArr)
                if(flag) return this.$message.error(this.$t('goods.tips.serialNumberTips'));
              }
            }
          }
        }
        this.handleBatchRelease()
        return;
      }
      this.$refs.goodsEditForm.validate().then(valid => {
        if (!valid) {
          return Promise.reject('validate fail.');
        }
        if(this.form?.standardDesc?.hasSpecs) {
          let specsData = this.form?.standardDesc?.specifications || []
          let specsList = this.form?.standardDesc?.specsList || []
          if(specsData.length ==0) return this.$message.error(this.$t('goods.tips.SpecsDescTip'));
          if(specsList.length) {
            let flag = false
            flag = specsList.some(item => ((item.inventory!=0 && !item.inventory) || !item.salePrice ))
            if(flag) return this.$message.error(this.$t('goods.tips.SpecsDescTip'));
            let serialNumberArr = specsList.filter(item => item?.serialNumber).map(v=> v?.serialNumber)
            flag = this.isRepeat(serialNumberArr)
            if(flag) return this.$message.error(this.$t('goods.tips.serialNumberTips'));
          }
        }
        this.handleSubmit();
      });
    },
    // 批量发布
    async handleBatchRelease() {
      let commodityList = []
      this.forms.forEach((form, index) => {
        let params = this.handleBuildParams(form)
        if (this.needServerDeleFilesList[index]?.length) {
          params.deleteFiles = this.needServerDeleFilesList[index]
        }
        commodityList.push(params)
      })
      this.loading = true
      const { code, msg } = await releaseGoodsV2({commodityList});
      this.loading = false;
      if (code != '200') {
        return this.$message.error(msg);
      }
      this.$message.success(this.$t('common.base.tip.releaseSuccess'));
      setTimeout(() => {
        let id = window.frameElement.dataset.id;
        this.$platform.closeTab(id);
        let fromId = window.frameElement.getAttribute('fromid');
        this.$platform.refreshTab(fromId);
      }, 1000);
    },
    isRepeat(arr) {
      return new Set(arr).size !== arr.length;
    },
    handleBuildParams(form) {
      try {
        const params = _.cloneDeep(form);
        params.priceType = ~~params.priceType;
        if (params.priceType === 1) params.price = params.originPrice;
        if (params.serviceType?.length > 1) {
          params.serviceType = 3;
        } else {
          params.serviceType = params.serviceType[0] === this.$t('goods.component.expressDelivery') ? 1 : 2;
        }
        params.catalogIds =
          params.catalogPathIds?.map(item => item[item.length - 1]) || [];

        params.standard = params.standardDesc.standard
        params.hasSpecs = params.standardDesc.hasSpecs
        params.specifications = []
        params.specsList = []
        if(params?.standardDesc?.hasSpecs) {
          let keys = []
          if(params?.standardDesc?.specsList) {
            keys = Object.keys(params?.standardDesc?.specsList[0]) || []
            params.standardDesc.specsList?.forEach((v) => {
              if(!(v.specsInfo)) {
                const specsInfo = []
                this.standArr?.forEach(f => {
                  const obj = {};
                  if (keys.includes(f.name)) {
                    obj.specsGroupKey = f.fieldName;
                    obj.specsGroupName = f.name;
                    obj.specsInfoValue = v?.[f.name];
                  }
                  if (Object.keys(obj).length !== 0) specsInfo.push(obj);
                })
                let paramsObj = {
                  specsInfo,
                  serialNumber: v.serialNumber,
                  salePrice: v.salePrice,
                  inventory: v.inventory,
                }
                params.specsList.push(paramsObj)
              }else {
                params.specsList.push(v)
              }
            })
            params.standard = ''
            params.specifications = params.standardDesc.specifications
          }
        }else {
          params.standard = params.standardDesc.standard
          params.specsPrice = '0.01~0.01'
        }
      return params;
      }catch (error) {
        console.error( error);
      }
    },
    async handleSubmit() {
      try {
        const params = this.handleBuildParams(this.form);
        if (this.needServerDeleFiles.length) {
          params.deleteFiles = this.needServerDeleFiles
        }
        const { code, msg } = await editGoodsInfo(params);

        if (code !== '200') {
          this.loading = false;
          return this.$message.error(msg);
        }

        this.$message.success(this.$t('goods.tips.modifySuccess'));
        setTimeout(() => {
          let id = window.frameElement.dataset.id;
          this.$platform.closeTab(id);

          this.handleBack();

          let fromId = window.frameElement.getAttribute('fromid');
          this.$platform.refreshTab(fromId);
        }, 1000);
      } catch (error) {
        this.loading = false;
        console.error('fetch goods Detail error', error);
      }
    },
    addNeedServerDeleFiles(files, index) {
      // 发布商品删除附件处理
      if (this.isRelease) {
        this.needServerDeleFilesList[index] = [...this.needServerDeleFilesList[index], ...files];
        return 
      }
      this.needServerDeleFiles = [...this.needServerDeleFiles, ...files];
    },  
  },
};
</script>

<style lang="scss">
body {
  padding: 10px;
}

.goods-container {
  width: 100%;
  height: 100%;
  background-color: #fff;
  border-radius: 4px;

  .page-title {
    border-bottom: 1px solid #f4f7f5;
    padding: 12px 10px;
    display: flex;
    justify-content: space-between;

    .iconfont {
      font-size: 12px;
    }

    .title {
      display: flex;
      justify-content: space-between;
      span.text {
        line-height: 34px;
        margin-right: 12px;
      }
    }
  }

  @mixin title-class {
    height: 48px;
    display: flex;
    align-items: center;
    padding: 0 20px;
    font-weight: 600;
  }
  .normal-title-1 {
    @include title-class();
    justify-content: space-between;
    padding: 12px 16px;
    height: 57px;
    font-size: 20px;
    gap: 10px;
    border-bottom: 1px solid #f4f7f5;
  }
  .header-title {
    display: flex;
    align-items: center;
    .title {
      font-size: 16px;
      font-weight: 500;
    }
    .tip {
      padding: 4px 8px;
      font-size: 12px;
      font-weight: normal;
      line-height: 20px;
      border-radius: 4px;
      background: #FCF6EC;
      color: #E6A23C;
    }
  }
  .base-form-ss {
    height: calc(100% - 57px);
    overflow: auto;
  }
  .goods-form-wrap {
    padding: 24px;
  }
  .goods-form-container {
    display: flex;
    width: 100%;
    .goods-menu {
      width: 184px;
      padding-right: 12px;
      border-right: 1px solid #F0F2F5;
      &-item {
        display: flex;
        align-items: center;
        width: 100%;
        padding: 0px 16px;
        line-height: 32px;
        margin-bottom: 4px;
        cursor: pointer;
        &:last-child {
          margin-bottom: 0;
        }
        &.active {
          border-radius: 4px;
          background: #E6FFFB !important;
        }
        &:hover {
          border-radius: 4px;
          background: #F5F8FA;
        }
        span {
          max-width: calc(100% - 20px);
          @include text-ellipsis();
        }
        .iconfont {
          color: #F56C6C;
        }
      }
    }
  }
}

.form-builder {
  margin: 0 auto;
  max-width: 900px;
  padding: 10px 0 0 10px;

  .input-and-btn {
    display: flex !important;
    flex-flow: row nowrap;

    .form-item,
    .form-text,
    .form-select,
    .biz-team-select {
      flex: 1;
    }

    .base-dist-picker {
      padding-right: 0;
    }

    button {
      margin-left: 10px;
    }
  }
}
</style>
