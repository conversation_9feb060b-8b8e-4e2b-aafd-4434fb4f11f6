import i18n from '@src/locales';
import { getRootWindow } from '@src/util/dom';
// 是否开启云仓灰度
const isCloudwarehouse = () => {
  const RootWindow = getRootWindow(window)
  return RootWindow.grayAuth?.cloudwarehouse || false
}
const isGrouping = () =>{
  const RootWindow = getRootWindow(window)
  return RootWindow.grayAuth?.DOOR_SHOP_GROUP || false
}
const goodsFields = isGrouping() ? [{
  tableName: 'goods',
  fieldName: 'base_info',
  displayName: i18n.t('common.base.basicInfo'),
  formType: 'separator',
  orderId: 0,
  isEdit: 1,
  show: false,
  isDelete: 0,
  isGuideData: true,
  isHidden: 0,
  isNull: 1,
  isSearch: 0,
  isSystem: 0,
  isVisible: true,
  placeHolder: '',
  setting: {
    isAuthorization: false
  },
  subFormFieldList: null,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'commodityName',
  displayName: i18n.t('goods.component.tradeName'),
  formType: 'text',
  defaultValue: null,
  isNull: 0,
  isSearch: 0,
  placeHolder: i18n.t('order.placeHolder.productName'),
  setting: {},
  maxlength: 50,
  orderId: 1,
  isDelete: 0,
  isEdit: 1,
  guideProfessions: [],
  show: true,
  export: true,
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'originType',
  displayName: i18n.t('order.detailV2.productType'),
  formType: 'select',
  defaultValue: null,
  isNull: 0, // 是否开启校验
  isSearch: 1,
  operator: 'eq',
  setting: {
    isMulti: false,
    dataSource: [{
      text: i18n.t('common.base.sparePart'),
      value: 1,
    }, {
      text: i18n.t('common.form.type.material'),
      value: 3,
    }, {
      text: i18n.t('common.label.serviceItem'),
      value: 2,
    }],
  },
  orderId: 2,
  isDelete: 0,
  isEdit: 1, // 是否在编辑页显示
  guideProfessions: [],
  show: true, // 是否在列表页显示
  export: true, // 是否可以导出
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'attachmentList',
  displayName: i18n.t('order.productImg'),
  formType: 'attachment',
  defaultValue: null,
  isNull: 0,
  isSearch: 0,
  placeHolder: null,
  setting: {},
  orderId: 1,
  isDelete: 0,
  isEdit: 1,
  guideProfessions: [],
  show: true,
  export: false,
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'commodityCode',
  displayName: i18n.t('common.base.goodsNo'),
  formType: 'text',
  defaultValue: null,
  isNull: 0,
  isSearch: 0,
  placeHolder: i18n.t('common.fields.productNo.placeholder'),
  setting: {},
  show: true,
  export: true,
  orderId: 0,
  isDelete: 0,
  isEdit: 1,
  disabled: 1,
  guideProfessions: [],
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'originCode',
  displayName: isCloudwarehouse() ? i18n.t('product.materialFields.sn.displayName') : i18n.t('common.part.partNo'),
  formType: 'text',
  defaultValue: null,
  isNull: 0,
  isSearch: 0,
  placeHolder: i18n.t('common.fields.productNo.placeholder'),
  setting: {},
  show: true,
  export: true,
  orderId: 0,
  isDelete: 0,
  isEdit: 1,
  disabled: 1,
  guideProfessions: [],
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'originName',
  displayName: i18n.t('order.serviceItemName'),
  formType: 'text',
  defaultValue: null,
  isNull: 0,
  isSearch: 0,
  placeHolder: i18n.t('order.placeHolder.productName'),
  setting: {},
  orderId: 0,
  isDelete: 0,
  isEdit: 1,
  guideProfessions: [],
  show: false,
  export: false,
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'originCategory',
  displayName: i18n.t('common.fields.catalog.displayName'),
  formType: 'select',
  defaultValue: null,
  export: false,
  isNull: 0,
  isSearch: 1,
  operator: 'eq',
  setting: {
    isMulti: false,
    dataSource: [],
  },
  orderId: 3,
  isDelete: 0,
  isEdit: 1,
  guideProfessions: [],
  show: true,
  export: true,
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'productTypeList',
  displayName: i18n.t('common.label.ralationProductType'),
  formType: 'select',
  defaultValue: null,
  isNull: 1,
  isSearch: 0,
  placeHolder: i18n.t('common.placeholder.selectProductType'),
  setting: {},
  orderId: 10,
  isDelete: 0,
  isEdit: 1,
  guideProfessions: [],
  show: true,
  export: true,
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'productGroup',
  displayName: '商品分组',
  formType: 'select',
  defaultValue: null,
  isNull: 1,
  isSearch: 0,
  placeHolder: '',
  setting: {},
  orderId: 99,
  isDelete: 0,
  isEdit: 1,
  guideProfessions: [],
  show: true,
  export: true,
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'purchaseLink',
  displayName: i18n.t('order.purchaseLink'),
  formType: 'text',
  defaultValue: null,
  isNull: 1,
  isSearch: 0,
  placeHolder: i18n.t('order.placeHolder.purchaseLinkText'),
  setting: {},
  orderId: 11,
  isDelete: 0,
  isEdit: 1,
  guideProfessions: [],
  show: true,
  export: true,
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'information',
  displayName: i18n.t('order.productDesc'),
  formType: 'richtext',
  defaultValue: null,
  isNull: 1,
  isSearch: 0,
  placeHolder: i18n.t('order.productDesc'),
  setting: {},
  orderId: 0,
  isDelete: 0,
  isEdit: 1,
  guideProfessions: [],
  show: true,
  export: false,
  isGuideData: false,
  guideData: false,
  maxlength: 200,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'displayName',
  displayName: i18n.t('common.base.createUser'),
  formType: 'user',
  defaultValue: null,
  isNull: 0,
  isSearch: 0,
  placeHolder: null,
  setting: {},
  orderId: 1,
  isDelete: 0,
  isEdit: 0,
  guideProfessions: [],
  show: true,
  export: true,
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'createTime',
  displayName: i18n.t('common.base.createTime'),
  formType: 'datetime',
  defaultValue: null,
  isNull: 0,
  isSearch: 0,
  placeHolder: null,
  setting: {},
  orderId: 12,
  isDelete: 0,
  isEdit: 0,
  guideProfessions: [],
  show: true,
  export: true,
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'putawayStatus',
  displayName: i18n.t('order.productStatus'),
  formType: 'text',
  defaultValue: null,
  isNull: 0,
  isSearch: 0,
  placeHolder: null,
  setting: {},
  orderId: 8,
  isDelete: 0,
  isEdit: 0,
  guideProfessions: [],
  show: true,
  export: true,
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'viewNumber',
  displayName: i18n.t('wiki.list.sortLabel.label1'),
  formType: 'text',
  defaultValue: null,
  isNull: 0,
  isSearch: 0,
  placeHolder: null,
  setting: {},
  orderId: 0,
  isDelete: 0,
  guideProfessions: [],
  show: false,
  export: false,
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  fieldName: 'price_inventory',
  displayName: i18n.t('order.priceInventory'),
  formType: 'separator',
  orderId: 0,
  isEdit: 1,
  show: false,
  isDelete: 0,
  isGuideData: true,
  isHidden: 0,
  isNull: 1,
  isSearch: 0,
  isSystem: 0,
  isVisible: true,
  placeHolder: '',
  setting: {
    isAuthorization: false
  },
  subFormFieldList: null,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'standardDesc',
  displayName: i18n.t('goods.component.isHaveSpecs'),
  formType: 'standardDesc',
  defaultValue: null,
  isNull: 0,
  isSearch: 0,
  placeHolder: '',
  setting: {},
  orderId: 4,
  isDelete: 0,
  isEdit: 1,
  guideProfessions: [],
  show: true,
  export: true,
  isGuideData: false,
  guideData: false,
},
{
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'specsPrice',
  displayName: i18n.t('goods.component.price'),
  formType: 'text',
  defaultValue: null,
  isNull: 0,
  isSearch: 0,
  placeHolder: null,
  setting: {
  },
  orderId: 5,
  isDelete: 0,
  isEdit: 1,
  guideProfessions: [],
  show: false,
  export: false,
  isGuideData: false,
  guideData: false,
  disabled: 1,
},
{
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'price',
  displayName: i18n.t('goods.component.price'),
  formType: 'number',
  defaultValue: null,
  isNull: 0,
  isSearch: 0,
  placeHolder: null,
  setting: {
    limitConig: {
      isLimit: 0,
      type: 1,
      min: 0.01,
      max: 99999
    }
  },
  orderId: 5,
  isDelete: 0,
  isEdit: 1,
  guideProfessions: [],
  show: true,
  export: true,
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'originPrice',
  displayName: i18n.t('order.serviceItemPrice'),
  formType: 'number',
  defaultValue: null,
  isNull: 0,
  isSearch: 0,
  placeHolder: i18n.t('goods.edit.modifyPrice1'),
  setting: {},
  orderId: 5,
  isDelete: 0,
  isEdit: 1,
  guideProfessions: [],
  show: false,
  export: false,
  isGuideData: false,
  guideData: false,
  disabled: true,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'inventoryNumber',
  displayName: i18n.t('common.part.stock'),
  formType: 'number',
  defaultValue: 0,
  isNull: 0,
  isSearch: 0,
  placeHolder: null,
  setting: {
    // limitConig: {
    //   isLimit: 0,
    //   type: 1,
    //   min: 0,
    //   max: 99999
    // }
  },
  orderId: 6,
  isDelete: 0,
  isEdit: 1,
  guideProfessions: [],
  show: true,
  export: true,
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'saleNumber',
  displayName: i18n.t('order.salesVolume'),
  formType: 'text',
  defaultValue: null,
  isNull: 0,
  isSearch: 0,
  placeHolder: null,
  setting: {},
  orderId: 9,
  isDelete: 0,
  isEdit: 0,
  guideProfessions: [],
  show: true,
  export: true,
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  fieldName: 'logistics_info',
  displayName: i18n.t('common.form.preview.sparepart.colum16'),
  formType: 'separator',
  orderId: 0,
  isEdit: 1,
  id: 11111,
  show: false,
  isDelete: 0,
  isGuideData: true,
  isHidden: 0,
  isNull: 1,
  isSearch: 0,
  isSystem: 0,
  isVisible: true,
  placeHolder: '',
  setting: {
    isAuthorization: false
  },
  subFormFieldList: null,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'serviceType',
  displayName: i18n.t('order.detailV2.serviceMethod'),
  formType: 'select',
  defaultValue: null,
  isNull: 0,
  isSearch: 0,
  placeHolder: i18n.t('order.placeHolder.serviceMethod'),
  setting: {
    isMulti: true,
    selectType: 2,
    dataSource: [{
      text: i18n.t('goods.component.expressDelivery'),
      value: 1,
    },
    {
      text: i18n.t('task.setting.taskTypeSetting.manage.industryNames.name7'),
      value: 2,
    },
    ],
  },
  orderId: 7,
  isDelete: 0,
  isEdit: 1,
  guideProfessions: [],
  show: true,
  export: true,
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'expressCost',
  displayName: i18n.t('order.freight'),
  formType: 'number',
  defaultValue: null,
  isNull: 0,
  isSearch: 0,
  placeHolder: null,
  setting: {},
  orderId: 0,
  isDelete: 0,
  isEdit: 1,
  guideProfessions: [],
  show: false,
  export: false,
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'serviceCost',
  displayName: i18n.t('order.onSiteServiceFee'),
  formType: 'number',
  defaultValue: null,
  isNull: 1,
  isSearch: 0,
  placeHolder: null,
  setting: {},
  orderId: 0,
  isDelete: 0,
  isEdit: 1,
  guideProfessions: [],
  show: false,
  export: false,
  isGuideData: false,
  guideData: false,
},{
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'isAppointmentSupported',
  displayName: i18n.t('order.isAppointmentSupported'),
  formType: 'select',
  defaultValue: null,
  isNull: 1,
  isSearch: 0,
  placeHolder: null,
  orderId: 0,
  isDelete: 0,
  isEdit: 1,
  guideProfessions: [],
  show: false,
  export: false,
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'hasInventory',
  displayName: i18n.t('order.isHaveInventory'),
  formType: 'select',
  defaultValue: null,
  export: false,
  isNull: 0,
  isSearch: 1,
  operator: 'contain',
  setting: {
    isMulti: false,
    dataSource: [{
      text: i18n.t('common.base.hasState.0'),
      value: 1,
    },
    {
      text: i18n.t('common.base.null'),
      value: 0,
    },
    ],
  },
  orderId: 0,
  isDelete: 0,
  isEdit: 0,
  guideProfessions: [],
  show: false,
  export: false,
  isGuideData: false,
  guideData: false,
}] : [{
  tableName: 'goods',
  fieldName: 'base_info',
  displayName: i18n.t('common.base.basicInfo'),
  formType: 'separator',
  orderId: 0,
  isEdit: 1,
  show: false,
  isDelete: 0,
  isGuideData: true,
  isHidden: 0,
  isNull: 1,
  isSearch: 0,
  isSystem: 0,
  isVisible: true,
  placeHolder: '',
  setting: {
    isAuthorization: false
  },
  subFormFieldList: null,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'commodityName',
  displayName: i18n.t('goods.component.tradeName'),
  formType: 'text',
  defaultValue: null,
  isNull: 0,
  isSearch: 0,
  placeHolder: i18n.t('order.placeHolder.productName'),
  setting: {},
  maxlength: 50,
  orderId: 1,
  isDelete: 0,
  isEdit: 1,
  guideProfessions: [],
  show: true,
  export: true,
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'originType',
  displayName: i18n.t('order.detailV2.productType'),
  formType: 'select',
  defaultValue: null,
  isNull: 0, // 是否开启校验
  isSearch: 1,
  operator: 'eq',
  setting: {
    isMulti: false,
    dataSource: [{
      text: i18n.t('common.base.sparePart'),
      value: 1,
    }, {
      text: i18n.t('common.form.type.material'),
      value: 3,
    }, {
      text: i18n.t('common.label.serviceItem'),
      value: 2,
    }],
  },
  orderId: 2,
  isDelete: 0,
  isEdit: 1, // 是否在编辑页显示
  guideProfessions: [],
  show: true, // 是否在列表页显示
  export: true, // 是否可以导出
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'attachmentList',
  displayName: i18n.t('order.productImg'),
  formType: 'attachment',
  defaultValue: null,
  isNull: 0,
  isSearch: 0,
  placeHolder: null,
  setting: {},
  orderId: 1,
  isDelete: 0,
  isEdit: 1,
  guideProfessions: [],
  show: true,
  export: false,
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'commodityCode',
  displayName: i18n.t('common.base.goodsNo'),
  formType: 'text',
  defaultValue: null,
  isNull: 0,
  isSearch: 0,
  placeHolder: i18n.t('common.fields.productNo.placeholder'),
  setting: {},
  show: true,
  export: true,
  orderId: 0,
  isDelete: 0,
  isEdit: 1,
  disabled: 1,
  guideProfessions: [],
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'originCode',
  displayName: isCloudwarehouse() ? i18n.t('product.materialFields.sn.displayName') : i18n.t('common.part.partNo'),
  formType: 'text',
  defaultValue: null,
  isNull: 0,
  isSearch: 0,
  placeHolder: i18n.t('common.fields.productNo.placeholder'),
  setting: {},
  show: true,
  export: true,
  orderId: 0,
  isDelete: 0,
  isEdit: 1,
  disabled: 1,
  guideProfessions: [],
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'originName',
  displayName: i18n.t('order.serviceItemName'),
  formType: 'text',
  defaultValue: null,
  isNull: 0,
  isSearch: 0,
  placeHolder: i18n.t('order.placeHolder.productName'),
  setting: {},
  orderId: 0,
  isDelete: 0,
  isEdit: 1,
  guideProfessions: [],
  show: false,
  export: false,
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'originCategory',
  displayName: i18n.t('common.fields.catalog.displayName'),
  formType: 'select',
  defaultValue: null,
  export: false,
  isNull: 0,
  isSearch: 1,
  operator: 'eq',
  setting: {
    isMulti: false,
    dataSource: [],
  },
  orderId: 3,
  isDelete: 0,
  isEdit: 1,
  guideProfessions: [],
  show: true,
  export: true,
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'productTypeList',
  displayName: i18n.t('common.label.ralationProductType'),
  formType: 'select',
  defaultValue: null,
  isNull: 1,
  isSearch: 0,
  placeHolder: i18n.t('common.placeholder.selectProductType'),
  setting: {},
  orderId: 10,
  isDelete: 0,
  isEdit: 1,
  guideProfessions: [],
  show: true,
  export: true,
  isGuideData: false,
  guideData: false,
},{
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'purchaseLink',
  displayName: i18n.t('order.purchaseLink'),
  formType: 'text',
  defaultValue: null,
  isNull: 1,
  isSearch: 0,
  placeHolder: i18n.t('order.placeHolder.purchaseLinkText'),
  setting: {},
  orderId: 11,
  isDelete: 0,
  isEdit: 1,
  guideProfessions: [],
  show: true,
  export: true,
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'information',
  displayName: i18n.t('order.productDesc'),
  formType: 'richtext',
  defaultValue: null,
  isNull: 1,
  isSearch: 0,
  placeHolder: i18n.t('order.productDesc'),
  setting: {},
  orderId: 0,
  isDelete: 0,
  isEdit: 1,
  guideProfessions: [],
  show: false,
  export: false,
  isGuideData: false,
  guideData: false,
  maxlength: 200,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'displayName',
  displayName: i18n.t('common.base.createUser'),
  formType: 'user',
  defaultValue: null,
  isNull: 0,
  isSearch: 0,
  placeHolder: null,
  setting: {},
  orderId: 1,
  isDelete: 0,
  isEdit: 0,
  guideProfessions: [],
  show: true,
  export: true,
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'createTime',
  displayName: i18n.t('common.base.createTime'),
  formType: 'datetime',
  defaultValue: null,
  isNull: 0,
  isSearch: 0,
  placeHolder: null,
  setting: {},
  orderId: 12,
  isDelete: 0,
  isEdit: 0,
  guideProfessions: [],
  show: true,
  export: true,
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'putawayStatus',
  displayName: i18n.t('order.productStatus'),
  formType: 'text',
  defaultValue: null,
  isNull: 0,
  isSearch: 0,
  placeHolder: null,
  setting: {},
  orderId: 8,
  isDelete: 0,
  isEdit: 0,
  guideProfessions: [],
  show: true,
  export: true,
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'viewNumber',
  displayName: i18n.t('wiki.list.sortLabel.label1'),
  formType: 'text',
  defaultValue: null,
  isNull: 0,
  isSearch: 0,
  placeHolder: null,
  setting: {},
  orderId: 0,
  isDelete: 0,
  guideProfessions: [],
  show: false,
  export: false,
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  fieldName: 'price_inventory',
  displayName: i18n.t('order.priceInventory'),
  formType: 'separator',
  orderId: 0,
  isEdit: 1,
  show: false,
  isDelete: 0,
  isGuideData: true,
  isHidden: 0,
  isNull: 1,
  isSearch: 0,
  isSystem: 0,
  isVisible: true,
  placeHolder: '',
  setting: {
    isAuthorization: false
  },
  subFormFieldList: null,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'standardDesc',
  displayName: i18n.t('goods.component.isHaveSpecs'),
  formType: 'standardDesc',
  defaultValue: null,
  isNull: 0,
  isSearch: 0,
  placeHolder: '',
  setting: {},
  orderId: 4,
  isDelete: 0,
  isEdit: 1,
  guideProfessions: [],
  show: true,
  export: true,
  isGuideData: false,
  guideData: false,
},
{
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'specsPrice',
  displayName: i18n.t('goods.component.price'),
  formType: 'text',
  defaultValue: null,
  isNull: 0,
  isSearch: 0,
  placeHolder: null,
  setting: {
  },
  orderId: 5,
  isDelete: 0,
  isEdit: 1,
  guideProfessions: [],
  show: false,
  export: false,
  isGuideData: false,
  guideData: false,
  disabled: 1,
},
{
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'price',
  displayName: i18n.t('goods.component.price'),
  formType: 'number',
  defaultValue: null,
  isNull: 0,
  isSearch: 0,
  placeHolder: null,
  setting: {
    limitConig: {
      isLimit: 0,
      type: 1,
      min: 0.01,
      max: 99999
    }
  },
  orderId: 5,
  isDelete: 0,
  isEdit: 1,
  guideProfessions: [],
  show: true,
  export: true,
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'originPrice',
  displayName: i18n.t('order.serviceItemPrice'),
  formType: 'number',
  defaultValue: null,
  isNull: 0,
  isSearch: 0,
  placeHolder: i18n.t('goods.edit.modifyPrice1'),
  setting: {},
  orderId: 5,
  isDelete: 0,
  isEdit: 1,
  guideProfessions: [],
  show: false,
  export: false,
  isGuideData: false,
  guideData: false,
  disabled: true,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'inventoryNumber',
  displayName: i18n.t('common.part.stock'),
  formType: 'number',
  defaultValue: 0,
  isNull: 0,
  isSearch: 0,
  placeHolder: null,
  setting: {
    // limitConig: {
    //   isLimit: 0,
    //   type: 1,
    //   min: 0,
    //   max: 99999
    // }
  },
  orderId: 6,
  isDelete: 0,
  isEdit: 1,
  guideProfessions: [],
  show: true,
  export: true,
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'saleNumber',
  displayName: i18n.t('order.salesVolume'),
  formType: 'text',
  defaultValue: null,
  isNull: 0,
  isSearch: 0,
  placeHolder: null,
  setting: {},
  orderId: 9,
  isDelete: 0,
  isEdit: 0,
  guideProfessions: [],
  show: true,
  export: true,
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  fieldName: 'logistics_info',
  displayName: i18n.t('common.form.preview.sparepart.colum16'),
  formType: 'separator',
  orderId: 0,
  isEdit: 1,
  id: 11111,
  show: false,
  isDelete: 0,
  isGuideData: true,
  isHidden: 0,
  isNull: 1,
  isSearch: 0,
  isSystem: 0,
  isVisible: true,
  placeHolder: '',
  setting: {
    isAuthorization: false
  },
  subFormFieldList: null,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'serviceType',
  displayName: i18n.t('order.detailV2.serviceMethod'),
  formType: 'select',
  defaultValue: null,
  isNull: 0,
  isSearch: 0,
  placeHolder: i18n.t('order.placeHolder.serviceMethod'),
  setting: {
    isMulti: true,
    selectType: 2,
    dataSource: [{
      text: i18n.t('goods.component.expressDelivery'),
      value: 1,
    },
    {
      text: i18n.t('task.setting.taskTypeSetting.manage.industryNames.name7'),
      value: 2,
    },
    ],
  },
  orderId: 7,
  isDelete: 0,
  isEdit: 1,
  guideProfessions: [],
  show: true,
  export: true,
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'expressCost',
  displayName: i18n.t('order.freight'),
  formType: 'number',
  defaultValue: null,
  isNull: 0,
  isSearch: 0,
  placeHolder: null,
  setting: {},
  orderId: 0,
  isDelete: 0,
  isEdit: 1,
  guideProfessions: [],
  show: false,
  export: false,
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'serviceCost',
  displayName: i18n.t('order.onSiteServiceFee'),
  formType: 'number',
  defaultValue: null,
  isNull: 1,
  isSearch: 0,
  placeHolder: null,
  setting: {},
  orderId: 0,
  isDelete: 0,
  isEdit: 1,
  guideProfessions: [],
  show: false,
  export: false,
  isGuideData: false,
  guideData: false,
},{
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'isAppointmentSupported',
  displayName: i18n.t('order.isAppointmentSupported'),
  formType: 'select',
  defaultValue: null,
  isNull: 1,
  isSearch: 0,
  placeHolder: null,
  orderId: 0,
  isDelete: 0,
  isEdit: 1,
  guideProfessions: [],
  show: false,
  export: false,
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'goods',
  isSystem: 1,
  fieldName: 'hasInventory',
  displayName: i18n.t('order.isHaveInventory'),
  formType: 'select',
  defaultValue: null,
  export: false,
  isNull: 0,
  isSearch: 1,
  operator: 'contain',
  setting: {
    isMulti: false,
    dataSource: [{
      text: i18n.t('common.base.hasState.0'),
      value: 1,
    },
    {
      text: i18n.t('common.base.null'),
      value: 0,
    },
    ],
  },
  orderId: 0,
  isDelete: 0,
  isEdit: 0,
  guideProfessions: [],
  show: false,
  export: false,
  isGuideData: false,
  guideData: false,
}] 

const orderFields = [{
  tableName: 'order',
  isSystem: 1,
  fieldName: 'serviceType',
  displayName: i18n.t('order.detailV2.serviceMethod'),
  formType: 'select',
  defaultValue: null,
  isNull: 0, // 是否开启校验
  isSearch: 1,
  operator: 'eq',
  setting: {
    isMulti: false,
    dataSource: [{
      text: i18n.t('goods.component.expressDelivery'),
      value: 1,
    }, {
      text: i18n.t('task.setting.taskTypeSetting.manage.industryNames.name7'),
      value: 2,
    }],
  },
  orderId: 0,
  isDelete: 0,
  isEdit: 1, // 是否在编辑页显示
  guideProfessions: [],
  show: true, // 是否在列表页显示
  export: true, // 是否可以导出
  isGuideData: false,
  guideData: false,
}, {
  tableName: 'order',
  isSystem: 1,
  fieldName: 'originType',
  displayName: i18n.t('order.detailV2.productType'),
  formType: 'select',
  defaultValue: null,
  isNull: 0, // 是否开启校验
  isSearch: 1,
  operator: 'eq',
  setting: {
    isMulti: false,
    dataSource: [{
      text: i18n.t('common.base.sparePart'),
      value: 1,
    }, {
      text: i18n.t('common.fields.serviceName.displayName'),
      value: 2,
    }],
  },
  orderId: 0,
  isDelete: 0,
  isEdit: 1, // 是否在编辑页显示
  guideProfessions: [],
  show: true, // 是否在列表页显示
  export: true, // 是否可以导出
  isGuideData: false,
  guideData: false,
}, {
  displayName: i18n.t('order.detailV2.orderTime'),
  fieldName: 'orderTime',
  formType: 'datetime',
  export: false,
  isNull: 1,
  isSystem: 1,
  operator: 'between',
  orderId: -2,
  tableName: 'order',
}, {
  tableName: 'order',
  isSystem: 1,
  fieldName: 'payType',
  displayName: i18n.t('common.form.preview.pay.payMethod'),
  formType: 'select',
  defaultValue: null,
  isNull: 0, // 是否开启校验
  isSearch: 1,
  operator: 'eq',
  setting: {
    isMulti: false,
    dataSource: [{
      text: i18n.t('order.detailV2.weChatPayment'),
      value: 1,
    }, {
      text: i18n.t('order.detailV2.alipayPayment'),
      value: 2,
    }, {
      text: i18n.t('order.listV2.limitSettlement'),
      value: 3,
    }],
  },
  orderId: 0,
  isDelete: 0,
  isEdit: 1, // 是否在编辑页显示
  guideProfessions: [],
  show: true, // 是否在列表页显示
  export: true, // 是否可以导出
  isGuideData: false,
  guideData: false,
},
//退款售后高级审批加一个筛选条件
{
  tableName: 'order',
  isSystem: 1,
  fieldName: 'refundState',
  displayName: '退款状态',
  formType: 'select',
  defaultValue: null,
  isNull: 0, // 是否开启校验
  isSearch: 1,
  operator: 'eq',
  setting: {
    isMulti: false,
    dataSource: [{
      text: '待审批',
      value: 0,
    }, {
      text: '已退款',
      value: 1,
    },{
      text: '已拒绝',
      value: 2,
    }],
  },
  orderId: 0,
  isDelete: 0,
  isEdit: 0, // 是否在编辑页显示
  guideProfessions: [],
  show: false, // 是否在列表页显示
  export: false, // 是否可以导出
  isGuideData: false,
  guideData: false,
}]

const formatFields = (configFields) => {
  let obj = {};

  const Fields = configFields.map(field => {
    return Object.assign({
      ...field,
      isNull: 1,
      formType: field.formType,
      originalFormType: field.formType,
    });
  }).sort((a, b) => a.orderId - b.orderId)
    .reduce((item, next) => {
      // 过滤掉相同fieldName的字段 去重
      obj[next.fieldName] ? '' : obj[next.fieldName] = true && item.push(next);
      return item;
    }, []);

  return Fields;
}

const otherFields = [
  {
    tableName: 'goods',
    isSystem: 1,
    fieldName: 'specsDesc',
    displayName: i18n.t('goods.component.productSpecifications'),
    formType: 'specsDesc',
    defaultValue: null,
    isNull: 0,
    isSearch: 0,
    placeHolder: '',
    setting: {},
    orderId: 4,
    isDelete: 0,
    isEdit: 1,
    guideProfessions: [],
    show: true,
    export: true,
    isGuideData: false,
    guideData: false,
  }, 
  {
    tableName: 'goods',
    isSystem: 1,
    fieldName: 'standard',
    displayName: i18n.t('goods.component.specificationDesc'),
    formType: 'standard',
    defaultValue: null,
    isNull: 0,
    isSearch: 0,
    placeHolder: '',
    setting: {},
    orderId: 10,
    isDelete: 0,
    isEdit: 1,
    guideProfessions: [],
    show: true,
    export: true,
    isGuideData: false,
    guideData: false,
  }, 
]
const pendingApprovalStateList = [
  {
    label:'全部',
    value:''
  },
  {
    label:'待退货审批',
    value:'pendingReturnApproval'
  },
  {
    label:'待退款审批',
    value:'refundApproval'
  }
]
const processedStateList = [
  {
    label:'全部',
    value:''
  },
  {
    label:'待退货',
    value:'toBeReturned'
  },
  {
    label:'待签收',
    value:'toBeSignedFor'
  },

  {
    label:'已退款',
    value:'refunded'
  },
  {
    label:'审批驳回',
    value:'approvalRejected'
  },
]
const afterSalesFields = [
  {
    tableName: 'pendingApproval',
    fieldName: 'afterSalesNo',
    displayName: '售后单编号',
    formType: 'text',
    orderId: 0,
    isEdit: 1,
    show: false,
    isDelete: 0,
    isGuideData: true,
    isHidden: 0,
    isNull: 1,
    isSearch: 0,
    isSystem: 0,
    isVisible: true,
    placeHolder: '',
    setting: {
      isAuthorization: false
    },
    subFormFieldList: null,
  }, 
  {
    tableName: 'pendingApproval',
    fieldName: 'afterSalesType',
    displayName: '售后类型',
    formType: 'text',
    orderId: 1,
    isEdit: 1,
    show: false,
    isDelete: 0,
    isGuideData: true,
    isHidden: 0,
    isNull: 1,
    isSearch: 0,
    isSystem: 0,
    isVisible: true,
    placeHolder: '',
    setting: {
      isAuthorization: false
    },
    subFormFieldList: null,
  }, 
  {
    tableName: 'pendingApproval',
    fieldName: 'reason',
    displayName: '申请原因',
    formType: 'text',
    orderId: 2,
    isEdit: 1,
    show: false,
    isDelete: 0,
    isGuideData: true,
    isHidden: 0,
    isNull: 1,
    isSearch: 0,
    isSystem: 0,
    isVisible: true,
    placeHolder: '',
    setting: {
      isAuthorization: false
    },
    subFormFieldList: null,
  }, 
  {
    tableName: 'pendingApproval',
    fieldName: 'recipient',
    displayName: '收货人',
    formType: 'text',
    orderId: 3,
    isEdit: 1,
    show: false,
    isDelete: 0,
    isGuideData: true,
    isHidden: 0,
    isNull: 1,
    isSearch: 0,
    isSystem: 0,
    isVisible: true,
    placeHolder: '',
    setting: {
      isAuthorization: false
    },
    subFormFieldList: null,
  }, 
  {
    tableName: 'pendingApproval',
    fieldName: 'contactNumber',
    displayName: '联系电话',
    formType: 'text',
    orderId: 4,
    isEdit: 1,
    show: false,
    isDelete: 0,
    isGuideData: true,
    isHidden: 0,
    isNull: 1,
    isSearch: 0,
    isSystem: 0,
    isVisible: true,
    placeHolder: '',
    setting: {
      isAuthorization: false
    },
    subFormFieldList: null,
  }, 
  {
    tableName: 'pendingApproval',
    fieldName: 'logisticsInformation',
    displayName: '物流信息',
    formType: 'text',
    orderId: 7,
    isEdit: 1,
    show: false,
    isDelete: 0,
    isGuideData: true,
    isHidden: 0,
    isNull: 1,
    isSearch: 0,
    isSystem: 0,
    isVisible: true,
    placeHolder: '',
    setting: {
      isAuthorization: false
    },
    subFormFieldList: null,
  }, 
  {
    tableName: 'pendingApproval',
    fieldName: 'state',
    displayName: '状态',
    formType: 'select',
    orderId: 5,
    isEdit: 1,
    show: false,
    isDelete: 0,
    isGuideData: true,
    isHidden: 0,
    isNull: 1,
    isSearch: 1,
    isSystem: 0,
    isVisible: true,
    placeHolder: '',
    setting: {
      isAuthorization: false
    },
    subFormFieldList: null,
  }, 
  {
    tableName: 'pendingApproval',
    fieldName: 'createTime',
    displayName: '创建时间',
    formType: 'datetime',
    orderId: 6,
    isEdit: 1,
    show: false,
    isDelete: 0,
    isGuideData: true,
    isHidden: 0,
    isNull: 1,
    isSearch: 1,
    isSystem: 0,
    isVisible: true,
    placeHolder: '',
    setting: {
      isAuthorization: false
    },
    subFormFieldList: null,
  }, 

]
const searchFieldInfo = [
  {
    tableName: 'pendingApproval',
    fieldName: 'state',
    displayName: '状态',
    formType: 'select',
    orderId: 5,
    isEdit: 1,
    show: false,
    isDelete: 0,
    isGuideData: true,
    isHidden: 0,
    isNull: 1,
    isSearch: 1,
    isSystem: 1,
    operator: 'eq',
    isVisible: true,
    placeHolder: '',
    setting: {
      isAuthorization: false,
      isMulti: false,
      dataSource: [{
        text: "待退货审批",
        value: "pendingReturnApproval",
      }, {
        text: "待退款审批",
        value: 'refundApproval',
      }, {
        text: "待退货",
        value: 'toBeReturned',
      }, {
        text: "待签收",
        value: 'toBeSignedFor',
      }, {
        text: "已退款",
        value: 'refunded',
      }, {
        text: "审批驳回",
        value: 'approvalRejected',
      }],
    },
    subFormFieldList: null,
  }, 
  {
    displayName:"创建时间",
    fieldName: 'creatingTime',
    formType: 'datetime',
    export: false,
    isNull: 1,
    isSystem: 1,
    operator: 'between',
    orderId: 7,
    isSearch: 1,
    tableName: 'pendingApproval',
  },
  {
    displayName:"完成时间",
    fieldName: 'completionTime',
    formType: 'datetime',
    export: false,
    isNull: 1,
    isSystem: 1,
    operator: 'between',
    orderId: 8,
    isSearch: 1,
    tableName: 'pendingApproval',
  },
]
const afterSalesDetailFields = [
  {
    tableName: 'afterSalesDetail',
    fieldName: 'base_info',
    displayName: i18n.t('common.base.basicInfo'),
    formType: 'separator',
    orderId: 0,
    isEdit: 1,
    show: false,
    isDelete: 0,
    isGuideData: true,
    isHidden: 0,
    isNull: 1,
    isSearch: 0,
    isSystem: 0,
    isVisible: true,
    placeHolder: '',
    setting: {
      isAuthorization: false
    },
    subFormFieldList: null,
  },
  {
    tableName: 'afterSalesDetail',
    fieldName: 'afterNo',
    displayName: '售后单编号',
    formType: 'text',
    orderId: 1,
    isEdit: 1,
    show: false,
    isDelete: 0,
    isGuideData: true,
    isHidden: 0,
    isNull: 1,
    isSearch: 1,
    isSystem: 0,
    isVisible: true,
    placeHolder: '',
    setting: {
      isAuthorization: false
    },
    subFormFieldList: null,
  }, 
  {
    tableName: 'afterSalesDetail',
    subFormFieldList: null,
    isSystem: 1,
    fieldName: 'createTime',
    displayName: '申请日期',
    formType: 'datetime',
    defaultValue: null,
    isNull: 0,
    isSearch: 0,
    placeHolder: null,
    setting: {},
    orderId: 2,
    isDelete: 0,
    isEdit: 0,
    guideProfessions: [],
    show: true,
    export: true,
    isGuideData: false,
    guideData: false,
  }, 
  {
    tableName: 'afterSalesDetail',
    fieldName: 'consignee',
    displayName: '收货人',
    formType: 'text',
    orderId: 3,
    isEdit: 1,
    show: false,
    isDelete: 0,
    isGuideData: true,
    isHidden: 0,
    isNull: 1,
    isSearch: 1,
    isSystem: 0,
    isVisible: true,
    placeHolder: '',
    setting: {
      isAuthorization: false
    },
    subFormFieldList: null,
  }, 
  {
    tableName: 'afterSalesDetail',
    fieldName: 'phone',
    displayName: '联系电话',
    formType: 'text',
    orderId: 4,
    isEdit: 1,
    show: false,
    isDelete: 0,
    isGuideData: true,
    isHidden: 0,
    isNull: 1,
    isSearch: 1,
    isSystem: 0,
    isVisible: true,
    placeHolder: '',
    setting: {
      isAuthorization: false
    },
    subFormFieldList: null,
  }, 
  {
    tableName: 'afterSalesDetail',
    fieldName: 'adress',
    displayName: '地址',
    formType: 'text',
    orderId: 5,
    isEdit: 1,
    show: false,
    isDelete: 0,
    isGuideData: true,
    isHidden: 0,
    isNull: 1,
    isSearch: 1,
    isSystem: 0,
    isVisible: true,
    placeHolder: '',
    setting: {
      isAuthorization: false
    },
    subFormFieldList: null,
  }, 
  {
    tableName: 'afterSalesDetail',
    fieldName: 'aftermarketType',
    displayName: '售后类型',
    formType: 'text',
    orderId: 6,
    isEdit: 1,
    show: false,
    isDelete: 0,
    isGuideData: true,
    isHidden: 0,
    isNull: 1,
    isSearch: 1,
    isSystem: 0,
    isVisible: true,
    placeHolder: '',
    setting: {
      isAuthorization: false
    },
    subFormFieldList: null,
  }, 
  {
    tableName: 'afterSalesDetail',
    fieldName: 'afterSalesReasons',
    displayName: '售后原因',
    formType: 'textarea',
    orderId: 7,
    isEdit: 1,
    show: false,
    isDelete: 0,
    isGuideData: true,
    isHidden: 0,
    isNull: 1,
    isSearch: 1,
    isSystem: 0,
    isVisible: true,
    placeHolder: '',
    setting: {
      isAuthorization: false
    },
    subFormFieldList: null,
  }, 
  {
    tableName: 'afterSalesDetail',
    fieldName: 'afterSaleProduct',
    displayName: '退货商品',
    formType: 'subForm',
    orderId: 8,
    isEdit: 0,
    show: false,
    isDelete: 0,
    isGuideData: true,
    isHidden: 0,
    isNull: 1,
    isSearch: 0,
    isSystem: 1,
    isVisible: true,
    placeHolder: '',
    setting: {
      isAuthorization: false
    },
    subFormFieldList: [
      {
        id: 11695,
        fieldName: "commodityNumber",
        displayName: "商品编号",
        formType: "text",
        setting: {},
        orderId: 0,
        isHidden: 0,
        isChildForm: 1,
        parentFieldName: "afterSaleProduct",
        visible: 1,
        revisable: 1,
        placeHolder: "",
        isNull: 1,
        isSearch: 0,
        isAppShow: 0,
        isSystem: 0,
        isVisible: 1,
        affectFormFields: []
    },
      {
        id: 11695,
        fieldName: "name",
        displayName: "商品",
        formType: "text",
        setting: {},
        orderId: 0,
        isHidden: 0,
        isChildForm: 1,
        parentFieldName: "afterSaleProduct",
        visible: 1,
        revisable: 1,
        placeHolder: "",
        isNull: 1,
        isSearch: 0,
        isAppShow: 0,
        isSystem: 0,
        isVisible: 1,
        affectFormFields: []
    },
      {
        id: 11695,
        fieldName: "goodsType",
        displayName: "类型",
        formType: "text",
        setting: {},
        orderId: 0,
        isHidden: 0,
        isChildForm: 1,
        parentFieldName: "afterSaleProduct",
        visible: 1,
        revisable: 1,
        placeHolder: "",
        isNull: 1,
        isSearch: 0,
        isAppShow: 0,
        isSystem: 0,
        isVisible: 1,
        affectFormFields: []
    },
      {
        id: 11695,
        fieldName: "partCount",
        displayName: "数量",
        formType: "text",
        setting: {},
        orderId: 0,
        isHidden: 0,
        isChildForm: 1,
        parentFieldName: "afterSaleProduct",
        visible: 1,
        revisable: 1,
        placeHolder: "",
        isNull: 1,
        isSearch: 0,
        isAppShow: 0,
        isSystem: 0,
        isVisible: 1,
        affectFormFields: []
      },
      {
        id: 11695,
        fieldName: "subtotal",
        displayName: "金额",
        formType: "text",
        setting: {},
        orderId: 0,
        isHidden: 0,
        isChildForm: 1,
        parentFieldName: "afterSaleProduct",
        visible: 1,
        revisable: 1,
        placeHolder: "",
        isNull: 1,
        isSearch: 0,
        isAppShow: 0,
        isSystem: 0,
        isVisible: 1,
        affectFormFields: []
      },
      {
        id: 11695,
        fieldName: "state",
        displayName: "发货状态",
        formType: "text",
        setting: {},
        orderId: 0,
        isHidden: 0,
        isChildForm: 1,
        parentFieldName: "afterSaleProduct",
        visible: 1,
        revisable: 1,
        placeHolder: "",
        isNull: 1,
        isSearch: 0,
        isAppShow: 0,
        isSystem: 0,
        isVisible: 1,
        affectFormFields: []
      }
    ],
  }, 
  {
    tableName: 'afterSalesDetail',
    fieldName: 'base_systemInformation',
    displayName: '系统信息',
    formType: 'separator',
    orderId: 9,
    isEdit: 1,
    id:1221,
    show: false,
    isDelete: 0,
    isGuideData: true,
    isHidden: 0,
    isNull: 1,
    isSearch: 0,
    isSystem: 0,
    isVisible: true,
    placeHolder: '',
    setting: {
      isAuthorization: false
    },
    subFormFieldList: null,
  }, 
  {
    tableName: 'afterSalesDetail',
    fieldName: 'memberName',
    displayName: '申请人',
    formType: 'text',
    orderId: 10,
    isEdit: 1,
    show: false,
    isDelete: 0,
    isGuideData: true,
    isHidden: 0,
    isNull: 1,
    isSearch: 1,
    isSystem: 0,
    isVisible: true,
    placeHolder: '',
    setting: {
      isAuthorization: false
    },
    subFormFieldList: null,
  }, 
  {
    tableName: 'afterSalesDetail',
    fieldName: 'applicationTimeline',
    displayName: '申请时间',
    formType: 'datetime',
    orderId: 11,
    isEdit: 1,
    show: false,
    isDelete: 0,
    isGuideData: true,
    isHidden: 0,
    isNull: 1,
    isSearch: 1,
    isSystem: 0,
    isVisible: true,
    placeHolder: '',
    setting: {
      isAuthorization: false
    },
    subFormFieldList: null,
  }, 
  {
    tableName: 'afterSalesDetail',
    fieldName: 'orderStatus',
    displayName: '订单状态',
    formType: 'text',
    orderId: 12,
    isEdit: 1,
    show: false,
    isDelete: 0,
    isGuideData: true,
    isHidden: 0,
    isNull: 1,
    isSearch: 1,
    isSystem: 0,
    isVisible: true,
    placeHolder: '',
    setting: {
      isAuthorization: false
    },
    subFormFieldList: null,
  }, 
]
const afterOrderFields = [
  {
    tableName: 'afterOrderDetail',
    fieldName: 'orderNum',
    displayName: '订单编号',
    formType: 'text',
    orderId: 1,
    isEdit: 1,
    show: false,
    isDelete: 0,
    isGuideData: true,
    isHidden: 0,
    isNull: 1,
    isSearch: 1,
    isSystem: 0,
    isVisible: true,
    placeHolder: '',
    setting: {
      isAuthorization: false
    },
    subFormFieldList: null,
  }, 
  {
    tableName: 'afterOrderDetail',
    fieldName: 'logisticsState',
    displayName: '订单状态',
    formType: 'text',
    orderId: 2,
    isEdit: 1,
    show: false,
    isDelete: 0,
    isGuideData: true,
    isHidden: 0,
    isNull: 1,
    isSearch: 1,
    isSystem: 0,
    isVisible: true,
    placeHolder: '',
    setting: {
      isAuthorization: false
    },
    subFormFieldList: null,
  }, 
  {
    tableName: 'afterOrderDetail',
    fieldName: 'serviceType',
    displayName: '服务方式',
    formType: 'text',
    orderId: 3,
    isEdit: 1,
    show: false,
    isDelete: 0,
    isGuideData: true,
    isHidden: 0,
    isNull: 1,
    isSearch: 1,
    isSystem: 0,
    isVisible: true,
    placeHolder: '',
    setting: {
      isAuthorization: false
    },
    subFormFieldList: null,
  }, 
  {
    tableName: 'afterOrderDetail',
    fieldName: 'appointmentTime',
    displayName: '预约上门时间',
    formType: 'datetime',
    orderId: 4,
    isEdit: 1,
    show: false,
    isDelete: 0,
    isGuideData: true,
    isHidden: 0,
    isNull: 1,
    isSearch: 1,
    isSystem: 1,
    isVisible: true,
    placeHolder: '',
    setting: {
      isAuthorization: false
    },
    subFormFieldList: null,
  }, 
  {
    tableName: 'afterOrderDetail',
    fieldName: 'name',
    displayName: '收货人',
    formType: 'text',
    orderId: 5,
    isEdit: 1,
    show: false,
    isDelete: 0,
    isGuideData: true,
    isHidden: 0,
    isNull: 1,
    isSearch: 1,
    isSystem: 1,
    isVisible: true,
    placeHolder: '',
    setting: {
      isAuthorization: false
    },
    subFormFieldList: null,
  }, 
  {
    tableName: 'afterOrderDetail',
    fieldName: 'address',
    displayName: '详细地址',
    formType: 'text',
    orderId: 6,
    isEdit: 1,
    show: false,
    isDelete: 0,
    isGuideData: true,
    isHidden: 0,
    isNull: 1,
    isSearch: 1,
    isSystem: 1,
    isVisible: true,
    placeHolder: '',
    setting: {
      isAuthorization: false
    },
    subFormFieldList: null,
  }, 
  {
    tableName: 'afterOrderDetail',
    fieldName: 'createTime',
    displayName: '下单时间',
    formType: 'datetime',
    orderId: 7,
    isEdit: 1,
    show: false,
    isDelete: 0,
    isGuideData: true,
    isHidden: 0,
    isNull: 1,
    isSearch: 1,
    isSystem: 1,
    isVisible: true,
    placeHolder: '',
    setting: {
      isAuthorization: false
    },
    subFormFieldList: null,
  }, 
  {
    tableName: 'afterOrderDetail',
    fieldName: 'payTime',
    displayName: '付款时间',
    formType: 'datetime',
    orderId: 7,
    isEdit: 1,
    show: false,
    isDelete: 0,
    isGuideData: true,
    isHidden: 0,
    isNull: 1,
    isSearch: 1,
    isSystem: 1,
    isVisible: true,
    placeHolder: '',
    setting: {
      isAuthorization: false
    },
    subFormFieldList: null,
  }, 
  {
    tableName: 'afterOrderDetail',
    fieldName: 'payType',
    displayName: '支付方式',
    formType: 'text',
    orderId: 8,
    isEdit: 1,
    show: false,
    isDelete: 0,
    isGuideData: true,
    isHidden: 0,
    isNull: 1,
    isSearch: 1,
    isSystem: 1,
    isVisible: true,
    placeHolder: '',
    setting: {
      isAuthorization: false
    },
    subFormFieldList: null,
  }, 
  {
    tableName: 'afterOrderDetail',
    fieldName: 'payAmount',
    displayName: '实付金额（元）',
    formType: 'text',
    orderId: 9,
    isEdit: 1,
    show: false,
    isDelete: 0,
    isGuideData: true,
    isHidden: 0,
    isNull: 1,
    isSearch: 1,
    isSystem: 1,
    isVisible: true,
    placeHolder: '',
    setting: {
      isAuthorization: false
    },
    subFormFieldList: null,
  }, 
  {
    tableName: 'afterOrderDetail',
    fieldName: 'remarks',
    displayName: '买家留言',
    formType: 'text',
    orderId: 10,
    isEdit: 1,
    show: false,
    isDelete: 0,
    isGuideData: true,
    isHidden: 0,
    isNull: 1,
    isSearch: 1,
    isSystem: 1,
    isVisible: true,
    placeHolder: '',
    setting: {
      isAuthorization: false
    },
    subFormFieldList: null,
  }, 
  {
    tableName: 'afterOrderDetail',
    fieldName: 'afterNo',
    displayName: '订单备注',
    formType: 'text',
    orderId: 11,
    isEdit: 1,
    show: false,
    isDelete: 0,
    isGuideData: true,
    isHidden: 0,
    isNull: 1,
    isSearch: 1,
    isSystem: 1,
    isVisible: true,
    placeHolder: '',
    setting: {
      isAuthorization: false
    },
    subFormFieldList: null,
  },
  {
    tableName: 'afterSalesDetail',
    fieldName: 'base_systemInformation',
    displayName: '发货信息',
    formType: 'separator',
    orderId: 12,
    isEdit: 1,
    id:1221,
    show: false,
    isDelete: 0,
    isGuideData: true,
    isHidden: 0,
    isNull: 1,
    isSearch: 0,
    isSystem: 1,
    isVisible: true,
    placeHolder: '',
    setting: {
      isAuthorization: false
    },
    subFormFieldList: null,
  },  
  {
    tableName: 'afterOrderDetail',
    fieldName: 'afterNo',
    displayName: '发货方式',
    formType: 'text',
    orderId: 13,
    isEdit: 1,
    show: false,
    isDelete: 0,
    isGuideData: true,
    isHidden: 0,
    isNull: 1,
    isSearch: 1,
    isSystem: 1,
    isVisible: true,
    placeHolder: '',
    setting: {
      isAuthorization: false
    },
    subFormFieldList: null,
  }, 
  {
    tableName: 'afterOrderDetail',
    fieldName: 'firstLogisticsTime',
    displayName: '首次发货时间',
    formType: 'datetime',
    orderId: 14,
    isEdit: 1,
    show: false,
    isDelete: 0,
    isGuideData: true,
    isHidden: 0,
    isNull: 1,
    isSearch: 1,
    isSystem: 1,
    isVisible: true,
    placeHolder: '',
    setting: {
      isAuthorization: false
    },
    subFormFieldList: null,
  }, 
]
export {
  goodsFields,
  orderFields,
  formatFields,
  otherFields,
  afterSalesFields,
  pendingApprovalStateList,
  processedStateList,
  afterSalesDetailFields,
  searchFieldInfo,
  afterOrderFields
}
