<template>
  <div class="customer-product-table-container">
    <el-table
      v-loading="listLoading"
      stripe
      :data="callList"
      :highlight-current-row="false"
      header-row-class-name="common-list-table-header__v2"
      v-bind="extraAttrs">
      <template slot="empty">
        <BaseListForNoData
          v-show="!listLoading"
          table-type="smallTable"
          :notice-msg="$t('common.base.tip.noData')"
        ></BaseListForNoData>
      </template>

      <el-table-column v-for="column in columns" :key="column.field" :label="column.label" :prop="column.field" :width="column.width"
                       :class-name="column.field == 'name' ? 'customer-product-name-superscript-td' : ''" :sortable="column.sortable" :show-overflow-tooltip="column.field !== 'name'" :align="column.align">
        <template slot-scope="scope">
          <template v-if="column.field === 'linkmanName'">
            {{scope.row.customerInfo && scope.row.customerInfo.linkmanName }}
          </template>
          <template v-else-if="column.field === 'agentname'">
            {{scope.row.agentInfo && scope.row.agentInfo.agentName}}
          </template>
          <template v-if="column.field === 'remarkStatus'">
            {{scope.row[column.field] == 1 ? $t('customer.detail.customerCallTable.remarkStatus.resolved') : (scope.row[column.field] == 0 ? $t('customer.detail.customerCallTable.remarkStatus.unsolved')  : '')}}
          </template>
          <template v-else-if="column.field === 'callType'">
            {{fmt_callType(scope.row)}}
          </template>
          <template v-else-if="column.field === 'solveStatus'">
            <span>{{{'UNSOLVED': $t('common.base.notResolved'),'SOLVED': $t('common.base.resolved'),}[scope.row[column.field]]}}</span>
          </template>
          <template v-else>
            {{scope.row[column.field]}}
          </template>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <BaseAddOnPagination :paginationInfo="paginationInfo" @pageJump="jump" @sizeChange="handleSizeChange" />
  </div>
</template>

<script>
import { getRecordList } from '@src/api/CallCenterApi.js'
export default {
  name: 'customer-call-table',
  props: {
    shareData: {
      type: Object,
      default: () => ({})
    },
    extraAttrs: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      callList: [],
      columns: this.buildColumns(),
      paginationInfo: {
        pageSize: 10,
        pageNum: 1,
        totalItems: 0
      },
      listLoading: true
    }
  },
  computed: {
    customerId() {
      return this.shareData.customer ? this.shareData.customer.id : '';
    },
  },
  mounted() {
    this.fetchData()
  },
  methods: {
    jump(pN) {
      this.paginationInfo.pageNum = pN
      this.fetchData()
    },
    handleSizeChange(pageSize) {
      this.paginationInfo.pageSize = pageSize;
      this.paginationInfo.pageNum = 1;
      this.fetchData();
    },
    fetchData() {
      this.listLoading = true;
      const params = {
        customerId: this.customerId,
        page: this.paginationInfo.pageNum,
        pageSize: this.paginationInfo.pageSize
      }

      getRecordList(params)
        .then(res => {
          this.callList = res.result && res.result.list
          this.paginationInfo.totalItems = res.result.total
        })
        .catch(e => console.error('fetchData product caught e', e))
        .finally(() => {
          this.listLoading = false
        })
    },
    buildColumns() {
      return [
        {
          label: this.$t('customer.detail.customerCallTable.table.label.recordId'),
          field: 'recordId',
          show: true
        },
        {
          label: this.$t('customer.detail.customerCallTable.table.label.linkmanName'),
          field: 'linkmanName',
          show: true
        },
        {
          label: this.$t('customer.detail.customerCallTable.table.label.agentName'),
          field: 'agentName',
          show: true
        },
        {
          label: this.$t('customer.detail.customerCallTable.table.label.ringTime'),
          field: 'ring',
          width:'100px',
          show: true
        },
        {
          label: this.$t('customer.detail.customerCallTable.table.label.callType'),
          field: 'callType',
          show: true
        },
        {
          label: this.$t('customer.detail.customerCallTable.table.label.consultCategory'),
          field: 'consultName',
          show: true
        },
        {
          label: this.$t('customer.detail.customerCallTable.table.label.remarkStatus'),
          field: 'solveStatus',
          show: true
        }
      ]
    },
    fmt_callType(row){
      // 呼叫类型
      let res = ''
      if(row.callType === 'normal') {
        res = row.state === 'dealing' ? this.$t('callcenter.stage.answeredCall') : this.$t('callcenter.stage.missedCall')
      } else if(row.callType === 'dialout'){
        res = row.state === 'dealing' ? this.$t('callcenter.stage.callOutAnswered') : this.$t('callcenter.stage.calloutNoAnswer')
      }
      return res
    },
  }
}
</script>

<style lang="scss">
.customer-product-table-container {
  padding: 16px;

  .product-link {
    @include fontColor();
  }
}

td.customer-product-name-superscript-td {
  padding: 0 !important;

  & > .cell {
    padding-left: 0 !important;
  }

  & > div {
    height: 43px;
    line-height: 43px !important;
    a {
      display: inline-block;
      height: 43px;
      line-height: 43px;
    }
  }
}
</style>