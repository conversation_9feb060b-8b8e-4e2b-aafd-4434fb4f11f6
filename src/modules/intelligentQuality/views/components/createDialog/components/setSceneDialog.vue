<template>
  <base-modal :title="title" @closed="() => (visible = false)" :show.sync="visible" width="800px">
    <form-item :label="$t('common.base.name')" :is-not-null="true" ref="nameDom">
      <el-input :placeholder="$t('common.placeholder.input2')" v-trim:blur v-model="modalData[nameKey]" :maxlength="20" @input="() => validateName()" show-word-limit></el-input>
    </form-item>
    <form-item :label="'条件配置'" class="normal-set-scene-dialog-condition" :validation="false">
      <template #tips>
        <i class="iconfont icon-info font-12-i color-gray"></i><span class="mar-l-4 color-gray font-12">不设置条件时，该质检规则适用于全部上传内容</span>
      </template>
      <ConditionalConfig ref="conditionalConfigDom" mode="isSceneSet" v-loading="conditionIsLoading" :get-condition-list-fnc="getUseStoreConditionSettingArr"></ConditionalConfig>
    </form-item>
    <form-item label="质检要求" :is-not-null="true" ref="desDom">
      <template #operation>
        <div class="link-text" @click="openTrainingDialog">{{ $t('robot.text48') }}</div>
      </template>
      <template #tips>
        <i class="iconfont icon-info font-12-i color-gray"></i><span class="mar-l-4 color-gray font-12">{{ $t('robot.text65') }}</span>
      </template>
      <div class="pad-12 bg-gray">
        <el-input type="textarea" :placeholder="$t('robot.text45')" v-trim:blur v-model="modalData[desKey]" @input="() => validateDes()" :maxlength="500" show-word-limit></el-input>
      </div>
    </form-item>
    <form-item :label="$t('robot.text47')">
      <div class="pad-12 bg-gray normal-content-box">
        <p>{{ $t('robot.text53') }}</p>
        <p>·{{ $t('robot.text54') }}<el-input-number class="mar-l-8 mar-r-8" v-model="modalData[persentKey]" controls-position="right" :precision="0" :min="0" :max="100"></el-input-number>%</p>
        <p>·{{ $t('robot.text55') }}<el-input-number class="mar-l-8 mar-r-8" v-model="modalData[imgKey]" controls-position="right" :precision="0" :min="0" :max="20"></el-input-number>张;</p>
      </div>
    </form-item>

    <div slot="footer" class="dialog-footer">
      <el-button type="plain-third" @click="visible = false">{{ $t('common.base.cancel') }}</el-button>
      <el-button type="primary" @click="onSubmit">{{ $t('common.base.save') }}</el-button>
    </div>

    <SceneTrainingDialog ref="sceneTrainingDialogDom" @submit="updateDes"></SceneTrainingDialog>
  </base-modal>
</template>
<script>
import { defineComponent, ref, computed, toRef } from 'vue';
import { t } from '@src/locales';
import ConditionalConfig from '@src/modules/trigger/view/setting/components/trigger_condition/components/conditionalConfig.vue';
import SceneTrainingDialog from './sceneTrainingDialog.vue';
import { useStoreConditionSettingArr, useStateConditionSetting, useStateSceneSet } from '../mock';
import TriggerState from '@src/modules/trigger/view/setting/util/state.js';
import { cloneDeep, flattenDeep, some } from 'lodash';
import { isEmpty } from 'pub-bbx-utils';
import { message } from '@src/util/message'

export default defineComponent({
  name: 'SetSceneDialog',
  props: {
    fieldList: {
      type: Array,
      default: () => [],
    },
    dataList: {
      type: Array,
      default: () => [],
    }
  },
  provide() {
    return {
      isViewMode: false,
      isDemo: false,
    };
  },
  components: {
    ConditionalConfig,
    SceneTrainingDialog,
  },
  setup(props, { emit }) {
    const dataList = toRef(props,'dataList')
    const setSceneRulerItemDom = ref([]);
    const visible = ref(false);
    const modalType = ref(0);
    const modalData = ref({});
    const conditionalConfigDom = ref(null);
    const nameDom = ref(null);
    const desDom = ref(null);
    const defaultConditionList = ref([])
    let nowGroupIndex = -1;
    let nowConditionIndex = -1;

    const conditionIsLoading = computed(() => {
      return useStoreConditionSettingArr.dataIsLoading;
    });

    const allExistConditions = computed(()=> {
      const flattenDeepCurrentConditionList = flattenDeep(defaultConditionList.value)
      return dataList.value.reduce((acc, item)=> {
        item.sceneList.map(sceneItem=> {
          const sceneItemCondition = flattenDeep(sceneItem.condition)
          const filterSceneItemCondition = sceneItemCondition.filter(rsItem=> {
            return !flattenDeepCurrentConditionList.some(existItem=> rsItem.toEnName === existItem.toEnName && JSON.stringify(rsItem.value) == JSON.stringify(existItem.value))
          })
          acc.push(...filterSceneItemCondition)
        })
        return acc
      }, [])
    })

    const { getUseStoreConditionSettingArr } = useStateConditionSetting();

    const useStateSceneSetData = useStateSceneSet();

    function initModalData(info = {}) {
      modalData.value = {
        [useStateSceneSetData.nameKey]: '',
        [useStateSceneSetData.conditionKey]: [],
        [useStateSceneSetData.desKey]: '',
        [useStateSceneSetData.persentKey]: 50,
        [useStateSceneSetData.imgKey]: 1,
        ...info,
      };
    }
    const title = computed(() => {
      return modalType.value === 0 ? '添加质检规则' : '编辑质检规则';
    });
    const nowCoinditionValue = computed(() => {
      return TriggerState.options?.conditionList;
    });
    const conditionList = ref([]);
    function createConditionInfo() {
      return [];
    }
    function createCondition() {
      conditionList.value.push(createConditionInfo());
    }

    function deleteGroup(index) {
      conditionList.value.splice(index, 1);
    }

    function updateDes(info) {
      modalData.value[useStateSceneSetData.desKey] = info;
    }

    function open(info = null, index = -1, index2 = -1) {
      clearValidate()
      initModalData(info);
      visible.value = true;
      nowGroupIndex = index;
      nowConditionIndex = index2;
      if(index2 > -1){
        modalType.value = 1
      }else{
        modalType.value = 0
      }
      // const cloneDeepCondition = info?.condition ?  : []
      defaultConditionList.value = index2 > -1 ? cloneDeep(info?.condition): []  

      conditionalConfigDom.value.initData(index2 > -1 ? cloneDeep(info.condition) : null);
    }
    function validateName(errArr = [], isClear = false) {
      if (!isClear && isEmpty(modalData.value[useStateSceneSetData.nameKey]?.trim())) {
        let errMessage = t('common.placeholder.inputSomething', { data1: t('common.base.name') });
        nameDom.value.errMessage = errMessage;
        errArr.push(errMessage);
        return false;
      }
      nameDom.value.errMessage = '';
      return true;
    }

    function validateDes(errArr = [], isClear = false) {
      if (!isClear && isEmpty(modalData.value[useStateSceneSetData.desKey]?.trim())) {
        let errMessage = t('common.placeholder.inputSomething', { data1: t('robot.text46') });
        desDom.value.errMessage = errMessage;
        errArr.push(errMessage);
        return false;
      }
      desDom.value.errMessage = '';
      return true;
    }

    async function validate() {
      let errArr = [];
      validateName(errArr);
      validateDes(errArr);
      if (errArr?.length)
        return Promise.resolve({
          pass: false,
          err: errArr,
        });
      return Promise.resolve({
        pass: true,
      });
    }

    function clearValidate() {
      validateName([], true);
      validateDes([], true);
    }

    const sceneTrainingDialogDom = ref(null);

    function openTrainingDialog() {
      if (!validateDes()) return;
      sceneTrainingDialogDom.value.open(modalData.value[useStateSceneSetData.desKey]); 
    }

    function packModalValue() {
      modalData.value[[useStateSceneSetData.conditionKey]] = cloneDeep(conditionalConfigDom.value.conditionDataList);
      modalData.value[[useStateSceneSetData.conditionDesKey]] = cloneDeep(conditionalConfigDom.value.conditionDataListForDes);
    }

    function validateCondition(formConditionList = [], dataList = []) {
      
      const flattenDeepAllFormConditionList = flattenDeep(formConditionList).filter(item=> item.toEnName)

      const currentExist = some(flattenDeepAllFormConditionList, function(v, index) {
        const prevIndex = flattenDeepAllFormConditionList.findIndex(item=> item.toEnName === v.toEnName && JSON.stringify(item.value) == JSON.stringify(v.value))
        return prevIndex !== index
      })

      const checkAllFormConditionAndDefaultConditionExist = flattenDeepAllFormConditionList.some(item=> {
          const notEqDefaultCondition = defaultConditionList.value.some(sItem=> sItem.toEnName === item.toEnName && JSON.stringify(sItem.value) == JSON.stringify(item.value))
          const notEqAllExistConditions = allExistConditions.value.some(sItem=> sItem.toEnName === item.toEnName && JSON.stringify(sItem.value) == JSON.stringify(item.value))
         return !notEqDefaultCondition && notEqAllExistConditions
      })


      // 判断是否有空条件（也就是全部条件）
      // const checkIsEmptyCondition = dataList.some(item=> {
      //     if(Array.isArray(item.sceneList) && item.sceneList) {
      //       return item.sceneList.some(sceneListItem => isEmpty(sceneListItem.condition))
      //     }
      // }) && isEmpty(formConditionList)
      // return currentExist || checkAllFormConditionAndDefaultConditionExist || checkIsEmptyCondition
      
      return currentExist || checkAllFormConditionAndDefaultConditionExist
    }
    async function onSubmit() {
      
      let validateRes = await validate();
      if (!validateRes.pass) return;

      try {
        conditionalConfigDom.value.rerenderContent()
      } catch (error) {
        console.warn('conditionalConfigDom.value.rerenderContent error:', error)
      }

      packModalValue();

      // 质检的配置数据
      let dataList = cloneDeep(props.dataList)
      // 如果当前是新增(-1)，则将当前质检配置添加到数据列表中, 否则 dataList 是外面传入的数据，是没有当前质检配置的数据
      if (nowConditionIndex == -1) {
        dataList[nowGroupIndex].sceneList.push(modalData.value)
      }

      const validateConditionRes = validateCondition(modalData.value.condition, dataList)
      if(validateConditionRes) return message.error('当前条件已存在质检要求，请勿重复设置') 

      visible.value = false;
      emit('submit', cloneDeep(modalData.value), nowGroupIndex, nowConditionIndex);
    }
    return {
      visible,
      title,
      conditionList,
      createCondition,
      deleteGroup,
      setSceneRulerItemDom,
      open,
      modalData,
      openTrainingDialog,
      onSubmit,
      conditionIsLoading,
      conditionalConfigDom,
      getUseStoreConditionSettingArr,
      nowCoinditionValue,
      nameDom,
      desDom,
      validate,
      updateDes,
      sceneTrainingDialogDom,
      validateName,
      validateDes,
      ...useStateSceneSetData,
    };
  },
});
</script>
<style lang="scss" scoped>
@import '../index.scss';
.normal-content-box {
  line-height: 22px;
}
</style>
<style lang="scss">
.normal-set-scene-dialog-condition {
  .conditional-config .conditional-config-content .conditional-config-set .conditional-config-set-content {
    background-color: $bg-color-l1;
    border: none;
    border-radius: 4px;
  }
}
.normal-set-scene__tips{
  color: #8C8C8C;
  font-size: 12px;
  display: flex;
  align-items: center;
}
</style>
