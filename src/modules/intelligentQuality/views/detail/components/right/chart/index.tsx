/* vue */
import { defineComponent, onMounted, PropType, ref, watch, computed, onBeforeUnmount, toRefs } from 'vue'
/* component */
import NoDataViewNew from '@src/component/common/NoDataViewNew'
import VShowBlock from '@src/modules/report/managementReport/charts/components/common/ChartsSearch/components/VShowBlock.tsx'
/* components */
import * as echarts from 'echarts5'
/* type */
import { executeDate } from '@src/modules/intelligentQuality/type/index.ts'
/* hook */
import { useCurrentInstance } from '@src/modules/intelligentQuality/hooks/index.ts'
/* http */
import { getQualityRunList } from '@src/api/intelligentQuality.js'
/* utils */
import { message, confirm } from '@src/util/message'
import { useDebounceFn } from "@vueuse/core";
import { getThemeColor } from "@src/util/business/color"
import EventBus from '@src/util/eventBus'
/* enum */
import { 
	QualityCountTypeEnum, 
	QualityEventBusEnum, 
	QualityPeriodTypeEnum, 
	QualityTimeTypeEnum
} from "@src/modules/intelligentQuality/views/detail/model/enum"
/* scss */
import '@src/modules/intelligentQuality/views/detail/components/right/chart/index.scss'
import MsgModel from '@model/MsgModel'

export default defineComponent({
	name: 'executeEcharts',
	props: {
		// 执行统计动作信息
		countType: {
			type: String as PropType<QualityCountTypeEnum>,
			default: ''
		},
		// 机器人编号
		robotId: {
			type: Number,
		},
		timeType: {
			type: Number as PropType<QualityTimeTypeEnum>,
			default: QualityTimeTypeEnum.LAST7
		}
	},
	setup(props, { emit, expose }) {
    
		const { timeType } = toRefs(props)
		const periodTypeText = ref<string>('最近7天')
		const chartRef = ref<HTMLDivElement | null>(null)
		let chartInstance = ref<any>(null)
		const loading = ref<boolean>(false)
		const [currentCtx] = useCurrentInstance()

		const echartsData = ref([])

		const showEchartsTitle = computed(() => {
			return '质检次数统计'
			
		})

		let options = {
			tooltip: {
				trigger: 'axis',
				axisPointer: {
					type: 'cross'
				}
			},
			grid: {
				containLabel: true,
				left: '12px',
				right: '12px',
				bottom: '0px'
			},
			xAxis: {
				type: 'category',
				axisTick: {
					show: false
				},
				data: []
			},
			yAxis: {
				type: 'value',
				name: '单位(次)',
				axisLine: {
					show: false
				},
				splitLine: {
					show: true,
					lineStyle: {
						type: 'dashed'
					}
				}
			},
			series: [
				{
					data: [],
					type: 'line',
					lineStyle: {
						color: '#165DFF'
					},
					itemStyle: {
						color: '#165DFF'
					}
				}
			],
			graphic: {
				type: 'text',     // 类型：文本
				left: 'center',
				top: 'middle',
				silent: true,     // 不响应事件
				invisible: true,   // 有数据就隐藏
				style: {
					fill: '#9d9d9d',
					fontWeight: 'bold',
					text: '暂无数据',
					fontFamily: 'Microsoft YaHei',
					fontSize: '25px'
				}
			}
		}
		const updateOptions = () => {
			options.xAxis.data = echartsData.value.map((item: any) => item.date) as any
			options.series[0].data = echartsData.value.map((item: any) => item.num) as any
			options.graphic.invisible = echartsData.value.length > 0
		}


		const initChart = () => {
			if (chartRef.value) {
				chartInstance.value = echarts.init(chartRef.value);
				updateChart()
			}
		}
		const updateChart = () => {
			if (chartInstance.value) {
				options && chartInstance.value.setOption(options);
			}
		};

		const resize = () => {
			if (chartInstance.value) {
				chartInstance.value.resize({ animation: { duration: 300 } });
			}
		}
		const debouncedResize = useDebounceFn(resize, 300);

		const getEchartsData = async () => {
			try {
				loading.value = true
			  
				const res = await getQualityRunList({
					robotId: props.robotId,
					countType: props.countType,
					timeType: props.timeType
				})

				const isFail = MsgModel.isFail(res)
				if (isFail) {
					const message = MsgModel.getMessage(res)
					message.error(message)
					return
				}

				echartsData.value = res.data
				updateOptions()
				updateChart()

				setTimeout(() => {
					debouncedResize()
					setTimeout(() => {
						loading.value = false
					}, 100)
				})

			} catch (error) {
				console.error(error)
			}
		}

		watch(() => props.timeType, () => {
			getEchartsData()
		})

		watch(() => props.countType, () => {
			getEchartsData()
		})

		watch(() => loading.value, (newLoading) => {
			if (newLoading) {
				chartInstance.value.showLoading({
					text: '加载中...',
					color: getThemeColor(),
					textColor: '#fff',
					maskColor: 'rgba(255, 255, 255, 0.8)',
					zlevel: 0
				});
			} else {
				chartInstance.value.hideLoading();
			}
		})
    
		expose({
			draw: debouncedResize
		})

		onMounted(() => {
			initChart()
			updateChart()
			window.addEventListener("resize", debouncedResize);
			window.addEventListener('visibilitychange', debouncedResize)
		})

		onBeforeUnmount(() => {
			chartInstance.value.dispose();
			window.removeEventListener("resize", debouncedResize);
		})

		return {
			getEchartsData,
			showEchartsTitle,
			timeType,
			chartRef,
			echartsData,
			loading,
			periodTypeText,
			updateChart,
			debouncedResize
		}
	},
	data() {
		return {
			resizeObserver: null as ResizeObserver | null
		}
	},
	computed: {
		attrs(): Record<string, any> {
      return {
        directives: [
          {
            name: 'loading',
            value: this.loading
          }
        ]
      }
    },
	},
	mounted() {
		this.getEchartsData().then(() => {
			this.$nextTick(() => {
				this.rerenderChart()
			})
		})
		EventBus.$on(QualityEventBusEnum.Resize, this.rerenderChart)
	},
	beforeDestroy() {
		EventBus.$off(QualityEventBusEnum.Resize, this.rerenderChart)
	},
	methods: {
		rerenderChart() {
			this.debouncedResize()
		},
		renderChart() {
			return (
				<div 
					ref="chartRef" 
					class="exe-chart" 
					v-show={this.echartsData.length > 0}
				>
				</div>
			)
		},
		renderNoData() {
			const isShow = this.echartsData.length == 0 && !this.loading
			return (
				<NoDataViewNew v-show={isShow} />
			)
		},
		renderContent() {
			return (
				<div class="exe-content">
					{this.renderChart()}
					{this.renderNoData()}
				</div>
			)
		}
	},
	render() {
		return (
			<div class="execute-echarts" {...this.attrs}>
				<div class="exe-header">
					<div class="exe-title">
						{this.showEchartsTitle}
					</div>
				</div>
				{this.renderContent()}
			</div>
		)
	}
})