<template>
  <div class="project-detail-gante">
    <div class="gante">
      <div class="add-btn">
        <el-popover
          placement="bottom-start"
          width="140"
          trigger="hover">
          <div v-for="(state,index) in stateList" :key="index" class="demoBlock">
            <span class="demoLabel">{{ state.label }}</span> <span :style="{background:state.color}"></span>
          </div>
          <el-button is-plain slot="reference"><i class="iconfont icon-tip"></i>&nbsp;{{t('common.projectManage.gantt.demoDescription')}}</el-button>
        </el-popover>
      </div>
    </div>
  </div>
</template>
<script>
import { projectTaskGantt } from '@src/api/ProjectManage.ts';
import { t } from '@src/locales';
import { useOpenTab } from '@src/modules/projectManage/taskManageList/hooks/useOpenTab';
import { fmt_datetime } from '@src/filter/fmt'

const { openTaskManageViewTab, openProjectViewTab } = useOpenTab();
export default {
  name: 'project-detail-gantt',
  props: {
    templateId: {
      type: String | Number,
      default: '',
    },
    projectId: {
      type: String | Number,
      default: '',
    },
    direction: {
      type: String | Number,
      default: '',
    },
  },
  data() {
    return {
      GANTT: null,
      th_data: {
        taskName: {
          value: t('common.fields.taskName.displayName'),
          width: 150,
          showToast: true,
          listen_click: true,
        },
        progress: {
          value: t('common.base.progress'),
          width: 140,
        },
        planStartTime: {
          value: t('common.form.type.planStartTime'),
          width: 120,
          showToast: true,
          chooseTime: true,
          time_mode: 1,
          sort_type: 'asc',
        },
        planEndTime: {
          value: t('common.form.type.planEndTime'),
          width: 120,
          showToast: true,
          chooseTime: true,
          time_mode: 2,
          sort_type: 'asc',
        },
        duration: {
          value: t('common.projectManage.gantt.duration'),
          width: 80,
          showToast: true,
          sort_type: 'asc',
        },
        actualStartTime: {
          value: t('common.projectManage.actualStartTime'),
          width: 120,
          showToast: true,
          chooseTime: true,
          time_mode: 1,
          sort_type: 'asc',
        },
        actualEndTime: {
          value:t('common.projectManage.actualEndTime'),
          width: 120,
          showToast: true,
          chooseTime: true,
          time_mode: 2,
          sort_type: 'asc',
        },
      },
      ganttData: [],
      stateList:[
        {
          label:t('common.base.planTime'),
          color:'#FFE58F'
        },
        {
          label:t('common.base.completeTime'),
          color:'#13C2C2'
        },
        {
          label:t('common.base.usualStatus.canceled'),
          color:'#EBEDF0'
        },
      ]
    };
  },
  mounted() {
    this.init();
    window.addEventListener('resize', this.initGantt,false)
  },
  destroyed(){
    window.removeEventListener('resize',this.initGantt)
  },
  watch: {
    direction() {
      setTimeout(() => {
        this.initGantt();
      }, 200);
    },
  },
  methods: {
    format(time, mode) {
      let year = time.getFullYear();
      let month = time.getMonth() + 1;
      let day = time.getDate();
      if (mode == 1) {
        return year + '-' + (month < 10 ? '0' + month : month) + '-' + (day < 10 ? '0' + day : day);
      } else if (mode == 2) {
        return year + '-' + (month < 10 ? '0' + month : month);
      } else if (mode == 3) {
        return day < 10 ? '0' + day : day;
      } else if (mode == 4) {
        return month < 10 ? '0' + month : month;
      } else if (mode == 5) {
        return year;
      }
    },
    init() {
      projectTaskGantt({ projectId: this.projectId, projectTypeId: this.templateId, pageNo: 1, pageSize: 500,filterPlanStartOrEndTimeIsNull:true }).then(res => {
        this.ganttData = (res?.data?.list || [])
          .filter(item => item.planStartTime&&item.planEndTime)
          .map(item => {
            const resultDate = item.completeTime ? new Date(item.completeTime) :  new Date();
            const actualEndTime = new Date(resultDate.setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000 - 1);
            return {
              // ...item,
              params: {
                ...item,
                planStartTime: this.format(new Date(new Date(item.planStartTime).setHours(0, 0, 0, 0)), 1),
                planEndTime: this.format(new Date(new Date(item.planEndTime).setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000 - 1), 1),
                actualStartTime: item.actualStartTime ? fmt_datetime(item.actualStartTime) : '',
                actualEndTime: item.completeTime ? fmt_datetime(item.completeTime) : '',
                progress: item.taskProgress,
              },
              start_time: new Date(new Date(item.planStartTime).setHours(0, 0, 0, 0)).getTime(),
              end_time: new Date(new Date(item.planEndTime).setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000 - 1).getTime(),
              actualStartTime: item.actualStartTime ? new Date(new Date(item.actualStartTime).setHours(0, 0, 0, 0)).getTime() : '',
              actualEndTime: actualEndTime.getTime(),
            };
          });
        this.initGantt();
      });
    },
    initGantt() {
      console.log(1111)
      let that = this
      this.$gante({
        container: '.gante',
        ganteData: this.ganttData,
        start_time: new Date(new Date().setHours(0, 0, 0, 0)).getTime() - 550 * 24 * 3600000,
        end_time: new Date(new Date().setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000 - 1).getTime() + 550 * 24 * 3600000,
        tabe_width: '50%',
        open: true,
        openLoad: true,
        time_mode: 1,
        th_data: this.th_data,
        onEdit(data) {
          console.log(data);
        },
        onClick(data) {
          if(data.argument == 'taskName'){
            openTaskManageViewTab({...data.data?.params,projectId:that.projectId})
          }
        },
        onLoad(resolve) {
          resolve(false, [], new Date(new Date().setHours(0, 0, 0, 0)).getTime() - 550 * 24 * 3600000, new Date(new Date().setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000 - 1).getTime() + 550 * 24 * 3600000);
        },
        onDragChangeTime(data, resolve) {
          resolve(false);
        },
      }).then(data => {
        this.GANTT = data;
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.project-detail-gante {
  height: 100%;
  .gante {
    height: 100%;
    position: relative;
  }
  .add-btn{
    position: absolute;
    top:0;
    height:40px;
    z-index: 10;
    padding-top: 6px;
    .icon-tip{
      font-size: 13px;
    }
  }
}
::v-deep.demoBlock{
  span{
    display: inline-block;
    width: 30px;
    height: 16px;
    border-radius: 2px;
    margin-left: 20px;
  }
  .demoLabel{
    width: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-left: 0;
  }
}
</style>
