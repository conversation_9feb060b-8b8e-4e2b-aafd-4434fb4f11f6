/* enum */
import { ProjectManageFieldNameMappingEnum } from '@model/enum/FieldMappingEnum';
import TableNameEnum from '@model/enum/TableNameEnum';
/* util */
import { getOssUrl } from '@src/util/assets';
import { t } from '@src/locales';

// 默认头像
export const DEFAULT_AVATAR  = getOssUrl('/avatar.png');

// 进行中状态
export const PROCESS_CODE = 'RUN';
// 已完成状态
export const FINISH_CODE = 'COMPLETE';
// 未开始状态
export const START_CODE = 'START';

// 列表视图模式组件名枚举
export enum ViewTypeComponentNameEnum {
  Table = 'project-table-list',
  Time = 'project-time-list',
  Card = 'project-card-list'
}
export const projectManageViewTypeValue = 'project-manage-view-type';

// 选择列字段个数限制
export const selectColumnMaxMap: any = {
  [ViewTypeComponentNameEnum.Table]: 0,
  [ViewTypeComponentNameEnum.Time]: 5,
  [ViewTypeComponentNameEnum.Card]: 5,
}

// 看板视图不支持字段
const CardViewNotSupportedFields = [
  ProjectManageFieldNameMappingEnum.ProjectName,
  ProjectManageFieldNameMappingEnum.ManagerPerson,
  ProjectManageFieldNameMappingEnum.ProjectProcess,
  ProjectManageFieldNameMappingEnum.ProjectStatusName,
]

// 时间进度不支持字段
const TimeViewNotSupportedFields = [
  'projectName','planStartTime','planEndTime','projectProcess','actualStartTime','completeTime'
]


// 选择列不支持的字段
export const selectColumnNotSupportedFields: any = {
  [ViewTypeComponentNameEnum.Table]: [],
  [ViewTypeComponentNameEnum.Time]: TimeViewNotSupportedFields,
  [ViewTypeComponentNameEnum.Card]: CardViewNotSupportedFields,
}

// 列表视图模式
export const viewTypeList = [
  {
    label: t('projectManage.projectList.tableView'),
    value: ViewTypeComponentNameEnum.Table
  },
  {
    label: t('projectManage.projectList.timeView'),
    value: ViewTypeComponentNameEnum.Time
  },
  {
    label: t('projectManage.projectList.cardView'),
    value: ViewTypeComponentNameEnum.Card
  },
]

// 创建视角
export const createViewList = [
  {
    label: t('common.base.all'),
    value: 'ALL',
  },
  {
    label: t('common.task.angle.create'),
    value: 'CREATE',
  },
  {
    label: t('common.task.angle.execute'),
    value: 'MANAGER',
  },
  {
    label: t('common.projectManage.participateIn'),
    value: 'PARTICIPATE',
  },
]

// 项目标签
export const projectLabelsList = [
  {
    label: t('common.base.all'),
    value: '0',
  },
  {
    label: t('projectManage.projectType.taskExtension'),
    value: '2',
  },
  {
    label: t('projectManage.projectType.projectExtension'),
    value: '1',
  },
]

export const delayedTasksField = [
  {
    fieldName: 'delayedTasks',
    displayName: t('projectManage.projectType.extensionTask'),
    formType: 'text',
    setting: {},
    isSystem: 1,
    isSearch: 0,
    tableName: TableNameEnum.Project,
  }
]

// 系统字段
export const systemFields = [
  {
    fieldName: 'projectStatusName',
    displayName: t('common.base.status'),
    formType: 'text',
    setting: {},
    isSystem: 1,
    isSearch: 0,
    tableName: TableNameEnum.Project,
  },
  {
    fieldName: 'projectProcess',
    displayName: t('common.projectManage.projectProgress'),
    formType: 'text',
    setting: {},
    isSystem: 1,
    isSearch: 0,
    tableName: TableNameEnum.Project,
  },
  {
    fieldName: 'createUser',
    displayName: t('common.fields.createUser.displayName'),
    formType: 'user',
    setting: {},
    isSystem: 1,
    isSearch: 1,
    tableName: TableNameEnum.Project,
  },
  {
    fieldName: 'createTime',
    displayName: t('common.fields.createTime.displayName'),
    formType: 'datetime',
    setting: {},
    isSystem: 1,
    isSearch: 1,
    tableName: TableNameEnum.Project,
  },
  {
    fieldName: 'actualStartTime',
    displayName: t('common.projectManage.actualStartTime'),
    formType: 'datetime',
    setting: {},
    isSystem: 1,
    isSearch: 1,
    tableName: TableNameEnum.Project,
  },
  {
    fieldName: 'completeTime',
    displayName: t('common.projectManage.actualEndTime'),
    formType: 'datetime',
    setting: {},
    isSystem: 1,
    isSearch: 1,
    tableName: TableNameEnum.Project,
  },
]


export const projectStatus: any = {
  ALL: t('common.base.all'), // 全部
  START: t('common.base.notStart'), // 未开始
  RUN: t('common.base.processing'), // 进行中
  COMPLETE: t('common.task.type.finished'), // 已完成
  CANCEL: t('common.form.preview.stock.status.cancel') // 已取消
}