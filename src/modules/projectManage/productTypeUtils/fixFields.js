import i18n from '@src/locales';

// 项目固定字段
export const fixWorkLog = [
  {
    displayName: i18n.t('common.base.column.createPerson'),
    fieldName: 'createUserName',
    formType: 'user',
    isSystem: 1,
  },
  {
    displayName: i18n.t('common.base.column.createTime'),
    fieldName: 'createTime',
    formType: 'datetime',
    isSystem: 1,
  },
  {
    displayName: i18n.t('common.base.column.updateTime'),
    fieldName: 'updateTime',
    formType: 'datetime',
    isSystem: 1,
  },
];

// 项目详情固定字段
export const fixDetail = [
  ...fixWorkLog,
  {
    displayName: i18n.t('common.projectManage.actualStartTime'),
    fieldName: 'actualStartTime',
    formType: 'datetime',
    isSystem: 1,
  },
  {
    displayName: i18n.t('common.projectManage.actualEndTime'),
    fieldName: 'completeTime',
    formType: 'datetime',
    isSystem: 1,
  },
];
// 人员 - 角色 固定字段
export const fixRole = [
  {
    displayName: i18n.t('common.base.roleName'),
    fieldName: `language.${i18n.locale}.name`,
    formType: 'text',
  },
  {
    displayName: i18n.t('projectManage.setting.authSetting.rolePersonnel'),
    fieldName: 'userList',
    formType: 'user',
  },
  {
    displayName: i18n.t('common.base.explain'),
    fieldName: `language.${i18n.locale}.description`,
    formType: 'textarea',
  },
];

// 进度管理字段
export const processFixTable = [
  {
    displayName: i18n.t('projectManage.setting.taskSetting.taskNum'),
    fieldName: 'taskNumber',
    formType: 'text',
    isSystem: 1,
  },
  {
    displayName: i18n.t('projectManage.setting.taskSetting.taskName'),
    fieldName: 'taskName',
    formType: 'text',
    isSystem: 1,
  },
  {
    displayName: i18n.t('projectManage.setting.taskSetting.taskType'),
    fieldName: 'taskType',
    formType: 'text',
    isSystem: 1,
  },
  {
    displayName: i18n.t('projectManage.setting.taskSetting.jobNo'),
    fieldName: 'taskBizNumber',
    formType: 'text',
    isSystem: 1,
  },
  {
    displayName: i18n.t('projectManage.setting.taskSetting.weight'),
    fieldName: 'taskWeight',
    formType: 'text',
    isSystem: 1,
  },
];

export const taskTypeMap = {
  'COMMON_TASK': i18n.t('common.projectManage.commonTask'),
  'WORK_TASK': i18n.t('common.projectManage.task'),
  'PAAS_TASK': i18n.t('common.projectManage.paas'),
}

export const statusObj = {
  DRAFT: i18n.t('common.base.draft'),
  NO_START: i18n.t('common.base.notStart'), 
  IN_PROGRESS: i18n.t('common.base.processing'),
  COMPLETE: i18n.t('common.base.usualStatus.finish'),
  CANCEL: i18n.t('common.base.usualStatus.canceled')
}

// 项目详情的任务列表
export const ProjectTaskFields = () => {
  return  [
    {
      displayName: i18n.t('projectManage.setting.taskSetting.taskNum'),
      field: 'taskNumber',
      minWidth: '180px'
    },
    {
      displayName: i18n.t('projectManage.setting.taskSetting.taskName'),
      field: 'projectTaskName',
      minWidth: '180px'
    },
    {
      displayName: i18n.t('projectManage.taskManageList.text1'),
      field: 'taskProgress',
      minWidth: '180px'
    },
    {
      displayName: i18n.t('projectManage.setting.taskSetting.taskType'),
      field: 'taskType',
      minWidth: '180px'
    },
    {
      displayName: i18n.t('projectManage.taskManageList.text2'),
      field: 'taskBizNumber',
      minWidth: '180px'
    },
    {
      displayName: i18n.t('projectManage.taskManageList.taskStatus'),
      field: 'taskStatus',
    },
    {
      displayName: i18n.t('common.task.flow.planStartTime'),
      field: 'planStartTime',
      minWidth: '180px'
    },
    {
      displayName: i18n.t('common.task.flow.planFinishTime'),
      field: 'planEndTime',
      minWidth: '180px'
    },
    {
      displayName: i18n.t('common.projectManage.actualStartTime'),
      field: 'actualStartTime',
    },
    {
      displayName:  i18n.t('common.projectManage.actualEndTime'),
      field: 'completeTime',
    },

    {
      displayName: i18n.t('projectManage.taskManageList.text4'),
      field: 'preTaskInfoList',
    },
    {
      displayName: i18n.t('common.label.principalName'),
      field: 'manager',
    },
    {
      displayName: i18n.t('projectManage.setting.taskSetting.projectContent'),
      field: 'projectTaskDesc',
    }
  ]
}