/* util */
import { t } from '@src/locales';
/* enum */
import TableNameEnum from '@model/enum/TableNameEnum';

interface CustomStatusList {
  [key: string]: string;
}

// 列表视图模式组件名枚举
export enum ViewTypeComponentNameEnum {
  Table = 'task-manage-table',
}

// 选择列字段个数限制
export const selectColumnMaxMap: any = {
  [ViewTypeComponentNameEnum.Table]: 0,
}

export const selectColumnImportantField: any = {
  [ViewTypeComponentNameEnum.Table]: [],
}

export const customStatusList: CustomStatusList = {
  ALL: t('common.base.all'),
  DRAFT: t('common.base.draft'),
  NO_START: t('common.base.notStart'),
  IN_PROGRESS: t('common.base.processing'),
  COMPLETE: t('common.base.usualStatus.finish'),
  CANCEL: t('common.base.usualStatus.canceled'),
}

// 任务状态高级搜索选项
function taskStatusDataSource(data: CustomStatusList = {}) {
  if(!data) return [];

  const dataSource = [];
  for(let key in data) {
    dataSource.push({ value: key === 'ALL' ? '' : key, text: data[key] })
  }
  return dataSource;
}


// 创建视角
export const createViewList = [
  {
    label: t('common.base.all'),
    value: 'ALL',
  },
  {
    label: t('common.task.angle.create'),
    value: 'CREATE',
  },
  {
    label: t('common.task.angle.execute'),
    value: 'MANAGER',
  }
]

// 主要内容字段（详情放最上面
export const topSystemFields = [
  {
    fieldName: 'taskProgress',
    displayName: t('projectManage.taskManageList.text1'),
    width: '150px',
    setting: {},
    isSystem: 1,
    isSearch: 0,
    tableName: TableNameEnum.TaskManage,
  },
  {
    fieldName: 'taskType',
    displayName: t('projectManage.setting.taskSetting.taskType'),
    width: '150px',
    formType: 'select',
    setting: {},
    isSystem: 1,
    isSearch: 0,
    tableName: TableNameEnum.TaskManage,
  },
  {
    fieldName: 'taskBizNumber',
    displayName: t('projectManage.taskManageList.text2'),
    width: '150px',
    formType: 'text',
    setting: {},
    isSystem: 1,
    isSearch: 1,
    tableName: TableNameEnum.TaskManage,
  },
  {
    fieldName: 'taskStatus',
    displayName: t('projectManage.taskManageList.text3'),
    formType: 'select',
    setting: {
      dataSource: taskStatusDataSource(customStatusList),
    },
    isSystem: 1,
    isSearch: 1,
    tableName: TableNameEnum.TaskManage,
  },
  {
    fieldName: 'preTaskInfoList',
    displayName: t('projectManage.taskManageList.text4'),
    formType: 'select',
    setting: {},
    isSystem: 1,
    isSearch: 0,
    tableName: TableNameEnum.TaskManage,
  },
  {
    fieldName: 'projectNumber',
    displayName: t('projectManage.taskManageList.text5'),
    formType: 'select',
    setting: {},
    isSystem: 1,
    isSearch: 0,
    tableName: TableNameEnum.TaskManage,
  },
  {
    fieldName: 'projectName',
    displayName: t('projectManage.taskManageList.text6'),
    formType: 'select',
    setting: {},
    isSystem: 1,
    isSearch: 1,
    tableName: TableNameEnum.TaskManage,
  }
]
// 次要内容字段（详情放最下面
export const bottomSystemFields = [
  {
    fieldName: 'createUser',
    displayName: t('common.fields.createUser.displayName'),
    formType: 'user',
    setting: {},
    isSystem: 1,
    isSearch: 1,
    tableName: TableNameEnum.TaskManage,
  },
  {
    fieldName: 'createTime',
    displayName: t('common.fields.createTime.displayName'),
    formType: 'datetime',
    setting: {},
    isSystem: 1,
    isSearch: 1,
    tableName: TableNameEnum.TaskManage,
  },
  {
    fieldName: 'actualStartTime',
    displayName: t('common.projectManage.actualStartTime'),
    formType: 'datetime',
    setting: {},
    isSystem: 1,
    isSearch: 1,
    tableName: TableNameEnum.TaskManage,
  },
  {
    fieldName: 'completeTime',
    displayName: t('common.projectManage.actualEndTime'),
    formType: 'datetime',
    setting: {},
    isSystem: 1,
    isSearch: 1,
    tableName: TableNameEnum.TaskManage,
  }
]

// 系统字段(所有字段)
export const systemFields = [...topSystemFields, ...bottomSystemFields];

// 任务类型k/v
export const taskTypeMap = {
  'COMMON_TASK': t('common.projectManage.commonTask'),
  'WORK_TASK': t('common.projectManage.task'),
  'PAAS_TASK': t('common.projectManage.paas'),
}