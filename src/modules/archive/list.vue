<template>
  <div class="product-list-container">
    <div ref="tableHeaderContainer" class="product-list-search-group-container bg-w">
      <!-- 搜索 -->
      <div class="task-list-header-seach">
        <form onsubmit="return false;">
          <div class="seach task-span1 task-flex task-ai guide-box">
            <div class="task-archive-search flex task-ai jus-bet">
              <div class="flex align-items-center">
                <!-- <BizIntelligentTagsFilterPanelOperatorButton
                  :showDot="showTagOperatorButtonDot"
                  :active="filterTagPanelShow"
                  @click="changeIntelligentTagsFilterPanelShow"/> -->
              </div>

              <div class="flex align-items-center">
                <el-input
                  v-model="params.keyword"
                  :placeholder="$t('task.tip.archiveTaskTip3')"
                  class="input-with-append-search task-mr12"
                >
                  <i slot="prefix" class="el-input__icon el-icon-search"></i>

                  <el-button
                    type="primary"
                    slot="append"
                    @click="
                      params.page = 1;
                      search();
                    "
                    native-type="submit"
                    v-track="$track.formatParams('KEYWORD_SEARCH')"
                  >
                    {{$t('common.base.search')}}
                  </el-button>
                </el-input>

                <el-button type="plain-third" @click="resetParams" v-track="$track.formatParams('RESET_SEARCH')">
                  {{$t('common.base.reset')}}
                </el-button>
                <div
                  :class="['advanced-search-visible-btn', 'bg-w']"
                  @click.self.stop="panelSearchAdvancedToggle"
                >
                  <i class="iconfont icon-filter"></i>
                  {{$t('component.advancedSearch.title')}}
                </div>
              </div>
            </div>

            <div class="task-list-header-nav" v-show="packUp">
              <!-- 创建 -->
              <div class="task-filter-item">
                <div class="task-font14 task-c7 state">{{$t('common.base.createAngle')}}：</div>
                <div class="list list-crate">
                  <div class="list-item task-flex task-ai">
                    <div
                      v-for="(item, index) in selectList"
                      :key="index"
                      class="task-nav-create"
                      :class="{
                        'task-c2': selectId === item.id,
                        'task-padding-0': selectId === item.id,
                      }"
                      @click.stop="radioChange(item.id)"
                    >
                      <span :title="item.name" class="actived">{{
                        item.name
                      }}</span>
                    </div>
                  </div>
                </div>
              </div>
              <!-- 工单类型 -->
              <div class="task-filter-item">
                <div class="task-font14 task-c7 state">{{$t('common.task.taskType')}}：</div>
                <div class="list">
                  <div class="list-item task-flex task-ai">
                    <div
                      v-for="item in taskTypeList"
                      :key="item.id"
                      class="task-nav-create"
                      :class="{
                        'task-c2': currentTaskType.id === item.id,
                        'task-padding-0': currentTaskType.id === item.id,
                      }"
                      @click="typeChange(item.id)"
                    >
                      <div :title="item.name" class="actived">
                        {{ item.name }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="pack-up">
              <div @click="changePackUp">
                <i v-if="packUp" class="iconfont icon-Icon_up"></i>
                <i v-else class="iconfont icon-more"></i>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
    <div class="common-list-table__flex-row product-list-section">
        <!-- <BizIntelligentTagsFilterPanel
            v-bind="filterTagsPanelBindAttr"
            v-on="filterTagsPanelBindOn"
        /> -->
      <!--operation bar start-->
      <div class="common-list-section">
        <div
          ref="tableDoContainer"
          class="operation-bar-container"
          :style="{ justifyContent: isShowDelete ? 'space-between' : 'flex-end' }"
        >
          <div class="top-btn-group flex-x" v-if="isShowDelete">
            <el-button type="plain-third" @click="deleteData" v-track="$track.formatParams('DELETE')">
              {{$t('common.base.delete')}}
            </el-button>
          </div>

          <div class="action-button-group flex-x bg-w">
            <!-- <BizIntelligentTaggingButtonMulti v-bind="taggingBindAttr" v-on="taggingBindOn" /> -->
            <el-dropdown trigger="click">
              <div class="task-ai task-flex task-font14 task-c6 cur-point bg-w" v-if="isButtonDisplayed && exportIn">
                <span class="task-mr4 task-ml4">{{$t('common.base.moreOperator')}}</span>
                <i class="iconfont icon-fdn-select"></i>
              </div>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-if="isButtonDisplayed && exportIn">
                  <div @click="exportTask(false)" v-track="$track.formatParams('EXPORT', null, 'MORE_ACTIONS')">{{$t('common.base.export')}}</div>
                </el-dropdown-item>

                <el-dropdown-item v-if="isButtonDisplayed && exportIn">
                  <div @click="exportTask(true)" v-track="$track.formatParams('EXPORT_ALL', null, 'MORE_ACTIONS')">{{$t('common.base.exportAll')}}</div>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <!-- 选择列 start-->
            <div class="guide-box">
              <div
                :class="['task-ai', 'task-flex', 'task-font14', 'task-c6', 'task-pointer', ' cur-point']"
                id="v-task-step-1"
                @click="showAdvancedSetting"
                v-track="$track.formatParams('SELECT_COLUMN')"
              >
                <span class="task-mr4">{{$t('common.base.choiceCol')}}</span>
                <i class="iconfont icon-fdn-select"></i>
              </div>
            </div>
            <!-- 选择列 end-->
          </div>
        </div>

        <div class="bg-w pad-l-16 pad-r-16">
          <el-table
            v-loading="loading"
            stripe
            :data="tableData"
            :highlight-current-row="false"
            :row-key="getRowKey"
            :border="true"
            @select="handleSelection"
            @select-all="handleSelection"
            @header-dragend="headerDragend"
            :class="['task-list-table', 'common-list-table', 'bg-w', 'bbx-normal-list-box']"
            header-row-class-name="common-list-table-header taks-list-table-header"
            ref="multipleTable"
            :height="tableContainerHeight"
          >
            <template slot="empty">
              <BaseListForNoData v-show="!loading" :notice-msg="$t('common.base.tip.noData')"></BaseListForNoData>
            </template>
            <el-table-column
              type="selection"
              width="48"
              align="center"
              class-name="flex-x jus-center"
            ></el-table-column>
            <template v-for="(column, index) in columns">
              <el-table-column
                v-if="column.show"
                :key="`${column.fieldName}_${index}`"
                :label="column.displayName"
                :prop="column.fieldName"
                :width="column.width"
                :min-width="column.minWidth || '120px'"
                show-overflow-tooltip
                :align="column.align"
                :fixed="column.fixLeft || false"
              >
                <template slot-scope="scope">
                  <!-- 工单编号 -->
                  <div v-if="column.fieldName === 'taskNo'">
                    <!-- <a
                      href=""
                      :class="globalIsHaveTaskViewDetailAuth ? 'view-detail-btn task-list-numbering' : 'view-detail-btn-disabled'"
                      @click.stop.prevent="
                        openTaskTab(scope.row.id, scope.row[column.fieldName])
                      "
                    >
                      {{ scope.row[column.fieldName] }}
                    </a> -->
                    <BizIntelligentTagsViewToConfig 
                      type="table"
                      :value="scope.row[column.fieldName]"
                      :tagsList="scope.row.label || []"
                      @viewClick="openTaskTab(scope.row.id, scope.row[column.fieldName])"
                    />
              </div>
                  <!-- 客户  TODO: 客户查看权限 -->
                  <template v-else-if="column.fieldName === 'customer'">
                    <div
                      :class="{
                        'view-detail-btn task-client': scope.row.linkAuth,
                      }"
                      @click.stop="openClientTab(scope.row)"
                    >
                      {{
                        scope.row['customerEntity'] &&
                          scope.row['customerEntity'].name
                      }}
                    </div>
                  </template>
                  <!-- 联系人 -->
                  <template v-else-if="column.fieldName === 'tlmName'">
                    <div>
                      {{ scope.row['linkMan'] && scope.row['linkMan'].name }}
                    </div>
                  </template>
                  <!-- 关联事件 -->
                  <template v-else-if="column.fieldName === 'eventNo'">
                    <div
                      class="view-detail-btn task-client"
                      @click.stop="openEventTab(scope.row)"
                    >
                      {{ scope.row['eventNo'] }}
                    </div>
                  </template>
                  <!-- 创建方式 -->
                  <template v-else-if="column.fieldName === 'source'">
                    <span>{{ scope.row['source'] }}</span>
                  </template>
                  <!-- 电话 -->
                  <template v-else-if="column.fieldName === 'tlmPhone'">
                    <div>
                      {{ scope.row['linkMan'] && scope.row['linkMan'].phone }}
                    </div>
                  </template>
                  
                  <!-- 国际货币 -->
                  <template v-else-if="column.formType === 'currency' && scope.row.attribute[column.field]">
                    {{ $formatFormField(column, scope.row) }}
                  </template>

                  <template v-else-if="column.formType === 'logistics'">
                    <biz-list-logistics-no
                      :row="scope.row"
                      :column="column"
                      :is-link="isCanLogisticsNoLink(column)"
                      mode="task"
                      :biz-id="scope.row.id"
                    />
                  </template>
                  <!-- 更新时间 -->
                  <template v-else-if="column.fieldName === 'updateTime'">
                    <template v-if="scope.row.latesetUpdateRecord">
                      <el-tooltip
                        class="item"
                        effect="dark"
                        :content="scope.row.latesetUpdateRecord"
                        placement="top"
                      >
                        <div @mouseover="showLatestUpdateRecord(scope.row)">
                          {{ scope.row.updateTime | fmt_datetime }}
                        </div>
                      </el-tooltip>
                    </template>
                    <template v-else>
                      <div @mouseover="showLatestUpdateRecord(scope.row)">
                        {{ scope.row.updateTime | fmt_datetime }}
                      </div>
                    </template>
                  </template>
                  <!-- 是否重复报修 -->
                  <template v-else-if="column.fieldName === 'isRepeatRepair'">
                    <span :style="{ color: column.setting.color || '#F4882F' }">{{
                      scope.row.attribute[column.field] || $t('common.base.no')
                    }}</span>
                  </template>
                  <!-- 自定义的选择类型字段显示， 与type 区别-->
                  <template
                    v-else-if="column.formType === 'select' && !column.isSystem"
                  >
                    {{ scope.row.attribute[column.fieldName] | displaySelect }}
                  </template>
                  <!-- 产品 -->
                  <template v-else-if="column.fieldName === 'product'">
                    {{
                      scope.row.products &&
                        scope.row.products.map(product => product.name).join(', ')
                    }}
                  </template>
                  <!-- 创建人 和 负责人 、派单人 -->
                  <template
                    v-else-if="
                      column.fieldName === 'createUserName' ||
                        column.fieldName === 'executorName' ||
                        column.fieldName === 'allotName'
                    "
                  >
                    <template v-if="permissionTaskView">
                      <a
                        href=""
                        class="view-detail-btn view-user-detail-btn"
                        @click.stop.prevent="
                          openUserTab(
                            presonDisplayObj(
                              'userId',
                              column.fieldName,
                              scope.row
                            )
                          )
                        "
                      >
                        <template v-if="isOpenData">
                          <template
                            v-if="
                              column.field === 'createUserName' &&
                                scope.row.createUser
                            "
                          >
                            <open-data
                              type="userName"
                              :openid="scope.row.createUser.staffId"
                            ></open-data>
                          </template>
                          <template
                            v-else-if="
                              column.field === 'executorName' &&
                                scope.row.executorUser
                            "
                          >
                            <open-data
                              type="userName"
                              :openid="scope.row.executorUser.staffId"
                            ></open-data>
                          </template>
                          <template
                            v-else-if="
                              column.field === 'allotName' && scope.row.allotUser
                            "
                          >
                            <open-data
                              type="userName"
                              :openid="scope.row.allotUser.staffId"
                            ></open-data>
                          </template>
                        </template>
                        <template v-else>
                          {{
                            presonDisplayObj(
                              'displayName',
                              column.field,
                              scope.row
                            )
                          }}
                        </template>
                      </a>
                    </template>
                    <template v-else>
                      <template v-if="isOpenData">
                        <template v-if="column.field === 'createUserName'">
                          <open-data
                            type="userName"
                            :openid="scope.row.createUser.staffId"
                          ></open-data>
                        </template>
                        <template v-else-if="column.field === 'executorName'">
                          <open-data
                            type="userName"
                            :openid="scope.row.executorUser.staffId"
                          ></open-data>
                        </template>
                        <template v-else-if="column.field === 'allotName'">
                          <open-data
                            type="userName"
                            :openid="scope.row.allotUser.staffId"
                          ></open-data>
                        </template>
                      </template>
                      <template v-else>
                        {{
                          presonDisplayObj('displayName', column.field, scope.row)
                        }}
                      </template>
                    </template>
                  </template>
                  <!-- 协同人 -->
                  <template v-else-if="column.fieldName === 'synergies'">
                    <template v-if="isOpenData && scope.row[column.field]">
                      <open-data
                        v-for="synergie in scope.row[column.field]"
                        :key="synergie.staffId"
                        type="userName"
                        :openid="synergie.staffId"
                      ></open-data>
                    </template>
                    <template v-else>
                      {{
                        scope.row[column.field] &&
                          scope.row[column.field]
                            .map(synergie => synergie.displayName)
                            .join(', ')
                      }}
                    </template>
                  </template>
                  <!-- 归档人 -->
                  <template v-else-if="column.fieldName === 'syncUserName'">
                    <template v-if="isOpenData && scope.row.syncUserStaffId">
                      <open-data
                        type="userName"
                        :openid="scope.row.syncUserStaffId"
                      ></open-data>
                    </template>
                    <template v-else>
                      {{ scope.row.syncUserName }}
                    </template>
                  </template>
                  <!-- 审批状态 -->
                  <template v-else-if="column.field === 'inApprove'">
                    {{ scope.row.inApprove | displayApprove }}
                  </template>
                  <!-- 用户 -->
                  <template
                    v-else-if="
                      column.formType === 'user' &&
                        scope.row.attribute[column.field]
                    "
                  >
                    <template v-if="isOpenData">
                      <open-data
                        v-for="staffId in getUserIds(
                          scope.row.attribute[column.field]
                        )"
                        :key="staffId"
                        type="userName"
                        :openid="staffId"
                      ></open-data>
                    </template>
                    <template v-else>
                      {{ getUserName(scope.row.attribute[column.field]) }}
                    </template>
                  </template>
                  <!-- 派单方式 -->
                  <template v-else-if="column.fieldName === 'allotTypeStr'">
                    {{ allotTypeText(scope.row.allotType) }}
                  </template>
                  <!-- 服务部门(负责人所在的部门) -->
                  <template v-else-if="column.field === 'executorTags'">
                    {{ formatExecutorTags(scope.row[column.field]) }}
                  </template>
                  <!-- 曾.. -->
                  <template
                    v-else-if="taskStatusFields.indexOf(column.fieldName) > -1"
                  >
                    {{ Number(scope.row[column.fieldName]) === 1 ? $t('common.base.yes') : $t('common.base.no') }}
                  </template>
                  <!-- 地址 -->
                  <template v-else-if="column.formType === 'address'">
                    {{ formatCustomizeAddress(scope.row[column.formType]) }}
                  </template>
                  <!-- 表单设计器特殊控件 -->
                  <template
                    v-else-if="
                      [
                        'cascader',
                        'select',
                        'user',
                        'related_task',
                        'relationProduct',
                        'location',
                      ].includes(column.formType)
                    "
                  >
                    {{
                      $formatFormField(column, scope.row)
                    }}
                  </template>
                  <!-- 时间 -->
                  <template v-else-if="column.formType === 'datetime'">
                    <template v-if="!column.isSystem">
                      {{
                        scope.row.attribute &&
                          scope.row.attribute[column.fieldName]
                      }}
                    </template>
                    <template v-else>
                      {{ scope.row[column.fieldName] | fmt_datetime }}
                    </template>
                  </template>
                  <!-- 接单用时 -->
                  <template v-else-if="column.fieldName === 'acceptUsedTimeStr'">
                    {{ scope.row.acceptUsedTime && scope.row.acceptUsedTime }}
                  </template>
                  <!-- 工单用时 -->
                  <template v-else-if="column.fieldName === 'taskUsedTimeStr'">
                    {{ scope.row.taskUsedTime && scope.row.taskUsedTime }}
                  </template>
                  <!-- 工作用时 -->
                  <template v-else-if="column.fieldName === 'workUsedTimeStr'">
                    {{ scope.row.workUsedTime && scope.row.workUsedTime }}
                  </template>
                  <!-- 响应用时 -->
                  <template
                    v-else-if="column.fieldName === 'taskResponseTimeStr'"
                  >
                    {{ scope.row.taskResponseTime && scope.row.taskResponseTime }}
                  </template>
                  <!-- 支付方式 -->
                  <template
                    v-else-if="
                      column.fieldName === 'paymentMethod' &&
                        initData.paymentConfig &&
                        initData.paymentConfig.version === 1
                    "
                  >
                    {{ scope.row.attribute && scope.row.attribute.paymentMethod }}
                  </template>
                  <!-- 自定义字段 -->
                  <template v-else-if="!column.isSystem">
                    <template
                      v-if="
                        scope.row.attribute &&
                          Array.isArray(scope.row.attribute[column.fieldName])
                      "
                    >
                      {{ scope.row.attribute[column.fieldName].join(',') }}
                    </template>
                    <template v-else>
                      {{
                        scope.row.attribute &&
                          scope.row.attribute[column.fieldName]
                      }}
                    </template>
                  </template>
                  <template v-else>
                    {{
                      Array.isArray(scope.row[column.fieldName])
                        ? scope.row[column.fieldName].join(',')
                        : scope.row[column.fieldName]
                    }}
                  </template>
                </template>
              </el-table-column>
            </template>
          </el-table>
        </div>

        <div ref="tableFooterContainer" class="table-footer bbx-normal-table-footer-10">
          <div class="list-info">
            <i18n path="common.base.table.totalCount">
              <span place="count" class="level-padding">{{ page.total }}</span>
            </i18n>
            <template v-if="multipleSelection&&multipleSelection.length>0">
              <i18n path="common.base.table.selectedNth">
                <span place="count" class="product-selected-count" @click="multipleSelectionPanelShow = true">{{ multipleSelection.length }}</span>
              </i18n>
              <span class="product-selected-count" @click="toggleSelection()"
              >{{$t('common.base.clear')}}</span
              >
            </template>
          </div>
          <el-pagination
            class="product-table-pagination"
            background
            @current-change="jump"
            @size-change="handleSizeChange"
            :page-sizes="defaultTableData.defaultPageSizes"
            :page-size="page.pageSize"
            :current-page="page.page"
            layout="prev, pager, next, sizes, jumper"
            :total="page.total"
          >
          </el-pagination>
        </div>
      </div>
    </div>

    <base-panel
      class="product-panel"
      :show.sync="multipleSelectionPanelShow"
      width="420px"
    >
      <h3 slot="title">
        <span>{{$t('task.taskTypes.archive.choseTask')}}({{ multipleSelection.length }})</span>
        <i
          v-if="multipleSelection.length"
          class="iconfont icon-qingkongshanchu product-panel-btn"
          @click="toggleSelection()"
          :title="$t('common.base.tip.clearChoseDataTip')"
          data-placement="right"
          v-tooltip
        ></i>
      </h3>

      <div class="product-selected-panel">
        <div class="product-selected-tip" v-if="!multipleSelection.length">
          <img :src="nodataImage" />
          <p>{{$t('common.base.exportModal.noData')}}</p>
        </div>
        <template v-else>
          <div class="product-selected-list">
            <div class="product-selected-row product-selected-head">
              <span class="product-selected-name">{{$t('common.base.task')}}</span>
            </div>
            <div
              class="product-selected-row"
              v-for="c in multipleSelection"
              :key="c.id"
            >
              <span class="product-selected-sn">{{ c.taskNo }}</span>
              <button
                type="button"
                class="product-selected-delete"
                @click="removeFromSelection(c)"
              >
                <i class="iconfont icon-fe-close"></i>
              </button>
            </div>
          </div>
        </template>
      </div>
    </base-panel>

    <search-panel :config="{ fields: searchFields }" ref="searchPanel">
      <div class="advanced-search-btn-group" slot="footer">
        <base-button type="ghost" @event="resetParams">{{$t('common.base.reset')}}</base-button>
        <base-button type="primary" @event="powerfulSearch" native-type="submit"
        >{{$t('common.base.search')}}</base-button
        >
      </div>
    </search-panel>
    <!-- start 选择列设置 -->
    <biz-select-column ref="advanced" @save="saveColumnStatus" />
    <!-- end 选择列设置 -->
  </div>
</template>

<script>
// pageDes 归档工单页面
import { isCalendar } from '@src/util/CalendarUtil';
import { isOpenData, openAccurateTab } from '@src/util/platform';
import StorageUtil from '@src/util/storage.ts';
import {AbnormalList, fields, advancedList, archive, allExport} from '../task/list/TaskFieldModel';
import { LinkmanList, AddressList, ProductList, TaskReceiptSystemFields } from './columns';
import * as TaskApi from '@src/api/TaskApi.ts';
import * as ArchiveApi from '@src/api/ArchiveApi';
import Page from '@model/Page';
import { isEmpty } from '@src/util/type';
import * as _ from 'lodash';
import {
  mergeFieldsWithProperty,
  getFieldName,
  filterExportFieldWithFormType,
  isCurrencyField,
} from '@service/FieldService.ts';
import {
  isBasicEditionHidePrintTask,
  isBasicEditionHideProduct,
  isBasicEditionHidePay,
  isBasicEditionHideEvent,
  isBasicEditionHidePlanWork,
  isBasicEditionHideApi,
  isBasicEditionHidePart,
  isBasicEditionHideServe,
} from '@shb-lib/version';
import { checkButtonDisplayed } from '@src/util/dom';
import AuthUtil from '@src/util/auth';
import { safeNewDate } from '@src/util/time';
import { smoothLogisticsField } from '@service/LogisticsService'

/* enum */
import StorageModuleEnum from '@model/enum/StorageModuleEnum';

import SearchPanel from './component/SearchPanel';

import AuthMixin from '@src/mixins/authMixin';
import { intelligentTagsListMixin } from '@src/modules/intelligentTags/mixins'
import { PageRoutesTypeEnum } from 'pub-bbx-global/pageType/dist/enum/PageRoutesEnum'
import i18n from '@src/locales'
import LogisticsMixin from '@src/mixins/logisticsMixin'

import { getOssUrl } from '@src/util/assets'
import { defaultTableData } from '@src/util/table'
import StorageKeyEnum from '@model/enum/StorageKeyEnum'
import { TABLE_HEIGHT_MIN } from '@src/model/const/Number'

const nodataImage = getOssUrl('/no_data.png')
// 导出过滤字段类型
// const EXPORT_FILTER_FORM_TYPE = ['attachment', 'address', 'autograph', 'systemAutograph'];
// ? 不知道为什么把 address 过滤掉
const EXPORT_FILTER_FORM_TYPE = ['attachment', 'autograph', 'systemAutograph'];

import { formatDate, useFormTimezone, formatAddress } from 'pub-bbx-utils'
const { disposeFormListViewTime } = useFormTimezone()

export default {
  name: 'archive',
  inject: ['initData'],
  mixins: [AuthMixin, LogisticsMixin, intelligentTagsListMixin],
  data() {
    return {
      defaultTableData,
      nodataImage,
      isCalendar,
      isOpenData,
      isButtonDisplayed: checkButtonDisplayed(),
      loading: false,
      pending: false,
      params: {
        keyword: '',
        templateId: '',
        customerId: '',
        productId: '',
        createUser: '', // 我创建的
        executor: '', // 我负责的
        synergyId: '', // 我协同的
        createUserIds: [],
        executorUserIds: [],
        synergyUserIds: [],
        archiveTimeStart: '',
        archiveTimeEnd: '',
        page: 1,
        pageSize: 10,
      },
      radioType: '0',
      columns: [],
      page: {
        list: [],
      },
      currentTaskType: {
        id: '',
      },
      taskFields: [],
      taskReceiptFields: [],
      taskCommonFields:[], // 公共字段
      taskStatusFields: [
        'onceOverTime',
        'onceRefused',
        'oncePaused',
        'onceRollback',
        'onceReallot',
        'oncePrinted',
        'positionException',
        'source',
      ],
      multipleSelection: [],
      multipleSelectionPanelShow: false,
      selectedLimit: 1000,
      advanceds: advancedList, // 高级搜索列表
      searchFields: [
        {
          displayName: this.$t('common.base.customer'),
          fieldName: 'customerId',
          formType: 'customer',
          setting: {
            isMulti: false,
            customerOption: {
              address: true,
              linkman: true,
              product: true,
            },
          },
          isSearch: 1,
          isSystem: 1,
          placeHolder: this.$t('common.placeholder.selectCustomer'),
        },
        {
          displayName: this.$t('common.base.product'),
          fieldName: 'productId',
          formType: 'product',
          isSearch: 1,
          isSystem: 1,
          placeHolder: this.$t('common.placeholder.selectProduct'),
        },
        {
          displayName: this.$t('common.base.createUser'),
          fieldName: 'createUserIds',
          formType: 'user',
          isSearch: 1,
          isSystem: 1,
          operator: 'user',
          placeHolder: this.$t('common.base.inputKeywordToSearch'),
        },
        {
          displayName: this.$t('task.executor'),
          fieldName: 'executorUserIds',
          formType: 'user',
          isSearch: 1,
          isSystem: 1,
          placeHolder: this.$t('common.base.inputKeywordToSearch'),
        },
        {
          displayName: this.$t('task.list.displayName.archiveTime'),
          fieldName:'syncTime',
          formType:'datetime',
          isSearch: 1,
          isSystem: 1,
        },
      ],
      exportParams: {},
      exportColumns: [],
      exportColumnList: [],
      partField: '', // 选备件导出的自定义字段
      packUp: true, // true 默认展开,false收缩
      selectId: '0',
      selectList: [
        // 头部筛选列表
        { name: this.$t('common.base.all'), id: '0' },
        { name: this.$t('common.task.angle.create'), id: '1' },
        { name: this.$t('common.task.angle.execute'), id: '2' },
        { name: this.$t('common.task.angle.synergy'), id: '3' },
      ],
      typeHeight: '',
      navWidth: window.innerWidth - 120,
      tableContainerHeight:'440px',
      taskTypeObj: {}
    };
  },
  computed: {
    // 处理时间后的列表数据
    tableData(){
      return disposeFormListViewTime(this.page.list, this.columns)
    },
    // 基础版功能是否隐藏产品
    isBasicEditionHideProduct() {
      return isBasicEditionHideProduct();
    },
    // 基础版功能是否隐藏支付
    isBasicEditionHidePay() {
      return isBasicEditionHidePay();
    },
    // 基础版功能是否隐藏事件
    isBasicEditionHideEvent() {
      return isBasicEditionHideEvent();
    },
    // 基础版功能是否隐藏计划任务
    isBasicEditionHidePlanWork() {
      return isBasicEditionHidePlanWork();
    },
    // 基础版功能是否隐藏api
    isBasicEditionHideApi() {
      return isBasicEditionHideApi();
    },
    // 基础版功能是否隐藏备件
    isBasicEditionHidePart() {
      return isBasicEditionHidePart();
    },
    // 基础版功能是否隐藏服务项目
    isBasicEditionHideServe() {
      return isBasicEditionHideServe();
    },
    // 基础版功能是否隐藏打印
    isBasicEditionHidePrintTask() {
      return isBasicEditionHidePrintTask();
    },
    taskTypeList() {
      return [{ id: '', name: this.$t('common.base.all') }].concat(this.initData.taskTypeList);
    },
    userId() {
      return this.initData.loginUser.userId;
    },
    /** 权限数据 */
    auth() {
      return this.initData.auth || {};
    },
    exportIn() {
      return AuthUtil.hasAuth(this.auth, 'TASK_EXPORT')
    },
    /** 查看工单全部权限 */
    permissionTaskView() {
      return this.globalIsHaveUserViewDetailAuth
      // return this.auth.TASK_VIEW === 3;
    },
    // 是否显示删除按钮
    isShowDelete() {
      return this.auth.TASK_DELETE;
    },
    /** 公共字段 */
    commonFields(){
      return this.taskCommonFields.filter(item => item.isCommon)
    },
    /** 工单类型过滤后的字段 */
    taskTypeFilterFields() {
      let fields = this.taskFields.concat(this.taskReceiptFields) || [];
      let taskTypeFilterFields = fields.filter(field => {
        return (
          EXPORT_FILTER_FORM_TYPE.indexOf(field.formType) == -1
          && field.isSystem == 0
        );
        // return field.isSystem == 0
      });
      return taskTypeFilterFields;
    },
    /** 工单列表字段 */
    taskListFields() {
      let fixedFields = fields.slice();

      return []
        .concat(fixedFields)
        .filter(f => f.formType !== 'separator' && f.formType !== 'info')
        .filter(item => {
          if (item.fieldName == 'planTime') {
            return !this.isCalendar;
          } else if (
            item.fieldName == 'planStartTime'
            || item.fieldName == 'planEndTime'
          ) {
            return this.isCalendar;
          }
          return true;
        })
        .sort((a, b) => a.orderId - b.orderId);
    },
    /** 获取是否开启联系人地址产品 */
    customerSetting() {
      let { address, product, linkman } = this.currentTaskTypeCustomerFieldSetting;
      return {
        addressOn: address == true,
        productOn: product == true,
        linkmanOn: linkman == true,
      };
    },
    /**
     * @description 检测导出条数
     * @return {String | null}
     */
    checkExportCount(ids, max) {
      let exportAll = !ids || ids.length == 0;
      return exportAll && this.page.totalElements > max
        ? this.$t('common.base.tip.exportLimit', { max })
        : null;
    },
    /** 当前工单类型客户字段设置 */
    currentTaskTypeCustomerFieldSetting() {
      let customerFields = this.taskFields.filter(
        field => field.formType == 'customer'
      );
      let customerField = customerFields[0];
      let customerSetting = {};

      if (customerField) {
        let setting = customerField.setting || {};
        customerSetting = setting.customerOption || {};
      }

      return customerSetting;
    },
    /** 服务项目 服务内容 系统字段设置 */
    sysFieldsSetting() {
      let serviceContentFields = this.taskFields.filter(
        field => field.formType == 'serviceContent'
      );
      let serviceTypeFields = this.taskFields.filter(
        field => field.formType == 'serviceType'
      );

      let serviceContentField = serviceContentFields[0] || {};
      let serviceTypeField = serviceTypeFields[0] || {};

      let isServiceContentFieldEnabled = serviceContentField.enabled == 1;
      let isServiceTypeFieldEnabled = serviceTypeField.enabled == 1;

      return {
        hasServiceContent: isServiceContentFieldEnabled,
        hasServiceType: isServiceTypeFieldEnabled,
      };
    },
    /** 当前选中的工单ids */
    selectedIds() {
      return this.multipleSelection.map(p => p.id);
    },
  },
  filters: {
    displaySelect(value) {
      if (!value) return null;
      if (value && typeof value === 'string') {
        return value;
      }
      if (Array.isArray(value) && value.length) {
        return value.join('，');
      }
      return null;
    },
    /** 审批状态 */
    displayApprove(value) {
      return value == 0 ? i18n.t('common.task.approveStatus.noApprove') : i18n.t('common.task.approveStatus.approve');
    },
  },
  created() {
    this.initIntelligentTagsParams('TASK')
  },
  async mounted() {
    this.advanceds = this.advanceds.filter(item => {
      if (item.fieldName == 'planTime') {
        return !this.isCalendar;
      } else if (
        item.fieldName == 'planStartTime'
        || item.fieldName == 'planEndTime'
      ) {
        return this.isCalendar;
      }
      return true;
    });
    if (localStorage.getItem('archive_list'))
      this.params.pageSize = JSON.parse(
        localStorage.getItem('archive_list')
      ).pageSize;

    // await this.fetchTaskTypeList()

    this.initialize();

    // 获取是否自定义导出字段 目前只有博立有数据 其它的数据为空
    this.getExpensePartField();

    // 对外开放刷新方法，用于其他tab刷新本tab数据
    // window.__exports__refresh = this.search;
    let that_ = this;
    // 监听切换后需要重新计算列表高度
    window.addEventListener('message', (event)=> {
      const {action, data} = event.data;
      if (action == 'shb.frame.activatedPage'){
        that_.$nextTick(()=> {
          that_.knowTableContainerHeight();
        })
      }
    });
    this.$nextTick(()=> {
      this.knowTableContainerHeight()
      window.onresize = _.debounce(()=>{
        that_.knowTableContainerHeight()
      }, 500)
    })
  },
  methods: {
    // async fetchTaskTypeList() {
    //   try {
    //     const { succ = true, status, data  } = await TaskApi.getCurrentAllTaskTypeList()
    //     if(succ) {
    //       this.taskTypeObj = data
    //     }
    //   } catch(e) {
    //       console.error('【fetch TaskApi.getCurrentAllTaskTypeList error】', e)
    //   }
    // },
    // 处理人员显示
    getUserName(value) {
      // 多选
      if (Array.isArray(value)) {
        return value.map(i => i.displayName || i.name).join(',');
      }

      let user = value || {};
      return user.displayName || user.name;
    },
    getUserIds(value) {
      // 多选
      if (Array.isArray(value)) {
        return value.map(i => i.staffId);
      }

      let user = value || {};
      return [user.staffId];
    },
    async getExpensePartField() {
      const _res = await TaskApi.getExpensePartField();
      if (_res.code == 0 && _res.result?.length) {
        _res.result.forEach(item => {
          this.partField += `${item.fieldName},`;
        });
      }
    },

    // 初始化
    initialize() {
      this.loading = true;
      this.initPage();
      Promise.all([this.fetchTaskFields(), this.fetchTaskReceiptFields(), this.getCommonFields()])
        .then(res => {
          this.$set(this, 'taskFields', smoothLogisticsField(res[0] || []));
          // 解析自定义字段 物流组件字段
          this.$set(this, 'taskReceiptFields', smoothLogisticsField(res[1] || []));
          // 解析公共字段 物流组件字段
          this.$set(this, 'taskCommonFields', smoothLogisticsField(res[2] || []))

          this.search();
          this.buildColumns();
          this._exportColumns();
        })
        .catch(err => {
          console.warn(err);
        })
        .finally(() => {
          this.loading = true;
        })
    },
    /**
     * @description 初始化page
     */
    initPage() {
      this.page = new Page();
      this.page.list = [];
      this.params.page = 1;
      if (!this.params.pageSize) {
        this.params.pageSize = 10;
      }
    },
    /**
     * @description 获取工单字段列表
     * @return {Promise}
     */
    fetchTaskFields() {
      let params = {
        typeId: this.params.templateId || '',
        tableName: 'task',
        isFromSetting: false,
      };
      return TaskApi.getAllFields(params).then(result => {
        result.forEach(field => {
          field.group = 'task';
          field.label = field.displayName;
          field.field = field.fieldName;
          if(isCurrencyField(field)) {
            field.exportAlias = `${field.fieldName}_amount_number`
          }
        });
        return result;
      });
    },
    /**
     * @description 获取工单回执字段列表
     * @return {Promise}
     */
    fetchTaskReceiptFields() {
      let params = {
        typeId: this.params.templateId || '',
        tableName: 'task_receipt',
        isFromSetting: false,
      };
      return TaskApi.getAllFields(params).then(result => {
        result.forEach(field => {
          field.group = 'task_receipt';
          field.label = field.displayName;
          field.field = field.fieldName;
          if(isCurrencyField(field)) {
            field.exportAlias = `${field.fieldName}_amount_number`
          }
        });
        return result;
      });
    },
    /** 
     * @description 获取公共字段
    */
    getCommonFields(){
      let params = {
        tableName: 'all',
        typeId: this.currentTaskType.id || '',
      };
      return TaskApi.getCommonFields(params).then((result) => {
        result && result.forEach((field) => {
          field.group = 'task';
          field.label = field.displayName;
          field.field = field.fieldName;
        });
        return result;
      });
    },
    // 切换工单
    radioChange(type) {
      this.$track.clickStat(this.$track.formatParams('QUICK_SEARCH', '创建视角'));

      this.params.createUser = '';
      this.params.executor = '';
      this.params.synergyId = '';
      this.selectId = type;
      switch (type) {
      case '1':
        this.params.createUser = this.userId;
        break;
      case '2':
        this.params.executor = this.userId;
        break;
      case '3':
        this.params.synergyId = this.userId;
        break;
      default:
        break;
      }
      this.search();
    },
    // 选择工单类型
    typeChange(val) {
      this.$track.clickStat(this.$track.formatParams('QUICK_SEARCH', '工单类型'));

      this.currentTaskType.id = val;
      val && this.getCardDetailList(val);
      this.params.templateId = val;
      // this.search();
      this.initialize();
    },
    /**
     * @description 时间戳转换
     */
    timestamp(value) {
      if (typeof value === 'number') {
        let h = value / 3600 < 0 ? 0 : parseInt(value / 3600),
          m;
        if (h > 0) {
          m = value % 3600 ? Math.ceil((value % 3600) / 60) : value % 3600;
        } else {
          m = Math.ceil(value / 60);
        }

        if (m > 59) {
          m = 0;
          h++;
        }
        return `${h}小时${m}分钟`;
      }
      return '';
    },
    // 搜索
    async search() {
      try {
        this.loading = true;
        let { code, message, result } = await ArchiveApi.archiveList(
          { ...this.params, ...this.builderIntelligentTagsSearchParams() }
        );
        this.loading = false;
        if (code === 0) {
          this.page = result;
          // 转换用时字段的时间格式
          this.page.list = this.page.list.map(item => {
            item.acceptUsedTime = this.timestamp(item.acceptUsedTime);
            item.taskUsedTime = this.timestamp(item.taskUsedTime);
            item.workUsedTime = this.timestamp(item.workUsedTime);
            item.taskResponseTime = this.timestamp(item.taskResponseTime);
            item.createToCompleteUsedTime = this.timestamp(
              item.createToCompleteUsedTime
            );
            if (item.planTime && this.planTimeType === 'date') {
              item.planTime = formatDate(safeNewDate(item.planTime), 'YYYY-MM-DD');
            }
            return item;
          });
          if (this.multipleSelection.length) {
            this.$nextTick(() => {
              this.multipleSelection.forEach(item => {
                this.page.list.forEach(v => {
                  if (v.id === item.id) {
                    this.$refs.multipleTable.toggleRowSelection(v);
                  }
                });
              });
            });
          }
        } else {
          this.$notify({
            title: this.$t('common.base.fail'),
            message,
            type: 'error',
          });
        }
      } catch (err) {
        this.loading = false;
        console.error(err);
      }
    },
    powerfulSearch() {
			this.$track.clickStat(this.$track.formatParams('ADVANCED_SEARCH'));
      
      this.params.page = 1;
      this.params = { ...this.params, ...this.$refs.searchPanel.buildParams() };
      this.$refs.searchPanel.hide();

      this.search();
    },
    /**
     * @description 打开工单详情tab
     * @param {String} taskId 工单id
     */
    openTaskTab(taskId, taskNo) {
      if (!taskId) return;
      if (!this.globalIsHaveTaskViewDetailAuth) return

      try {
        sessionStorage.setItem(
          taskId,
          JSON.stringify(
            this.tableData.filter(item => (!(item.isDelete == 5 && item.inApprove == -1))).map(item => ({
              taskId: item.id,
              taskNo: item.taskNo,
            }))
          )
        );
      } catch (error) {
        console.error(error);
      }

      let fromId = window.frameElement.getAttribute('id');

      openAccurateTab({
        type: PageRoutesTypeEnum.PageTaskView,
        key: taskId,
        titleKey: taskNo,
        params: `noHistory=1&JumpKey=${taskId}`,
        fromId
      })
    },
    openEventTab(clientInfo) {
      let id = clientInfo.eventId;
      openAccurateTab({
        type: PageRoutesTypeEnum.PageEventView,
        key: id,
        titleKey: clientInfo.eventNo,
      })
    },
    /**
     *
     * @description 打开客户详情
     * @param {object} clientInfo 客户详情
     */
    openClientTab(clientInfo) {
      const { linkAuth, customerEntity } = clientInfo;
      const { id } = customerEntity;
      if (!linkAuth) return;

      let fromId = window.frameElement.getAttribute('id');

      openAccurateTab({
        type: PageRoutesTypeEnum.PageCustomerView,
        key: id,
        params: 'noHistory=1',
        fromId
      })
    },
    // 重置
    resetParams() {
      this.radioType = '0';
      this.params = {
        page: 1,
        pageSize: this.page.pageSize,
        keyword: '',
        customerId: '',
        productId: '',
        createUser: '', // 我创建的
        executor: '', // 我负责的
        synergyId: '', // 我协同的
        createUserIds: [],
        executorUserIds: [],
        synergyUserIds: [],
        archiveTimeStart: '',
        archiveTimeEnd: '',
        templateId: '',
      };
      this.currentTaskType.id = '';
      this.selectId = '0';
      this.$refs.searchPanel.resetParams();
      this.search();
    },
    // 高级搜索
    panelSearchAdvancedToggle() {
      this.$refs.searchPanel.open();
      this.$nextTick(() => {
        let forms = document.getElementsByClassName('advanced-search-form');
        for (let i = 0; i < forms.length; i++) {
          let form = forms[i];
          form.setAttribute('novalidate', true);
        }
      });
    },
    // 删除
    deleteData() {
      if (!this.multipleSelection.length) {
        return this.$message.warning(this.$t('task.tip.archiveTaskTip4'));
      }
      this.$confirm(this.$t('common.base.tip.confirmForDelete'), this.$t('common.base.delete'), {
        confirmButtonText: this.$t('common.base.makeSure'),
        cancelButtonText: this.$t('common.base.cancel'),
        type: 'warning',
      })
        .then(() => {
          const taskIds = this.multipleSelection.map(item => item.id);
          ArchiveApi.removeTask({ taskIds }).then(res => {
            const { code, message } = res;
            if (code === 0) {
              this.toggleSelection();
              this.search();
            }
            this.$notify({
              title: code === 0 ? this.$t('common.base.deleteSuccess') : this.$t('common.base.deleteFail'),
              message,
              type: code === 0 ? 'success' : 'error',
            });
          });
        })
        .catch(() => {});
    },
    // 多选
    handleSelection(selection) {
      let tv = this.selectionCompute(selection);

      let original = this.multipleSelection.filter(ms =>
        this.page.list.some(cs => cs.id === ms.id)
      );

      let unSelected = this.page.list.filter(c =>
        original.every(oc => oc.id !== c.id)
      );

      if (tv.length > this.selectedLimit) {
        this.$nextTick(() => {
          original.length > 0
            ? unSelected.forEach(row => {
              this.$refs.multipleTable.toggleRowSelection(row, false);
            })
            : this.$refs.multipleTable.clearSelection();
        });
        return this.$platform.alert(this.$t('common.base.tip.choiceLimit', {limit: this.selectedLimit}));
      }

      this.multipleSelection = tv;

      this.$refs.baseSelectionBar?.openTooltip();
      this.$nextTick(()=>{
        this.knowTableContainerHeight()
      })
    },
    // 计算已选择
    selectionCompute(selection) {
      let tv = [];

      tv = this.multipleSelection.filter(ms =>
        this.page.list.every(c => c.id !== ms.id)
      );
      tv = _.uniqWith([...tv, ...selection], _.isEqual);

      return tv;
    },
    /**
     * @description 排序变化
     * @param {Object} option 配置
     */
    sortChange(option) {
      const UserNameConvertMap = {
        createUserName: 'createUser',
        executorName: 'executorUser',
        customer: 'customerName',
      };

      try {
        let { prop, order } = option;

        if (!order) {
          this.params.orderDetail = {};
          return this.search();
        }
        const sortedField = this.taskListFields.filter(sf => sf.fieldName === prop)[0] || {};

        let isSystem = 0;
        let isConvertedProp = Object.keys(UserNameConvertMap).indexOf(prop) > -1;

        if (
          prop === 'createTime'
          || prop === 'updateTime'
          || prop === 'syncTime'
          || isConvertedProp
        ) {
          isSystem = 1;
        } else {
          isSystem = sortedField.isSystem;
        }

        if (isConvertedProp) {
          prop = UserNameConvertMap[prop];
        }

        let sortModel = {
          isSystem,
          sequence: order === 'ascending' ? 'ASC' : 'DESC',
          // column: isSystem ? `task.${prop}` : prop,
          column: prop,
        };

        if (
          prop === 'createTime'
          || prop === 'updateTime'
          || prop === 'syncTime'
          || sortedField.formType === 'date'
          || sortedField.formType === 'datetime'
        ) {
          sortModel.type = 'date';
        } else if (prop === 'level' || prop === 'taskNo') {
          sortModel.type = 'string';
        } else {
          sortModel.type = sortedField.formType;
        }

        this.params.orderDetail = sortModel;
        this.page.list = [];

        this.search();
      } catch (e) {
        console.error('e', e);
      }
    },
    // 拖动列
    headerDragend() {
      let data = this.columns
        .map(item => {
          if (item.fieldName === column.property) {
            item.width = column.width;
          }
          return item;
        })
        .map(item => {
          return {
            field: item.field,
            show: item.show,
            width: item.width,
          };
        });
      this.modifyColumnStatus({ type: 'column', data });
    },
    /**
     * @description 修改选择列设置
     * @param {Object} event 事件对象
     */
    modifyColumnStatus(event) {
      let columns = event.data || [],
        colMap = columns.reduce(
          (acc, col) => (acc[col.field] = col) && acc,
          {}
        );
      this.columns.forEach(col => {
        let newCol = colMap[col.field];
        if (null != newCol) {
          this.$set(col, 'show', newCol.show);
          this.$set(col, 'width', newCol.width);
        }
      });

      this.saveColumnStatusToStorage();
    },
    /** 导出列 */
    _exportColumns() {
      let { taskFields, taskReceiptFields } = this;
      // 工单信息
      let taskSelfFields = [];
      // 回执信息
      let taskReceiptSystemFields = TaskReceiptSystemFields;

      // 工单信息逻辑
      let linkman_list = '',
        address_list = '',
        product_list = '';
      taskSelfFields = taskFields.filter(field =>
        filterExportFieldWithFormType(field)
      );
      if (taskFields.length) {
        let first = taskFields.filter(item => {
          return item.fieldName === 'customer';
        })[0];
        if (first.setting.customerOption.linkman) {
          linkman_list = LinkmanList;
        }
        if (first.setting.customerOption.address) {
          address_list = AddressList;
        }
        if (first.setting.customerOption.product) {
          product_list = ProductList;
        }
      }
      taskSelfFields.forEach((item, index) => {
        if (item.fieldName === 'taskNo') {
          taskSelfFields.splice(index + 1, 0, {
            id: 476,
            tableName: 'customer',
            isSystem: 1,
            fieldName: 'templateName',
            exportAlias: 'templateName',
            displayName: this.$t('common.task.taskType'),
            formType: 'text',
            defaultValue: null,
            isNull: 0,
            isSearch: 1,
            placeHolder: null,
            setting: {
              customerNameDuplicate: false,
            },
            orderId: 0,
            isDelete: 0,
            guideProfessions: [],
            show: true,
            isGuideData: false,
            guideData: false,
          });
        }
        if (item.fieldName === 'customer') {
          if (linkman_list) {
            taskSelfFields.splice(
              index + 1,
              0,
              linkman_list[0],
              linkman_list[1]
            );
          }
          if (address_list) {
            taskSelfFields.splice(
              index + linkman_list.length + 1,
              0,
              address_list[0]
            );
          }
          if (product_list) {
            taskSelfFields.splice(
              index + linkman_list.length + address_list.length + 1,
              0,
              product_list[0]
            );
          }
        }
      });
      taskSelfFields.map(item => {
        item.label = item.displayName;
        item.export = true;
        return item;
      });

      // 回执信息逻辑
      const receiptFilterFields = ['sparepart', 'serviceIterm'];
      taskReceiptSystemFields = [
        ...taskReceiptSystemFields,
        ...taskReceiptFields.filter(
          field =>
            filterExportFieldWithFormType(field)
            && !receiptFilterFields.includes(field.formType)
        ),
      ].map(field => {
        field.export = true;
        return field;
      });

      // 系统信息
      let sysList = [...allExport, ...AbnormalList];

      // 基础版过滤产品、支付方式、结算时间、结算人、关联事件、曾打印字段
      if (this.isBasicEditionHideProduct) {
        taskSelfFields = taskSelfFields.filter(
          item => item.fieldName !== 'product'
        );
      }
      if (this.isBasicEditionHidePay) {
        sysList = sysList.filter(
          item =>
            item.fieldName !== 'balanceTime'
            && item.fieldName !== 'paymentMethod'
            && item.exportAlias !== 'balanceUser'
        );
      }
      if (this.isBasicEditionHideEvent) {
        sysList = sysList.filter(item => item.fieldName !== 'eventNo');
      }
      if (this.isBasicEditionHidePrintTask) {
        sysList = sysList.filter(item => item.exportAlias !== 'oncePrinted');
      }

      // 基础版导出列的系回执信息过滤备件、服务项目、费用信息字段
      if (this.isBasicEditionHidePart) {
        taskReceiptSystemFields = taskReceiptSystemFields.filter(
          field => field.fieldName !== 'spare_name'
        );
      }
      if (this.isBasicEditionHideServe) {
        taskReceiptSystemFields = taskReceiptSystemFields.filter(
          field => field.fieldName !== 'service_name'
        );
      }
      if (this.isBasicEditionHidePay) {
        taskReceiptSystemFields = taskReceiptSystemFields.filter(
          field => field.fieldName !== 'balance_total'
        );
      }

      this.exportColumns = [
        {
          label: this.$t('task.taskInfo'),
          value: 'taskChecked',
          columns: taskSelfFields,
        },
        {
          label: this.$t('task.receiptInfo'),
          value: 'receiptChecked',
          columns: taskReceiptSystemFields,
        },
        {
          label: this.$t('task.systemInfo'),
          value: 'systemChecked',
          columns: sysList.map(item => {
            item.export = true;
            item.label = item.displayName;
            return item;
          }),
        },
      ];

      this.getCardDetailList(this.params.templateId);
    },
    /**
     * 获取附件
     */
    async getCardDetailList(typeId) {
      const res = await TaskApi.getCardDetailList({
        typeId,
      });
      let list = res
        .map((item, index) => {
          if (item.canRead) {
            let columns,
              endAddress = {
                displayName: this.$t('common.base.address'),
                fieldName: 'endAddress',
              },
              startAddress = {
                displayName: this.$t('common.base.address'),
                fieldName: 'startAddress',
              };
            if (item.specialfrom === '工时记录') {
              let list = [];
              // 添加固定导出参数
              item.fields.splice(
                item.fields
                  .map((v, i) => {
                    if (v.fieldName === 'endTime') {
                      return i + 1;
                    }
                  })
                  .filter(v => {
                    return v;
                  })[0],
                0,
                endAddress
              );

              item.fields.splice(
                item.fields
                  .map((v, i) => {
                    if (v.fieldName === 'startTime') {
                      return i + 1;
                    }
                  })
                  .filter(v => {
                    return v;
                  })[0],
                0,
                startAddress
              );

              item.fields.forEach(v => {
                if (v.fieldName !== 'remark' && v.fieldName !== 'attachment') {
                  list.push(v);
                }
              });
              list.map((v, i) => {
                if (!v.map) {
                  v.fieldName = `${item.cardId}_${v.fieldName}`;
                }
              });
              item.fields = [
                ...list,
                ...[
                  {
                    displayName: this.$t('task.list.strokeDistance'),
                    fieldName: `${item.cardId}_distance`,
                  },
                  {
                    displayName: this.$t('task.list.operator'),
                    fieldName: `${item.cardId}_workingOperatorName`,
                  },
                  {
                    displayName: this.$t('task.list.operateTime'),
                    fieldName: `${item.cardId}_workingOperateTime`,
                  },
                ],
              ];
            } else {
              item.fields = [
                ...item.fields,
                ...[
                  { displayName: this.$t('task.list.operator'), fieldName: `cu_${item.cardId}` },
                  { displayName: this.$t('task.list.operateTime'), fieldName: `ct_${item.cardId}` },
                ],
              ];
            }
            columns = item.fields
              .map((v, i) => {
                return {
                  export: item.canRead,
                  label: v.displayName,
                  exportAlias: v.fieldName,
                  ...v,
                };
              })
              .filter(field => {
                return filterExportFieldWithFormType(field);
              });
            return {
              value: `annexChecked${index}`,
              label: `${this.$t('task.record.taskCard')}：${item.cardName}`,
              inputType: item.inputType,
              columns,
            };
          }
        })
        .filter(item => {
          if (item) {
            return item;
          }
        });
      this.exportColumnList = [...this.exportColumns, ...list];
    },
    // 构建导出参数
    buildExportParams(all) {
      this.exportParams = {
        typeId: this.params.templateId,
        exportOneRow: true,
        exportCopyFromFirst: true,
        checked: '',
        cardFieldChecked: '',
        data: '',
        exportSearchModel: JSON.stringify({
          ...this.params,
          exportTotal: all ? this.page.total : this.selectedIds.length,
        }),
        receiptChecked: '',
        sysChecked: '',
        isArchive: true,
      };
      if (!all) this.exportParams.data = this.selectedIds.join(',');
      let cardField = [];
      this.exportColumnList.forEach((column, index) => {
        if (column.value === 'taskChecked') {
          // 关联表单和服务商的过滤
          if(column.columns.some(item => item.formType === 'relationForm' || item.formType === 'serviceProviders')){
            column.columns = column.columns.filter(item => item.formType !== 'relationForm' && item.formType !== 'serviceProviders')
          }
          this.exportParams.checked = column.columns
            .map(item => {
              const fieldName = item.fieldName;
              if (fieldName === 'product') return 'product,productSN';
              return item.exportAlias || fieldName;
            })
            .join(',');
        } else if (column.value === 'receiptChecked') {
          // 关联表单过滤
          column.columns = column.columns.filter(item => item.formType === 'relationForm')

          this.exportParams.receiptChecked = column.columns
            .map(item => {
              const fieldName = item.fieldName;
              if (fieldName === 'spare_name') {
                // 添加定制客户的自定义导出备件字段
                return `spare_name,spare_serialNumber,${this.partField}spare_type,spare_standard,spare_number,spare_cost`;
              }
              if (fieldName === 'service_name') {
                return 'service_name,service_type,service_number,service_cost';
              }
              if (fieldName === 'balance_total') {
                return 'balance_total,balance_discount,balance_sum';
              }
              return item.exportAlias || fieldName;
            })
            .join(',');
        } else if (column.value === 'systemChecked') {
          // 归档工单不支持转派时间导出
          column.columns = column.columns.filter(item => item.fieldName !== 'reallotTime' && item.exportAlias !== 'reAllotUser')
          this.exportParams.sysChecked = column.columns
            .map(item => item.exportAlias || item.fieldName)
            .join(',');
        }
        if (index > 2) {
          column.columns.forEach(item =>
            cardField.push(item.exportAlias || item.fieldName)
          );
        }
      });
      this.exportParams.cardFieldChecked = cardField.join(',');
      // 工单类型为全部的时候值为空
      if (!this.params.templateId) {
        this.exportParams.cardFieldChecked = '';
      }
    },
    // 导出
    async exportTask(all = false) {
      let isContinue = true;
      if (this.isOpenData) {
        isContinue = false;
        await this.$confirm(
          this.$t('task.tip.archiveTaskTip5'),
          this.$t('task.taskTypes.archive.exportTip'),
          {
            confirmButtonText: this.$t('common.base.export'),
            cancelButtonText: this.$t('common.base.cancel'),
            type: 'warning',
          }
        )
          .then(() => {
            isContinue = true;
          })
          .catch(() => {
            isContinue = false;
          });
      }
      if (isContinue) {
        this.buildExportParams(all);
        if (this.pending) return this.$message.warning(this.$t('task.tip.archiveTaskTip6'));
        if (!all && !this.selectedIds.length) {
          return this.$message.warning(this.$t('task.tip.archiveTaskTip4'));
        }
        this.buildExportParams(all);
        this.pending = true;
        const { status, message } = await ArchiveApi.newExport(
          this.exportParams
        );
        this.pending = false;
        if (status === 0) {
          this.$message.success(message);
          window.parent.showExportList();
          window.parent.exportPopoverToggle(true);
        } else {
          this.$platform.alert(message);
        }
      }
    },
    // 选择
    toggleSelection(rows) {
      let isNotOnCurrentPage = false;
      let row = undefined;

      if (rows) {
        for (let i = 0; i < rows.length; i++) {
          row = rows[i];
          isNotOnCurrentPage = this.page.list.every(item => {
            return item.id !== row.id;
          });
          if (isNotOnCurrentPage) return;
        }
        rows.forEach(row => {
          this.$refs.multipleTable.toggleRowSelection(row);
        });
      } else {
        this.$refs.multipleTable.clearSelection();
        this.multipleSelection = [];
      }
    },
    // 构建列
    async buildColumns() {
      const localStorageData = await this.getIndexedDbData();
      let columnStatus = localStorageData.columnStatus || [];

      let localColumns = columnStatus
        .map(i => (typeof i == 'string' ? { field: i, show: true } : i))
        .reduce((acc, col, currentIndex) => {
          acc[col.field] = {
            field: col,
            index: currentIndex,
          };
          return acc;
        }, {});

      let taskListFields = this.filterTaskListFields();
      let fields = taskListFields.concat(this.taskTypeFilterFields);
      fields = fields.concat(AbnormalList);

      const index = fields.findIndex(item => item.fieldName === 'closeTime');
      fields.splice(index + 1, 0, ...archive);

      fields = [...fields.filter(item=>!item.isCommon), ...this.commonFields].sort((a, b) => a.orderId - b.orderId);

      if (Array.isArray(columnStatus) && columnStatus.length > 0) {
        fields = this.buildSortFields(fields, localColumns);
      }

      let columns = fields
        .filter(
          f =>
            !['attachment', 'separator', 'info', 'autograph', 'relationForm', 'serviceProviders'].includes(
              f.formType
            )
        )
        .filter(f => f.fieldName !== 'state' && f.fieldName !== 'reAllotUserList')
        .map(field => {
          let sortable = false;
          let minWidth = 120;

          if (['date', 'datetime', 'number'].indexOf(field.formType) >= 0) {
            sortable = 'custom';
            minWidth = 100;
          }

          if (['address'].indexOf(field.formType) >= 0) {
            minWidth = 200;
          }

          if (
            [
              'level',
              'updateTime',
              'createUserName',
              'executorName',
              'state',
            ].indexOf(field.fieldName) >= 0
          ) {
            sortable = 'custom';
          }

          if (field.displayName.length > 4) {
            minWidth = field.displayName.length * 20;
          }

          if (sortable && field.displayName.length >= 4) {
            minWidth = 125;
          }

          if (
            field.formType === 'datetime'
            || field.fieldName === 'updateTime'
            || field.fieldName === 'createTime'
          ) {
            minWidth = 150;
          }

          if (['taddress', 'templateName'].indexOf(field.fieldName) >= 0) {
            minWidth = 200;
          }

          if (['taskNo'].indexOf(field.fieldName) !== -1) {
            minWidth = 250;
            sortable = 'custom';
          }
          if (field.fieldName === 'customer') {
            sortable = 'custom';
            minWidth = 125;
          }
          if (field.fieldName === 'taskNo') {
            field.width = 216;
          }
          return {
            ...field,
            label: field.displayName,
            field: field.fieldName,
            formType: field.formType,
            minWidth: typeof minWidth == 'number' ? minWidth : `${minWidth}px`,
            sortable,
            isSystem: field.isSystem,
          };
        })
        .map(col => {
          let show = col.show === true;
          let { width } = col;
          let localField = localColumns[col.field]?.field || null;
          let fixLeft = localField?.fixLeft || null;
          if (null != localField) {
            if (localField.width) {
              width = typeof localField.width == 'number'
                ? `${localField.width}px`
                : localField.width;
            }
            show = localField.show !== false;
          } else {
            show = true;
          }
          col.show = show;
          col.width = width;
          col.type = 'column';
          col['fixLeft'] = fixLeft && 'left'
          return col;
        });

      this.columns = [];
      this.$nextTick(() => {
        this.$set(this, 'columns', columns.slice());

        // 根据版本号判断是否需要支付方式
        if (
          !this.initData.paymentConfig
          || !this.initData.paymentConfig.version
        ) {
          this.advanceds = this.advanceds.filter(item => {
            return item.fieldName !== 'paymentMethod';
          });
          this.columns = this.columns.filter(item => {
            // 展示列过滤掉转派时间
            return item.fieldName !== 'paymentMethod' && item.fieldName !== 'reallotTime';
          });
        }

        // 并本地缓存列数据至当前实例的列数据
        this.mergeLocalStorageColumnsToColumns();
      });
    },
    /**
     * @description 合并本地缓存列数据至当前实例的列数据
     */
    async mergeLocalStorageColumnsToColumns() {
      const { columnStatus } = await this.getIndexedDbData();
      if (isEmpty(columnStatus)) return;

      mergeFieldsWithProperty(
        this.columns,
        columnStatus,
        (column, localStorageDataColumnItem) => {
          // 列名不匹配则返回
          if (getFieldName(column) !== getFieldName(localStorageDataColumnItem))
            return;
          // 覆盖列显示状态
          column.show = Boolean(localStorageDataColumnItem.show);
          // 覆盖宽度数据
          if (localStorageDataColumnItem?.width) {
            column.width = localStorageDataColumnItem.width;
          }
        }
      );
    },
    buildSortFields(originFields = [], fieldsMap = {}) {
      let fields = [];
      let unsortedFields = [];

      originFields.forEach(originField => {
        let { fieldName } = originField;
        let field = fieldsMap[fieldName];

        if (field) {
          let { index } = field;
          fields[index] = originField;
        } else {
          unsortedFields.push(originField);
        }
      });

      return fields.concat(unsortedFields);
    },
    /**
     * @description 过滤工单列表字段
     * @return {Array<TaskField>} 过滤后工单列表字段
     */
    filterTaskListFields() {
      let fields = this.taskListFields || [];
      let field = null;

      let customerSetting = this.customerSetting;
      let sysFieldsSetting = this.sysFieldsSetting;

      let newFields = [];

      for (let i = 0; i < fields.length; i++) {
        field = fields[i];

        // 未开启联系人
        if (
          !customerSetting.linkmanOn
          && (field.fieldName == 'tlmName' || field.fieldName == 'tlmPhone')
        ) {
          continue;
        }

        // 未开启地址
        if (!customerSetting.addressOn && field.fieldName == 'taddress') {
          continue;
        }

        // 未开启产品
        if (!customerSetting.productOn && field.fieldName == 'product') {
          continue;
        }

        // 服务类型
        if (
          !sysFieldsSetting.hasServiceType
          && field.fieldName == 'serviceType'
        ) {
          continue;
        }

        // 服务内容
        if (
          !sysFieldsSetting.hasServiceContent
          && field.fieldName == 'serviceContent'
        ) {
          continue;
        }

        newFields.push(field);
      }

      return newFields;
    },
    // 获取缓存数据
    async getIndexedDbData() {
      let data = {};

      try {
        data = await StorageUtil.storageGet(
          this.currentTaskType.id ? `${StorageKeyEnum.TaskArchiveListColumns}_${this.currentTaskType.id}` : `${StorageKeyEnum.TaskArchiveListColumns}_all`,
          {},
          StorageModuleEnum.Task
        );
      } catch (error) {
        data = {};
        console.error('Caused ~ ArchiveList ~ getIndexedDbData ~ error', error);
      }
      return data;
    },
    /**
     * @description 选择列
     */
    showAdvancedSetting() {
      this.$refs.advanced.open(this.columns, this.currentTaskType);
    },
    /**
     * @description 修改选择列设置
     * @param {Object} event 事件对象
     */
    saveColumnStatus(event) {
      let columns = event.data || [];

      this.columns = [];
      this.$nextTick(() => {
        this.$set(this, 'columns', columns.slice());
        this.saveColumnStatusToStorage();
      });
      this.$message.success(this.$t('common.base.saveSuccess'));
    },
    async saveColumnStatusToStorage() {
      const localStorageData = await this.getIndexedDbData();
      let columnsStatus = null;

      // 判断是否存储选择列
      const columnsList = this.columns.map(c => ({
        field: c.fieldName,
        show: c.show,
        width: c.width,
        fixLeft: c.fixLeft
      }));

      if (localStorageData.columnStatus) {
        localStorageData.columnStatus = columnsList;
        columnsStatus = localStorageData.columnStatus;
      } else {
        columnsStatus = columnsList;
      }

      this.saveDataToIndexedDb('columnStatus', columnsStatus);
    },
    /**
     * @description 保存数据到本地indexedDB
     */
    async saveDataToIndexedDb(key, value) {
      const data = await this.getIndexedDbData();
      data[key] = value;
      StorageUtil.storageSet(
        this.currentTaskType.id ? `${StorageKeyEnum.TaskArchiveListColumns}_${this.currentTaskType.id}` : `${StorageKeyEnum.TaskArchiveListColumns}_all`,
        data,
        StorageModuleEnum.Task
      );
    },
    getRowKey(row) {
      return row.id || '';
    },
    // 页跳转
    jump(val) {
      this.params.page = val;
      this.search();
    },
    // 页条数
    handleSizeChange(pageSize) {
      this.saveDataToIndexedDb('pageSize', pageSize);
      this.params.pageSize = pageSize;
      this.params.page = 1;
      this.search();
    },
    removeFromSelection(c) {
      if (!c || !c.id) return;

      this.multipleSelection = this.multipleSelection.filter(
        ms => ms.id !== c.id
      );
      this.multipleSelection.length < 1
        ? this.toggleSelection()
        : this.toggleSelection([c]);
    },
    /**
     * @description 显示最后一次更新记录
     * @param {Object} row 行数据
     */
    showLatestUpdateRecord(row) {
      if (row.latesetUpdateRecord) return;

      TaskApi.getTaskUpdateRecord({
        taskId: row.id,
      })
        .then(res => {
          if (!res || res.status) return;

          this.page.list = this.page.list.map(c => {
            if (c.id === row.id) {
              c.latesetUpdateRecord = res.data;
            }
            return c;
          });
        })
        .catch(e => console.error('e', e));
    },
    /**
     * @description 打开用户详情tab
     * @param {String} userId 用户id
     */
    openUserTab(userId) {
      if (!userId) return;

      let fromId = window.frameElement.getAttribute('id');

      openAccurateTab({
        type: PageRoutesTypeEnum.PageSecurityUserView,
        key: userId,
        params: 'noHistory=1&from=task',
        fromId
      })
    },
    /** @return 人员userId和展示字段 */
    presonDisplayObj(attr, fieldName, row) {
      let obj = {};
      switch (fieldName) {
      case 'createUserName':
        obj = row.createUser;
        break;
      case 'executorName':
        obj = row.executorUser;
        break;
      case 'allotName':
        obj = row.allotUser;
        break;
      default:
        break;
      }

      obj = obj || {};

      return obj[attr];
    },
    /**
     * @description 格式化服务团队
     */
    formatExecutorTags(executorTags) {
      if (null == executorTags) return '';

      return executorTags.map(item => item.name).join('、');
    },
    /**
     * @description 派单方式文案
     */
    allotTypeText(params) {
      let text;
      switch (params) {
      case 1:
        text = this.$t('task.detail.components.manualDispatch');
        break;
      case 2:
        text = this.$t('task.detail.components.poolDispatch');
        break;
      case 3:
        text = this.$t('task.detail.components.autoDispatch');
        break;
      default:
        text = '';
        break;
      }
      return text;
    },
    /**
     * @description 格式化自定义地址
     * @param {Object} customizeAddress 自定义地址
     * @return {String} 过滤后的地址
     */
    formatCustomizeAddress(customizeAddress) {
      if (null == customizeAddress) return '';

      return formatAddress(customizeAddress);
    },
    // 获取列表当前的可滚动高度
    knowTableContainerHeight() {
      let min = TABLE_HEIGHT_MIN;
      try {
        let window_ = window.innerHeight;
        let header = this.$refs.tableHeaderContainer?.offsetHeight || 0;
        let do_ = this.$refs.tableDoContainer?.offsetHeight || 0;
        let footer = this.$refs.tableFooterContainer?.offsetHeight || 0;
        min = window_ - header * 1 - do_ * 1 - footer * 1 - 20 - 16 - 10;
        min = min > TABLE_HEIGHT_MIN ? min : TABLE_HEIGHT_MIN;
      } catch (error) {
        console.warn(error, 'error try catch');
      }
      this.$set(this, 'tableContainerHeight', `${min}px`);
    },
    changePackUp() {
      this.packUp = !this.packUp;
      this.$nextTick(() => {
        this.knowTableContainerHeight();
      });
    },
  },
  components: {
    [SearchPanel.name]: SearchPanel,
  },
};
</script>

<style lang="scss" scoped>
.actived {
  height: 28px;
  line-height: 18px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 4px 8px;
  display: inline-block;
  max-width: 160px;
  margin: 0 12px 8px 0;
  border: 1px solid rgba(0, 0, 0, 0);
}
::v-deep .el-input-group__append, .el-input-group__prepend{
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
.task-list {
  &-header {
    background: #ffffff;
    box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.06);
    border-radius: 4px;
    margin-bottom: 12px;
    border-top: none;
    overflow: hidden;

    &-seach {
      .seach {
        justify-content: flex-end;
        padding: 16px;
        margin-bottom: 6px;
        .advanced-search-visible-btn {
          margin-left: 12px;
          height: 32px;
          border-radius: 4px;
          font-size: 14px;
          @include fontColor();
          line-height: 32px;
          text-align: center;
          cursor: pointer;
          white-space: nowrap;
        }
      }
    }

    &-nav {
      width: 100%;
      .task-filter-item {
        display: flex;
      }
      > div {
        position: relative;
        cursor: pointer;
        // border-top: 1px solid #F5F5F5;
        .state {
          line-height: 22px;
          padding-top: 4px;
          // padding-left: 11px;
          width: 90px;
          font-weight: 500;
          // background-color: #FAFAFA;
        }
        .element-icon {
          position: absolute;
          right: 12px;
          top: 6px;
          span {
            color: rgba(0, 0, 0, 0.65);
          }
        }
        .list {
          width: 90%;
          overflow: hidden;
          .list-item {
            > div {
              font-size: 13px;
              max-width: 160px;
              overflow: hidden;
              color: #808080;
              height: 38px;
              white-space: nowrap;
              text-overflow: ellipsis;
              &:hover {
                color: $color-primary;
              }
            }
          }
        }
      }
    }
  }
}

</style>

<style lang="scss">

html,
body {
  height: 100%;
}
.product-list-container {
  height: 100%;
  padding: 10px;
  overflow: auto;
  display: flex;
  flex-direction: column;
}

.product-columns-dropdown-menu {
  max-height: 300px;
  overflow: auto;
  .el-dropdown-menu__item {
    padding: 0;
  }
  .el-checkbox {
    width: 100%;
    padding: 5px 15px;
    margin: 0;
  }
}

// search
.product-list-container .product-list-search-group-container {
  border-radius: 4px;
  .base-search {
    background: #fff;
    border-radius: 3px;
    font-size: 14px;
    display: flex;
    justify-content: space-between;
    padding: 12px 10px;

    .product-list-base-search-group {
      display: flex;
      width: 440px;
      justify-content: space-between;

      .el-input {
        width: 300px;
        input {
          height: 31px;
          line-height: 31px;
          width: 300px;
        }
      }

      a {
        line-height: 33px;
      }
    }

    .advanced-search-visible-btn {
      font-size: 14px;
      line-height: 31px;
      color: $color-primary;
      border-color: $color-primary;
      background: #fff;
      padding: 0 13px;
      &:hover {
        cursor: pointer;
      }

      .iconfont {
        font-size: 12px;
        font-weight: bolder;
      }
    }
  }
}

.product-list-container .product-list-section {
  // padding-top: 10px;
}

// operation
.product-list-container .product-list-section .operation-bar-container {
  background: #fff;
  border-radius: 3px 3px 0 0;
  display: flex;
  justify-content: space-between;
  padding: 10px;

  .top-btn-group .base-button {
    margin-right: 5px;
  }

  .action-button-group {
    .base-button {
      margin-left: 5px;
    }
  }

  .el-dropdown-btn {
    padding: 0 15px;
    line-height: 31px;
    display: inline-block;
    background: $color-primary-light-9;
    color: $text-color-primary;
    outline: none;
    margin-left: 5px;
    .iconfont {
      margin-left: 5px;
      font-size: 12px;
    }

    &:hover {
      cursor: pointer;
      color: #fff;
      background: $color-primary;
    }
  }
}

// table
.el-table {
  border: none;
}
.el-table--small th,
.el-table--small td {
  height: 40px;
  padding: 3px 0;
}
.product-list-container .product-table {
  padding: 10px;

  &:before {
    height: 0;
  }

  .goods-img-list {
    height: 100%;
    img {
      width: 32px;
      height: 32px;
      margin-right: 4px;
      cursor: pointer;
    }
  }

  .product-table-header th {
    background: #f5f5f5;
    color: $text-color-primary;
    font-weight: normal;
  }

  th {
    color: #606266;
    font-size: 14px;
  }
  td {
    color: #909399;
    font-size: 13px;
  }

  .view-detail-btn {
    color: $color-primary;
  }

  .select-column .el-checkbox {
    position: relative;
    top: 3px;
  }
}

.product-list-container .table-footer {
  display: flex;
  justify-content: space-between;
  padding: 0px 10px 10px 10px;
  background: #fff;
  border-radius: 0 0 3px 3px;

  .list-info {
    font-size: 13px;
    line-height: 32px;
    margin: 0;
    color: #767e89;

    .iconfont {
      position: relative;
      top: 1px;
    }

    .product-selected-count {
      color: $color-primary;
      padding: 0 3px;
      width: 15px;
      text-align: center;
      cursor: pointer;
      font-size: 13px;
    }
  }

  .el-pagination__jump {
    margin-left: 0;
  }
}

// select panel
.product-list-container .product-selected-panel {
  font-size: 14px;
  height: calc(100% - 51px);

  .product-selected-tip {
    padding-top: 80px;

    img {
      display: block;
      width: 160px;
      margin: 0 auto;
    }

    p {
      text-align: center;
      color: $text-color-regular;
      margin: 8px 0 0 0;
      line-height: 20px;
    }
  }

  .product-selected-list {
    height: 100%;
    padding: 10px;
    overflow-y: auto;

    .product-selected-row {
      display: flex;
      flex-flow: row nowrap;
      align-items: center;
      border-bottom: 1px solid #ebeef5;
      font-size: 13px;

      &:hover {
        background-color: #f5f7fa;

        .product-selected-delete {
          visibility: visible;
        }
      }
    }

    .product-selected-head {
      background-color: #f0f5f5;
      color: #333;
      font-size: 14px;
    }

    .product-selected-sn {
      padding-left: 10px;
      width: 100%;
      @include text-ellipsis;
    }

    .product-selected-name {
      padding-left: 10px;
      flex: 1;
      @include text-ellipsis;
    }

    .product-selected-delete {
      width: 36px;
    }

    .product-selected-row button.product-selected-delete {
      padding: 0;
      width: 36px;
      height: 36px;
      border: none;
      background-color: transparent;
      outline: none;
      color: #646b78;
      visibility: hidden;

      i {
        font-size: 14px;
      }

      &:hover {
        color: #e84040;
      }
    }
  }
}

// advanced search form

.base-import-warn {
  p {
    margin: 0;
  }
}

.product-panel {
  .base-panel-title {
    h3 {
      display: flex;
      justify-content: space-between;
    }
    .product-panel-btn {
      cursor: pointer;
      &:hover {
        color: $color-primary;
      }
    }
  }
}
.el-table .cell {
  line-height: 31px;
}

.input-with-append-search {
  // width: 300px !important;

  .el-input input {
    border-radius: 4px 0 0 4px;
  }
  .el-input-group__append {
    background-color: $color-primary-light-6;
    border-radius: 0 4px 4px 0;
    border: 0;
    .el-button {
      border-radius: 0 4px 4px 0;
    }
  }
}
.ykl-radio-group {
  margin-right: 3px;

  .el-radio-button__inner {
    font-size: 13px !important;
    padding: 8px 15px 9px !important;
  }
}

.pack-up {
  width: 100%;
  margin-bottom: 3px;
  position: absolute;
  bottom: -13px;
  div {
    width: 48px;
    height: 10px;
    background: linear-gradient(180deg, #ffffff 0%, #f4f4f4 100%);
    box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.12);
    border-radius: 0px 0px 3px 3px;
    margin: 0 auto;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    i {
      font-size: 10px;
      color: #4e4e4e;
    }
  }
  div:hover {
    background: $color-primary-light-1;
    border-radius: 0px 0px 3px 3px;
    border: 1px solid $color-primary-light-2;
    i {
      color: $color-primary;
    }
  }
}

.flex-x{
  .cell{
    display:flex !important;
    justify-content:center;
  }
}

.task-archive-search{
  width: 100%;
}
</style>
