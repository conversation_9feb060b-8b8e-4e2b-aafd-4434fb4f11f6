import { ModulesEnum } from "@src/modules/intelligentTags/model/enum/modulesEnum";
import { ModuleBizDataKeyValue } from '@src/modules/intelligentTags/model/biz/moduleBizDataKeyValue'
import { AuthKeyMap, ModuleBizDataAuthKeyValue } from '@src/modules/intelligentTags/model/biz/moduleBizDataAuthKeyValue'

const TAG_KEY = 'label'

const generateValue = (bizType: ModulesEnum, appId: string)=> {
    return { bizType, appId }
}

export const constModuleMap: Record<string, any> = {
    TASK:  generateValue(ModulesEnum.TASK, 'TASK'),
    EVENT: generateValue(ModulesEnum.EVENT, 'EVENT'),
    PRODUCT_TYPE: generateValue(ModulesEnum.PRODUCT_TYPE, 'PRODUCT_TYPE'),
    PRODUCT_LIST: generateValue(ModulesEnum.PRODUCT_LIST, 'PRODUCT_LIST'),
    CUSTOMER: generateValue(ModulesEnum.CUSTOMER, 'CUSTOMER'),
    PROJECT_LIST: generateValue(ModulesEnum.PROJECT_LIST, 'PROJECT_LIST'),
    PROJECT_TASK_LIST: generateValue(ModulesEnum.PROJECT_TASK_LIST, 'PROJECT_TASK_LIST'),
    MALL_ORDER: generateValue(ModulesEnum.MALL_ORDER, 'MALL_ORDER'),
    WIKI: generateValue(ModulesEnum.WIKI, 'WIKI'),
    COURSE: generateValue(ModulesEnum.COURSE, 'COURSE'),
    EXAMINATION: generateValue(ModulesEnum.EXAMINATION, 'EXAMINATION'),
    ORGANIZATION_USER: generateValue(ModulesEnum.ORGANIZATION_USER, 'ORGANIZATION_USER'),
    CALLCENTER: generateValue(ModulesEnum.CALLCENTER, 'CALLCENTER'),
    IM_CONVERSATION: generateValue(ModulesEnum.IM_CONVERSATION, 'IM_CONVERSATION'),
    CONTRACT: generateValue(ModulesEnum.CONTRACT, 'CONTRACT'),
    IM_EMAIL: generateValue(ModulesEnum.IM_EMAIL, 'IM_EMAIL'),
    TENANT_PROVIDER: generateValue(ModulesEnum.TENANT_PROVIDER, 'TENANT_PROVIDER'),
    TENANT_PROVIDER_USER: generateValue(ModulesEnum.TENANT_PROVIDER_USER , 'TENANT_PROVIDER_USER'),
    LINK_MAN: generateValue(ModulesEnum.LINK_MAN , 'LINK_MAN'),
    PART2_SPAREPART: generateValue(ModulesEnum.PART2_SPAREPART , 'PART2_SPAREPART'),
}

// 没有编辑全新的的模块列表（就是一直显示打标组件）
export const constNotNeedAuthModules = [ModulesEnum.PROJECT_TASK_LIST, ModulesEnum.WIKI, ModulesEnum.PROJECT_LIST]

// 相关模块中权限获取的 key 详情跟列表不一样
export const constModuleForAuthValueMap = {
    [ModulesEnum.TASK]: new ModuleBizDataAuthKeyValue(
        new AuthKeyMap('', '', 'exportPermissionTaskEdit'),
        new AuthKeyMap('', '', 'editOperatorValue')
    ),// 完成
    [ModulesEnum.EVENT]:new ModuleBizDataAuthKeyValue(
        new AuthKeyMap('', '', 'editAll'),
        new AuthKeyMap('', '', 'canEdit')
    ),// 完成
    [ModulesEnum.PRODUCT_TYPE]: new ModuleBizDataAuthKeyValue(
        new AuthKeyMap('', '', 'allowEditAllProduct'),
        new AuthKeyMap('', '', 'editCatalog')
    ),// 完成
    [ModulesEnum.PRODUCT_LIST]: new ModuleBizDataAuthKeyValue(
        new AuthKeyMap('', '', 'editOperatorValue'),
        new AuthKeyMap('', '', 'hasEditProductAuth')
    ),// 完成
    [ModulesEnum.CUSTOMER]: new ModuleBizDataAuthKeyValue(
        new AuthKeyMap('', '', 'isAllEditedPermission'),
        new AuthKeyMap('', '', 'allowEditCustomer')
    ), // 完成
    [ModulesEnum.PROJECT_LIST]: new ModuleBizDataAuthKeyValue(
        new AuthKeyMap('', '', 'editOperatorValue'),
        new AuthKeyMap('', '', 'editOperatorValue')
    ), // 完成
    [ModulesEnum.PROJECT_TASK_LIST]: new ModuleBizDataAuthKeyValue(
        new AuthKeyMap('', '', 'exportPermissionTaskEdit'),
        new AuthKeyMap('', '', 'exportPermissionTaskEdit')
    ), // 未完成（没有编辑权限）
    [ModulesEnum.MALL_ORDER]: new ModuleBizDataAuthKeyValue(
        new AuthKeyMap('', '', ''),
        new AuthKeyMap('', '', '')
    ), // 未完成（没有编辑权限）
    [ModulesEnum.WIKI]: new ModuleBizDataAuthKeyValue(
        new AuthKeyMap('', '', 'allowImportAttachment'),
        new AuthKeyMap('', '', 'canEdit')
    ),// 完成
    [ModulesEnum.ORGANIZATION_USER]: new ModuleBizDataAuthKeyValue(
        new AuthKeyMap('', '', 'allowTagEdit'),
        new AuthKeyMap('', '', 'canEditLabel')
    ),// 完成
    COURSE:  {},
    EXAMINATION:  {},
    PAAS: {}
}


    // public key: string 模块的 key
    // public singleBizDataKeyArray: string[] 单个业务数据 key
    // public multiBizDataKeyArray: string[] 多个业务数据 key
    // public searchFunKey: (string | string[])[] //搜索的方法 key (列表使用 定义为为数组 --数组第一个为刷新的key, 第二个为对应刷新后方法)
    // public bizIdKey?: string 业务数据 id key
    // public bizNoKey?: string 业务数据 value key
    // public refreshKeyArr?:(string | string[]) [] // 每个模块页面刷新数据的方法的取值 key
    // public deleteKey: string // 业务实例中对应是否删除数据的 key

export const constModuleForValueMap = {
    TASK: new ModuleBizDataKeyValue(
        'task',  // key
        ['attribute', TAG_KEY], // singleBizDataKeyArray
        ['multipleSelection'],  // multiBizDataKeyArray
        ['search'],  // searchFunKey
        'id',  // bizIdKey
        'taskNo', // bizNoKey
        ['$refs.record.initializeRecord'], // refreshKeyArr
        '',
        ['setpageNum'], // setPageNum
    ), // ok
    EVENT: new ModuleBizDataKeyValue(
        'workEvent',  // key
        ['labelList'], // singleBizDataKeyArray
        ['multipleSelection'], // multiBizDataKeyArray
        ['search'],  // searchFunKey 
        'id',  // bizIdKey
        'eventNo',  // bizNoKey
        ['$refs.eventProgressRate.getEventRecord'],  // refreshKeyArr
        '$refs.eventButtonGroupRef.isDelete', // deleteKey
        ['setpageNum'], // setPageNum
    ), // ok
    PRODUCT_TYPE: new ModuleBizDataKeyValue(
        'dataInfo', // key
        [TAG_KEY], // singleBizDataKeyArray
        ['multipleSelection'],  // multiBizDataKeyArray
        ['search'],  // searchFunKey 
        'id', // bizIdKey
        'catalogName',  // bizNoKey
        ['reloadRecord'],  // refreshKeyArr
        'dataInfo.isDelete', // deleteKey
        ['setpageNum'], // setPageNum
    ), // ok
    PRODUCT_LIST: new ModuleBizDataKeyValue(
        'newestProduct', // key
        [TAG_KEY], // singleBizDataKeyArray
        ['multipleSelection'], // multiBizDataKeyArray
        ['search'],  // searchFunKey 
        'id', // bizIdKey
        'name', // bizNoKey
        ['updateInfoDongtai'], // refreshKeyArr
        'dataInfo.isDelete',  // deleteKey
        ['setpageNum'], // setPageNum
    ), // ok
    CUSTOMER:  new ModuleBizDataKeyValue(
        'customer', // key
        [TAG_KEY], // singleBizDataKeyArray
        ['multipleSelection'],  // multiBizDataKeyArray
         ['search'], // searchFunKey 
         'id', // bizIdKey
         'serialNumber', // bizNoKey
         ['dongtaiHandler'],   // refreshKeyArr
         'isDelete', // deleteKey
         ['setpageNum'], // setPageNum
    ), // ok
    PROJECT_LIST:  new ModuleBizDataKeyValue(
        'detailData', // key
        ['attribute', TAG_KEY],  // singleBizDataKeyArray
        ['$refs','currentComponentRef','multipleSelection'],   // multiBizDataKeyArray
        ['$refs.currentComponentRef.search'], // searchFunKey 
        'id', // bizIdKey
        'projectNo',  // bizNoKey
        ['rightActiveTabRef.searchRecord'],  // refreshKeyArr
        '', // deleteKey
        ['$refs.currentComponentRef.setpageNum'], // setPageNum
    ), // ok
    PROJECT_TASK_LIST:  new ModuleBizDataKeyValue(
        'detailData',  // key
        [TAG_KEY],  // singleBizDataKeyArray
        ['$refs', 'taskManageRef','multipleSelection'],  // multiBizDataKeyArray
        ['$refs.taskManageRef.search'], // searchFunKey 
        'id',  // bizIdKey
        'taskNumber', // bizNoKey
        ['componentTabRefs.searchRecord'],   // refreshKeyArr
        '', // deleteKey
        ['$refs.taskManageRef.setpageNum'] // setPageNum
    ), // ok
    MALL_ORDER: new ModuleBizDataKeyValue(
        'dataInfo',  // key
        ['labelList'],  // singleBizDataKeyArray
        ['multipleSelection'],  // multiBizDataKeyArray
        ['handleSearch'],  // searchFunKey 
        'orderId', // bizIdKey
        'orderNum', // bizNoKey
        ['getOrderDynamic'], // refreshKeyArr
        '', // deleteKey
        ['setpageNum'] // setPageNum
    ), // ok
    WIKI: new ModuleBizDataKeyValue(
        'detail',  // key
        ['labelList'], // singleBizDataKeyArray
        ['selection'], // multiBizDataKeyArray
        ['search'],   // searchFunKey 
        'id', // bizIdKey
        'wikiNumber', // bizNoKey
        [''], // refreshKeyArr
        '', // deleteKey
        ['setpageNum'] // setPageNum
    ), // ok
    // COURSE:  new ModuleBizDataKeyValue(ModulesEnum.COURSE, ['attribute', TAG_KEY], ['multipleSelection'], 'search', 'id', 'taskNo'),
    // EXAMINATION:  new ModuleBizDataKeyValue(ModulesEnum.EXAMINATION, ['attribute', TAG_KEY], ['multipleSelection'], 'search', 'id', 'taskNo'),
    // PAAS: {},
    ORGANIZATION_USER: new ModuleBizDataKeyValue(
        'organizationUser', // key
        ['labelList'], // singleBizDataKeyArray
        ['multipleSelection'],   // multiBizDataKeyArray
        ['fetchUser'],  // searchFunKey 
        'userId', // bizIdKey
        'workNo', // bizNoKey
        ['$refs.record.initializeRecord'],  // refreshKeyArr
        '', // deleteKey
        ['setpageNum'] // setPageNum
    ), // ok
    CALLCENTER: new ModuleBizDataKeyValue(
        'callDetail', // key
        ['labelList'], // singleBizDataKeyArray
        ['multipleSelection'],   // multiBizDataKeyArray
        ['getRecordList'],  // searchFunKey 
        'id', // bizIdKey
        'recordId', // bizNoKey
        ['$refs.record.initializeRecord'],  // refreshKeyArr
        '', // deleteKey
        ['setpageNum'] // setPageNum
    ), // ok
    IM_CONVERSATION: new ModuleBizDataKeyValue(
        'imConversationInfo', // key
        ['labelList'], // singleBizDataKeyArray
        ['multipleSelection'],   // multiBizDataKeyArray
        ['search'],  // searchFunKey 
        'id', // bizIdKey
        'conversationNumber', // bizNoKey
        ['handleRefreshData'],  // refreshKeyArr
        '', // deleteKey
        ['setpageNum'] // setPageNum
    ), // ok
    CONTRACT: new ModuleBizDataKeyValue(
        'detailData', 
        [TAG_KEY], 
        ['multipleSelection'], 
        ['handleSearch'], 
        'id', 
        'contractNo', 
        ['$refs.infoRecordRef.searchRecord'],
        '', // deleteKey
        ['setpageNum'] // setPageNum
    ), // ok
    IM_EMAIL: new ModuleBizDataKeyValue(
        'detailInfo', 
        [TAG_KEY], 
        ['multipleSelection'], 
        ['handleSearch'], 
        'id', 
        'serialnumber', 
        ['$refs.infoRecordRef.searchRecord'],
        '', // deleteKey
        ['setpageNum'] // setPageNum   
    ), // ok
    // 服务商
    TENANT_PROVIDER: new ModuleBizDataKeyValue(
        'fieldValue', 
        ['labelList'], 
        ['multipleSelection'], 
        ['onSearch'], 
        'tenantProviderId', 
        'providerName', 
        ['$refs.infoRecordRef.searchRecord'],
        '', // deleteKey
        ['setpageNum'] // setPageNum   
    ),
    // 服务商工程师
    TENANT_PROVIDER_USER : new ModuleBizDataKeyValue(
        'detailData', 
        ['labelList'], 
        ['multipleSelection'], 
        ['search'], 
        'loginUserId', 
        'name', 
        ['$refs.infoRecordRef.searchRecord'],
        '', // deleteKey
        ['setpageNum'] // setPageNum   
    ),
    // 客户联系人
    LINK_MAN: new ModuleBizDataKeyValue(
        'detailData', 
        ['labelList'], 
        ['multipleSelection'], 
        ['search'], 
        'id', 
        'name', 
        ['$refs.infoRecordRef.searchRecord'],
        '', // deleteKey
        ['setpageNum'] // setPageNum   
    ),
    // 备件品类
    PART2_SPAREPART: new ModuleBizDataKeyValue(
        'part', 
        ['labelList'], 
        ['selected'], 
        ['loadData'], 
        'id', 
        'serialNumber', 
        ['initRecords'],
        '', // deleteKey
        ['setpageNum'] // setPageNum   
    ),
}