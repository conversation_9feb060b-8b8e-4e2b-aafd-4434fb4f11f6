import { computed, defineComponent, PropType, ref, toRefs, nextTick, watch } from "vue";
/* enum */
import { IntelligentTagsComponentNameEnum, IntelligentTagsMainTabEnum, StoreModuleEnum } from "@src/modules/intelligentTags/model/enum";
import { userSelect } from '@src/modules/intelligentTags/model/enum/index.ts'
/* const */
import { appListTableColumnItem, groupListTableColumnItem, SET_CURRENT_TAB, SET_CURRENT_TAB_PARAMS, tagListTableColumn } from "@src/modules/intelligentTags/model/const";
/* store */
import store from '@src/modules/intelligentTags/store/index'
/* component */
import CreateOrEditTagForm from '@src/modules/intelligentTags/view/home/<USER>/List/components/TagsList/components/CreateOrEditTagForm'
import TaskExceptionSetting from '@src/modules/intelligentTags/view/home/<USER>/List/components/TagsList/components/TaskExceptionSetting'
import BaseListForNoData from '@src/component/common/BaseListForNoData';
import SortTable from '@src/modules/intelligentTags/view/home/<USER>/List/components/TagsList/components/SortTable.tsx'
import LabelBatchDialog from '@src/modules/intelligentTags/view/home/<USER>/List/components/TagsList/components/LabelBatchDialog.tsx'
import TriggerListDialog from '@src/modules/intelligentTags/view/home/<USER>/List/components/TagsList/components/TriggerListDialog.tsx'
import CreateTriggerDialog from '@src/modules/trigger/components/CreateTriggerDialog';
// 设置消息模版
import SetMessageDialog from '@src/modules/intelligentTags/view/home/<USER>/List/components/TagsList/components/setMessageLabel.tsx'
import { Fragment } from "vue-frag";
/* model */
import { TableColumnItem, TagsGroupItem, TagsItem, TagsModulesItem } from '@src/modules/intelligentTags/model/interface'
/* hooks */
import{ useCurrentInstance } from '@src/modules/intelligentTags/hooks/useVueInstance.ts'
import { useEnableTagsListItemFetch, useTagsFormSubmitOrUpdateFetch, useTagsListFetch, useTagsListItemDelFetch, useFetTagsListItemDetailFetch, useFetchTriggerListOfLabel,useFetchEnableSubscribe } from "@src/modules/intelligentTags/view/home/<USER>/List/hooks/useTagsFetch";
import { useIntelligentTagsAuth } from '@src/modules/intelligentTags/hooks/useAuth'
import { useTriggerCount, useTriggerTools } from '@src/modules/intelligentTags/view/home/<USER>/List/hooks/useTriggerCount.ts'
import { useGray } from '@src/modules/intelligentTags/hooks/useGray.ts'
import { useCreateSubscribeTip } from '@src/modules/intelligentTags/view/home/<USER>/List/components/TagsList/components/hooks/useCreateSubscribeTip.ts'
import { useCreateTriggerDialog } from '@src/modules/intelligentTags/view/home/<USER>/List/components/TagsList/components/hooks/useCreateTriggerDialog.ts'
/* utils */
import { t } from "@src/locales";
import { Message, MessageBox } from "element-ui";
import { formatDate } from "pub-bbx-utils";
import { deleteObjectKeys } from "@src/modules/intelligentTags/utils/utils";
import { cloneDeep } from "lodash";
/* style */
import '@src/modules/intelligentTags/view/home/<USER>/List/style/tagList.scss'

export default defineComponent({
    name: IntelligentTagsComponentNameEnum.IntelligentTagsTableList,
    props: {
        groupList: {
            type: Array as PropType<(TagsGroupItem & Partial<TagsModulesItem>) []>,
            default: ()=> ([])
        },
        groupItem: {
            type: Object as PropType<(TagsGroupItem & Partial<TagsModulesItem>)>,
            default: ()=> ({})
        }
    },
    setup(props, { emit, expose }) {
        const { groupItem, groupList }= toRefs(props)
        const createOrEditRef = ref<typeof CreateOrEditTagForm>()
        const createLabelEditRef = ref<typeof LabelBatchDialog>()
        const createTriggerListRef = ref<typeof TriggerListDialog>()
        const SetMessageDialogRef = ref<typeof SetMessageDialog>()
        const selectRowArray = ref<TagsItem []>([])
        const sortTableRef = ref<typeof SortTable>()
        const tableRef = ref<any>()
        // 排序布局
        const sortLayout = ref<boolean>(false)
        const CreateTriggerDialogRef = ref<typeof CreateTriggerDialog | null>(null);
        const { tagsListWithPage, loading, handleFetchTagsList,tagsFetchParams, fetchTagListResult, handleTagsListPageChange, handleTagsListCurrentChange,
         } = useTagsListFetch(groupItem.value)
        const { submitResult, handleFetchSubmitTagsListItem, submitFetchSuccess, submitFetchLoading, submitFetchErrorMessage } = useTagsFormSubmitOrUpdateFetch()
        const { enableResult, handleFetchEnableTagsListItem } = useEnableTagsListItemFetch()
        const { deleteResult, handleFetchDelTagsListItem, delFetchISsSuccess, delFetchMessage } = useTagsListItemDelFetch()
        const { tagDetailResult, handleFetchDetailTagsListItem, detailTagFetchISsSuccess, detailTagFetchLoading } = useFetTagsListItemDetailFetch()
        const { intelligentTagsCreateAuth, intelligentTagsDeleteAuth, intelligentTagsEditAuth, intelligentTagsViewAuth } = useIntelligentTagsAuth()
        const { labelTriggerResult, handleFetchLabelTrigger, labelTriggerLoading, labelTriggerISsSuccess } = useFetchTriggerListOfLabel();
        const { enableSubscribeResult, handleFetchEnableSubscribe, enableSubscribeLoading, enableSubscribeISsSuccess, enableSubscribeMessage } = useFetchEnableSubscribe();
        const { subscribeTips, pleaseSetMessageTips } = useCreateSubscribeTip();

        const messageLabelShow = ref(false)
        // 获取触发器数量hooks
        const { triggerResult, handleFetchTriggerCount, triggerLoading, triggerISsSuccess,triggerErrorMessage } = useTriggerCount()
        // 触发器余量不足
        const { dosageNotification, isExistTrigger } = useTriggerTools()
        // 触发器灰度
        const { getTriggerGray } = useGray()
        const [currentCtx] = useCurrentInstance()

        const { onDialogSubmitHandler, showTriggerWay } = useCreateTriggerDialog();
        // 弹窗处理逻辑
        const onDialogSubmit = (formValue: any) => {
            onDialogSubmitHandler(CreateTriggerDialogRef.value, formValue)
        }

        const isNormalTag = computed(()=> groupItem.value.type === 0)
        let sortList = ref<TagsItem []>([])

        const groupDescription = computed(()=> groupItem.value.description)

        const isAppFilterTag = computed(()=> groupItem.value?.appId)

        const isFullTableHeight = computed(()=> !intelligentTagsCreateAuth.value && !intelligentTagsDeleteAuth.value)

        const currentTableColumn = computed(()=> {
            if(isNormalTag.value) {
                if(isAppFilterTag.value) {
                    tagListTableColumn.splice(2, 1, groupListTableColumnItem)
                } else{
                    // tagListTableColumn.splice(2, 1, appListTableColumnItem)
                }
                return tagListTableColumn
            }
            return [tagListTableColumn[0], tagListTableColumn[tagListTableColumn.length - 2]]
        })

        const tableHeight = computed(()=> {
            // if(isFullTableHeight.value) {
            //     return `calc(100vh - 384px + ${isNormalTag.value ? '0px' : '56px'})`
            // }
            // return `calc(100vh - 432px + ${isNormalTag.value ? '0px' : '56px'})`
            return '100%'
        })

        // 应用模块范围
        const groupModuleInfo = computed(() => {
            if (Boolean(fetchTagListResult.value.result?.globalGroup)) {
                return '全局应用'
            }
            return fetchTagListResult.value.result?.groupAppList?.map((item: any) => item?.appName || '').join(', ')
        })

        // 可见范围
        const groupUserInfo = computed(() => {
            let _v = fetchTagListResult.value.result?.groupAuthorityList
            if (Array.isArray(_v) && _v.length && _v[0]?.objType === userSelect.ALL) {
                return '全部员工'
            }
            if (!_v) return
            return _v.map((item: any) => item?.objName || '').join(', ')
        })

        // 是拖拽排序
        const isSort = computed(() => {
            return sortLayout.value
        })

        const groupName = computed(()=> fetchTagListResult.value.result?.groupName || groupItem.value.name)

        // 智能流程按钮
        const flowIntLoading = computed(() => {
            return triggerLoading.value || labelTriggerLoading.value
        })

        const handleCreate = ()=> {
            const defaultParams = !isAppFilterTag.value ? { groupId: groupItem.value.id } : { appList: [groupItem.value] }
            createOrEditRef.value && createOrEditRef.value.show('新建标签', defaultParams )
        }

        // 批量创建
        const handleCreateGroup = () => {
            createLabelEditRef.value && createLabelEditRef.value.showDialog()
        }

        const refreshTableData = ()=> {
            setTimeout(()=> handleFetchTagsList(tagsFetchParams.value), 800)
        }

        const refreshListHandlerMessage = () => {
            setTimeout(()=> handleFetchTagsList(tagsFetchParams.value), 300)
        }

        const handleDelete = async (row?: TagsItem | undefined)=> {
            if(!selectRowArray.value.length && !row) {
                return MessageBox.confirm('请选择需要删除的数据', '提示', {
                    showCancelButton: false,
                    confirmButtonText: '确定',
                })
            }
            
            await MessageBox.confirm('删除标签后，将删除历史打标数据，请确认？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
            })

            await handleFetchDelTagsListItem({ ids: row ? [row?.id] : selectRowArray.value.map(item=> item.id) })

            if(delFetchISsSuccess.value) {
                Message.success('删除成功')
                setTimeout(()=>  refreshTableData(), 100)
            } else {
                Message.warning(delFetchMessage.value)
            }
        }

        const handleEdit = async(row: TagsItem)=> {
            await handleFetchDetailTagsListItem({ id: row.id })
        
            if(detailTagFetchISsSuccess.value) {
                const currentRow =  {...tagDetailResult.value?.result ?? {}}
                createOrEditRef.value && createOrEditRef.value.show('编辑标签', currentRow)
            }
        }

        const handleToLogsPage = (row: TagsItem)=> {
    
            store.commit(`${StoreModuleEnum.Main}/${SET_CURRENT_TAB_PARAMS}`, isAppFilterTag.value ? { appId: [groupItem.value?.appId], row } : { group: groupItem.value, row })
            store.commit(`${StoreModuleEnum.Main}/${SET_CURRENT_TAB}`, IntelligentTagsMainTabEnum.IntelligentTagsLogs)
        }

        /**
         * @description 智能流程逻辑处理
         * @param row 表格行数据
         */
        const handlerIntFlow = async () => {
            // 如果没有买触发器
            if (!getTriggerGray()) {
                isExistTrigger(currentCtx);
                return 
            }
            try {
                await handleFetchTriggerCount()
            } catch (error) {
                console.error(error)
            }
            if (!triggerISsSuccess.value) {
                return Message.error(triggerErrorMessage.value)
            }
            if (triggerResult.value?.result?.total === 0 || 
                triggerResult.value?.result?.used >= triggerResult.value?.result?.total
            ) {
                dosageNotification(currentCtx, { used: triggerResult.value?.result?.used, total: triggerResult.value?.result?.total })
                return
            }
            try {
                await handleFetchLabelTrigger({pageSize: 10,pageNum: 1,bizType: 'AI_LABEL',})
            } catch (error) {
                console.error(error)
            }
            // 如果不存在数据
            if (labelTriggerResult.value.result.total == 0) {
                CreateTriggerDialogRef.value && CreateTriggerDialogRef.value.showDialog()
            } else {
                createTriggerListRef.value && createTriggerListRef.value?.show()
            }
            
        }

        const handleSelection = (v: TagsItem [])=> {
            selectRowArray.value = v
        }

        /**
         * @desc 处理设置逻辑函数
         */
        const handlerSetting = (row: any) => {
            messageLabelShow.value = true
            nextTick(() => {
                SetMessageDialogRef.value && SetMessageDialogRef.value.show({
                    labelId: row.id,
                    config: row?.subscribeConfig?.configId || void 0
                })
            })
        }

        const handleChangeEnable = (v: number, row: TagsItem, prop: keyof TagsItem )=> {
            row[prop] = v as unknown as never
            //  type 1 为系统标签
            handleFetchEnableTagsListItem({ id: row.id, enabled: v, type: isNormalTag.value ? 0 : 1 })
        }

        const handleChangeEnableSubscribe = async (v: number, row: TagsItem, prop: TagsItem['subscribeConfig'] )=> {
            // console.log(v, row, prop)
            // 想要开启😊 先判断吧！？
            if (Boolean(v)) {
                if (!row?.subscribeConfig || !row?.subscribeConfig?.configId) {
                    pleaseSetMessageTips()
                    return
                }

                let res = await subscribeTips(currentCtx)
                if (!res) return
            }
            try {
                await handleFetchEnableSubscribe({  enabled: v || 0, id: row?.id || ''})
            } catch (error) {
                console.error('error:', error)
            }
            if (!enableSubscribeISsSuccess.value) {
                Message.error(enableSubscribeMessage.value)
                return
            }
            if (row.subscribeConfig && Reflect.has(row.subscribeConfig, 'isSubscribe')) {
                row.subscribeConfig['isSubscribe'] = v as unknown as never
            }
        }

        // 排序处理逻辑
        const handlerSort = () => {
            sortLayout.value = !sortLayout.value
        }

        // 排序保存处理逻辑
        const handlerSortSave = () => {
            emit("refreshTagList", { value: sortList.value })
            nextTick(() => {
                refreshTableData()
                sortLayout.value = false
            })
        }

        // 更新排序处理函数
        const handlerUpdateSort = (value: any) => {
            if (!value) return

            let currentCalc = tagsListWithPage.value.pageSize * (tagsListWithPage.value.pageNum - 1)
            let _v = tagsListWithPage.value.total - currentCalc
            sortList.value = value.map((item: any, index: number) => {
                return {
                    id: item.id,
                    labelSequence:  currentCalc + _v - index
                }
            })
        }

        const renderTableScopedSlots = (column: TableColumnItem)=> {
            return  {
               default: (scope: any)=>  {
                if(column.prop === 'enabled') {
                    return (
                        <el-switch
                            active-value={1}
                            inactive-value={0}
                            disabled={!intelligentTagsEditAuth.value}
                            value={scope.row[column.prop]}
                            onChange={(v: number)=> handleChangeEnable(v, scope.row, column.prop)}
                            >
                        </el-switch>  
                    )                  
                }

                if (column.prop === 'subscribeConfig') {
                    return (
                        <div class="intelligent_subscribe">
                            <el-switch
                                active-value={1}
                                inactive-value={0}
                                disabled={!intelligentTagsEditAuth.value}
                                value={scope.row['subscribeConfig']?.isSubscribe || 0}
                                key={scope.row['subscribeConfig']?.configId || ''}
                                onChange={(v: number)=> handleChangeEnableSubscribe(v, scope.row, column.prop)}
                            >
                            </el-switch>  
                            <span class="intelligent_subscribe__text" onClick={() => handlerSetting(scope.row)}>设置</span>
                        </div>
                    )
                }

                if(column.prop === 'updateTime') {
                    return formatDate(scope.row[column.prop])           
                }

                if(column.prop === 'name') {
                    return (
                        <div class="intelligent-tags-view__text">
                            <i class={["iconfont", isNormalTag.value ? 'icon-biaoqian-mian' : 'icon-zhinengbiao-mian']} style={{color: scope.row.logoColor}} ></i>
                            <span>{ scope.row[column.prop]}</span>
                        </div>
                    )      
                }

                if(column.prop === 'appList') {
                    if(scope.row['globalLabel'] === 1) return  '全部应用' 
                    if (!scope.row[column.prop]) return '全部应用'
                    return scope.row[column.prop].map((item: TagsModulesItem)=> item.name).join(',')
                }
                
                return scope.row[column.prop]
               },
               header: (scope: any) =>{
                if(isNormalTag.value ? scope.$index === 6 :  scope.$index === 1) {
                    let contentText = '停用后，已打的标签不受影响'
                    return (
                        <div class="intelligent-tags-table__column-header">
                            {scope.column.label}
                            <el-tooltip class="item" effect="dark" content={contentText} placement="top">
                                <i class="iconfont icon-question-circle"></i>
                            </el-tooltip>
                        </div>
                    )
                }
                return scope.column.label
               }
            }
        }  
        
        const handleSubmitTagForm = async (value: Record<string, any>) => {
            const params = cloneDeep(value)
            // params.labelAppList = params.labelAppList.map((item: TagsModulesItem)=> {
            //     const { appId, appName, bizType } = item 
            //     return {
            //         appId,
            //         appName,
            //         bizType
            //     }
            // })

            deleteObjectKeys(params, ['appList', 'createUserId', 'createUserName'])

            await handleFetchSubmitTagsListItem(params, Boolean(params.id))
            if(submitFetchSuccess.value) {
                Message.success('保存成功')
                createOrEditRef.value && createOrEditRef.value.hide()
                setTimeout(()=> {
                    const group = !isAppFilterTag.value ? groupList.value.find(groupItem => groupItem.id === params.groupId) : groupItem as unknown as TagsGroupItem & Partial<TagsModulesItem>
                    if(group) {
                        emit('selectGroup', group, true)
                    }
                }, 300)
            } else {
                Message.warning(submitFetchErrorMessage.value)
            }
        }

        // 批量编辑
        const handlerConfirmEdit = (labelList: any, groupId: string) => {
                refreshTableData()
        }

        // 消息关闭信息
        const handlerMessageLabel = (val: boolean) => {
            messageLabelShow.value = val
        }

        watch(() => isSort.value, (newVal) => {
            nextTick(() => {
                const table = newVal ? sortTableRef.value?.$children[0] : tableRef.value
                if (table && typeof table.doLayout === 'function') {
                    table.doLayout()
                }
            })
        })

        expose({
            refreshTableData
        })

        return ()=> (
            <div class="intelligent-tags-list__table-box"    
                {...{
                    directives: [{
                        name: 'loading', value: loading.value
                    }]
                }}>

                { isNormalTag.value ?  
                    <div>
                        {
                            !isAppFilterTag.value ? (
                                <div class="intelligent-tags-list__table-title">
                                    <h1 class="title">
                                        <div class="title-left">{groupName.value || ''}</div>
                                        <div class="title-right">
                                            <div class="flow-btn">
                                                <el-button type="text" size="small" class="intelligent-flow" onClick={handlerIntFlow} loading={flowIntLoading.value}>标签智能助手</el-button>
                                                <el-tooltip class="item" effect="dark" placement="top">
                                                    <div domPropsInnerHTML={`基于标签建立流程自动化机器人，可根据工\n单时效、客户等级等标签信息帮助您实现推\n送、预警、服务升级等功能。`} slot="content" style="white-space: pre-line"></div>
                                                    <i class="iconfont icon-question-circle"></i>
                                                </el-tooltip>
                                            </div>
                                        </div>
                                    </h1>
                                    <div class="title-box">
                                        <div class="left">
                                            <span class="label">应用范围：</span>
                                            <span class="content">
                                                <span class="tip">{ groupModuleInfo.value }</span>
                                            </span>
                                        </div>
                                        <div class="right">
                                            <span class="label">可见范围：</span>
                                            <span class="content">
                                                <span class="tip">{ groupUserInfo.value }</span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            ) : null
                        }
                        <div class="intelligent-tags-list__table-operator">
                            <div class="left">
                                {
                                    intelligentTagsCreateAuth.value ? (
                                        !isAppFilterTag.value ? (
                                            <el-dropdown split-button={true} type="primary" size="small" onClick={handleCreate} class="intelligent-tags-list__table-operator-dropdown">
                                                新建标签
                                                <el-dropdown-menu slot="dropdown">
                                                    <el-dropdown-item nativeOnClick={handleCreate}>新建标签</el-dropdown-item>
                                                    <el-dropdown-item nativeOnClick={handleCreateGroup}>批量新建</el-dropdown-item>
                                                </el-dropdown-menu>
                                            </el-dropdown>
                                        ) : (
                                            <el-button type="primary" onClick={handleCreate}>新建标签</el-button>
                                        )
                                        
                                        
                                    ) : null
                                }
                                {/* { intelligentTagsCreateAuth.value ? <el-button type="primary" onClick={handleCreate}>新建</el-button> : null } */}
                                { intelligentTagsDeleteAuth.value ? <el-button type="plain-third" onClick={()=> handleDelete()}>删除</el-button> : null }
                            </div>
                            {
                                !isAppFilterTag.value ? (
                                    <div>
                                        {
                                            sortLayout.value ? (
                                                <div style="display: flex;align-items: center;">
                                                    <el-button type="plain-third" onClick={() => handlerSort()}>取消</el-button>
                                                    <el-button type="primary" onClick={() => handlerSortSave()}>保存</el-button>
                                                </div>
                                            ) : (
                                                <el-button type="plain-third" onClick={() => handlerSort()}>排序</el-button>
                                            )
                                        }
                                    </div>
                                ) : null
                            }
                            
                        </div> 
                    </div>
                    
                    : 
                    groupDescription.value ? 
                    <div class="intelligent-tags-list__table-hint-group">
                        <div class="intelligent-tags-list__table-tips">
                            <i class="iconfont icon-info"></i>
                            <span class="text">{groupDescription.value}</span>
                        </div>  
                        <div class="intelligent-tags-list__table-unusual-setting">
                            <div class="flow-btn">
                                <el-button type="text" size="small" class="intelligent-flow" onClick={handlerIntFlow} loading={flowIntLoading.value}>标签智能助手</el-button>
                                <el-tooltip class="item" effect="dark" placement="top">
                                    <div domPropsInnerHTML={`基于标签建立流程自动化机器人，可根据工\n单时效、客户等级等标签信息帮助您实现推\n送、预警、服务升级等功能。`} slot="content" style="white-space: pre-line"></div>
                                    <i class="iconfont icon-question-circle"></i>
                                </el-tooltip>
                            </div>
                            <TaskExceptionSetting />
                        </div>
                    </div>
                    : 
                    null
                }
            <div class="intelligent-tags-list__table-content">
                <div class={['intelligent-tags-list__table', isFullTableHeight.value ? 'intelligent-tags-list__table-full' : null]}>
                    {
                        isSort.value ? (
                            <SortTable list={tagsListWithPage.value.list} onUpdateSort={handlerUpdateSort} ref={el => sortTableRef.value = el}>

                            </SortTable>
                        ) : (
                            <el-table
                                height={tableHeight.value}
                                data={tagsListWithPage.value.list}
                                class="common-list-table bbx-normal-list-box"
                                border={true}
                                cell-style={{'border-left': 'none','border-right': 'none'}}
                                headerCellStyle={{ background: '#fafafa', color: '#262626' }}
                                row-key="id"
                                onSelect={handleSelection}
                                onSelect-all={handleSelection}
                                ref={el => tableRef.value = el}
                                border>
                                    <template slot="empty">
                                        {/* @ts-ignore */}
                                        <BaseListForNoData></BaseListForNoData>
                                    </template>
                                    { isNormalTag.value ?   
                                        <el-table-column
                                            type="selection"
                                            width="55">
                                        </el-table-column> 
                                        : null 
                                    }
                                    {currentTableColumn.value.map(column=> {
                                         return <el-table-column
                                                    prop={column.prop}
                                                    label={column.name}
                                                    min-width={isNormalTag.value ? column.width : ''}
                                                    scopedSlots={renderTableScopedSlots(column)}
                                                    show-overflow-tooltip
                                                    resizable>
                                                </el-table-column>
                                        })}
                                    { isNormalTag.value ? 
                                        <el-table-column
                                            fixed="right"
                                            label="操作"
                                            width="160"
                                            scopedSlots={{
                                                default: (scope: { row: TagsItem }) => {
                                                    return (
                                                        // @ts-ignore
                                                        <div class="action-group">
                                                            {
                                                                intelligentTagsEditAuth.value ? (
                                                                    <el-button type="text" size="small" onClick={()=> handleEdit(scope.row)}>编辑</el-button>
                                                                ) : null 
                                                            }
                                                                <el-button type="text" size="small" onClick={()=> handleToLogsPage(scope.row)}>日志</el-button>
                                                            {
                                                                intelligentTagsDeleteAuth.value ? (
                                                                    <el-button type="text" size="small" onClick={()=> handleDelete(scope.row)}>删除</el-button>
                                                                ) : null
                                                            }
                                                        </div>
                                                    )
                                                },
                                            }}>
                                        </el-table-column>
                                    : null
                                    }
                            </el-table>
                        )
                    }
                    
                </div>
                { isNormalTag.value && !isSort.value ?  
                    <div class="table-footer comment-list-table-footer bbx-normal-table-footer-10">
                        <div class="list-info">
                            <i18n path="common.base.table.totalCount">
                                {/* @ts-ignore */}
                                <span place="count" class="level-padding">{tagsListWithPage.value.total}</span>
                            </i18n>
                            <template>
                                <i18n path="common.base.table.selectedNth">
                                    {/* @ts-ignore */}
                                    <span place="count" class="color-primary pad-l-5 pad-r-5"></span>
                                </i18n>
                                <span class="color-primary cur-point">{t('common.base.clear')}</span>
                            </template>
                        </div>
                        <el-pagination
                            background
                            class="comment-list-table-footer-pagination"
                            pageSize={tagsListWithPage.value.pageSize}
                            currentPage={tagsListWithPage.value.pageNum}
                            total={tagsListWithPage.value.total}
                            layout="prev, pager, next, sizes, jumper"
                            {...{
                                on: {
                                    'current-change': handleTagsListCurrentChange,
                                    'size-change': handleTagsListPageChange
                                }
                            }}
                        >
                        </el-pagination>
                    </div>
                    : 
                    null
                }
            </div>
                <CreateOrEditTagForm pending={submitFetchLoading.value} ref={createOrEditRef} groupList={groupList.value} onSubmit={handleSubmitTagForm}/>
                <LabelBatchDialog groupItem={groupItem.value} ref={el => createLabelEditRef.value = el} onConfirm={handlerConfirmEdit} />
                    {/* 消息通知弹窗 */}
                    {
                        messageLabelShow.value ? (
                            <SetMessageDialog ref={el => SetMessageDialogRef.value = el} onRefreshList={refreshListHandlerMessage} onCloseMessageLabel={handlerMessageLabel} />
                        ) : null
                    }
                <TriggerListDialog ref={el => createTriggerListRef.value = el} />
                <CreateTriggerDialog 
                    ref={ el => CreateTriggerDialogRef.value = el}
                    onSubmit={onDialogSubmit}
                    showTriggerWay={showTriggerWay}
                    from={'tagsList'}
                />
            </div>
        )
    }
})