<template>
  <base-modal
    :title="approveButtonText" 
    :show="visible"
    width="500px"
    height="500px"
    class="task-advanced-approve-dialog"
    @cancel="visible = false">
    <div class="base-modal-content">
      <div class="signature-advise">
        <div class='nameAndAction'>
        <div class="form-view-row">
          <label>{{$t('common.base.promoter')}}</label>
          <div class="form-view-row-content">
            <template v-if="isOpenData && approve.proposerStaffId">
              <open-data type="userName" :openid="approve.proposerStaffId"></open-data>
            </template>
            <template v-else>
              {{ approve.proposerName }}
            </template>
          </div>
        </div>
        <div class="form-view-row">
          <label>{{$t('common.base.node')}}</label>
          <div class="form-view-row-content">{{ getApproveActionLabel(approve.action) }}</div>
        </div>
        
      </div>
      <!-- 指派时显示负责人和协同人 start -->
      <div class='nameAndAction' v-if="['指派', '转派'].includes(approve.action)">
        <div class="form-view-row">
          <label>{{$t('task.executor')}}</label>
          <div class="form-view-row-content">
            <template v-if="isOpenData && executor.staffId">
              <open-data type="userName" :openid="executor.staffId"></open-data>
            </template>
            <template v-else>
              {{ executor.displayName }}
            </template>
          </div>
        </div>
        <div class="form-view-row">
          <label>{{$t('task.synergies')}}</label>
          <div class="form-view-row-content">
            <template v-if="isOpenData && synergies">
              <open-data v-for="item in synergies" type="userName" :openid="item.staffId"></open-data>
            </template>
            <template v-else>
              {{ synergies.map(item => item.displayName).join(',') || '' }}
            </template>
          </div>
        </div>
      </div>
      <!-- 指派时显示负责人和协同人 end -->
      <div class="form-view-row">
        <label>{{$t('task.promoteTime')}}</label>
        <div class="form-view-row-content">{{ approve.createTime | fmt_datetime }}</div>
      </div>
      <div class="form-view-row">
        <label>{{$t('task.promoteRemark')}}</label>
        <div class="form-view-row-content">{{ atTextToHtml1(approve.applyRemark) }}</div>
      </div>
      <div class="form-view-row" v-if="approve.source == 'task' && approve.action == '暂停'">
        <label>{{$t('task.pauseReason')}}</label>
        <div class="form-view-row-content">{{ approve.reason }}</div>
      </div>
      <div class="form-view-row" v-if="approve.source == 'task' && approve.action == '暂停'">
        <label>{{$t('common.base.usualStatus.paused')}}{{$t('task.detailReason')}}</label>
        <div class="form-view-row-content">{{ approve.detailedReasons }}</div>
      </div>
      </div>
      
    </div>
    <div class="base-modal-content task-advanced-approve mt4">
      <template v-if="vipBtnSetting && vipBtnSetting.showRemark">
        <el-row>
          <label><span style="color:red" v-if="vipBtnSetting.checkRemark">*</span>{{ $t('common.paas.view.template.detail.approveSuggestion') }}</label>
        </el-row>
        <biz-at-textarea 
          class="biz-at-textarea" 
          ref="atTextarea" 
          search-url="/message/user/lists" 
          name-key="displayName" 
          v-model="approveRemark" 
          :at-users.sync="atUsers"
          direction="bottom">
          <template slot="item" slot-scope="item">
            <img :src="head(item.user.head)">
            <span>{{item.user.displayName}}</span>
          </template>
          <textarea 
            ref="editor" 
            class="base-at-textarea" 
            @input="inputContent" 
            :placeholder="$t('task.tip.approveTip1')" 
            maxlength="500" 
            :rows="3">
          </textarea>
        </biz-at-textarea>
      </template>
      <template v-if="vipBtnSetting && vipBtnSetting.showAttached">
        <el-row class="approve-attach">
          <label><span style="color:red" v-if="vipBtnSetting.checkAttached">*</span>{{$t('common.form.type.attachment')}}</label>
          <base-upload 
            size="small"
            :value="approveAttachments"
            @input="upload" 
            @remove="remove" 
          ></base-upload>
        </el-row>
        <p class="approve-tips">{{$t('task.tip.approveTip2')}}</p>
      </template>
      <div class="signature-content" v-if="enableNewSign">
        <div class="padding-top is-required">{{ $t('account.signature') }}</div>
        <div class="form-view-row">
          <div v-if="enableLastSign && approveSignConfig.lastSignData" class="form-view-row-content">
            <img class="signature-img" :src="approveSignConfig.lastSignData" />
          </div>
          <div v-else class="form-view-row-content">
            <div class="form-view-row-content-signature">
              <div class="change-line">
                <div>{{ $t('task.autograph') }}</div>
                <div>{{ $t('task.mobileHandle') }} </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <div class="dialog-footer-right">
        <el-button @click="visible = false" :disabled="pending">{{ $t('common.paas.buttons.cancel') }}</el-button>
        <el-button :type="vipBtnSetting.enName == 'agree' ? 'primary' : 'danger'" @click="submit('success')" :disabled="pending" :loading="pending">{{ vipBtnSetting.cnName }}</el-button>
      </div>
    </div>
  </base-modal>
</template>

<script>
import { isOpenData } from '@src/util/platform'
import { ApproveActionLabelEnum } from '@model/enum/LabelEnum.ts'
import { enCodeAtText, atTextToHtml1 } from '@src/util/atText';
export default {
  name: 'TaskAdvancedApprove',
  data() {
    return {
      isOpenData,
      visible: false,
      isSignatures: false,
      fromVip: false,
      pending: false,
      approveRemark: '',
      atUsers: [],
      atTextToHtml1,
    }
  },
  props: {
    approveButtonText: {
      default: '',
      type: String 
    },
    vipBtnSetting: {
      type: Object,
      default: () => ({}),
    },
    approveAttachments: {
      type: Array,
      default: () => ([]),
    },
    approveInfoParams: {
      type: Object,
      default: () => ({}),
    },
    // 审批数据
    signaturesApprove: {
      default: () => ({}),
      type: Object
    },
    // 审批数据
    approve: {
      default: () => ({}),
      type: Object
    },
    head: {
      type: Function
    },
  },
  computed: {
    // 指派负责人
    executor() {
      return this.approve?.otherInfo?.params.executor || {}
    },
    // 指派协同人
    synergies() {
      return this.approve?.otherInfo?.params.synergies || []
    },
    // 开启审批签名
    approveSignConfig() {
      return this.approveInfoParams.approveSignConfig || {};
    },
    enableNewSign() {
      return this.approveSignConfig.newSign;
    },
    enableLastSign() {
      return this.approveSignConfig.lastSign;
    },
  },
  methods: {
    // 获取action的多语言label
    getApproveActionLabel(key) {
      return ApproveActionLabelEnum[key] || key
    },
    open() {
      this.visible = true;
      this.pending = false;
      // 重置
      this.reset()
    },
    reset() {
      this.approveRemark = '';
      this.atUsers = [];
    },
    inputContent(event) {
      let value = event.target.value;
      this.approveRemark = value;
    },
    submit() {
      if (this.enableNewSign && !this.approveSignConfig.lastSignData) {
        return this.$platform.notification({ type: 'error', title: this.$t('task.tip.approvalSignatureTip2') })
      }

      const { checkRemark = false, checkAttached = false } = this.vipBtnSetting

      if (checkRemark && !this.approveRemark) {
        return this.$platform.notification({ type: 'error', title: this.$t('task.tip.approveTip4')})
      }

      if (checkAttached && !this.approveAttachments?.length) {
        return this.$platform.notification({ type: 'error', title: this.$t('task.tip.vipApproveTip')})
      }

      const remark = enCodeAtText(this.approveRemark, this.atUsers);

      const params = {
        approveRemark: this.approveRemark,
        applyMessage: remark,
        atUsers: this.atUsers
      }

      if(this.enableNewSign && this.enableLastSign) {
        params.sign = this.approveSignConfig.lastSignData;
      }

      this.pending = true;
      this.$emit('submit', params);
    },
    upload(queue) {
      this.$emit('upload', queue);
    },
    remove(file) {
      this.$emit('remove', file);
    }
  }
}
</script>
<style lang="scss" scoped>
.padding-top {
  padding-top: 10px;
}
.is-required {
  &::before {
    content: "*";
    color: #F56C6C;
    margin-right: 4px;
  }
}

::v-deep .el-input__inner {
  height: 100px !important;
  text-align: center;
}

.form-view-row {
  padding:10px 0 0 0  !important;
  .form-view-row-content {
    img {
      height: 100px;
    }
    .form-view-row-content-signature {
      width: 100%;
      height: 100px;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #f5f8fa;
      cursor: not-allowed;
      .change-line {
        color: #BFBFBF;
        width: 45%;
        text-align: center;
      }
    }
  }
}

.biz-at-textarea {
  margin-bottom: 10px;
}

.task-advanced-approve {
  label {
    margin-bottom: 10px;
  }
  .approve-tips {
    color: #999;
    font-size: 12px;
    margin-top: 6px;
  }
}
.signature-advise {
  .form-view-row {
    label {
      color:#262626;
      //width:auto!important;
      min-width: auto;
    }
  }
  .nameAndAction {
    display:flex;
    .form-view-row {
      width:50%;
    }
  }
  .mt10 {
    margin-top:16px;
  }
}
.mt4 {
  margin-top:10px;
}

</style>