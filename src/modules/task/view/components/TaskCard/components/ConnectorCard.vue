<template>
  <div class="connector-card-container">
    
    <ConnectorModuleConnectorCard
      :main-module-value="mainModuleValue"
      :card="card"
      :task="task"
      :bizId="bizId"
      :visible="visible"
      :fromBizNo="fromBizNo"
      :showCreateButton="showCreateButton"
      :showEditButton="showEditButton"
      :showDeleteButton="showDeleteButton"
      :showExportButton="showExportButton"
      :showRelationButton="showRelationButton"
    />
    
  </div>
</template>

<script>
/* components */
import { ConnectorModuleConnectorCard } from '@src/modules/connector/components'
/* mixin */
import cardMixin from './CardMixin.js'

export default {
  name: 'connector-card-warper',
  mixins: [cardMixin],
  props: {
    bizId: {
      type: String,
      default: ''
    },
    mainModuleValue: {
      type: Object,
      default: () => ({})
    },
    card: {
      type: Object,
      default: () => ({})
    },
    task: {
      type: Object,
      default: () => ({})
    },
    visible: {
      type: Boolean,
      default: false
    },
  },
  computed: {
    // 是否不为归档工单，已归档的工单不能操作数据
    isNotArchive() {
      return !this.shareData.isArchive
    },
    // 是否显示新建按钮
    showCreateButton() {
      return this.isNotArchive && this.allowCreate
    },
    // 是否显示编辑按钮
    showEditButton() {
      return this.isNotArchive && this.allowEdit
    },
    // 是否显示删除按钮
    showDeleteButton() {
      return this.isNotArchive && this.allowDelete
    },
    // 是否显示导出按钮
    showExportButton() {
      return this.isNotArchive && this.isExport
    },
    // 是否显示关联添加按钮
    showRelationButton() {
      return this.isNotArchive && this.allowCreate
    },
    fromBizNo() {
      return this.mainModuleValue?.taskNo || ''
    }
  },
  components: {
    ConnectorModuleConnectorCard
  }
}
</script>
<style lang="scss"> 
</style>