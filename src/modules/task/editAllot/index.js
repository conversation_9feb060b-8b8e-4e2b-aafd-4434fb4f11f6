import Vue from 'vue'
import http from '@src/util/http'
import TaskEditView from './view/TaskEditView.vue'
import { Loading } from 'element-ui';
import { getAsyncTaskEditAllotInitData } from '@src/api/InitDataApi'
import { getTaskInfoByServiceShopOrder } from '@src/api/TaskApi'
import qs from 'qs';
import platform from '@src/platform';
import { RESOURCE_PREFIX } from '@src/util/linkSwitch';
import { v4 as uuidv4 } from 'uuid';
import { isCarrieGray } from '@src/util/grayInfo'

const componentItem = (initData) => {
  return {
    provide: {
      initData: Object.freeze(initData)
    },
    render(h) {
      return h(TaskEditView);
    }
  }
}

Vue.component('async-component', function(resolve) {
  
  // 需要加loading 打开此注释
  const instance = Loading.service({ fullscreen: true });
  
  let query = qs.parse(window.location.search.substr(1));
  let pathName = window.location.pathname
  if(pathName.indexOf(RESOURCE_PREFIX) > -1) pathName = pathName.replace(RESOURCE_PREFIX, '')
  let params = Object.assign({}, query, {})
  // 是否带id的url
  const isIdUrl = [
    { path:'/task/edit', type:'taskId' },
    { path:'/task/noFilterEdit', type:'taskId' },
    { path:'/task/createFromCustomer', type:'id' },
    { path:'/task/createFromProduct', type:'id' },
  ]
  isIdUrl.forEach(item=>{ 
    if(pathName !== item.path && pathName.includes(item.path) && pathName !== '/task/edit4CallCenter' ){
      const id = window.location.href.substring(window.location.href.lastIndexOf('/') + 1, window.location.href.length).split('?')[0]
      params[item.type] = id
      pathName = pathName.replace(`/${id}`, '')
    }
  })
  const urlList = ['/task/copyTask', '/task/planTask/create', '/task/planTask/edit', '/task/planTask/copy', '/event/convent2Task/jump'] 
  
  let url = ''
  if(urlList.includes(pathName)){
    url = `${pathName}/init` 
  }else if (pathName.indexOf( 'task/noFilterEdit') !== -1){
    url = '/task/editV2/init'
  }else if (pathName.indexOf('task/edit4CallCenter') !== -1){
    url = '/task/edit4CallCenter/init'
  }else{
    url = `${pathName}V2/init` 
  }
  
  getAsyncTaskEditAllotInitData(params, url).then(async result => {
    let initData = result?.data?.initJson || {}
    try {
      // 开利订单带入工单信息
      if(query?.bizType == 'serviceShop' && isCarrieGray){
        let getTaskInfoByServiceShopOrderRes = await getTaskInfoByServiceShopOrder({
          orderId:query.bizId
        })
        if(getTaskInfoByServiceShopOrderRes.code === 0){
          initData.task = getTaskInfoByServiceShopOrderRes.data
        }
      }
    } catch (error) {
      console.error(error, 'getTaskInfoByServiceShopOrder is Error')
    }
    
    const title = (result && result.data && result.data.title) || ''
    const isSuccess = result.succ || result.success
    
    if (!isSuccess) {
      window.location.href = '/500'
      return 
    }
    
    try {
      window._init = JSON.stringify(initData)
    } catch (error) {
      console.error('initData 赋值失败')
    }
    
    // 需要加loading 打开此注释
    instance.close();
    
    if (window.frameElement) {
      const currentTabId = window.frameElement.dataset.id;
      platform.setTabTitle({
        id: currentTabId,
        title
      })
    }

    try {
      const taskId = initData?.task?.id
      if (!taskId) {
        const id = uuidv4();
        initData.task.tempId = 'TEMP_' + id
      }
    } catch (error) {
      console.error('initData 赋值失败')
    }

    resolve(componentItem(initData))
    
  }).catch(error => {
    console.error(error)
    window.location.href = '/500'
  })
  
});

let app = {
  render(h) {
    return <async-component />
  }
}

export default app;
