
<template>
  <div class="task-box task-list-view">
    <div id="task-task-list-view"></div>
    <!-- s 列表展示 -->
    <div
      class="task-list-view common-list-container flex-y"
      ref="taskListPage"
    > 
      <div ref="tableHeaderContainer" v-if="!showAiSearch">
        <div class="task-list-header" :style="{paddingBottom: `${headerPaddingTop} !important`}">
          <!-- 搜索 -->
          <div class="task-list-header-seach">
            <form onsubmit="return false;">
              <div class="seach task-span1 guide-box no-padding-left">
                <div style="position: relative;" >
                  <div itemid="" @mouseenter="guideDropdownMenu_enter" class="task-flex task-ai jus-bet">
                    <div class="flex align-items-center int-tags-btn">
                        <BizIntelligentTagsFilterPanelOperatorButton
                          v-if="!mapShow"
                          :showDot="showTagOperatorButtonDot"
                          :active="filterTagPanelShow"
                          @click="changeIntelligentTagsFilterPanelShow"/>
                        <!-- 视图列表 -->
                        <div id="task-filter-set-1" :class="showViewportGuide ? 'viewport-guide' : 'viewport-no-guide'">
                          <viewport-dropdown
                            ref="viewportListRef"
                            module="task"
                            :current-view="currentView"
                            @choose="chooseView"
                            @edit="editViewByViewport"
                            @create="editViewByViewport"
                            :pre-views="preViews"
                          ></viewport-dropdown>
                        </div>
                    </div>
                    <!-- 视图编辑弹框 -->
                    <advanced-search-modal
                      ref="advancedSearchModalRef"
                      module="task"
                      :allow-empty="true"
                      :fields="seoSetList"
                      :before-save="beforeSaveView"
                      @save="handleViewportSave">
                      <!-- 快捷条件 -->
                      <task-common-advanced-search-form
                        slot="prefix"
                        ref="modelCommonAdvancedSearchFromRef"
                        v-bind="shortcutConditions"
                        :state-options="taskStates"
                        :visual-angle-options="visualAngleList"
                        :type-options="taskTypes"
                        :exception-node-options="taskCustomExceptionNodeList"
                      />
                    </advanced-search-modal>

                    <div class="task-flex">
                      <el-input
                        v-model="newSearchParams.keyword"
                        v-trim:blur
                        :placeholder="
                          taskSearchInputPlaceholderMap[newSearchParams.searchCondition] ||
                            taskSearchInputPlaceholderMap.default
                        "
                        @keyup.enter.native="handleSearch"
                        class="task-select-search-input input-with-append-search task-mr12"
                      >
                        <el-select
                          v-model="newSearchParams.searchCondition"
                          slot="prepend"
                          :placeholder="$t('common.placeholder.select')"
                          class="task-with-select"
                        >
                          <!-- TODO 国际化待办 -->
                          <el-option :label="$t('task.list.formContent')" value=""></el-option>
                          <el-option :label="$t('common.base.remark')" value="按工单备注"></el-option>
                          <el-option :label="$t('task.record.taskCard')" value="按附加组件"></el-option>
                        </el-select>
                        <el-button
                          type="primary"
                          slot="append"
                          @click="handleSearch"
                          v-track="$track.formatParams('KEYWORD_SEARCH')">{{$t('common.base.search')}}</el-button>
                      </el-input>
                      <el-button type="plain-third" @click="handleReset" v-track="$track.formatParams('RESET_SEARCH')">
                        {{$t('common.base.reset')}}
                      </el-button>
                      <div class="guide-box">
                        <div
                          id="v-task-step-2"
                          :class="['advanced-search-visible-btn', 'task-ml12']"
                        >
                          <advanced-search
                            ref="popperAdvancedSearchRef"
                            :fields="seoSetList"
                            :search-model="viewportSearchModel"
                            :has-save="!!(currentView && currentView.viewId && currentView.authEdit)"
                            :in-common-use="inCommonUse"
                            @search="handleAdvancedSearch"
                            @create="createViewBySearchModel"
                            @save="updateViewBySearchModel"
                            @reset="handleReset"
                            @changeCommonUse="changeCommonUse"
                            module="task"
                          >
                          </advanced-search>
                        </div>
                      </div>
                      <div v-if="isAI_SEARCHGray" type="plain-third" @click="handleAiSaerch" class="ai-button-seach ai-button-search task-ai-button">
                        <!-- <i class="iconfont icon-magic-full" ></i> -->
                          <div class="content">
                                <img src="../../../assets/img/icon-magic-full.gif" class="icon-magic-full">
                                <span>AI搜索</span>
                          </div>
                          <AiFreeButton />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </div>
          <!-- 筛选 -->
          <div class="task-list-header-nav" v-show="packUp">
            <BaseFilterTagList ref="statesFilterRef" :label="$t('common.base.statusAngle')+':'" :active="taskState" :list="taskStates" @choose="handleFilterClick($event, '状态视角')" @toggleCollapse="updateListHeight">
              <template v-slot="{label, value}">
                <!-- 异常工单 -->
                <el-tooltip
                  v-if="value ==='exception' && abnormalData.hoverText"
                  :content="`${$t('task.list.currentChose')} ${abnormalData.hoverText}`"
                  placement="top"
                >
                  <span class="actived task-cef">{{label}} {{filterData && filterData[value] ? `(${filterData[value]})` : ''}}</span>
                </el-tooltip>
                <!-- 异常工单 -->
                <template v-else>
                  {{label}}{{filterData && filterData[value] ? `(${filterData[value]})` : ''}}
                </template>
              </template>
            </BaseFilterTagList>
            <BaseFilterTagList :label="$t('common.base.createAngle')+':'" :active="visualAngle" :list="visualAngleList" @choose="handleFilterClick($event, '创建视角')" @toggleCollapse="updateListHeight"></BaseFilterTagList>
            <BaseFilterTagList :label="$t('common.task.taskType')+':'" :active="currentTaskType.id" :list="taskTypes" :props="{label: 'name', value: 'id'}" @choose="handleFilterClick($event, '工单类型')" @toggleCollapse="updateListHeight"></BaseFilterTagList>
            <BaseFilterTagList v-show="taskState === 'exception'" :label="$t('common.task.exceptionNode')+':'" :active="exceptionNodes" :list="taskCustomExceptionNodeList" :props="{label: 'exceptionName', value: 'englishName'}" @choose="checkAbnormal" @toggleCollapse="updateListHeight"></BaseFilterTagList>
          </div>
        </div>
        <div class="pack-up">
          <div @click="changePackUp()">
            <i class="iconfont icon-Icon_up" v-show="packUp"></i>
            <i class="iconfont icon-more" v-show="!packUp"></i>
          </div>
        </div>
      </div>

      <AiSearchTaskInput
        v-if="showAiSearch"
        ref="aiSearch"
        :isHaveNodeFlowAuth="isHaveNodeFlowAuth"
        :planTimeType="planTimeType"
        :planStartTimeType="planStartTimeType"
        :planEndTimeType="planEndTimeType"
        :currentPage="taskPage"
        @beforeSearch="aiSearchHandlerBeforeSearch"
        @afterSearch="aiSearchHandlerAfterSearch"
        @searchError="aiSearchHandlerSearchError"
        @searchCatch="aiSearchHandlerSearchCatch"
        @searchFinally="aiSearchHandlerSearchFinally"
        @searchSuccess="aiSearchHandlerSearchSuccess"
        @close="aiSearchHandlerClose"
      />

      <div class="common-list-table__flex-row">
        <BizIntelligentTagsFilterPanel
            v-if="!mapShow"
            v-bind="filterTagsPanelBindAttr"
            v-on="filterTagsPanelBindOn"
        />
        <div class="common-list-section" v-show="!mapShow">
          <!--operation bar start-->
          <div ref="tableDoContainer" class="task-list-operation-bar-container task-flex task-ai">
            <div class="top-btn-group task-span1 task-flex task-ai">
              <!-- 新建 -->
              <el-dropdown v-if="auth.TASK_ADD">
                <el-button type="primary" icon="el-icon-plus">{{$t('common.base.create')}}</el-button>
                <el-dropdown-menu slot="dropdown">
                  <div class="task-type-dropdown-group">
                    <el-dropdown-item
                      v-for="(item, index) in createTaskTypeList"
                      :key="index"
                    >
                      <div @click="createTask(item)">{{ taskTypeListLanguage(item) }}</div>
                    </el-dropdown-item>
                  </div>
                </el-dropdown-menu>
              </el-dropdown>

              <!-- 批量编辑 -->
              <el-button
                type="plain-third"
                v-if="exportPermissionTaskEdit"
                @click="Alledit"
                v-track="$track.formatParams('BATCH_EDIT')"
              >
                <i class="iconfont icon-edit-square"></i>{{$t('common.base.bulkEdit')}}
              </el-button>

              <!-- 删除 -->
              <el-button
                type="plain-third"
                v-if="allowDelete"
                @click="delTask"
                v-track="$track.formatParams('DELETE')"
              >
                <i class="iconfont icon-delete"></i>{{$t('common.base.delete')}}
              </el-button>

              <!-- start 1.0 Ai摘要 -->
              <el-button
                v-if="isShowAISummary"
                type="ai"
                @click="aiTask"
                class="ai-button task-ai-button"
              >
                <i class="iconfont icon-AI"></i>AI {{$t('common.base.abstract')}}
              </el-button>
              <!-- end 1.0 Ai摘要 -->

              <!-- start 2.0 Ai摘要 -->
              <AISummary
                v-if="allowSummary"
                is-new
                is-task
                :fields="columns"
                :template="taskSummaryTemplate"
                :multipleSelection="multipleSelection"
                :taskTypesMap="taskTypesMap"
              />
              <!-- end 2.0 Ai摘要 -->
              
              <template v-for="item in customButtonList">
                <el-button
                :type="item.viewType"
                :key="item.buttonId"
                :disabled="customButtonPending(item)"
                @click="handlePageButtonClick(item, multipleSelection, taskFields)"
                >
                {{item.name}}
              </el-button>
              </template>

            </div>

            <div class="action-button-group task-flex task-ai">
              <BizIntelligentTaggingButtonMulti v-bind="taggingBindAttr" v-on="taggingBindOn" />
              <!-- 模式 -->
              <el-dropdown>
                <div class="task-ai task-flex task-font14 task-c6 task-pointer cur-point">
                  <span class="task-mr4 task-ml4">{{
                    mapShow ? $t('common.base.mapMode') : $t('common.base.listMode')
                  }}</span>
                  <i class="iconfont icon-fdn-select"></i>
                </div>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item>
                    <div @click="taskMode($t('common.base.listMode'))">{{$t('common.base.listMode')}}</div>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <div @click="taskMode($t('common.base.mapMode'))">{{$t('common.base.mapMode')}}</div>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>

              <!-- start 更多操作 -->
              <el-dropdown v-if="showMoreActionBtn">
                <div
                  class="task-ai task-flex task-font14 task-c6 task-pointer cur-point"
                  @click="trackEventHandler('moreAction')"
                >
                  <span class="task-mr4">{{$t('common.base.moreOperator')}}</span>
                  <i class="iconfont icon-fdn-select"></i>
                </div>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    v-if="showImportBtn">
                    <div class="import-task">
                      {{$t('common.base.importTask')}}
                      <div class="import-task-item import-item">
                        <div
                          v-for="(item, index) in taskTypeList"
                          :key="index"
                          @click="imporTask(item)"
                          v-track="$track.formatParams('IMPORT_TASK', null, 'MORE_ACTIONS')"
                        >
                          {{ item.name }}
                        </div>
                      </div>
                    </div>
                  </el-dropdown-item>

                  <el-dropdown-item v-if="isShowImportCost">
                    <div @click="imporCost()" v-track="$track.formatParams('IMPORT_COST', null, 'MORE_ACTIONS')">{{$t('common.base.importCost')}}</div>
                  </el-dropdown-item>

                  <el-dropdown-item v-if="showExportBtn">
                    <div @click="exportTask(false)" v-track="$track.formatParams('EXPORT', null, 'MORE_ACTIONS')">{{$t('common.base.export')}}</div>
                  </el-dropdown-item>

                  <el-dropdown-item v-if="showExportBtn">
                    <div @click="exportTask(true)" v-track="$track.formatParams('EXPORT_ALL', null, 'MORE_ACTIONS')">{{$t('common.base.exportAll')}}</div>
                  </el-dropdown-item>
                  <el-dropdown-item v-if="canTaskExport">
                    <!-- 附件下载 -->
                    <div @click="exportAttachment" v-track="$track.formatParams('DOWNLOAD_ATTACHMENT', null, 'MORE_ACTIONS')">{{$t('common.base.downloadAttachment')}}</div>
                  </el-dropdown-item>
                  <!-- 工单转派 -->
                  <el-dropdown-item v-if="batchReallotPermission">
                    <div @click="reallotBatch" v-track="$track.formatParams('TASK_RE_ALLOT', null, 'MORE_ACTIONS')">{{$t('common.task.taskAllot')}}</div>
                  </el-dropdown-item>
                  <!-- start 批量生成服务报告 -->
                  <el-dropdown-item v-if="showBatchCreateServiceReportBtn">
                    <div @click="batchCreateServiceReport" v-track="$track.formatParams('BATCH_CREATE_SERVICE_REPORT', null, 'MORE_ACTIONS')">{{$t('common.task.batchGenServiceReport')}}</div>
                  </el-dropdown-item>
                  <!-- end 批量生成服务报告 -->
                  <!-- start 批量打印工单 -->
                  <el-dropdown-item v-if="showBatchPrintBtn">
                    <div @click="batchPrintServiceReport" v-track="$track.formatParams('BATCH_PRINT_TASK', null, 'MORE_ACTIONS')">{{$t('common.task.batchPrintTask')}}</div>
                  </el-dropdown-item>
                  <!-- end 批量打印工单 -->
                  <!-- start 归档工单 -->
                  <el-dropdown-item v-if="showGuidangBtn">
                    <div @click="guidang" v-track="$track.formatParams('TASK_ARCHIVE', null, 'MORE_ACTIONS')">{{$t('task.archiveTask')}}</div>
                  </el-dropdown-item>
                  <!-- end 归档工单 -->
                  <!-- start 批量导出附加组件 -->
                  <el-dropdown-item v-if="showExportAdditional">
                    <div class="import-task">
                      {{$t('common.base.BatchExport')}}
                      <div class="import-task-item import-item">
                        <div
                          v-for="(item, index) in taskCardList"
                          :key="index"
                          @click="exportAdditional(item)"
                        >
                          {{ item.name }}
                        </div>
                      </div>
                    </div>
                  </el-dropdown-item>
                  <!-- end 批量导出附加组件 -->
                </el-dropdown-menu>
              </el-dropdown>

              <!-- 选择列 -->
              <div class="guide-box">
                <div
                  :class="['task-ai', 'task-flex', 'task-font14', 'task-pointer', ' cur-point, task-select-column']"
                  id="v-task-step-1"
                  @click="showColumnsSetting"
                  v-track="$track.formatParams('SELECT_COLUMN')"
                >
                  <span class="task-mr4">{{$t('common.base.choiceCol')}}</span>
                  <i class="iconfont icon-fdn-select"></i>
                </div>
              </div>
            </div>
          </div>
          <!-- start content 列表表格 -->
          <div class="guide-box" id="v-task-step-0">
            <div ref="tableContainer">
              <div
                class="task-list-section common-list-table-view"
                v-loading="loading"
              >
                <el-table
                  v-if="columns.length"
                  stripe
                  :data="taskPage.list"
                  :highlight-current-row="false"
                  :key="tableKey"
                  :border="true"
                  @select="handleSelection"
                  @select-all="handleSelection"
                  @sort-change="sortChange"
                  @header-dragend="headerDragend"
                  :class="['task-list-table', 'common-list-table', 'bbx-normal-list-box']"
                  header-row-class-name="common-list-table-header taks-list-table-header bg-w"
                  ref="multipleTable"
                  :height="tableContainerHeight"
                >
                  <template slot="empty">
                    <BaseListForNoData v-show="!loading" :notice-msg="$t('common.base.tip.noData')"></BaseListForNoData>
                  </template>
                  <el-table-column
                    type="selection"
                    width="48"
                    align="center"
                    class-name="select-column"
                  ></el-table-column>
                  <el-table-column
                    v-for="column in columns"
                    v-if="column && column.show"
                    :align="column.align"
                    :class-name="
                      column.field == 'name'
                        ? 'common-list-table-name-superscript-td'
                        : ''
                    "
                    :key="column.field"
                    :label="column.label"
                    :min-width="column.minWidth"
                    :prop="column.field"
                    :sortable="column.sortable"
                    :show-overflow-tooltip="getCommonListShowTooltip(column) && column.field !== 'updateTime' && column.field !=='description' && column.field !== 'taskNo' && column.field !== 'customer' && column.field !== 'tlmName'"
                    :width="column.width"
                    :resizable="true"
                    :fixed="column.fixLeft || false"
                  >
                    <template slot-scope="scope">
                      <!-- 工单编号 -->
                      <template v-if="column.field === 'taskNo'">
                        <sample-tooltip :row="scope.row" v-if="isShowTagGroup">
                          <template slot="content" slot-scope="{isContentTooltip}">
                            <el-tooltip
                              :content="scope.row[column.field]"
                              placement="top"
                              :disabled="!isContentTooltip"
                            >
                              <div>
                                <a
                                  href=""
                                  :class="globalIsHaveTaskViewDetailAuth ? 'view-detail-btn task-list-numbering' : 'view-detail-btn-disabled'"
                                  @click.stop.prevent="
                                    openTaskTab(scope.row, scope.row[column.field])
                                  "
                                >
                                  {{ scope.row[column.field] }}
                                </a>
                                <!-- 质检异常标签 -->
                                <span
                                  class="task-state-block task-state-block-intelligent task-font12"
                                  v-if="scope.row && scope.row.attribute && scope.row.attribute.intelligentCheckStateStr == 1"
                                >
                                  {{$t('common.task.qualityTestingStatus.abnormal')}}
                                </span>
                                <!-- TODO: 曾超时 审批中标签 -->
                                <!-- 暂停中 -->
                                <span v-if="scope.row.inApprove == 1">
                                  <span
                                    class="task-state-block task-state-block-quality-approve task-font12"
                                    v-if="scope.row && scope.row.qualityEntity && scope.row.qualityEntity.inApprove == 1"
                                  >
                                    {{$t('common.task.qualityGuarantee.approve')}}
                                  </span>
                                  <span v-else class="task-state-block task-state-block-approve task-font12">
                                    {{$t('common.task.qualityGuarantee.inApprove')}}
                                  </span>
                                </span>

                                <!-- 异常标签 -->
                                <span
                                  class="task-state-block task-state-block-overtime task-font12"
                                  :class="{'task-state-block-approve': v === $t('common.task.exceptionStatus.allot') || v === $t('common.task.exceptionStatus.rollback'), 'task-state-block-ff': v === $t('common.task.exceptionStatus.oncePaused') || v === $t('common.task.exceptionStatus.onceOverTime') || v === $t('common.task.exceptionStatus.onceRefused')}"
                                  v-for="(v, i) in abnormalHover(scope.row)"
                                  :key="i"
                                  v-show="i < 2"
                                >
                                  {{v}}
                                </span>
                                <el-tooltip
                                  v-if="abnormalHover(scope.row).length > 2"
                                  :content="abnormalHover(scope.row).join(',')"
                                  placement="top"
                                >
                                  <span class="task-ml4">...</span>
                                </el-tooltip>
                              </div>
                            </el-tooltip>
                          </template>
                        </sample-tooltip>
                        <BizIntelligentTagsView
                          v-else
                          type="table"
                          :value="scope.row[column.field]"
                          :config="labelConfigTable"
                          :tagsList="abnormalHover(scope.row, true).concat(scope.row.labelList || [])"
                          @viewClick="openTaskTab(scope.row, scope.row[column.field])"
                        />
                      </template>

                        <!-- 连接器来源编号 -->
                        <template v-else-if="column.field === 'sourceBizNo'">
                          <div class="view-detail-btn task-client"
                              @click.stop="openSourceBizNoTab(scope.row)"
                          >
                            {{ scope.row["sourceBizNo"]}}
                          </div>
                        </template>
                        <!-- 描述 -->
                        <template v-else-if="column.field === 'description'">
                          <el-popover
                            popper-class="description-popper"
                            placement="top"
                            width="400"
                            trigger="hover"
                            v-if="scope.row.description && scope.row.description.length > 500"
                            >
                              <div class="description-des">{{ scope.row.description }}</div>
                              <div slot="reference"> {{ scope.row.description || '' }}</div>
                          </el-popover>
                          <el-tooltip
                            effect="dark"
                            placement="top"
                            :content="scope.row.description"
                            v-else
                          >
                            <div> {{ scope.row.description || '' }}</div>
                          </el-tooltip>
                        </template>

                        <!-- 客户  TODO: 客户查看权限 -->
                        <template v-else-if="column.field === 'customer'">
                          <!-- <div
                            :class="{
                              'view-detail-btn task-client': scope.row.linkAuth,
                            }"
                            @click.stop="openClientTab(scope.row)"
                          >
                            {{
                              scope.row["customerEntity"] &&
                                scope.row["customerEntity"].name
                            }}
                          </div> -->
                          <BizIntelligentTagsView
                            type="table"
                            class="table-blacklist"
                            :config="labelConfigTable"
                            :canClick="scope.row.linkAuth"
                            :value="scope.row['customerEntity'] && scope.row['customerEntity']['name'] || ''"
                            :tagsList="showLinkIntelligentTags ? scope.row['customerEntity'].labelList || [] : []"
                            @viewClick="openClientTab(scope.row)"
                          />
                        </template>
                        <!-- 国际货币 -->
                        <template v-else-if="column.formType === 'currency' && scope.row.attribute[column.field]">
                          {{ $formatFormField(column, scope.row) }}
                        </template>
                        <!-- TODO 连接器灰度控制  -->
                        <template v-else-if="column.formType === 'connector'">
                          <div v-if="scope.row.attribute && scope.row.attribute[column.field]" class="view-detail-btn task-client" @click.stop="openConnectorDialog(column, scope.row)">
                            {{ $t('common.base.view') }}
                          </div>
                        </template>
                      <!-- 创建方式 -->
                      <template v-else-if="column.field === 'source'">
                        <span>{{ getSoureceLabel(scope.row["source"])}}</span>
                      </template>

                        <!-- 关联事件 -->
                        <template v-else-if="column.field === 'eventNo'">
                          <div class="view-detail-btn task-client"
                              @click.stop="openEventTab(scope.row)"
                          >
                            {{ scope.row["eventNo"]}}
                          </div>
                        </template>
                        <!-- 联系人 -->
                        <template v-else-if="column.field === 'tlmName'">
                          <div class="cell el-tooltip">
                            <!-- {{ scope.row["linkMan"] && scope.row["linkMan"].name }} -->
                            <BizIntelligentTagsView
                              class="table-blacklist"
                              type="table"
                              :config="labelConfigTable"
                              :canClick="false"
                              :value="scope.row['linkMan'] ? scope.row['linkMan'].name || '' : ''"
                              :tagsList="showLinkIntelligentTags ? scope.row['linkMan'] ? (scope.row['linkMan'].labelList || []) : [] : []"
                          />
                          </div>
                        </template>
                        <!-- 质保start -->
                        <template v-else-if="column.field === 'qualityStartTime'">
                          <div>
                            {{ scope.row["qualityEntity"] && scope.row["qualityEntity"].qualityStartTime | fmt_date }}
                          </div>
                        </template>
                        <template v-else-if="column.field === 'qualityEndTime'">
                          <div>
                            {{ scope.row["qualityEntity"] && scope.row["qualityEntity"].qualityEndTime | fmt_date }}
                          </div>
                        </template>
                        <template v-else-if="column.field === 'qualityStatus'">
                          <div>
                            {{ scope.row["qualityEntity"] && scope.row["qualityEntity"].qualityStatus | qualityStatus }}
                          </div>
                        </template>
                        <!-- 质保end -->
                        <!-- 电话 -->
                        <template v-else-if="column.field === 'tlmPhone'">
                          <div>
                            {{ scope.row["linkMan"] && scope.row["linkMan"].phone }}
                            <template v-if="scope.row.linkMan && scope.row.linkMan.phone">
                              <biz-call-icon :value="scope.row['linkMan'].phone" :sourceData="callCenterSourceData(scope)" />
                            </template>

                          </div>
                        </template>
                        <template v-else-if="column.formType === 'phone'">
                          <div>
                            {{ scope.row.attribute[column.field] }}
                            <biz-call-icon :value="scope.row.attribute[column.field]" :sourceData="callCenterSourceData(scope)" />
                          </div>
                        </template>
                        <!-- 是否重复报修 -->
                        <template v-else-if="column.field === 'isRepeatRepair'">
                          <span :style="{color: column.setting.color || '#F4882F'}">{{scope.row.attribute[column.field] || $t('common.base.no')}}</span>
                        </template>
                        <!-- 自定义的选择类型字段显示， 与type 区别-->
                        <template
                          v-else-if="column.formType === 'select' && !column.isSystem"
                        >
                          {{ scope.row.attribute[column.field] | displaySelect }}
                        </template>

                      <!-- 更新时间 -->
                      <template v-else-if="column.field === 'updateTime'">
                        <template v-if="scope.row.latesetUpdateRecord && !isOpenData">
                          <el-tooltip
                            :content="scope.row.latesetUpdateRecord"
                            class="item"
                            effect="dark"

                            placement="top"
                          >
                            <div @mouseover="showLatestUpdateRecord(scope.row)">
                              {{ scope.row.updateTime | fmt_datetime }}
                            </div>
                          </el-tooltip>
                        </template>
                        <template v-else>
                          <div @mouseover="showLatestUpdateRecord(scope.row)">
                            {{ scope.row.updateTime | fmt_datetime }}
                          </div>
                        </template>
                      </template>

                      <template v-else-if="column.formType === 'logistics'">
                        <biz-list-logistics-no
                          :row="scope.row"
                          :column="column"
                          :is-link="isCanLogisticsNoLink(column)"
                          mode="task"
                          :biz-id="scope.row.id"
                        />
                      </template>
                      <!-- 产品 -->
                      <template v-else-if="column.field === 'product'">
                        <template v-if="Array.isArray(scope.row['products'])">
                          <div class="common-table-column__view-list table-blacklist">
                            <div class="common-table-column__view-list-item" v-for="(item, index) in scope.row['products']" :key="item.id">
                              {{ item['name'] }}
                              <BizIntelligentTagsView
                                v-if="showLinkIntelligentTags"
                                type="detail"
                                :tags-list="getLinkInitIntelligentTagsList(scope.row['products'])"
                                :config="{ normalShowType:'icon', normalMaxLength: 1 }"
                                :show-more-icon="false"/>
                              {{ `${ scope.row['products'].length - 1 !== index ? ',' : ''}` }}
                            </div>
                          </div>
                        </template>
                      </template>

                      <!-- 创建人 和 负责人 、派单人 -->
                      <template
                        v-else-if="
                          column.field === 'createUserName' ||
                            column.field === 'executorName' ||
                            column.field === 'allotName'
                        "
                      >
                        <template v-if="permissionTaskView">
                          <a
                            href=""
                            class="view-detail-btn view-user-detail-btn"
                            @click.stop.prevent="
                              openUserTab(
                                presonDisplayObj('userId', column.field, scope.row)
                              )
                            "
                          >
                            <template v-if="isOpenData">
                              <template v-if="column.field === 'createUserName' && scope.row.createUser">
                                <open-data type="userName" :openid="scope.row.createUser.staffId"></open-data>
                              </template>
                              <template v-else-if="column.field === 'executorName' && scope.row.executorUser">
                                <open-data type="userName" :openid="scope.row.executorUser.staffId"></open-data>
                              </template>
                              <template v-else-if="column.field === 'allotName' && scope.row.allotUser">
                                <open-data type="userName" :openid="scope.row.allotUser.staffId"></open-data>
                              </template>
                            </template>
                            <template v-else>
                              {{
                                presonDisplayObj("displayName", column.field, scope.row)
                              }}
                            </template>
                          </a>
                        </template>
                        <template v-else>
                          <template v-if="isOpenData">
                            <template v-if="column.field === 'createUserName' && scope.row.createUser">
                              <open-data type="userName" :openid="scope.row.createUser.staffId"></open-data>
                            </template>
                            <template v-else-if="column.field === 'executorName' && scope.row.executorUser">
                              <open-data type="userName" :openid="scope.row.executorUser.staffId"></open-data>
                            </template>
                            <template v-else-if="column.field === 'allotName' && scope.row.allotUser">
                              <open-data type="userName" :openid="scope.row.allotUser.staffId"></open-data>
                            </template>
                          </template>
                          <template v-else>
                            {{
                              presonDisplayObj("displayName", column.field, scope.row)
                            }}
                          </template>
                        </template>
                      </template>

                      <!-- 协同人 -->
                      <template v-else-if="column.field === 'synergies'">
                        <template v-if="isOpenData && scope.row[column.field]">
                          <open-data
                            v-for="synergie in scope.row[column.field]"
                            :key="synergie.staffId"
                            type="userName"
                            :openid="synergie.staffId"></open-data>
                        </template>
                        <template v-else>
                          {{
                            scope.row[column.field] &&
                              scope.row[column.field]
                                .map((synergie) => synergie.displayName)
                                .join(", ")
                          }}
                        </template>
                      </template>

                      <!-- 自定义人员 -->
                      <template v-else-if="column.formType === 'user' && scope.row.attribute[column.field]">
                        <template v-if="isOpenData">
                          <open-data
                            v-for="staffId in getUserIds(scope.row.attribute[column.field])"
                            :key="staffId"
                            type="userName"
                            :openid="staffId"></open-data>
                        </template>
                        <template v-else>
                          {{ getUserName(scope.row.attribute[column.field]) }}
                        </template>
                      </template>

                      <!-- 客户产品关联字段 -->
                      <template v-else-if="isOpenData && (column.formType === 'relationCustomer'|| column.formType === 'relationProduct') && column.setting && (column.setting.formType === 'user' || column.setting.fieldName === 'customerManager') && scope.row.attribute && scope.row.attribute[column.field]">
                        <template v-if="column.setting.formType === 'user'">
                          <open-data
                            v-for="staffId in getRelationUserIds(scope.row.attribute[column.field])"
                            :key="staffId"
                            type="userName"
                            :openid="staffId"></open-data>
                        </template>
                        <template v-else>
                          <open-data type="userName" :openid="scope.row.attribute[column.field]"></open-data>
                        </template>
                      </template>

                      <!-- 派单方式 -->
                      <template v-else-if="column.field === 'allotTypeStr'">
                        {{ allotTypeText(scope.row.allotType) }}
                      </template>

                      <!-- 服务部门(负责人所在的部门) -->
                      <template v-else-if="column.field === 'executorTags'">
                        {{ formatExecutorTags(scope.row[column.field]) }}
                      </template>

                    <!-- 审批状态 -->
                    <template v-else-if="column.field === 'inApprove'">
                      <div class="table-blacklist task-state-block" style="background: #ff9100;color: #fff;" v-if="scope.row.inApprove">
                        {{ scope.row.inApprove | displayApprove }}
                      </div>
                    </template>
                    <!-- 工单状态 -->
                    <template v-else-if="column.field === 'state'">
                      <!-- 暂停中 -->
                      <div
                        class="task-state-block task-font12 table-blacklist"
                        v-if="scope.row.isPaused == 1"
                        style="color: #FFF;background-color: rgba(255, 77, 79, 1);"
                      >
                        {{$t('task.list.paused')}}
                      </div>
                      <div
                        class="task-state-block task-font12 table-blacklist"
                        v-else-if="scope.row.attribute && scope.row.attribute.lastCompletedNodeType == 'normal' && scope.row.attribute.lastPassedNodeStateName"
                        style="
                          color: #FFF;
                          background-color: rgb(255, 145, 0);
                        "
                      >
                        {{scope.row.attribute.lastPassedNodeStateName}}
                      </div>
                      <div
                        class="task-state-block task-font12 table-blacklist"
                        v-else-if="scope.row.isPaused == 1"
                        style="
                          color: #FFF;
                          background-color: rgba(255, 77, 79, 1);
                        "
                      >
                        {{$t('task.list.paused')}}
                      </div>
                      <!-- 其他状态 -->
                      <div
                        v-else
                        class="task-state-block task-font12 table-blacklist"
                        :style="{
                          backgroundColor: taskStateEnum.getBgColor(
                            scope.row[column.field],
                            1
                          ),
                          color: '#FFF'
                        }"
                      >
                        {{
                          scope.row[column.field] &&
                            taskStateEnum.getName(scope.row[column.field])
                        }}
                      </div>
                    </template>

                      <!-- 曾.. -->
                      <template
                        v-else-if="taskStatusFields.indexOf(column.field) > -1"
                      >
                        {{ Number(scope.row[column.field]) === 1 ? $t('common.base.yes') : $t('common.base.no') }}
                      </template>

                      <!-- 富文本 -->
                      <template v-else-if="column.formType === 'richtext'">
                        <div class='view-detail-btn' @click.stop="openRichtextVisible(scope.row, column)">
                          <span v-if="scope.row.attribute[column.field]">{{$t('common.base.view')}}</span>
                        </div>
                      </template>
                      

                      <!-- 审批信息-->
                      <template v-else-if="column.field == 'approveInfoList'">
                        <div class='view-detail-btn' v-if="scope.row[column.field]" @click.stop="openApprovalInfoDialog(scope.row, column.field)">
                          <span>{{$t('common.base.view')}}</span>
                        </div>
                      </template>

                      <!-- 地址 -->
                      <template v-else-if="column.formType === 'address'">
                        {{ formatCustomizeAddress(scope.row, column) }}
                      </template>

                      <!-- start 故障库 -->
                      <template v-else-if="column.field === 'budgetAmountMax'">
                        {{
                          scope.row.esFaultLibrary && scope.row.esFaultLibrary.budgetAmountMax
                        }}
                      </template>
                      <template v-else-if="column.field === 'budgetAmountMin'">
                        {{
                          scope.row.esFaultLibrary && scope.row.esFaultLibrary.budgetAmountMin
                        }}
                      </template>
                      <template v-else-if="column.field === 'productType'">
                        {{
                          scope.row.esFaultLibrary && scope.row.esFaultLibrary.productType
                        }}
                      </template>
                      <template v-else-if="column.field === 'faultReason'">
                        {{
                          scope.row.esFaultLibrary && scope.row.esFaultLibrary.faultReason
                        }}
                      </template>
                      <template v-else-if="column.field === 'faultDetails'">
                        {{
                          scope.row.esFaultLibrary && scope.row.esFaultLibrary.faultDetails
                        }}
                      </template>
                      <template v-else-if="column.field === 'faultScene'">
                        {{
                          scope.row.esFaultLibrary && scope.row.esFaultLibrary.faultScene
                        }}
                      </template>
                      <!-- end 故障库 -->

                      <!-- start 服务商 -->
                      <template v-else-if="column.field === 'serviceProviders'">
                        {{
                          scope.row.serviceProviderSubForm &&
                            scope.row.serviceProviderSubForm
                              .map((serviceProvider) => serviceProvider.providerName)
                              .join(", ")
                        }}
                      </template>
                      <!-- end 服务商 -->

                      <template v-else-if="column.formType === 'relationCustomer' && (column.setting || {}).formType === 'cascader'">
                        {{ (scope.row[column.field] || scope.row.attribute[column.field]) | fmt_form_cascader((column.setting || {}).isMulti)}}
                      </template>
                      <template v-else-if="column.formType === 'relationProduct' && (column.setting || {}).formType === 'cascader'">
                        <template v-for="(item, index) in (scope.row[column.field] || scope.row.attribute[column.field] || [])">
                          {{index ? '、' : ''}}{{ item | fmt_form_cascader((column.setting || {}).isMulti)}}
                        </template>
                      </template>

                      <!-- 表单设计器特殊控件 -->
                      <template v-else-if="['cascader', 'select', 'user', 'related_task','relationProduct','location', 'jsCodeBlock'].includes(column.formType)">
                        {{ $formatFormField(column, scope.row) }}
                      </template>

                      <!-- 用户 -->
                      <template
                        v-else-if="
                          column.formType === 'user' &&
                            scope.row.attribute[column.field]
                        "
                      >
                        {{
                          scope.row.attribute[column.field].displayName ||
                            scope.row.attribute[column.field].name
                        }}
                      </template>

                      <!-- 时间 -->
                      <template v-else-if="column.formType === 'datetime'">
                        <template v-if="!column.isSystem">
                          {{
                            scope.row.attribute && scope.row.attribute[column.field]
                          }}
                        </template>
                        <template v-else>
                          {{ scope.row[column.field] | fmt_datetime(column) }}
                        </template>
                      </template>

                      <div
                        v-else-if="column.formType === 'textarea'"
                        v-html="buildTextarea(scope.row.attribute[column.field])"
                        @click="openOutsideLink"
                      ></div>

                      <!-- 接单用时 -->
                      <template v-else-if="column.field === 'acceptUsedTimeStr'">
                        {{ scope.row.acceptUsedTime && scope.row.acceptUsedTime }}
                      </template>
                      <!-- 工单用时 -->
                      <template v-else-if="column.field === 'taskUsedTimeStr'">
                        {{ scope.row.taskUsedTime && scope.row.taskUsedTime }}
                      </template>
                      <!-- 工作用时 -->
                      <template v-else-if="column.field === 'workUsedTimeStr'">
                        {{ scope.row.workUsedTime && scope.row.workUsedTime }}
                      </template>
                      <!-- 响应用时 -->
                      <template v-else-if="column.field === 'taskResponseTimeStr'">
                        {{
                          scope.row.taskResponseTime && scope.row.taskResponseTime
                        }}
                      </template>
                      <!-- 响应用时 -->
                      <template v-else-if="column.field === 'createToCompleteUsedTimeStr'">
                        {{ scope.row.createToCompleteUsedTime && scope.row.createToCompleteUsedTime }}
                      </template>
                      <!-- 支付方式 -->
                      <template
                        v-else-if="
                          column.field === 'paymentMethod' &&
                            initData.paymentConfig &&
                            initData.paymentConfig.version === 1
                        "
                      >
                        {{
                          scope.row.attribute && scope.row.attribute.paymentMethod
                        }}
                      </template>
                      <template v-else-if="!column.isSystem">
                        <template
                          v-if="
                            scope.row.attribute &&
                              Array.isArray(scope.row.attribute[column.field])
                          "
                        >
                          {{ scope.row.attribute[column.field].join(",") }}
                        </template>
                        <template v-else>
                          {{
                            scope.row.attribute && scope.row.attribute[column.field]
                          }}
                        </template>
                      </template>
                      <!-- 里程 -->
                      <template v-else-if="['taskEstimatedMileage','estimatedMileage','actualMileage'].includes(column.field)">
                        {{ scope.row[column.field] || 0 }} km
                      </template>

                      <template v-else>
                        {{ Array.isArray(scope.row[column.field]) ? scope.row[column.field].join(',') : scope.row[column.field] }}
                      </template>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
            <div ref="tableFooterContainer" class="table-footer comment-list-table-footer bbx-normal-table-footer-10 nor-static">
              <div class="list-info">
                <i18n path="common.base.table.totalCount">
                  <span place="count" class="level-padding">{{ taskPage.totalElements || 0 }}</span>
                </i18n>
                <template v-if="multipleSelection&&multipleSelection.length>0">
                  <i18n path="common.base.table.selectedNth">
                    <span place="count" class="color-primary pad-l-5 pad-r-5">{{ multipleSelection.length }}</span>
                  </i18n>
                  <span class="color-primary cur-point" @click="toggleSelection">{{$t('common.base.clear')}}</span>
                </template>
              </div>
              <el-pagination
                class="comment-list-table-footer-pagination"
                background
                @current-change="jump"
                @size-change="handleSizeChange"
                :page-size="taskPage.pageSize"
                :current-page="taskPage.pageNum"
                layout="prev, pager, next, sizes, jumper"
                :total="taskPage.totalElements"
              >
              </el-pagination>
            </div>
          </div>
          <!-- end content 列表表格 -->
        </div>
      </div>

      <!-- start 选择列设置 -->
      <biz-select-column ref="advanced" @save="saveColumnStatus" />
      <!-- <base-table-advanced-setting ref="advanced" @save="modifyColumnStatus" /> -->
      <!-- end 选择列设置 -->

      <!-- start 导出工单 -->
      <base-export-group
        ref="exportPanel"
        :storage-key="taskTypeExportStorageKey"
        :alert="exportAlert"
        :columns="exportColumnList.length ? exportColumnList : exportColumns"
        :build-params="buildExportParams"
        :group="true"
        :validate="checkExportCount"
        method="post"
        :action="taskListExport"
        :is-show-export-tip="isOpenData"
      />
      <!-- end 导出工单 -->

      <!-- S 批量编辑 -->
      <batch-editing-customer-dialog
        ref="batchEditingCustomerDialog"
        :config="{
          fields: taskFields,
          currentTaskType: currentTaskType,
        }"
        :selected-ids="selectedIds"
        :select-list="multipleSelection"
        @update="updateEdit"
      ></batch-editing-customer-dialog>
      <!-- E 批量编辑 -->
      <!-- S 导入工单 -->
      <base-import
        :title="`${$t('common.base.importTask')}-${checkImportTask.name}`"
        ref="importCustomerModal"
        :action="`${taskListImport}?typeId=${checkImportTask.id}`"
        :template-url="`${taskListImportTem}?way=1&typeId=${checkImportTask.id}`"
      >
      </base-import>
      <!-- E 导入工单 -->
      <!-- S 导入费用 -->
      <base-import
        :title="$t('common.base.importCost')"
        ref="importCostModal"
        :template-url="taskCostListImportTem"
        :action="taskCostListImport">
      </base-import>
      <!-- E 导入费用 -->
      <!-- S 工单转换 -->
      <task-transfer ref="TaskTransfer" :task-id-list="selectedIds" />
      <!-- E 工单转换 -->
      <!-- S 地图预览 -->
      <task-map
        ref="taskMap"
        :map-show="mapShow"
        :auth="auth"
        :build-search="buildSearchParams"
        @hide="mapShow = false"
        @updateTaskTypesMap="updateTaskTypesMap"
        :config="{ selectedIds: selectedIds, searchParams: newSearchParams }"
      />
      <!-- E 地图预览 -->
    </div>
    <!-- E 列表展示 -->

    <div class="task-bj" v-show="showBj"></div>

    <!-- 富文本弹窗 -->
    <base-modal
      :title="$t('task.list.richTextContent')"
      :show.sync="richtextVisible"
      width="600px"
      @close="richtextVisible = false">
      <div class="richtext-box">
        <BaseListForNoData v-if="!richtextContent" :notice-msg="$t('common.base.tip.noData')"></BaseListForNoData>
        <div class="richtext-content" v-else v-html="richtextContent"></div>
      </div>
    </base-modal>

    <!-- 连接器明细弹窗 -->
    <connector-table-dialog ref="connectorDialogRef" />

    <!-- 附加组件数据导出 -->
    <statisticalDialog ref="statisticalDialog" :use-export-url="false" :card="selectCard" />
    <!-- 备件服务清单统计 -->
    <spare-statistics-dialog :card="selectCard" :use-export-url="false"  ref="spareStatisteDialog" ></spare-statistics-dialog>
    <!-- ai摘要弹窗 -->
    <!-- <aiSummariesDialog
      ref="aiSummariesDialogRef"
      :aifield-list="getAllFields"
      :current-field-list="getCurrentFieldList"
      :current-type="getCurrentType"
      :systemRobotId="systemRobotId"
    /> -->

    <!-- 工单保存 -->
    <base-save
      ref="taskSaveRef"
      :has-save="!!(currentView && currentView.viewId && currentView.authEdit)"
      :current-view="currentView"
      :before-save="beforeSaveView"
      :task-state="taskState"
      :visual-angle="visualAngle"
      :task-type="currentTaskType.id"
      :exception-nodes="exceptionNodes"
      @save="handleViewportSave"
      module="task"
    />
    <approval-info-dialog ref="approvalInfoDialogRef"></approval-info-dialog>
  </div>
</template>

<script>
import TaskList from './TaskList';
export default TaskList;
</script>
<style lang="scss">
// .biz-form-remote-select-clear {
//   i {
//     background-color: #f0f0f0;
//     width: 16px;
//     height: 16px;
//     border-radius: 16px;
//     &::before {
//       content: "x";
//     }
//   }
// }

.task-list-view {
  .el-loading-mask {
    // loading 层级调低
    z-index: 500 !important;
  }
}
.description-popper {
  background: #2a2b2d;
  color: #fafafa;
  .description-des {
    font-size: 12px;
  }
}
 .description-popper[x-placement^=top] .popper__arrow::after {
    border-top-color: #2a2b2d;
  }
.description-popper[x-placement^=top] .popper__arrow {
    border-top-color:#2a2b2d;
  }
.description-popper[x-placement^=bottom] .popper__arrow::after {
  border-bottom-color: #2a2b2d;
}
.description-popper[x-placement^=bottom] .popper__arrow {
  border-bottom-color: #2a2b2d;
}
 .description-des {
    max-height: 300px;
    overflow-y: auto;
  }
</style>
<style lang="scss" scoped>
@import "./TaskList.scss";
</style>

<style lang="scss" scoped>
.task-box {
  height:100%;
  #novice-wizard{
    width: 240px;
    height: auto;
    position: fixed;
    filter: drop-shadow(0 0 4px rgba(0, 0, 0, 0.5)) !important;
    z-index: 996;
    top: 50px;
    left: 210px;
    border-radius: 4px;
    background: #fff;
    max-height: 400px;
    &::before{
      visibility: visible;
      content: "";
      margin-left: 14px;
      position: absolute;
      width: 10px;
      height: 10px;
      background: inherit;
      transform: rotate(45deg) translate(0%, -50%);
    }
    .tour-contet-box{
      position: relative;
      overflow: hidden;
      padding: 0 20px;
      border-radius: 4px;
      overflow: hidden;
      text-align: start;
      padding-bottom: 12px;
      .tour-left-tips {
        width: 80px;
        height: 32px;
        color: #fff;
        position: absolute;
        left: -40px;
        top: 0px;
        line-height: 35px;
        font-size: 12px;
        transform-origin: center top;
        transform: rotateZ(-45deg);
        text-align: center;
        background-color: $color-primary-light-6;
      }
      .tour-content-head{
        padding-bottom: 10px;
        padding-top: 16px;
        // display: flex;
        // align-items: center;
        .overHideCon-1 {
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
          overflow: hidden;
          white-space: normal;
          word-wrap: break-word;
          word-break: break-all;
        }
        .flex-x {
          display: flex;
          align-items: center;
        }
        .flex-1 {
          flex: 1;
        }
        .close-btn{
          font-size: 10px;
          margin-bottom: 2px;
          color: #999;
          cursor: pointer;
      }
      }

    }
    .tour-bottom{
      height: 52px;
      padding: 0 20px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      // .now-step{
      //   color: #666;
      // }
      .next-step-button{
        cursor: pointer;
        width: 60px;
        height: 28px;
        color: #fff;
        text-align: center;
        line-height: 28px;
        border-radius: 4px;
        background-color: $color-primary-light-6;
      }
    }
  }
  .v-step[data-v-7c9c03f0] {
    background: #fff !important;
    color: #333 !important;
    -webkit-filter: drop-shadow(
      0px 9px 28px 8px rgba(0, 0, 0, 0.05)
    ) !important;
    filter: drop-shadow(0px 9px 28px 8px rgba(0, 0, 0, 0.05)) !important;
    padding: 0 !important;
    min-width: 240px !important;
    max-width: 350px !important;
  }
  .v-step .v-step__arrow[data-v-7c9c03f0] {
    border-color: #fff !important;
    border-left-color: transparent !important;
    border-right-color: transparent !important;
  }
  .v-tour-content-box {
    position: relative;
    overflow: hidden;
    padding: 0 20px;
    border-radius: 4px;
    .v-tour-left-tips {
      width: 80px;
      height: 32px;
      background: $color-primary;
      color: #fff;
      position: absolute;
      left: -40px;
      top: 0px;
      line-height: 40px;
      font-size: 12px;
      transform-origin: center top;
      transform: rotateZ(-45deg);
      text-align: center;
    }
    .v-tour-content {
      .v-tour-content-head {
        padding-top: 16px;
        padding-bottom: 10px;
        justify-content: flex-end;
        .iconfont {
          font-size: 10px;
          margin-bottom: 2px;
          color: #999;
          cursor: pointer;
        }
      }
      .v-tour-content-con {
        text-align: start;
        padding-bottom: 12px;
      }
    }
  }

  .v-tour-bottom {
    height: 52px;
    padding: 0 20px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    .btns {
      width: 60px;
      height: 28px;
      background: $color-primary;
      color: #fff;
      text-align: center;
      line-height: 28px;
      border-radius: 4px;
    }
    .text {
      @include fontColor();
    }
    :nth-child(n) {
      cursor: pointer;
    }
    :not(:last-child) {
      margin-right: 12px;
    }
  }

  /* 向上的箭头 */

  .normal-arrow-top {
    font-size: 0;
    line-height: 0;
    border-width: 0.5rem;
    border-color: #fff;
    width: 0;
    border-top-width: 0;
    border-style: dashed;
    border-bottom-style: solid;
    border-left-color: transparent;
    border-right-color: transparent;
    position: absolute;
    top: -0.5rem;
  }

  .guide-model-box {
    position: fixed;
    width: 100%;
    height: 100vh;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 996;
  }
  .guide-point {
    z-index: 997;
    position: sticky;
  }
  .bg-w {
    background: #fff;
  }
}

::v-deep .el-pagination {
  display: flex;
  width: 100%;
  flex:1;
  justify-content:flex-end;
  .el-pagination__sizes {
  }
}
.min-h-440{
  min-height:440px;
}
.viewport-guide {
  background: white;
  padding: 8px 16px;
  border-radius: 4px;
}
.viewport-no-guide {
  padding-left: 12px;
}
.task-type-dropdown-group {
  display: flex;
  flex-flow: column;
  max-height: 70vh;
  overflow: auto;
}
</style>
<style lang="scss">
  @import "@src/assets/scss/common-list.scss";
</style>
<style lang="scss" scoped>
.task-ai-button {
  margin-left: 12px;
}
.int-tags-btn {
  ::v-deep .viewport-dropdown .viewport-dropdown__button{
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #f5f8fa;
    padding: 0 12px;
    border-radius: 4px;
    border: 1px solid #E4E7ED;
  }
  
  ::v-deep .viewport-dropdown .viewport-dropdown__button {
    .iconfont {
      margin-left: 12px;
    }
    &:hover {
      border: 1px solid $color-primary;
    }
  }
}
.common-list-table__flex-row {
  // min-height: 230px;
}
.nor-static {
  position: static;
}
</style>
