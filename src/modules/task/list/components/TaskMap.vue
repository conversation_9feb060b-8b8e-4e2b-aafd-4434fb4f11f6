<template>
  <div class="task-map" v-show="mapShow" v-loading="loading">
    <div class="task-map-body">
      <!-- 返回列表 -->
      <el-button class="back-btn" type="primary" @click="$emit('hide')">{{$t('task.list.components.backToList')}}</el-button>
      <!-- 地图 -->
      <bbx-power-map v-if="mapShow && currentMapType" :current-map-type="currentMapType" @infoWindowFnc="infoWindowFnc" :style="`height: ${mapHeight - 90}px`" :init-setting="{zoom:5}" ref="baseMapContainer"/>
      <!-- 列表 -->
      <div
        class="task-map-body-workSide"
        :style="
          `height: ${mapHeight - 90}px;transition: all 0.3s linear;transform: translate(${mapPostion}px,0);`
        "
      >
        <div class="fold_btn" @click="show">
          <i class="iconfont icon-left1" v-show="mapPostion === 320"></i>
          <i class="iconfont icon-right1" v-show="mapPostion === 8"></i>
        </div>
        <div class="task-map-body-workSide-header task-flex task-ai">
          <span class="task-span1">{{$t('task.list.components.taskNum')}} {{ totalNumber }}</span>
          <el-tooltip class="item" effect="dark" :content="$t('task.tip.mapModeTip1')" placement="bottom">
            <i class="iconfont icon-tip cur-point"></i>
          </el-tooltip>
        </div>
        <!-- 列表 -->
        <task-map-list
          ref="taskMapList"
          :is-multi="allowBatchReassignment"
          :map-height="mapHeight"
          :map-list="mapList"
          :has_call_center_module="has_call_center_module"
          @next="next"
          @openInfo="openInfo"
          @updateMapList="updateMapList"
          @showModifyPlanTimeDialog="showModifyPlanTimeDialog"
          :type="1"
          :is-last-page="isLastPage"
        />
        <!-- 已选中列表 and 未选中 -->
        <div
          class="task-map-check"
          :style="
            `transition: all 0.3s linear;transform: translate(${checkPostion}px,0);`
          "
        >
          <div class="task-map-check-header">
            <span>{{chooseFilterText}} {{mdataFilter.length}} {{$t('common.base.piece')}}</span>
            <i class="iconfont icon-close" @click="checkPostion = 320"></i>
          </div>
          <task-map-list
            v-if="selectType == 'select'"
            ref="taskMapList1"
            :is-multi="allowBatchReassignment"
            :map-height="mapHeight - 40"
            :map-list="mdataFilter"
            :has_call_center_module="has_call_center_module"
            @openInfo="openInfo"
            @updateMapList="updateMapList"
            @showModifyPlanTimeDialog="showModifyPlanTimeDialog"
          />
          <!-- 解决初始化三次调用getTaskTypesMap接口的问题，同一时间多次调用接口会导致后端初始化有问题，后端不好解决，由前端修改。 -->
          <task-map-list
            v-if="selectType == 'unselect'"
            ref="taskMapList2"
            :is-multi="allowBatchReassignment"
            :map-height="mapHeight - 40"
            :map-list="mdataFilter"
            :has_call_center_module="has_call_center_module"
            @openInfo="openInfo"
            @updateMapList="updateMapList"
            @showModifyPlanTimeDialog="showModifyPlanTimeDialog"
          />
        </div>
        <!-- foot -->
        <div class="task-map-select" v-show="selectShow">
          <div @click="chooseFilter('select')">{{$t('task.list.components.justShowSelected')}}</div>
          <div @click="chooseFilter('unselect')">{{$t('task.list.components.justShowUnSelected')}}</div>
        </div>
        <div class="task-map-foot task-flex task-ai" v-if="allowBatchReassignment">
          <div class="task-span1 task-map-foot-content" @click="reallotBatch">
            {{$t('task.list.components.batchTaskTransfer')}}({{ selectIds.length }}/{{
              checkPostion === 0 ? mdataFilter.length : mapList.length
            }})
          </div>
          <div class="task-map-foot-select" @click="selectShow = !selectShow">
            <i class="iconfont icon-caret-up"></i>
          </div>
        </div>
      </div>
    </div>
    <!-- S 工单转换 -->
    <task-transfer ref="TaskTransfer" :taskIdList="selectIds" />
    <!-- E 工单转换 -->
    <!-- S 修改计划时间弹窗 -->
    <calendar-plantime-dialog 
      ref='CalendarPlanTimeDialogComponent'
      :enableSendSms="false"
      :task="taskData"
      :modifiable="allowModifyPlanTimeJudgeHandler(taskData)"
      :isAcceptCalendarTime="false"
      :successCallback="modifyCalendarPlanTimeSuccessCallBack"
    />
    <plantime-dialog
      ref="PlanTimeDialogComponent"
      :enableSendSms="false"
      :field="planTimeField"
      :modifiable="allowModifyPlanTimeJudgeHandler(taskData)"
      top
      :task="taskData"
      :unique="false"
      :successCallback="modifyPlanTimeSuccessCallBack"
    />
    <!-- E 修改计划时间弹窗 -->
  </div>
</template>
<script>
/* Api */
// import vue from "vue";
import * as TaskApi from "@src/api/TaskApi.ts";
import TaskMapList from "./TaskMapList.vue";
import TaskTransfer from "./TaskTransfer";
import PlanTimeDialog from '@src/modules/task/view/components/PlanTimeDialog.vue'
import CalendarPlanTimeDialog from '@src/modules/task/view/components/CalendarPlanTimeDialog.vue'
import { isCalendar } from '@src/util/CalendarUtil';
/* util */
import {openTabForCustomerView} from '@src/util/business/openTab'
import AuthUtil from '@src/util/auth'
import AuthEnum from '@model/enum/AuthEnum'
import AuthMixin from '@src/mixins/authMixin'
import { fmt_datetime } from '@src/filter/fmt'
/* image */
import { getOssUrl } from '@src/util/assets'
const DefaultHead = getOssUrl('/avatar.png')
/* service */
import { allowModifyPlanTimeJudgeHandler } from '@service/TaskService'

import { openAccurateTab } from '@src/util/platform'
import { PageRoutesTypeEnum } from 'pub-bbx-global/pageType/dist/enum/PageRoutesEnum'
import { formatAddress } from 'pub-bbx-utils';


import { useNewVue } from '@hooks/useNewVue'
const { extendVue } = useNewVue();
import { MapTypeEnum } from '@model/enum/MapEnum'
import { useMapType } from '@src/util/map';

export default {
  name: "task-map",
  mixins: [AuthMixin],
  props: {
    auth: {
      type: Object,
      default: () => ({})
    },
    config: {
      type: Object, //接口测试
    },
    // 远程搜索
    buildSearch: {
      type: Function,
      default: null
    },
    mapShow: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      isCalendar,
      loading: false,
      taskTypesMap: {}, // 全部工单类型数据
      planTimeField: {}, // 计划时间字段
      mapHeight: window.innerHeight, //根据屏幕尺寸而变
      mapPostion: 8, // 列表滑动距离
      checkPostion: 320, // 仅显示已选中项活动距离
      mapList: [], //地图数据
      mdataFilter: [], //筛选的地图数据
      chooseFilterText: this.$t('task.list.components.selected'), // 当前筛选的文案
      currentMarkId: "", //当前打开marker的id
      selectShow: false,
      selectType: '',
      page: 1,
      map: "",
      has_call_center_module: localStorage.getItem("call_center_module") == 1,
      taskData: {}, // 当前点击工单数据
      isLastPage: false,
      totalNumber: 0,
      currentMapType:null
    };
  },
  watch: {
    mapShow(v) {
      if (v) {
        this.init()
      }
    },
  },
  computed: {
    // 是否允许批量转派
    allowBatchReassignment() {
      return AuthUtil.hasAuth(this.auth, AuthEnum.TASK_BATCH_REASSIGNMENT)
    },
    selectIds() {
      const ids = this.mapList
        .filter(function(item) {
          return item.selected; // 目前不可进行转交的工单不在允许选中
        })
        .map((item) => {
          return item.id;
        });
      return ids;
    },
  },
  async mounted() {
    const { currentMapType } = await useMapType();
    this.currentMapType = currentMapType()
    this.fetchTaskTypesMap()
    const that = this;
    window.onresize = () => {
      return (() => {
        that.mapHeight = window.innerHeight;
      })();
    };
  },
  methods: {
    allowModifyPlanTimeJudgeHandler,
    showEditPlanTimeBtn(item) {
      if (!isCalendar) return true
      if (isCalendar) return item.planStartTimeShow || item.planEndTimeShow
    },
    /**  获取工单类型（全部） */
    fetchTaskTypesMap() {
      TaskApi.getTaskTypesMap().then((data) => {
        let isSuccess = data.success
        if (!isSuccess) return
      
        let taskTypes = data?.result || []
        // key : 工单类型id(string) -> value: TaskType
        this.taskTypesMap = taskTypes
          .reduce((acc, currentTaskType) => {
            acc[currentTaskType.templateId] = currentTaskType
            return acc
          }, {})

          this.$emit('updateTaskTypesMap', this.taskTypesMap)

      }).catch(err => {
        console.error(err)
      })
    },
    init() {
      this.mapList = []
      this.page = 1
      this.search()
    },
    show() {
      const { mapPostion } = this;
      this.mapPostion = mapPostion === 8 ? 320 : 8;
    },
    // 创建地图坐标
    createMarker(mapList, refresh = true) {
      let markerList = [];

      mapList.forEach((item, index) => {
        if (item.address && item.address.longitude) {
          const allowReallot = this.$refs.taskMapList.allowReallot(item);
          const markCon = `<div class="just-cur-point task-map-body-workSide-body-item-index ${
              item.selected ? 'active' : ''
            } ${!allowReallot ? 'disabled' : ''}">${item.i}</div>`
          const windowCon = this.infowindowConFnc(item);
          markerList.push({
            lat:item.address.latitude,
            lng:item.address.longitude,
            size:{
              x:32,
              y:32
            },
            infoWindow:windowCon,
            markInfo:markCon,
            markClickFn:(event, item) => {
              const { markKey } = item;
              this.openInfo({markKey, editInfo:true})
            },
            markKey:item.id
          });
        }
      });
      if(!markerList.length && refresh) return this.$refs.baseMapContainer.clearListMark()
      this.$refs.baseMapContainer.setListMark(markerList, refresh)
    },
    infowindowConFnc(item){
      const allowReallot = this.$refs.taskMapList.allowReallot(item);
      const { id, address = {}, taskNo, linkMan = {}, customerEntity = {}, executorUser = {}, planTime, planStartTime, planEndTime, planStartTimeShow, planEndTimeShow } = item;
          const this_ = this;
      const infoWindow = {
            render(){
              return (
                  <div class="map-mark-box bg-w pad-12">
                    <div class="map-mark-head task-flex">
                      <div class={['customer-name', 'task-span1', this.globalIsHaveCustomerViewDetailAuth ? 'link-text' : '']} id="customerId" onClick={()=>this_.infoCustomerId(customerEntity.id)} >{customerEntity.name}</div>
                      { allowReallot ? <div class="transfer-wrap"><img class="transfer-img" id="transferImg" src={executorUser.head || DefaultHead} onClick={(e)=>this_.infoTransferImg(e, executorUser.userId)} /><button type="button" class="btn btn-primary" onClick={()=>this_.infoTransfer()} id="">{this.$t('common.task.exceptionStatus.allot')}</button></div>
                      : this.globalIsHaveTaskViewDetailAuth ? <button type="button" class="btn btn-primary map-modal-view-btn" onClick={()=>this_.turnToTaskView(id)}>{this.$t('common.base.view')}</button> : ''}
                    </div>
                    <div class="map-mark-body">
                        <p>{this_.$t('common.form.type.taskNo')}：<a class={['open-new-tab', 'task-span1', this_.globalIsHaveTaskViewDetailAuth ? 'link-text' : '']} id="ids" onClick={()=>this_.turnToTaskView(id)}>{taskNo}</a></p>
                        
                        <p>{this_.$t('common.base.contact')}：{linkMan.name}</p>

                        <p>{this_.$t('common.base.phone')}：{linkMan.phone} {this_.has_call_center_module && linkMan.phone && this_.has_call_center_module ? <i class="icon-dianhua2 link-text iconfont" id="phone" onClick={()=>this_.infoPhone(linkMan.phone)}></i> : ''}
                        </p>

                        <p>{this_.$t('common.base.address')}：{`${address.province}${address.city}${address.dist}${address.address}`}</p>

                        <div class="calendar-time">
                          <div>
                            {!isCalendar ? <p>{this_.$t('common.form.type.planTime')}：<span id="workSidePlantimeText">{ (planTime && fmt_datetime(planTime)) || '--'}</span></p> : isCalendar && planStartTimeShow ? <p>{this_.$t('common.form.type.planStartTime')}：<span id="workSidePlantimeText">{ (planStartTime && fmt_datetime(planStartTime)) || '--'}</span></p> : ''}
                            {isCalendar && planEndTimeShow ? <p>{this_.$t('common.form.type.planEndTime')}：<span id="workSidePlantimeText">{ (planEndTime && fmt_datetime(planEndTime)) || '--'}</span></p> : ''}
                          </div>
                          {this_.allowModifyPlanTimeJudgeHandler(item) && this_.showEditPlanTimeBtn(item) && <i class="iconfont icon-bianji1" onClick={()=>this_.infoEditPlanTime()} id="editPlanTime"></i>}
                        </div>
                      </div>
                  </div>
              )
            }
          }
          return extendVue(infoWindow);
    },
    infoWindowFnc({mapIndex, markKey}){
      this.openInfo({markKey, editInfo:false})
    },
    openInfo({markKey, editInfo}){
      const key_ = markKey
      const oldIndex = this.mapList.findIndex(i=>i.isClick);
      let oldItem;
      if(oldIndex > -1){
        // 先关闭
        oldItem = this.mapList[oldIndex];
        this.$set(this.mapList[oldIndex], 'isClick', false)
        if(editInfo){
          this.$refs.baseMapContainer.openInfoWindow({ markKey:oldItem.id })
        }
      }
      if(key_ && oldItem?.id !== key_){
        const index = this.mapList.findIndex(i=>i.id == key_)
        const item = this.mapList[index]
        //选中
        this.currentMarkId = item.id;
        this.taskData = item;
        this.$set(this.mapList[index], 'isClick', true)
        this.$refs.baseMapContainer.setMapCenter(null, {lat:item.address.latitude,
            lng:item.address.longitude})
        if(editInfo){
          this.$refs.baseMapContainer.openInfoWindow({ markKey:item.id })
        }
      }
      
    },
    // 更新数据
    updateMapList({index, key, value}) {
      this.$set(this.mapList[index], key, value)
      this.mdataFilter.forEach((item, ind) => {
        if (item.id == this.mapList[index].id) {
          this.$set(this.mdataFilter[ind], key, value)
        }
      })

      this.$refs.baseMapContainer.changeMarkContent({markKey:this.mapList[index].id, content: `<div class="task-map-body-workSide-body-item-index ${
        value ? 'active' : ''
            }">${this.mapList[index].i}</div>` })
    },
    // 展示修改计划时间弹窗
    showModifyPlanTimeDialog(data) {
      this.taskData = {}
      let taskType = this.taskTypesMap[data.taskData.templateId]
      for (const key in taskType.field) {
        const name = taskType.field[key].formType || taskType.field[key].name || taskType.field[key].fieldName
        if(name === 'planTime') {
          this.planTimeField = taskType.field[key]
        }
      }
      this.taskData = data.taskData || {}
      this.$nextTick(() => {
        isCalendar ? this.$refs.CalendarPlanTimeDialogComponent?.openDialog('modifyPlanTime') : this.$refs.PlanTimeDialogComponent?.openDialog()
      })
    },
    modifyCalendarPlanTimeSuccessCallBack(newCalendarTime) {
      this.handleModifyTimeSuccess(newCalendarTime, 'calendarTime')
    },
    modifyPlanTimeSuccessCallBack(planTime) {
      this.handleModifyTimeSuccess(planTime, 'planTime')
    },
    handleModifyTimeSuccess(time, type) {
      this.mapList.forEach((item, index) => {
        let hasTime = true
        if (type == 'calendarTime') {
          hasTime = time
        }
        if (item.id == this.taskData.id && hasTime) {
          if (type == 'calendarTime') {
            this.$set(this.mapList[index], 'planStartTime', time.start)
            this.$set(this.mapList[index], 'planEndTime', time.end)
          } else if (type == 'planTime') {
            this.$set(this.mapList[index], 'planTime', time)
          }
          // 重新加载信息窗口
          if (item.isClick) {
            let div = document.createElement("div");
            let divVue = document.createElement("div")
            div.append(divVue)
            const vueDom = this.infowindowConFnc(this.mapList[index])
            vueDom.$mount(divVue)
            this.$refs.baseMapContainer.changeInfoWindow({markKey:item.id, content:div})

          }
          this.$message.success(this.$t('common.base.tip.editSuccess'))
        }
      })
    },
    turnToTaskView(id){
      if (!this.globalIsHaveTaskViewDetailAuth) return
      let fromId = window.frameElement.getAttribute("id");
      openAccurateTab({
        type: PageRoutesTypeEnum.PageTaskView,
        key: id,
        params: 'noHistory=1',
        fromId,
      });
    },
    infoPhone(value){
      this.$refs.taskMapList.makePhoneCall(value);
    },
    infoTransfer(){
      this.reallotBatch();
    },
    infoEditPlanTime(){
      this.$refs.taskMapList.openPlanTimeDialog(null, this.taskData)
    },
    infoCustomerId(value){
      if (!this.globalIsHaveCustomerViewDetailAuth) return
      openTabForCustomerView(value)
    },
    infoTransferImg(el, value){
      const user = {
        userId: value
      }
      // 显示人员卡片
      this.$OpenUserCard({ el, user })
    },
    // 仅显示已选中项活动距离
    chooseFilter(type) {
      this.checkPostion = 0;
      this.selectShow = false;
      this.selectType = type
      if (type === "select") {
        this.chooseFilterText = this.$t('task.list.components.selected')
        this.mdataFilter = this.mapList.filter(function(item) {
          return item.selected; // 目前不可进行转交的工单不在允许选中
        });
      }
      if (type === "unselect") {
        this.chooseFilterText = this.$t('task.list.components.unselected')
        this.mdataFilter = this.mapList.filter(function(item) {
          return !item.selected;
        });
      }
    },
    // 加载更多
    next() {
      this.page++;
      this.search(false);
    },
    // 列表数据
    async search(refresh = true) {
      this.loading = true
      const { selectedIds } = this.config;
      const searchParams = this.buildSearch?.() || {};
      const params = {
        ...searchParams,
        ids: selectedIds,
        ...{ page: this.page },
      };
      const { success, result } = await TaskApi.search(params);
      this.loading = false
      if (success) {
        this.mapList = [...this.mapList, ...result.content];
        this.mapList.map((item, index) => {
          item["i"] = index + 1;
        });
        this.createMarker(result.content, refresh);
        this.isLastPage = Boolean(result?.last)
        this.totalNumber = result.totalElements || 0
      }
    },
    /**
     * @description 工单转派
     */
    reallotBatch() {
      const { selectIds } = this;
      if (!selectIds.length) {
        this.$platform.alert(this.$t('task.tip.taskListTip7'));
        return;
      }
      this.$refs.TaskTransfer.openSendMessageDialog();
    }
  },
  components: {
    [TaskMapList.name]: TaskMapList,
    [TaskTransfer.name]: TaskTransfer,
    [PlanTimeDialog.name]: PlanTimeDialog,
    [CalendarPlanTimeDialog.name]: CalendarPlanTimeDialog,
  },
};
</script>
<style lang="scss">
.base-modal-content {
  padding: 20px 20px 8px 20px;
}
// 地图图标
.task-map-body-workSide-body-item-index {
  background: url("src/assets/img/task/mapListIcon.png") no-repeat 0 0 / 96px 32px;
  width: 32px;
  height: 32px;
  margin-right: 10px;
  line-height: 28px;
  text-align: center;
  color: #fff;
  font-family: tahoma;
  font-size: 12px;
  font-weight: bold;
  &.active {
    background: url("src/assets/img/task/mapListIcon.png") no-repeat -64px 0 / 96px 32px;
  }
  &.disabled {
    background: url("src/assets/img/task/mapListIcon.png") no-repeat -32px 0 / 96px 32px;
  }
}
.back-btn {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 99;
}
.amap-info-close {
  right: 5px !important;
}
// 信息框
.map-mark-box {
  width: 300px;
  font-size: 13px;
  padding-top: 18px;
  padding-left: 8px;
  padding-bottom: 10px;
  a {
    text-decoration: none;
    cursor: pointer;
  }
  .map-mark-head {
    display: flex;
    flex-flow: row nowrap;
    padding-bottom: 5px;
    align-items: center;
    button,
    .map-modal-view-btn {
      font-size: 12px;
    }
    .customer-name {
      font-weight: bold;
      font-size: 16px;
      line-height: 20px;
      word-break: break-all;
      cursor: pointer;
    }
  }
  .map-mark-body {
    p {
      margin: 0;
      margin-top: 5px;
    }
    .icon-dianhua2 {
      font-size: 12px;
      margin-right: 0;
    }
  }
}
.calendar-time {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .iconfont {
    color: $color-primary;
    font-size: 13px;
    cursor: pointer;
  }
}
.transfer-wrap {
  display: flex;
  align-items: center;
  .transfer-img {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-right: 10px;
    cursor: pointer;
  }
}

.map-mark-box-disabled {
  a {
    color: #666;
  }
}
</style>
<style lang="scss" scoped>
.task-map {
  &-header {
    padding: 10px 18px 10px 10px;
    background-color: #fff;
    border-radius: 3px;
    line-height: 34px;
  }
  &-body {
    position: relative;
    overflow: hidden;
    &-workSide {
      width: 320px;
      position: absolute;
      top: 0px;
      right: 0;
      border-left: 1px solid #e5e5e5;
      background-color: #fff;
      z-index: 9;
      &-header {
        height: 40px;
        padding: 0 18px 0 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #eee;
        font-size: 16px;
        font-weight: 500;
        .workSide-header-right {
          display: flex;
          align-items: center;
        }
      }
      &-body {
        overflow: hidden;
        overflow-y: scroll;
        background-color: #fff;
        color: #333;
        font-size: 13px;
        &-item {
          padding: 10px;
          border-bottom: 1px solid #eee;
          &.active {
            background: #E6FFFD;
          }
          &-info {
            margin-top: -5px;
          }
          &-index {
            background: url("src/assets/img/task/mapListIcon.png") no-repeat 0 0 / 96px 32px;
            width: 24px;
            height: 30px;
            margin-right: 10px;
            line-height: 24px;
            text-align: center;
            color: #fff;
            font-family: tahoma;
            font-size: 12px;
            font-weight: bold;
          }
          p {
            margin-bottom: 5px;
            font-weight: normal;
          }
        }
        span {
          color: $color-primary;
        }
      }
      .fold_btn {
        cursor: pointer;
        position: absolute;
        top: 50%;
        left: -12px;
        margin-top: -12px;
        z-index: 10;
        background-color: #fff;
        width: 12px;
        height: 50px;
        line-height: 50px;
        text-align: center;
        border-radius: 5px 0 0 5px;
        > i {
          font-size: 12px;
        }
      }
    }
  }
  &-select {
    position: absolute;
    right: 11px;
    bottom: 33px;
    background-color: #fff;
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    z-index: 101;
    > div {
      padding: 5px 20px;
      line-height: 26px;
      text-align: center;
      color: #777;
      font-size: 14px;
      transition: background-color ease 0.3s, color ease 0.3s,
        border-color ease 0.3s;
      cursor: pointer;
      &:hover {
        background-color: $color-primary;
        color: #fff;
      }
    }
  }
  &-check {
    width: 100%;
    position: absolute;
    top: 40px;
    z-index: 100;
    &-header {
      height: 40px;
      padding: 0 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #eee;
      font-size: 16px;
      font-weight: 500;
      background: #fff;
      .iconfont {
        margin-right: 5px;
        cursor: pointer;
      }
    }
  }
  &-foot {
    height: 50px;
    font-weight: 600;
    font-size: 12px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    padding: 10px 18px 10px 10px;
    &-content {
      color: #fff;
      background-color: $color-primary;
      flex: initial;
      width: 254px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      border-radius: 5px;
      cursor: pointer;
      font-size: 14px;
    }
    &-select {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 30px;
      height: 30px;
      border: 1px solid #eee;
      background-color: #F3FAF8;
      cursor: pointer;
      position: relative;
      > i {
        font-size: 16px;
        color: #8A8F8D;
      }
    }
  }
}
</style>
