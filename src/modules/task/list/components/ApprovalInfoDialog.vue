<template>
  <base-modal
    :title="$t('common.task.taskApproveInfo')"
    :show.sync="visible"
    width="800px"
    class="approve-dialog"
  >
  <el-table
    border
    :class="['bbx-normal-list-box table-box',approvalData.length>0 && 'no-border-bottom']"
    headerRowClassName='common-list-table-header__v2'
    rowClassName='base-table-row-v3'
    :data="approvalData"
    max-height="460"
    stripe>
    <el-table-column type="index" width="50" :label="$t('common.base.SN')" /> 
    <el-table-column
      v-for="column in columns"
      :key="column.fieldName"
      :prop="column.fieldName"
      :label="column.displayName"
      :width="column.width"
      :sortable="column.sortable || false"
      show-overflow-tooltip>
      <template slot-scope="{ row }">
        <template v-if="column.fieldName === 'approveAction'">
          {{ formatApproveAction(row[column.fieldName])}}
        </template>
        <template v-else>
          {{ $formatFormField(column, row) }}
        </template>
      </template>
    </el-table-column>
    </el-table>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="visible = false">{{$t('common.base.close')}}</el-button>
    </div>
  </base-modal>
</template>
<script>
import { taskApprovalFields } from '@src/modules/task/list/TaskFieldModel.js';

export default {
  name: 'approval-info-dialog',
  props: {
  },
  data() {
    return {
      visible: false,
      approvalData: []
    }
  },
  computed: {
    columns() {
      const widthMap = {
        datetime: 165,
        submitRemark: 160,
        approveRemark: 160,
      };
      return taskApprovalFields
        .filter(item => item.fieldName !== 'approveUserList')
        .map(({ formType, fieldName, ...rest }) => {
          const width = widthMap[formType] ?? widthMap[fieldName] ?? 100;
          return {
            formType,
            fieldName,
            width,
            ...rest
          };
        });
    }
  },
  methods: {
    openToast(approvalData) {
      this.visible = true
      this.approvalData = approvalData
    },
    /** 格式化审批动作 */
    formatApproveAction(value) {
      const map = {
        agree: this.$t('common.base.agree'),
        refuse: this.$t('common.base.refuse'),
        submitApprove: this.$t('common.base.initiateApproval'),
        offApprove: this.$t('common.task.button.offApprove')
      };
      return map[value] || '';
    }
  }
}

</script>
<style lang="scss" scoped>
.approve-dialog {
  .table-box {
    border: 1px solid #ebeef5;
  }
  .no-border-bottom {
    border-bottom: 0;
  }
}

</style>

