import { 
  AllotTypeConvertOptions, 
  FlagConvertOptions,
  TaskOnceConvertOptions,
  TaskApproveConvertOptions
} from '../model/TaskConvertMap'
import TaskStateEnum from '@model/enum/TaskStateEnum';
import i18n from '@src/locales'
import { getRootWindow } from '@src/util/dom';
import { exportFields } from '@src/modules/intelligentTags/model/const';
const RootWindow = getRootWindow(window)

/** 是否有查看审批信息的灰度 */
const isHaveSyncTaskApproveGray = RootWindow.grayAuth?.SYNC_TASK_APPROVE_INFO || false

/**审批相关信息 */
const approveExportField = [{
  id: 5460,
  tableName: 'task',
  isSystem: 1,
  fieldName: 'approveUserList',
  exportAlias: 'approveUserList',
  displayName: i18n.t('common.base.approveUser'),
  formType: 'user',
  defaultValue: null,
  isNull: 1,
  isSearch: 1,
  placeHolder: null,
  setting: {},
  orderId: 1,
  isDelete: 0,
  guideProfessions: [],
  show: true,
  isGuideData: false,
  guideData: false,
},
{
  id: 5460,
  tableName: 'task',
  isSystem: 1,
  fieldName: 'approveInfoList',
  exportAlias: 'approveInfoList',
  displayName: i18n.t('common.task.taskApproveInfo'),
  formType: 'approveInfoList',
  defaultValue: null,
  isNull: 1,
  isSearch: 1,
  placeHolder: null,
  setting: {},
  orderId: 1,
  isDelete: 0,
  guideProfessions: [],
  show: true,
  isGuideData: false,
  guideData: false,
}]

// 导出列选项
const allExport = [
  {
    exportAlias: 'state',
    displayName: i18n.t('task.taskState'),
  },
  ...exportFields,
  {
    exportAlias: 'createUser',
    displayName: i18n.t('common.base.createUser'),
  },
  {
    exportAlias: 'allotUser',
    displayName: i18n.t('task.allotUser'),
  },
  {
    exportAlias: 'reAllotUser',
    displayName: i18n.t('task.reAllotUsers'),
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'executorName',
    exportAlias: 'executor',
    displayName: i18n.t('task.executor'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    exportAlias: 'balanceUser',
    displayName: i18n.t('task.list.displayName.reckoner'),
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'executorTag',
    exportAlias: 'executorTag',
    displayName: i18n.t('task.list.displayName.departmentOfExecutor'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'synergies',
    exportAlias: 'synergies',
    displayName: i18n.t('task.synergies'),
    formType: 'user',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'createTime',
    exportAlias: 'createTime',
    displayName: i18n.t('common.base.createTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'allotTime',
    exportAlias: 'allotTime',
    displayName: i18n.t('task.list.displayName.allotTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'acceptTime',
    exportAlias: 'acceptTime',
    displayName: i18n.t('task.list.displayName.acceptTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'startTime',
    exportAlias: 'startTime',
    displayName: i18n.t('task.list.displayName.startTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'reallotTime',
    exportAlias: 'reallotTime',
    displayName: i18n.t('task.list.displayName.transferTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'completeTime',
    exportAlias: 'completeTime',
    displayName: i18n.t('common.base.completeTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'allotType',
    exportAlias: 'allotType',
    displayName: i18n.t('task.list.displayName.allotType'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'acceptUsedTimeStr',
    displayName: i18n.t('task.detail.components.acceptTime'),
    exportAlias: 'acceptUsedTime',
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'taskUsedTimeStr',
    exportAlias: 'taskUsedTime',
    displayName: i18n.t('task.detail.components.taskTime'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'workUsedTimeStr',
    exportAlias: 'workUsedTime',
    displayName: i18n.t('task.detail.components.workTime'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'taskResponseTimeStr',
    exportAlias: 'taskResponseTime',
    displayName: i18n.t('task.detail.components.respondTime'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'createToCompleteUsedTimeStr',
    exportAlias: 'createToCompleteUsedTime',
    displayName: i18n.t('task.detail.components.finishTime'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'onceOverTime',
    exportAlias: 'onceOverTime',
    displayName: i18n.t('common.task.exceptionStatus.onceOverTime'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'onceRefused',
    exportAlias: 'onceRefused',
    displayName: i18n.t('common.task.exceptionStatus.onceRefused'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'oncePaused',
    exportAlias: 'oncePaused',
    displayName: i18n.t('common.task.exceptionStatus.oncePaused'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'onceRollback',
    exportAlias: 'onceRollback',
    displayName: i18n.t('common.task.exceptionStatus.onceRollback'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'onceReallot',
    exportAlias: 'onceReallot',
    displayName: i18n.t('common.task.exceptionStatus.onceAllot'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'oncePrinted',
    exportAlias: 'oncePrinted',
    displayName: i18n.t('task.list.displayName.oncePrinted'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'positionException',
    exportAlias: 'positionException',
    displayName: i18n.t('common.task.exceptionStatus.positionException'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'inApprove',
    exportAlias: 'inApprove',
    displayName: i18n.t('task.list.displayName.approveStatus'),
    formType: 'select',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  ...( isHaveSyncTaskApproveGray ? approveExportField : []),
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 0,
    fieldName: 'paymentMethod',
    exportAlias: 'paymentMethod',
    displayName: i18n.t('task.detail.components.payMethod'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'reviewTime',
    exportAlias: 'reviewTime',
    displayName: i18n.t('task.detail.components.reviewTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'balanceTime',
    exportAlias: 'balanceTime',
    displayName: i18n.t('task.list.displayName.balanceTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'closeTime',
    exportAlias: 'closeTime',
    displayName: i18n.t('task.list.displayName.closeTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'updateTime',
    exportAlias: 'updateTime',
    displayName: i18n.t('task.list.displayName.lastUpdated'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'source',
    exportAlias: 'source',
    displayName: i18n.t('common.base.createSource'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'eventNo',
    exportAlias: 'eventNo',
    displayName: i18n.t('task.list.displayName.relatedEvent'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  }
]

// 高级搜索系统审批相关字段
const approvalInfoField = [{
  id: 5460,
  tableName: 'task',
  isSystem: 1,
  fieldName: 'approveUserList',
  exportAlias: 'approveUserList',
  displayName: i18n.t('common.base.approveUser'),
  formType: 'user',
  defaultValue: null,
  isNull: 1,
  isSearch: 1,
  placeHolder: null,
  setting: {},
  orderId: 1,
  isDelete: 0,
  guideProfessions: [],
  show: true,
  isGuideData: false,
  guideData: false,
},{
  id: 5460,
  tableName: 'task',
  isSystem: 1,
  fieldName: 'approveUser',
  exportAlias: 'approveUser',
  displayName: i18n.t('performance.label.approveOperator'),
  formType: 'user',
  defaultValue: null,
  isNull: 1,
  isSearch: 1,
  placeHolder: null,
  setting: {},
  orderId: 1,
  isDelete: 0,
  guideProfessions: [],
  show: true,
  isGuideData: false,
  guideData: false,
},{
  id: 5460,
  tableName: 'task',
  isSystem: 1,
  fieldName: 'approveAction',
  exportAlias: 'approveAction',
  displayName: i18n.t('common.task.taskApproveAction'),
  formType: 'select',
  defaultValue: null,
  isNull: 1,
  isSearch: 1,
  placeHolder: null,
  setting: {
    isMulti: false, 
    dataSource: [
      {
        text: i18n.t('common.base.agree'),
        value: 'agree',
      },
      {
        text: i18n.t('common.base.refuse'),
        value: 'refuse',
      },
      {
        text: i18n.t('common.base.initiateApproval'),
        value: 'submitApprove',
      },
      {
        text: i18n.t('common.task.button.offApprove'),
        value: 'offApprove',
      },
    ]
  },
  orderId: 1,
  isDelete: 0,
  guideProfessions: [],
  show: true,
  isGuideData: false,
  guideData: false,
},{
  id: 5460,
  tableName: 'task',
  isSystem: 1,
  fieldName: 'approveTime',
  exportAlias: 'approveTime',
  displayName: i18n.t('common.base.approvalTime'),
  formType: 'datetime',
  defaultValue: null,
  isNull: 1,
  isSearch: 1,
  placeHolder: null,
  setting: {},
  orderId: 1,
  isDelete: 0,
  guideProfessions: [],
  show: true,
  isGuideData: false,
  guideData: false,
  sortable: true, 
},{
  id: 5460,
  tableName: 'task',
  isSystem: 1,
  fieldName: 'approveRemarkDesc',
  exportAlias: 'approveRemarkDesc',
  displayName: i18n.t('wiki.detail.approveDialog.label10'),
  formType: 'text',
  defaultValue: null,
  isNull: 1,
  isSearch: 1,
  placeHolder: null,
  setting: {},
  orderId: 1,
  isDelete: 0,
  guideProfessions: [],
  show: true,
  isGuideData: false,
  guideData: false,
}]

// 高级搜索系统服务商字段
const providerField = {
  id: 5460,
  tableName: 'task',
  isSystem: 1,
  fieldName: 'serviceProviderId',
  displayName: i18n.t('common.base.serviceProvider'),
  formType: 'serviceProviders',
  defaultValue: null,
  isNull: 1,
  isSearch: 1,
  placeHolder: null,
  setting: {},
  orderId: 1,
  isDelete: 0,
  guideProfessions: [],
  show: true,
  isGuideData: false,
  guideData: false,
} 

// 服务商信息
const providerFields = [
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'providerSettlement',
    exportAlias: 'providerSettlement',
    displayName: i18n.t('common.task.providerSettlement'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
]
// 选择列数据or表格
let fields = [
  {
    id: 476,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'taskNo',
    displayName: i18n.t('common.form.type.taskNo'),
    formType: 'text',
    defaultValue: null,
    isNull: 0,
    isSearch: 1,
    placeHolder: null,
    setting: {
      customerNameDuplicate: false,
    },
    show: true,
    orderId: 0,
    isDelete: 0,
    guideProfessions: [],
    isGuideData: false,
    guideData: false,
  },
  {
    id: 476,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'templateName',
    displayName: i18n.t('common.task.taskType'),
    formType: 'text',
    defaultValue: null,
    isNull: 0,
    isSearch: 1,
    placeHolder: null,
    setting: {
      customerNameDuplicate: false,
    },
    orderId: 0,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'customer',
    displayName: i18n.t('common.base.customer'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    show: true,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'tlmName',
    displayName: i18n.t('common.base.contact'),
    exportAlias: 'customerLinkman',
    formType: 'select',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    show: true,
    guideProfessions: [],
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'tlmPhone',
    exportAlias: 'customerPhone',
    displayName: i18n.t('common.base.phone'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'taddress',
    exportAlias: 'customerAddress',
    displayName: i18n.t('common.form.type.customerAddress'),
    formType: 'address',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'product',
    exportAlias: 'product',
    displayName: i18n.t('common.base.product'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'level',
    displayName: i18n.t('common.form.type.level'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'serviceType',
    displayName: i18n.t('common.form.type.serviceType'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'serviceContent',
    displayName: i18n.t('common.form.type.serviceContent'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'executorTags',
    displayName: i18n.t('common.base.serviceDepartment'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'planTime',
    displayName: i18n.t('common.form.type.planTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: "planStartTime",
    displayName: i18n.t('common.form.type.planStartTime'),
    formType: "datetime",
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: "task",
    isSystem: 1,
    fieldName: 'planEndTime',
    displayName: i18n.t('common.form.type.planEndTime'),
    formType: "datetime",
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: "customer",
    isSystem: 1,
    fieldName: "description",
    displayName: i18n.t('common.form.type.description'),
    formType: "text",
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'createUserName',
    exportAlias: 'createUser',
    displayName: i18n.t('common.base.createUser'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'executorName',
    exportAlias: 'executor',
    displayName: i18n.t('task.executor'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'allotName',
    exportAlias: 'allotUser',
    displayName: i18n.t('task.allotUser'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'reAllotUserList',
    exportAlias: 'reAllotUser',
    displayName: i18n.t('task.reAllotUsers'),
    formType: 'user',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'synergies',
    displayName: i18n.t('task.synergies'),
    formType: 'user',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'state',
    displayName: i18n.t('task.taskState'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'inApprove',
    displayName: i18n.t('task.list.displayName.approveStatus'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  ...(isHaveSyncTaskApproveGray ? approveExportField : []),
  {
    id: 5460,
    tableName: 'task',
    isSystem: 0,
    fieldName: 'paymentMethod',
    displayName: i18n.t('task.detail.components.payMethod'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'createTime',
    displayName: i18n.t('common.base.createTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'allotTime',
    displayName: i18n.t('task.list.displayName.allotTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'allotTypeStr',
    displayName: i18n.t('task.list.displayName.allotType'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'acceptTime',
    displayName: i18n.t('task.list.displayName.acceptTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'startTime',
    displayName: i18n.t('task.list.displayName.startTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },

  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'reallotTime',
    displayName: i18n.t('task.list.displayName.transferTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'completeTime',
    displayName: i18n.t('common.base.completeTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'acceptUsedTimeStr',
    displayName: i18n.t('task.detail.components.acceptTime'),
    exportAlias: 'acceptUsedTime',
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'taskUsedTimeStr',
    exportAlias: 'taskUsedTime',
    displayName: i18n.t('task.detail.components.taskTime'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'workUsedTimeStr',
    exportAlias: 'workUsedTime',
    displayName: i18n.t('task.detail.components.workTime'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'taskResponseTimeStr',
    exportAlias: 'taskResponseTime',
    displayName: i18n.t('task.detail.components.respondTime'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'createToCompleteUsedTime',
    displayName: i18n.t('task.detail.components.finishTime'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'onceOverTime',
    displayName: i18n.t('common.task.exceptionStatus.onceOverTime'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'onceRefused',
    displayName: i18n.t('common.task.exceptionStatus.onceRefused'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'oncePaused',
    displayName: i18n.t('common.task.exceptionStatus.oncePaused'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'onceRollback',
    displayName: i18n.t('common.task.exceptionStatus.onceRollback'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'onceReallot',
    displayName: i18n.t('common.task.exceptionStatus.onceAllot'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'oncePrinted',
    displayName: i18n.t('task.list.displayName.oncePrinted'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'positionException',
    displayName: i18n.t('common.task.exceptionStatus.positionException'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'updateTime',
    displayName: i18n.t('task.list.displayName.lastUpdated'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'reviewTime',
    displayName: i18n.t('task.detail.components.reviewTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'balanceTime',
    displayName: i18n.t('task.list.displayName.balanceTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'closeTime',
    displayName: i18n.t('task.list.displayName.closeTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'source',
    displayName: i18n.t('common.base.createSource'),
    formType: 'user',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'eventNo',
    displayName: i18n.t('task.list.displayName.relatedEvent'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'serviceProviderQualification',
    displayName: i18n.t('common.form.type.serviceProviderQualification'),
    formType: 'serviceProviderQualification',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'engineerQualification',
    displayName: i18n.t('common.form.type.engineerQuality'),
    formType: 'engineerQualification',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'serviceProviders',
    displayName: i18n.t('common.form.type.serviceProviders'),
    formType: 'serviceProviders',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  }
];

// 高级搜索数据
const advancedList = [
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'customer',
    displayName: i18n.t('common.base.customer'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    show: true,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'tlmName',
    displayName: i18n.t('common.base.contact'),
    exportAlias: 'customerLinkman',
    formType: 'select',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    show: true,
    guideProfessions: [],
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'area',
    exportAlias: 'customerAddress',
    displayName: i18n.t('common.form.type.customerAddress'),
    formType: 'address',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'cusAddress',
    displayName: i18n.t('task.list.displayName.detailAddress'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'product',
    displayName: i18n.t('common.base.product'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'serviceType',
    displayName: i18n.t('common.form.type.serviceType'),
    formType: 'select',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'serviceContent',
    displayName: i18n.t('common.form.type.serviceContent'),
    formType: 'select',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'paymentMethod',
    displayName: i18n.t('task.detail.components.payMethod'),
    formType: 'select',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {
      isMulti: false, dataSource: [
        {
          text: i18n.t('task.detail.components.onlinePayByAli'),
          value: '在线支付-支付宝'
        },
        {
          text: i18n.t('task.detail.components.aliPayCode'),
          value: '支付宝收款码'
        },
        {
          text: i18n.t('task.detail.components.weChatCode'),
          value: '微信收款码'
        },
        {
          text: i18n.t('task.detail.components.bankCardCollection'),
          value: '银行卡收款'
        },
        {
          text: i18n.t('task.detail.components.otherPayUnifiedCollection'),
          value: '其他支付-统一收款'
        },
        {
          text: i18n.t('task.detail.components.otherPayMonthCollection'),
          value: '其他支付-月结'
        },
        {
          text: i18n.t('task.detail.components.otherPayWeekCollection'),
          value: '其他支付-周结'
        },
      ]
    },
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'level',
    displayName: i18n.t('common.form.type.level'),
    formType: 'select',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'tags',
    displayName: i18n.t('task.list.displayName.chooseTag'),
    formType: 'select',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    show: true,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'createUser',
    exportAlias: 'createUser',
    displayName: i18n.t('common.base.createUser'),
    formType: 'user',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {
      isMulti: true
    },
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'allotUser',
    exportAlias: 'createUser',
    displayName: i18n.t('task.allotUser'),
    formType: 'user',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {
      isMulti: true
    },
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'executor',
    exportAlias: 'createUser',
    displayName: i18n.t('task.executor'),
    formType: 'user',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {
      isMulti: true
    },
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'synergyId',
    displayName: i18n.t('task.synergies'),
    formType: 'user',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {
      isMulti: true
    },
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'state',
    displayName: i18n.t('task.taskState'),
    formType: 'select',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {
      isMulti: false, dataSource: [
        {
          text: i18n.t('common.base.all'),
          value: ''
        },
        {
          text: i18n.t('common.task.type.created'),
          value: 'created'
        },{
          text: i18n.t('common.task.type.allocated'),
          value: 'allocated'
        },{
          text: i18n.t('common.task.type.accepted'),
          value: 'accepted'
        },{
          text: i18n.t('common.task.type.processing'),
          value: 'processing'
        },{
          text: i18n.t('common.task.type.finished'),
          value: 'finished'
        },{
          text: i18n.t('common.task.type.refused'),
          value: 'refused'
        },{
          text: i18n.t('common.task.type.costed'),
          value: 'costed'
        },{
          text: i18n.t('common.task.type.closed'),
          value: 'closed'
        },{
          text: i18n.t('common.task.type.offed'),
          value: 'offed'
        },{
          text: i18n.t('common.task.type.taskPool'),
          value: 'taskPool'
        },{
          text: i18n.t('common.task.type.unfinished'),
          value: 'unfinished'
        },
      ]
    },
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'createTime',
    displayName: i18n.t('common.base.createTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'planTime',
    displayName: i18n.t('common.form.type.planTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: "planStartTime",
    displayName: i18n.t('common.form.type.planStartTime'),
    formType: "datetime",
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: "customer",
    isSystem: 1,
    fieldName: "planEndTime",
    displayName: i18n.t('common.form.type.planEndTime'),
    formType: "datetime",
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: "customer",
    isSystem: 1,
    fieldName: "allotTime",
    displayName: i18n.t('task.list.displayName.allotTime'),
    formType: "datetime",
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'acceptTime',
    displayName: i18n.t('task.list.displayName.acceptTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'startTime',
    displayName: i18n.t('task.list.displayName.startTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'reallotTime',
    displayName: i18n.t('task.list.displayName.transferTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'completeTime',
    displayName: i18n.t('common.base.completeTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'updateTime',
    displayName: i18n.t('task.list.displayName.updateTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'reviewTime',
    displayName: i18n.t('task.detail.components.reviewTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'balanceTime',
    displayName: i18n.t('task.list.displayName.balanceTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'closeTime',
    displayName: i18n.t('task.list.displayName.closeTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'allotTypeStr',
    displayName: i18n.t('task.list.displayName.allotType'),
    formType: 'select',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {
      isMulti: false, dataSource: [
        {
          text: i18n.t('common.base.all'),
          value: 0
        },
        {
          text: i18n.t('task.detail.components.manualDispatch'),
          value: 1
        },
        {
          text: i18n.t('task.detail.components.poolDispatch'),
          value: 2
        },
        {
          text: i18n.t('task.detail.components.autoDispatch'),
          value: 3
        },
      ]
    },
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'onceException',
    exportAlias: 'createUser',
    displayName: i18n.t('task.list.displayName.exceptionTag'),
    formType: 'select',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {
      isMulti: false, dataSource: [
        {
          text: i18n.t('common.task.exceptionStatus.onceOverTime'),
          value: 'ONCEOVERTIME',
        },
        {
          text: i18n.t('common.task.exceptionStatus.onceRefused'),
          value: 'ONCEREFUSED',
        },
        {
          text: i18n.t('common.task.exceptionStatus.oncePaused'),
          value: 'ONCEPAUSED',
        },
        {
          text: i18n.t('common.task.exceptionStatus.onceRollback'),
          value: 'ONCEROLLBACK',
        },
        {
          text: i18n.t('common.task.exceptionStatus.positionException'),
          value: 'POSITIONEXCEPTION',
        },
      ]
    },
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'onceReallot',
    displayName: i18n.t('common.task.exceptionStatus.onceAllot'),
    formType: 'select',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {
      isMulti: false, dataSource: [
        {
          text: i18n.t('common.base.all'),
          value: '',
        },
        {
          text: i18n.t('common.base.yes'),
          value: '1',
        },
      ]
    },
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'oncePrinted',
    displayName: i18n.t('task.list.displayName.oncePrinted'),
    formType: 'select',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {
      isMulti: false, dataSource: [
        {
          text: i18n.t('common.base.all'),
          value: '',
        },
        {
          text: i18n.t('common.base.yes'),
          value: '1',
        },
        {
          text: i18n.t('common.base.no'),
          value: '0',
        },
      ]
    },
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'inApprove',
    displayName: i18n.t('task.list.displayName.approveStatus'),
    formType: 'select',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {
      isMulti: false, dataSource: [
        {
          text: i18n.t('common.base.all'),
          value: '',
        },
        {
          text: i18n.t('common.task.approveStatus.approve'),
          value: '1',
        },
        {
          text: i18n.t('common.task.approveStatus.noApprove'),
          value: '0',
        },
      ]
    },
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
];
const FaultLibraryInquire = [
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'faultReason',
    displayName: i18n.t('common.form.preview.faultLibrary.concatLabel4'),
    formType: 'text',
    exportAlias: 'faultReason',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'faultDetails',
    displayName: i18n.t('common.form.preview.faultLibrary.concatLabel3'),
    formType: 'text',
    exportAlias: 'faultDetails',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'faultScene',
    displayName: i18n.t('common.form.preview.faultLibrary.concatLabel2'),
    formType: 'text',
    exportAlias: 'faultScene',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'productType',
    displayName: i18n.t('common.form.preview.faultLibrary.concatLabel1'),
    formType: 'cascader',
    exportAlias: 'productType',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {
      isMulti: false,
      dataSource: [],
    },
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  }
]
// 搜索显示字段
const Inquire = [
  {
    id: 5460,
    tableName: 'serviceProviders',
    isSystem: 1,
    fieldName: 'serviceProviders',
    displayName: i18n.t('common.form.type.serviceProviders'),
    formType: 'select',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    show: true,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'paymentMethod',
    displayName: i18n.t('task.detail.components.payMethod'),
    formType: 'select',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {
      isMulti: false, dataSource: [
        {
          text: i18n.t('task.detail.components.onlinePayByAli'),
          value: '在线支付-支付宝'
        },
        {
          text: i18n.t('task.detail.components.aliPayCode'),
          value: '支付宝收款码'
        },
        {
          text: i18n.t('task.detail.components.weChatCode'),
          value: '微信收款码'
        },
        {
          text: i18n.t('task.detail.components.bankCardCollection'),
          value: '银行卡收款'
        },
        {
          text: i18n.t('task.detail.components.otherPayUnifiedCollection'),
          value: '其他支付-统一收款'
        },
        {
          text: i18n.t('task.detail.components.otherPayMonthCollection'),
          value: '其他支付-月结'
        },
        {
          text: i18n.t('task.detail.components.otherPayWeekCollection'),
          value: '其他支付-周结'
        },
      ]
    },
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'tags',
    displayName: i18n.t('common.base.serviceDepartment'),
    formType: 'select',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    show: true,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'createUser',
    exportAlias: 'createUser',
    displayName: i18n.t('common.base.createUser'),
    formType: 'user',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {
      isMulti: true
    },
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'allotUser',
    exportAlias: 'createUser',
    displayName: i18n.t('task.allotUser'),
    formType: 'user',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {
      isMulti: true
    },
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'executor',
    exportAlias: 'createUser',
    displayName: i18n.t('task.executor'),
    formType: 'user',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {
      isMulti: true
    },
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'synergyId',
    displayName: i18n.t('task.synergies'),
    formType: 'user',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {
      isMulti: true
    },
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'reAllotUsers',
    displayName: i18n.t('task.reAllotUsers'),
    formType: 'user',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {
      isMulti: true
    },
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'state',
    displayName: i18n.t('task.taskState'),
    formType: 'select',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {
      isMulti: false, 
      dataSource: [
        { text: i18n.t('common.task.type.created'), value: TaskStateEnum.CREATED.value, },
        { text: i18n.t('common.task.type.allocated'), value: TaskStateEnum.ALLOCATED.value, },
        { text: i18n.t('common.task.type.accepted'), value: TaskStateEnum.ACCEPTED.value, },
        { text: i18n.t('common.task.type.processing'), value: TaskStateEnum.PROCESSING.value, },
        { text: i18n.t('common.task.type.finished'), value: TaskStateEnum.FINISHED.value, },
        { text: i18n.t('common.task.type.refused'), value: TaskStateEnum.REFUSED.value, },
        { text: i18n.t('common.task.type.costed'), value: TaskStateEnum.COSTED.value, },
        { text: i18n.t('common.task.type.closed'), value: TaskStateEnum.CLOSED.value, },
        { text: i18n.t('common.task.type.offed'), value: TaskStateEnum.OFFED.value, },
        { text: i18n.t('common.task.type.taskPool'), value: TaskStateEnum.TASK_POOL.value, },
        // 如果开启工单草稿灰度，高级搜索工单状态增加草稿状态
        ...(RootWindow.grayAuth?.taskCreateHalf ? [{ text: i18n.t('common.task.type.draft'), value: TaskStateEnum.DRAFT.value, }] : []),
      ]
    },
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'createTime',
    displayName: i18n.t('common.base.createTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'allotTime',
    displayName: i18n.t('task.list.displayName.allotTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'acceptTime',
    displayName: i18n.t('task.list.displayName.acceptTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'startTime',
    displayName: i18n.t('task.list.displayName.startTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'reallotTime',
    displayName: i18n.t('task.list.displayName.transferTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'completeTime',
    displayName: i18n.t('common.base.completeTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'updateTime',
    displayName: i18n.t('task.list.displayName.updateTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'reviewTime',
    displayName: i18n.t('task.detail.components.reviewTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'balanceTime',
    displayName: i18n.t('task.list.displayName.balanceTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'closeTime',
    displayName: i18n.t('task.list.displayName.closeTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'allotTypeStr',
    displayName: i18n.t('task.list.displayName.allotType'),
    formType: 'select',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {isMulti: false, dataSource: AllotTypeConvertOptions.slice(1)},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'onceException',
    exportAlias: 'createUser',
    displayName: i18n.t('task.list.displayName.exceptionTag'),
    formType: 'select',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {
      isMulti: false, 
      dataSource: FlagConvertOptions,
    },
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'onceReallot',
    displayName: i18n.t('common.task.exceptionStatus.onceAllot'),
    formType: 'select',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {
      isMulti: false,
      dataSource: [TaskOnceConvertOptions[0]]
    },
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'oncePrinted',
    displayName: i18n.t('task.list.displayName.oncePrinted'),
    formType: 'select',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {
      isMulti: false, 
      dataSource: TaskOnceConvertOptions
    },
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'inApprove',
    displayName: i18n.t('task.list.displayName.approveStatus'),
    formType: 'select',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {
      isMulti: false, 
      dataSource: TaskApproveConvertOptions,
    },
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'exceptionType',
    displayName: i18n.t('task.list.displayName.overTimeTask'),
    formType: 'select',
    // formType: "user",
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {
      isMulti: false, 
      dataSource: [
        { text: i18n.t('common.task.exceptionStatus.pause'), value: '1' },
        { text: i18n.t('common.task.exceptionStatus.overTime'), value: '2' },
      ]
    },
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'source',
    displayName: i18n.t('common.base.createSource'),
    formType: 'select',
    orderId: 1,
    isDelete: 0,
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
    setting: {isMulti: false, dataSource:[
      {
        text: i18n.t('task.list.displayName.importCreate'),
        value: '导入创建',
      },
      {
        text: i18n.t('task.list.displayName.manualCreate'),
        value: '手动创建',
      },
      {
        text: i18n.t('task.list.apiCreate'),
        value: '开放API',
      },
      {
        text: i18n.t('task.list.displayName.eventCreate'),
        value: '由事件创建',
      },
      {
        text: i18n.t('task.list.planCreate'),
        value: '计划任务创建',
      },
    ]},
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'eventNo',
    displayName: i18n.t('task.list.displayName.relatedEvent'),
    formType: 'text',
    // formType: "user",
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  // ...FaultLibraryInquire,
];

// 异常数据
const AbnormalList = [
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'refusedReason',
    exportAlias: 'refusedReason',
    englishName: 'refused',
    displayName: i18n.t('task.refuseReason'),
    formType: 'select',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'pausedReason',
    exportAlias: 'pausedReason',
    englishName: 'paused',
    displayName: i18n.t('task.pauseReason'),
    formType: 'select',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'rollbackReason',
    exportAlias: 'rollbackReason',
    englishName: 'rollback',
    displayName: i18n.t('task.backReason'),
    formType: 'select',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'reallotReason',
    exportAlias: 'reallotReason',
    englishName: 'reallot',
    displayName: i18n.t('task.list.displayName.transferReason'),
    formType: 'select',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'offedReason',
    exportAlias: 'offedReason',
    englishName: 'offed',
    displayName: i18n.t('task.cancelReason'),
    formType: 'select',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
]

// 归档时间
const archive = [
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'syncTime',
    exportAlias: 'syncTime',
    englishName: 'syncTime',
    displayName: i18n.t('task.list.displayName.archiveTime'),
    formType: 'datetime',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'syncUserName',
    exportAlias: 'syncUserName',
    englishName: 'syncUserName',
    displayName: i18n.t('task.list.displayName.archiveUser'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  }
]

// 顶部筛选id
const selectIds = {
  createdId: '133d2ef7-19a8-11e7-8d4e-00163e304a25', // 待指派
  allocatedId: '12fcb144-1ea3-11e7-8d4e-00163e304a25', // 已指派
  acceptedId: '475e3328-1e63-11e7-8d4e-00163e304a25', // 已接受
  processingId: '06db63fe-5017-11e7-a318-00163e304a25', // 进行中
  exceptionId: 'a78cbfec-d7d2-420f-9a56-32a503702f2d', // 异常工单
  allId: '998620df-0d55-11e7-8d4e-00163e304a25', // 全部工单
  unfinishedId: 'wangyue6-**************-************', // 未完成工单
  finished: '5ac722d9-1e63-11e7-8d4e-00163e304a25', // 已完成工单 
};

// 质保信息
const qualityFields = [
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: "qualityStartTime",
    field:"qualityStartTime",
    exportAlias:'qualityStartTime',
    displayName: i18n.t('common.form.preview.qualityField.label1'),
    formType: "datetime",
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: "qualityEndTime",
    field:"qualityEndTime",
    exportAlias:'qualityEndTime',
    displayName: i18n.t('common.form.preview.qualityField.label2'),
    formType: "datetime",
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: "qualityStatus",
    field:"qualityStatus",
    exportAlias:'qualityStatus',
    displayName: i18n.t('common.form.preview.qualityField.label3'),
    formType: "select",
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {
      isMulti: false, dataSource: [
        {
          text: i18n.t('common.base.warranty'),
          value: '保内'
        },
        {
          text: i18n.t('common.base.surrender'),
          value: '保外'
        },
      ]
    },
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
]



// 故障库
const FaultLibrary = [
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'budgetAmountMin',
    displayName: i18n.t('task.list.displayName.budgetAmountMin'),
    formType: 'text',
    exportAlias: 'budgetAmountMin',
    defaultValue: null,
    isNull: 1,
    isSearch: 0,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 5460,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'budgetAmountMax',
    displayName: i18n.t('task.list.displayName.budgetAmountMax'),
    formType: 'text',
    exportAlias: 'budgetAmountMax',
    defaultValue: null,
    isNull: 1,
    isSearch: 0,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  ...FaultLibraryInquire,
]

// 客户字段
const customerExtendFields = {
  tlmName: {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'tlmName',
    displayName: i18n.t('common.base.contact'),
    exportAlias: 'customerLinkman',
    formType: 'select',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    show: true,
    guideProfessions: [],
    isGuideData: false,
    guideData: false,
  },
  area: {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'area',
    exportAlias: 'customerAddress',
    displayName: i18n.t('task.list.displayName.area'),
    formType: 'address',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  address: {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'cusAddress',
    displayName: i18n.t('task.list.displayName.detailAddress'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  product: {
    id: 5460,
    tableName: 'customer',
    isSystem: 1,
    fieldName: 'product',
    displayName: i18n.t('common.base.product'),
    formType: 'product',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  }
}

// 连接器字段
const connectorFields =[
  {
    id: 10000,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'sourceTemplateName',
    displayName: i18n.t('common.base.source'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  },
  {
    id: 10001,
    tableName: 'task',
    isSystem: 1,
    fieldName: 'sourceBizNo',
    displayName: i18n.t('common.connector.sourceBizNo'),
    formType: 'text',
    defaultValue: null,
    isNull: 1,
    isSearch: 1,
    placeHolder: null,
    setting: {},
    orderId: 1,
    isDelete: 0,
    guideProfessions: [],
    show: true,
    isGuideData: false,
    guideData: false,
  }
]
const taskApprovalFields = [
  { 
    displayName:  i18n.t('common.task.taskNodeTitle'), 
    field: 'approveNode', 
    fieldName: 'approveNode', 
    formType: 'text',
    show: true, 
    isSystem: 1,
  },
  { 
    displayName:  i18n.t('common.base.promoter'), 
    field: 'submitUser', 
    fieldName: 'submitUser', 
    formType: 'user',
    show: true, 
    isSystem: 1 
  },
  { 
    displayName:  i18n.t('wiki.detail.approveDialog.label7'), 
    field: 'submitTime', 
    fieldName: 'submitTime', 
    formType: 'datetime',
    show: true, 
    isSystem: 1,
    sortable: true, 
  },
  { 
    displayName:  i18n.t('task.promoteRemark'), 
    field: 'submitRemarkDesc', 
    fieldName: 'submitRemarkDesc', 
    formType: 'text',
    show: true, 
    isSystem: 1 
  },
  ...approvalInfoField
]

export {
  connectorFields,
  fields,
  selectIds,
  advancedList,
  allExport,
  providerField,
  providerFields,
  approvalInfoField,
  Inquire,
  AbnormalList,
  archive,
  qualityFields,
  customerExtendFields,
  FaultLibrary,
  taskApprovalFields
};
// const TaskFieldModelData= {
//     fields,
//     selectIds,
//     advancedList,
//     allExport,
//     Inquire,
//     AbnormalList
// }
// export default TaskFieldModelData
