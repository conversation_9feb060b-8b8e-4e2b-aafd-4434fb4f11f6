.task-state-block {
  display: inline-block;
  width: auto;
  height: 22px;
  border-radius: 11px;
  margin-left: 8px;
  text-align: center;
  line-height: 22px;
  padding: 0 8px;
  text-wrap: nowrap;

  &-overtime,
  &-approve,
  &-quality-approve {
    display: inline-block;
    color: #fff;
  }

  &-overtime {
    background-color: #F56C6C;
  }
  
  &-quality-approve {
    background-color: #FF7043;
  }

  &-approve {
    background-color: #FAAE14;
  }
  &-ff {
    background-color: #FFA39E;
  }
  &-intelligent{
    color: #fff;
    background-color: #FF7043;;
  }
}

.task-list-operation-bar-container {
  justify-content: flex-end !important;
  .task-allot-button { 
    margin-left: 12px;
  }
}

.common-list-selection {
  padding: 0 7px;
  background: rgba(254, 139, 37, 0.08);
  line-height: 28px;
  font-size: 12px;
}

// S 顶部筛选
.common-list {
  &-section {
    background-color: #fff;
    border-radius: 4px;
    // padding: 16px;
    padding: 16px 16px 0 16px;
  }

  &-filter {
    padding-bottom: 10px;

    &-flow {
      position: relative;
      display: flex;
      flex-wrap: wrap;
      align-content: center;
      height: 35px;
      background-color: #eee;
      font-size: 12px;
      text-align: center;
      line-height: 35px;
      cursor: pointer;
      border-radius: 3px;

      &-item,
      &-dropdown {
        height: 100%;
        transition: background-color ease 0.3s, color ease 0.3s;

        &:hover {
          background-color: #55b7b4;
          color: #fff;
        }

        &:first-child {
          border-radius: 3px 0 0 3px;
        }
      }

      .extion {
        &:hover {
          background-color: #ef6b6b;
          color: #fff;
        }
      }

      &-dropdown {
        padding: 0 8px;
        border-radius: 0 3px 3px 0;
      }

      &-active {
        background-color: #55b7b4;
        color: #fff !important;
      }
    }

    &-span1 {
      flex: 1;
    }

    &-other {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 120px;
      height: 35px;
      margin-left: 10px;
      border-radius: 3px;
      background-color: #eee;
      font-size: 12px;
      cursor: pointer;

      .icon-more {
        font-size: 12px;
      }

      &-item {
        transition: background-color ease 0.3s, color ease 0.3s,
          border-color ease 0.3s;
        height: 100%;
        width: 100%;
        text-align: center;
        line-height: 35px;
        border-radius: 3px;

        &:hover {
          background-color: #55b7b4;
          color: #fff;
        }
      }
    }
  }
}

// E 顶部筛选

// S 新建 and 删除
.top-btn-group {
  position: relative;
}

// E 新建 and 删除

// S 头部
.select-list {
  background-color: #f0f0f0;
  border-radius: 4px;

  &-item {
    padding: 0 20px;
    line-height: 31px;

    &:first-child {
      border-radius: 4px 0 0 4px;
    }

    &:last-child {
      border-radius: 0 4px 4px 0;
    }

    cursor: pointer;

    &:hover {
      transition: background-color ease 0.3s, color ease 0.3s;
      color: #fff;
      background-color: #55b7b4;
    }
  }

  &-active {
    transition: background-color ease 0.3s, color ease 0.3s;
    color: #fff;
    background-color: #55b7b4;
  }
}

// E 头部
//导入工单
.import-task {
  position: relative;
  left: -15px;
  padding-left: 15px;

  &:hover &-item {
    display: block;
  }

  &-item {
    position: absolute;
    background: #fff;
    color: #333;
    left: -115px;
    top: -8px;
    border: 1px solid #eee;
    border-radius: 4px;
    display: none;
    max-height: 50vh;
    overflow-y: auto;

    >div {
      padding: 4px 15px;
      width: 120px;

      &:hover {
        background-color: $select-draggable-color;
        color: $color-primary;
      }
    }
  }
}

.superscript {
  position: relative;
  padding-left: 22px !important;
}

.superscript::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 34px;
  height: 34px;
  background: linear-gradient(135deg, #ff9900 50%, transparent 50%);
}

.superscript::after {
  content: "示例";
  position: absolute;
  left: 0;
  top: 0;
  height: 30px;
  width: 30px;
  line-height: 20px;
  font-size: 12px;
  color: #fff;
  text-align: center;
  transform: scale(0.9166666) rotateZ(-45deg);
}

.task-client {
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}

.task-list {
  &-header {
    background: #ffffff;
    box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.06);
    border-radius: 4px;
    margin-bottom: 12px;
    border-top: none;
    // overflow: hidden;
    padding: 16px;

    &-seach {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 0 16px;
      
      form {
        width: 100%;
      }

      .seach {
        justify-content: flex-end;
        
      }
    }

    &-nav {
      .task-filter-item {
        display: flex;
      }
      > div {
        position: relative;
        cursor: pointer;
        // border-top: 1px solid #F5F5F5;
        .state {
          padding-top: 4px;
          padding-left: 11px;
          width: 110px;
          font-weight: 500;
          // background-color: #FAFAFA;
        }
        .list {
          // width: 90%;
          // padding-right: 50px;
          flex: 1;
          overflow: hidden;
          // height: 30px;
          .list-item {
            > div {
              margin: 0 12px 8px 0;
              // padding-left: 11px;
              font-size: 13px;
              max-width: 160px;
              color: #808080;
              display: flex;
              align-items: center;
              justify-content: center;
              span {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                padding: 4px 8px;
                border: 1px solid rgba(0,0,0,0);
              }
              &:hover {
                color:$color-primary;

              }
            }
          }
          
        }
      }
    }
  }
}
.task-padding-0{
  padding: 0 !important;
}

.task-list-del {
  cursor: pointer;
  text-align: center;
}
.task-list-batch-button {
  cursor: pointer;
  width: 120px;
  text-align: center;
}
.task-width103 {
  padding: 0 25px;
  text-align: center;
}

.table-footer {
  // margin-top: 20px;
}

.task-ic19 {
  width: 18px;
  height: 15px;
  margin-right: 3px;
}

.task-list-dropdown-item {
  width: 100px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
}

.task-list-dropdown-icon {
  display: none;
}

.task-list-numbering {
  display: inline-block;
  min-width: 110px;
}

.task-buttom {
  >button {
    margin: 0 !important;
    margin-right: 10px !important;
  }
}
.task-bj{
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99;
  background-color: rgba(0, 0, 0, 0.6) ;
}

.icon-fdn-phone{
  cursor: pointer;
  color: $color-primary;
}

.task-flex {
  display: flex;
  align-items: center;
  .task-with-select{
    min-width: 120px !important;
    max-width:150px !important;
  }
}

.pack-up{
  width: 100%;
  margin-top:-12px;
  margin-bottom: 6px;
  div{
    width: 48px;
    height: 10px;
    background: linear-gradient(180deg, #FFFFFF 0%, #F4F4F4 100%);
    box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.12);
    border-radius: 0px 0px 3px 3px;
    margin: 0 auto;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    i{
      font-size: 10px;
      color: #4E4E4E;
    }
 }
 div:hover{
  background: $color-primary-light-1;
  border-radius: 0px 0px 3px 3px;
  border: 1px solid $color-primary-light-2;
  i{
    color: $color-primary;
  }
 }
}


.task-state-row-collapse {
  // 工单状态收起 样式
  height: 30px;
  overflow: hidden;
}

.task-state-collapse-btn {
  // 折叠按钮
  padding: 4px 8px;
  font-size: 12px;
  color: $text-color-secondary;
  i {
    font-size: 12px;
  }
}
// 富文本弹窗相关
.richtext-box{
  padding: 10px;
  min-height: 500px;
}
.task-list-view {
  .common-list-table-view {
    height: 100%;
  }
}
.task-select-column {
  color: #595959;
  &:hover {
    color:$color-primary;
  }
}
.editable-box {

  position: relative;
  white-space: pre; /* 保持文本连续性 */
}

// /* 光标样式 */
// .editable-box:focus::after {
//   content: '';
//   position: absolute;
//   right: -1px; /* 初始位置在右侧 */
//   top: 8px;
//   height: 1.2em;
//   width: 2px;
//   background: #333;
//   animation: blink 1s step-end infinite;
// }

/* 当有内容时调整光标位置 */
.editable-box[data-content]:not([data-content=""])::after {
  right: auto;
  left: 0;
}

@keyframes blink {
  50% { opacity: 0; }
}
.biz-chat-btn-img-block{
  width: 20px;
  height: 20px;
}
.aisearchColor{
  color: #8C8C8C;
}
.clickable {
  cursor: pointer;
}
.end-color{
  color: #8C8C8C;
}
.icon-magic-full{
  width:24px;
  height: 24px;
}
.ai-button-seach{
  height: 32px;
  cursor: pointer;
  box-sizing: border-box;
  background: linear-gradient(121deg, rgba(255, 118, 44, 0.1) -5%, rgba(206, 43, 243, 0.063) 30%, rgba(32, 116, 241, 0.1) 104%);
  box-sizing: border-box;
  border-radius: 4px;
  position: relative;
  padding: 0 12px;
  border: 1px solid #CBD6E2;
  display: flex;
  justify-content: center;
  align-items: center;
  span{
    background: linear-gradient(128deg, #FF762C -9%, #CE2BF3 29%, #2074F1 102%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
    margin-left: 4px;
  }
  .icon-magic-full{
    width: 16px;
    height: 16px;
    margin-top: -4px;
  }
}

.ai-button-search {
  position: relative;
  .ai-free-button {
    position: absolute;
    right: -15px;
    top: -12px;
    transform: scale(.8);
  }
}