// @ts-nocheck
/* vue */
import VC from '@model/VC'
import { CreateElement, VNode } from 'vue'
import { Component, Emit, Prop, Watch, Ref } from 'vue-property-decorator'
/* scss */
import '@src/modules/task/components/TaskAllotModal/TaskAllotServiceProvider/TaskAllotServiceProvider.scss'
/* components */
import TaskAllotServiceProviderTable from '@src/modules/task/components/TaskAllotModal/TaskAllotServiceProvider/Table/Table'
import UiInput from '@src/component/ui/UiInput/UiInput'
import TaskCascader from '@src/modules/task/components/TaskAllotModal/TaskAllotServiceProvider/TaskCascader.vue'
import DynamicContentTree from './DynamicContentTree'
import QualificationManagementField from '@src/component/common/BaseSelectUser/components/Form/QualificationManagementField.tsx'

/* model */
import ComponentNameEnum from '@model/enum/ComponentNameEnum'
import {
  TaskAllotUserTableCommonColumns,
  QualificationsEnum,
  GetServiceProviderListParams,
  TaskAllotServiceProviderInfo,
  TaskAllotServiceProviderEmitEventEnum
} from '@src/modules/task/components/TaskAllotModal/TaskAllotServiceProvider/TaskAllotServiceProviderModel'
import Page from '@model/Page'
import Column from '@model/types/Column'
import { TaskAllotTypeModeEnum, ICreateEditTask, TaskVNodeTypeEnum } from '@src/modules/task/components/TaskAllotModal/TaskAllotModalModel'
import HookEnum from '@model/enum/HookEnum'
import StorageModuleEnum from '@model/enum/StorageModuleEnum'
import StorageKeyEnum from '@model/enum/StorageKeyEnum'
import PageInfo from '@model/entity/PageInfo'
import { DepeMultiUserResult, ITaskAllotProduct } from '@src/modules/task/components/TaskAllotModal/TaskAllotModalInterface'
import { MAX_SELECT_USER_COUNT } from '@src/model/const/Number'
import LoginUser from '@model/entity/LoginUser/LoginUser'
import TaskConfig from '@model/config/TaskConfig'
import { BaseSelectUserModeEnum } from '@src/component/common/BaseSelectUser/model/enum'
import { chooseExUser } from '@model/config/SelectUserConfig'

/* api */
import { getServiceProviderApi, getTopCatalogListByName, getTaskConfig, getSpUserForSpAllot } from '@src/api/TaskApi'
import { searchEngineerConfig } from '@src/api/serviceProviderApi';
// 工单筛选数据 工单资质、服务商资质、产品类型资质
import { getAsyncTaskListInitData } from '@src/api/InitDataApi'
// 工单设置
import { getTenantConfigByCodeList, getTenantConfigData } from '@src/api/SettingApi'
/* directive */
import Loadmore from '@src/directive/loadmore'

/* util */
import { findComponentUpward, findComponentDownward } from '@src/util/assist'
import * as _ from 'lodash'
import LogUtil from '@src/util/log.ts'
import Platform, { isOpenData } from '@src/util/platform.ts'
import { storageGet, storageSet } from '@src/util/storage.ts'
import i18n from '@src/locales'
import { isQualification } from '@src/util/grayInfo';
import { isEmpty } from '@src/util/type'
import { getOssUrl } from '@src/util/assets'


import { baseDistValueToObject } from '@src/util/addressUtil';
import AuthEnum from '@model/enum/AuthEnum'
const defaultAvatar = getOssUrl('/avatar.png')

/* 加载的组件 */
const LoadComponentMap: { [x: string]: string } = {
  [TaskAllotTypeModeEnum.ServiceProvider]: ComponentNameEnum.TaskAllotServiceProviderTable,
}


@Component({
  name: ComponentNameEnum.TaskAllotServiceProvider,
  components: {
    TaskAllotServiceProviderTable,
    UiInput,
    TaskCascader,
    DynamicContentTree
  }
})

class TaskAllotServiceProvider extends VC {
  /* 服务商 -> 人员表格组件 */
  @Ref() readonly TaskAllotServiceProviderTableComponent!: TaskAllotServiceProviderTable
  /* 派单方式 模式(服务商列表) */
  @Prop() readonly mode: TaskAllotTypeModeEnum | undefined
  /* 异常列表 */
  @Prop({ default: [] }) readonly backList: string[] | undefined
  /* 转派选择的原因 */
  @Prop({ default: '' }) readonly customReason: string | undefined
  /* 是否为转派 */
  @Prop() readonly isReAllot: boolean | undefined
  /* 是否显示协同人 */
  @Prop() readonly isShowSynergy: boolean | undefined
  /* 转派原因 */
  @Prop() readonly reason: string | undefined
  /* 协同人列表 */
  @Prop() readonly synergyUserList: LoginUser[] | undefined
  /* 工单信息 */
  @Prop() readonly task: any | undefined
  /* 工单设置 */
  @Prop() readonly taskConfig: TaskConfig | undefined
  /*判断是否配置 */
  @Prop() readonly systemAdmin: any | undefined
  /* 转派原因是否必填 */
  @Prop() readonly notNull: boolean | undefined
  /* 新建、编辑工单相关数据 */ 
  @Prop({ default: {}}) readonly createEditTask: ICreateEditTask = {}
  /* @人员 */ 
  @Prop() readonly atUsers: [] | undefined

  /* 是否是按服务团队派单 */
  get allotByExclusiveTag() {
    return this.taskConfig?.allotByExclusiveTag === true
  }
  // 选择列 组件
  get BaseTableAdvancedSettingComponent(): any {
    return findComponentDownward(this, ComponentNameEnum.BaseTableAdvancedSetting)
  }
  /* 是否是按团队派单 */
  get isAllotByTag() {
    return this.taskConfig?.allotByTag === true
  }

  /** 
   * @description 是否显示服务商派单表格组件
  */
  get isShowTaskAllotServiceProviderTableComponent(): boolean {
    return this.loadedComponents.includes(ComponentNameEnum.TaskAllotServiceProviderTable)
  }

  /* 加载的模块组件 */
  get modeComponents(): { [x: string]: { [y: string]: string } } {
    return {
      [TaskAllotTypeModeEnum.List]: { display: this.mode === TaskAllotTypeModeEnum.List ? 'block' : 'none' },
    }
  }

  // 判断是否是新建或编辑时，点击的派单
  get isCreateEditTaskState(): boolean {
    return this.createEditTask?.isCreateEditTask || false;
  }

  /**
   * @description 转派说明是否必填
   */
  get reallotRemarkNotNull(): boolean {
    return this.taskConfig?.reallotRemarkNotNull === true
  }
  /* 当前选择的服务商 */
  public selectServiceProvider: TaskAllotServiceProviderInfo[] = []
  /* 表格列 */
  public columns: Column[] = TaskAllotUserTableCommonColumns
  /* 是否禁用加载更多 */
  public isDisableLoadmore: boolean = false
  /* 是否在正在加载中 */
  public isHaveLoading: boolean = false
  /* 已经加载的组件列表 TODO: 实现 keep-alive component 功能 */
  public loadedComponents: string[] = [ComponentNameEnum.TaskAllotServiceProviderTable]
  /* 等待状态 */
  public pending: boolean = false
  /* 当前选择的排序方式 */
  public selectSortord: number | null = null
  /* 表格服务商page */
  public tableServiceProviderPage: Page = new Page()
  /* 表格高度 */
  public tableHeight: number | null = null
  /* 服务商选择状态 */
  public serviceProviderPageCheckedMap: { [x: string]: boolean } = {}
  /* 模糊搜索 */
  public keyword: string | null = ''
  /* 分页 */
  public pageNum = 1
  /* 资质列表 */
  public qualificationsList: { label: string, value: string }[] = [
    { label: i18n.t('task.components.taskAllotModal.taskQualification') as string, value: QualificationsEnum.TaskQualifications },
    { label: i18n.t('task.components.taskAllotModal.productMenuQualification') as string, value: QualificationsEnum.ProductQualifications },
    { label: i18n.t('task.components.taskAllotModal.areaQualification') as string, value: QualificationsEnum.AreaQualifications },
    { label: i18n.t('common.form.type.engineerQualification') as string, value: QualificationsEnum.ServiceProviderQualification },
  ]
  /* 是否选择了授权资质 */
  public isSelectQualifications: boolean = false
  /* 选中的授权资质的值 */
  public selectQualifications: string[] = []

  // 服务商数据
  public ServiceProviderData: string[] = []

  // 是否派单给服务商的工程师
  public assignToEngineerEnable: boolean = false
  // 查询状态
  public  pendingState: boolean = false;

  // 查询工程师的时候，选人组件需要排除的工作状态
  public stateBlackList: string[] = []

  // 是否第一次打开
  public firstOpenEnable: boolean = true;

  public GetServiceProviderData(){
    getSpUserForSpAllot({
      pageNum: 0,
    })
    .then(res=>{
      if(res.status === 0){
        this.ServiceProviderData = res.data?.list.map(item=>{
          item.label = item.serviceProviderName
          return item
        }) || []
      }
    })

  }

  /**
   * @des 协同人选人组件需要的自定义参数
   * @see https://publink.yuque.com/docs/share/14a5c525-1842-4a59-b09a-1435b8e3592b?#
   */
  public synergySelectOptions: object={
    ...chooseExUser
  }

  @Watch('mode', { immediate: true })
  onModeChangedHandler(newValue: string) {
    // 添加到已加载的组件列表
    this.loadedComponents.push(LoadComponentMap[newValue])
  }

  @Watch('serviceProvider', { immediate: true })
  onServiceProviderChangedHandler(newValue: LoginUser | null) {
    if (!newValue) {
      this.selectServiceProvider = []
    } else {
      this.selectServiceProvider = [newValue]
    }
  }

  /**
 * @description 服务商删除事件
 */
  @Emit(TaskAllotServiceProviderEmitEventEnum.DeleteServiceProvider)
  public serviceProviderCloseHandler(info: TaskAllotServiceProviderInfo): TaskAllotServiceProviderInfo {
    return info
  }

  /**
   * @description 协同人用户删除事件
   */
  @Emit(TaskAllotServiceProviderEmitEventEnum.DeleteSynergyUser)
  public synergyUserCloseHandler(user: LoginUser): LoginUser {
    return user
  }

  /**
   * @description 协同人用户列表变化事件
   */
  @Emit(TaskAllotServiceProviderEmitEventEnum.SetSynergys)
  public synergyUserListChangedHandler(users: LoginUser[]): LoginUser[] {
    return users
  }

  /**
 * @description 服务商变化事件
 */
  @Emit(TaskAllotServiceProviderEmitEventEnum.SetServiceProvider)
  public serviceProviderListChangedHandler(list: TaskAllotServiceProviderInfo[]): TaskAllotServiceProviderInfo[] {
    return list
  }

  /**
   * @description 转派原因变化事件
   */
  @Emit(TaskAllotServiceProviderEmitEventEnum.SetReason)
  public reasonChangedHandler(reason: string): string {
    return reason
  }

  /**
   * @description 转派原因at人员
   */
  @Emit(TaskAllotServiceProviderEmitEventEnum.SetAtUsers)
  public atUsersChangedHandler(users: []): [] {
    return users
  }

  @Emit(TaskAllotServiceProviderEmitEventEnum.SetCustomReason)
  /**
   * @description  转派说明
   */
  public customReasonChangedHandler(customReason: string): string {
    return customReason
  }

  /** 
   * @description 绑定表格滚动事件
  */
  public bindTableScrollEvent(): void {
    // 滚动元素
    let scrollEl = this.getScrollTableEl();
    // 绑定
    Loadmore.bind(
      scrollEl,
      {
        value: {
          distance: 10,
          disabled: this.isDisableLoadmore,
          callback: () => this.loadmore()
        }
      }
    )

    // 解除绑定
    this.$once(HookEnum.Destroyed, () => {
      Loadmore.unbind(scrollEl)
    })

  }

  /** 
   * @description 构建列
  */
  public async buildColumns() {
    const res = await searchEngineerConfig();
    let customColumns = res?.result || [];
    // 单行文本、多行文本、数字、下拉菜单、多级菜单、人员、日期、日期时间、电话、邮箱、地址、位置、计算公式
    let filterType = ['text', 'textarea', 'number', 'select', 'cascader', 'user', 'date', 'datetime', 'phone', 'email', 'address', 'location', 'formula']
    customColumns.forEach((item: any) => {
      if (!this.columns.find((column: Column) => column.field === item.fieldName) && filterType.includes(item.formType) && !item.isSystem && item.isHidden !== 1) {
        this.columns.push({
          "label": item.displayName,
          "field": item.fieldName,
          "formType": item?.formType,
          "show": true,
          "fixed": false,
          "sortable": 'custom',
          "width": "150px",
          "type": "column",
        })
      }
    });
    let localColumns = await this.getDataToStorage(StorageKeyEnum.TaskAllotTableColumns, [])
    localColumns = localColumns.filter((item: any) => {
      const correspondingCustomColumn = customColumns.find((customItem: any) => customItem.fieldName === item.field);
      return correspondingCustomColumn ? correspondingCustomColumn.isHidden !== 1 : true;
    });
    if (localColumns.length <= 0) return
    localColumns.forEach((item:{field:string,show:boolean,width:string}) => {
        if(item.field === 'userId'){
          item.show = true
          item.width = '50px'
        }
      })
    let columnMap = localColumns.reduce((acc: any, current: Column) => {
      acc[current.field || ''] = current
      return acc
    }, {})

    this.columns = this.columns.map((column: Column) => {
      column.show = columnMap[column.field || '']?.show || false
      // 不可编辑的字段永远不可能被隐藏
      if(column?.disabled === true){
        column.show = true
      }
      column.width = columnMap[column.field || '']?.width || undefined
      return column
    })
  }

  /**
   * @description 构建搜索服务商参数
   */
  public buildServiceProviderParams() {
    let params: GetServiceProviderListParams = {
      pageNum: this.pageNum ++,
      pageSize: 20,
      taskId: this.task?.id || '',
    }
    // 区域类型资质
    if(!!this.customerAreaQualification){
      params.customerAreaQualification = this.ScreenParams.customerAreaQualification
    }
    // 工单资质
    if(this.ScreenParams.taskTypeQualification){
      params.taskTypeQualification = this.ScreenParams.taskTypeQualification
    }
    // 服务商资质
    if(this.ScreenParams.serviceProviderQualification){
      params.serviceProviderQualification = this.ScreenParams.serviceProviderQualification
    }
    // 工程师资质
    if(this.ScreenParams.engineerQualification){
      params.engineerQualification = this.ScreenParams.engineerQualification
    }
    // 产品类型资质
    if(this.ScreenParams.productTypeQualification){
      params.productTypeQualification = this.ScreenParams.productTypeQualification.map(item=>{
        return item.label
      })
    }
    // 资质管理
    // 产品类型资质
    if(this.ScreenParams.qualificationIds){
      let arr = []
      this.ScreenParams.qualificationIds?.forEach(item=>arr.push(item.value))
      params.qualificationIds = arr
    }
    if(this.allowQualificationManagementServiceProvider){
      let taskInfo = this.isCreateEditTaskState ? this.createEditTask?.task : this.task
      params.taskTypeQualification = taskInfo?.templateId
      params.productTypeQualification = taskInfo?.products?.filter(item=>!isEmpty(item.type)).map(item=>item.type)
    }else if(isQualification() && !this.allowQualificationManagementServiceProvider){
      delete params.taskTypeQualification
      delete params.productTypeQualification
    }

    
    // 模糊搜索
    if(this.ScreenParams.providerNameLike){
      params.providerNameLike = this.ScreenParams.providerNameLike
    }
   
    return params
  }
  // 工单数据初始化
  @Watch('task', { immediate: true })
  onTaskInfo(){
    this.initScreenParams()
  }

  public QualificationKey:number= Date.now()
  // 用工单数据初始化搜索列
  public async initScreenParams(){
    try {
      this.pendingState = true;

      /*查询指派工单负责人时不显示以下状态的用户Start*/
      try {
        // 获取工作状态开关
        const { data: taskData } = await getTenantConfigData();
        if(taskData?.taskConfig?.workStateConfig) {
          this.stateBlackList = taskData?.workTimeConfig?.stateBlackList ?? []
        }else {
          this.stateBlackList = []
        }
      } catch (e) {
        console.error(e)
      }
      /*查询指派工单负责人时不显示以下状态的用户End*/

      let { data } = await getTenantConfigByCodeList({
        codeList:['providerQualificationScreening', 'ASSIGN_TO_PROVIDER_ENGINEER']
      });
      let providerAutoFilterEnable = data.find(item => item.configCode === 'providerQualificationScreening')?.isOpen === 1 || false
      this.assignToEngineerEnable = data.find(item => item.configCode === 'ASSIGN_TO_PROVIDER_ENGINEER')?.isOpen === 1 || false
      // 处理等级名称显示
      let providerQualificat = this.columns?.find(column => column.field === 'serviceProviderQualification')
      providerQualificat?.label && (providerQualificat.label = this.assignToEngineerEnable ? i18n.t('common.form.type.engineerQualification') : i18n.t('common.form.type.serviceProviderQualification'))

      if(!providerAutoFilterEnable) {
        this.initialize()
        return;
      }
      // 存放用来筛选的数据,是新建编辑的就取用户填写的数据，详情的话就取详情的数据
      let taskInfo = this.isCreateEditTaskState ? this.createEditTask?.task : this.task
      console.log(taskInfo);
      // 如果存在工单资质
      if(taskInfo?.templateId){
        this.ScreenParams.taskTypeQualification = taskInfo?.templateId || ''
      }
      // 如果存在产品类型资质，则需要添加对应参数
      // 过滤脏数据
      if(taskInfo?.products && taskInfo?.products.length && taskInfo?.products[0]?.type){
        // 用产品类型名称去换取对应的产品目录
        await getTopCatalogListByName({
          name:taskInfo?.products?.[0].type
        })
            .then(res=>{
              if(res.code === 0 && res?.result?.id){
                let data = res?.result || {}
                this.ScreenParams.productTypeQualification = [{
                  value:data.id,
                  label:data.name,
                  id:data.id,
                  name:data.name,
                }]
                this.QualificationKey =  Date.now()

              }
              return
            })
      }
      // 如果存在服务商资质，则需要添加对应参数
      if (taskInfo?.serviceProviderQualification) {
        this.ScreenParams.serviceProviderQualification = taskInfo.serviceProviderQualification
      }
      // 如果存在工程师资质，则需要添加对应参数
      if (taskInfo?.engineerQualification) {
        this.ScreenParams.engineerQualification = taskInfo.engineerQualification
      }
      // 如果存在区域类型资质，则需要添加对应参数
      if (taskInfo?.address || taskInfo?.taddress) {
        const province =  taskInfo?.address?.province || taskInfo?.taddress?.province || ''
        const city =  taskInfo?.address?.city ||taskInfo?.taddress?.city || ''
        const country =  taskInfo?.address?.country || taskInfo?.taddress?.country || ''
        const dist = taskInfo?.address?.dist || taskInfo?.taddress?.dist || ''

        if(province || city || country || dist){
          this.ScreenParams.customerAreaQualification = {
            province,
            city,
            country,
            dist
          }
          this.customerAreaQualification = { ...this.ScreenParams.customerAreaQualification }
        }
      }
      this.initialize()
    } catch (e) {
      console.error(e)
    } finally {
      this.pendingState = false;
    }
  }


  /**
   * @description 计算表格高度
   */
  public computedTableHeight() {
    // 窗口高度
    let windowHeight = window.innerHeight || document.body.clientHeight
    // 其他元素高度
    let otherElHeight = 386 + (this.isReAllot ? 48 : 0)
    this.tableHeight = windowHeight - otherElHeight
  }

  /** 
   * @description 选择协同人
  */
  public chooseSynergyUser(): void {
    let options = {
      title: i18n.t('common.placeholder.selectSomething', {0: i18n.t('task.synergies')}),
      selectedUsers: this.synergyUserList,
      max: MAX_SELECT_USER_COUNT,
      unique: false,
      haveRole: true,
      mode: BaseSelectUserModeEnum.Filter,
      ...chooseExUser
    };

    // @ts-ignore
    this.$fast.select.multi.user(options)
      .then((result: DepeMultiUserResult) => {
        let isSuccess = result.status == 0
        if (!isSuccess) return

        // 协同人赋值
        this.synergyUserListChangedHandler(result?.data?.users || [])
      })
      .catch((err: any) => {
        console.error(err)
      })
  }

  /** 
   * @description 获取服务商列表
   * -- 内部调用的
  */
  public getServiceProvider = _.debounce(function() {
    this.pending = true
    const params = this.buildServiceProviderParams()
    getServiceProviderApi(params)
      .then((data) => {
        let isSuccess = data.success
        if (!isSuccess) {
          Platform.alert(data.message)
        }
        data.result = data?.result || []
        // 服务商列表处理
        this.tableServiceProviderPageHandler(data.result)
      })
      .catch(err => {
        LogUtil.error(err, this.getServiceProvider.name)
      })
      .finally(() => {
        this.pending = false
        this.isHaveLoading = false
      })
  }, 300)

  public getAttributes(): any {
    return {
      directives: [
        {
          name: 'loading',
          value: this.pending
        }
      ]
    }
  }

  /** 
   * @description 获取滚动的表格元素
  */
  public getScrollTableEl(): Element | null {
    const ProviedTable = document.getElementById('ProviedTable')
    return ProviedTable.querySelector('.task-allot-user-table-block .el-table__body-wrapper')
  }

  /** 
   * @description 获取滚动的表格元素
  */
  public getScrollGanttEl(): Element | null {
    return document.querySelector('.task-allot-user-calendar #gantt-container')
  }

  /**
   * @description 从缓存获取数据
  */
  public async getDataToStorage(key: string, data: any) {
    return await storageGet(key, data, StorageModuleEnum.Task)
  }

  /** 
   * @description 排序变化
  */
  public handlerTableSortChanged(option: { prop?: any, order?: any } = {}) {
    const { prop, order } = option

    if (!order) {

      this.selectSortord = null
      return this.initialize()
    }
    this.selectSortord = null
    this.initialize()
  }

  /** 
   * @description 表格拖动事件
  */
  public handlerHeaderDragend(newWidth: number, tableColumn: any = {}) {
    let field: string = tableColumn.property || ''
    let column: Column | null = null

    for (let i = 0; i < this.columns.length; i++) {
      column = this.columns[i]
      if (column.field === field) {
        column.width = newWidth
      }
    }

    const columns = this.simplifyTableColumnsProperty(this.columns)
    this.saveDataToStorage(StorageKeyEnum.TaskAllotTableColumns, columns)
  }

  /**
   * @description 初始化 获取服务商列表
  */
  public initialize(): void {
    LogUtil.succ(LogUtil.Start, this.initialize.name)

    this.tableServiceProviderPage = new Page({ pageNum: 0, pageSize: 20 })
    this.pageNum = 1;
    this.pending = true
    // 抓取服务商列表数据
    this.getServiceProvider()

    this.GetServiceProviderData()
  }

  /** 
   * @description 加载更多
  */
  public loadmore() {
    if (this.isHaveLoading) return;

    if (this.isDisableLoadmore) {
      return LogUtil.warn('Caused: this.isDisableLoadmore is true', this.loadmore.name)
    }


    this.isHaveLoading = true;

    LogUtil.succ(LogUtil.Start, this.loadmore.name)
    // 抓取服务商列表数据
    this.getServiceProvider()
  }

  /**
   * @description 设置选择的服务商
   * @param {Boolean} isSelected 是否选中
   * @param {TaskAllotServiceProviderInfo} serviceProviderInfo 服务商信息
   * -- 支持外部调用的
  */
  public outsideSetServiceProvider(isSelected: boolean, serviceProviderInfo: TaskAllotServiceProviderInfo) {
    let isChecked: boolean = false
    for (let key in this.serviceProviderPageCheckedMap) {
      isChecked = isSelected && key == serviceProviderInfo?.userId
      this.serviceProviderPageCheckedMap[key] = isChecked
      // this.ScreenParams.selectServiceProvider = key
    }
    let serviceProvider = isSelected ? serviceProviderInfo : {}
    this.selectServiceProvider = (
      isSelected
        ? [{
          serviceProviderId: serviceProvider.serviceProviderId || '',
          serviceProviderName: serviceProvider.serviceProviderName || '',
          userId: serviceProvider?.userId || '',
          username: serviceProvider?.username || '',
        }]
        : []
    )

    this.ScreenParams.selectServiceProvider = this.selectServiceProvider?.[0]?.userId || ''
    // 调用emit告诉父组件选中的服务商数据
    this.serviceProviderListChangedHandler(this.selectServiceProvider)
  }

  /**
   * @description 向上 设置选择的服务商
   * -- 支持外部调用的
  */
  public outsideUpwardSetServiceProvider(isSelected: boolean, serviceProviderInfo: any) {
    if (isSelected) {
      serviceProviderInfo.selfSelected = true
    }
    this.outsideSetServiceProvider(isSelected, serviceProviderInfo)
  }

  /**
   * @description 拖动变化事件
   * -- 支持外部调用的
  */
  public outsideDragendHandler(newWidth: number, oldWidth: number, tableColumn: any = {}) {
    this.handlerHeaderDragend(newWidth, tableColumn)
  }

  /**
   * @description 排序变化事件
   * -- 支持外部调用的
  */
  public outsideSortChangedHandler(option: { prop?: any, order?: any } = {}) {
    this.handlerTableSortChanged(option)
  }



  /**
   * @description 移除协同人
   */
  public removeSynergyUser(user: LoginUser) {
    this.synergyUserCloseHandler(user)
  }

  // 移除服务商
  public removeServiceProvider(info: TaskAllotServiceProviderInfo) {
    this.serviceProviderCloseHandler(info)
    this.selectServiceProvider = []
    this.$nextTick(() => {
      this.initialize()
    })
  }




  /** 
   * @description 显示高级设置 选择列
  */
  public showAdvancedSetting(): void {
    this.BaseTableAdvancedSettingComponent.open(this.columns)
  }

  /**
   * @description 保存指派表格列
  */
  public saveTaskAllotTableColumn(value: { type: string, data: any[] }) {
    let columns = value.data || []
    let columnMap = columns.reduce(
      (acc, col) => (acc[col.field] = col) && acc,
      {}
    )

    this.columns = this.columns.map((column: Column) => {
      let newCol = columnMap[column.field || ''] || {}

      column.show = newCol.show
      column.width = newCol.width

      return column
    })

    const showColumns = this.simplifyTableColumnsProperty(this.columns)

    this.saveDataToStorage(StorageKeyEnum.TaskAllotTableColumns, showColumns)
  }

  /* 精简列属性 */
  public simplifyTableColumnsProperty(columns: Column[]): Column[] {
    return (
      columns.map((column: Column) => ({
        field: column.field,
        show: column.show,
        width: column.width
      }))
    )
  }

  /**
   * @description 保存数据到缓存
  */
  public saveDataToStorage(key: string, data: any) {
    storageSet(key, data, StorageModuleEnum.Task)
  }

  /**
   * @description 表格用户列表处理
  */
  public tableServiceProviderPageHandler(result: PageInfo<TaskAllotServiceProviderInfo>) {
    try {
      // 合并page数据
      // this.tableServiceProviderPage.list = []
      this.tableServiceProviderPage.merge({
        ...result,
        list: (result?.list || []).map(item => {
          return {
            ...item,
            ...item?.formVOList,
          }
        })
      })
      // 去重
      this.tableServiceProviderPage.list = _.uniqBy(this.tableServiceProviderPage.list, 'userId')
      // key : userId(string) -> value: boolean
      this.serviceProviderPageCheckedMap = (
        this.tableServiceProviderPage.list
          .reduce((acc: { [x: string]: boolean }, cur: LoginUser) => {
            acc[cur.userId] = Boolean(this.selectServiceProvider[0]?.userId === cur?.userId)
            return acc
          }, {})
      )
      // 设置服务商列表数据
      this.TaskAllotServiceProviderTableComponent?.outsideSetServiceProviderPage(this.tableServiceProviderPage.list)
      // 是否禁用加载更多
      this.isDisableLoadmore = !(result?.hasNextPage === true)
      // 解绑滚动事件
      this.unBindTableScrollEvent()
      // 添加滚动事件
      this.$nextTick(() => {
        this.bindTableScrollEvent()
        // 列表数据不更新错位
        //  this.TaskAllotServiceProviderTableComponent?.TaskAllotServiceProviderElTableComponent?.doLayout();
      })

      LogUtil.succ(LogUtil.End, this.tableServiceProviderPageHandler.name)

    } catch (error) {
      LogUtil.error(error, this.tableServiceProviderPageHandler.name)
    }
  }

  /** 
   * @description 解绑表格滚动事件
  */
  public unBindTableScrollEvent() {
    // 滚动元素
    let scrollEl = this.getScrollTableEl();
    // 绑定
    Loadmore.unbind(scrollEl)
  }

  /**
   * @description 渲染协同人
  */
  public renderSynergySelect(): any | null {
    if (!this.synergyUserList) return null

    // 是否存在协同人
    let isHaveSynergyUser = this.synergyUserList.length > 0
    // 需要显示的第一个协同人
    let user: any =this.synergyUserList.length? this.synergyUserList[0] : {}
    // 是否有更多的协同人 (大于1个)
    let isMoreOne = Boolean(this.synergyUserList && this.synergyUserList.length > 1)
    // 样式类名
    let classNames: string[] = ['task-allot-sysnergy-select']

    return (
      <div class={classNames} onClick={() => this.chooseSynergyUser()}>
        <ui-input style={`min-height:${this.synergyUserList.length > 5 ? '60px' : '32px'};width:400px;`} hideIcon placeholder={i18n.t('common.placeholder.selectSomething', {0: i18n.t('task.synergies')})}>
          {
            isHaveSynergyUser && (
              this.synergyUserList.map(item=>{
                return (
                  <el-tag key={item?.userId} size='mini' disable-transitions closable type='info' onClose={() => this.removeSynergyUser(item)}>
                    {isOpenData ? <open-data type="userName" openid={item.staffId}></open-data> : item.displayName}
                  </el-tag>
                )
              })
            )
          }
        </ui-input>
      </div>
    )
  }

  // 模糊搜索
  public renderTaskSearch(){
    return (
      <div class='TaskSearch'>
        <el-input class="TaskSearch-input" placeholder={i18n.t('common.placeholder.inputName')}
          value={this.ScreenParams.providerNameLike} 
          onInput={(value)=>{this.ScreenParams.providerNameLike = value}}></el-input>
        <div onClick={this.initialize} class="TaskSearch-buttom">{i18n.t('common.base.search')}</div>
      </div>
    )
  }

  public renderEngineer():any {
    let classNames: string[] = ['task-allot-sysnergy-select']
    // 是否存在服务商
    let isHaveServiceProvider = this.selectServiceProvider.length > 0
    // 需要显示的第一个服务商
    let user: any = this.selectServiceProvider?.[0] || {}
    let name = `${user?.tagNameList || user?.serviceProviderName || ''}-${user?.displayName || user?.username || ''}`
    return (
        <div className={classNames}>
          { <ui-input style={'width:240px;'} onClick={() => this.chooseSynergyProvider()}  hideIcon placeholder={i18n.t('common.placeholder.selectEngineer', {0: i18n.t('common.form.type.serviceProviders')})}>
          {
            isHaveServiceProvider && (
              <el-tag key={user?.userId} size='mini' disable-transitions closable type='info' onClose={() => this.removeServiceProvider(user)}>
                { name }
              </el-tag>
            )
          }
        </ui-input> }
        </div>
    )
  }
  // 渲染服务商
  public renderServiceProvider():any {
    if (!this.selectServiceProvider) return null
    // 样式类名
    let classNames: string[] = ['task-allot-sysnergy-select']
    if(this.assignToEngineerEnable) return this.renderEngineer();
    return (
      <div class={classNames}>

        <el-select 
        clearable
        filterable
        style="width:160px;margin-left:8px;" 
        value={this.ScreenParams.selectServiceProvider} 
        onInput={(value)=>{this.ScreenParams.selectServiceProvider = value}} 
        onChange={this.selectProvider}
        placeholder={i18n.t('common.placeholder.selectSomething', {0: i18n.t('common.form.type.serviceProviders')})}
        >
        {
          this.ServiceProviderData.map((item:any)=>{
            return (
              <el-option
                key={item.userId}
                label={item.label}
                value={item.userId}
                >
              </el-option>
            )
          })
        }
      </el-select>
      </div>
    )
  }

  /**
   * @description 单独下拉框选择服务商
  */
  public selectProvider(userId: any): any {
    this.selectServiceProvider = this.ServiceProviderData.filter((item) => item.userId === userId)
    // 调用emit告诉父组件选中的服务商数据
    this.serviceProviderListChangedHandler(this.selectServiceProvider)
    this.initialize()
  }

  /**
   * @description 选择服务商
  */
     public chooseSynergyProvider(): void {

      let options = {
        title: i18n.t('common.placeholder.selectEngineer'),
        max: 1,
        unique: false,
        mode: BaseSelectUserModeEnum.Filter,
        showRole: false,
        showDepartment:false,
        isShowDynamic: true,
        hideNullProvider: true, // 隐藏没有工程师的服务商
        isShowSearchOrganizationUsers: false, // 搜索不显示内部人员
        isShowSearchServiceProviderUsers: true, // 搜索显示工程师
        authKey: AuthEnum.TASK_DISPATCH,
        stateBlackList: this.stateBlackList
      };

      // @ts-ignore
      this.$fast.select.single.user(options)
        .then((result: DepeMultiUserResult) => {
          let isSuccess = result.status == 0
          if (!isSuccess) return
          this.selectServiceProvider = result?.data?.all || []
           // 调用emit告诉父组件选中的服务商数据
          this.serviceProviderListChangedHandler(this.selectServiceProvider)
          this.initialize()
         })
        .catch((err: any) => {
          console.error(err)
        })
    }

  /**
   * @description 渲染工单派单转派原因
   */
  public head(src:string):String{
    if (!src) return defaultAvatar
    return src
  }
  public renderTaskAllotReason(): VNode | null {
    const scopedSlots ={
      item: (props: any) => {
        return (
          <div>
            <img src={this.head(props.user.head)} />
            <span>{props.user.displayName}</span>
          </div>
        )
      },
    }
    return (
      <biz-at-textarea 
        class="biz-at-textarea attextarea" 
        ref="atTextarea" 
        search-url="/message/user/lists" 
        name-key="displayName" 
        value={this.reason} 
        onSetUsers={(users: []) =>this.atUsersChangedHandler(users)}
        scopedSlots={scopedSlots}
        direction="bottom"
        >
          <el-input
            autocomplete="off"
            class='task-allot-reason-input'
            type='textarea'
            resize="none"
            value={this.reason}
            placeholder={i18n.t('task.tip.taskAllotModalTip12',)}
            autosize={{ minRows: 1, maxRows: 2 }}
            onInput={(value: any) =>this.reasonChangedHandler(value)}
        />
       </biz-at-textarea>
    )
  }

  public renderTaskAllotExecutorHeaderRow(label: string, node: VNode | null, style:string = ''): any {
    return (
      <div class='task-allot-executor-header-row' style={style}>
        <span class='task-allot-executor-header-row-label1'>
          { (this.reallotRemarkNotNull && label !== TaskVNodeTypeEnum.Synergies) ? <span class="task-cef">*</span> : null }
          {label}
        </span>
        {node}
        { (label === TaskVNodeTypeEnum.ServiceProvider && !this.assignToEngineerEnable) && this.renderTips(i18n.t('task.components.taskAllotModal.allotToServiceProviderManager')) }
      </div>
    )
  }

  public renderTips(content:string): any{
    return (
      <el-tooltip class="item" effect="dark" content={content} placement="top">
         <span class="tips iconfont icon-info-circle"></span>
      </el-tooltip>
     
    )
  }

  /** 
   * @description 渲染工单派单头部派单选择
  */
  public renderTaskAllotExecutorHeader(): any {
    return (
      <div>
        <div class='task-allot-executor-header'>
          <div class='task-allot-executor-header-left'>
            {this.renderTaskAllotExecutorHeaderRow(this.assignToEngineerEnable ? `${i18n.t('common.base.engineer')}：` : `${i18n.t('common.form.type.serviceProviders')}：`, this.renderServiceProvider())}
            {this.renderTaskAllotExecutorHeaderRow(`${i18n.t('task.synergies')}：`, this.renderSynergySelect())}
          </div>
          {/* <div class='task-allot-executor-header-right'>
            {this.renderTaskSearch()}
          </div> */}
        </div>
      </div>
    )
  }


  /**
   * @description 渲染工单派单转派原因行
  */
  public renderTaskAllotReasonRow(): any | null {
    if (!this.isReAllot) return null

    return (
      <div class='task-allot-reason-row task-allot-executor-header' style='margin-bottom:12px;'>
        <div class="task-flex task-ai task-mr12">
          {
            this.notNull ? <span class="task-cef">*</span> : null
          }
          {i18n.t('task.list.displayName.transferReason')}：
          <el-select style='width:240px;' value={this.customReason} placeholder={`${this.notNull ? i18n.t('common.base.isArrRequire') : i18n.t('common.base.isArrOptional')}${i18n.t('task.tip.taskAllotModalTip13')}`} onChange={(v: string) => this.customReasonChangedHandler(v)}>
            {
              this.backList && this.backList.map(item => {
                return (
                  <el-option
                    key={item}
                    label={item}
                    value={item}
                  />
                )
              })
            }
          </el-select>
          {
            this.systemAdmin ? <div class="task-font12 task-c13 task-ml12 task-pointer" onClick={() => { window.location.href = '/setting/task/taskSet' }}>
              <el-tooltip class="item" effect="dark" content={i18n.t('task.goSetReason')} placement="top">
                <span class="iconfont icon-xitongguanli" style="color:#6c6c6d;"></span>
              </el-tooltip>
            </div> : null
          }</div>
        {this.renderTaskAllotExecutorHeaderRow(TaskVNodeTypeEnum.AllotReason, this.renderTaskAllotReason(), 'flex: 1 0 auto;')}
      </div>
    )
  }

  /** 
   * @description 渲染工单派单头部派单选择
  */
  public renderTaskAllotExecutorBackgroundChunk() {
    return (
      <div class='task-allot-executor-chunk'></div>
    )
  }

  public allowQualificationManagementServiceProvider = false
  /** 
   * @description 渲染工单指派人员表格头部
  */
  public renderTaskAllotUserTableHeader(): any {
    return (
      <div>
        <div class='task-allot-user-table-header'>
          <div class="flex-1">
            { isQualification() ? <el-checkbox style="margin-right:8px;" v-model={this.allowQualificationManagementServiceProvider} label={this.$t('qualificationManagement.view.label10')} onChange={(e)=>{this.allowQualificationManagementServiceProvider =e;this.initialize()}} /> : ''}
          </div>
          {this.renderTaskAllotUserTableHeaderSelectBlock()}
        </div>
        <div class="mar-b-16 mar-l-16" v-show={this.showServiceProviderFilter}>
          {this.renderTaskScreen()}
        </div>
    </div>
    )
  }


  // 获取筛选数据
  public GetSelectTaskListInitData():any{
    getAsyncTaskListInitData({
      listForMenu:1,
      pageSize:10,
    })
    .then(res=>{
      let initData = res?.data?.initJson
      if(res.status === 0){
        // 工单数据
        this.ScreenParamsData.taskTypeQualification = initData?.tagTaskTypeList || []
        // 服务商资质
        this.ScreenParamsData.serviceProviderQualification = initData?.taskConfig?.spQualificationDataSource?.map((item:any)=>{
          return {
            id:item,
            label:item,
            value:item
          }
        }) || []
        // 工程师资质
        this.ScreenParamsData.egQualificationDataSource = initData?.taskConfig?.egQualificationDataSource?.map((item:any)=>{return { id:item, label:item, value:item}}) || []
        // 当前服务商资质数据未匹配上,删除空数据
        if(!initData?.taskConfig?.spQualificationDataSource.includes(this.ScreenParams.serviceProviderQualification)
          ){
            this.ScreenParams.serviceProviderQualification = ''
        }
        if(!this.ScreenParamsData.taskTypeQualification.some(item=>{
          return item.id === this.ScreenParams.taskTypeQualification
        })){
          this.ScreenParams.taskTypeQualification = ''
        }
      }
    })
  }
  public renderTaskAllotUserServiceProviderTableHeaderQualificationManagement(){
    return (
      <QualificationManagementField key="serviceProvider" style="min-width:150px" value={this.ScreenParams.qualificationIds} needLabel={false} onInput={(e)=>{
        this.ScreenParams.qualificationIds = e
        this.initialize()
      }} />
    )
  }
  /** 
   * @description 筛选
  */
  public renderTaskScreen(): any{
    return (
      <div class="TaskScreen flex-x al-center flex-wrap">
        {/* 区域类型资质 */}
        {this.AddressSelection()}
        {/* 服务商资质 */}
        { this.assignToEngineerEnable ? this.EngineerQualification() : this.ServiceProviderQualification()}
        {/* 产品类型资质 */}
        {isQualification() ? '' : this.ProductType()}
        {/* 工单类型 */}
        {isQualification() ? '' : this.WorkType()}
        {/* 资质证书 */}
        {this.renderTaskAllotUserServiceProviderTableHeaderQualificationManagement()}
        {/* 重置 */}
        {this.Reset()}

      </div>
    )
  }

  public Reset():any{
    const ResetData = () =>{
      const params = {
        // 关键字
        providerNameLike:'',
        // 工单资质
        taskTypeQualification:'',
        // 服务商资质
        serviceProviderQualification:'',
        // 工程师资质
        engineerQualification: [],
        // 产品类型资质
        productTypeQualification:[],
         // 区域类型资质
        customerAreaQualification:{
          province: '',
          city: '',
          dist: '',
          country:'',
        },
        qualificationIds:[]
      }
      this.ScreenParams = Object.assign(this.ScreenParams, params)
      this.pageNum = 1
      this.customerAreaQualification = {}
    }
    return (
      <el-button style="margin-left:8px;height:32px;" type="plain-third" onClick={ResetData}>{i18n.t('common.base.reset')}</el-button>
    )
  }

  public ScreenParams:any={
    // 关键字
    providerNameLike:'',
    // 工单资质
    taskTypeQualification:'',
    // 服务商
    selectServiceProvider: '',
    // 服务商资质
    serviceProviderQualification:'',
    // 工程师资质
    engineerQualification: [],
    // 产品类型资质
    productTypeQualification:[],
     // 区域类型资质
    customerAreaQualification:{
      province: '',
      city: '',
      dist: '',
      country:'',
    },
    qualificationIds:[]
  }
  // 列表数据
  public ScreenParamsData:any={
    // 工单资质
    taskTypeQualification:[],
    // 服务商资质
    serviceProviderQualification:[],
    //工程师资质
    egQualificationDataSource: [],
    // 产品类型资质
    productTypeQualification:[],
  }
  // 区域类型资质
  public customerAreaQualification:any = {}
  
  // 地址选择
  public AddressSelection():any {
    let CitySelectorChange = (val:any) =>{
      // let info = baseDistValueToObject(val)
      if(this.ScreenParams.customerAreaQualification){
        this.ScreenParams.customerAreaQualification = Object.assign(this.ScreenParams.customerAreaQualification,val) 
      }else{
        this.ScreenParams.customerAreaQualification = val
      }
      
      this.customerAreaQualification = val
      if(!this.firstOpenEnable) {
        this.initialize()
      }
      this.firstOpenEnable = false;
    }
    return (
      <base-dist  
        onInput={CitySelectorChange} 
        value={this.customerAreaQualification} 
        placeholder={i18n.t('common.form.type.authorizeAddress')}
        showOneLine={true}
      />
    )
  }
   // 服务商资质
  public ServiceProviderQualification():any {
    let change = ()=>{
      this.initialize()
    }


    return (
      <el-select 
        onChange={change} 
        class="mar-r-8"
        clearable 
        style="width:160px;margin-left:8px;" 
        value={this.ScreenParams.serviceProviderQualification} 
        onInput={(value)=>{this.ScreenParams.serviceProviderQualification = value}} 
        placeholder={i18n.t('common.form.type.serviceProviderQualification')}>
        {
          this.ScreenParamsData.serviceProviderQualification.map((item:any)=>{
            return (
              <el-option
                key={item.id}
                label={item.label}
                value={item.id}
                >
              </el-option>
            )
          })
        }
      </el-select>
    )
  }

  // 工程师资质
  public EngineerQualification():any {
    let change = ()=>{
      this.initialize()
    }

    return (
        <el-select
            onChange={change}
            class="mar-r-8"
            clearable
            multiple
            collapse-tags
            style="width:160px;margin-left:8px;"
            value={this.ScreenParams.engineerQualification}
            onInput={(value)=>{this.ScreenParams.engineerQualification = value}}
            placeholder={i18n.t('common.form.type.engineerQuality')}>
          {
            this.ScreenParamsData.egQualificationDataSource.map((item:any)=>{
              return (
                <el-option
                  key={item.id}
                  label={item.label}
                  value={item.id}
                >
                </el-option>
              )
            })
          }
        </el-select>
    )
  }

  // 产品类型
  public ProductType():any {
    const SaveData=(value:any)=>{
      this.ScreenParams.productTypeQualification = value
      this.initialize()
    }

    return (
    <TaskCascader
      defaultValue={this.ScreenParams.productTypeQualification}
      key={this.QualificationKey}
      onChange = {SaveData}
    >
    </TaskCascader>)
  }
  // 工单类型
  public WorkType():VNode {
    let change = ()=>{
      this.initialize()
    }


    return (
      <el-select 
         onChange={change} 
         clearable 
         class="mar-r-8"
         style="width:160px;margin-left:8px;" 
         value={this.ScreenParams.taskTypeQualification} 
         onInput={(value)=>{this.ScreenParams.taskTypeQualification = value}} 
         placeholder={i18n.t('common.task.taskType')}>
        {
          this.ScreenParamsData.taskTypeQualification.map((item:any)=>{
            return (
              <el-option
                key={item.id}
                label={item.name}
                value={item.id}>
              </el-option>
            )
          })
        }
      </el-select>
    )
  }
  public showServiceProviderFilter = true
  public renderTaskAllotUserTableHeaderServiceProviderAll(){
    return (
      <div class="cur-point mar-r-12 flex-x al-center" onClick={()=>{
        this.showServiceProviderFilter = !this.showServiceProviderFilter
      }}>{ this.showServiceProviderFilter ? this.$t('qualificationManagement.view.label17') : this.$t('qualificationManagement.view.label16')}<div class={[this.showServiceProviderFilter ? 'rotatez-180' : '', 'mar-l-8']} style="height:15px;line-height:1;"><i class="iconfont icon-gongsimingchengxiala" style="font-size:12px;"></i></div></div>
    )
  }

  /** 
   * @description 渲染工单指派人员表格头部 select选择
  */
  public renderTaskAllotUserTableHeaderSelectBlock() {
    return (
      <div class='task-allot-user-table-header-select-block flex-x al-center'>
        {this.renderTaskAllotUserTableHeaderServiceProviderAll()}
        {this.renderSelectColumn()}
      </div>
    )
  }






  /** 
   * @description 渲染选择列
  */
  public renderSelectColumn() {
    return (
      <div class="cur-point" onClick={()=>{
        this.showAdvancedSetting()
      }}>{this.$t('common.base.choiceCol')}<i class="iconfont icon-more mar-l-8" style="font-size:12px;"></i></div>
    )
    
  }

  created() {
    // 计算表格高度
    this.computedTableHeight()
    this.isSelectQualifications = true
  }

  mounted() {
    // 构建列
    this.buildColumns()
    this.GetSelectTaskListInitData()
  }
  render(h: CreateElement):any {
    // 属性列表
    const attrs = this.getAttributes()

    return (
      <div class={ComponentNameEnum.TaskAllotExcutor} {...attrs}>
        {/* 服务部门、负责人、协同人等搜索 */}
        {!this.pendingState && this.renderTaskAllotExecutorHeader()}
        {/* 转派原因 */}
        {this.renderTaskAllotReasonRow()}

        {this.renderTaskAllotExecutorBackgroundChunk()}
        {this.renderTaskAllotUserTableHeader()}
        {
          this.isShowTaskAllotServiceProviderTableComponent && (
            <task-allot-service-provider-table
              ref='TaskAllotServiceProviderTableComponent'
              style={this.modeComponents[TaskAllotTypeModeEnum.ServiceProvider]}
              columns={this.columns}
              assignToEngineerEnable={this.assignToEngineerEnable}
              dragendFunc={(newWidth: number, oldWidth: number, tableColumn: any = {}) => {
                this.outsideDragendHandler(newWidth, oldWidth, tableColumn)
              }}
              isDisableLoadmore={this.isDisableLoadmore}
              sortChangeFunc={(option: any) => this.outsideSortChangedHandler(option)}
              tableHeight={this.tableHeight}
              serviceProviderPageCheckedMap={this.serviceProviderPageCheckedMap}
            >
            </task-allot-service-provider-table>
          )
        }
        <base-table-advanced-setting onSave={(value: any) => this.saveTaskAllotTableColumn(value)} ref='BaseTableAdvancedSettingComponent' />
      </div>
    )
  }
}
export default TaskAllotServiceProvider