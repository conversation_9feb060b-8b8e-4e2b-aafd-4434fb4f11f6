<template>
  <div v-loading.fullscreen.lock="loading">
    <form-builder 
      v-if="showProductField"
      class="task-form-builder bbx-cell-form-builder" 
      ref="form" 
      :key="formBuilderKey"
      :class="agendaTask ? 'agenda-task-form' : ''" 
      :fields="taskFormFields" 
      :value="taskValue" 
      :isEdit="isEdit"
      :form-cell-count="formCellCount"
      :intelligentConfig="intelligentConfig"
      mode="task" 
      @update="update"
      :formEditingMode="formEditingMode"
      @getDeleteFiles="(value)=>{$emit('getDeleteFiles',(value))}"
    >

      <template slot="planNumber" slot-scope="{ field, value }">

        <!-- start 计划编号 -->
        <form-item :label="field.displayName" class="bbx-form-cell-item" :validation="false">
          <div class="form-taskNo">{{ value || $t('task.tip.editTip10') }}</div>
        </form-item>
        <!-- end 计划编号 -->
      </template>
      <template slot="taskNo" slot-scope="{ field, value }">

        <!-- start 工单编号 -->
        <form-item :label="field.displayName" class="bbx-form-cell-item" :validation="false" v-if="value">
          <div class="form-taskNo" >{{ value || $t('task.tip.editTip11') }}</div>
        </form-item>
        <!-- end 工单编号 -->

        <!-- start 工单类型 -->
        <form-item :label="$t('common.task.taskType')" class="bbx-form-cell-item" :validation="false">
          <form-select v-if="judegeSelectTaskType(value)" :field="field" :source="taskTypes" :value="selectedType.value" :clearable="false"  :disabledJustForSelect="submitCount > 0" @input="chooseTemplate"/>
          <div class="form-taskNo" v-else>{{ taskValue.templateName }}</div>
          <div v-if="agendaTask" class="task-type-tip"><i class="iconfont icon-fdn-info"></i>{{$t('task.tip.editTip12')}}</div>
        </form-item>
        <!-- end 工单类型 -->
        
      </template>

      <!-- start 计划时间 -->
      <template slot="planTime" slot-scope="{ field, value }">
        <form-item :label="field.displayName" :validation="validation.planTime">
          <form-plantime :picker-options="isVilidatePlantime ? planTimeDatePickerOptions : {}" :field="field" :value="value" @update="update"></form-plantime>
          <!-- start 通知客户 checkbox -->
          <div class="task-notice-customer-block" v-if="isShowNoticeCustomer">
            <el-checkbox :value="value ? value.tick : false" @input="noticeCustomerCheckdChange">{{$t('task.edit.sameTimeInformCustomer')}}</el-checkbox>
            <el-tooltip placement="top" :content="$t('task.tip.editTip13')">
              <i class="iconfont icon-info"></i>
            </el-tooltip>
          </div>
          <!-- end 通知客户 checkbox -->

        </form-item>
      </template>
      <!-- end 计划时间 -->
      <!-- start 计划开始时间 -->
      <template slot="planStartTime" slot-scope="{ field, value }">
        <form-item :label="field.displayName">
          <form-planstarttime :picker-options="planTimeDatePickerOptions" :field="field" :value="value" :clearable="!taskValue.templateName" @update="update" @planTimeChange="planTimeChange"></form-planstarttime>
        </form-item>
      </template>
      <!-- end 计划开始时间 -->

      <!-- start 计划完成时间 -->
      <template slot="planEndTime" slot-scope="{ field, value }">
        <form-item :label="field.displayName">
          <form-planendtime :picker-options="planTimeDatePickerOptions" :start-time="taskValue['planStartTime']" :field="field" :value="value" :clearable="!taskValue.templateName" @update="update" @planTimeChange="planTimeChange"></form-planendtime>
        </form-item>
      </template>
      <!-- end 计划完成时间 -->

      <!-- start 客户字段 -->
      <template slot="customer" slot-scope="{ field }">
        
        <!-- start 客户 -->
        <form-item class="bbx-form-cell-item" :label="field.displayName">
          <div class="input-and-btn input-btn-change">
            <biz-remote-select
              :key="JSON.stringify(value.customer)"
              :value="value.customer"
              :field="customerField"
              :remote-method="searchCustomer"
              @input="updateCustomerForCustomerSelect"
              :placeholder="$t('task.tip.searchTip', {name: $t('common.base.customer')})"
              :input-disabled="isCreateCustomer"
              :computed-width-keys="['name']"
              cleared
            >
              <div class="customer-template-option" slot="option" slot-scope="{ option }">
                <h3>{{ option.name }}</h3>
                <p>
                  <span>
                    <label>{{$t('common.base.phone')}}：</label>
                    <span>{{ option.lmPhone }}</span>
                  </span>
                  <span>
                    <label>{{$t('common.base.serialNumber')}}：</label>
                    <span>{{ option.serialNumber }}</span>
                  </span>
                  <span v-if="option && option.customerAddress">
                    <label>{{$t('common.base.address')}}：</label>
                    <span>
                      {{ option.customerAddress | fmt_address}}
                    </span>
                  </span>
                </p>
              </div>
            </biz-remote-select>
            <el-button type="text" icon="el-icon-circle-plus-outline" @click="dialogOpen('customer')" v-if="!isCreateCustomer && !justGuide && allowCreateCustomer">{{$t('common.base.create')}}</el-button>
          </div>

          <el-tooltip v-if="isShowCustomerRelevanceTaskCountButton" placement="top">
            <div slot="content" v-html="$t('task.detail.components.unfinishedAndAllTask', {unfinished: customerRelevanceTaskCountData.unfinished || 0, all: (customerRelevanceTaskCountData.all || 0) + customerArchiveCount})"></div>
            <div class="task-count-button" @click="openCustomerView">
              {{ `${customerRelevanceTaskCountData.unfinished || 0}/${(customerRelevanceTaskCountData.all || 0) + customerArchiveCount}` }}
            </div>
          </el-tooltip>

          <!-- start 客户关联查询字段 -->
          <div class="relation-product-list" v-if="value.customer && value.customer.length && relationCustomerfields.length !== 0">
            <div class="relation-product-list-item" v-for="(customer, index) in value.customer" :key="customer.id">
              <div class="product-name">{{ customer.label }}</div>
              <div class="form-row-two-columns product-relation-field-list">
                <div class="form-view-row" v-for="field in relationCustomerfields" :key="field.id">
                  <label>{{ field.displayName }}</label>
                  <div class="form-view-row-content"> {{ disposeFormItemViewTime(field, (value[field.fieldName] || '')) }} </div>
                </div>
              </div>
            </div>
          </div>
          <!-- end 客户关联查询字段 -->
        </form-item>
        <!-- end 客户 -->

        <!-- start 联系人 -->
        <form-item v-if="customerOption.linkman" class="bbx-form-cell-item" :label="$t('common.base.contact')">
          <div class="input-and-btn input-btn-change">
            <biz-remote-select
              :key="JSON.stringify(value.linkman)"
              ref="linkman"
              :placeholder="$t('task.tip.searchTip', {name: $t('common.base.contact')})"
              v-model="value.linkman"
              :cleared="true"
              :remote-method="searchLinkmanOuterHandler"
              :input-disabled="isCreateCustomer"
              @input="updateLinkman(value.linkman[0])"
              :computed-width-keys="['name']"
            >
              <div class="customer-template-option task-ptb5" slot="option" slot-scope="{ option }">
                <p>{{ option.label }}</p>
              </div>
            </biz-remote-select>
            <el-button type="text" icon="el-icon-circle-plus-outline" @click="dialogOpen('contact')" v-if="!isCreateCustomer && !justGuide && allowCreateLinkman">{{$t('common.base.create')}}</el-button>
          </div>
        </form-item>
        <!-- end 联系人 -->

        <!-- start 地址 -->
        <form-item v-if="customerOption.address" class="bbx-form-cell-item" :label="$t('common.base.address')">
          <div class="input-and-btn input-btn-change">
            <biz-remote-select
              ref="bizRemoteSelectAddress"
              :value="value.address"
              @input="updateAddress"
              :placeholder="$t('task.tip.searchTip', {name: $t('common.base.address')})"
              :cleared="true"
              :remote-method="searchAddressOuterHandler"
              :computed-width-keys="['address']"
              :mounted-search="true"
            >
            </biz-remote-select>
            <el-button type="text" icon="el-icon-circle-plus-outline" @click="dialogOpen('address')" v-if="!isCreateCustomer && !justGuide && allowCreateCustomer">{{$t('common.base.create')}}</el-button>
          </div>
        </form-item>
        <!-- end 地址 -->

        <!-- start 产品 -->
        <form-item v-if="customerOption.product" class="bbx-form-cell-item" :label="$t('common.base.product')" :validation="validation.product">

          <div class="input-and-btn input-btn-change">
            <biz-remote-select
              ref="product"
              :field="productField"
              :is-show-select="true"
              v-model="value.product"
              :remote-method="searchProductOuterHandler"
              @input="updateProductForProductSelect"
              :placeholder="$t('task.tip.searchTip', {name: $t('common.base.product')})"
              multiple
              :computed-width-keys="['name', 'serialNumber']"
              :keyword-length-limit="true"
              cleared
            >
              <div class="product-template-option" slot="option" slot-scope="{ option }">
                <h3>{{ option.name }}</h3>
                <p>
                  <span>
                    <label>{{$t('common.form.type.productNo')}}：</label>
                    <span>{{ option.serialNumber }}</span>
                  </span>
                  <span>
                    <label>{{$t('common.base.productType')}}：</label>
                    <span>{{ option.type }}</span>
                  </span>
                  <span v-if="showProductLm">
                    <label>{{$t('common.base.contact')}}：</label>
                    <span>{{ option.linkman && option.linkman.name }}</span>
                  </span>
                  <span>
                    <label>{{$t('common.base.customer')}}：</label>
                    <span v-if="option.customer && option.customer.id">{{option.customer.name}}</span>
                    <span class="customer-unbind-name" v-else>{{$t('common.base.notContact')}}</span>
                  </span>
                  <!-- 产品表单移动端展示字段 -->
                  <span v-for="item in productAppShowFields" :key="item.fieldName">
                    <label>{{ item.displayName }}：</label>
                    <!-- 自定义人员 -->
                    <template v-if="item.formType === 'user' && option.attribute[item.fieldName]">
                      <template v-if="isOpenData">
                        <template v-if="Array.isArray(option.attribute[item.fieldName])">
                          <template v-for="(userItem, index) in option.attribute[item.fieldName]">
                            <open-data :key="index" type='userName' :openid="userItem.staffId"></open-data>{{index + 1 === option.attribute[item.fieldName].length ? '' : ','}}
                          </template>
                        </template>
                        <template v-else>
                          <open-data type='userName' :openid="option.attribute[item.fieldName] && option.attribute[item.fieldName].staffId"></open-data>
                        </template>
                      </template>
                      <template v-else>
                        {{ $formatFormField(item, option) }}
                      </template>
                    </template>
                    <!-- 其它字段 -->
                    <template v-else>
                      <span>{{ $formatFormField(item, option) }}</span>
                    </template>
                  </span>
                </p>
                <p v-if="showProductAdd && option.containsProductCompleteAddress">
                  <span>
                    <label>{{$t('common.form.type.productCompleteAddress')}}：</label>
                    <span>{{ option.productCompleteAdd }}</span>
                  </span>
                </p>         
              </div>
            </biz-remote-select>
            <el-button type="text" icon="el-icon-circle-plus-outline" v-if="!justGuide && allowCreateProduct" @click="dialogOpen('product')">{{$t('common.base.create')}}</el-button>
          </div>

          <div v-if="isQualityDialog || isFaultLibraryDialog" class="max-warning-tip">*{{$t('task.tip.editTip14')}}</div>
          <div v-if="isAllowQualityIn" class="max-warning-tip">*{{$t('task.tip.editTip15')}}</div>
          <el-tooltip v-if="isShowProductRelevanceTaskCountButton || productArchiveCount" placement="top">
            <div slot="content" v-html="$t('task.detail.components.unfinishedAndAllTask', {unfinished: productRelevanceTaskCountData.unfinished, all: productRelevanceTaskCountData.all + productArchiveCount})"></div>
            <div class="task-count-button" @click="openProductView">
              {{ `${productRelevanceTaskCountData.unfinished}/${productRelevanceTaskCountData.all + productArchiveCount}` }}
            </div>
          </el-tooltip>

          <!-- start 产品关联查询字段 -->
          <div class="relation-product-list" v-if="value.product && value.product.length && relationProductfields.length">
            <div class="relation-product-list-item" v-for="(product, index) in value.product" :key="product.id">
              <div class="product-name">{{ product.label }}</div>
              <div class="form-row-two-columns product-relation-field-list">
                <div class="form-view-row" v-for="field in relationProductfields" :key="field.id">
                  <label>{{ field.displayName }}</label>
                  <!-- 多级菜单多选 -->
                  <div
                    v-if="(field.setting || {}).formType === 'cascader' && (field.setting || {}).isMulti"
                    class="form-view-row-content">
                    {{ (value[field.fieldName] || [])[index] | fmt_form_cascader((field.setting || {}).isMulti, (field.setting || {}).displayMode) }}
                  </div>
                  <div class="form-view-row-content" v-else> {{ disposeFormItemViewTime(field, (value[field.fieldName] || [])[index]) }} </div>
                </div>
              </div>
            </div>
          </div>
          <!-- end 产品关联查询字段 -->

        </form-item>
        <!-- end 产品 -->

      </template>
      <!-- end 客户字段 -->

      <!-- start 质保信息 -->
      <template slot="quality" slot-scope="{ field, value }">
        <!-- start 质保开始时间 -->
        <form-item class="bbx-form-cell-item" :label="$t('common.form.preview.qualityField.label1')">
          <!-- <el-date-picker
            v-model="qualityStartTime"
            @change="updateStartTime"
            type="datetime"
            prefix-icon="iconfont icon-fd-date"
            clearable
            :placeholder="$t('task.edit.importedByProduct')"
            value-format="timestamp"
            format="yyyy-MM-dd"
            disabled
          >
          </el-date-picker> -->
          <form-date 
            :field="field" 
            :value="qualityStartTime"  
            :placeholder="$t('task.edit.importedByProduct')" 
            @nativeChange="updateStartTime"
            prefix-icon="iconfont icon-fd-date"
            native-disabled />
        </form-item>
        <!-- end 质保开始时间 -->

        <!-- start 质保结束时间 -->
        <form-item class="bbx-form-cell-item" :label="$t('common.form.preview.qualityField.label2')">
          <!-- <el-date-picker
            v-model="qualityEndTime"
            @change="updateEndTime"
            type="datetime"
            prefix-icon="iconfont icon-fd-date"
            clearable
            :placeholder="$t('task.edit.importedByProduct')"
            value-format="timestamp"
            format="yyyy-MM-dd"
            disabled
          >
          </el-date-picker> -->
          <form-date 
            :field="field" 
            :value="qualityEndTime"  
            :placeholder="$t('task.edit.importedByProduct')"
            @nativeChange="updateEndTime"
            prefix-icon="iconfont icon-fd-date"
            native-disabled />
        </form-item>
        <!-- start 质保结束时间 -->

        <!-- start 质保状态 -->
        <form-item class="bbx-form-cell-item" :label="$t('common.form.preview.qualityField.label3')">
          <el-select v-model="qualityStatus" disabled :placeholder="$t('task.edit.importedByProduct')" @change="updateQuality">
            <el-option :label="$t('common.base.warranty')" value="IN"></el-option>
            <el-option :label="$t('common.base.surrender')" value="OUT"></el-option>
          </el-select>
        </form-item>
        <!-- end 质保状态 -->
      </template>
      <!-- end 质保信息 -->

      <!-- 客户产品关联字段跟人有关的 -->
      <template slot="relationCustomer" slot-scope="{ field, value }">
        <form-item class="bbx-form-cell-item" :label="field.displayName">
          <template v-if="isOpenData && value">
            <template v-if="field.setting.fieldName === 'customerManager'">
              <div class="form-relation-text">
                <open-data type="userName" :openid="value"></open-data>
              </div>
            </template>
            <template v-if="field.setting.formType === 'user'">
              <!-- 这里人可能是多选 -->
              <div class="form-relation-text">
                <open-data v-for="staffId in value.split(',')" :key="staffId" type="userName" :openid="staffId"></open-data>
              </div>
            </template>
          </template>
          <template v-else>
            <form-relation :value="value"></form-relation>
          </template>
        </form-item>
      </template>

      <template slot="relationProduct" slot-scope="{ field, value }">
        <form-item class="bbx-form-cell-item" :label="field.displayName">
          <template v-if="isOpenData && value">
            <template v-if="field.setting.fieldName === 'customerManager'">
              <div class="form-relation-text">
                <open-data type="userName" :openid="value"></open-data>
              </div>
            </template>
            <template v-if="field.setting.formType === 'user'">
              <!-- 这里人可能是多选 -->
              <div class="form-relation-text">
                <open-data v-for="staffId in value.split(',')" :key="staffId" type="userName" :openid="staffId"></open-data>
              </div>
            </template>
          </template>
          <template v-else>
            <form-relation :value="value"></form-relation>
          </template>
        </form-item>
      </template>

      <!-- start 故障库 -->
      <template slot="faultLibrary" slot-scope="{ field, value }">
        <form-item v-if="faultLibraryOption.productType" class="bbx-form-cell-item" :label="$t('common.base.productType')">
          <el-cascader
            style="width: 100%"
            :options="catalogTree" 
            :props="productTypeProps"
            :value="value.productTypeId" 
            :placeholder="$t('common.placeholder.selectSomething', {0: $t('common.base.productType')})"
            :disabled="isProductTypeDisabled"
            @input="updateCatalogInput">
            <template slot-scope="{ data }" class="type">
              <span> 
                {{ data.name }}
                <!-- 临时解决图标不显示方法 -->
                <!-- <template v-if="data.tasks">
                  <i class="iconfont icon-arrowright"> </i>
                </template> -->
              </span>
            </template>
          </el-cascader>
        </form-item>
        <form-item v-if="faultLibraryOption.faultScene" class="bbx-form-cell-item" :label="$t('common.form.preview.faultLibrary.concatLabel2')">
          <biz-remote-select
            :value="value.faultSceneList"
            :field="field"
            :filterable="false"
            :remote-method="searchFaultScene"
            @input="updateFaultScene"
            :placeholder="$t('common.placeholder.selectSomething', {0: $t('common.form.preview.faultLibrary.concatLabel2')})"
            :input-disabled="isFaultSceneDisabled"
            :computed-width-keys="['title']"
          >
          </biz-remote-select>
        </form-item>
        <form-item v-if="faultLibraryOption.faultDetails" class="bbx-form-item-i" :label="$t('common.form.preview.faultLibrary.concatLabel3')">
          <form-textarea :field="field" v-model="value.faultDetails" :disabled="isFaultLibraryDisabled" :placeholder="$t('common.placeholder.inputSomething', {data1: $t('common.form.preview.faultLibrary.concatLabel3')})" />
        </form-item>
        <form-item class="bbx-form-cell-item" v-if="faultLibraryOption.faultReason && showFaultReason" :label="$t('common.form.preview.faultLibrary.concatLabel4')">
          <el-input v-model="value.faultReason" :disabled="true" placeholder="" />
        </form-item>
        <form-item class="bbx-form-cell-item" v-if="faultLibraryOption.budgetAmount && showBudgetAmount" :label="$t('common.form.preview.faultLibrary.concatLabel6')">
          <el-input v-model="value.budgetAmount" :disabled="true" placeholder="" />
        </form-item>
      </template>
      <!-- end 故障库 -->

    </form-builder>

    <!-- start 新建客户弹窗 -->
    <base-modal :title="$t('task.edit.createSth', {name: $t('common.base.customer')})" :show.sync="addCustomerDialog" class="add-dialog-container" width="800px" @closed="dislogClose('customer')">
      <div id="createCustomerView"></div>
      <div class="dialog-footer" slot="footer">
        <el-button @click="addCustomerDialog = false">{{$t('common.base.close')}}</el-button>
        <el-button type="primary" @click="addCustomerSubmit">{{$t('common.base.save')}}</el-button>
      </div>
    </base-modal>
    <!-- end 新建客户弹窗 -->

    <!-- start 新建产品弹窗 -->
    <base-modal :title="$t('task.edit.createSth', {name: $t('common.base.product')})" :show.sync="addProductDialog" class="add-dialog-container" width="800px" @closed="dislogClose('product')">
      <div id="createProductView"></div>
      <div class="dialog-footer" slot="footer">
        <el-button @click="addProductDialog = false">{{$t('common.base.close')}}</el-button>
        <el-button type="primary" @click="addProductSubmit">{{$t('common.base.save')}}</el-button>
      </div>
    </base-modal>
    <!-- end 新建产品弹窗 -->

    <!-- start 联系人弹窗 -->
    <edit-contact-dialog ref="EditContactDialog" :customer="convertCustomerOfSelect(selectedCustomer)" :is-phone-unique="customerInitData.isPhoneUnique"/>
    <!-- end 联系人弹窗 -->

    <!-- start 地址弹窗 -->
    <edit-address-dialog ref="EditAddressDialog" :customer-id="selectedCustomer.id || selectedCustomer.value" :default-address="customerInitData.customerAddress"/>
    <!-- end 地址弹窗 -->

    <!-- start 提示弹窗 -->
    <base-modal :title="$t('common.base.toast')" :show.sync="visible" width="500px" class="batch-warning-dialog">
      <div class="subtext">{{ dialogSubtext }}</div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="visible = false">{{$t('common.base.makeSure')}}</el-button>
      </div>
    </base-modal>

    <wiki-recommend
      v-if="wikiTaskFunction && !agendaTask"
      :keywords="wikiKeywords"
      :tips.sync="showFaultLibraryTips"
      :wiki-id="faultLibraryWikiId">
    </wiki-recommend>
  </div>
</template>

<script>
import TaskEditForm from './TaskEditForm';
export default TaskEditForm;
</script>

<style lang="scss">
  @import './TaskEditForm.scss';
  .form-relation-text {
    background: #F5F5F5;
    padding: 3px 10px;
    line-height: 24px;
    min-height: 32px;
    border: 1px solid #D9D9D9;
    border-radius: 4px;
    margin: 0;
    outline: none;
    color: #BFBFBF !important;
  }
  .form-builder {
    --input-btn-max-width: 77px;
    --input-btn-margin-left: 10px;
    --input-right: 10px;
    --input-icon-width: 25px;
    --input-icon-right: calc(var(--input-btn-max-width) + var(--input-right));
    --input-padd-right: calc(var(--input-icon-right) + var(--input-icon-width));
    --input-tag-right: var(--input-padd-right);
    .input-btn-change {
      .biz-form-remote-select {
        input {
          padding-right: var(--input-padd-right);
        }
        .el-input__suffix {
          right: var(--input-icon-right);
        }
        // 产品选中的时候关闭按钮位置会偏移，去掉固定宽度，让其自适应
        .el-select__tags {
          width: calc(100% - var(--input-padd-right))!important;
          .el-select__input {
            padding-right: 0;
          }
        }
      }
      &+.el-tooltip {
        right: var(--input-tag-right)
      }
      position: relative;
      button {
        position: absolute;
        right: var(--input-right);
        top: 50%;
        transform: translateY(-50%);
        max-width: var(--input-btn-max-width);
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
</style>
