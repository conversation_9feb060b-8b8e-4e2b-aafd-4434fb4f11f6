/* components */
import EditAddressDialog from '@src/modules/customer/view/operationDialog/EditAddressDialog.vue'
import EditContactDialog from '@src/modules/customer/view/operationDialog/EditContactDialog.vue'
import BbxTimezoneTimePicker from '@src/component/Bbx/TimezoneTimePicker'

import WikiRecommend from '@src/modules/task/components/WikiRecommend'
/* util */
import _ from 'lodash'
import * as FormUtil from '@src/component/form/util'
import { uuid, getPlanEndTime, parseDateTime, getTimestamp, useFormTimezone, formatAddress, isEmpty as pubIsEmpty} from 'pub-bbx-utils'
import { findComponentDownward } from '@src/util/assist'
import { getFieldValue2string } from '@service/TaskService.ts'
import ObjectUtil from '@src/util/lang/object.ts';
import Filter from '@src/filter/filter.js';
import { isEmpty } from '@src/util/type';
import { getCount } from '@src/api/ArchiveApi'
import { getConfig } from '@src/api/TaskApi.ts';
import { safeNewDate } from '@src/util/time';
import i18n from '@src/locales'

import { 
  customerAddressSelectConversion,
  linkmanSelectConversion,
  taskTypeSelectConversion
} from '@src/util/conversionFunctionUtil.ts'
/* Vue */
import props from './props'
import data from './data'
import computed from './computed'
import methods from './methods'
/* enum */
import { TaskFieldNameMappingEnum } from '@model/enum/FieldMappingEnum.ts'
import { QualityInfoStateValueEnum } from '@model/enum/QualityInfoEnum.ts'
/* constant */
import { 
  TASK_PRODUCT_LINKMAN_AND_ADDRESS_NOT_EQUAL_MESSAGE,
  TASK_PRODUCT_LINKMAN_NOT_EQUAL_MESSAGE,
  TASK_PRODUCT_ADDRESS_NOT_EQUAL_MESSAGE,
  REQUIRES_PRODUCT_MESSAGE,
  PLAN_TIME_NOT_LESS_THEN_NOW_MEESSAGE,
  PLAN_START_TIME_NOT_LESS_THEN_NOW_MEESSAGE,
  PLAN_END_TIME_NOT_LESS_THEN_NOW_MEESSAGE,
  ONLY_SELECT_INQUALITY,
  ONLY_SELECT_ONE_QUALITY
} from '@src/model/const/Alert.ts'
// 关联项类型
const RELATION_TYPE_MAP = {
  customer: 'relationCustomer',
  product: 'relationProduct'
}

import { taskFields } from '@src/modules/guideForNewUser/initData.js'
import { searchLinkman } from './methods/initData'

import { getWikiConfig } from '@src/api/SettingApi'
import {
  getProductFields
} from '@src/api/ProductApi'
/* service */
import { useQualityInfoComputedRuleState } from '@hooks/useQualityInfo.ts'
import moment from 'moment'
import DateFormatEnum from '@model/enum/DateFormatEnum'
/* label */
import { useLabelRelate } from '@hooks/intlabel/useLabelRelate.ts'

import { openAccurateTab } from '@src/util/platform'
import { PageRoutesTypeEnum } from 'pub-bbx-global/pageType/dist/enum/PageRoutesEnum'

const { disposeFormItemViewTime } = useFormTimezone()

const { fieldIntLabel } = useLabelRelate()

export default {
  name: 'task-edit-form',
  inject: ['initData'],
  props,
  data() {
    data.validation = this.buildValidation();
    data.planTimeDatePickerOptions = {
      disabledDate(time) {
        return time.getTime() < safeNewDate(safeNewDate().toLocaleDateString()).getTime()
      }
    }
    data.customerArchiveCount = 0; // 客户关联归档工单数量
    data.productArchiveCount = 0; // 产品关联归档工单数量
    data.visible = false
    data.dialogSubtext = ''
    data.isQualityDialog = false
    data.isAllowQualityIn = false
    data.qualityField = {}
    data.quality = {}

    data.wikiKeywords = []

    data.wikiTaskFunction = false
    
    data.relationWikiSetting = []
    data.intelligentConfig = {}

    data.customerDefaulInfo = {}
    data.isInFirst = 0
    
    data.formBuilderKey = uuid()
    data.showProductField = true;
    data.productAppShowFields = []; // 产品表单移动端展示字段
    data.firstSelctProduct = false

    return data
  },
  computed,
  created () {
    this.init();
    this.taskValue = this.value;
  },
  mounted () {
    this.taskFields = this.fields
    // this.taskValue = this.value;
    this.selectedType = taskTypeSelectConversion(this?.initData?.defaultType) || this.taskTypes[0] || {};
    if (this.isEdit) {
      this.selectedType = Object.freeze({
        label: this?.initData.task.templateName || '',
        name: this?.initData.task.templateId || '',
        value: this?.initData.task.templateId || ''
      })
    }
    this.$emit('updatetemplateId', this.selectedType);
    this.getConfig(this.selectedType?.value || '')
    this.getProductFields();

    this.getWikiConfig()

    // 获取产品类型
    this.fetchProductTypeTree()

    // 初始化故障库
    if(this.hasFaultLibraryField){
      this.initFaultLibrary()
    }

    this.$eventBus.$on('task_create_or_edit.update_linkman', this.updateLinkman);
    this.$eventBus.$on('task_create_or_edit.update_address', this.addAddressCallback);
  },
  beforeDestroy() {
    this.$eventBus.$off('task_create_or_edit.update_linkman', this.updateLinkman);
    this.$eventBus.$off('task_create_or_edit.update_address', this.addAddressCallback);
  },
  methods: {
    ...methods.fetch,
    ...methods.render,
    ...methods.search,
    ...methods.update,
    disposeFormItemViewTime,
    formatAddress,
    fieldIntLabel,

    // 后面有需求再放开
    /*getCurrentTimeRange(val) {
      const time = this.taskValue?.[val] ?? ''
      let startTime = '00:00:00'
      const endTime = '23:59:59';

      if(time && moment().isSame(time, 'day')) {
        const currentTime = safeNewDate();
        const hours = currentTime.getHours();
        const minutes = currentTime.getMinutes();
        const seconds = currentTime.getSeconds();
        // 构建可选择范围的字符串
        startTime = `${hours}:${minutes}:${seconds}`;
      }

      return `${startTime} - ${endTime}`;
    },*/
    planTimeChange({ field, newValue, oldValue }) {
      let { fieldName, formType } = field;
      // if(this.agendaTask) {
      let isDateTimeType = field?.setting?.dateType == 'date';
      if(isDateTimeType) {
        newValue = getTimestamp(newValue)
      }
      let planTime = parseDateTime(newValue).getTime();
      let nowTime = safeNewDate().getTime();
      let tip = formType == 'planStartTime' ? PLAN_START_TIME_NOT_LESS_THEN_NOW_MEESSAGE : PLAN_END_TIME_NOT_LESS_THEN_NOW_MEESSAGE
      
      // 是否为年月日类型
      const dateType = field?.setting?.dateType || '';
      const isDateType = dateType.toLowerCase() == 'yyyy-mm-dd';
      let isToday = false
      
      if (isDateType) {
        const today = moment().format(DateFormatEnum.YMD_UP)
        const planTime = moment(newValue).format(DateFormatEnum.YMD_UP)
        isToday = (today === planTime)
      }
      
      if (planTime < nowTime && !isToday) {
        this.taskValue[fieldName] = oldValue
        this.$emit('update:value', this.taskValue);
        return this.$platform.notification({
          title: tip,
          type: 'error'
        });
      }
      
      if(this.taskValue.planEndTime && this.taskValue.planStartTime) {
        let sTime = safeNewDate(this.taskValue.planStartTime).getTime();
        let eTime = safeNewDate(this.taskValue.planEndTime).getTime();
        if((sTime >= eTime)){
          this.$emit('update:value', this.taskValue);
          this.taskValue[fieldName] = oldValue;
          return this.$platform.notification({
            title: i18n.t('task.tip.planTimeTip6'),
            type: 'error'
          });
        }
      }
      if(fieldName === 'planStartTime') {
        if(safeNewDate(newValue).getTime() > safeNewDate(this.taskValue.planEndTime).getTime()) {
          this.taskValue.planStartTime = newValue;
          this.taskValue.planEndTime = getPlanEndTime(this.planTimeConfig, newValue);
          this.$emit('update:value', this.taskValue);
          this.$eventBus.$emit('agenda-task-plan-end-time', this.taskValue.planEndTime)
        }
        this.$eventBus.$emit('agenda-task-plan-start-time', newValue)
      } else if(fieldName === 'planEndTime') {
        if(safeNewDate(newValue).getTime() < safeNewDate(this.taskValue.planStartTime).getTime()) {
          this.taskValue[fieldName] = oldValue
          this.$emit('update:value', this.taskValue);
          return this.$platform.notification({
            title: i18n.t('task.tip.planTimeTip7'),
            type: 'error'
          });
        }
        this.$eventBus.$emit('agenda-task-plan-end-time', newValue)
      }
      // }
    },
    // 获取归档工单数量
    async getArchiveCount(id, mode){
      let params = mode === 'customer' ? { customerId: id } : { productId: id }
      const {code, message, result} = await getCount(params);

      if (code !== 0) {
        this.$notify({
          title: i18n.t('common.base.fail'),
          message,
          type: 'error'
        });
      }

      return result?.archiveCount;
    },
    // 获取产品联系人、地址显示状态
    async getProductFields(){
      try{
        let {status,message,data}=await getProductFields({isFromSetting:false});
        if(status === 0){
          this.productAppShowFields = (data || []).filter(item => item.isAppShow)
          const customerItem=data.find(item=>item.formType==='customer');
          if(customerItem){
            this.showProductLm=customerItem.setting.customerOption.linkman;
            this.showProductAdd=customerItem.setting.customerOption.address;
          }
        }else{
          this.$platform.alert(message);
        }
      }catch(err){
        console.error(err);
      }
    },
    /** 
     * @description 添加客户 提交
    */
    addCustomerSubmit() {
      // 提交
      this.customerFormDom.submit(data => {
        // 新建客户时，去掉禁用
        // this.isCreateCustomer = true;
        
        // 绑定客户
        this.updateCustomerValue([{
          label: data.name,
          value: data.id,
          id: data.id
        }])
        
        // 绑定联系人
        this.bindLinkman({
          id: data.lmId,
          name: data.lmName,
          phone: data.lmPhone
        })
        
        // 绑定地址
        data.addressId && this.bindAddress({
          id: data.addressId,
          country: data.country,
          province: data.province,
          city: data.city,
          dist: data.dist,
          address: data.address,
          latitude: data.latitude,
          longitude: data.longitude,
        })

        // 更新产品数据
        this.updateProductValue([]);
        
        // 查询客户关联字段
        this.relationFieldSelectHandler();
        // 关闭弹窗
        this.addCustomerDialog = false;
        
      });
    },
    /** 
     * @description 添加产品 提交
    */
    addProductSubmit() {
      let selectedCustomer = this.selectedCustomer
      let {linkman, address} = this.customerDefaulInfo
      let customer = {
        ...selectedCustomer,
        id: selectedCustomer.id || selectedCustomer.value,
        linkman: linkman,
        address: address
      }
      // 提交
      this.productFormDom.submit(customer, data => {

        let productArr = this.value.product?.length ? _.cloneDeep(this.value.product) : [];
        productArr.push({
          value: data.productId,
          label: data.productName,
          id: data.productId,
          name: data.productName,
          customerId:customer.id,
          ...data
        })
        // 绑定产品
        this.updateProductValue(productArr);
        // 更新产品信息
        this.updateProduct(productArr, {
          isForceUpdateCustomer: false,
          isUpdateCustomerProduct: true,
          isSilentUpdateLinkmanAndAddress: true
        });

        // 新建产品后es查询会延迟1秒，这里延迟1秒再去查询
        setTimeout(() => {
          this.$refs.product.outsideSearch('', {
            customerId: customer.id || ''
          })
        }, 1000)
        
        // 关闭弹窗
        this.addProductDialog = false;
        
      });
    },
    buildValidation(){
      const that = this;
      return Object.freeze({
        product(value, field, changeStatus){
          changeStatus(true);
          let isProductRequired = that.customerOption?.productNotNull === true;
          let isSelectedProduct = Array.isArray(value) && value.length > 0;
          
          return new Promise((resolve, reject) => {
            changeStatus(false);
            let errorMessage = isSelectedProduct ? '' : REQUIRES_PRODUCT_MESSAGE;
            resolve(isProductRequired ? errorMessage : '')
          })
        },
        planTime(value, field, changeStatus){
          changeStatus(true);
          
          let isDateTimeType = field?.setting?.dateType == 'dateTime';
          let errorMessage = '';
          
          if(isDateTimeType) {
            let planTime = parseDateTime(value).getTime();
            let nowTime = safeNewDate().getTime();
            errorMessage = planTime < nowTime && that.isVilidatePlantime ? PLAN_TIME_NOT_LESS_THEN_NOW_MEESSAGE : '';
          }
          
          return new Promise((resolve, reject) => {
            changeStatus(false);
            resolve(errorMessage)
          })
        },
        planStartOrEndTime(value, field, changeStatus){
          changeStatus(true);
          let { formType } = field
          let isDateTimeType = field?.setting?.dateType == 'date';
          if(isDateTimeType) {
            value = getTimestamp(value)
          }
          let planTime = parseDateTime(value).getTime();
          let nowTime = safeNewDate().getTime();
          let tip = formType == 'planStartTime' ? PLAN_START_TIME_NOT_LESS_THEN_NOW_MEESSAGE : PLAN_END_TIME_NOT_LESS_THEN_NOW_MEESSAGE
          
          // 是否为年月日类型
          const dateType = field?.setting?.dateType || '';
          const isDateType = dateType.toLowerCase() == 'yyyy-mm-dd';
          let isToday = false
          
          if (isDateType) {
            const today = moment().format(DateFormatEnum.YMD_UP)
            const planTime = moment(newValue).format(DateFormatEnum.YMD_UP)
            isToday = (today === planTime)
          }
          
          let errorMessage = ((planTime < nowTime) && !isToday) ? tip : '';
          
          return new Promise((resolve, reject) => {
            changeStatus(false);
            resolve(errorMessage)
          })
        }
      });
    },
    /**
     * @description: 绑定地址select数据
     * @param {Object | null} address 地址信息
     * @return {void}
    */    
    bindAddressOptions(address) {
      if (isEmpty(address)) return
      
      this.customerAddressOptions = [customerAddressSelectConversion(address)]
      this.$nextTick(() => {
        this.bindAddress(address)
      })
    },
    /**
     * @description: 绑定联系人select数据
     * @param {Object | null} address 地址信息
     * @return {void}
    */    
    bindLinkmanOptions(linkman) {
      if (isEmpty(linkman)) return
      
      this.customerLinkmanOptions = [linkmanSelectConversion(linkman)]
      this.$nextTick(() => {
        this.bindLinkman(linkman)
      })
    },
     /** 
     * @description 新增地址成功后触发
     * @param {Object} address 地址数据
    */
    addAddressCallback(address = {}) {
      // 获取列表数据
      this.$refs.bizRemoteSelectAddress.search()
      this.bindAddress(address)
    },
    /** 
     * @description 绑定地址
     * @param {Object} address 地址数据
    */
    bindAddress(address = {}) {
      this.updateAddressValue([customerAddressSelectConversion(address)])
    },
    /** 
     * @description 绑定联系人
     * @param {Object} linkman 联系人数据
    */
    bindLinkman(linkman = {}) {
      this.updateLinkmanValue([linkmanSelectConversion(linkman)])
    },
    bindProductLinkmanAndAddress(product = {}) {
      let { linkman, address } = product;
      let linkmanId = linkman?.id || '';
      let addressId = address?.id || '';
      
      linkmanId && this.bindLinkman(linkman);
      addressId && this.bindAddress(address);
    },
    /** 
     * @description 选择工单类型
     * @param {String} templateId 工单类型id
    */
    async chooseTemplate(templateId) {
      // 切换类型时将之前选择的连接器清空
      this.$eventBus.$emit('connector-related-field-clear')
      // 切换类型时可选择
      this.isCreateCustomer = false;
      
      if(this.state.isCopyTask) {
        return this.copyTaskHandler(templateId);
      }
      if(this.state.isFromEvent) {
        return this.convertTaskHandler(templateId);
      }
      
      let loading = this.$loading();
      this.getConfig(templateId)
      try {
        if(this.justGuide) this.taskFields = taskFields;
        else this.taskFields = await this.fetchTaskTemplateFields({ typeId: templateId, tableName: 'task', isFromSetting: true, isShowRichText:true });
        // 过滤不能编辑的自定义字段 不展示(是否重复报修)
        this.taskFields = this.taskFields.filter(field => field.setting?.isEdit != 0)
        this.taskFields.unshift({
          ...this.fieldIntLabel()
        })
        let planStartTime = this.taskValue.planStartTime
        let planEndTime = this.taskValue.planEndTime
        this.handlePlantimeFields()
        this.taskValue = FormUtil.initialize(this.taskFields, { templateId });
        this.taskValue.planStartTime = planStartTime
        this.taskValue.planEndTime = planEndTime
        // 表单初始化
        this.$emit('update:value', this.taskValue);
        this.$emit('update:fields', this.taskFields);
        this.$emit('changeTemplateId', templateId);
        
        // 清空校验结果
        setTimeout(() => {
          this.$refs.form.$children.map(child => {
            if (child.$el.className == 'form-item err') {
              child.$el.dispatchEvent(new CustomEvent('form.clear.validate', {bubbles: false}));
            }
          })
        }, 0);
        
        // 更新工单类型数据
        this.selectedType = this.taskTypesMap[templateId];
        this.$emit('updatetemplateId', this.selectedType);
        
        // 切换工单类型重新渲染form-builder, 不然会有问题，关联bug19403， bug23122
        this.showProductField = false;
        this.$nextTick(() => {
          this.showProductField = true;
        })
      } catch (error) {
        console.error(error)
      }
      
      loading.close();
    },
    handlePlantimeFields(){
      // 日程新建工单需要将计划开始时间和结束时间字段调整到表单最前面
      if(this.planStartTime && this.planEndTime) {
        let newFields = []
        const planStartTimeField = this.taskFields.filter(field=>field.formType === 'planStartTime')
        const planEndTimeField = this.taskFields.filter(field=>field.formType === 'planEndTime')
        for (let index = 0; index < this.taskFields.length; index++) {
          const field = this.taskFields[index];
          if(field.formType != 'planStartTime' && field.formType != 'planEndTime') {
            newFields.push(field)
          }
        }
        this.taskFields = [...planStartTimeField, ...planEndTimeField, ...newFields]
      } 
    },
    // 获取工单类型中知识库关联字段配置
    getConfig(templateId) {
      getConfig({
        typeId: templateId
      }).then(res => {
        if(res.succ) {
          let data = res.data?.wikiSetting || []
          data = data.map(v => v.fieldName).filter(v => v)
          this.relationWikiSetting = data
          // 智能质检
          let intelligentConfig = res.data.intelligentConfig
          this.intelligentConfig = intelligentConfig
          // console.log(this.intelligentConfig)
        }
      }).catch(err => {
        console.error(err)
      })
    },
    
    // 获取知识库设置中的工单服务配置
    getWikiConfig() {
      getWikiConfig().then(res => {
        if(res.succ) {
          this.wikiTaskFunction = res.data.taskFunction
        }
      }).catch(err => {
        console.error(err)
      })
    },

    /** 
     * @description 复制工单处理
     * @param {String} templateId 工单类型id
    */
    copyTaskHandler(templateId = '') {
      if(!this.state.isCopyTask) return
      
      this.$emit('loading', true);
      
      let { taskId = '' } = this.urlParams;
      window.location.href = `${this.$resourcePrefix}/task/copyTask?taskId=${taskId}&newTaskTemplateId=${templateId}`
    },
    /**
     * @description 事件转工单处理
     * @param {String} templateId 工单类型id
    */
    convertTaskHandler(templateId = '') {
      if(!this.state.isFromEvent) return

      this.$emit('loading', true);

      let { eventId = '', flow = '' } = this.urlParams;
      window.location.href = `${this.$resourcePrefix}/event/convent2Task/jump?eventId=${eventId}&defaultTypeId=${templateId}&flow=${flow}`
    },
    convertCustomerOfSelect(customer = {}) {
      let customerCloneData = _.cloneDeep(customer)
      customerCloneData.id = (customer.id || customer.value || '')

      return customerCloneData
    },
    /**
     * @description 关闭弹窗
     * @param {String} 动作 customer/product 
    */
    dislogClose(action = '') {
      switch (action) {
      case 'customer': {
        this.customerFormDom.initFormData();
        this.customerFormDom.init = false;
        break;
      }
      case 'product': {
        this.productFormDom.initFormData();
        this.productFormDom.init = false;
        break;
      }
      default: {
        break;
      }
      }
    },
    /** 
     * @description 打开弹窗
     * @param {String} 动作 address/contact/customer/product 
    */
    async dialogOpen(action) {
      let { id, value } = this.selectedCustomer;
      let customerId = id || value;

      if (!customerId && action != TaskFieldNameMappingEnum.Customer) {
        this.$platform.alert(i18n.t('task.tip.pleaseChooseSth', {name: i18n.t('common.base.customer')}));
        return;
      }

      switch (action) {
      case 'address': {
        this.$refs.EditAddressDialog.openDialog();
        break;
      }
      case 'contact': {
        this.$refs.EditContactDialog.openDialog();
        break;
      }
      case 'customer': {
        this.openCustomerDialogHandler();
        break;
      }
      case 'product': {
        this.openProductDialogHandler();
        break;
      }
      default: {
        break;
      }
      }
    },
    /** 
     * @description 初始化
    */
    init() {
      // this.loading = true;

      // // 获取客户、产品数据
      // Promise.all([
      //   this.fetchCustomerData(),
      //   this.fetchProductData()
      // ])
      //   .then(res => {
      //     this.loading = false;
      //   })
      //   .catch(err => console.error('error', err));

    },
    judegeSelectTaskType(value) {
      return !value || this.state.isFromEvent;
    },
    /** 
     * @description 同时通知客户 checkbox变化
    */
    noticeCustomerCheckdChange(value) {
      this.updateTickValue(Number(value));
    },
    /** 
     * @description 打开客户详情
    */
    openCustomerView() {
      let fromId = window.frameElement.getAttribute('id');
      let { id, value } = this.selectedCustomer;
      let customerId = id || value;
      if(!customerId) return

      openAccurateTab({
        type: PageRoutesTypeEnum.PageCustomerView,
        key: customerId,
        params: 'noHistory=1',
        fromId
      });
    },
    /** 
     * @description 打开产品详情
    */
    openProductView() {
      let fromId = window.frameElement.getAttribute('id');
      let productId = this.selectProduct.id;
      if(!productId) return
      
      openAccurateTab({
        type: PageRoutesTypeEnum.PageProductView,
        key: productId,
        params: 'noHistory=1',
        fromId
      })
    },
    /** 
     * @description 打开客户弹窗处理
    */
    async openCustomerDialogHandler() {
      try {
        // 打开客户弹窗前，判断是否存在客户初始化数据
        let isHaveCustomerInitData = Object.keys(this.customerInitData) > 0;
        if(!isHaveCustomerInitData) {
          this.loading = true;
          await this.fetchCustomerData();
        }
        
        this.addCustomerDialog = true;
        this.customerFormDom.init = true;

      } catch (error) {
        this.$platform.alert(i18n.t('common.base.tip.pleaseTryAgainLater'));
        console.warn('openCustomerDialogHandler -> error', error)
      } finally {
        this.loading = false;
      }
    },
    /** 
     * @description 打开产品弹窗处理
    */
    async openProductDialogHandler() {
      try {
        // 打开产品弹窗前，判断是否存在产品初始化数据
        let isHaveProductInitData = Object.keys(this.productInitData) > 0;
        if(!isHaveProductInitData) {
          this.loading = true;
          await this.fetchProductData();
        }
        
        this.addProductDialog = true;
        this.productFormDom.init = true;
        
      } catch (error) {
        this.$platform.alert(i18n.t('common.base.tip.pleaseTryAgainLater'));
        console.warn('openProductDialogHandler -> error', error)
      } finally {
        this.loading = false;
      }
    },
    /** 
     * @description 产品关联联系人/地址处理
     * 判断选择的产品的 关联联系人/地址 是否 当前选择的的 联系人/地址相同，如果不相同，则提示，并替换
     * @param {Object} product 产品
    */
    async productBindLinkmanAndAddressHandler(product) {
      // 产品联系人
      let productLinkman = product.linkman || {};
      // 产品地址
      let productAddress = product.address || {};
      // 当前已选择的联系人地址
      let { linkman, address } = this.value;
      linkman = linkman[0] || {};
      address = address[0] || {};

      // 是否是相同的 联系人/地址
      let isSameLinkman = productLinkman.id === linkman.id || !productLinkman.id;
      // 地址需要判断是否 值也相同
      let isSameAddress = productAddress.id === address.id || !productAddress.id;
      let productAddressText = Filter.prettyAddress(productAddress)
      let addressText = Filter.prettyAddress(address)
      if (productAddressText && addressText && (productAddressText == addressText)) {
        isSameAddress = true
      }
      
      let confirm = false;
      
      // 联系人和地址都不相同
      if(!isSameLinkman && !isSameAddress) {
        confirm = await this.$platform.confirm(TASK_PRODUCT_LINKMAN_AND_ADDRESS_NOT_EQUAL_MESSAGE);
        if(!confirm) return
        
        this.bindLinkman(productLinkman);
        this.bindAddress(productAddress);
        
      }
      // 联系人不相同
      else if(!isSameLinkman) {
        confirm = await this.$platform.confirm(TASK_PRODUCT_LINKMAN_NOT_EQUAL_MESSAGE);
        if(!confirm) return
        
        this.bindLinkman(productLinkman);
        
      } 
      // 地址不相同
      else if(!isSameAddress) {
        confirm = await this.$platform.confirm(TASK_PRODUCT_ADDRESS_NOT_EQUAL_MESSAGE);
        if(!confirm) return
        
        this.bindAddress(productAddress);
      }
      
    },
    relationFieldsFilter(type = TaskFieldNameMappingEnum.Customer) {
      let relationFields = [];

      // 判断类型是否存在
      let types = [TaskFieldNameMappingEnum.Customer, TaskFieldNameMappingEnum.Product];
      if(types.indexOf(type) < 0) {
        console.warn(`Caused: relationFieldHandler params.type is not in ${types}`);
        return relationFields;
      }
      
      // 判断对应类型的关联显示项字段是否存在
      let formType = RELATION_TYPE_MAP[type];
      relationFields = this.taskFields.filter(field => field.formType == formType);
      if(relationFields.length <= 0) {
        console.warn(`Caused: this.taskFields not have ${formType} field`);
      }

      return relationFields;
    },
    /** 
     * @description 关联显示项字段清空
     * @param {string} type customer/product
    */
    async relationFieldClear(type = TaskFieldNameMappingEnum.Customer) {
      let relationFields = this.relationFieldsFilter(type);
      relationFields.forEach(relationField => { 
        this.update({ field: relationField, newValue: '' }) 
      })
    },
    /** 
     * @description 关联显示项字段选择处理
     * @param {string} type customer/product
    */
    async relationFieldSelectHandler(type = TaskFieldNameMappingEnum.Customer) {
      let relationFields = this.relationFieldsFilter(type)
      if (relationFields.length <= 0) return

      let productIds = [];
      if (Array.isArray(this.value.product) && this.value.product.length) {
        productIds = this.value.product.map(product => product.value);
      }
      
      try {
        let params = {
          customerId: this.selectedCustomer?.value || '',
          productIds
        }
        // fix:接口加载过慢时，导致保存工单时关联字段未赋值正确，需要加pending
        this.$emit('togglePending', true)
        // fix:29223, 新建产品后，后端数据同步没那么快，加个延迟
        await new Promise(resolve => setTimeout(resolve, 2000))
        let res = await this.fetchRelatedInfo(params);
        this.$emit('togglePending', false)
        let isSuccess = res.success;

        if (isSuccess) {
          let result = res.result || {};
          let { customerInfo, productInfo, relateCustomerFields, relateProductFields } = result;
          let isCustomerRelation = type == TaskFieldNameMappingEnum.Customer;

          this.relationFieldUpdateHandler(
            isCustomerRelation ? customerInfo : productInfo,
            relationFields,
            isCustomerRelation ? relateCustomerFields : relateProductFields,
            isCustomerRelation
          );

        } else {
          this.$platform.alert(res.message);
        }

      } catch (error) {
        console.warn('relationFieldSelectHandler -> error', error)
      }

    },
    /** 
     * @description 关联显示项字段更新处理
    */
    relationFieldUpdateHandler(info, relationFields = [], customerOrPorductFields = [], isCustomerRelation = true) {
      let fieldName = '';
      let formType = '';
      let fieldValue = '';

      relationFields.forEach(relationField => {
        fieldName = relationField?.setting?.fieldName;
        formType = relationField?.setting?.formType;
        
        if (isCustomerRelation) {
          // 多级菜单多选时保留原本的数据格式
          if (formType === 'cascader' && relationField?.setting.isMulti) {
            fieldValue = info.attribute?.[fieldName] || []
          } else {
            fieldValue = getFieldValue2string(info, fieldName, formType, customerOrPorductFields, isCustomerRelation) || '';
          }
        } else {
          if (Array.isArray(info)) {
            fieldValue = [];
            info.map(item => {
              // 多级菜单多选时保留原本的数据格式
              if (formType === 'cascader' && relationField?.setting.isMulti) {
                return fieldValue.push(item.attribute?.[fieldName] || [])
              }
              fieldValue.push(getFieldValue2string(item, fieldName, formType, customerOrPorductFields, isCustomerRelation) || '');
            })
          }
        }

        this.update({ field: relationField, newValue: fieldValue });
      })

    },
    /** 
     * @description 搜索地址 外层处理器
     * @param {Object} params 搜索参数
    */
    async searchAddressOuterHandler(params = {}) {
      let { id, value } = this.selectedCustomer;
      params.customerId = id || value;
      return this.searchAddress(params);
    },
    /** 
     * @description 搜索联系人 外层处理器
     * @param {Object} params 搜索参数
    */
    async searchLinkmanOuterHandler(params = {}) {
      let customerId = this.selectedCustomer?.value || '';
      params.customerId = this.selectedCustomer?.value || '';
      
      return customerId ? this.searchLinkman(params) : this.searchCustomerByPhone(params);
    },
    /** 
     * @description 搜索产品 外层处理器
     * @param {Object} params 搜索参数
    */
    async searchProductOuterHandler(params = {}) {
      // 精确搜索处理
      if(this.isPreciseProduct && !params.keyword) return

      let { id, value } = this.selectedCustomer;
      // 清空的时候清空customerId
      params.customerId = id || value || '';

      if(this.isShowRelationCustomer){
        params.isRelationCustomer = true;
      }

      if(this.value?.product?.filter(item=>item.customerId).length){
        params.customerId=this.value?.product?.filter(item=>item.customerId)[0].customerId
      }
      // 精确搜索
      if( this.isPreciseProduct ) {
        // 是否精确搜索
        let conditions = this.customerField?.setting?.preciseSearch?.conditions ?? []
        conditions.forEach(item=>{
          item.value = params.keyword || '';
        })

        // 服务商是否绕过权限
        const dataLevel = this.customerField?.setting?.preciseSearch?.dataLevel || ''
        if(!pubIsEmpty(dataLevel)) {
          params.dataLevel = dataLevel;
        }

        params.orConditions = conditions;
        params.keyword = '';
        params.customerId=''
      }
    
      return this.searchProduct(params);
    },
    /** 
     * @deprecated 旧的方法，已废弃
     * @description 选择客户关联
     * @param {String} customerId 客户id
    */
    selectCustomerRelation(customerId) {
      let forRelation = {
        module: 'customer',
        id: customerId
      };
      
      this.$eventBus.$emit('es.Relation.Customer', forRelation);
    },
    /** 
     * @description 更新客户信息
     * @param {Array<Object>} value 客户数据
     * @param {Object} options 配置
    */
    updateCustomerForCustomerSelect(value) {
      this.updateCustomer(value, {
        isForceUpdateCustomer: false,
        isUpdateCustomerProduct: true,
        isFromProductUpdate: false
      })
      if (value.length === 0) {
        // 如果产品为空 第一次选中firstSelctProduct为false
        this.firstSelctProduct = false;
      } else {
        this.firstSelctProduct = true;
      }

    },
    /** 
     * @description 更新客户信息
     * @param {Array<Object>} value 客户数据
     * @param {Object} options 配置
    */
    async updateCustomer(value = [], options = {
      isForceUpdateCustomer: false,
      isUpdateCustomerProduct: true,
      isFromProductUpdate: false
    }) {
      this.isInFirst++;

      let { isForceUpdateCustomer, isUpdateCustomerProduct, isFromProductUpdate } = options;
      let selectedCustomer = value?.[0] || {};
      let currentCustomerId = this.selectedCustomer?.id || this.selectedCustomer?.value;
      let selectedCustomerId = selectedCustomer?.id || selectedCustomer?.value || '';

       // isInFirst 只有初始化的时候才会传linkmanId参数，切换客户的时候不传
       let defaultLinkManId = (this.isInFirst > 1 ) ? '' : this.urlParams.linkmanId ;
      
      // 更新客户数据
      this.updateCustomerValue(value.slice());
      
      // 判断选中的客户是否与当前客户数据一致
      if(currentCustomerId == selectedCustomerId && !isForceUpdateCustomer) return
      
      try {
        this.$emit('togglePending', true)
        const result = await this.fetchTaskDefaultInfo({ customerId: selectedCustomerId, linkmanId: defaultLinkManId });
        this.$emit('togglePending', false)
        this.customerDefaulInfo = result || {}
        let { linkman, address } = result;
        /**
         * 1、如果是产品选择 触发的客户数据更新，则在此处不对 客户联系人和客户地址 进行处理 。
         * 2、让逻辑继续往下执行通过 productBindLinkmanAndAddressHandler 方法去比对产品和客户中的联系人和地址数据。
         * 3、通过执行 productBindLinkmanAndAddressHandler 方法，决定如何去更新客户数据(联系人和地址)。
         */
          // 如果不是从产品更新客户数据的
          if (!isFromProductUpdate) {
            // 重置联系人和地址
            this.updateLinkmanValue([]);
            this.updateAddressValue([]);
            // 绑定联系人和地址
            linkman && this.bindLinkman(linkman);
            address && this.bindAddress(address);
        }
        
        
        // 更新产品数据
        if (Array.isArray(this.value.product) && this.value.product.filter(item=>item.customerId).length && isUpdateCustomerProduct) {
          this.updateProductValue(
            this.value.product.filter(item => item.customerId == selectedCustomer.id)
          )
        }
        
        // 查询关联工单数量
        this.fetchCountForCreate({ module: TaskFieldNameMappingEnum.Customer, id: selectedCustomerId });
        // 获取归档工单数量
        this.getArchiveCount(selectedCustomerId, 'customer').then(res => {
          this.customerArchiveCount = res || 0
        });

        // 重置故障库
        if(this.hasFaultLibraryField) {
          this.resetFaultLibrary()
        }
        
      } catch (error) {
        console.warn('task-edit-form: updateCustomer -> error', error);
      }
        
      // 查询客户关联字段
      this.relationFieldSelectHandler();
    },
    /** 
     * @description 更新联系人信息
    */
    async updateLinkman(linkman = {}) {
      if (ObjectUtil.isEmpty(linkman)) {
        return console.warn('Caused: linkman is not object or is empty')
      }
      
      let isHaveCustomer = this.value.customer && this.value.customer.length;
      let linkmanCustomer = linkman?.customer || {};
      
      try {
        // 判断客户是否存在
        if (!isHaveCustomer) {
          // 客户不存在时则下拉框隐藏
          findComponentDownward(this.$refs.linkman, 'base-select')?.close();
        
          const customerData = [{
            label: linkmanCustomer.name,
            value: linkmanCustomer.id,
            id: linkmanCustomer.id
          }];
          
          // 设置客户数据
          this.updateCustomerValue(customerData);
          // 更新客户信息
          await this.updateCustomer(customerData);
        }
        
        // 绑定联系人数据
        this.bindLinkman(linkman);
        const addressResult = await this.fetchLmBindAddress({ lmId: linkman.id });
        // 如果存在地址信息则绑定地址
        addressResult.data.id && this.bindAddress(addressResult.data);
        
      } catch (error) {
        console.warn('task-edit-form: updateLinkman -> error', error);
      }
    
    },
    // 出现质保弹框的时候，手动关闭产品下拉框
    closeProductSelect() {
      // 需要使用$nextTick()方法来确保在选中选项后再获取下拉框实例，从而手动关闭下拉框。
      this.$nextTick(() => {
        this.$refs?.product?.$refs?.BaseRemoteSelect.blur();
      })
    },
    /** 
     * @description 更新产品信息
     * @param {Array[Product]} value 产品数据
     * @param {Object} options 配置
    */
    async updateProduct(value, options = {
      isFromProduct:false,
      isForceUpdateCustomer: false,
      isUpdateCustomerProduct: true,
      isSilentUpdateLinkmanAndAddress: false
    }) {
      let { isForceUpdateCustomer, isUpdateCustomerProduct, isSilentUpdateLinkmanAndAddress,isFromProduct } = options;
        //选择的产品未关联用户则不需要更新客户数据
      let product = isFromProduct?value[0]:this.value?.product.filter(item=>item.customerId)[0] || {};
      let isHaveCustomer = (this.value.customer && this.value.customer.length);
      try {
        // 判断客户是否存在
        if ((!isHaveCustomer || isForceUpdateCustomer) && product.value) {
          // 客户不存在时则下拉框隐藏
          const LinkmanSelectComponent = findComponentDownward(this.$refs.linkman, 'base-select')
          LinkmanSelectComponent?.close()
          
          this.$emit('togglePending', true)
          const result = await this.fetchCustomerByProduct({ id: product.value });
          this.$emit('togglePending', false)
            
          const customerData = [{
            label: result.customerName,
            value: result.customerId,
            id: result.customerId
          }];

          //选择的产品未关联用户则不需要更新客户数据
           // 设置客户数据
            this.updateCustomerValue(customerData);
            await this.updateCustomer(customerData, { isForceUpdateCustomer, isUpdateCustomerProduct, isFromProductUpdate: true });
        }
        
        // 故障库逻辑
        if(this.hasFaultLibraryField) {
          this.buildFaultLibraryFunction(value)
        }
        
        // 只允许选择一个产品，选择第二个产品时，则弹出提示
        let isQuality = this.taskFields.findIndex(field =>  field.formType == 'quality')
        // 计划任务，华大基因灰度可以选择多个产品，不做判断
        const isBgiSaasPlanTask = this.isBgiSaas && this.isPlanTaskPage
        if (!isBgiSaasPlanTask) {
          this.isQualityDialog = (value.length >= 2) && (isQuality > -1) 
          if(this.isQualityDialog) {
            this.dialogSubtext = ONLY_SELECT_ONE_QUALITY
            this.visible = true
            this.closeProductSelect()
          }
        }
        // 计划任务，华大基因灰度，多个产品时质保数据置空
        if(value.length == 0 || (value.length > 1 && isBgiSaasPlanTask)) {
          this.$set(this.value.quality, 'qualityStartTime', '')
          this.$set(this.value.quality, 'qualityEndTime', '')
          this.$set(this.value.quality, 'qualityStatus', '')
        }

        // 查询产品关联字段, 选一个产品才带入
        let isOnlyOneProduct = this.value?.product.filter(item=>item.customerId).length === 1;
        
        if(isOnlyOneProduct && isSilentUpdateLinkmanAndAddress) {
          // 静默绑定联系人/地址
          this.bindProductLinkmanAndAddress(product);
        }
        // 只有一个产品 且 客户存在
        else if (isOnlyOneProduct && isHaveCustomer) {
          // 产品关联联系人地址
          this.productBindLinkmanAndAddressHandler(product);
        }
        // 只有一个产品 且 客户不存在
        else if(isOnlyOneProduct && !isHaveCustomer) {
          this.bindProductLinkmanAndAddress(product);
        }

        this.productArchiveCount = 0;

        //未关联客户的产品
        let noRelatedProducts = (this.value?.product || []).filter(item => !item.customerId).length === 1;
        // 选择的产品
        let selectedProducts = isFromProduct ? value[0] : (this.value?.product || [])[0] || {};
        
        // 质保信息显示处理
        if (isOnlyOneProduct || noRelatedProducts) {
          if(isQuality) {
            this.qualityField = this.taskFields.filter(field => field.formType == 'quality')[0] || {}
            let defaultQuality = this.qualityField?.setting?.defaultQuality
            this.updateQualityInfo(selectedProducts, defaultQuality)
            // 选中产品时，判断①该产品是否为保内状态②该工单是否允许添加保外状态产品，如果①不是②不允许，则弹出提示
            this.isAllowQualityIn = this.value.quality.qualityStatus == 'OUT' && this.qualityField?.setting?.allowProduct == 0
            if(this.isAllowQualityIn) {
              this.dialogSubtext = ONLY_SELECT_INQUALITY
              this.visible = true
              this.closeProductSelect()
            }
          }
          // 查询关联工单数量
          this.fetchCountForCreate({ module: TaskFieldNameMappingEnum.Product, id: product.id });
          // 获取归档工单数量
          this.getArchiveCount(product.id, 'product').then(res => {
            this.productArchiveCount = res || 0
          });
        }

        // 产品关联字段数据
        this.relationFieldSelectHandler(TaskFieldNameMappingEnum.Product);


        // 第一次选中产品的时候过滤同客户的产品
        if(!this.firstSelctProduct && (this.value?.product.filter(item=>item.customerId).length===1 || !this.value?.product.length)){
          this.$nextTick(() => {
            let customerId=this.value?.product.filter(item=>item.customerId)[0]?.customerId?this.value?.product.filter(item=>item.customerId)[0]?.customerId:''
            this.$refs.product.outsideSearch('', {
              customerId
            })
          })
          // 第一次选中状态值
          this.firstSelctProduct = true
        }
      } catch (error) {
        console.warn('task-edit-form: updateProduct -> error', error);
      }
    },
    // 故障库逻辑
    buildFaultLibraryFunction(value) {

      if(!value.length) {
        // 重置故障库
        return this.resetFaultLibrary()
      }

      // 配置故障库 只允许选择一个产品
      this.isFaultLibraryDialog = this.hasFaultLibraryField && (value.length > 1)
      if(this.isFaultLibraryDialog) {
        this.dialogSubtext = ONLY_SELECT_ONE_QUALITY
        this.visible = true
        this.closeProductSelect()
      }
      // 当前选中产品无产品类型
      if(this.hasFaultLibraryField && value[0]?.id && !value[0]?.catalogId){
        this.noProductTypeId = true
        this.dialogSubtext = i18n.t('task.tip.editTip16')
        this.visible = true
        // 禁止编辑故障库
        this.setFaultLibraryDisabled(true)
        this.closeProductSelect()
      }else{
        // 允许编辑故障库
        this.setFaultLibraryDisabled(false)
      }
      // 获取当前产品的产品类型
      if(this.hasFaultLibraryField && value[0]?.catalogId) {
        this.isProductTypeDisabled = true
        this.fetchCurrentProductTypeTree(value[0].catalogId)
      }
      // 清空故障库
      this.clearFaultLibrary()
    },
    setFaultLibraryDisabled(value) {
      this.taskFields.forEach(field => {
        if(field.formType == 'faultLibrary'){
          this.$set(field, 'disabled', value)
        }            
      })
      this.isFaultSceneDisabled = value
      this.isFaultLibraryDisabled = value
      this.isProductTypeDisabled = value
    },
    updateProductForProductSelect(value = []) {
      if(value.length == 0) {
        this.isQualityDialog = false
        this.isAllowQualityIn = false
        this.isFaultLibraryDialog = false
        this.noProductTypeId = false
      }

      this.updateProduct(value, { isForceUpdateCustomer: true })
      this.$refs.product.$refs.BaseRemoteSelect.query='';
    },
    updateQualityInfo(value, defaultQuality) {
      this.value.quality = {}
      if(value.qualityStartTime) {
        this.$set(this.value.quality, 'qualityStartTime', getTimestamp(moment(value.qualityStartTime).format(DateFormatEnum.YMD_UP)) )
      }else {
        this.$set(this.value.quality, 'qualityStartTime',  '' )
      }
      if(value.qualityEndTime) {
        this.$set(this.value.quality, 'qualityEndTime', getTimestamp(moment(value.qualityEndTime).format(DateFormatEnum.YMD_UP)))
      }else {
        this.$set(this.value.quality, 'qualityEndTime',  '')
      }
      let qualityStatus = useQualityInfoComputedRuleState(value.qualityStartTime, value.qualityEndTime)
      qualityStatus = QualityInfoStateValueEnum[qualityStatus] || defaultQuality
      this.$set(this.value.quality, 'qualityStatus', qualityStatus)
      this.updateQualityInfoValue(this.value.quality)
    },
    /** 
     * @description 效验
    */
    validate() {
      return this.$refs.form.validate(false);
    },
    useQualityInfoComputeState(endTime) {
      if(!endTime) return ''
      endTime = safeNewDate(endTime).getTime();
      let nowTime = safeNewDate().getTime();
      return endTime < nowTime ? 'OUT' : 'IN'
    },
    /** 
     * @description 更新质保信息
    */
    updateQuality() {
      this.updateQualityInfoValue(this.value.quality)
    },
    updateStartTime() {
      this.updateQualityInfoValue(this.value.quality)
    },
    // 质保结束时间修改时判断产品是在保内还是保外
    updateEndTime(value) {
      let qualityStatus = this.useQualityInfoComputeState(value)
      this.$set(this.value.quality, 'qualityStatus', qualityStatus)
      this.updateQualityInfoValue(this.value.quality)
    },

    // 从表单的输入中获取标签
    getLabels: _.debounce(function() {
      let arr = []
      this.effectWikiFields.forEach(field => {
        let data = this.value[field.fieldName]
        if(!data) return
        // let tags = this.value[field.fieldName].match(/#.*? /g)
        // tags && tags.forEach(tag => {
        //   if(!arr.includes(tag)) arr.push(tag.slice(1, tag.length - 1))
        // })
        if(!arr.includes(data)) arr.push(data)
      })
      this.wikiKeywords = arr
    }, 500),
    // 更新客户地址
    updateAddress(value) {
      this.value.address = value || []
    },
    // 更新产品类型
    updateCatalogInput(value) {
      try {
        if(!value || !Array.isArray(value)) return

        this.isFaultSceneDisabled = false

        const productType = this.searchCatalogIds(this.catalogTree, value[value.length - 1])
        this.productTypeIds = [{
          id: value[value.length - 1],
          name: productType
        }]
        this.$set(this.value.faultLibrary, 'productTypeId', value)
        this.$set(this.value.faultLibrary, 'productType', productType)
        
        // 更新产品类型 清空故障现象
        if(this.hasFaultLibraryField){
          this.clearFaultLibrary()
        }
        
        this.updateFaultLibraryValue(this.value.faultLibrary)
      } catch (error) {
        console.error(error)
      }
    },
    initFaultLibrary() {
      try {
        if(this.taskValue?.product?.length > 0) {
          this.isProductTypeDisabled = true
          this.isFaultSceneDisabled = false
        }
        // 事件转工单 故障库产品类型回显
        if(this.state.isFromEvent && this.taskValue?.product?.length > 0 ) {
          this.buildFaultLibraryFunction(this.taskValue?.product)
        }
        
        const faultLibrary = this.taskValue?.faultLibrary
        if(faultLibrary) {
          let budgetAmount = this.buildBudgetAmount(faultLibrary.budgetAmountMax, faultLibrary.budgetAmountMin)
          this.$set(this.taskValue.faultLibrary, 'budgetAmount', budgetAmount)
        }
      } catch (error) {
        console.error(error)
      }
    },
    resetFaultLibrary() {
      this.$set(this.value.faultLibrary, 'productTypeId', [])
      this.$set(this.value.faultLibrary, 'productType', '')
      this.isFaultSceneDisabled = true
      this.isProductTypeDisabled = false
      this.clearFaultLibrary()
    },
    clearFaultLibrary() {
      this.showFaultReason = true
      this.showBudgetAmount = true
      const faultLibrary = this.value.faultLibrary
      this.$set(faultLibrary, 'faultReason', '')
      this.$set(faultLibrary, 'solution', '') // 解决方案
      this.$set(faultLibrary, 'budgetAmount', '')
      this.$set(faultLibrary, 'budgetAmountMax', '')
      this.$set(faultLibrary, 'budgetAmountMin', '')
      this.$set(faultLibrary, 'wikiId', '')
      this.$set(faultLibrary, 'faultSceneList', [])
      this.$set(faultLibrary, 'faultScene', '')
    },
    // 在产品类型选项中查找对应的值
    searchCatalogIds (catalogOption, catalogId) {
      try {
        for(let catalog of catalogOption) {
          if(catalog.tasks) {
            // 递归寻找值
            let res = this.searchCatalogIds(catalog.tasks, catalogId)
            if (res) {
              return res
            }
          } else if (catalog.id === catalogId) {
            // 找到值并返回
            return catalog.name
          }
        }
      } catch(error) {
        console.error(error)
      }
    },
    /** 
     * @description 故障现象
    */
    updateFaultScene(value) {
      try {
        if(!value || !Array.isArray(value)) return
        // 更新故障详情 原因 维修费用
        if(value[0].value == -1) {
          this.showFaultReason = false
          this.showBudgetAmount = false
        } else {
          this.showFaultReason = true
          this.showBudgetAmount = true
        }
        const content = value[0].content
        const faultReason = value[0].faultReason
        const wikiId = (`${value[0].value }`)
        const budgetAmountMax = value[0].repairCostEnd
        const budgetAmountMin = value[0].repairCostBegin
        const faultPerformance = value[0].faultPerformance || value[0].title


        let budgetAmount = this.buildBudgetAmount(budgetAmountMax, budgetAmountMin)
        if(wikiId && wikiId != -1) {
          // 跳转知识库
          this.showFaultLibraryTips = true
        }
        
        const faultLibrary = this.value.faultLibrary
        this.$set(faultLibrary, 'faultReason', faultReason)
        this.$set(faultLibrary, 'solution', content) // 解决方案
        this.$set(faultLibrary, 'budgetAmount', budgetAmount)
        this.$set(faultLibrary, 'budgetAmountMax', budgetAmountMax)
        this.$set(faultLibrary, 'budgetAmountMin', budgetAmountMin)
        this.$set(faultLibrary, 'wikiId', wikiId)
        this.$set(faultLibrary, 'faultSceneList', value)
        this.$set(faultLibrary, 'faultScene', faultPerformance)

        this.updateFaultLibraryValue(this.value.faultLibrary)
      } catch(error) {
        console.error(error)
      }
    },
    buildBudgetAmount(max, min) {
      let budgetAmount = ''
      if(min && max) {
        budgetAmount = `${min} ~ ${max}`
      } else if(min) {
        budgetAmount = min
      } else if(max) {
        budgetAmount = max
      }
      return budgetAmount
    },
    rerenderFormBuilder() {
      this.formBuilderKey = uuid()
    }
  },
  components: {
    BbxTimezoneTimePicker,
    [EditAddressDialog.name]: EditAddressDialog,
    [EditContactDialog.name]: EditContactDialog,
    [WikiRecommend.name]: WikiRecommend
  }
}
