<!-- 费用结算明细列表 -->
<template>
  <div class="amount-settlement-list">
    <div class="amount-settlement-list-header">
      <div class="total-amount">
        <span>{{ $t('common.smartSettlement.amountSettleDisplayNames.total') }}: {{ settlementAmountTotal }}</span>
        <span>{{ $t('common.smartSettlement.amountSettleDisplayNames.actualTotal') }}: {{ actualAmountTotal }}</span>
      </div>
    </div>
    <div class="amount-settlement-list-wrap">
      <!-- 表格Start -->
      <el-table
        v-loading="loading"
        ref="tableComponentRef"
        header-row-class-name="common-list-table-header__v2"
        class="bbx-normal-list-box"
        :data="dataList"
        stripe
        border
      >
        <template v-for="column in columns">
          <el-table-column
            :key="column.fieldName"
            :label="column.displayName"
            :prop="column.fieldName"
            :min-width="column.width || 160"
            :show-overflow-tooltip="column.showTooltip || false"
          >
            <template slot-scope="{ row }">
              <!-- 结算金额：在结算完成前，取结算金额的值，在结算完成后，取调整后结算金额的值； -->
              <template v-if="column.fieldName === 'settlementAmount'">
                {{ row.settlementStatus == 3 ? row.afterAdjustAmount : row.settlementAmount }}
              </template>
              <!-- 结算状态：未提交结算申请前，显示“结算池的结算状态”，在提交结算单后，显示结算单的状态  -->
              <template v-else-if="column.fieldName === 'settlementStatus'">
                {{ getStateText(row[column.fieldName]) }}
              </template>
              <!-- 结算编号 -->
              <template v-else-if="column.fieldName === 'settlementFormNo'">
                <!-- view-detail-btn-disabled -->
                <span :class="viewSettleFormAuth ? 'view-detail-btn' : ''" @click="openPassTab(row)">{{ row[column.fieldName] }}</span>
              </template>
              <template v-else> {{ row[column.fieldName] }}</template>
            </template>
          </el-table-column>
        </template>
        <template slot="empty">
          <BaseListForNoData  v-show="!loading" :notice-msg="$t('common.base.tip.noData')"/>
        </template>
      </el-table>
      <!-- 表格End -->
    </div>
    
  </div>
</template>

<script>
import { getSettlementPoolByModule, getFormAuth } from 'src/modules/smartSettlement/api/index.js'
import { t } from '@src/locales'
import { getRootWindowInitData } from '@src/util/window'
import { openAccurateTab } from '@src/util/platform'
import { PageRoutesTypeEnum } from 'pub-bbx-global/pageType/dist/enum/PageRoutesEnum'
import AuthUtil from '@src/util/auth';


export default {
  name: 'amount-settlement-list',
  props: {
    module: {
      type: String,
      default: ''
    },
    moduleSourceId: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      loading: false,
      dataList: [],
      settlementAmountTotal: 0,
      actualAmountTotal: 0,
      columns: [{
        fieldName: 'settlementItemName',
        displayName: t('common.smartSettlement.standardDisplayNames.settleItem'),
      }, {
        fieldName: 'settlementAmount',
        displayName: t('common.smartSettlement.amountSettleDisplayNames.amount'),
      }, {
        fieldName: 'settlementStatus',
        displayName: t('common.smartSettlement.amountSettleDisplayNames.state'),
      }, {
        fieldName: 'settlementFormNo',
        displayName: t('common.smartSettlement.amountSettleDisplayNames.serialNumber'),
      }],
      viewSettleFormAuth: false,
    }
  },
  computed: {
    auth() {
      const rootWindowInitData = getRootWindowInitData()
      return rootWindowInitData?.user?.auth || {}
    },
    // 结算单查看权限
    hasSettlePoolAuth() {
      return AuthUtil.hasAuth(this.auth, 'INTELLIGENT_SETTLE_POOL_VIEW')
    },
  },
  methods: {
    getViewAuth() {
      getFormAuth({templateBizId: 'c41a2071-7624-4b2a-b30a-c18ba1273ba3'}).then(res => {
        const authority = res?.data?.find(item => item.operateType == 1)?.authority || []
        if (authority.length) {
          const rootWindowInitData = getRootWindowInitData()
          const roles = rootWindowInitData?.user?.roles || []
          authority.forEach(auth => {
            roles.forEach(role => {
              if (auth.id == role.id) {
                this.viewSettleFormAuth = true
              }
            })
          })
        }
      })
    },
    async search() {
      try {
        this.loading = true;

        let params = {}
        if (this.module) {
          params.module = this.module
        }
        if (this.moduleSourceId) {
          params.moduleSourceId = this.moduleSourceId
        }

        const { data } = await getSettlementPoolByModule(params)
        this.dataList = data?.settlePoolList ?? []
        this.settlementAmountTotal = data?.settlementAmountTotal ?? 0
        this.actualAmountTotal = data?.actualAmountTotal ?? 0
      } catch (e) {
        console.error(e)
      } finally {
        this.loading = false;
      }
    },
    getStateText(state) {
      if (state == 0) return t('common.smartSettlement.amountSettleStateText[0]')
      if (state == 1) return t('common.smartSettlement.amountSettleStateText[1]')
      if (state == 2) return t('common.smartSettlement.amountSettleStateText[2]')
      if (state == 3) return t('common.smartSettlement.amountSettleStateText[3]')
      if (state == 4) return t('common.smartSettlement.amountSettleStateText[4]')
      if (state == 5) return t('common.smartSettlement.amountSettleStateText[5]')
      return ''
    },
    // 打开paas详情tab
    openPassTab(row) {
      if (!this.viewSettleFormAuth) return
      const {settlementFormId, settlementFormNo} = row

      let fromId = window.frameElement?.getAttribute('id')
      let params = `formContentId=${settlementFormId}`;
      openAccurateTab({
        type: PageRoutesTypeEnum.PagePaasTemplateDetail,
        key: settlementFormNo,
        titleKey: settlementFormNo,
        params,
        fromId
      })
    },
  },
  mounted() {
    this.getViewAuth()
    this.search()
  },
}
</script>

<style lang="scss" scoped>
.amount-settlement-list {
  padding: 16px;
  &-header {
    margin-bottom: 16px;
    .total-amount {
      display: flex;
      span {
        flex: 1;
      }
    }
  }
  .view-detail-btn {

  }
}
</style>