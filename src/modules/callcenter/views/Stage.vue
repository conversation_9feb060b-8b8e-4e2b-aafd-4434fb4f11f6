<template>
  <div class="call-center-stats-container" >
    <div ref="tableHeaderContainer">
      <div class="stats-station-header">
        <p>{{$t('callcenter.stage.label1')}}：<span>{{callState.hotLine}}</span></p>
        <p>{{$t('callcenter.stage.label2')}}：<span>{{callState.agentNum}}</span>
          <el-tooltip :content="$t('callcenter.stage.content1')">
            <i class="iconfont icon-fdn-info"></i>
          </el-tooltip>
        </p>
        <p>{{$t('callcenter.stage.label3')}}：<span>{{callState.endTime}}</span></p>
        <p>{{$t('callcenter.stage.label4')}}：<span class="money">{{callState.cost}}{{$t('common.base.yuan')}}</span></p>

      </div>
      <div v-show="packUp">
        <div class="stats-station-today-header">{{$t('callcenter.stage.title1')}}</div>
        <div class="stats-station-today-content">

          <div class="stats-station-card">
            <div class="card-content">
              <p><i class="iconfont icon-huru"></i> {{$t('callcenter.stage.title2')}}</p>
              <h3> {{statisticsRecord.normalDealingCount}}</h3>
              <div class="card-bottom">
                <h4>{{$t('callcenter.stage.missedCall')}}：{{statisticsRecord.normalNotDealCount}}</h4>
                <h4>{{$t('callcenter.stage.label6')}}：{{parseFloat((statisticsRecord.normalDealingRate || 0) * 100).toFixed(2) }}%</h4>
              </div>
            </div>
          </div>

          <div class="stats-station-card">
            <div class="card-content">
              <p><i class="iconfont icon-huru"></i> {{$t('callcenter.stage.title3')}}</p>
              <h3>{{statisticsRecord.normalSolvedCount}}</h3>
              <div class="card-bottom" style="padding-top:0;">{{$t('callcenter.stage.label7')}}：{{ parseFloat((statisticsRecord.normalSolvedRate || 0)*100).toFixed(2) }}%</div>
            </div>
          </div>

          <div class="stats-station-card">
            <div class="card-content">
              <p><i class="iconfont icon-huru"></i> {{$t('callcenter.stage.title4')}}</p>
              <h3>{{statisticsRecord.totalNormalDealings >> 0|fmt_h_m_s}}</h3>
              <div class="card-bottom" style="padding-top:0;">{{$t('callcenter.stage.label8')}}：{{statisticsRecord.avgNormalDealings >> 0|fmt_h_m_s}}</div>
            </div>
          </div>

          <div class="stats-station-card">
            <div class="card-content">
              <p><i class="iconfont icon-huchu"></i> {{$t('callcenter.stage.title5')}}</p>
              <h3>{{statisticsRecord.dialoutDealingCount}}</h3>
              <div class="card-bottom">
                <h4>{{$t('callcenter.stage.missedCall')}}：{{statisticsRecord.dialoutNotDealCount}}</h4>
                <h4>{{$t('callcenter.stage.label6')}}：{{ parseFloat((statisticsRecord.dialoutDealingRate || 0)*100).toFixed(2) }}%</h4>
              </div>
            </div>
          </div>

          <div class="stats-station-card">
            <div class="card-content">
              <p><i class="iconfont icon-huchu"></i> {{$t('callcenter.stage.title6')}}</p>
              <h3>{{statisticsRecord.totalDialoutDealings >> 0|fmt_h_m_s}}</h3>
              <div class="card-bottom" style="padding-top:0;">{{$t('callcenter.stage.label8')}}：{{statisticsRecord.avgDialoutDealings >> 0|fmt_h_m_s}}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="bbx-normal-pack-up">
        <div @click="changePackUp()">
          <i class="iconfont icon-Icon_up" v-show="packUp"></i>
          <i class="iconfont icon-more" v-show="!packUp"></i>
        </div>
      </div>
    </div>
    <div class="common-list-table__flex-row">
      <BizIntelligentTagsFilterPanel
          v-bind="filterTagsPanelBindAttr"
          v-on="filterTagsPanelBindOn"
      />
      <div class="common-list-section">
        <div class="customer-list-container" ref="customerListPage" v-loading.fullscreen.lock="loadingListData">


          <!--operation bar start-->
          <!-- <div class="operation-bar-container">
            <div class="top-btn-group">
              <el-radio-group v-model="activeName" @change="stateChangeHandler" size="medium">
                <el-radio-button label="全部"></el-radio-button>
                <el-radio-button label="已接来电"></el-radio-button>
                <el-radio-button label="未接来电"></el-radio-button>
                <el-radio-button label="呼出已接"></el-radio-button>
                <el-radio-button label="呼出未接"></el-radio-button>
              </el-radio-group>
            </div>
          </div> -->
          <!--operation bar end-->

          <!--list start-->
          <div class="customer-list-component">
            <div ref="tableDoContainer" >
              <!--搜索-->
              <div ref="tableDoContainer" class="customer-list-search-group-container">
            
                <form class="base-search" onsubmit="return false;">
                  <BizIntelligentTagsFilterPanelOperatorButton
                    :showDot="showTagOperatorButtonDot"
                    :active="filterTagPanelShow"
                    @click="changeIntelligentTagsFilterPanelShow"
                  />
                  <el-radio-group class="call-center-filter__tab flex-1" v-model="activeName" @change="stateChangeHandler" size="small">
                    <el-radio-button :label="$t('common.base.all')"></el-radio-button>
                    <el-radio-button :label="$t('callcenter.stage.answeredCall')"></el-radio-button>
                    <el-radio-button :label="$t('callcenter.stage.missedCall')"></el-radio-button>
                    <el-radio-button :label="$t('callcenter.stage.callOutAnswered')"></el-radio-button>
                    <el-radio-button :label="$t('callcenter.stage.calloutNoAnswer')"></el-radio-button>
                  </el-radio-group>
                  <div class="customer-list-base-search-group input-with-append-search">
                    <el-input v-model="params.keyword" :placeholder="$t('callcenter.stage.placeholder1')" v-trim:blur class="mar-r-12">
                      <i slot="prefix" class="el-input__icon el-icon-search"></i>
                      <el-button type="primary" slot="append" @click="params.pageNum=1;getRecordList();trackEventHandler('search')" native-type="submit" v-track="$track.formatParams('KEYWORD_SEARCH')">{{$t('common.base.search')}}</el-button>
                    </el-input>
                    <el-button type="plain-third" @click="resetParams" v-track="$track.formatParams('RESET_SEARCH')">{{$t('common.base.reset')}}</el-button>
                  </div>
                  <span class="advanced-search-visible-btn" @click.self.stop="panelSearchAdvancedToggle">
                    <i class="iconfont icon-filter"></i>
                    {{$t('common.base.advancedSearch')}}
                  </span>
                </form>
                <!--高级搜索-->
              </div>
              <div class="action-button-group">

                <div class="action-button-group-left">
                  <AISummary
                    v-if="allowSummary"
                    is-new
                    :fields="columns"
                    :multipleSelection="multipleSelection"
                    :template="summaryTemplate"
                  />
                </div>
                 
                <div class="action-button-group-right">
                  <BizIntelligentTaggingButtonMulti v-bind="taggingBindAttr" v-on="taggingBindOn" />
                  <el-dropdown
                    trigger="hover"
                    v-if="allowExport"
                  >
                    <div class="task-ai task-flex task-font14 task-c6 cur-point bg-w">
                      <span class="task-mr4 task-ml4">{{$t('common.base.moreOperator')}}</span>
                      <i class="iconfont icon-fdn-select task-icon"></i>
                    </div>
                    <el-dropdown-menu slot="dropdown">
                      <!-- <el-dropdown-item v-if="tableSetting.needImport">
                        <div @click="openDialog('importProduct')">导入</div>
                      </el-dropdown-item>
                      <el-dropdown-item v-if="tableSetting.needExport">
                        <div @click="exportFn(false)">导出</div>
                      </el-dropdown-item> -->
                      <el-dropdown-item >
                        <div @click="exportFn(false)" v-track="$track.formatParams('EXPORT', null, 'MORE_ACTIONS')">{{$t('common.base.export')}}</div>
                      </el-dropdown-item>
                      <el-dropdown-item >
                        <div @click="exportFn(true)" v-track="$track.formatParams('EXPORT_ALL', null, 'MORE_ACTIONS')">{{$t('common.base.exportAll')}}</div>
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                  <span class="el-dropdown-link cur-point" @click="showAdvancedSetting" v-track="$track.formatParams('SELECT_COLUMN')">
                    {{$t('common.base.choiceCol')}}
                    <i class="iconfont icon-fdn-select"></i>
                  </span>
                </div>
                
              </div>
            </div>

            <!-- <div ref="BaseSelectionBarComponent" class="base-selection-wrapper">
              <base-selection-bar ref="baseSelectionBar" v-model="multipleSelection" @toggle-selection="toggleSelection" @show-panel="() => multipleSelectionPanelShow = true" />
            </div> -->
            <div class="pad-l-16 pad-r-16 bg-w" style="height: calc(100% - 170px)">
              <el-table
                :data="tableData"
                stripe
                @select="handleSelection"
                @select-all="handleSelection"
                :row-key="getRowKey"
                header-row-class-name="common-list-table-header__v2"
                ref="multipleTable"
                class="customer-table bbx-normal-list-box"
                height="100%"
                :border="true"
                @header-dragend="headerDragend"
              >
                <template slot="empty">
                  <BaseListForNoData v-show="!loadingListData" :notice-msg="$t('common.base.tip.noData')"></BaseListForNoData>
                </template>
                <!-- 目前后端不支持批量导出 先注释 -->
                <el-table-column
                  type="selection"
                  width="48"
                  align="center"
                  class-name="select-column"
                ></el-table-column>
                <template v-for="(column, index) in columns" >
                  <el-table-column
                    v-if="column && column.show"
                    show-overflow-tooltip
                    :align="column.align"
                    :key="`${column.field}_${index}`"
                    :label="column.label"
                    :min-width="column.minWidth"
                    :prop="column.field"
                    :sortable="column.sortable"
                    :width="column.width"
                    :resizable="true"
                    :fixed="column.fixLeft || false">
                    <template slot-scope="scope">
                      <template v-if="column.field === 'dialPhone'">
                        <span>{{scope.row[column.field]}}</span>
                        <template v-if="hasCallAuth && scope.row[column.field]">
                          <biz-call-icon :value="scope.row[column.field]" v-track="$track.formatParams('CALL_OUT')"/>
                        </template>
                      </template>
                      <template v-else-if="column.field === 'recordId'">
                        <span
                          v-if="false"
                          class="recordId"
                          :class="globalIsHaveCallCenterViewDetailAuth ? '' : 'view-detail-btn-disabled'"
                          @click="detail(scope.row)">
                          {{scope.row[column.field]}}
                        </span>
                        <BizIntelligentTagsViewToConfig
                          v-else
                          type="table"
                          :canClick="globalIsHaveCallCenterViewDetailAuth"
                          :value="scope.row[column.field]"
                          :config="{tableShowType: 'text'}"
                          :tagsList="scope.row.labelList || []"
                          @viewClick="detail(scope.row)"
                        />
                      </template>
                      <template v-else-if="column.field === 'callType'">
                        {{fmt_callType(scope.row)}}
                      </template>
                      <template v-else-if="column.field === 'customerName'">
                        <div class="table-blacklist flex a-center" v-if="scope.row[column.field]">
                          <span >{{scope.row[column.field]}}</span>
                          <BizIntelligentTagsViewToConfig
                            v-if="scope.row[column.field]"
                            type="detail"
                            class="table-blacklist"
                            :tagsList="showLinkIntelligentTags ? scope.row['customerLabelList'] || [] : []"
                          />
                        </div>
                        <span v-else>--</span>
                      </template>
                      <template v-else-if="column.field === 'sortName'">
                        <span v-if="scope.row[column.field]">{{scope.row[column.field]}}</span>
                        <span v-else>--</span>
                      </template>
                      <template v-else-if="column.field === 'linkmanName'">
                        <div v-if="scope.row[column.field]" class="flex a-center table-blacklist">
                          <span>{{scope.row[column.field]}}</span>
                          <BizIntelligentTagsViewToConfig
                            v-if="scope.row[column.field]"
                            type="detail"
                            class="table-blacklist"
                            :tagsList="showLinkIntelligentTags ? scope.row['linkmanLabelList'] || [] : []"
                          />
                        </div>
                        <div v-else>--</div>
                      </template>
                      <template v-else-if="column.field === 'status'">
                        <span v-if="scope.row[column.field] == 0" style="color:#FB602C">{{$t('common.base.notResolved')}}</span>
                        <span v-else-if="scope.row[column.field] == 1">{{$t('common.base.resolved')}}</span>
                        <span v-else>--</span>
                      </template>
                      <template v-else-if="column.field === 'handleStatus'">
                        <span v-if="scope.row[column.field] == 0" style="color:#FB602C">{{$t('common.base.notProcessed')}}</span>
                        <span v-else-if="scope.row[column.field] == 1">{{$t('common.base.processed')}}</span>
                        <span v-else>--</span>
                      </template>
                      <template v-else-if="column.field === 'solveStatus'">
                        <span>{{{'UNSOLVED': $t('common.base.notResolved'),'SOLVED': $t('common.base.resolved'),}[scope.row[column.field]]}}</span>
                      </template>
                      <!-- <template v-else-if="column.field === 'operation'" slot-scope="scope">
                      <el-button type="text" v-if="scope.row.state=='notDeal'" @click="dealDialog(scope.row)">处理</el-button>
                      <el-button class="color-black" type="text" v-else @click="detail(scope.row)">详情</el-button>
                    </template> -->
                      <template v-else-if="column.field === 'agentName'">
                        <template v-if="isOpenData && scope.row.agentStaffId">
                          <open-data type="userName" :openid="scope.row.agentStaffId"></open-data>
                        </template>
                        <template v-else>
                          <span>{{scope.row[column.field]}}</span>
                        </template>
                      </template>
                      <template v-else-if="column.field === 'transfered'">
                        <span>{{scope.row[column.field]===1? $t('common.base.yes'): $t('common.base.no')}}</span>
                      </template>
                      <!-- 富文本 -->
                      <template v-else-if="column.formType === 'richtext'">
                        <div class='view-detail-btn' @click.stop="openRichtextVisible(scope.row, column)">
                          <span v-if="scope.row.attribute && scope.row.attribute[column.field]">{{$t('common.base.view')}}</span>
                        </div>
                      </template>
                      <template v-else-if="column.isSystem === 0">
                        {{ $formatFormField(column, scope.row) }}
                      </template>
                      <template v-else-if="column.field === 'hasLeaveMessage'">
                        <span>{{scope.row[column.field]===1? $t('common.base.yes'): $t('common.base.no')}}</span>
                      </template>
                      <template v-else-if="column.field === 'callRecordContentSummary'">
                        <span>
                          {{ getContentSummary(scope.row.callRecordContent) }}
                        </span>
                      </template>
                      <template v-else-if="column.field === 'leaveMessageContentSummary'">
                        <span>
                          {{ getContentSummary(scope.row.leaveMessageContent) }}
                        </span>
                      </template>
                      <template v-else-if="column.field === 'leaveMessageFileUrl'">
                        <span
                          class="play-btn"
                          v-if="scope.row[column.field]"
                          @click="playLeaveMessage(scope.row[column.field])">
                          {{$t('common.base.play')}}
                        </span>
                      </template>
                      <template v-else>
                        <pre class="pre-text">{{scope.row[column.field]}}</pre>
                      </template>
                    </template>
                  </el-table-column>
                </template>
              </el-table>
            </div>
            <div ref="tableFooterContainer" class="table-footer">
              <div class="list-info">{{$t('callcenter.stage.total')}}<span class="level-padding">{{totalItems}}</span>{{$t('common.base.record')}}
              {{$t('common.base.selected')}}
                <span
                  class="color-primary pad-l-5 pad-r-5"
                >{{ multipleSelection.length }}</span>{{$t('common.base.piece')}}
                <span class="color-primary cur-point" @click="toggleClearSelection">{{$t('common.base.clear')}}</span>
              </div>
              <el-pagination
                class="customer-table-pagination"
                background
                @current-change="jump"
                @size-change="handleSizeChange"
                :page-sizes="defaultTableData.defaultPageSizes"
                :page-size="params.pageSize"
                :current-page="params.pageNum"
                layout="prev, pager, next, sizes, jumper"
                :total="totalItems">
              </el-pagination>
            </div>


          </div>

          <!--list end-->

          <biz-select-column ref="advanced" @save="saveColumnStatus" />

          <search-panel ref="searchPanel" :customer-search-fields="customerSearchFields">
            <div class="advanced-search-btn-group" slot="footer">
              <el-button type="plain-third" @click="resetParams">{{$t('common.base.reset')}}</el-button>
              <el-button type="primary" @click="powerfulSearch" native-type="submit">{{$t('common.base.search')}}</el-button>
            </div>
          </search-panel>
        </div>
      </div>
    </div>
    <!-- 处理未接来电的对话框 -->
    <el-dialog :title="$t('callcenter.stage.title7')" :visible.sync="missCallDialogVisible" width="30%" @close="missCallDialogClosed">
      <!-- 内容主体区域 -->
      <el-form :model="missCallForm" ref="missCallFormRef" label-position="top">
        <el-form-item :label="$t('callcenter.stage.label12')">
          {{missCallForm.dialPhone}}
          <span class="make-phone-call" v-if="hasCallAuth" @click="makePhoneCall(missCallForm.dialPhone)">{{$t('common.base.dial')}}<i class="iconfont icon-dianhua1"></i></span>
        </el-form-item>

        <el-form-item :label="$t('callcenter.stage.label13')">
          <el-radio-group v-model="missCallForm.handleStatus">
            <el-radio :label="0">{{$t('common.base.notProcessed')}}</el-radio>
            <el-radio :label="1">{{$t('common.base.processed')}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('common.base.remark')" prop="handleRemark">
          <el-input type="textarea"
                       :placeholder="$t('common.placeholder.inputRemark')"
                       v-model="missCallForm.handleRemark"
                       maxlength="500"
                       show-word-limit></el-input>
        </el-form-item>
      </el-form>
      <!-- 底部区域 -->
      <span slot="footer" class="dialog-footer">
        <el-button @click="missCallDialogVisible = false">{{$t('common.button.cancel')}}</el-button>
        <el-button type="primary" @click="deal">{{$t('common.button.confirm')}}</el-button>
      </span>
    </el-dialog>

    <!-- start 导出s -->
    <base-export-group
      ref="exportPanel"
      :alert="exportAlert"
      :columns="exportColumns"
      :build-params="buildExportParams"
      :group="true"
      :needchoose-break="false"
      method="post"
      :action="callCenterStage"
      :export-type="'free'"
      :key-id="`test-list`"
      :export-skip-select="false"
    />
    <!-- end 导出e -->
    <!--查看富文本 -->
    <base-view-rich-text ref="baseViewRichTextRef"></base-view-rich-text>

  </div>
</template>

<script>
// pageDes 呼叫中心 通话记录列表
import { isOpenData, openAccurateTab } from '@src/util/platform'
import _ from 'lodash'
import { parse } from '@src/util/querystring';
import SearchPanel from '../component/SearchPanel'
import * as CallCenterApi from '@src/api/CallCenterApi'
import * as IMApi from '@src/api/ImApi.js';
import { ctiCallOut } from '@src/util/ctisdk';
import { getRootWindow } from '@src/util/dom';
import { defaultTableData } from '@src/util/table'
import BaseCallPhone from 'src/component/common/BaseCallPhone/index.vue'
/* mixin */
import ThemeMixin from '@src/mixins/themeMixin/index.ts'
import AuthMixin from '@src/mixins/authMixin'
import { intelligentTagsListMixin } from '@src/modules/intelligentTags/mixins'

import {callCenterStage} from '@src/api/Export';

/** util */
import { fixedColumns, getDynamicColumns} from '@src/modules/callcenter/util.js'
import { safeNewDate } from '@src/util/time';
import { PageRoutesTypeEnum } from 'pub-bbx-global/pageType/dist/enum/PageRoutesEnum'
/* version control mixin */
import { VersionControlTaskMixin } from '@src/mixins/versionControlMixin'
/* components */
import { AISummary } from '@src/modules/ai/components'
import { AIAgentTemplateEnum } from '@src/modules/ai/model/enum'
import { getAIAgentWorkflowShow } from '@src/api/AIv2API'

const STAGE_LIST_KEY = 'stage_list';
const STAGE_PAGE_SIZE_KEY = 'stage_page_size';
import { formatDate, useFormTimezone, formatAddress } from 'pub-bbx-utils'
import { parse_with_default_value } from '@src/util/lang/object';
const { disposeFormListViewTime } = useFormTimezone()

export default {
  name: 'stage',
  mixins: [ThemeMixin, AuthMixin, VersionControlTaskMixin, intelligentTagsListMixin],
  props: {
    initData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      defaultTableData,
      isOpenData,
      pending: false,
      loadingListData: true,
      activeName: this.$t('common.base.all'),
      callState: {},
      statisticsRecord: {},
      params: {
        // orderDetail: {},
        // moreConditions: {
        //   conditions: []
        // },
        keyword: '',
        pageNum: 1,
        pageSize: 10
      },
      totalItems: 0,
      multipleSelection: [],
      defaultAddress: [],

      recordList: [],
      columns: fixedColumns(),
      selectedLimit: 500,
      columnNum: 1,
      tableKey: (Math.random() * 1000) >> 2,
      missCallDialogVisible: false,
      missCallForm: {
        id:'',
        dialPhone:'',
        handleStatus: 0,
        handleRemark: ''
      },
      callCenterStage,
      tableContainerHeight:'440px',
      packUp:true,
      customerFields: [],
      allowSummary: false,
      summaryTemplate: AIAgentTemplateEnum.VOC
    }
  },
  computed: {
    // 处理时间后的列表数据
    tableData(){
      return disposeFormListViewTime(this.recordList, this.columns)
    },
    hasCallAuth() {
      return !!getRootWindow(window).CTI
    },
    panelWidth() {
      return `${420 * this.columnNum}px`
    },

    exportColumns() {
      let arr = [...this.columns]
        .filter((item) => item.formType !== 'doSome')
        .map((field) => {
          if (
            ['customer', 'productTemplate', 'remindCount'].some(
              (key) => key === field.fieldName
            )
            || field.formType === 'info'
          ) {
            field.export = false;
          } else {
            field.export = true;
          }
          return field;
        });
      let systemArr = arr.filter(v=>v.isSystem === 1)
      let attArr = arr.filter(v=>v.isSystem === 0)
      const intelligentTagsExportFields = [...this.intelligentTagsExportFields].map(item=> ({
            ...item, 
            field: item.fieldName, 
            tableName: 'im', 
            export: true, 
            isSystem: 1, 
            label: item.displayName
      }))

      systemArr.splice(1, 0, ...intelligentTagsExportFields)
      
      let arr_ = [
        {
          label: this.$t('common.form.fieldGroupName.system'),
          value: 'callCenterStageExport',
          columns: systemArr,
        },
        {
          label: this.$t('common.form.fieldGroupName.attribute'),
          value: 'attArr',
          columns: attArr,
        },
      ];
            console.log(arr_)
      return arr_.filter(item => {
        return item.columns?.length > 0;
      });
    },
    // 服务备注表单高级搜索字段
    customerSearchFields() {
      return this.customerFields.filter(f => f.isSearch == 1)
    },
    selectedIds() {
      return this.multipleSelection.map(v => v.id);
    },
    allowExport() {
      return this.globalIsHaveCallCenterExportAuth
    }
  },
  created() {
    this.initIntelligentTagsParams('CALLCENTER')
  },
  async mounted() {
    // 获取缓存的pagesize
    const localStorageData = await this.getLocalStorageData();
    this.params.pageSize = localStorageData[STAGE_PAGE_SIZE_KEY] || 10;
    const query = parse(window.location.search) || {};
    if(query.keyword) {
      this.params.keyword = query.keyword
    }
    this.customerFields = await this.getTemplateByTenantId()
    this.getTodayCallState()
    this.getTodayStatisticsRecord()
    this.columns = this.buildTableColumn()
    this.getRecordList()

    this.getAIAgentWorkflowShow()

    // this.search()
    let that_ = this;
    // 监听切换后需要重新计算列表高度 后续可以用activated生命周期替换
    window.addEventListener('message', (event)=> {
      const {action, data} = event.data;
      if (action == 'shb.frame.activatedPage'){
        that_.$nextTick(()=> {
          that_.knowTableContainerHeight();
        })
      }
    });
    this.$nextTick(()=> {
      this.knowTableContainerHeight()
      window.onresize = _.debounce(()=>{
        that_.knowTableContainerHeight()
      }, 500)
    })
  },

  methods: {
    async getAIAgentWorkflowShow() {
      try {

        const params = {
          template: AIAgentTemplateEnum.VOC
        }
        const result = await getAIAgentWorkflowShow(params)

        this.allowSummary = Boolean(result?.data)

      } catch (error) {
        console.error(error)
      }
    },
    getContentSummary(contentJSON) {
      try {
        const contentObject = parse_with_default_value(contentJSON, {})
        const summary = contentObject?.summary || ''
        return summary
      } catch (error) {
        console.error(error)
        return ''
      }
    },
    /** 打开富文本弹窗 */
    openRichtextVisible(row, column) {
      const richtextId = row?.attribute?.[column.fieldName] || ''
      this.$refs?.baseViewRichTextRef?.openDialog(richtextId, false)
    },
    // 选择列排序
    buildSortFields(originFields = [], fieldsMap = {}) {
      let fields = [];
      let unsortedFields = [];

      originFields.forEach(originField => {
        let { field:fieldName } = originField;
        let field = fieldsMap[fieldName];

        if (field) {
          let { index } = field;
          fields[index] = originField;
        } else {
          unsortedFields.push(originField);
        }
      });

      return fields.concat(unsortedFields);
    },
    /**
     * @description 表头更改
     */
    headerDragend(newWidth, oldWidth, column, event) {
      let data = this.columns
        .map(item => {
          if (item.fieldName === column.property) {
            item.width = column.width;
          }
          return item;
        })
        .map(item => {
          return {
            field: item.field,
            show: item.show,
            width: item.width,
          };
        });
      this.modifyColumnStatus({ type: 'column', data });
    },
    // 保存选择列配置到本地
    saveColumnStatusToStorage() {
      const localStorageData = this.getLocalStorageData();
      let columnsStatus = null;

      // 判断是否存储选择列
      const columnsList = this.columns.map(c => ({
        field: c.field,
        show: c.show,
        width: c.width,
        fixLeft: c.fixLeft
      }));

      if (localStorageData.columnStatus) {
        localStorageData.columnStatus = columnsList;
        columnsStatus = localStorageData.columnStatus;
      } else {
        columnsStatus = columnsList;
      }

      this.saveDataToStorage('columnStatus', columnsStatus);
    },
    // 保存选择列配置
    saveColumnStatus(event) {
      let columns = event.data || [];

      this.columns = [];
      this.$nextTick(() => {
        this.$set(this, 'columns', columns.slice());
        this.saveColumnStatusToStorage();
      });
      this.$message.success(this.$t('common.base.saveSuccess'));
    },
    // 处理未接来电打电话
    async makePhoneCall(tel){
      if(!tel) return
      try {
        ctiCallOut(tel) // taskType:'handle'
      } catch (error) {
        console.error(error);
      }
    },
    setpageNum(){
      this.params.pageNum = 1;
    },
    async getRecordList(){
      this.loadingListData = true
      try {
        let {code, message, result} = await CallCenterApi.getRecordList({ ...this.params, ...this.builderIntelligentTagsSearchParams() })
        if (code != 0) return this.$message.error(message || this.$t('callcenter.callCenter.innerError'))
        this.loadingListData = false
        if (!result || !result.list) {
          this.recordList = [];
          this.totalItems = 0;
          this.params.pageNum = 1;
        } else {
          const { total, pageNum, list } = result;
          this.recordList = list.map(c => {
            c.pending = false;
            c.attribute = c?.attribute ?? {}
            return c;
          });
          this.totalItems = total;
          this.params.pageNum = pageNum;
          this.matchSelected(); // 把选中的匹配出来
        }
      } catch (error) {
        this.loadingListData = false
        console.error(error)
      }
    },

    getTodayCallState(){
      CallCenterApi.getTodayCallState().then(({code, message, result}) => {
        if (code != 0) return this.$message.error(message || this.$t('callcenter.callCenter.innerError'))
        this.callState = result || {}
      }).catch((err) => {
        console.error(err)
      })
    },

    getTodayStatisticsRecord(){
      CallCenterApi.getTodayStatisticsRecord().then(({code, message, result}) => {
        if (code != 0) return this.$message.error(message || this.$t('callcenter.callCenter.innerError'))
        this.statisticsRecord = result || {}
      }).catch((err) => {
        console.error(err)
      });
    },
    stateChangeHandler (state) {
      this.$track.clickStat(this.$track.formatParams('STATE_FILTER_TABS_CHANGE', state));

      // 切换tab
      this.activeName = state;
      this.columns.forEach(col => {
        if (col.field == 'handleStatus') {
          this.$set(col, 'show', this.activeName === this.$t('common.base.all') || this.activeName === this.$t('callcenter.stage.missedCall'));
        } else if (col.field == 'status') {
          this.$set(col, 'show', this.activeName != this.$t('callcenter.stage.missedCall'));
        }
      })
      switch (state) {
      case this.$t('common.base.all'):
        this.params.callType = ''
        this.params.state = ''
        break;
      case this.$t('callcenter.stage.answeredCall'):
        this.params.callType = 'normal'
        this.params.state = 'dealing'
        break;
      case this.$t('callcenter.stage.missedCall'):
        this.params.callType = 'normal'
        this.params.state = 'notDeal'
        break;
      case this.$t('callcenter.stage.callOutAnswered'):
        this.params.callType = 'dialout'
        this.params.state = 'dealing'
        break;
      case this.$t('callcenter.stage.calloutNoAnswer'):
        this.params.callType = 'dialout'
        this.params.state = 'notDeal'
        break;
      default:
        break;
      }
      this.getRecordList()
      this.$track.clickStat(this.$track.formatParams('CALL_STATUS_TAB'));
    },

    powerfulSearch() {
      // 高级搜索
      let keyword = this.params.keyword
      const p = this.$refs.searchPanel.buildParams()
      const { conditions = [], ...others } = p
      this.params = {
        conditions: [],
        ...others,
        keyword,
        pageNum: 1,
        pageSize: 10
      }
      conditions.forEach(item=>{
        if(item.property == 'sortId') {
          // 咨询分类
          this.params.sortId = item.value[0].id || ''
        } else {
          const isCustomerSearchFields = this.customerSearchFields.some(f => f.fieldName == item.property)
          // 服务备注表单高级搜索字段放到conditions中
          if (isCustomerSearchFields) {
            this.params.conditions.push(item)
          } else {
            this.params[item.property] = item.value || ''
          }
        }
      })

			this.$track.clickStat(this.$track.formatParams('ADVANCED_SEARCH'));

      this.getRecordList()
    },
    showAdvancedSetting() {
      // window.TDAPP.onEvent('pc：通话记录-选择列事件')

      this.$refs.advanced.open(this.columns)
    },

    formatAddress(ad) {
      if (null == ad) return ''

      const { adProvince, adCity, adDist } = ad
      return [adProvince, adCity, adDist].filter(d => !!d).join('-')
    },
    formatCustomizeAddress(ad) {
      if (null == ad) return ''

      return formatAddress(ad)
    },

    buildParams() {
      const sm = Object.assign({}, this.params)
      let params = {
        keyword: sm.keyword,
        pageSize: sm.pageSize,
        pageNum: sm.pageNum
      }
      return params
    },

    jump(pageNum) {
      this.params.pageNum = pageNum
      // this.search()
      this.getRecordList()
    },
    handleSizeChange(pageSize) {
      this.saveDataToStorage(STAGE_PAGE_SIZE_KEY, pageSize);
      this.params.pageNum = 1
      this.params.pageSize = pageSize
      // this.search()
      this.getRecordList()
    },
    handleSelection(selection) {
      this.multipleSelection = selection;
      this.$nextTick(()=>{
        this.knowTableContainerHeight()
      })
    },
    computeSelection(selection) {
      let tv = [];
      tv = this.multipleSelection.filter(ms =>
        this.customers.every(c => c.id !== ms.id)
      );
      tv = _.uniqWith([...tv, ...selection], _.isEqual);
      return tv;
    },
    toggleSelection(rows) {
      let isNotOnCurrentPage = false;
      let row = undefined;

      if (rows) {
        for (let i = 0; i < rows.length; i++) {
          row = rows[i];
          isNotOnCurrentPage = this.recordList.every(item => {
            return item.id !== row.id;
          });
          if (isNotOnCurrentPage) return;
        }
        rows.forEach(row => {
          this.$refs.multipleTable.toggleRowSelection(row);
        });
      } else {
        this.$refs.multipleTable.clearSelection();
        this.multipleSelection = [];
      }
    },
    /**
     * @description 修改选择列设置
     * @param {Object} event 事件对象
     */
    modifyColumnStatus(event) {
      let columns = event.data || [],
        colMap = columns.reduce(
          (acc, col) => (acc[col.field] = col) && acc,
          {}
        );
      this.columns.forEach(col => {
        let newCol = colMap[col.field];
        if (null != newCol) {
          this.$set(col, 'show', newCol.show);
          this.$set(col, 'width', newCol.width);
        }
      });

      this.saveColumnStatusToStorage();
    },
    // common methods
    getLocalStorageData() {
      const dataStr = localStorage.getItem(STAGE_LIST_KEY) || '{}'
      return JSON.parse(dataStr)
    },
    saveDataToStorage(key, value) {
      const data = this.getLocalStorageData()
      data[key] = value
      localStorage.setItem(STAGE_LIST_KEY, JSON.stringify(data))
    },
    buildTableColumn() {
      const localStorageData = this.getLocalStorageData()
      let columnStatus = localStorageData.columnStatus || []
      const localColumns = columnStatus
        .map(i => (typeof i == 'string' ? { field: i, show: true } : i))
        .reduce((acc, col, currentIndex) => {
          acc[col.field] = {
            field: col,
            index: currentIndex,
          };
          return acc;
        }, {});
      let baseColumns = fixedColumns().map(v=>{
        return{
          ...v,
          isSystem:1
        }
      })
      let dynamicColumns = getDynamicColumns()
        .map(field => {
          let sortable = false
          let minWidth = null

          if (['date', 'datetime', 'number'].indexOf(field.formType) >= 0) {
            sortable = 'custom'
            minWidth = 100
          }

          if (field.displayName && field.displayName.length > 4) {
            minWidth = field.displayName.length * 20
          }

          // if (sortable && field.displayName.length >= 4) {
          //   minWidth = 125
          // }

          if (field.formType === 'datetime') {
            minWidth = 180
          }

          return {
            label: field.displayName,
            field: field.fieldName,
            formType: field.formType,
            minWidth: typeof minWidth == 'number' ? minWidth : `${minWidth}px`,
            sortable,
            isSystem: 1
          }
        })
      let imListFields = [...baseColumns, ...dynamicColumns, ...this.customerFields];
      if (Array.isArray(columnStatus) && columnStatus.length > 0) {
        // 有本地缓存--列表排序
        imListFields = this.buildSortFields(imListFields, localColumns);
      }

      let columns = imListFields.map(col => {
        let show = col.show === true
        let width = col.width
        let localField = localColumns[col.field]?.field || null;
        let fixLeft = localField?.fixLeft || false;

        if (null != localField) {
          if (localField.width){
            width = typeof localField.width == 'number' ? `${localField.width}px` : localField.width
          }
          show = localField.show !== false
        }
        console.info('width::', width);

        col.show = show
        col.width = width
        col.type = 'column'
        col.displayName = col.label
        col.fieldName = col.field
        col['fixLeft'] = fixLeft && 'left'
        col.tableName = 'im'
        return col
      }).filter(v => {

        if (v.fieldName == 'taskId') {
          return this._isShowTaskModule
        }

        return v
      })
      return columns
    },
    resetParams() {
      // window.TDAPP.onEvent('pc：客户管理-重置事件')
      this.$refs.searchPanel.resetParams()

      this.params = {
        keyword: '',
        pageNum: 1,
        pageSize: this.params.pageSize,
        // orderDetail: {},
        // moreConditions: {
        //   conditions: []
        // }
      }
      // this.getRecordList()
      // this.search()
      this.reloadTab()
    },
    // match data
    matchSelected() {
      if (!this.multipleSelection.length) return;
      const selected = this.tableData.filter(c => {
        if (this.multipleSelection.some(sc => sc.id === c.id)) {
          this.multipleSelection = this.multipleSelection.filter(
            sc => sc.id !== c.id
          );
          this.multipleSelection.push(c);
          return c;
        }
      }) || [];

      this.$nextTick(() => {
        this.toggleSelection(selected);
      });
    },
    fmt_callType(row){
      // 呼叫类型
      let res = ''
      if(row.callType === 'normal') {
        res = row.state === 'dealing' ? this.$t('callcenter.stage.answeredCall') : this.$t('callcenter.stage.missedCall')
      } else if(row.callType === 'dialout'){
        res = row.state === 'dealing' ? this.$t('callcenter.stage.callOutAnswered') : this.$t('callcenter.stage.calloutNoAnswer')
      }
      return res
    },
    panelSearchAdvancedToggle() {
      // window.TDAPP.onEvent('pc：客户管理-高级搜索事件')
      this.$refs.searchPanel.open()
      this.$nextTick(() => {
        let forms = document.getElementsByClassName('advanced-search-form')
        for (let i = 0; i < forms.length; i++) {
          let form = forms[i]
          form.setAttribute('novalidate', true)
        }
      })
    },
    // TalkingData事件埋点
    trackEventHandler(type) {
      // if (type === 'search') {
      //   window.TDAPP.onEvent('pc：呼叫工作台-搜索事件')
      //   return
      // }
    },
    getRowKey(row) {
      return row.id
    },
    detail(row){

      if (!this.globalIsHaveCallCenterViewDetailAuth) return

      // let fromId = window.frameElement.getAttribute('id');
      // this.$platform.openTab({
      //   id: `callcenter_view_${row.id}`,
      //   title: '通话详情',
      //   close: true,
      //   url: `/setting/callcenter/view?id=${row.id}&phone=${row.dialPhone}`,
      //   fromId: 'M_CASE'
      // });
      openAccurateTab({
        type: PageRoutesTypeEnum.PageCallcenterView,
        key: row.id,
        params: `id=${row.id}&phone=${row.dialPhone}`,
        fromId: 'M_CASE'
      })
      this.$track.clickStat(this.$track.formatParams('TO_DETAIL'))
    },
    missCallDialogClosed() {
      this.$refs.missCallFormRef.resetFields()
    },
    dealDialog(item){
      this.missCallDialogVisible = true
      this.missCallForm.id = item.id
      this.missCallForm.dialPhone = item.dialPhone
    },
    async deal(row){
      // 处理未接来电
      // console.info('form::', this.missCallForm);
      try {
        this.pending = true;
        const params = this.missCallForm
        delete params.dialPhone
        const { code, message } = await CallCenterApi.updateHandleStatus(params)
        if (code !== 0) return this.$platform.notification({
          title: this.$t('common.base.tip.processFailed'),
          message: message || '',
          type: 'error',
        })
        this.pending = false;
        this.missCallDialogVisible = false
        this.missCallDialogClosed()
        this.$platform.notification({
          title: this.$t('common.base.tip.processSuccess'),
          type: 'success',
        })
        this.getRecordList()
      } catch (error) {
        this.pending = false;
        console.error(error)
      }
    },
    /**
     * @des 导出会话记录
     */
    exportFn(exportAll) {
      // if (this.tableSetting.fastExport) {
      //   // 快速导出
      //   window.location.href = this.tableSetting.fastExport;
      //   return;
      // }
      let ids = [];
      let fileName = `${formatDate(safeNewDate(), 'YYYY-MM-DD')}${this.$t('callcenter.callcenter')}.xlsx`;
      if (!exportAll) {
        if (!this.multipleSelection.length)
          return this.$platform.alert(this.$t('common.base.tip.exportNoChoice'));
        ids = this.selectedIds;
      }
      this.$refs.exportPanel.open(ids, fileName);
    },
    /**
     * @description 导出提示
     */
    exportAlert(result, params = {}) {
      this.$platform.alert(result.message);
    },
    /**
     * 导出数据
     */
    exportData(list = []) {
      let export_list = [];
      this.exportColumns.map(v=>{
        export_list = export_list.concat(v?.columns ?? [])
      })
      if (!list.length) return;
      return export_list
        .filter((v) => {
          let bool = list.some((item) => {
            if (v.exportAlias) {
              return v.exportAlias === item;
            }
            return v.field === item || v.fieldName === item;
          });
          return bool;
        }).map(v=> v.exportAlias ? v.exportAlias : (v.field || v.fieldName));
    },
    /**
     * @description 构建导出参数
     * @return {Object} 导出参数
     */
    buildExportParams(checkedMap, ids, exportOneRow) {
      const { callCenterStageExport = [], attArr = [] } = checkedMap;

      let exportAll = !ids || !ids.length;
      const all = exportAll
        ? {
          ...this.params,
          ids: this.selectedIds,
          // tagIds: loginUser.tagIds,
          // tenantId: JSON.parse(rootWindow._init).user.tenantId,
        }
        : {
          ids: this.selectedIds,
          // tagIds: loginUser.tagIds,
          // tenantId: JSON.parse(rootWindow._init).user.tenantId,
        };

      let exportSearchModel = {};
      let params = {
        exportSearchModel: JSON.stringify({
          ...all,
          ...{
            exportTotal: exportAll ? this.totalItems : this.selectedIds.length,
          },
          ...this.builderIntelligentTagsSearchParams()
        }),
      };
      /** ********************* *********************/
      // 信息
      let export_product = this.exportData([...callCenterStageExport, ...attArr]);

      params['ids'] = exportAll
        ? ''
        : this.selectedIds.join(',');

      params['checked'] = export_product.join(',');

      return params;
    },
    // 刷新页面
    reloadTab() {
      let fromId = window.frameElement.getAttribute('data-id')
      console.log(fromId)
      this.$platform.refreshTab(fromId)
    },
    /**
     * @des 获取列表当前的可滚动高度
     */
		 knowTableContainerHeight(){
      let min = 440;
      try {
        let window_ = window.innerHeight;
        let header = this.$refs.tableHeaderContainer?.offsetHeight || 0;
        let do_ = this.$refs.tableDoContainer?.offsetHeight || 0;
        let footer = this.$refs.tableFooterContainer?.offsetHeight || 0;
        let selection = this.$refs.BaseSelectionBarComponent?.offsetHeight || 0;
        selection = selection ? selection + 8 : selection;
        min = window_ - header * 1 - do_ * 1 - footer * 1 - selection * 1 - 24 - 6;
        console.log(window_, header, do_, footer, selection, 'window_, header, do_, footer, selection');
        console.log(min, 'min')
        min = min > 440 ? min : 440;
      } catch (error) {
        console.warn(error, 'error try catch');
      }
      this.$set(this, 'tableContainerHeight', `${min}px`)
    },
    changePackUp(){
      this.packUp = !this.packUp;
      this.$nextTick(()=>{
        this.knowTableContainerHeight()
      })
    },
    // 清空选择框
    toggleClearSelection() {
      this.multipleSelection = [];
      this.$refs.multipleTable.clearSelection();
    },
    // 获取用户自定义模板
    async getTemplateByTenantId() {
      let fields = []
      await IMApi.getTemplateByTenantId().then(res => {
        if (res.code === 0) {
          res.data.map(v=>{
            v.isSystem = 0
            v.minWidth = 150
            v.dataType = v.formType
            v.field = v.fieldName
            v.label = v.displayName
            // 筛选出四个系统字段
            if(['serviceNumber', 'consultName', 'solveStatus', 'serviceRemark'].includes(v.fieldName)){
              v.isSystem = 1
            }
          });
          // 过滤掉重复字段,系统字段，不需要显示的自定义字段
          fields = res.data.filter((v)=>{
              let res_1 = ['serviceNumber','consultName','','solveStatus'].some(v1=>v1==v.fieldName)
              let res_2 = ['autograph','attachment','info','separator'].some(v2=>v2==v.formType)
              return !res_1 && !res_2
            })
        }
      });
      return fields
    },
    // 播放录音
    playLeaveMessage(url){
      this.$previewVideo(url)
      this.$track.clickStat(this.$track.formatParams('LISTEN_TO_CALL_MESSAGES'))
    }
  },

  components: {
    [SearchPanel.name]: SearchPanel,
    BaseCallPhone,
    AISummary
  }
}
</script>

<style lang="scss" scoped>
.make-phone-call {
  color: #ffffff;
  background: #55b7b4;
  border-color: #55b7b4;
  padding: 3px 5px;
  border-radius: 4px;
  height: 24px;
  line-height: 24px;
  margin-left: 5px;
  i {
    cursor:pointer;
  }
}
.call-center-stats-container {
  min-width: 1024px;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding: 12px;
  .stats-station-header {
    display: flex;
    border-radius: 4px;
    background-color: #FAFAFA;
    padding: 16px;
    align-items: center;
    p {
      flex: 1;
      margin: 0;
      font-size: 16px;
      color: #051a13;
      font-weight: 500;
      i {
        color: #595959;
        margin-left: 10px;
      }
      span {
        font-size: 14px;
        font-weight: 400;
      }
      .money {
        color: #fb602c;
      }
    }
  }

  .stats-station-today-header {
    border-radius: 4px 4px 0 0;
    background-color: #FAFAFA;
    height: 48px;
    line-height: 48px;
    font-size: 16px;
    font-weight: 500;
    color: #051a13;
    align-items: center;
    margin-top: 12px;
    padding-left: 16px;
  }

  .stats-station-today-content {
    display: flex;
    flex-flow: row nowrap;
    border-radius: 4px;
    overflow: hidden;
    padding-top: 1px;
    // margin-bottom: 12px;
    justify-content: flex-end;

    .stats-station-card {
      flex: 1;
      background-color: #fff;
      border-right: 1px solid #eee;
      .card-content {

        p {
          display: flex;
           padding-left: 20px;
        }
        h3 {
           padding-left: 20px;
        }
        .icon-huru{
          color: #6ECF40;
          margin-right: 8px;
        }
        .icon-huchu{
          color: #FFAE00;
          margin-right: 8px;
        }
      }
      .card-bottom {
        background: #FAFAFA;
        height: 80px;
        line-height: 80px;
        padding-left: 20px;
        padding: 10px 0 10px 20px;
        h4 {
          font-weight: 400;
          margin: 0;
          height: 30px;
          line-height: 30px;
        }
      }
      p {
        padding: 15px 0 8px;
        margin: 0;
        font-size: 14px;
      }

      h3 {
        font-weight: 400;
        font-size: 24px;
        line-height: 34px;
        margin: 0;
        padding: 5px 0 20px 0;
      }

      &:first-child {
        margin-left: 0;
      }
    }
  }

  .customer-list-container {
    height: 100%;
    // overflow: auto;

    .panel-title {
      font-size: 16px;
      line-height: 60px;
      padding: 0 25px;
      color: rgb(132, 138, 147);
      border-bottom: 1px solid rgb(242, 248, 247);
      font-weight: normal;
      display: flex;
      justify-content: space-between;
      .iconfont:hover {
        cursor: pointer;
      }
    }
  }

  // search
  .customer-list-search-group-container {
    .advanced-search-function,
    .base-search {
      background: #fff;
      border-radius: 4px 4px 0 0;
    }

    .base-search {
      font-size: 14px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      padding: 16px 16px 0;

      .customer-list-base-search-group {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .el-input {
          width: 300px;
          input {
            width: 300px;
            border-radius: 4px 0 0 4px
          }
        }

        a {
          line-height: 33px;
        }
      }

      .advanced-search-visible-btn {
        font-size: 14px;
        line-height: 32px;
        // @include fontColor();
        color: $color-primary;
        border-color: $color-primary;
        background: #fff;
        padding: 0 13px;
        white-space: nowrap;

        &:hover {
          cursor: pointer;
        }
      }
    }

    .advanced-search-form {
      overflow: auto;
      padding: 10px 0 63px 0;
      height: calc(100% - 51px);

      .form-item-container {
      }

      .two-columns {
        display: flex;
        flex-wrap: wrap;
        .el-form-item {
          width: 50%;
        }
      }

      .el-form-item {
        .el-form-item__content,
        .el-select,
        .base-dist-picker,
        .el-cascader,
        .el-date-editor {
          width: 290px;
        }
      }

      .advanced-search-btn-group {
        display: flex;
        justify-content: flex-end;
        width: 100%;
        position: absolute;
        bottom: 0px;
        background: #fff;
        padding: 15px 20px;

        .base-button {
          margin: 0 10px;
        }
      }
    }

    .advanced-search-function {
      margin-top: 10px;
      padding-bottom: 10px;

      h4 {
        border-bottom: 1px solid #f4f4f4;
        padding: 10px;
      }

      .el-row {
        padding: 5px 0;
      }
      .input-label {
        text-align: right;
        line-height: 32px;
        padding-right: 0;
      }
    }
  }

  // list
  .customer-list-component {
    height: 100%;
    // padding-top: 10px;
    /*min-height: calc(100% - 100px);*/
    border-radius: 4px;
    display: flex;
    flex-direction: column;

    .action-button-group {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: #fff;
      padding: 16px;
      // text-align: right;
    }

    .action-button-group-left {
      display: flex;
      align-items: center;
    }

    .action-button-group-right {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .el-dropdown-btn {
      padding: 0 15px;
      display: inline-block;
      outline: none;
      .iconfont {
        margin-left: 5px;
        font-size: 12px;
      }

      &:hover {
        cursor: pointer;
      }
    }

    .el-table__fixed-right {
      // 操作栏固定到最右侧
      // top: 10px;
    }

    .customer-table {
      // padding: 16px;

      &:before {
        height: 0;
      }

      .customer-table-header th {
        background: #FAFAFA;
        color: $text-color-primary;
        font-weight: normal;
      }

      .view-detail-btn {
        width: 100%;
        @include fontColor();
      }

      .view-detail-btn {
        @include fontColor();
      }

      .select-column .el-checkbox {
        position: relative;
        top: 3px;
      }
    }

    .table-footer {
      display: flex;
      justify-content: space-between;
      padding: 0px 10px 10px 10px;
      background: #fff;
      border-radius: 0 0 4px 4px;

      .list-info {
        font-size: 13px;
        line-height: 32px;
        margin: 0;
        color: #767e89;

        .iconfont {
          position: relative;
          top: 1px;
        }
      }

      .el-pagination__jump {
        margin-left: 0;
      }
    }
  }

  .customer-panel-btn {
    float: right;
    cursor: pointer;
    font-size: 14px;
    margin-right: 5px;

    &:hover {
      @include fontColor();
    }
  }

  // -------- customer selected panel --------
  .customer-selected-count {
    @include fontColor();
    padding: 0 3px;
    width: 15px;
    text-align: center;
    cursor: pointer;
    font-size: 13px;
  }

  .customer-selected-panel {
    font-size: 14px;
    height: calc(100% - 51px);

    .customer-selected-tip {
      padding-top: 80px;

      img {
        display: block;
        width: 240px;
        margin: 0 auto;
      }

      p {
        text-align: center;
        color: #9a9a9a;
        margin: 30px 0 0 0;
        line-height: 20px;
      }
    }

    .customer-selected-list {
      height: 100%;
      padding: 10px;
      overflow-y: auto;

      .customer-selected-row {
        display: flex;
        flex-flow: row nowrap;
        line-height: 36px;
        border-bottom: 1px solid #ebeef5;
        font-size: 13px;

        &:hover {
          background-color: #f5f7fa;

          .customer-selected-delete {
            visibility: visible;
          }
        }
      }

      .customer-selected-head {
        background-color: #f0f5f5;
        color: #333;
        font-size: 14px;
      }

      .customer-selected-sn {
        padding-left: 10px;
        width: 150px;
        @include text-ellipsis;
      }

      .customer-selected-name {
        padding-left: 10px;
        flex: 1;
        @include text-ellipsis;
      }

      .customer-selected-delete {
        width: 36px;
      }

      .customer-selected-row button.customer-selected-delete {
        padding: 0;
        width: 36px;
        height: 36px;
        border: none;
        background-color: transparent;
        outline: none;
        color: #646b78;
        visibility: hidden;

        i {
          font-size: 14px;
        }

        &:hover {
          color: #e84040;
        }
      }
    }
  }

  // operation
  .customer-columns-dropdown-menu {
    max-height: 300px;
    overflow: auto;
    .el-dropdown-menu__item {
      padding: 0;
    }
    .el-checkbox {
      width: 100%;
      padding: 5px 15px;
      margin: 0;
    }
  }

  .operation-bar-container {
    background: #fff;
    border-radius: 0;
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    border-radius: 0 0 4px 4px;
  }

  // superscript
  .customer-name-superscript-td {
    padding: 0 !important;
    & > div {
      height: 43px;
      line-height: 43px !important;
      a {
        display: inline-block;
        height: 43px;
        line-height: 43px;
      }
    }
  }
}
.color-black{
  color: $text-color-primary;
}
.recordId{
  color: $color-primary-light-6;
  cursor: pointer;
}
.recordId:hover{
  text-decoration: underline;
}
.play-btn{
  color: $color-primary-light-6;
  cursor: pointer;
}
.call-center-filter__tab{
  height: 32px;
  margin-left: 12px;
  ::v-deep .el-radio-button__inner{
    padding: 8px 15px;
  }
}
</style>
