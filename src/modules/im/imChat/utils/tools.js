import { isObject } from 'pub-bbx-utils'
import { previewElementImg } from '@src/util/entry.js'
import { getThemeColor } from '@src/util/business/color'

export const emojiNames = [
  '微笑',
  '撇嘴',
  '色',
  '发呆',
  '得意',
  '流泪',
  '害羞',
  '闭嘴',
  '睡',
  '大哭',
  '尴尬',
  '发怒',
  '调皮',
  '呲牙',
  '惊讶',
  '难过',
  '酷',
  '冷汗',
  '抓狂',
  '吐',
  '偷笑',
  '可爱',
  '白眼',
  '傲慢',
  '饥饿',
  '困',
  '惊恐',
  '流汗',
  '憨笑',
  '大兵',
  '奋斗',
  '咒骂',
  '疑问',
  '嘘',
  '晕',
  '折磨',
  '衰',
  '骷髅',
  '敲打',
  '再见',
  '擦汗',
  '抠鼻',
  '鼓掌',
  '糗大了',
  '坏笑',
  '左哼哼',
  '右哼哼',
  '哈欠',
  '鄙视',
  '委屈',
  '快哭了',
  '阴险',
  '亲亲',
  '吓',
  '可怜',
  '菜刀',
  '西瓜',
  '啤酒',
  '篮球',
  '乒乓',
  '咖啡',
  '饭',
  '猪头',
  '玫瑰',
  '凋谢',
  '示爱',
  '爱心',
  '心碎',
  '蛋糕',
  '闪电',
  '炸弹',
  '刀',
  '足球',
  '瓢虫',
  '便便',
  '月亮',
  '太阳',
  '礼物',
  '拥抱',
  '强',
  '弱',
  '握手',
  '胜利',
  '抱拳',
  '勾引',
  '拳头',
  '差劲',
  '爱你',
  'NO',
  'OK',
  '爱情',
  '飞吻',
  '跳跳',
  '发抖',
  '怄火',
  '转圈',
  '磕头',
  '回头',
  '跳绳',
  '挥手',
  '激动',
  '街舞',
  '献吻',
  '左太极',
  '右太极',
];
/**
 * isImage 判断是否图片类型
 * @param {filename} filename 文件名
 * @returns 
 */
export function isImage(filename) {
  if (!filename) return false;
  const fn = filename.toLocaleLowerCase();
  if (/\.(png|bmp|gif|jpg|jpeg|tiff|webp)$/i.test(fn)) {
    return true;
  }
  return false;
}

/**
 * isVideo 判断是否视频类型
 * @param {filename} filename 文件名
 * @returns 
 */
export function isVideo(filename) {
  if (!filename) return false;
  const fn = filename.toLocaleLowerCase();
  if (/\.(avi|wmv|mpeg|mp4|m4v|mov|asf|flv|f4v|rmvb|rm|3gp|vob|webm|ogg)$/i.test(fn)) {
    return true;
  }
  return false;
}

// 获取后缀名
export function getExtension (filename) {
  return filename.substring(filename.lastIndexOf('.') + 1)
}

// 文本转链接和表情
export function replaceURLWithHTMLLinks(text) {
  // 先去掉最后一个\n
  text = text.replace(/\n$/g, '')
  text = text.replace(/\n/g,'<br/>') // 替换成换行标签
  let httpReg = new RegExp(
    '(http[s]{0,1}|ftp)://[a-zA-Z0-9\\.\\-]+\\.([a-zA-Z]{2,4})(:\\d+)?(/[a-zA-Z0-9\\.\\-~!@#$%^&amp;*+?:_/=<>]*)?',
    'gi'
  );

  let emojiReg = /\[[\u0041-\u9FA5]{1,3}\]/gi;

  // 用于判断文本内容超链接
  if (httpReg.test(text)) {
    // 文本地址变为a链接
    text = text.replace(httpReg, function (httpText) {
      return `<a style="color: ${getThemeColor()};" href="${httpText}" target="_blank">${httpText}</a>`;
    });
  }

  // 文本,匹配表情
  if(emojiReg.test(text)){
    text = text.replace(emojiReg, function (res) {
      let word = res.replace(/\[|\]/gi, '');
      let index = emojiNames.indexOf(word);
      if (~index) {
        return `<img src="/resource/images/emoji/${index}.gif" align="middle">`;
      }
      // 没有匹配到表情
      return word;
    });
  }
  
  // 如果存在这种错误的标签, img标签中包含a标签, 则取出a标签中的href, 替换img标签的src
  // <img src="<a href="xxx" target="_blank">
  // 则需要替换为
  // <img src="xxx" alt="xxx">
  text = text.replace(/<img src="<a [^>]*href="([^"]+)"[^>]*>(?:[^<]*)<\/a>"/g, function(match, href) {
    return `<img src="${href}"`;
  });

  // 删除错误的嵌套a标签
  text = text.replace(/<a href="<a [^>]*>(?:[^<]*)<\/a>">/g, function(match) {
    return '';
  });
  text = text.replace(/<a href="<a [^>]*>(?:[^<]*)<\/a>/g, function(match) {
    return '';
  });

  return text;
}

/**
 * 渲染接收人
 * @param {} receiver 接收人列表 [{personalName|email}]
 * @returns 
 */
export function renderReceiver(receiver) {
  return (
    receiver
      ?.map(v => {
        if(isObject(v)){
          const name = v.personalName || '';
          const email = v.email ? `&lt;<span>${v.email}</span>&gt;` : '';
          return name + email;
        }
        return v ? `&lt;${v}&gt;` : '';
      })
      .join(',') ?? ''
  );
}

/**
 * 渲染发送人
 * @param {*} sender {senderName,sender}
 * @returns 
 */
export function renderSender(sender) {
  const senderName = sender?.senderName ?? '';
  const senderEmail = sender.sender ? `&lt;<span>${sender.sender}</span>&gt;` : '';
  return senderName + senderEmail;
}

/**
 * 点击图片元素进行预览
 * @param {*} e 
 */
export function imgDomPreview(e) {
  if(e?.target.nodeName == 'IMG') previewElementImg(e.target.currentSrc)
}