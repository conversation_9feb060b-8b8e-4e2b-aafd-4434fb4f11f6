<template>
    <div>
        <div class="message-notification">
            <el-form
                class="connector-message-content"
                ref="form"
                label-width="80px"
                label-position="left"
                hide-required-asterisk
                :disabled="isViewMode"
            >
                <el-form-item :label="$t('common.connector.trigger.notificationType.label')">
                    <el-radio-group v-model="type">
                        <el-radio label="inside">{{ $t('common.connector.trigger.notificationType.inside') }}</el-radio>
                        <el-radio label="customer">{{ $t('common.connector.trigger.notificationType.customer') }}</el-radio>
                    </el-radio-group>
                </el-form-item>
                <set-message-content
                    ref="setMessageDialogRef"
                    :module="module"
                    :type="type"
                    :disabled="isViewMode"
                    :messageTypeName="state.currentNodeInfoFront.id || ''"
                    :value="dialogVisible"
                    :key="setMessageDialogKey"
                    :biz-type="state.currentNodeInfoFront.type"
                    :biz-type-id="bizTypeId"
                    :biz-type-ids="bizTypeIds"
                    :node-config="state.currentNodeInfoFront.options"
                    :isEdit="isEdit"
                />
            </el-form>
        </div>
        <!-- <footer class="message-footer">
            <el-button @click="saveData" type="primary">保存</el-button>
        </footer> -->
    </div>
</template>

<script>
/* component */
import SetMessageContent from '@src/modules/doMyself/setting/message/components/dialog/SetMessageContent.vue';
import state from '@src/modules/trigger/view/setting/util/state.js';
import { isNotEmpty } from 'pub-bbx-utils';

export default {
    name: 'message_notification',
    inject: ['isDemo', 'isDemoCreate', 'isViewMode', 'getDemoTriggerId'],
    components: {
        SetMessageContent,
    },
    provide() {
        return {
            // 模块 事件、工单、客户、产品、PaaS
            module: 'api',
        };
    },
    computed: {
        state() {
            return state;
        },
        bizTypeId() {
            return state.parentNodeApiIdList?.[0]
        },
        bizTypeIds() {
            try {
                
                const startNodeInfo = this.state?.startNodeInfo || {}
                const startOptionsApiId = startNodeInfo?.options?.apiId

                const parentNodeApiIdList = [...this.state.parentNodeApiIdList]
                if (isNotEmpty(parentNodeApiIdList)) {
                    const isParentAPIListIncludeOptionsApiId = parentNodeApiIdList.includes(startOptionsApiId)
                    if (isParentAPIListIncludeOptionsApiId) {
                        return parentNodeApiIdList
                    }
                    return [startOptionsApiId, ...parentNodeApiIdList]
                }
                
                if (!this.isDemo && !this.isDemoCreate) {
                    return []
                }

                if (startOptionsApiId) {
                    return [startOptionsApiId]
                }

                return []

            } catch (error) {
                return []
            }
        },
        isEdit(){
            return state.editStatus
        }
    },
    created() {
        if (state.currentNodeInfoFront.options?.fieldOptions?.side == null) {
            this.$set(state.currentNodeInfoFront, 'options', {
                ...state.currentNodeInfoFront.options,
                apiId: 'message',
                fieldOptions: {
                    side: 0,
                }
            })
        }
    },
    mounted() {
        if (state.currentNodeInfoFront.options?.fieldOptions?.side === 1) {
            this.type = 'customer';
        }
        state.currentSelectNodePanelContentRef = this
    },
    data() {
        return {
            setMessageDialogKey: 0,
            module: 'api',
            type: 'inside', // 消息类型 inside：内部，customer：客户
            dialogVisible: true, // 弹框显示/隐藏
        };
    },
    methods: {
        async saveData() {
            const params = await this.$refs.setMessageDialogRef.saveNeedValidTab()
            if(!params) return Promise.reject()
            params.side = this.type === 'customer' ? 1 : 0
            params.isEditMessage = true;
            
            this.$set(state.currentNodeInfoFront, 'options', {
                ...state.currentNodeInfoFront.options,
                apiId: 'message',
                fieldOptions: params,
            });
            
            return params
        },
        async setOnlyMessageParams() {
            const setMessageDialogRef = this.$refs.setMessageDialogRef
            if(!setMessageDialogRef) return Promise.reject()
            const { channelTabValidate, channelTabSubmitParam } = await setMessageDialogRef.getModuleValidate()
            const params = await setMessageDialogRef.getParams(channelTabSubmitParam)
            return params
        }
    },
}
</script>

<style scoped lang="scss">
.message-notification {
    width: 100%;
    height: 100%;
    padding: 24px;
    &::after {
        content: '';
        display: block;
        height: 52px;
        width: 100%;
    }
}
::v-deep .message-content {
    .system-mes-tel__content {
        .sys-template-view {
            flex-basis: 230px;
            flex-shrink: 0;
            flex-grow: 0;
            max-width: 230px !important;
        }

        .content-right {
            flex: 1;
            width: 250px !important;
            margin-left: 16px;

            .template-opetate {
                padding: 0 12px;
            }
        }
    }
    .item-content__txt {
        max-width: 100% !important;
    }
}
</style>

<style scoped lang="scss">
.message-footer {
    padding-right: 16px;
    align-items: center;
    background-color: #fff;
    border-top: 1px solid #e8e8e8;
    bottom: 0;
    display: flex;
    height: 52px;
    justify-content: flex-end;
    padding-right: 16px;
    position: absolute;
    width: 100%;
    z-index: 2;
}
</style>