import { defineComponent, onMounted, ref, onBeforeUnmount, watch, VNode, inject, onBeforeMount } from "vue";


import MainNodeProcessWrap from '@src/modules/trigger/components/ProcessNodeChart/components/MainNodeProcessWrap'
/* enum */
import { TriggerType } from '@src/modules/trigger/model/enum/index.ts'
import { NodeTypeEnum, AllNode, AllNodeType, AddNodeT } from '@src/modules/trigger/model/enum/index.ts'
import { useState, useCurrentInstance, useRouter } from '@src/modules/trigger/hooks/useVC'
import type {ProcessNodeListItem} from '@src/modules/trigger/model/interface/index.ts'
import { PageRoutesTypeEnum } from 'pub-bbx-global/pageType/dist/enum/PageRoutesEnum'
/* type */
import type { deleteMode } from '@src/modules/trigger/model/type/index.ts'

/* utils */
import _, { cloneDeep } from 'lodash'
import { uuid } from '@src/util/lang/string';
import state from '@src/modules/trigger/view/setting/util/state.js'
import {convertProcessCanvasDataForServer, convertServerDataForProcessCanvas} from '@src/modules/trigger/utils/convert.ts'
import { getRootWindow } from '@src/util/dom'
import '@src/modules/trigger/components/ProcessNodeChart/style/wrap.scss'
import { message } from '@src/util/message'
import { openAccurateTab } from '@src/util/platform'
import platform from '@src/platform';
import { codeGray,agentGray } from '@src/modules/trigger/view/setting/util/codeGray.js'
import { compatibility } from '@src/modules/trigger/utils/compatibility.ts'
/* code */
import { executeCode } from '@src/modules/connector/util/sampleCode.ts'
/* hooks */
import { getNodeType } from '@src/modules/trigger/utils/index.ts'
import { useTag } from '@src/modules/trigger/hooks/useTag.ts'
import { useSmartFlowTag } from '@src/modules/trigger/hooks/useSmartFlowTag.ts'
import { useClickInSide } from '@src/modules/trigger/hooks/useClickInSide.ts'
/* model */
import { TriggerFlowData } from '@src/modules/trigger/model'
/* http */
import{ createTrigger, getTriggerInfo, editTrigger, getTriggerInfoDemo, getTriggerInfoDemoInside, createTriggerDemo }  from '@src/modules/trigger/api/trigger.js'
import { getLabelTriggerDetail } from '@src/modules/trigger/api/intelltags.js'
import * as intelligentTagApi from '@src/modules/trigger/api/intelltags.js'
import { createSettleTrigger, updateSettleTrigger, getSettleTriggerInfo } from '@src/modules/smartSettlement/api/index.js'
/* middleware */
import { triggerValidate, validateHandler } from '@src/modules/trigger/middleware/index.ts'

export default defineComponent({
    name: TriggerType.triggerDesignProcessNodeChat,
    props: {
        triggerId: {
            type: String,
            default: ''
        },
        settlementShow: {
            type: Boolean,
            default: false
        },
        loading: {
            type: Boolean,
            default: false
        }
    },
    emit: ['loading'],
    setup(props, { emit }) {
        
        const { query } = useRouter()
        const startUuid = uuid()
        const executeUuid = uuid()
        const isDemo = inject('isDemo')
        const isDemoCreate = inject('isDemoCreate')
        const getDemoTriggerId = inject('getDemoTriggerId') as () => string
        // 智能流程标签
        const isSmartFlowTag = inject('isSmartFlowTag') as Boolean
        const { executeInit, startNodeInsertInfo, isTriggerSmartFlowSucc, messageTriggerSmartFlow } = useSmartFlowTag();
        const { isInsideMain, isSupportComposePath } = useClickInSide()
        // 是否支持composePath
        const _isSupportComposePath = isSupportComposePath()
        
        const [chartList, setChartList] = useState<ProcessNodeListItem[]>([{
            "type": NodeTypeEnum.START_NODE,
            "name":  "",
            "content": "",
            "children": [],
            "uniqueId": startUuid,
            "id": '',
            "options": {
                "requiredCheck": true
            },
            "parentNode": null
        }])
        
        const [currentCtx, setData] = useCurrentInstance()
        // 智能标签状态
        const { tagShow } = useTag()

        const serverData = ref<TriggerFlowData | any>({}) // 后端结构数据用于前端数据结构转换函数中

        /**
         * @description 获取新建灰度
         */
        const getCreateGray = () => {
            if (props.triggerId) return  Promise.resolve();
            return codeGray().then(res => {
                setStateGray(res)
            }).catch(() => {
                setStateGray(false)
            })
        }

        /**
         * @description 获取agent灰度
         * @returns 
         */
        const getCreateGrayAgent = () => {
            if (props.triggerId) return  Promise.resolve();
            return agentGray().then(res => {
                setAgentGray(res)
            }).catch(() => {
                setAgentGray(false)
            })
        }

        /**
         * @description 触发器数据转前端数据处理函数
         */
        const getTriggerInfoFront = async () => {
            try {
               
                emit('loading', true)
               
                const params = {
                    triggerId: props.triggerId
                }
                const demoParams = {
                    ...params,
                    demoTriggerId: getDemoTriggerId && getDemoTriggerId()
                }
               
                let getTriggerInfoPromise = null
                if (isDemoCreate) {
                    getTriggerInfoPromise = getTriggerInfoDemoInside(demoParams)
                } else if (isDemo) {
                    getTriggerInfoPromise = getTriggerInfoDemo(demoParams)
                } else if (tagShow) {
                    getTriggerInfoPromise = getLabelTriggerDetail(params)
                } else if (props.settlementShow) {
                    getTriggerInfoPromise = getSettleTriggerInfo(params)
                } else {
                    getTriggerInfoPromise = getTriggerInfo(params)
                }
                
                if (!getTriggerInfoPromise) {
                    throw new Error('获取触发器详情失败')
                }
                
                const [res, grayRes,agentRes] = await Promise.all([getTriggerInfoPromise, codeGray(),agentGray()])
                
                if (!res.success) {
                    message.error(res.message)
                    return
                }
                if (!res.data) return
                setStateGray(grayRes)
                setAgentGray(agentRes)
                serverData.value = res.data
                state.serverData = cloneDeep(res.data)
                setExecuteWay(res.data)
                setChartListData(convertServerDataForProcessCanvas(res.data as TriggerFlowData))
                emit('loading', false)
            } catch (error) {
                console.error(error)
            }
            
        }

        /**
         * @description 设置触发器灰度
         * @param grayData 
         */
        const setStateGray = (gray: boolean) => {
            currentCtx.$set(state, 'trigger_code_gray', gray)
        }

        /**
         * @description 设置agent灰度
         * 
         */
        const setAgentGray = (gray: boolean) => {
            currentCtx.$set(state, 'trigger_AI_V2', gray)
        }

        /**
         * @description 重新赋值树形结构数据
         * @param data 
         */
        function setChartListData(data: any) {
            if (!data) return
            setChartList(data) // 设置值
            addEndNode() // 添加结束节点
        }

        /**
         * @description 编辑情况下根据后端获取触发方式和名称
         * @param data 执行方式
         */
        function setExecuteWay(data: any) {
            state.executeWay = data.executeWay
            state.currentTriggerInfo.intelligentTagsInit = data.needInit ?? false
            // @ts-ignore
            currentCtx.$set(state.currentTriggerInfo, 'name', data.name)
        }

        // 如果当前存在triggerId就获取详情
        watch(() => props.triggerId, async (newVal) => {
            if (newVal) {
                await getTriggerInfoFront()
                // 编辑进入新页面之后校验一下数据
                validateHandler(chartList.value, currentCtx)
            }
        }, { immediate: true })

        // 侦听当如果修改了开始节点的应用和对象，这将会将开始节点下面的子节点数据清空
        watch([
            () => chartList.value[0]?.options?.appId,
            () => chartList.value[0]?.options?.apiId,
        ], ([newAppId, newApiId], [oldAppId, oldApiId]) => {
            // console.log(newAppId, oldAppId)
            // console.log(newApiId, oldApiId)
            // 新建时：如果当前元素之前已经更改且新值不等于旧值 重新清空子节点数据
            if (
                (oldAppId && newAppId && newAppId !== oldAppId) 
                || (oldApiId && newApiId && oldApiId !== newApiId)
            ) {
                chartList.value[0].children = []
            }
        }, { immediate: true })

        /**
         * @description 创建节点模板
         * @param type  节点类型
         * @param uniqueId id
         * @param newNodeType  
         */
        const createTemplateNode = (type: NodeTypeEnum, uniqueId: string, newNodeType?: AddNodeT) => {
            const template: ProcessNodeListItem = {
                type,
                name: '',
                content: '',
                children: [],
                id: '',
                uniqueId,
                options: {},
                parentNode: null
            };
            // 新建添加条件节点的时候，该节点options配置对象里添加一个属性conditionList
            if (type === NodeTypeEnum.CONDITION_NODE) {
                template.options = {
                    conditionList: []
                }
            }
            if ([AllNode.CODE, AllNode.MESSAGE, AllNode.INTELL_TAG, AllNode.SETTLEMENT_NODE,AllNode.AGENT].includes(newNodeType)) {
                template.options.type = newNodeType;
                if (newNodeType === AllNode.CODE) {
                    template.options.apiId = 'code';
                    template.options.fieldOptions = {
                        convertCode: executeCode
                    }
                }
                if(newNodeType === AllNode.AGENT) {
                    template.options.apiId = 'agent';
                }
            }
            // 默认如果当前类型是条件节点或消息节点默认校验通过
            if ([AllNode.CONDITION_NODE, AllNode.MESSAGE, AllNode.INTELL_TAG, AllNode.SETTLEMENT_NODE].includes(newNodeType)) {
                template.options.requiredCheck = true
            }
            return template;
        };

        /**
         * 处理弹窗添加节点逻辑功能函数
         * 
         * @TODO 需要优化 本次使用的都是手动拼接 可以实现一个自动插入函数，只需要输入节点id和插入的节点信息自动传参
         *       时间关系 本次先手动简单一点。。。。😥
         * 
         * 
         * @param node 当前节点信息
         * @param newNode 添加新节点数据
         */
        const addNodeChangeHandler = (node: ProcessNodeListItem, newNode: {type: AddNodeT}) => {
            let ids = uuid()
        
            const templateCondiation = createTemplateNode(NodeTypeEnum.CONDITION, `condition-${ids}`); // 这里添加condition-其实就是唯一标识
            const templateCondiationNode = createTemplateNode(NodeTypeEnum.CONDITION_NODE, ids, newNode.type);
            const templateNode = createTemplateNode(NodeTypeEnum.NODE, ids, newNode.type);

            const _targetNode = node;
            if (!_targetNode) return
            // 存在拷贝一份，防止引用类型导致的错误
            const targetNode = _.cloneDeep(_targetNode);
            const {children, ...rest} = targetNode
            if (rest.parentNode && Object.keys(rest.parentNode).length) {
                rest.parentNode = null;
            }

            // 如果是智能结算-待结算池 且属于没有父级条件节点
            // 智能结算节点 因为是一个固定格式 一个条件+一个智能结算节点 的格式 我们强制判断一下
            // 下面判断逻辑是 如果下面是一个普通节点 而不是一个条件组节点包裹的说明 此时固定格式里面的第一个条件组已经被删除了 我们直接添加一个新的条件组节点包裹即可
            if (AllNode.SETTLEMENT_NODE === newNode.type && targetNode.children.some(item => item.type === 'node')) {
                
                // 创建一个条件组节点
                const condition = createTemplateNode(NodeTypeEnum.CONDITION, `condition-${uuid()}`);
                const conditionNode = createTemplateNode(NodeTypeEnum.CONDITION_NODE, uuid(), NodeTypeEnum.CONDITION_NODE);
                
                // 保存原始节点的子节点
                const originalChild = targetNode.children[0];
                
                // 设置条件节点的父节点关系
                condition.parentNode = {
                    ...targetNode,
                    children: void 0,
                    parentNode: null
                } as Omit<ProcessNodeListItem, "children">;
                
                // 设置条件节点节点的父节点关系
                conditionNode.parentNode = {
                    ...condition,
                    children: void 0,
                    parentNode: null
                } as Omit<ProcessNodeListItem, "children">;
                
                // 设置原始子节点的父节点关系
                originalChild.parentNode = {
                    ...conditionNode,
                    children: void 0,
                    parentNode: null
                } as Omit<ProcessNodeListItem, "children">;
                
                // 构建节点树
                conditionNode.children = [originalChild];
                condition.children = [conditionNode];
                
                // 更新目标节点的子节点
                _targetNode.children = [condition];
                
                return;
            }
            // 如果新增的是智能标签或者智能结算-待结算池
            if ([AllNode.INTELL_TAG, AllNode.SETTLEMENT_NODE].includes(newNode.type)) {
                const condition = createTemplateNode(NodeTypeEnum.CONDITION, `condition-${uuid()}`);
                const conditionNode = createTemplateNode(NodeTypeEnum.CONDITION_NODE, uuid(), NodeTypeEnum.CONDITION_NODE);
                const node = createTemplateNode(NodeTypeEnum.NODE, uuid(), newNode.type);
                const { children: _sc, ...startRest } = targetNode

                // 如果开始节点下面存在condition节点
                if (targetNode.children.some(item => item.type === NodeTypeEnum.CONDITION)) {
                    const {children, ...noChildrenObj} = targetNode.children[0]
                    noChildrenObj.parentNode = null
                    conditionNode.parentNode = noChildrenObj
                    const { children: _c, ...rest2} = conditionNode
                    rest2.parentNode = null
                    node.parentNode = rest2
                    conditionNode.children.push(node);

                    _targetNode.children[0]?.children.push(conditionNode)
                    return
                }
                // 如果开始节点下面不存在condition节点
                const { children: _c, ...rest2 } = condition
                const { children: _c2, ...rest3} = conditionNode
                condition.parentNode = startRest
                
                conditionNode.parentNode = rest2
                conditionNode.parentNode.parentNode = null

                node.parentNode = rest3
                node.parentNode.parentNode = null
                conditionNode.children.push(node);
                condition.children.push(conditionNode);
                _targetNode.children.push(condition)

                return
            }
            if (newNode.type !== NodeTypeEnum.CONDITION_NODE) {
                
                templateNode.children = targetNode.children
                templateNode.parentNode = rest

                if (Array.isArray(targetNode.children) && targetNode.children.length > 0) {
                    const { children , ...rest} = templateNode
                    templateNode.children.forEach(item => {
                      item.parentNode = { ...rest, parentNode: null}  
                    })
                }

                _targetNode.children = [templateNode]

            } else {
                // 如果当前节点的子节点存在condition则直接添加，如果没有需要在创建条件节点的同时生成condition来包裹条件节点
                if (targetNode?.children.some(item => item.type === NodeTypeEnum.CONDITION)) {
                    const {children, ...noChildrenObj} = targetNode.children[0]
                    if (Reflect.has(noChildrenObj, 'parentNode') && noChildrenObj.parentNode) {
                        noChildrenObj.parentNode = null
                    }
                    templateCondiationNode.parentNode = noChildrenObj
                    // 将条件节点直接放到condiation包裹下面的children的数组后面
                    _targetNode.children[0]?.children.push(templateCondiationNode)
                } else {
                    templateCondiationNode.children = targetNode.children
                    templateCondiation.parentNode = rest
                    
                    if (Array.isArray(templateCondiationNode.children) && templateCondiationNode.children.length > 0) {
                        const { children , ...rest} = templateCondiationNode
                        templateCondiationNode.children.forEach(item => {
                          item.parentNode = { ...rest, parentNode: null}  
                        })
                    }

                    templateCondiationNode.parentNode = _.cloneDeep({...templateCondiation, parentNode: null})

                    templateCondiation.children.push(templateCondiationNode)
                    _targetNode.children = [templateCondiation]
                }
            }
        }

        /**
         * @description 通过uniqueId查找指定节点
         * @param id 
         * @param nodes 
         */
        function findNodeById(id: string, nodes: ProcessNodeListItem[]): ProcessNodeListItem | null {
            for (let node of nodes) {
                if (node.uniqueId === id) {
                    return node;
                }
                if (node.children && node.children.length > 0) {
                    const foundNode = findNodeById(id, node.children);
                    if (foundNode) {
                        return foundNode;
                    }
                }
            }
            return null;
        }

        /**
         * @description 查找当前节点的父节点
         * @param id 要查找的节点的id
         * @param nodes 查找数据源
         * @param parent 当前节点的父节点 （暂不需要
         */
        function findParentNodeById(id: string, nodes: ProcessNodeListItem[], parent: ProcessNodeListItem | null = null): ProcessNodeListItem | null {
            for (let node of nodes) {
                if (node.children && node.children.some(child => child.uniqueId === id)) {
                    return node;
                }
                if (node.children && node.children.length > 0) {
                    const foundParent = findParentNodeById(id, node.children, node);
                    if (foundParent) {
                        return foundParent;
                    }
                }
            }
            return null;
        }
        
        /**
         * @description 删除节点逻辑
         * @param node 当前节点数据
         * @param mode 删除当前数据模式
         */
        const deleteNodeChangeHandler = (node: ProcessNodeListItem, mode: deleteMode) => {
            const { uniqueId, type } = node
            const _nodeT = getNodeType(node) // 当前节点类型
            const targetNode = node;
            const targetNodeParent = findParentNodeById(uniqueId, chartList.value)
            if (!targetNode || !targetNodeParent) return
          
            if (mode === 'all') {
                const parentParentNodeUniqueId = targetNodeParent.uniqueId
                if (node.type === NodeTypeEnum.CONDITION_NODE) {
                    if (targetNodeParent.children.length >1) {
                        targetNodeParent.children = targetNodeParent.children.filter(child => child.uniqueId !== targetNode.uniqueId);
                    } else{
                        const parentNode = findParentNodeById(parentParentNodeUniqueId, chartList.value)
                        if (parentNode) {
                            parentNode.children = []
                        }
                    }
                    return
                }
                targetNodeParent.children = targetNodeParent.children.filter(child => child.uniqueId !== targetNode.uniqueId);
            }
            if (mode === 'current') {
                const { parentNode, children: nodeChildren } = node
                // @ts-ignore
                if (node.type === NodeTypeEnum.NODE) {
                    if (nodeChildren.length === 0) {
                        targetNodeParent.children = targetNodeParent.children.filter(child => child.uniqueId !== targetNode.uniqueId);
                        return
                    }
                    if (parentNode?.type === NodeTypeEnum.NODE || parentNode?.type === NodeTypeEnum.START_NODE) {
                        const { children, ...rest} = targetNodeParent
                        rest.parentNode = null
                        const child = targetNode.children[0]
                        child.parentNode = rest
                        // 如果当前节点是执行动作则清空下面数据，否则不请客
                        let clearOptionsChild = ([NodeTypeEnum.NODE].includes(_nodeT) ? clearCurrentNodeOptions(child) : child);
                        targetNodeParent.children = [clearOptionsChild]
                    }
                    if (parentNode?.type === NodeTypeEnum.CONDITION_NODE) {
                        if (nodeChildren[0].type !== NodeTypeEnum.CONDITION) {
                            const { children, ...rest} = targetNodeParent
                            rest.parentNode = null
                            const child = targetNode.children[0]
                            child.parentNode = rest
                            // 如果当前节点是执行动作则清空下面数据，否则不请客
                            let clearOptionsChild = ([NodeTypeEnum.NODE].includes(_nodeT) ? clearCurrentNodeOptions(child) : child);
                            targetNodeParent.children = [clearOptionsChild]
                        }
                        if (nodeChildren[0].type === NodeTypeEnum.CONDITION) {
                            targetNodeParent.children = []
                        }
                    }
                    return
                }
                if (node.type === NodeTypeEnum.CONDITION_NODE) {
                    if (nodeChildren.length > 0) {
                        const parentNodeId = parentNode.uniqueId
                        const parentParentNode = findParentNodeById(parentNodeId, chartList.value)
                        if (!parentParentNode) return
                        const { children, ...rest } = parentParentNode
                        rest.parentNode = null
                        const childConditionGroupInConditionNode = targetNode.children[0]
                        childConditionGroupInConditionNode.parentNode = rest
                        // let clearOptionsChild = clearCurrentNodeOptions(childConditionGroupInConditionNode)
                        parentParentNode.children = [childConditionGroupInConditionNode]
                    } else {
                        const parentParentNodeUniqueId = targetNodeParent.uniqueId
                        if (targetNodeParent.children.length >1) {
                            targetNodeParent.children = targetNodeParent.children.filter(child => child.uniqueId !== targetNode.uniqueId);
                        } else{
                            const parentNode = findParentNodeById(parentParentNodeUniqueId, chartList.value)
                            if (parentNode) {
                                parentNode.children = []
                            }
                        }
                    }
                    try {
                        deleteConditionLine(node, serverData.value)
                    } catch(err) {
                        console.error('【trigger delete line node error】:', err)
                    }
                }
            }
        }

        /**
         * @description 当且仅当删除当个条件时，删除条件线
         */
        const deleteConditionLine = (node: ProcessNodeListItem, serverData: TriggerFlowData) => {
            const deleteLineId = node.id || node.uniqueId
            const lineID = serverData?.lineList?.findIndex(item => item.id === deleteLineId)
            if (lineID >= 0) {
                serverData.lineList[lineID].options = {
                    ...serverData.lineList[lineID].options,
                    conditionList: null
                }
            }
        }

        /**
         * @description 清空当前节点下面的options
         * @param data 
         */ 
        const clearCurrentNodeOptions = (data: ProcessNodeListItem) => {
            if (!data) return null;
        
            const clearOptions = (node: ProcessNodeListItem) => {
                if ([NodeTypeEnum.NODE].includes(getNodeType(node))) {
                    node.options = {};
                    // TODO 也可以不清除父节点的options，目前没什么用，只用到了uniqueId
                    // if (node.parentNode) {
                    //     node.parentNode.options = {}
                    // }
                }
                if ([NodeTypeEnum.CONDITION_NODE].includes(getNodeType(node))) {
                    node.options = {
                        conditionList: []
                    }
                    node.content = ''
                }
                
                if (node.children && node.children.length > 0) {
                    node.children.forEach(child => clearOptions(child));
                }
            };
        
            const dataCopy = _.cloneDeep(data);
        
            clearOptions(dataCopy);
        
            return dataCopy;
        };

        /**
         * @description 更新节点逻辑
         * @param nodeId 
         * @param newNode 
         */
        const updateNodeChangeHandler = (nodeId: string, newNode: any) => {
            let findUpdateNodeInfo = findNodeById(nodeId, chartList.value)
            if (!findUpdateNodeInfo) return
            findUpdateNodeInfo = newNode
            // Object.assign(findUpdateNodeInfo, newNode)
        }

        /**
         * @description 获取除自己以外父级节点 apiId 数组数据（不含 message 和 code）
         * @param node 当前节点信息
         * @param chartList 当前流程图数据
         */
        const getParentApiIds = (node: ProcessNodeListItem, chartList: ProcessNodeListItem[]): (string | null)[] => {
            let validApiIds: (string | null)[] = [];
        
            function traverseParent(id: string | undefined) {
                if (!id) return;
        
                const node = findNodeById(id, chartList);
                if (!node) return;
        
                if ((getNodeType(node) === 'condition-node' || getNodeType(node) === 'condition')) {
                    traverseParent(node?.parentNode?.uniqueId);
                    return
                }

                if (getNodeType(node) === 'code' || getNodeType(node) === 'message') {
                    traverseParent(node?.parentNode?.uniqueId);
                    return
                }

                validApiIds.push(node.options.apiId);
        
                traverseParent(node?.parentNode?.uniqueId);
            }
        
            traverseParent(node?.parentNode?.uniqueId);
        
            return validApiIds;
        };
        
        /**
         * @description 创建触发器
         */
        const createTriggerHandler = _.debounce(async function() {
            try {
                // 这里是了防止当前点击的节点是消息节点的时候直接保存没传给后端
                await validateMessageNode()
                if (!chartList.value || chartList.value.length === 0) {
                    console.warn("请先添加节点")
                    return
                }
                state.buttonStatus = true
                
                const vailRes = triggerValidate(chartList.value, currentCtx)
                if (!vailRes) return
                
                let msg = convertProcessCanvasDataForServer(chartList.value)
                if (msg && msg.executeWay) {
                    Object.assign(msg.executeWay, state.executeWay);
                    msg.name = state.currentTriggerInfo.name;
                    msg.remarks = state.currentTriggerInfo.remarks;
                    if (tagShow) {
                        state.currentTriggerInfo.robotId && (msg.robotId = state.currentTriggerInfo.robotId);
                        msg.needInit = state.currentTriggerInfo.intelligentTagsInit;
                    }
                }
                
                if (isDemoCreate) {
                    msg.desc = serverData.value.desc
                }
                
                // 如果存在前后不兼容数据，则进行兼容处理
                // @ts-ignore
                compatibility(msg, state._compatibilityList)
                
                let createPromise = null
              
                if (isDemoCreate) {
                    const demoTriggerId = props.triggerId
                    createPromise = createTriggerDemo(msg, demoTriggerId)
                } else if (tagShow) {
                    createPromise = intelligentTagApi.createLabelTrigger(msg)
                } else if (props.settlementShow) {
                    createPromise = createSettleTrigger(msg)
                } else {
                    createPromise = createTrigger(msg)
                }
                
                if (!createPromise) {
                    throw new Error('未找到创建触发器方法')
                }
                
                const res = await createPromise
               
                if (!res.success) {
                    message.error(res.message)
                    return
                }
                message.success('创建成功')
                if (props.settlementShow) {
                    return currentCtx.$eventBus.$emit('triggerCreateSuccess', res.data.triggerId)
                }
                goTriggerIdDetail(res.data.triggerId, {
                    robotId: state.currentTriggerInfo.robotId,
                    labelInfo: {
                        noRefresh: true
                    }
                })
                if (!tagShow) {
                    refreshTab()
                }
            } catch (error) {
                console.error(error)
            } finally {
                state.buttonStatus = false
            }
        }, 100)

        /**
         * @description 刷新当前tab
         */
        const refreshTab = () => {
            platform.refreshTab(window.frameElement.getAttribute('fromId'));
        }

        const closeTab = () => {
            const id = window.frameElement?.dataset?.id
            platform.closeTab(id);
        }

        const refreshListOfRules = (info: { noRefresh?: boolean } = {}) => {
            getRootWindow(window)?.postMessage({
                action: 'shb.label.list',
                data: {
                    type: info?.noRefresh ? 'refresh' : 'noRefresh'
                }
            }, '*')
        }

        const goTriggerIdDetail = (triggerId: string, value?: {robotId?: string, labelInfo?: { noRefresh: boolean }}) => {
            const fromId = window.frameElement.getAttribute('id') || ''
            if (tagShow) {
                closeTab()
                // 如果是编辑就执行跳转
                state.editStatus && openAccurateTab({
                    type: PageRoutesTypeEnum.pageTriggerDesignLabel,
                    params: `triggerId=${triggerId}&tag=true&robotId=${value?.robotId}&timestamp=${new Date().getTime()}`,
                    titleKey: triggerId || '',
                    key: triggerId || '',
                    fromId
                })
                openAccurateTab({
                    fromId,
                    type: PageRoutesTypeEnum.intelligentTags
                })
                refreshListOfRules(value?.labelInfo)
                return
            }
            openAccurateTab({
                type: PageRoutesTypeEnum.PageTriggerDesign,
                params: `triggerId=${triggerId}&timestamp=${new Date().getTime()}`,
                fromId
            })
        }

        /**
         * @description 编辑触发器
         */
        const editTriggerHandler = async () => {
            try {
                // 这里是了防止当前点击的节点是消息节点的时候直接保存没传给后端
                await validateMessageNode()
                if (!chartList.value || chartList.value.length === 0) {
                    console.warn("请先添加节点")
                    return
                }
                state.buttonStatus = true

                const vailRes = triggerValidate(chartList.value, currentCtx)
                if (!vailRes) return

                let msg = convertProcessCanvasDataForServer(chartList.value, serverData.value)
                if (msg && msg.executeWay) {
                    Object.assign(msg.executeWay, state.executeWay);
                    msg.name = state.currentTriggerInfo.name;
                    if (tagShow) {
                        msg.needInit = state.currentTriggerInfo.intelligentTagsInit;
                        state.currentTriggerInfo.robotId && (msg.robotId = state.currentTriggerInfo.robotId);
                    }
                }
                // 如果存在前后不兼容数据，则进行兼容处理
                // @ts-ignore
                compatibility(msg, state._compatibilityList)
                let updatePromise = null
                if (tagShow) {
                    updatePromise = intelligentTagApi.updateLabelTrigger(msg)
                } else if (props.settlementShow) {
                    updatePromise = updateSettleTrigger(msg)
                } else {
                    updatePromise = editTrigger(msg)
                }
                const res = await updatePromise
                if (!res.success) {
                    message.error(res.message || '保存错误')
                    return
                }
                message.success('保存成功')
                if (props.settlementShow) {
                    return currentCtx.$eventBus.$emit('triggerUpdateSuccess', res.data.triggerId)
                }
                goTriggerIdDetail(res.data.triggerId, {
                    robotId: state.currentTriggerInfo.robotId,
                    labelInfo: {
                        noRefresh: true
                    }
                })
                if (!tagShow) {
                    refreshTab()
                }
            } catch (error) {
                console.error(error)
            } finally {
                state.buttonStatus = false
            }
        }

        /**
         * @description 查到当前节点上所有父节点数据的节点id
         * @param nodeId 节点id
         */
        const findParentApiList = (nodeId: string) => {
            const currentNode = findNodeById(nodeId, chartList.value)
            if (!currentNode) return
            const resList = getParentApiIds(currentNode, chartList.value)
            // @ts-ignore
            state.parentNodeApiIdList = resList
        }

        /**
         * @description 初始化第二个节点
         */
        const initSecondNode = () => {
            const {children, ...rest} = chartList.value[0]
            chartList.value[0].children.push({
                "type": NodeTypeEnum.NODE,
                "name":  "",
                "content": "",
                "children": [

                ],
                "uniqueId": executeUuid,
                "id": '',
                "options": {
                    "requiredCheck": true
                },
                "parentNode": rest
            })
        }

        /**
         * @description 初始化定制的自定义节点
         */
        const initCustomizedSecondNode = (nodeType: AllNodeType) => {
            if (!nodeType) return
            const {children, ...rest} = chartList.value[0]

            const initTagCondiation = createTemplateNode(NodeTypeEnum.CONDITION, uuid())
            initTagCondiation.parentNode = rest
            const { children: _c, ..._rest  }  = initTagCondiation
            _rest.parentNode = null as any
            const initTagConditionChildrenNode = createTemplateNode(NodeTypeEnum.CONDITION_NODE, uuid(), NodeTypeEnum.CONDITION_NODE)
            initTagConditionChildrenNode.parentNode = _rest
            const { children: _c1, ...rest2 } = initTagConditionChildrenNode
            rest2.parentNode = null as any
            // 初始化标签节点

            const initTagNode = createTemplateNode(NodeTypeEnum.NODE, uuid(), nodeType)
            initTagNode.parentNode = rest2
            
            initTagConditionChildrenNode.children.push(initTagNode)
            initTagCondiation.children.push(initTagConditionChildrenNode as unknown as any)
            chartList.value[0].children.push(initTagCondiation)
        }

        /**
         * @description 添加结束节点
         */
        const addEndNode = () => {
            chartList.value.push({
                type: NodeTypeEnum.END_NODE,
                name: '',
                content: '',
                uniqueId: 'end',
                id: 'end',
                children: [],
                options: {}
            })
        }
        if (tagShow) {
            initCustomizedSecondNode(AllNode.INTELL_TAG)
        } else if (props.settlementShow) {
            initCustomizedSecondNode(AllNode.SETTLEMENT_NODE)
        } else {
            initSecondNode()
        }
        addEndNode()


        /**
         * @des 相关校验对应的消息节点事件（比如有没有填写等）
         * @returns {any}
         */
        const validateMessageNode = async ()=> {
            return new Promise((resolve, reject)=> {
                try {
                    if(currentNodeType() === 'message') {
                        if(state.currentSelectNodePanelContentRef) {
                            return (state.currentSelectNodePanelContentRef as unknown as any)?.saveData()
                                .then(()=> {
                                    resolve(true)
                                }).catch(()=> reject(false))
                        }
                    }   
                    resolve(true)
                } catch (error) {
                    reject(error)
                }
            })
        }       

        /**
         * @description 清除当前节点状态数据
         */
        const clearCurrentNodeStateData = async () => {
                try {
                    await validateMessageNode()
                    state.data.changeOpenPanel = false
                    state.currentSelectNode = ''
                    currentCtx.$eventBus.$emit('trigger-design-validate')
                } catch (error) {
                    console.log(error)
                }
        }

        /**
         * @description 简单封装查找到的的节点中的当前节点类型
         */
        const currentNodeType = () => {
            if (!state.currentSelectNode) return
            const currentNode = findNodeById(state.currentSelectNode, chartList.value)
            if (!currentNode) return
            return getNodeType(currentNode) || ''
        }

        const handlerClickOutSide = (e: Event) => {
            const whiteDom = ['.trigger-config-panel', '.process-node-normal__box-wrap', '.range-scale', '.temp-textarea','.layx-window', '.el-message-box','.trigger-design-header__right']
            if (!whiteDom.some(selector => e.target.closest(selector))) {
                clearCurrentNodeStateData()
            }

        }

        /**
         * @des 处理相关节点点击事件
         * @param {any} newNode:ProcessNodeListItem
         * @param {any} oldNode:ProcessNodeListItem
         * @returns {any}
         */
        const handleNodeClick = async (newNode: ProcessNodeListItem, oldNode: ProcessNodeListItem)=> {
            if(getNodeType(oldNode) === 'message') {
                try {
                    const params = await (state.currentSelectNodePanelContentRef as unknown as any)?.setOnlyMessageParams()
                    params.isEditMessage = true
                    oldNode.options = {
                        ...oldNode.options,
                        apiId: 'message',
                        fieldOptions: params,
                    }
                } catch (error) {
                    console.error('[setOnlyMessageParams error]', error)
                }
            }
        }

        /**
         * @description 鼠标移动事件
         * @param {Event} event
         */
        let mouseMoveNodeClickHandler = (event: Event) => {
            if (_isSupportComposePath) {
                let isInside = isInsideMain(event, {
                    target: document.querySelectorAll('.process-node-normal__box-wrap') as unknown as any
                })
                if (!isInside) {
                    validateHandler(chartList.value, currentCtx)
                }
                return
            }
            if (!['.process-node-normal__box-wrap'].some(selector => {
                if (!event.target) return false;
                return (event.target as Element).closest(selector)
            })) {
                validateHandler(chartList.value, currentCtx)
            }
        }
        // create throttle function
        const throttleMouseMoveNodeClickHandler = _.throttle(mouseMoveNodeClickHandler, 100)


        onMounted(() => {
            document.querySelector('.trigger-design-box')?.addEventListener('click', handlerClickOutSide)
            document.querySelector('.trigger-design-box')?.addEventListener('mousemove', throttleMouseMoveNodeClickHandler)
            currentCtx?.$eventBus.$on('trigger-design-addNodeChange', (node: ProcessNodeListItem, newNode: any) => {
                addNodeChangeHandler(node, newNode)
            })
            currentCtx?.$eventBus.$on('trigger-design-deleteNodeChange', (node: ProcessNodeListItem, mode: deleteMode) => {
                deleteNodeChangeHandler(node, mode)
            })
            currentCtx?.$eventBus.$on('trigger-design-updateNodeChange', (nodeId: string, newNode: any) => {
                updateNodeChangeHandler(nodeId, newNode)
            })
            currentCtx?.$eventBus.$on('trigger-design-node-click', async (nodeId: string, newNode: ProcessNodeListItem , oldNode: ProcessNodeListItem) => {
                if(![NodeTypeEnum.START_NODE, NodeTypeEnum.CONDITION].includes(newNode.type as unknown as NodeTypeEnum)) {
                    findParentApiList(nodeId)
                }
                // 处理相关节点点击value事件 (节点的点击事件都可以放下面事件中处理)
                handleNodeClick(newNode, oldNode)
                
            })
            currentCtx.$eventBus.$on('trigger-design-save', () => {
                createTriggerHandler()
            })
            currentCtx.$eventBus.$on('trigger-design-update', () => {
                editTriggerHandler()
            })
            currentCtx.$eventBus.$on('trigger-design-validate', () => {
                validateHandler(chartList.value, currentCtx)
            })
            currentCtx?.$eventBus.$on('deleteAfterNode', () => {
                // 初始化结算的节点
                if (props.settlementShow) {
                    initCustomizedSecondNode(AllNode.SETTLEMENT_NODE)
                }
            })
        })

        onBeforeMount(async () => {
            try {
                await getCreateGray()

                await getCreateGrayAgent()
        
                if (isSmartFlowTag) {
                    await executeInit(currentCtx)
                    // 提前返回失败情况
                    if (!isTriggerSmartFlowSucc.value) {
                        message.error(`[智能流程标签初始化失败]: ${messageTriggerSmartFlow.value}`)
                        return
                    }
                    
                    chartList.value[0].options = {
                        ...chartList.value?.[0].options,
                        ...(typeof startNodeInsertInfo.value[0] === 'object' 
                            ? startNodeInsertInfo.value[0] 
                            : {})
                    }
                }
            } catch (error) {
                console.error(
                    isSmartFlowTag 
                        ? '智能流程标签初始化失败' 
                        : '获取触发器灰度失败', 
                    error
                )
            }
        })

        onBeforeUnmount(() => {
            document.removeEventListener('click', handlerClickOutSide)
            document.removeEventListener('mousemove', throttleMouseMoveNodeClickHandler)
            currentCtx?.$eventBus.$off('trigger-design-addNodeChange')
            currentCtx?.$eventBus.$off('trigger-design-deleteNodeChange')
            currentCtx?.$eventBus.$off('trigger-design-updateNodeChange')
            currentCtx?.$eventBus.$off('trigger-design-node-click')
            currentCtx?.$eventBus.$off('trigger-design-save')
            currentCtx?.$eventBus.$off('trigger-design-update')
            currentCtx?.$eventBus.$off('trigger-design-validate')
            currentCtx?.$eventBus.$off('deleteAfterNode')
        })
        return ()=> (
            <div class="process-node-chart">
                <div class="process-node-chart__content" style={{paddingLeft: '60px'}}>
                    {
                        chartList.value.map((item, index)=> {
                            return  (
                                <MainNodeProcessWrap 
                                    nodeIndex={index + 1}
                                    nodeData={item}
                                    nodeList={chartList.value}
                                    settlementShow={props.settlementShow}
                                />
                            )
                        })
                    }
                 </div>
            </div>
        ) as unknown as VNode
    }
})