<template>
  <div>
    <el-dialog
      title="设置"
      :visible.sync="dialogVisible"
      width="70%"
      :mask-closeable="false"
      :top="isScroll ? '24px': '8vh'"
      append-to-body
      ref="settingDialog"
    >
      <div class="setting-top">
        <div>
          模版：
          <el-select :value="value" @change="templateIdChange" placeholder="请选择">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <i class="iconfont icon-info"></i>
          下方可自定义配置需要展示的数据内容
        </div>
        <div>
          示例数据：
          <el-switch :value="needExample" :disabled="noAuth" @change="changeOpenExample"></el-switch>
        </div>
      </div>

      <div class="setting-content">
        <div class="setting-content-left">
          <setting-card :setting-options="leftSetting"></setting-card>
          <div class="setting-map"></div>
        </div>
        <div class="setting-content-right">
          <setting-card :setting-options="rightTopSetting" :single-style="true"></setting-card>
          <setting-card class="setting-card-center" :setting-options="rightHistogramSetting"></setting-card>
          <setting-card :setting-options="rightPieChartSetting"></setting-card>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmHandler">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import settingCard from './settingCard';
import { getSettingGroup } from './setting';
import { setModel } from '@src/api/ScreenDataApi';
const options = [
  {
    value: 0,
    label: '深邃星空',
  },
  {
    value: 1,
    label: '蓝色格调',
  },
  {
    value: 2,
    label: '浅淡风华',
  },
  {
    value: 3,
    label: '翡翠斑斓',
  },
];
export default {
  name: 'dialog-setting',
  props: {
    params: {
      type: Object
    },
    noAuth: {
      type: Boolean,
    }
  },
  data() {
    return {
      dialogVisible: false,
      value: 3,
      needExample: false,
      options,
      settingGroup: {},
      isScroll: false,
      dataScreenId: '',
      normalScreen: false,
    };
  },
  components: {
    settingCard,
  },
  computed: {
    leftSetting() {
      return this.settingGroup['leftTop'];
    },
    rightTopSetting() {
      return this.settingGroup['rightTop'];
    },
    rightHistogramSetting() {
      return this.settingGroup['rightHistogram'];
    },
    rightPieChartSetting() {
      return this.settingGroup['rightPieChart'];
    },
  },
  mounted() {
    window.addEventListener('resize', this.setHeight);
  },
  methods: {
    getSettingBuildParams () {
      let result = {};

      for (let group in this.settingGroup) {
        this.settingGroup[group].items.forEach(setting => {
          let key = setting.key;
          let value = setting.value;

          result[key] = value;
        })
      }

      return result;
    },
    confirmHandler () {
      let params = this.getSettingBuildParams();
      let group = this.settingGroup;
      let data = {
        params,
        group
      }
      sessionStorage.setItem('isExamples', JSON.stringify(this.needExample))
      this.$emit('update', data);
      this.dialogVisible = false
    },
    async templateIdChange(val) {
      if(val !== 3) {

        // 定制的大屏需要设置setModel
        if(!this.normalScreen) {
          const { code }  =  await setModel(val);
          if(code !== 200) return
        }
        sessionStorage.removeItem('FROM_NEWDATASCREEN');
        window.location.replace(`/foundation/dataScreen?from=oldDataScreen&id=${this.dataScreenId}&model=${val}`)
        return
      }
    },
    changeOpenExample(value) {
      this.needExample = value
    },
    setHeight() {
      this.$nextTick(() => {
        try {
          const dom = document.querySelector('.el-dialog__body')
          dom.style.height = '612px';
          const offsetHeight = document.body.clientHeight;
          const dialogOffsetHeight = this.$refs.settingDialog.$el.children[0].offsetHeight;
          if (offsetHeight <= dialogOffsetHeight) {
            dom.style.height = 'calc(100vh - 145px)';
            dom.style.overflow = 'auto';
            this.isScroll = true
          } else {
            dom.style.height = '612px';
            dom.style.overflow = 'hidden';
            this.isScroll = false
          }
        } catch (error) {}
        
      });
    },
    openSetting(id = '', normalScreen = false) {
      this.dataScreenId = id;
      this.normalScreen = normalScreen;
      this.dialogVisible = true;
      this.setHeight();
      this.needExample = JSON.parse(sessionStorage.getItem('isExamples') ) || false
      this.value = 3
      this.settingGroup = getSettingGroup(this.params);
    },
  },
};
</script>

<style lang="scss" scoped>
.setting-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .icon-info {
    font-size: 16px;
    color: $color-primary-light-6;
    margin: 0 4px;
  }
  .icon-jieshishuoming1 {
    color: #262626;
  }
}
.setting-content {
  margin-top: 12px;
  padding: 16px;
  background-color: #fafafa;
  width: 100%;
  display: flex;
  > div {
    width: 50%;
  }
  &-left {
      margin-right: 10px;
    .setting-map {
      height: calc(100% - 128px);
      background: url('../../../../assets/img/mapBg.png');
      background-size: contain;
      background-repeat: no-repeat;
      background-position: 50% 85%;
    }
  }
  &-right {
    .setting-card-center {
      margin: 16px 0;
    }
  }
}
::v-deep .el-dialog {
  min-width: 800px;
  margin-bottom: 0!important;
}
::v-deep .el-dialog__wrapper {
  overflow: hidden!important
}
</style>
