<template>
  <div style="padding: 10px">
    <AITaskSummary 
      ref="AITaskSummary" 
      :task-list="taskList"
    />
  </div>
</template>

<script>
import AITaskSummary from '@src/modules/ai/components/ai-task-summary/index.tsx';

export default {
  name: 'demo-view',
  components: {
    AITaskSummary,
  },
  data() {
    return {
    };
  },
  computed: {
    taskList() {
      return []
    }
  },
  methods: {},
  mounted() {
    this.$refs.AITaskSummary.openDialog();
  },
};
</script>

<style lang="scss">
.demo-view {
  width: 100%;
  height: 100%;
}
</style>



