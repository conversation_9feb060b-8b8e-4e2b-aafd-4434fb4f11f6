
import * as UserCenterApi from '@src/api/UserCenterApi'
import { getRootWindow } from '@src/util/dom'
import platform from '@src/platform';

import { isQYWX, isNotDingTalkTenant, isDingDingDesktop } from '@src/util/platform'

import EventBus from '@src/util/eventBus';

/**
 * @description: 用户中心模拟登录
 * @param {Object} params {userId, tenantId}
 * @return {Object} tokenInfo {token, shbVersion, userId}
 */
export function userCenterMockLogin(params) {
  UserCenterApi.mockLogin(params)
    .then(res => {
      return res.status === 0 ? res.data : {}
    })
    .catch(err => {
      console.log('userCenterMockLogin err', err)  
    })
}

// 用户中心登出
export function userCenterLogOut() {
  //测试独立端用户中新登出
  UserCenterApi.logOut()
    .then(res => {
      if(res.status === 0) {
        // removeTokenInfo()
        reLogin()
        console.log(res);
      } else {
        platform.alert(res.message || '注销失败')
      }
    })
    .catch(err => {
      console.error('userCenterLogOut err', err)  
    })
}

// 获取tokeninfo
export function userCenterGetTokenInfo() {
  const rootWindow = getRootWindow(window);
  const userId = rootWindow?.loginUser?.userId || localStorage.getItem('loginUserId') || localStorage.getItem('userId')
  let tokenInfo = {}
  
  if (userId) {
    const TokenInfoKey = `${userId}_tokenInfo`
    const tempInfo = localStorage.getItem(TokenInfoKey)
    try {
      if (typeof tempInfo == 'string') {
        tokenInfo = JSON.parse(tempInfo)
      }
    } catch (e) {
      console.error('获取token 解析失败', e)
    }
  }

  return tokenInfo
}

export function userCenterSetTokenInfo(tokenInfo) {
  const { userId = '', token = '', shbVersion = '', shbEnv = '' } = tokenInfo
  const TokenInfoKey = `${userId}_tokenInfo`
  // 保存token到本地
  localStorage.setItem('loginUserId', userId)
  localStorage.setItem(TokenInfoKey, JSON.stringify(tokenInfo))
  // 保存token到cookie，兼容跳转请求 :-）
  document.cookie = `PUB_TOKEN=${token};path=/;`
  document.cookie = `PUB_VERSION=${shbVersion};path=/;`
  document.cookie = `PUB_ENV=${shbEnv};path=/;`
}

export const ErrorMap = {
  2001: {
      title: '授权码为空',
      message: '请联系售后宝客服人员进行处理',
      type: 'error'
  },
  2002: {
      title: 'CorpId为空',
      message: '请联系售后宝客服人员进行处理',
      type: 'error'
  },
  2003: {
      title: '授权码和CorpId为空',
      message: '请联系售后宝客服人员进行处理',
      type: 'error'
  },
  2004: {
      title: '获取企业信息失败',
      message: '请联系售后宝客服人员进行处理',
      type: 'error'
  },
  2005: {
      title: '免登失败',
      message: '请联系售后宝客服人员进行处理',
      type: 'error'
  },
  2006: {
      title: '未知的用户',
      message: '请联系售后宝客服人员进行处理',
      type: 'error'
  },
  2007: {
      title: '使用账号数量超过上限',
      message: '请联系管理员，购买更多账户数量',
      type: 'warning'
  },
  2008: {
      title: '无权限查看客户信息',
      message: '请联系管理员，开通相关权限',
      type: 'warning'
  },
  2009: {
      title: '用户未分配',
      message: '请联系管理员，分配用户权限',
      type: 'warning'
  },
  2010: {
      title: '无客户端登录权限',
      message: '请联系管理员，获取对应权限',
      type: 'warning'
  },
  2011: {
      title: '用户已禁用',
      message: '请联系管理员，解锁用户权限',
      type: 'warning'
  },
  2012: {
      title: '服务已到期',
      message: '请联系管理员，续费服务',
      type: 'warning'
  },
  2013: {
      title: '企业被禁用',
      message: '请联系管理员，解除禁用',
      type: 'warning'
  },
  2014: {
      title: '多租户企业，请先选择企业',
      message: '同一租户，不同企业所有权限不同',
      type: 'warning'
  },
  2015: {
      title: '未授权',
      message: '请联系售后宝客服人员进行处理',
      type: 'error'
  },
  2016: {
      title: '登录已过期',
      message: '请重新登录，如无法登录请联系售后宝客服人员处理',
      type: 'warning'
  },
  2017: {
      title: '密码错误',
      message: '密码错误，请重新输入',
      type: 'warning'
  },
  2021: {
      title: '登录限制',
      message: '您的账号不支持在中国大陆以外地区访问，请切换国际版账号进行登录',
      type: 'error'
  }
}

export function userCenterErrorHandler(response) {
  const { data } = response
  if (data.status && (data.status == '2016' || data.status == '2021')) {
    // status==2016，代表token过期，前端需要跳转到对应的登录页
    platform.alert(ErrorMap[data.status].message)
    // removeTokenInfo()
    reLogin()
    throw '哦，在这停顿！' // 有的地方在接口.then判断有二次弹窗，所以直接在这中止
  }

  // 20001 代表需要登录自助门户
  if (data.status && data.status == '20001') {
    EventBus.$emit('sspLoginError')
  }
  
}

// 清除token
function removeTokenInfo() {
  const rootWindow = getRootWindow(window);
  const userId = rootWindow?.loginUser?.userId || localStorage.getItem('userId')

  // 如果清掉cookie，多账号登录的情况，所有账号页面访问都会跳转到登录页；（应该是要清除的，这里有问题）
  // rootWindow.document.cookie = `PUB_TOKEN=;path=/;`
  // rootWindow.document.cookie = `PUB_VERSION=;path=/;`
  // rootWindow.localStorage.removeItem('loginUserId')
  rootWindow.localStorage.removeItem(`${userId}_tokenInfo`)
}

// 重新登录
export function reLogin() {
  const rootWindow = getRootWindow(window);
  // 用户中心登录平台的入口
  const loginHref = localStorage.getItem('PUBLOGINHREF')
  // 旧版的登出路径
  const oldLogoutPath = platform.inDingTalk ? '/smlogin/pc/logout' : '/logout';
  
  rootWindow.location.href = getLoginHref(loginHref, oldLogoutPath)
  removeTokenInfo();
}

function getLoginHref(loginHref, oldLogoutPath) {
  
  if (loginHref) {
    return loginHref
  }
  
  // 非企业微信客户端环境 且 非钉钉客户端环境
  if (!isQYWX() && !isDingDingDesktop()) {
    
    const loginPath = isNotDingTalkTenant() ? '/login/app' : '/login/dingtalk'
    
    return loginPath
  }
  
  return oldLogoutPath
}

export const UserCenter = {
  userCenterMockLogin,
  userCenterLogOut,
  userCenterGetTokenInfo,
  userCenterErrorHandler,
  ErrorMap
}

export default UserCenter;