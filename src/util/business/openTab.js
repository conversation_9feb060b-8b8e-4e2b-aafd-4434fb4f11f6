import qs from 'qs';
import qs2 from '@src/util/querystring2';
import platform from './../../platform';
import { openAccurateTab } from '@src/util/platform'
import { PageRoutesTypeEnum } from 'pub-bbx-global/pageType/dist/enum/PageRoutesEnum'
import { haveWikiV2Gray } from '@src/util/grayInfo'
import { getMenuItemId } from '@src/util/menuIdUtil';
import { getRootWindowInitData } from '@src/util/window'
const rootWindowInitData = getRootWindowInitData();
/** 
 * @description 获取fromId 刷新tab用
*/
export function getFromId() {
  let fromId = '';
  
  try {
    fromId = window.frameElement.getAttribute('id');
  } catch (error) {
    console.warn('getFromId -> error', error)
  }
  
  return fromId
}

/* ----- start 工单 ------- */
/** 
 * @description 打开工单详情tab
*/
export function openTabForTaskView(taskId = '', isNoHistory = true, isBalance = false, isAllot = false, taskNo = '') {
  if (!taskId) return
  
  let fromId = getFromId()
  let params = `${isNoHistory ? 'noHistory=1' : ''}${isBalance ? isNoHistory ? '&active=balance' : 'active=balance' : ''}${isAllot ? '&allot=true' : ''}`
  
  openAccurateTab({
    type: PageRoutesTypeEnum.PageTaskView,
    key: taskId,
    titleKey: taskNo || taskId,
    params,
    fromId
  })
}

/** 
 * @description 打开工单新建/编辑tab
 * @param {String} defaultTypeId 默认工单类型id
*/
export function openTabForTaskCreate(defaultTypeId = '') {
  let fromId = getFromId();

  openAccurateTab({
    type: PageRoutesTypeEnum.PageCreateTask,
    titleKey: '正在加载',
    params: `defaultTypeId=${defaultTypeId}`,
    fromId
  })
}

/** 
 * @description 打开工单计划任务新编辑tab
 * @param {String} templateId 工单类型id
*/
export function openTabForPlanTaskCreate(templateId = '') {
  let fromId = getFromId()
  
  openAccurateTab({
    type: PageRoutesTypeEnum.PagePlanTaskCreate,
    params: `defaultTypeId=${templateId}`,
    fromId
  })
}

/** 
 * @description 打开工单指派tab
 * @param {String} taskId 工单id
*/
export function openTabForTaskAllot(taskId = '') {
  if (!taskId) return
  
  let fromId = getFromId()

  openAccurateTab({
    type: PageRoutesTypeEnum.PageTaskAllot,
    key: taskId,
    titleKey: taskId,
    params: `id=${taskId}`,
    fromId
  })
}

/* ----- end 工单 ------- */


/* ----- start 客户 ------- */
/** 
 * @description 打开客户详情tab
 * @param {String} customerId 客户id
 * @param {Boolean} isNoHistory 是否不显示历史参数
*/
export function openTabForCustomerView(customerId = '', isNoHistory = true, option = {}) {
  if (!customerId) return
  
  let fromId = getFromId();
  
  openAccurateTab({
    type: PageRoutesTypeEnum.PageCustomerView,
    key: customerId,
    params: isNoHistory ? 'noHistory=1' : void 0,
    fromId,
    ...option
  });
}
/* ----- end 客户 ------- */


/* ----- start 产品 ------- */
/** 
 * @description 打开产品详情tab
 * @param {String} productId 产品id
 * @param {Boolean} isNoHistory 是否不显示历史参数
*/
export function openTabForProductView(productId = '', isNoHistory = true, option = {}) {
  if (!productId) return
  
  let fromId = getFromId();

  openAccurateTab({
    type: PageRoutesTypeEnum.PageProductView,
    key: productId,
    params: isNoHistory ? 'noHistory=1' : void 0,
    titleKey: '产品详情',
    fromId,
    ...option
  })
}

/** 
 * @description 打开产品类型编辑tab
 * @param {String} productCatalogId 产品类型id
 * @param {Boolean} isNoHistory 是否不显示历史参数
*/
export function openTabForProductCatalogEditView(productCatalogId = '') {
  if (!productCatalogId) return
  
  let fromId = getFromId();
  
  openAccurateTab({
    type: PageRoutesTypeEnum.PageProductCatalogEdit,
    key: productCatalogId,
    params: `id=${productCatalogId}`,
    fromId
  })
}
/* ----- end 产品 ------- */


/* ----- start 团队 ------- */
/** 
 * @description 打开团队详情tab
 * @param {String} teamId 团队id
 * @param {Boolean} isNoHistory 是否不显示历史参数
*/
export function openTabForTeamtView(teamId = '', isNoHistory = true) {
  if (!teamId) return

  let fromId = getFromId();

  openAccurateTab({
    type: PageRoutesTypeEnum.PageTeamView,
    reload: true,
    key: teamId,
    params: isNoHistory ? 'noHistory=1' : void 0,
    fromId
  });
}

/** 
 * @description 打开新建团队tab
 * @param {Boolean} isNoHistory 是否不显示历史参数
*/
export function openTabForTeamCreate(isNoHistory = true) {
  let fromId = getFromId();

  openAccurateTab({
    type: PageRoutesTypeEnum.PageSecurityTagCreate,
    reload: true,
    titleKey: '新建部门',
    params: isNoHistory ? 'noHistory=1' : void 0,
    fromId
  });
}

/** 
 * @description 打开新建子团队tab
 * @param {String} parentData 父团队数据
*/
export function openTabForTeamChildCreate(parentData) {
  let fromId = getFromId();
  
  openAccurateTab({
    type: PageRoutesTypeEnum.PageSecurityTagCreate,
    reload: true,
    titleKey: '新建子部门',
    params: qs.stringify(parentData),
    fromId
  });
}

/** 
 * @description 打开部门主页tab
*/
export function openTabForTagHome(id) {
  let fromId = getFromId();
  const tabId = window.frameElement.dataset.id
  
  openAccurateTab({
    type: PageRoutesTypeEnum.PageDeptHome,
    key: id,
    params: `parentTab=${tabId}`,
    fromId
  })
}

/** 
 * @description 打开服务商主页tab
*/
export function openTabServiceProviderHome(id) {
  let fromId = getFromId();
  const tabId = window.frameElement.dataset.id
  
  openAccurateTab({
    type: PageRoutesTypeEnum.PageServiceProviderViewHome,
    key: id,
    params: `parentTab=${tabId}`,
    fromId
  })
}
/* ----- end 团队 ------- */


/* ----- start 用户 ------- */
/** 
 * @description 打开用户详情
 * @param {String} parentData 父团队数据
 * @param {Object} params 参数对象
*/
export function openTabForUserView(userId, params = {}) {
  if (!userId) return
  
  let fromId = getFromId();
  
  openAccurateTab({
    type: PageRoutesTypeEnum.PageSecurityUserView,
    reload: true,
    key: userId,
    params: qs.stringify(params),
    fromId
  });
}
/* ----- end 用户 ------- */


/* ----- start 绩效 ------- */
/** 
 * @description 打开绩效详情
 * @param {String} performanceId 绩效id
*/
export function openTabForPerformanceView(performanceId) {
  if (!performanceId) return

  let fromId = getFromId();
  
  openAccurateTab({
    type: PageRoutesTypeEnum.PagePerformanceReport,
    titleKey: '绩效报告详情',
    key: performanceId,
    fromId
  })

}
/* ----- end 绩效 ------- */


/* ----- start 知识库 ------- */
/** 
 * @description 打开知识库新建
*/
export function openTabForWikiCreate(
  {
    isFromList,
    copyId, 
    taskId, 
    taskNo, 
    taskTypeId, 
    typeId,
    wikiType,
    title = '知识库新建', 
    wikiTypeId,
    questionId
  } = {}
) {
  let fromId = getFromId();
  let params = '';
  
  if(copyId) {
    // 复制知识库
    params = `copyId=${copyId}`
  } else if(taskId) {
    // 转为知识库
    params = `taskId=${taskId}&taskNo=${taskNo}&taskTypeId=${taskTypeId}`
  }  else {
    // 复制 与 转为走获取详情判断 知识库还是故障库流程
    // 正常新增流程，获取当前目录分类 与 知识库还是故障库类别
    if(wikiTypeId) {
      params += params?`&wikiTypeId=${wikiTypeId}`:`wikiTypeId=${wikiTypeId}` // 新建知识库需要带入选择的分类
    } 
    if(wikiType) {
      //type  知识库 0 or '',故障库 1
      params += params?`&wikiType=${wikiType}`:`wikiType=${wikiType}`
    }
  }
  
  if (questionId) {
    const questionParams = `questionId=${questionId}`
    params += (params ? `&${questionParams}`: `${questionParams}`)
  }
  if (isFromList) {
    params += `&isFromList=${isFromList}`
  }
  
  let type = PageRoutesTypeEnum.PageWikiCreate
  // 故障库还是用旧版的新建编辑
  if (haveWikiV2Gray() && wikiType != 1) {
    type = PageRoutesTypeEnum.PageWikiV2Create
  }
  openAccurateTab({
    type,
    reload: true,
    params,
    fromId
  });
}

/** 
 * @description 打开知识库编辑
 * @param {String} wikiId 知识库id
*/
export function openTabForWikiEdit(wikiId = '') {
  if (!wikiId) return
  
  let fromId = getFromId();

  let type = PageRoutesTypeEnum.PageWikiEdit
  if (haveWikiV2Gray()) {
    type = PageRoutesTypeEnum.PageWikiV2Edit
  }
  openAccurateTab({
    type,
    key: wikiId,
    reload: true,
    params: `wikiId=${wikiId}`,
    fromId
  });
}

/** 
 * @description 打开知识库详情
 * @param {String} wikiId 知识库id
*/
export function openTabForWikiView(wikiId = '') {
  if (!wikiId) return
  
  let fromId = getFromId();
  
  let type = PageRoutesTypeEnum.PageWikiDetail
  if (haveWikiV2Gray()) {
    type = PageRoutesTypeEnum.PageWikiV2Detail
  }
  openAccurateTab({
    type,
    key: wikiId,
    reload: true,
    params: `wikiId=${wikiId}`,
    fromId
  });
}

/** 
 * @description 打开知识库列表
*/
export function openTabForWikiList() {
  let fromId = getFromId();
  
  let type = PageRoutesTypeEnum.PageWikiList
  if (haveWikiV2Gray()) {
    type = PageRoutesTypeEnum.PageWikiV2List
  }
  openAccurateTab({
    type: PageRoutesTypeEnum.PageWikiList,
    reload: true,
    fromId
  });
}
/* ----- end 知识库 ------- */

/* ----- start 事件 ------- */
/** 
 * @description 打开事件详情
 * @param {String} eventId 事件id
*/
export function openTabForEventView(eventId) {
  if (!eventId) return
  
  let fromId = getFromId()

  openAccurateTab({
    type: PageRoutesTypeEnum.PageEventView,
    key: eventId,
    fromId
  })
}
/* ----- end 事件 ------- */

/* ----- start 设置 ------- */
/** 
 * @description 客户管理设置
 * @param {String} eventId 事件id
*/
export function openTabForSettingCustomerView() {
  
  let fromId = getFromId()

  openAccurateTab({
    type: PageRoutesTypeEnum.PageCustomerManagement,
    fromId
  })
}
/* ----- end 事件 ------- */


/* ----- start 通用 ------- */
/** 
 * @description 打开短信消息设置
*/
export function openSettingSmsmessage() {
  
  let type = PageRoutesTypeEnum.PageSettingSmsmessage
  // 开了消息灰度，需要跳转新的渠道设置页面
  if(rootWindowInitData.openMessageReform){
    type = PageRoutesTypeEnum.PageSettingMessage
  }
  openAccurateTab({
    type,
  });
}
/* ----- end 通用 ------- */

/* ----- start 寄修 ------- */
/** 
 * @description 打开tab
*/
export function openTabPaasJiXiu(formId, formContentId, processId, detailNo) {
  
  const fromId = getFromId()
  
  openAccurateTab({
    type: PageRoutesTypeEnum.PagePaasTemplateDetail,
    titleKey: `寄修${detailNo || ''}`,
    params: `formId=${formId}&formContentId=${formContentId}&processId=${processId}`,
    fromId
  })
  
}
/* ----- end 寄修 ------- */

export function openTabCalendar() {
  
  const fromId = getFromId()
  
  openAccurateTab({
    type: PageRoutesTypeEnum.PageSchedule,
    fromId
  })
  
}

export function openTabCalendarWithParams(params) {
  
  const fromId = getFromId()
  
  openAccurateTab({
    type: PageRoutesTypeEnum.PageSchedule,
    params: `${qs2.stringify(params)}`,
    fromId
  })
  
}

export function closeTabCalendar() {
  platform.closeTab(PageRoutesTypeEnum.PageSchedule)
}

export function openTabNoticeList() {
  
  const fromId = getFromId()
  
  openAccurateTab({
    type: PageRoutesTypeEnum.PageInfoNotice,
    fromId
  })
  
}

export function openTabSettingReportAuth() {
  const fromId = getFromId()
  
  openAccurateTab({
    type: PageRoutesTypeEnum.PageSettingReportAuth,
    fromId
  })
}

// 查看服务商角色权限
export function openTabServiceManageAuth() {
  const fromId = getFromId()

  openAccurateTab({
    type: PageRoutesTypeEnum.PageRoleServiceView,
    fromId
  })
}


// 查看仓库权限组设置
export function openTabWarehouseRoleAuth() {
  const fromId = getFromId()

  openAccurateTab({
    type: PageRoutesTypeEnum.PageWarehouseRoleSetting,
    fromId
  })
}

// 在线客服
export function openTabCustomerService() {
  const fromId = getFromId();
  openAccurateTab({
    type:PageRoutesTypeEnum.PageImImchat,
    fromId
  })
}


// 工程师客服
export function openTabEngineerDetail(tenantProviderId, loginUserId) {
  const fromId = getFromId();
  openAccurateTab({
    type:PageRoutesTypeEnum.PageServiceEngineerDetail,
    params: `id=${loginUserId}&tenantProviderId=${tenantProviderId}`,
    key: loginUserId,
    fromId
  })
}

// 跳转到用量页面
export const jumpToUsage = (openType ='') => {
  const fromId = getFromId();
  openAccurateTab({
    type: PageRoutesTypeEnum.PageMyApplication,
    params: `openType=${openType}`,
    fromId
  })
}

// 智能机器人 小宝机器人
export function openTabGptRobotList() {
  
  const fromId = getFromId();
  
  platform.openTab({
    id: `INTELLIGENT_REBOT_GPT`,
    title: `小宝AI`,
    reload: true,
    close: true,
    url: `/shb/home/<USER>/gpt`,
    fromId
  })
  
}

// 智能机器人 小宝机器人 首页
export function openTabGptRobotHome() {
  
  const fromId = getFromId();
  
  platform.openTab({
    id: `M_XIAOBAOGPT_MANAGE_MENU`,
    title: `能力管理`,
    reload: true,
    close: true,
    url: `/shb/home/<USER>/gpt/home`,
    fromId
  })
  
}

// 智能机器人 小宝机器人 应用 - 智能问答 首页
export function openTabGptRobotAppIntelligentQA(robotAppId, robotId) {
  
  const fromId = getFromId();
  
  platform.openTab({
    id: `INTELLIGENT_ROBOT_GPT_INTELLIGENT_QA`,
    title: `智能问答`,
    reload: true,
    close: true,
    url: `/shb/home/<USER>/gpt/intelligentQA?robotId=${robotId}&robotAppId=${robotAppId}`,
    fromId
  })
  
}

// 智能机器人 小宝机器人 应用 - 小宝 BI 首页
export function openTabGptRobotAppBI(robotAppId, robotId) {
  
  const fromId = getFromId();
  
  platform.openTab({
    id: `INTELLIGENT_ROBOT_GPT_BI`,
    title: `小宝BI`,
    reload: true,
    close: true,
    url: `/shb/home/<USER>/gpt/bi?robotId=${robotId}&robotAppId=${robotAppId}`,
    fromId
  })
  
}

// 智能机器人 小宝机器人 应用 - 智能翻译 首页
export function openTabGptRobotAppTranslate(robotAppId, robotId) {
  
  const fromId = getFromId();
  
  platform.openTab({
    id: `INTELLIGENT_ROBOT_GPT_TRANSLATE`,
    title: `智能翻译`,
    reload: true,
    close: true,
    url: `/shb/home/<USER>/gpt/translate?robotId=${robotId}&robotAppId=${robotAppId}`,
    fromId
  })
  
}

// 智能机器人 小宝机器人
export function openTabGptRobotEdit(robotId, robotName) {
  
  const fromId = getFromId();
  
  platform.openTab({
    id: `INTELLIGENT_ROBOT_GPT_${robotId}`,
    title: robotName,
    reload: true,
    close: true,
    url: `/shb/home/<USER>/gpt/edit?id=${robotId}`,
    fromId
  })
  
}

// AI 2.0 Agent 应用配置
export function openTabAIAgentAppConfig(agentAppId, agentAppName, isSystem, isWorkflow) {
  
  const fromId = getFromId();
  
  platform.openTab({
    id: `INTELLIGENT_AGENT_APP_${agentAppId}`,
    title: agentAppName,
    reload: true,
    close: true,
    url: `/shb/home/<USER>/agent/app/${agentAppId}?isWorkflow=${isWorkflow}`,
    fromId
  })
  
}

// AI 2.0 Agent 应用配置
export function openTabAIAgentDetail(item, fromId_ = null) {

  const agentName = item.agentName || item.name || '智能体管理'
  const agentId = item.id
  const fromId = getFromId();
  const id = item.agentAppId
  
  platform.openTab({
    id: `INTELLIGENT_AGENT_${agentId}`,
    title: agentName,
    reload: true,
    close: true,
    url: `/ai/agentEdit?id=${id}&agentId=${agentId}`,
    fromId: fromId_ || fromId
  })
  
  // openAccurateTab({
  //   title: agentName,
  //   type: PageRoutesTypeEnum.PageBaoAiEdit,
  //   key: item.id,
  //   params:`id=${item.agentAppId}&agentId=${item.id}`,
  //   fromId: fromId_ ? fromId_ : fromId
  // })
  
}

