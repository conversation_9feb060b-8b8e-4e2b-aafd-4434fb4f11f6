import i18n, { t } from '@src/locales';
import { isArray, isEmpty, isObject, formatDate } from 'pub-bbx-utils';
import { cloneDeep } from 'lodash';
// @ts-ignore
import { fmt_address } from '@src/filter/fmt';

export function createField(formType: string, fieldName: string, displayName: string, setting: any = {}, newOpts: any = {}, defaultValue: any = '') {
  const randMath: any = Math.floor(Math.random() * 1000000);
  return {
    id: 'data' + randMath,
    modifyTime: null,
    createTime: null,
    guideProfessions: [],
    formType,
    displayName,
    fieldName: fieldName ? fieldName : `field_${randMath}`,
    tableName: 'active_table',
    setting,
    placeHolder: formType === 'text' || formType === 'textarea' ? t('common.placeholder.input2') : formType === 'select' ? t('common.placeholder.select') : '',
    defaultValue,

    enabled: 1,
    isSystem: 1,
    isNull: 1,
    isSearch: 0,
    isAdd: 1,
    isAppShow: 0,
    isCommon: 0,
    isHidden: 0,
    isOnceCommon: 0,
    isUpgrade: 0,
    isGuideData: false,
    isVisible: true,
    isDragCommon: null,
    guideData: false,
    show: true,
    disabled: false,
    ...newOpts,
  };
}

export const formateFormValueForTable = (field:any = {}, tableitem:any) => {
  const { formType, fieldName, setting, valueP } = field;
  let value: any = null;
  
  try {
    if (valueP) {
      value = valueP(tableitem)
    } else {
      
      value = cloneDeep(tableitem[fieldName]);
    }
  } catch (error) {
    value = cloneDeep(tableitem[fieldName]);
  }
  if (isEmpty(value)) {
    return '';
  }
  if (formType == 'date') {
    return formatDate(value, field?.setting?.dateType || 'YYYY-MM-DD HH:mm:ss');
  }else if(formType == 'select'){
    value = value.map((i:any)=>{
      if(Reflect.has(i, 'language')){
        return i.language?.[i18n.locale] || ''
      }else if(Reflect.has(i, 'label')){
        return i.label || ''
      }else if(!isObject(i)){
        return i
      }
      
    })
  }else if(formType == 'address'){
    value = fmt_address(value)
  }
  if (isArray(value)) {
    if (isObject(value[0])) {
      const valArr = value.map((i:any) => i.label);
      return valArr.join('，');
    }
    return value.join('，');
  }
  return value
};