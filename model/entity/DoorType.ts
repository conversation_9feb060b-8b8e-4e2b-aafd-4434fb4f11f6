interface DoorType {
  id: number;
  tenantId: string
  // 自助门户模版名称
  typeName: string
  // 自助门户类型配置
  setting: Record<string, any>
  // 删除标示 0未删除，1已删除
  isDelete: number
  // 所有类型只能有一个启用状态，0未启用，1已启用
  inUse: number
  createTime: string	
  updateTime: string	
  // 说明信息
  explainInfo: string
  // 排序
  orderId: number
  // 是否为默认系统类型, 0: 系统类型, 1: 自定义类型·
  defaultType?: number
}

export interface DoorTypeNav {
  // 是否显示
  isVisible: boolean
  // 标题
  title: string
  // 图标
  icon: string
  // 值
  value: string
  // 禁用状态
  disabled: boolean
  // 排序
  order: number
}

export default DoorType
