/* model */
import { SHBProductLineEnum } from "@shb-lib/tenant"
import i18n from '@src/locales'

class Role {
  
  public id: string = ''
  // 角色名称
  public name: string = ''
  public text?: string = ''
  //角色描述
  public description?: string = ''
  //是否默认角色
  public isSys?: number | null = null
  // 角色标识
  public rKey?: string = ''
  // 权限列表,仅在新增角色权限时用过
  public authorityList?: String[] = []
  // 权限
  public authoritys?: any;
  // 是否自定义
  public custom?: string = ''
  public isDelete?: Number | null = null
  
  public children?: Role[] = []
  
  public productLine?: SHBProductLineEnum
  
}

class RoleType {
  public id: string = ''
  // 角色名称
  public name: string = ''
  // 角色下用户数
  public count: string = ''
  // 类型
  public type: RoleTypeEnum = RoleTypeEnum.ParentTag
  // 角色描述
  public describe?: string = ''
  // 是否默认角色
  public isSystem?: number | null = null
  
  public children?: RoleType[] = []

  public qualificationIds?: any[]
  
  /**
   * 编辑状态,0 未编辑过, 1 编辑过
  */
  public state: number = 0
}

enum RoleTypeEnum {
  // 父级团队
  ParentTag = 'parentTag',
  // 已分配
  Assigned = 'assigned',
  // 待分配
  ToBeAssigned = 'toBeAssigned'
}

enum RoleStateEnum {
  // 未编辑过
  UnEdited = 0,
  // 编辑过
  Edited = 1
}

enum RoleAuthDepthEnum {
  All = 3,
  Dept = 2,
  Person = 1,
  custom = 4
}

const getRoleAuthDepthNameObj = () => ({
  All: i18n.t('common.base.all'),
  Dept: i18n.t('common.base.department'),
  Person: i18n.t('common.base.personal'),
  custom: i18n.t('common.base.customData')
})


interface RoleAuthGroup {
  
  // 分组id
  groupId: string;
  // 分组名称
  groupName: string;
  
  // 所属分组
  twoAuthorityGroupList: RoleTwoAuthGroup[];
}

interface RoleTwoAuthGroup { 
  // 二级分组id
  twoGroupId: string;
  // 二级分组名称
  twoGroupName: string;
  // 所属分组
  authorityList: RoleAuth[];
  // 二级分组排序
  twoGroupOrder: number;
}

interface RoleAuth {
  
  // 提交的时候用的
  authorityId: string;
  
  id: number | string;
  // 中文名
  cnName: string;
  // 英文名称
  enName: string;
  // 排序
  sort: number;
  /**
   * 等级深度 个人 1，部门 2， 全部 3, 自定义数据权限4）
   */
  depth: 1 | 2 | 3 | 4 |RoleAuthDepthEnum | string;
  // 备注
  remarks: string;
  /**
   * 分组兼容老版页面展示用的
   */
  groupId: string;
  
  aLevel: string;
  
  // 判断是否有数据权限
  supportData: number;
  
  check: boolean;
  
  twoGroupId: string;

  // 判断是否显示全部条件
  showAllCheck?: number;
}

export {
  Role,
  RoleAuth,
  RoleAuthGroup,
  RoleTwoAuthGroup,
  RoleAuthDepthEnum,
  getRoleAuthDepthNameObj,
  RoleType,
  RoleTypeEnum,
  RoleStateEnum,
}

export default Role