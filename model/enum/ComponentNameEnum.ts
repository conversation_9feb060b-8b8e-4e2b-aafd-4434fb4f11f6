enum ComponentNameEnum {
  // 折叠
  BaseFlod = 'base-flod',
  BaseFlodItem = 'base-flod-item',
  // 表格高级设置 选择列
  BaseTableAdvancedSetting = 'base-table-advanced-setting',
  // 工单指派弹窗
  TaskAllotModal = 'task-allot-modal',
  // 工单指派人员表格
  TaskAllotUserTable = 'task-allot-user-table',
  // 工单服务商派单表格
  TaskAllotServiceProviderTable = 'task-allot-service-provider-table',
  // 工单指派人员地图
  TaskAllotUserMap = 'task-allot-user-map',
  // 工单指派人员日历
  TaskAllotUserCalendar = 'task-allot-user-calendar',
  // 工单自动分配
  TaskAllotAuto = 'task-allot-auto',
  // 工单分配给负责人
  TaskAllotExcutor = 'task-allot-excutor',
  // 工单分配到工单池
  TaskAllotPool = 'task-allot-pool',
  // 工单分配到服务商
  TaskAllotServiceProvider = 'task-allot-service-provider',
  // 工单指派方式
  TaskAllotType = 'task-allot-type',
  // 工单指派 地图
  TaskAllotMap = 'task-allot-map',
  // 工单指派 工单池 通知
  TaskAllotPoolNotification = 'task-allot-pool-notification',
  // 工单指派 工单池 统计信息
  TaskAllotPoolInfo = 'task-allot-pool-info',
  // 新建分配规则
  AllotRuleModal = 'allot-rule-modal',
  // 工单指派人员卡片
  UserCard = 'user-card',
  // 工单指派人员按钮
  UserButton = 'user-button',
  // 工单流程步骤
  TaskProcessSteps = 'task-process-steps',
  // 基础面板侧边栏
  BasePanelAside = 'base-panel-aside',
  // 呼叫中心打电话
  BizCallCenterPhone = 'biz-call-center-phone',
  // 工单地图信息弹窗
  TaskMapInfoWindow = 'task-map-info-window',
  // 修改计划时间
  BizModifyPlanTime = 'biz-modify-plan-time',
  // UIInput
  UiInput = 'ui-input',
  UiEmpty = 'ui-empty',
  UiTitle = 'ui-title',
  // 工单状态
  UiTaskState = 'ui-task-state',
  // 版本限制数量弹窗
  BizVersionLimitDialog = 'biz-version-limit-dialog',
  // 版本限制数量弹窗 (HOC)
  BizVersionLimitDialogHOC = 'biz-version-limit-dialog-hoc',
  // 版本混入组件
  VersionMixin = 'version-mixin',
  // 空
  Empty = 'empty',
  // 应用升级提示弹窗
  ApplicationUpgradePromptDialog = 'application-upgrade-prompt-dialog',
  // 主题
  ThemeMixin = 'theme-mixin',
  // 业务远程搜索
  BizRemoteSelect = 'biz-remote-select',
  // 基础窗口
  BaseWindow = 'base-window',
  DepartmentAndUserHOC = 'department-and-user-hoc',
  DepartmentHOC = 'department-hoc',
  WXSelectEnterPriseContactHOC = 'wx-select-enterprise-contact-hoc',
  // 列表页面
  BizListView = 'biz-list-view',
  // 列表页面
  BizListViewTask = 'biz-list-view-task',
  // 业务表格
  BizTable = 'biz-table',
  BizLogisticsNo = 'biz-list-logistics-no',
  // 工单业务表格
  BizTableTask = 'biz-table-task',
  // 工单计划任务列表
  TaskPlanTaskList = 'task-plan-task-list',
  // 基础错误页面 无权限页面
  BaseErrorPageNoPermission = 'base-error-page-no-permission',
  Permission = 'permission',
  // 工单审核结算页面
  TaskBalanceList = 'task-balance-list',
  // 订阅按钮
  SubscriptionButton = 'subscription-button',
  // 表单弹窗
  BizFormDialog = 'biz-form-dialog',
  // 工单列表混入
  TaskListMixin = 'task-list-mixin',
  // 工单关闭列表
  TaskCloseList = 'task-close-list',
  // 工单池列表
  TaskPoolList = 'task-pool-list',
  // 工单回访列表
  TaskReviewList = 'task-review-list',
  // 工单指派列表
  TaskAllotList = 'task-allot-list',
  // 批量指派弹窗
  BatchAllotDialog = 'batch-allot-dialog',
  // 工单分配选择
  TaskAllotSelect = 'task-allot-select',
  // 指派地图弹窗
  AllotMapDialog = 'allot-map-dialog',
  // 客户标签设置
  CustomerTagSetting = 'customer-tag-setting',
  // 客户标签编辑显示
  CustomerTagEdit = 'customer-tag-edit',
  // 客户标签编辑弹窗
  CustomerTagEditDialog = 'customer-tag-edit-dialog',
  // 基础标签
  BaseTags = 'base-tags',
  // 微信标签
  UiWxTag = 'ui-wx-tag',
  // 客户标签选择select
  BizCustomerTagSelect = 'biz-customer-tag-select',
  // 企业微信聊天工具栏弹窗
  BizWorkWxChatToolBarDialog = 'biz-work-wx-chat-toolbar-dialog',
  // 企业微信聊天工具栏弹窗 (HOC)
  BizWorkWxChatToolBarDialogHOC = 'biz-work-wx-chat-toolbar-dialog-hoc',
  // 列表页面复选框选择列表
  BizListCheckboxSelect = 'biz-list-checkbox-select',
  // 微信聊天按钮
  BizWxChatButton = 'biz-wx-chat-button',
  // 客户联系人弹窗
  CustomerLinkmanDialog = 'customer-linkman-dialog',
  // 表单样式字段
  FormStyleBuild = 'form-style',
  // 表单样式字体
  FormStyleFont = 'form-style-font',
  // 表单样式颜色
  FormStyleColor = 'form-style-color',
  // 工作信息维护标签设置
  WorkMessagesTag = 'work-messages-tag',
  // 产品类型关联修改弹窗
  BizProductTypeRelationModifyDialog = 'biz-product-type-relation-modify-dialog',
  // 产品类型质保规则显示
  FormQualityRuleView = 'form-quality-rule-view',
  // 质保信息
  FormQualityInfoBuild = 'form-quality-info-build',
  // 质保信息 质保期
  FormQualityInfoPeriod = 'form-quality-info-period',
  // 质保信息 质保信息分割线
  FormQualityInfoSeparator = 'form-quality-info-separator',
  // 质保信息 计算规则
  FormQualityInfoComputedRule = 'form-quality-info-computed-rule',
  // 基础警告
  BaseAlert = 'base-alert',
  // 质保信息 计算规则 自动计算
  FormQualityInfoComputedRuleAuto = 'form-quality-info-computed-rule-auto',
  // 质保信息 手动计算
  FormQualityInfoComputedRuleManual = 'form-quality-info-computed-rule-manual',
  // 质保
  QualityMixin = 'quality-mixin',
  // 质保信息记录
  QualityInfoRecord = 'quality-info-record',
  // 表单构建
  FormBuilder = 'form-builder',
  UiSeparator = 'ui-separator',
  ChartsSearch = 'charts-search',
  ChartsSearchRenXiao = 'charts-search-renxiao',
  ChartsSearchTask = 'charts-search-task',
  ChartsSearchWuZi = 'charts-search-wuzi',
  ChartsSearchYingShou ='charts-search-yingshou',
  ChartsSearchYingShouSpareParts = 'charts-search-yingshou-spare-parts',
  ChartTaskTypeSelect = 'charts-task-type-select',
  ChartTeamSelect = 'charts-team-select',
  ChartUserSelect = 'charts-user-select',
  ChartRepertorySelect = 'charts-repertory-select',
  ChartSparePartSelect = 'charts-spare-part-select',
  ChartServiceTypeSelect = 'charts-service-type-select',
  ChartsSearchRow = 'charts-search-row',
  ChartsSearchRowItem = 'charts-search-row-item',
  ChartTimePeriodSelect = 'charts-time-period-select',
  VShowBlock = 'v-show',
  // 企业新功能试用
  WeiXinApplyTrailFeature = 'wei-xin-apply-trail-feature',
  // 企业新功能试用弹窗
  WeiXinApplyTrailFeatureDialog = 'wei-xin-apply-trail-feature-dialog',
  // 企业新功能试用标签组件
  WeiXinApplyTrailFeatureTag = 'wei-xin-apply-trail-feature-tag',
  // 业务套接字组件
  BizWebSocket = 'biz-web-socket',
  // 业务浏览器通知
  BizWebNotification = 'biz-web-notification',
  // 基础人员标签
  BaseUserLabel = 'base-user-label',
  // 角色管理页面
  RoleManagementView = 'role-management-view',
  RoleViewLeftPanel = 'role-view-left-panel',
  RoleViewRightPanel = 'role-view-right-panel',
  RoleViewHeader = 'role-view-header',
  RoleViewTabs = 'role-view-tabs',
  RoleViewTabsForView = 'role-view-tabs-for-view',
  RoleViewTabsForEdit = 'role-view-tabs-for-edit',
  RoleViewContentView = 'role-view-content-view',
  RoleViewContentEditView = 'role-view-content-edit-view',
  RoleViewUserTab = 'role-view-user-tab',
  RoleViewUserTabHeader = 'role-view-user-tab-header',
  RoleViewUserContent = 'role-view-user-content',
  RoleViewUserContentHeader =  'role-view-user-content-header',
  RoleViewUserContentTable = 'role-view-user-content-table',
  RoleViewContentStayAllotView = 'role-view-content-stay-allot-view',
  RoleViewContentStayAllotViewHeader = 'role-view-content-stay-allot-view-header',
  RoleViewUserTable = 'role-view-user-table',
  RoleViewAllotRoleModal = 'role-view-allot-role-modal',
  AuthTreeNode = 'auth-tree-node',
  AuthTreeNodeContent = 'auth-tree-node-content',
  AuthTree = 'auth-tree',
  RoleViewAuthView = 'role-view-auth-view',
  RoleViewForm = 'role-view-form',
  RoleViewDefaultAllotModal = 'role-view-default-allot-modal',
  AuthList = 'auth-list',
  AuthListItem = 'auth-list-item',
  UserBuyAndUseBar = 'user-buy-and-use-bar',
  RoleManagementViewHeader = 'role-management-view-header',
  RoleManagementViewHeaderRadio = 'role-management-view-header-radio',
  BizRoleSelect = 'biz-role-select',
  // 表单设计器表格
  FormBuilderTable = 'form-builder-table',
  // 表单设计器表格列
  FormBuilderTableColumnView = 'form-builder-table-column-view',
  // 表单设计器单元格容器内部组件
  FormBuilderTableColumnViewInline = 'form-builder-table-column-view-inline-inside',
   // 表单行内设计单元格容器
  FormBuilderTableColumnViewInlineContainer = 'form-builder-table-column-view-inline-container',
  // 表单设计器表格列表
  FormBuilderTableColumnsView = 'form-builder-table-columns-view',
  // 表单设计器表格列表 表单项
  FormBuilderTableColumnFormItem = 'form-builder-table-column-form-item',
  
  /* start setting GPT */
  SettingGPTIndexView = 'setting-gpt-index-view',
  SettingGPTChatView = 'setting-gpt-chat-view',
  SettingGPTOpenView = 'setting-gpt-open-view',
  SettingGPTHomeViewRobotList = 'setting-gpt-home-view-robot-list',
  SettingGPTCloseView = 'setting-gpt-close-view',
  SettingGPTViewHeader = 'setting-gpt-view-header',
  SettingGPTViewContent = 'setting-gpt-view-content',
  SettingGPTViewDialog = 'setting-gpt-view-dialog',
  SettingGPTViewFeedback = 'setting-gpt-view-feedback',
  SettingGPTViewCheckbox = 'setting-gpt-view-checkbox',
  SettingGPTViewCheckboxGroup = 'setting-gpt-view-checkbox-group',
  SettingGPTViewContentTitle = 'setting-gpt-view-content-title',
  SettingGPTViewContentSubTitle = 'setting-gpt-view-content-sub-title',
  SettingTableView = 'setting-table-view',
  SettingTablePagination = 'setting-table-pagination',
  SettingGPTQuestionView = 'setting-gpt-question-view',
  SettingGPTQuestionEditView = 'setting-gpt-question-edit-view',
  SettingGPTQuestionViewTabs = 'setting-gpt-question-view-tabs',
  SettingGPTQuestionListTabs = 'setting-gpt-question-list-tabs',
  SettingGPTQuestionViewSelect = 'setting-gpt-question-view-select',
  SettingGPTQuestionViewOperate = 'setting-gpt-question-view-operate',
  SettingGPTQuestionViewTable = 'setting-gpt-question-view-table',
  SettingGPTManageViewTable = 'setting-gpt-manage-view-table',
  SettingGPTSettingViewRobotTable = 'setting-gpt-setting-view-robot-table',
  SettingGPTSettingViewRobotOperate = 'setting-gpt-setting-view-robot-operate',
  SettingGPTEditView = 'setting-gpt-edit-view',
  SettingGPTEditViewTabs = 'setting-gpt-edit-view-tabs',
  SettingGPTEditDescription = 'setting-gpt-edit-view-description',
  SettingGPTEditViewModuleInside = 'setting-gpt-edit-view-module-inside',
  SettingGPTEditViewModuleOutside = 'setting-gpt-edit-view-module-outside',
  SettingGPTEditViewModuleCommon = 'setting-gpt-edit-view-module-common',
  SettingGPTEditViewModulePlugin = 'setting-gpt-edit-view-module-plugin',
  SettingGPTEditViewModuleMixin = 'setting-gpt-edit-view-module-mixin',
  SettingGPTEditViewModuleAdd = 'setting-gpt-edit-view-module-add',
  SettingGPTRobotViewName = 'setting-gpt-robot-view-name',
  SettingGPTEditViewModuleAddGroup = 'setting-gpt-edit-view-module-add-group',
  SettingGPTManageViewTabs = 'setting-gpt-manage-view-tabs',
  SettingGPTManageView = 'setting-gpt-manage-view',
  SettingGPTHomeView = 'setting-gpt-home-view',
  SettingGPTServiceCardItem = 'setting-gpt-service-card-item',
  SettingGPTServiceCardList = 'setting-gpt-service-card-list',
  SettingGPTServiceCheckbox = 'setting-gpt-service-checkbox',
  SettingGPTHomeViewModelGroup = 'setting-gpt-home-view-model-group',
  SettingGPTEditViewChannelGroup = 'setting-gpt-edit-view-channel-group',
  SettingGPTHomeViewLogDialog = 'setting-gpt-home-view-log-dialog',
  SettingGPTHomeViewUrlDescriptionDialog = 'setting-gpt-home-view-url-description-dialog',
  SettingGPTHomeLogChart = 'setting-gpt-home-log-chart',
  SettingGPTHomeViewBi = 'setting-gpt-bi',
  SettingGPTIntelligentQAView = 'setting-gpt-intelligent-qa-view',
  SettingGPTIntelligentQAViewTabs = 'setting-gpt-intelligent-qa-view-tabs',
  SettingGPTPromptWordIndex = 'setting-gpt-prompt-word-index',
  SettingGPTPromptWordContent = 'setting-gpt-prompt-word-content',
  SettingGPTPromptWordList = 'setting-gpt-prompt-word-list',
  SettingGPTPromptWordItem = 'setting-gpt-prompt-word-item',
  SettingGPTPromptWordPreview = 'setting-gpt-prompt-word-preview',
  SettingGPTPromptWordHeader = 'setting-gpt-prompt-word-header',
  SettingGPTSimilarQuestionDialog = 'setting-gpt-similar-question-dialog',
  SettingGPTTranslateView = 'setting-gpt-translate-view',
  SettingGPTDingtalkAssistantSettingDialog = 'setting-gpt-dingtalk-assistant-setting-dialog',
  SettingGPTDingtalkAssistantProtocolDialog = 'setting-gpt-dingtalk-assistant-protocol-dialog',
  SettingGPTDingtalkAssistantDocDialog = 'setting-gpt-dingtalk-assistant-doc-dialog',
  SettingGPTDingtalkAssistantDocTable = 'setting-gpt-dingtalk-assistant-doc-table',
  SettingGPTDingtalkAssistantDocViewDialog = 'setting-gpt-dingtalk-assistant-doc-view-dialog',
  /* end setting GPT */
  
  /* start offline */
  SettingOfflineView = 'setting-offline-view',
  /* end offline */

  /* start ai */
  AiAgentHeader = 'ai-agent-header',
  AiAgentView = 'ai-agent-view',
  AiAgentViewData = 'ai-agent-view-data',
  AiAgentViewTab = 'ai-agent-view-tab',
  AiAgentViewList = 'ai-agent-view-list',
  AiAgentViewListHeader = 'ai-agent-view-list-header',
  AiAgentSelect = 'ai-agent-select',
  AiAgentViewListItem = 'ai-agent-view-list-item',
  AiAgentDetailHeader = 'ai-agent-detail-header',
  AiAgentDetailContent = 'ai-agent-detail-content',
  AiAgentDetailContentCardList = 'ai-agent-detail-content-card-list',
  AiAgentDetailContentCardItem = 'ai-agent-detail-content-card-item',
  AiAgentServiceCheckbox = 'ai-agent-service-checkbox',
  AiAgentCheckboxGroup = 'ai-agent-checkbox-group',
  AiAgentServiceCardItem = 'ai-agent-service-card-item',
  AiAgentViewCheckbox = 'ai-agent-view-checkbox',
  AiAgentCreateDialog = 'ai-agent-create-dialog',
  AiAgentTypeCheckbox = 'ai-agent-type-checkbox',
  AiAgentLog = 'ai-agent-log',
  AiAgentLogHeader = 'ai-agent-log-header',
  AiAgentLogTable = 'ai-agent-log-table',
  AiAgentChartView = 'ai-agent-chart',
  AiAgentChartHeader = 'ai-agent-chart-header',
  AiAgentChartTable = 'ai-agent-chart-table',
  AiAgentChartItem = 'ai-agent-chart-item',
  AiSearchTaskInput = 'ai-search-task-input',
  /* end ai */

  /* start ai agent setting */
  AiAgentAppSetting = 'ai-agent-app-setting',
  AiAgentAppHeader = 'ai-agent-app-header',
  AiAgentBasicInfo = 'ai-agent-basic-info',
  AiAgentBasicInfoWorkflow = 'ai-agent-basic-info-workflow',
  AiAgentPrompt = 'ai-agent-prompt',
  AiAgentPreview = 'ai-agent-preview',
  AiAgentAppBaseSetting = 'ai-agent-app-base-setting',
  AiAgentAppBaseSettingChat = 'ai-agent-app-base-setting-chat',
  AiAgentAppBaseSettingWorkflow = 'ai-agent-app-base-setting-workflow',
  AiAgentAppAbilitySetting = 'ai-agent-app-ability-setting',
  AiAgentAppAuthSetting = 'ai-agent-app-auth-setting',
  AiAgentAppDefaultQuestionsDialog = 'ai-agent-app-default-questions-dialog',
  AiAgentAppCreateDialog = 'ai-agent-app-create-dialog',
  AiSummary = 'ai-summary',
  AiSummaryButton = 'ai-summary-button',
  AiSummaryDialog = 'ai-summary-dialog',
  AiAgentDingtalkAssistantSettingDialog = 'ai-agent-dingtalk-assistant-setting-dialog',
  AiAgentDingtalkAssistantProtocolDialog = 'ai-agent-dingtalk-assistant-protocol-dialog',
  AiAgentUpgradeV2Button = 'ai-agent-upgrade-v2-button',
  AiAgentUpgradeV2Dialog = 'ai-agent-upgrade-v2-dialog',
  AiAgentUpgradeV2 = 'ai-agent-upgrade-v2',
  AiAgentModelItem = 'ai-agent-model-item',
  AiAgentV2FreeTrialButton = 'ai-agent-v2-free-trial-button',
  AiAgentV2ReleaseButton = 'ai-agent-v2-release-button',
  AiTaskForm = 'ai-task-form',
  AiTaskFormButton = 'ai-task-form-button',
  AiTaskFormDialog = 'ai-task-form-dialog',
  /* end ai agent setting */

}

export default ComponentNameEnum
