/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2021-03-11 15:58:44
 * @LastEditTime: 2025-05-19 16:33:45
 * @LastEditors: 铃兰 <EMAIL>
 * @Description: 
 * You build it, You run it.
 */

import BaseEnum from './BaseEnum';
import i18n from '@src/locales';
const $t = i18n.t.bind(i18n);

// 事件状态
type EventState = {
  // 背景颜色
  bgColor ?: string,
  // 颜色
  color ?: string,
  // 显示名字
  name: string,
  // 值
  value: string,
}

/** 
 * @description 事件状态枚举
 * -- 稍微重写了下，暂未拆分，兼容之前的方法
*/
class EventStateEnum extends BaseEnum {
  
  static CREATED: EventState = {
    bgColor: '255, 112, 67',
    color: '#FF7A45',
    name: $t('common.event.stateProcess.created') as string,
    value: 'created'
  }
  
  static ALLOCATED: EventState = {
    bgColor: '255, 167, 38',
    color: '#FAAD14',
    name: $t('common.event.stateProcess.allocated') as string,
    value: 'allocated',
  };
  
  static PROCESSING: EventState = {
    bgColor: '0, 176, 255',
    color: '#FAAD14',
    name: $t('common.event.stateProcess.processing') as string,
    value: 'processing',
  };
  
  static FINISHED: EventState = {
    bgColor: '0, 200, 83',
    color: '#73D13D',
    name: $t('common.base.usualStatus.finish') as string,
    value: 'finished',
  };
  
  static OFFED: EventState = {
    bgColor: '189, 189, 189',
    color: '#8C8C8C',
    name: $t('common.event.stateProcess.offed') as string,
    value: 'offed',
  };
  
  static PSUSED: EventState = {
    bgColor: '255, 77, 79',
    color: '#FF4D4F',
    name: $t('common.task.status.paused') as string,
    value: 'paused',
  };
  static CLOSED: EventState = {
    bgColor: '96, 125, 139',
    color: '#4F564D',
    name: i18n.t('common.task.type.closed') as string, //'已关闭',
    value: 'closed',
  };
  
  constructor() {
    super();
  }
  
  /** 
   * @description 根据 value 获取 bgColor
   * @param {String} value 值
   * @param {Number} transparency 透明度
  */
  static getBgColor(value: string, transparency?: number): string {
    const eventStateEnum: any = EventStateEnum;
    let bgColor: string  = ''
    let state: EventState
    
    for (let stateKey in eventStateEnum) {
      state = eventStateEnum[stateKey]
      if (state.value == value) {
        bgColor = state.bgColor || ''
        break
      }
    }
    
    // 错误提示
    if (!bgColor) console.warn('Caused: EventStateEnum getBgColor got the value is empty')
    // 判断是否有透明度
    if (typeof transparency === 'number') {
      bgColor = `rgba(${bgColor}, ${transparency})` 
    } else {
      bgColor = `rgb(${bgColor})`
    }
    
    return bgColor;
  }
  
  /** 
   * @description 根据 value 获取 color
   * @param {String} value 值
  */
  static getColor(value: string): string {
    const eventStateEnum: any = EventStateEnum
    let state: EventState
    
    for (let stateKey in eventStateEnum) {
      state = eventStateEnum[stateKey]
      if (state.value == value) {
        return state.color || ''
      }
    }
    // 错误提示
    console.warn('Caused: EventStateEnum getColor got the value is empty')
    
    return ''
  }
  
  /** 
   * @description 根据 value 获取 name
   * @param {String} value 值
  */
  static getName(value: string): string {
    const eventStateEnum: any = EventStateEnum
    let state: EventState
    
    for (let stateKey in eventStateEnum) {
      state = eventStateEnum[stateKey]
      if (state.value == value) {
        return state.name
      }
    }
    // 错误提示
    console.warn('Caused: EventStateEnum getName got the value is empty')
    
    return '';
  }
  
  /** 
   * @description 根据 name 获取 value
   * @param {String} name 名字
  */
  static getValue(name: string): string {
    const eventStateEnum: any = EventStateEnum
    let state: EventState
    
    for (let stateKey in eventStateEnum) {
      state = eventStateEnum[stateKey]
      if (state.name == name) {
        return state.value
      }
    }
    // 错误提示
    console.warn('Caused: EventStateEnum getValue got the value is empty')
    
    return '';
  }
  
  /** 
   * @description 根据事件状态 获取 name
   * @param {Object} event 事件数据
  */
  static getNameForEvent(event: any = {}): string {
    if (event.isPaused) return EventStateEnum.PSUSED.name;
    
    let { state } = event;
    
    return EventStateEnum.getName(state);
  }
  
  /** 
   * @description 根据事件状态 获取 color
   * @param {Object} event 事件数据
  */
  static getColorForEvent(event: any= {}): { bgColor: string, color: string} {
    let { state } = event;
    if (event.isPaused) state = EventStateEnum.PSUSED.value;
    
    return {
      color: EventStateEnum.getColor(state),
      bgColor: EventStateEnum.getBgColor(state, 1)
    }
  }
  
  /** 
   * @description 根据事件状态 获取 value
   * @param {Object} event 事件数据
  */
  static getValueForEvent(event: any= {}): string {
    if(event.isPaused) return EventStateEnum.PSUSED.value;
    
    let { state } = event;
    
    return EventStateEnum.getValue(state);
  }
}

export default EventStateEnum;
